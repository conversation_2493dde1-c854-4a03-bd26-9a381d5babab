#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

log_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# 创建部署包目录
mkdir -p deploy_package/{images,scripts}

# 检查必要文件
if [ ! -f "elasticsearch-analysis-ik-8.14.3.zip" ]; then
    log_error "Missing elasticsearch-analysis-ik-8.14.3.zip"
    exit 1
fi

# 复制必要文件
log_info "Copying necessary files..."
cp elasticsearch-analysis-ik-8.14.3.zip deploy_package/
cp elasticsearch.Dockerfile deploy_package/
cp install-es.sh deploy_package/
cp config.yaml deploy_package/

# 保存 Docker 镜像
log_info "Saving Docker images..."
if docker images | grep -q "es-ik"; then
    docker save es-ik > deploy_package/images/es-ik.tar
else
    log_error "es-ik image not found. Please build it first."
    exit 1
fi

if docker images | grep -q "hngpt-bi"; then
    docker save hngpt-bi > deploy_package/images/hngpt-bi.tar
else
    log_error "hngpt-bi image not found. Please build it first."
    exit 1
fi


if docker images | grep -q "minio"; then
    docker save minio > deploy_package/images/minio.tar
else
    log_error "minio image not found. Please build it first."
    exit 1
fi

if docker images | grep -q "es-search"; then
    docker save es-search > deploy_package/images/es-search.tar
else
    log_error "es-search image not found. Please build it first."
    exit 1
fi

# 创建部署脚本
cat > deploy_package/deploy.sh << 'EOF'
#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

log_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    log_error "Docker is not installed"
    exit 1
fi

# 加载 Docker 镜像
log_info "Loading Docker images..."
docker load < images/es-ik.tar
docker load < images/hngpt-bi.tar
docker load < images/minio.tar
docker load < images/es-search.tar
# 运行安装脚本
log_info "Running installation script..."
chmod +x install-es.sh
./install-es.sh
EOF

# 添加执行权限
chmod +x deploy_package/deploy.sh

# 打包
log_info "Creating deployment package..."
tar -czf deploy_package.tar.gz deploy_package/

log_info "Deployment package created successfully: deploy_package.tar.gz"
log_info "
To deploy:
1. Copy deploy_package.tar.gz to target server
2. Extract: tar -xzf deploy_package.tar.gz
3. cd deploy_package
4. Run: ./deploy.sh
" 