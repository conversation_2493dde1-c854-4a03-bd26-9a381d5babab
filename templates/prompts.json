[{"act": "科技项目简介", "prompt": "已知信息:{context},请从前面的信息中抽取1.项目实施单位及负负责人，2.项目投资额（只要一个总金额）及建设工期（只要立项到竣工验收两个时间段），3.项目研究内容、解决问题及创新点或技术措施，4.项目交付成果等信息的详细的综述。不允许在答案中添加编造成分,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "科技项目汇编", "prompt": "<think>\n我需要从提供的信息中提取科技项目的关键信息，包括：\n1. 项目主要研究内容\n2. 解决问题\n3. 关键技术\n4. 项目目标\n5. 创新点\n6. 研究成果（交付成果）\n7. 项目投资额\n8. 项目建设周期或建设工期\n\n我需要仔细分析提供的信息，确保不添加编造成分，对于无法获得的信息使用空白代替。\n</think>\n\n已知信息:{context},请从前面的信息中抽取1.项目主要研究内容，2.解决问题，3.关键技术，4.项目目标，5.创新点 6.研究成果（交付成果），7.项目投资额，8.项目建设周期或建设工期等信息的详细的综述。不允许在答案中添加编造成分,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "科技项目汇编(patent)", "prompt": "已知信息:{context},请从前面的信息中抽取1.项目主要研究内容，2.解决问题，3.关键技术，4.项目目标，5.创新点 6.研究成果（交付成果），7.项目投资额，8.项目建设周期或建设工期, 9专利信息等信息的详细的综述。不允许在答案中添加编造成分,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "科技项目汇编(patent-modify)", "prompt": "已知信息:{context},请从前面的信息中抽取1.项目主要研究内容,解决问题,项目目标，2.关键技术，3.创新点 4.项目投资额，包含总投资额，5.项目建设周期或建设工期, 6.研究成果（交付成果）包含专利信息等信息的详细的综述。不允许在答案中添加编造成分,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "科技项目开题报告/项目报告", "prompt": "已知信息:{context},请从前面的信息中抽取抽取项目名称，项目编号，项目承担单位，项目负责人，起止年限，主要研究要点，拟解决的主要技术难点等信息的详细的综述,不允许在答案中添加编造成分,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "科技项目竣工验收", "prompt": "已知信息:{context},请从前面的信息中抽取抽取项目名称,总投资额,竣工验收时间,主要交付成果等信息的详细的综述,不允许在答案中添加编造成分,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "科技项目可研报告", "prompt": "已知信息:{context},请从前面的信息中抽取抽取项目名称,申请单位,起止时间,项目负责人，申请日期，申请经费总额，项目摘要，研究内容，预期成果等信息的详细的综述,不允许在答案中添加编造成分,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "科技项目相关创新点技术路线", "prompt": "我希望你是电网公司内部文档汇编专家。你的任务根据:{context}, 抽取年份,统计项目数目,罗列每一个项目,每个项目需要包含：项目名称,创新点,技术路线,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "管理会议纪要汇编", "prompt": "我希望你是电网公司内部文档汇编专家。你的任务根据:{context}, 抽取:时间，地点，与会人员，会议纪要信息，进行加工合并,形成通顺的信息,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "党建工作会议汇编", "prompt": "我希望你是电网公司内部文档汇编专家。你的任务根据:{context}, 抽取年份,统计党建工作会议数目,罗列所有党建工作会议,每个会议需要包含:会议时间，会议地点，与会人员，会议纪要信息，进行加工合并,形成通顺的信息,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "简单问答", "prompt": "<think>\n用户提出了一个问题，我需要基于提供的上下文信息来回答。我需要：\n1. 仔细分析问题的核心要求\n2. 从上下文中寻找相关信息\n3. 组织答案，确保逻辑清晰\n4. 不添加任何编造的内容\n5. 如果信息不足，需要明确指出\n</think>\n\n我希望你是电网公司内部文档汇编专家。你的任务根据:{context},进行加工合并,形成通顺的信息,回答问题。不允许在答案中添加编造成分, 问题：{query}"}, {"act": "简单总结", "prompt": "我希望你是电网公司内部文档汇编专家。你的任务根据:{context},进行加工合并,形成通顺的信息。形成总结短文。"}, {"act": "专业汇编", "prompt": "<think>\n我需要作为电网公司内部文档汇编专家，对提供的信息进行专业的汇编处理。我的任务包括：\n1. 合并相关信息\n2. 按逻辑顺序排序\n3. 筛选和过滤重要内容\n4. 合理排版组织\n5. 提取关键信息：项目名称、项目编号、项目年份、起止年限、主要完成单位、协作单位、投资金额、验收时间、关键技术、研究内容、交付成果\n\n如果信息不足，我需要明确指出无法回答，不能编造任何内容。\n</think>\n\n我希望你是电网公司内部文档汇编专家。你的任务根据:{context},进行合并,排序,筛选,过滤,合理排版组织给出简短专业文档汇编文章,提取信息中的最好包含关键信息,关键信息可能包含：项目名称,项目编号,项目年份,起止年限,主要完成单位,协作单位,投资金额,验收时间,关键技术,研究内容,交付成果的相关内容。如果无法从中得到答案,说'相关词条信息无法回答该问题',不允许在答案中添加编造成分,请使用简体中文。"}, {"act": "通用", "prompt": "我希望你是AI助手,你需要认真思考用户的问题，为用户提供最准确的答案,不允许在答案中添加编造成分,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "科技项目概况", "prompt": "\n        已知信息:{context},\n        参考概述：\n        海南电网公司××科技项目概况\n\n        一、项目工程概况\n        1.项目批准文件\n        列明项目批准文件完整标题及文件编号\n        示例：本项目于20XX年XX月XX日获得南方电网公司/海南电网公司批准建设/实施，批准文件为《南方电网公司/海南电网公司关于下达20XX年公司科技项目专项经费计划的通知》（琼发改能源〔20XX〕XX号）。\n        2.合同情况\n        列明双方签订的时间、合同名称、合同编号及合同金额（如无明确金额可省略）。\n        示例：2021年3月，海南电网有限责任公司与海南数字电网研究院有限公司签订《XXXXXX科技项目开发实施合同》（合同编号：），合同金额为XXXX万元。\n        3.项目简介\n        说明项目规模与实施情况\n        （注意：写清楚项目的基本背景、创新点及技术实现路径，尤其要写明项目获得的奖励、表彰以及相关知识产权、软件著作权或发明专利证书名称和编号）\n        示例：\n        项目1：XXXXX项目总投资人民币……万元，项目主要以解决……问题为出发点，项目主要创新点有一是……，二是……，三是……；项目主要研发内容包括……（以上项目内容请简述）。项目于*年*月*日开始实施，*年*月*日通过竣工鉴定验收并交付应用。项目获得什么级别的荣誉或表彰，取得什么知识产权、软件著作权证书或发明专利（实用新型、外观专利）证书（证书名称、编号）\n        项目2：XXXXX项目总投资人民币……万元，项目主要以解决……问题为出发点，项目主要创新点有一是……，二是……，三是……；项目主要研发内容包括……（以上项目内容请简述）。项目于*年*月*日开始实施，*年*月*日通过竣工鉴定验收并交付应用。项目获得什么级别的荣誉或表彰，取得什么知识产权、软件著作权证书或发明专利（实用新型、外观专利）证书（证书名称、编号）\n        项目3：XXXXX项目总投资人民币……万元，项目主要以解决……问题为出发点，项目主要创新点有一是……，二是……，三是……；项目主要研发内容包括……（以上项目内容请简述）。项目于*年*月*日开始实施，*年*月*日通过竣工鉴定验收并交付应用。项目获得什么级别的荣誉或表彰，取得什么知识产权、软件著作权证书或发明专利（实用新型、外观专利）证书（证书名称、编号）\n        项目X：……\n        4.参建单位\n        列明建设单位、设计单位（如有）、监理单位（如有）、实施（开发）单位各单位全称\n        示例：\n        建设单位：海南电网有限责任公司（建设分公司）\n        设计单位：XXXXXXXXXX\n        实施（开发）单位：XXXXXXXXXX\n        监理单位：XXXXXXXXXX监理有限公司\n        5.主要设备及测试、试验厂家：列明本项目主要设备、测试/试验厂家名称\n        主要设备厂家：\n        测试/试验厂家：\n        \n        使用以上项目工程概述说明生成相应的项目概述,需要的信息请你从已经信息中抽取,不允许在答案中添加编造成分,不能获得信息的使用空白代替,请使用简体中文\n        "}, {"act": "科技项目大事记", "prompt": "已知信息:{context},请从前面的信息中抽:可行性研究阶段-项目立项（下达投资计划）-签订合同-需求分析（确定需求，形成需求规格、概要和详细设计说明书）阶段-设计开发阶段-部署测试阶段-中期检查阶段-试运行阶段-科技成果鉴定（产出成果类型及内容简介）-竣工验收阶段等信息的详细的综述,不允许在答案中添加编造成分,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "项目大事记", "prompt": "已知信息:{context},前面的信息是关于多个项目的摘要信息，请对这些信息进行概括总结，分项目和各个项目的各个阶段编写详细的纪要,不允许在答案中添加编造成分,不能获得信息的使用空白代替,请使用简体中文"}, {"act": "文案大事记", "prompt": "已经信息：{context}， 示例：2024年1月，海南电网公司重要会议、干部人事任免及调动、表彰奖惩信息等大事记记录如下：\\n（一）重要决策会议\\n1.1月XX日，公司召开第XX次党委会，研究审议如下事项：\\n（1）研究审议了……\\n（2）原则同意了……\\n（3）研究讨论了……\\n2.1月XX日，公司召开第XX次董事会会议，研究审议如下事项（形成如下决议）：\\n同上……\\n（二）印发重要文件\\n1.1月XX日，印发《**********************》文件；\\n2.1月XX日，印发《**********************》文件；\\n（三）干部人事任免情况\\n1.1月XX日，某某某同志任XXX，免去某某某同志XXX职务；\\n2.同上。\\n如本月无任免信息，可显示“经查找，本月无干部人事任免信息。”\\n（四）表彰获奖情况\\n1.1月XX日，某某单位、某某同志荣获XXXXX先进集体、先进个人；\\n2.1月XX日，某某项目、工程荣获XXXXX优秀工程奖；\\n如本月无获奖表彰信息，可显示“经查找，本月无获奖、表彰信息。”\\n（五）问责、处分情况\\n1.1月XX日，某某某某同志被给予警告处分、……；\\n2.1月XX日，某某单位给予通报批评；\\n如本月无处分问责信息，可显示“经查找，本月无处分、问责信息。”\\n， 请你参考示例使用已知信息重写一段总结归纳短文"}, {"act": "名词解释", "prompt": "已知信息：{context},请你给出名词解释，不允许在答案中添加编造成分，答案请使用中文。如果无法给出解释，请说\"无相关信息\""}]