#!/usr/bin/env python3
"""
服务A FastAPI应用 - 宿主机版本
专门用于在宿主机***********上运行，连接容器中的服务B
"""

from fastapi import FastAPI, Request, HTTPException, status, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
import httpx
import time
import hmac
import hashlib
import json
from typing import Dict, Any, Optional
from pathlib import Path
import uvicorn
import os
import logging

# 配置文件加载
def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    config_path = Path(__file__).parent / "config.json"

    if not config_path.exists():
        # 如果配置文件不存在，返回默认配置
        logging.warning(f"配置文件不存在: {config_path}，使用默认配置")
        return get_default_config()

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logging.info(f"成功加载配置文件: {config_path}")
        return config
    except Exception as e:
        logging.error(f"加载配置文件失败: {e}，使用默认配置")
        return get_default_config()

def get_default_config() -> Dict[str, Any]:
    """获取默认配置"""
    return {
        "service_a": {
            "name": "Service A - 配置化安全对接系统",
            "version": "3.0.0-host",
            "host": "0.0.0.0",
            "port": 8080,
            "base_url": "http://***********:8080"
        },
        "service_b": {
            "frontend": {
                "host": "***********",
                "port": 8081,
                "protocol": "http",
                "url": "http://***********:8081"
            },
            "backend": {
                "host": "localhost",
                "port": 18888,
                "protocol": "http",
                "url": "http://localhost:18888",
                "api_key": "service_a_to_b_secret_key_2024"
            }
        },
        "network": {
            "deployment_mode": "host",
            "environment": "development",
            "cors_origins": ["*"],
            "timeout": 30
        },
        "security": {
            "api_key": "service_a_to_b_secret_key_2024"
        }
    }

# 加载配置
CONFIG = load_config()

# 创建FastAPI应用
app = FastAPI(
    title=CONFIG["service_a"]["name"],
    description=CONFIG["service_a"].get("description", "提供服务B代理接口和测试页面"),
    version=CONFIG["service_a"]["version"]
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=CONFIG["network"]["cors_origins"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 从配置文件获取服务B配置
SERVICE_B_BASE_URL = os.getenv("SERVICE_B_BASE_URL", CONFIG["service_b"]["backend"]["url"])
SERVICE_B_API_KEY = os.getenv("SERVICE_B_API_KEY", CONFIG["service_b"]["backend"]["api_key"])

# 动态获取当前服务的基础URL
def get_current_base_url(request: Request) -> str:
    # """动态获取当前服务的基础URL，支持nps等转发工具"""
    # # 优先使用X-Forwarded-Host头（nps等转发工具会设置）
    # forwarded_host = request.headers.get("X-Forwarded-Host")
    # if forwarded_host:
    #     # 检查是否有协议前缀
    #     if not forwarded_host.startswith(('http://', 'https://')):
    #         # 优先使用https，如果是localhost则使用http
    #         protocol = "https" if not forwarded_host.startswith('localhost') else "http"
    #         return f"{protocol}://{forwarded_host}"
    #     return forwarded_host

    # # 其次使用Host头
    # host = request.headers.get("Host")
    # if host:
    #     # 检查是否是localhost或内网IP，决定使用http还是https
    #     if host.startswith('localhost') or host.startswith('127.0.0.1') or host.startswith('192.168.') or host.startswith('10.'):
    #         return f"http://{host}"
    #     else:
    #         return f"https://{host}"

    # 最后回退到默认值
    return "http://***********:8080"

def get_service_b_urls(request: Request = None) -> dict:
    """从配置文件获取服务B的URL配置"""
    # 直接从配置文件获取服务B的URL
    frontend_config = CONFIG["service_b"]["frontend"]
    backend_config = CONFIG["service_b"]["backend"]

    # 支持环境变量覆盖配置文件
    frontend_url = os.getenv("SERVICE_B_FRONTEND_URL", frontend_config["url"])
    backend_url = os.getenv("SERVICE_B_BACKEND_URL", backend_config["url"])

    return {
        'frontend': frontend_url,
        'backend': backend_url,
        'frontend_host': frontend_config["host"],
        'frontend_port': frontend_config["port"],
        'backend_host': backend_config["host"],
        'backend_port': backend_config["port"]
    }

# 模拟用户数据库
MOCK_USERS = {
    "1": {
        "user_id": "1",
        "username": "admin",
        "nick_name": "系统管理员",
        "dept_id": "100",
        "dept_name": "总公司",
        "roles": ["admin", "user"],
        "active": True
    },
    "2": {
        "user_id": "2",
        "username": "dept_admin",
        "nick_name": "部门管理员",
        "dept_id": "105",
        "dept_name": "技术部",
        "roles": ["deptAdmin", "user"],
        "active": True
    },
    '7': {
        "user_id": 7,
        "username": 'dev',
        "nick_name": '开发人员',
        "dept_id": 105,
        "dept_name": '技术部',
        "roles": ['user'],
        "active": True
    },
    '1939989242101223424': {
        "user_id": 1939989242101223424,
        "username": '<EMAIL>',
        "nick_name": '庄娇丽',
        "dept_id": 105,
        "dept_name": '第三方机构',
        "roles": ['user'],
        "active": True
    },
    
}

class MockUser:
    """模拟用户类"""
    
    def __init__(self, user_data: Dict[str, Any]):
        self.user_id = user_data["user_id"]
        self.username = user_data["username"]
        self.nick_name = user_data["nick_name"]
        self.dept_id = user_data["dept_id"]
        self.dept_name = user_data["dept_name"]
        self.roles = user_data["roles"]
        self.active = user_data["active"]

def get_current_user(request: Request) -> MockUser:
    """获取当前用户（模拟）"""
    # 从请求头获取用户ID（实际应用中应该验证JWT Token）
    user_id = request.headers.get("X-User-ID", "1")
    
    if user_id in MOCK_USERS:
        return MockUser(MOCK_USERS[user_id])
    else:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户未认证"
        )

def generate_service_auth_headers(data: Dict[str, Any]) -> Dict[str, str]:
    """生成服务间认证头"""
    timestamp = str(int(time.time()))
    data_str = json.dumps(data, sort_keys=True)
    
    # 生成HMAC签名
    message = f"{data_str}{timestamp}"
    signature = hmac.new(
        SERVICE_B_API_KEY.encode(),
        message.encode(),
        hashlib.sha256
    ).hexdigest()
    
    return {
        "X-API-Key": SERVICE_B_API_KEY,
        "X-Timestamp": timestamp,
        "X-Signature": signature,
        "X-Service-Name": "ServiceA",
        "Content-Type": "application/json"
    }

# 根路径重定向到测试页面
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """根路径重定向到测试页面"""
    current_base = get_current_base_url(request)
    service_b_urls = get_service_b_urls(request)

    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>服务A - 配置化安全对接系统 (宿主机版)</title>
        <meta charset="UTF-8">
        <style>
            body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
            .container {{ max-width: 600px; margin: 0 auto; }}
            .btn {{ background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px; }}
            .host-info {{ background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔒 服务A - 配置化安全对接系统</h1>
            <div class="host-info">
                <h3>🖥️ 宿主机部署版本</h3>
                <p>当前访问地址: {current_base}</p>
                <p>连接到容器中的服务B</p>
            </div>
            <div>
                <a href="/test" class="btn">🧪 主测试页面</a>
                <a href="/config" class="btn">⚙️ 配置管理</a>
                <a href="/docs" class="btn">📚 API文档</a>
                <a href="/health" class="btn">💚 健康检查</a>
            </div>
            <div style="margin-top: 30px; color: #666;">
                <p>🌐 网络环境 (动态配置):</p>
                <p>服务A: {current_base}</p>
                <p>服务B前端: {service_b_urls['frontend']}</p>
                <p>服务B后端: {service_b_urls['backend']}</p>
            </div>
        </div>
    </body>
    </html>
    """

# 健康检查
@app.get("/health")
async def health_check(request: Request):
    """健康检查"""
    current_base = get_current_base_url(request)
    service_b_urls = get_service_b_urls(request)

    return {
        "status": "healthy",
        "service": "ServiceA",
        "version": "3.0.0-host",
        "deployment": "host",
        "timestamp": time.time(),
        "environment": {
            "current_base_url": current_base,
            "service_b_frontend": service_b_urls['frontend'],
            "service_b_backend": service_b_urls['backend'],
            "api_key_configured": bool(SERVICE_B_API_KEY),
            "forwarded_host": request.headers.get("X-Forwarded-Host"),
            "host_header": request.headers.get("Host")
        }
    }

# 配置管理页面
@app.get("/config", response_class=HTMLResponse)
async def config_manager_page():
    """返回配置管理页面"""
    config_page_path = Path(__file__).parent / "static" / "config_manager.html"

    if config_page_path.exists():
        return FileResponse(config_page_path)
    else:
        raise HTTPException(status_code=404, detail="配置管理页面不存在")

# 测试页面
@app.get("/test", response_class=HTMLResponse)
async def test_page(request: Request):
    """返回测试页面"""
    test_page_path = Path(__file__).parent / "static" / "test_host.html"

    if test_page_path.exists():
        return FileResponse(test_page_path)
    else:
        # 如果文件不存在，返回简单的测试页面
        current_base = get_current_base_url(request)
        service_b_urls = get_service_b_urls(request)

        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>服务A测试页面 (宿主机版)</title>
            <meta charset="UTF-8">
        </head>
        <body>
            <h1>🔒 服务A测试页面 (宿主机版)</h1>
            <p>测试页面文件不存在，请检查 static/test_host.html</p>
            <p><a href="/docs">查看API文档</a></p>
            <div style="margin-top: 20px; padding: 20px; background: #f0f0f0; border-radius: 5px;">
                <h3>动态环境信息</h3>
                <p>服务A: {current_base}</p>
                <p>服务B前端: {service_b_urls['frontend']}</p>
                <p>服务B后端: {service_b_urls['backend']}</p>
            </div>
        </body>
        </html>
        """

# 获取网络配置API
@app.get("/api/network-config")
async def get_network_config(request: Request):
    """获取网络配置，供前端使用 - 基于配置文件"""
    current_base = get_current_base_url(request)
    service_b_urls = get_service_b_urls()

    # 从配置文件获取完整的网络配置
    network_config = {
        "service_a": {
            "url": current_base,
            "name": CONFIG["service_a"]["name"],
            "version": CONFIG["service_a"]["version"]
        },
        "service_b_frontend": service_b_urls['frontend'],
        "service_b_backend": service_b_urls['backend'],
        "service_b": {
            "frontend": {
                "url": service_b_urls['frontend'],
                "host": service_b_urls['frontend_host'],
                "port": service_b_urls['frontend_port']
            },
            "backend": {
                "url": service_b_urls['backend'],
                "host": service_b_urls['backend_host'],
                "port": service_b_urls['backend_port']
            }
        },
        "network": {
            "deployment_mode": CONFIG["network"]["deployment_mode"],
            "environment": CONFIG["network"]["environment"],
            "timeout": CONFIG["network"]["timeout"]
        },
        "is_public": not current_base.startswith(('http://localhost', 'http://127.0.0.1', 'http://192.168.', 'http://10.')),
        "config_source": "config.json",
        "headers": {
            "forwarded_host": request.headers.get("X-Forwarded-Host"),
            "host": request.headers.get("Host"),
            "x_forwarded_proto": request.headers.get("X-Forwarded-Proto"),
            "x_forwarded_for": request.headers.get("X-Forwarded-For")
        }
    }

    return network_config

# 配置管理接口
@app.get("/api/config")
async def get_config():
    """获取当前配置"""
    return {
        "config": CONFIG,
        "config_file": str(Path(__file__).parent / "config.json"),
        "environment_overrides": {
            "SERVICE_B_BASE_URL": os.getenv("SERVICE_B_BASE_URL"),
            "SERVICE_B_API_KEY": os.getenv("SERVICE_B_API_KEY"),
            "SERVICE_B_FRONTEND_URL": os.getenv("SERVICE_B_FRONTEND_URL"),
            "SERVICE_B_BACKEND_URL": os.getenv("SERVICE_B_BACKEND_URL")
        }
    }

@app.post("/api/config/reload")
async def reload_config():
    """重新加载配置文件"""
    global CONFIG
    try:
        CONFIG = load_config()
        return {
            "status": "success",
            "message": "配置文件重新加载成功",
            "config": CONFIG
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重新加载配置失败: {str(e)}"
        )

@app.put("/api/config/service-b")
async def update_service_b_config(config_update: Dict[str, Any]):
    """更新服务B配置"""
    global CONFIG
    try:
        # 更新配置
        if "frontend" in config_update:
            CONFIG["service_b"]["frontend"].update(config_update["frontend"])
        if "backend" in config_update:
            CONFIG["service_b"]["backend"].update(config_update["backend"])

        # 保存到文件
        config_path = Path(__file__).parent / "config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(CONFIG, f, indent=2, ensure_ascii=False)

        return {
            "status": "success",
            "message": "服务B配置更新成功",
            "updated_config": CONFIG["service_b"]
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新配置失败: {str(e)}"
        )

# iframe通信测试页面
@app.get("/iframe_communication_test.html", response_class=HTMLResponse)
async def iframe_communication_test_page():
    """返回iframe通信测试页面"""
    test_page_path = Path(__file__).parent / "static" / "iframe_communication_test.html"

    if test_page_path.exists():
        return FileResponse(test_page_path)
    else:
        raise HTTPException(status_code=404, detail="iframe通信测试页面不存在")

# 无限制iframe测试页面
@app.get("/iframe_unrestricted_test.html", response_class=HTMLResponse)
async def iframe_unrestricted_test_page():
    """返回无限制iframe测试页面"""
    test_page_path = Path(__file__).parent / "static" / "iframe_unrestricted_test.html"

    if test_page_path.exists():
        return FileResponse(test_page_path)
    else:
        raise HTTPException(status_code=404, detail="无限制iframe测试页面不存在")

# iframe代理页面
@app.get("/iframe_proxy.html", response_class=HTMLResponse)
async def iframe_proxy_page():
    """返回iframe代理页面"""
    test_page_path = Path(__file__).parent / "static" / "iframe_proxy.html"

    if test_page_path.exists():
        return FileResponse(test_page_path)
    else:
        raise HTTPException(status_code=404, detail="iframe代理页面不存在")

# iframe测试页面
@app.get("/iframe_test.html", response_class=HTMLResponse)
async def iframe_test_page():
    """返回iframe测试页面"""
    test_page_path = Path(__file__).parent / "static" / "iframe_test.html"

    if test_page_path.exists():
        return FileResponse(test_page_path)
    else:
        raise HTTPException(status_code=404, detail="iframe测试页面不存在")

# 权限管理页面
@app.get("/permissions")
async def permissions_page(request: Request):
    """权限管理页面 - 重定向到front_end的permissions页面"""
    # front_end运行在2222端口
    frontend_permissions_url = "http://***********:2222/permissions"

    # 直接重定向到front_end的permissions页面
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url=frontend_permissions_url, status_code=302)

# 审核页面
@app.get("/audit", response_class=HTMLResponse)
async def audit_page(request: Request):
    """返回审核页面 - 通过iframe集成es-search的audit组件"""
    current_base = get_current_base_url(request)
    service_b_urls = get_service_b_urls(request)

    # 构建es-search的audit页面URL (es-search运行在8080端口)
    es_search_audit_url = f"{service_b_urls['frontend']}/audit"

    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>审核管理 - Service A</title>
        <style>
            body {{
                margin: 0;
                padding: 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background-color: #f5f5f5;
            }}
            .header {{
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                color: white;
                padding: 20px;
                text-align: center;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .header h1 {{
                margin: 0;
                font-size: 24px;
                font-weight: 600;
            }}
            .header p {{
                margin: 5px 0 0 0;
                opacity: 0.9;
                font-size: 14px;
            }}
            .iframe-container {{
                width: 100%;
                height: calc(100vh - 100px);
                border: none;
                background: white;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .loading {{
                display: flex;
                justify-content: center;
                align-items: center;
                height: 200px;
                color: #666;
            }}
            .error {{
                text-align: center;
                padding: 40px;
                color: #e74c3c;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>📋 审核管理</h1>
            <p>通过Service A集成的审核管理系统</p>
        </div>

        <div class="loading" id="loading">
            <div>正在加载审核管理界面...</div>
        </div>

        <iframe
            id="audit-iframe"
            src="{es_search_audit_url}"
            class="iframe-container"
            style="display: none;"
            onload="handleIframeLoad()"
            onerror="handleIframeError()">
        </iframe>

        <div class="error" id="error" style="display: none;">
            <h3>❌ 加载失败</h3>
            <p>无法加载审核管理界面</p>
            <p>ES-Search URL: <code>{es_search_audit_url}</code></p>
            <button onclick="location.reload()">重新加载</button>
        </div>

        <script>
            function handleIframeLoad() {{
                document.getElementById('loading').style.display = 'none';
                document.getElementById('audit-iframe').style.display = 'block';
                console.log('审核管理页面加载成功');
            }}

            function handleIframeError() {{
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                console.error('审核管理页面加载失败');
            }}

            // 设置超时处理
            setTimeout(() => {{
                if (document.getElementById('loading').style.display !== 'none') {{
                    handleIframeError();
                }}
            }}, 10000); // 10秒超时
        </script>
    </body>
    </html>
    """

    return HTMLResponse(html_content)

# 服务B代理接口
@app.post("/api/service-b/generate-token")
async def proxy_generate_token(request: Request):
    """
    代理调用服务B的Token生成接口
    直接从请求体获取用户信息
    """
    try:
        # 直接从请求体获取用户数据
        user_data = await request.json()

        print(f"[Service A] 收到Token生成请求: {user_data}")

        # 验证必要字段
        required_fields = ["user_id", "user_name", "nick_name", "dept_id", "dept_name", "roles"]
        for field in required_fields:
            if field not in user_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"缺少必要字段: {field}"
                )

        # Service B的generate-token端点只需要IP验证，不需要服务间认证头
        # 直接调用，不添加认证头
        print(f"[Service A] 调用服务B URL: {SERVICE_B_BASE_URL}/api/simple-auth/generate-token")

        # 调用服务B API
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{SERVICE_B_BASE_URL}/api/simple-auth/generate-token",
                json=user_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

            print(f"[Service A] 服务B响应状态码: {response.status_code}")
            print(f"[Service A] 服务B响应内容: {response.text}")

            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"服务B调用失败: {response.text}"
                )

    except httpx.RequestError as e:
        print(f"[Service A] 网络连接错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"无法连接到服务B: {str(e)}"
        )
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        print(f"[Service A] 未知错误: {str(e)}")
        print(f"[Service A] 错误类型: {type(e)}")
        import traceback
        print(f"[Service A] 错误堆栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"代理调用失败: {str(e)}"
        )

@app.post("/api/service-b/revoke-token")
async def proxy_revoke_token(request: Request):
    """
    代理调用服务B的Token撤销接口
    直接从请求体获取用户信息
    """
    try:
        user_id = request.headers.get('X-User-ID', '1')
        # 构造撤销数据
        revoke_data = {"user_id": user_id}
        
        # 生成服务间认证头
        headers = generate_service_auth_headers(revoke_data)
        
        # 调用服务B API
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{SERVICE_B_BASE_URL}/api/simple-auth/revoke-token",
                json=revoke_data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"服务B调用失败: {response.text}"
                )
                
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"无法连接到服务B: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"代理调用失败: {str(e)}"
        )

@app.post("/api/service-b/check-permission")
async def proxy_check_permission(
    request: Request,
    current_user: MockUser = Depends(get_current_user)
):
    """
    代理调用服务B的权限检查接口
    """
    try:
        # 获取请求体
        request_body = await request.json()

        # 生成服务间认证头
        headers = generate_service_auth_headers(request_body)

        # 调用服务B的权限检查API
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{SERVICE_B_BASE_URL}/api/permissions/check-document-access",
                json=request_body,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return {
                    "accessible_urls": result.get("data", {}).get("accessible_urls", []),
                    "denied_urls": result.get("data", {}).get("denied_urls", []),
                    "user_info": result.get("data", {}).get("user_info"),
                    "message": result.get("msg", "权限检查完成")
                }
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"服务B权限检查失败: {response.text}"
                )

    except httpx.RequestError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"无法连接到服务B: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"权限检查代理失败: {str(e)}"
        )

@app.get("/api/service-b/health")
async def proxy_health_check():
    """
    代理调用服务B的健康检查
    """
    try:
        async with httpx.AsyncClient() as client:
            # 服务B的根路径会重定向到/docs，我们直接访问/docs来检查连通性
            response = await client.get(
                f"{SERVICE_B_BASE_URL}/docs",
                timeout=10
            )

            if response.status_code == 200:
                # 返回自定义的健康状态
                return {
                    "status": "healthy",
                    "service": "ServiceB",
                    "backend_url": SERVICE_B_BASE_URL,
                    "response_code": response.status_code,
                    "timestamp": time.time(),
                    "endpoint_tested": "/docs"
                }
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"服务B健康检查失败: HTTP {response.status_code}"
                )

    except httpx.RequestError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"无法连接到服务B: {str(e)}"
        )

# 用户信息接口
@app.get("/api/user/info")
async def get_user_info(current_user: MockUser = Depends(get_current_user)):
    """获取当前用户信息"""
    return {
        "user_id": current_user.user_id,
        "username": current_user.user_name,
        "nick_name": current_user.nick_name,
        "dept_id": current_user.dept_id,
        "dept_name": current_user.dept_name,
        "roles": current_user.roles,
        "active": current_user.active
    }

# 模拟登录接口
@app.post("/api/auth/login")
async def mock_login(login_data: Dict[str, str]):
    """模拟登录接口"""
    username = login_data.get("username")
    password = login_data.get("password")
    
    # 简单的用户验证
    for user_id, user_data in MOCK_USERS.items():
        if user_data["username"] == username:
            # 在实际应用中应该验证密码
            return {
                "access_token": f"mock_token_{user_id}",
                "token_type": "bearer",
                "user_info": user_data
            }
    
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="用户名或密码错误"
    )

# 前端配置API - 用于iframe集成 (nginx部署版本)
@app.get("/api/frontend-config")
async def get_frontend_config(request: Request):
    """获取前端配置，供Service A的test_host.html使用"""
    current_base = get_current_base_url(request)

    # 解析当前请求的协议和主机
    from urllib.parse import urlparse
    parsed = urlparse(current_base)
    protocol = parsed.scheme
    hostname = parsed.hostname

    # 前端服务配置 (指向容器前端服务)
    frontend_config = {
        "frontend_url": f"{protocol}://{hostname}:8080/",
        "frontend_host": hostname,
        "frontend_port": 8080,
        "service_a_host": hostname,
        "service_a_frontend_port": 8081,
        "service_a_backend_port": 8082,
        "iframe_mode": True,
        "deployment_type": "nginx_production"
    }

    return frontend_config

# Service A信息API
@app.get("/api/service-info")
async def get_service_info():
    """获取Service A服务信息"""
    return {
        "service_name": "Service A - 权限管理系统",
        "version": "1.0.0",
        "description": "通过iframe集成nginx部署的前端功能",
        "frontend_port": 8081,
        "backend_port": 8082,
        "integrated_frontend": "http://***********:8080/",
        "deployment_type": "nginx_production"
    }

# 挂载静态文件
static_dir = Path(__file__).parent / "static"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

if __name__ == "__main__":
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Service A 后端服务')
    parser.add_argument('--port', type=int, default=int(os.getenv('SERVICE_A_PORT', 8082)),
                       help='服务监听端口 (默认: 8082)')
    parser.add_argument('--host', type=str, default='0.0.0.0',
                       help='服务监听地址 (默认: 0.0.0.0)')
    args = parser.parse_args()

    # 确保静态文件目录存在
    static_dir = Path(__file__).parent / "static"
    static_dir.mkdir(exist_ok=True)

    # 获取服务B配置
    service_b_urls = get_service_b_urls()

    print("🚀 启动服务A后端 (配置文件版)")
    print(f"📋 服务名称: {CONFIG['service_a']['name']}")
    print(f"🔢 版本: {CONFIG['service_a']['version']}")
    print(f"🌐 部署模式: {CONFIG['network']['deployment_mode']}")
    print(f"🏠 环境: {CONFIG['network']['environment']}")
    print("")
    print("📍 服务地址:")
    print(f"   服务A后端: http://{args.host}:{args.port}")
    print(f"   服务B前端: {service_b_urls['frontend']}")
    print(f"   服务B后端: {service_b_urls['backend']}")
    print("")
    print("🧪 API接口:")
    print("   /api/network-config - 网络配置")
    print("   /api/config - 查看配置")
    print("   /api/config/reload - 重新加载配置")
    print("   /api/config/service-b - 更新服务B配置")
    print("   /docs - API文档")
    print("")
    print("💡 配置文件: config.json")
    print("💡 支持环境变量覆盖配置")

    # 启动服务器
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        log_level="info"
    )
