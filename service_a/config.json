{"service_a": {"name": "Service A - 配置化安全对接系统", "version": "3.0.0-host", "description": "提供服务B代理接口和测试页面 - 运行在宿主机", "host": "***********", "port": 8081, "base_url": "http://***********:8081"}, "service_b": {"name": "Service B - ES-Search 文档搜索系统", "frontend": {"host": "***********", "port": 8080, "protocol": "http", "url": "http://***********:8080"}, "backend": {"host": "***********", "port": 18888, "protocol": "http", "url": "http://***********:18888", "api_key": "service_a_to_b_secret_key_2024"}}, "network": {"deployment_mode": "host", "environment": "development", "cors_origins": ["*"], "timeout": 30, "retry_attempts": 3}, "security": {"api_key": "service_a_to_b_secret_key_2024", "token_expiry_minutes": 60, "allowed_issuers": ["ServiceA"], "allowed_audiences": ["FastAPIService"]}, "logging": {"level": "info", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "service_a.log"}, "features": {"enable_health_check": true, "enable_proxy": true, "enable_static_files": true, "enable_cors": true}}