<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务A - 配置化安全对接测试 (宿主机版)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .host-info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 30px;
            padding: 30px;
            max-width: 100%;
            overflow: hidden;
        }

        .display-area {
            width: 100%;
            max-width: 100%;
            overflow: hidden;
            position: relative;
        }
        
        .control-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            height: fit-content;
        }
        
        .control-section {
            margin-bottom: 30px;
        }
        
        .control-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 8px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .btn.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .btn.danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 1em;
        }
        
        .status-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .status-value {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .iframe-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            min-height: 600px;
            width: 100% !important;
            max-width: 100% !important;
            position: relative !important;
        }
        
        .iframe-header {
            background: #343a40;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        #service-b-iframe {
            width: 100% !important;
            max-width: 100% !important;
            height: 600px !important;
            border: 2px solid #007bff !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            z-index: 1000 !important;
            background: white !important;
            position: relative !important;
            box-sizing: border-box !important;
        }
        
        .log-panel {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding: 5px 0;
            border-left: 3px solid #4facfe;
            padding-left: 10px;
        }
        
        .network-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .network-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .network-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>🔒 服务A - 配置化安全对接测试</h1>
            <p id="deployment-info">宿主机部署版本 - 动态配置</p>
            <div class="host-info">
                <h3>🖥️ 宿主机环境</h3>
                <p>服务A运行在宿主机，连接容器中的服务B</p>
            </div>
        </div>

        <!-- 网络环境信息 -->
        <div class="network-info">
            <h3>🌐 动态网络环境</h3>
            <div class="network-grid" id="network-grid">
                <div class="network-item">
                    <h5>服务A (宿主机)</h5>
                    <p id="service-a-url">加载中...</p>
                </div>
                <div class="network-item">
                    <h5>服务B前端 (容器)</h5>
                    <p id="service-b-frontend-url">加载中...</p>
                </div>
                <div class="network-item">
                    <h5>服务B后端 (容器)</h5>
                    <p id="service-b-backend-url">加载中...</p>
                </div>
                <div class="network-item">
                    <h5>部署模式</h5>
                    <p id="deployment-mode">检测中...</p>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧控制面板 -->
            <div class="control-panel">
                <!-- 系统状态 -->
                <div class="control-section">
                    <h3>📊 系统状态</h3>
                    <div class="status-panel">
                        <div class="status-item">
                            <span>服务A状态</span>
                            <span class="status-value status-success" id="service-a-status">运行中 (宿主机)</span>
                        </div>
                        <div class="status-item">
                            <span>服务B连接</span>
                            <span class="status-value status-warning" id="service-b-status">检测中...</span>
                        </div>
                        <div class="status-item">
                            <span>Token状态</span>
                            <span class="status-value status-error" id="token-status">未生成</span>
                        </div>
                        <div class="status-item">
                            <span>iframe状态</span>
                            <span class="status-value status-error" id="iframe-status">未加载</span>
                        </div>
                    </div>
                </div>

                <!-- 用户模拟 -->
                <div class="control-section">
                    <h3>👤 用户模拟</h3>
                    <div class="input-group">
                        <label for="mock-user-select">选择用户</label>
                        <select id="mock-user-select">
                            <option value="1">系统管理员 (admin)</option>
                            <option value="2">部门管理员 (dept_admin)</option>
                            <option value="7">开发人员 (dev)</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label>当前用户信息</label>
                        <div id="current-user-info" style="padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px; color: #666;">
                            未加载用户信息
                        </div>
                    </div>
                    <button class="btn" onclick="loadUserInfo()">加载用户信息</button>
                    <button class="btn success" onclick="quickSwitchUser('admin')" style="font-size: 11px; padding: 6px 12px;">快速切换Admin</button>
                    <button class="btn success" onclick="quickSwitchUser('dept_admin')" style="font-size: 11px; padding: 6px 12px;">快速切换部门管理员</button>
                    <button class="btn success" onclick="quickSwitchUser('dev')" style="font-size: 11px; padding: 6px 12px;">快速切换Dev</button>
                </div>

                <!-- Token操作 -->
                <div class="control-section">
                    <h3>🔑 Token操作</h3>
                    <button class="btn success" onclick="generateToken()">生成Token</button>
                    <button class="btn danger" onclick="revokeToken()">撤销Token</button>
                </div>

                <!-- 用户会话管理 -->
                <div class="control-section">
                    <h3>👤 会话管理</h3>
                    <button class="btn danger" onclick="simulateUserLogout()" id="logoutBtn">模拟用户退出</button>
                    <div style="margin: 5px 0; font-size: 11px; color: #999;">
                        完整退出流程：撤销Token + 清理服务B会话
                    </div>
                </div>



                <!-- iframe操作 -->
                <div class="control-section">
                    <h3>🖼️ iframe操作</h3>
                    <button class="btn success" onclick="loadServiceB()">加载服务B</button>
                    <button class="btn danger" onclick="destroyIframe()">销毁iframe</button>
                    <button class="btn" onclick="testIframeCommunication()">测试通信</button>
                </div>

                <!-- 路由切换 -->
                <div class="control-section">
                    <h3>🔄 ES-Search 路由切换</h3>

                    <!-- 主要功能页面 -->
                    <div style="margin: 10px 0;">
                        <h4 style="font-size: 14px; margin-bottom: 8px; color: #333;">📋 主要功能</h4>
                        <button class="btn" onclick="switchRoute('/')" id="route-index" style="font-size: 11px; padding: 6px 12px; margin: 2px;">首页 (/)</button>
                        <button class="btn" onclick="switchRoute('/search')" id="route-search" style="font-size: 11px; padding: 6px 12px; margin: 2px;">搜索 (/search)</button>
                        <button class="btn" onclick="switchRoute('/assembly')" id="route-assembly" style="font-size: 11px; padding: 6px 12px; margin: 2px;">汇编 (/assembly)</button>
                        <button class="btn" onclick="switchRoute('/audit')" id="route-audit" style="font-size: 11px; padding: 6px 12px; margin: 2px;">审核 (/audit)</button>
                        <button class="btn" onclick="switchRoute('/permissions')" id="route-permissions" style="font-size: 11px; padding: 6px 12px; margin: 2px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">权限管理 (/permissions)</button>
                    </div>

                    <!-- 项目信息页面 -->
                    <div style="margin: 10px 0;">
                        <h4 style="font-size: 14px; margin-bottom: 8px; color: #333;">📊 项目信息</h4>
                        <button class="btn" onclick="switchRoute('/info')" id="route-info" style="font-size: 11px; padding: 6px 12px; margin: 2px;">项目信息 (/info)</button>
                        <button class="btn" onclick="switchRoute('/desc')" id="route-desc" style="font-size: 11px; padding: 6px 12px; margin: 2px;">项目详情 (/desc)</button>
                        <button class="btn" onclick="switchRoute('/preview')" id="route-preview" style="font-size: 11px; padding: 6px 12px; margin: 2px;">预览 (/preview)</button>
                    </div>

                    <!-- 强制切换 -->
                    <div style="margin: 10px 0; font-size: 12px;">
                        <h4 style="font-size: 14px; margin-bottom: 8px; color: #333;">🔄 强制切换</h4>
                        <button class="btn" onclick="forceRouteSwitch('/')" style="font-size: 10px; padding: 3px 6px; margin: 1px;">强制首页</button>
                        <button class="btn" onclick="forceRouteSwitch('/search')" style="font-size: 10px; padding: 3px 6px; margin: 1px;">强制搜索</button>
                        <button class="btn" onclick="forceRouteSwitch('/assembly')" style="font-size: 10px; padding: 3px 6px; margin: 1px;">强制汇编</button>
                        <button class="btn" onclick="forceRouteSwitch('/audit')" style="font-size: 10px; padding: 3px 6px; margin: 1px;">强制审核</button>
                    </div>

                    <!-- 路由状态显示 -->
                    <div style="margin: 10px 0; font-size: 12px; color: #666; background: #f8f9fa; padding: 8px; border-radius: 4px;">
                        <strong>当前路由:</strong> <span id="current-route" style="color: #007bff; font-weight: bold;">未加载</span><br>
                        <strong>iframe状态:</strong> <span id="iframe-route-status" style="color: #6c757d;">未创建</span>
                    </div>

                    <div style="margin: 5px 0; font-size: 11px; color: #999;">
                        <strong>说明:</strong><br>
                        • 普通切换: 使用redirect参数，保持iframe状态<br>
                        • 强制切换: 重新创建iframe，完全刷新<br>
                        • 路由基于 es-search 项目的实际路由配置
                    </div>
                </div>

                <!-- 路由测试工具 -->
                <div class="control-section">
                    <h3>🧪 路由测试工具</h3>

                    <div style="margin-bottom: 15px;">
                        <h4 style="font-size: 14px; margin-bottom: 8px;">🚀 快速访问</h4>
                        <button class="btn" onclick="openHomePage()" style="font-size: 11px; padding: 6px 12px; margin: 2px;">首页</button>
                        <button class="btn" onclick="openSearchPage()" style="font-size: 11px; padding: 6px 12px; margin: 2px;">搜索</button>
                        <button class="btn" onclick="openAssemblyPage()" style="font-size: 11px; padding: 6px 12px; margin: 2px;">汇编</button>
                        <button class="btn" onclick="openPermissionsPage()" style="font-size: 11px; padding: 6px 12px; margin: 2px;">审核</button>
                        <button class="btn" onclick="openProjectInfoPage()" style="font-size: 11px; padding: 6px 12px; margin: 2px;">项目信息</button>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <h4 style="font-size: 14px; margin-bottom: 8px;">🔍 路由信息</h4>
                        <button class="btn" onclick="getCurrentRouteInfo()" style="font-size: 11px; padding: 6px 12px; margin: 2px;">当前路由信息</button>
                        <button class="btn" onclick="testAllRoutes()" style="font-size: 11px; padding: 6px 12px; margin: 2px;">测试所有路由</button>
                        <button class="btn" onclick="debugAuditPage()" style="font-size: 11px; padding: 6px 12px; margin: 2px; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);">调试审核页面</button>
                    </div>

                    <div style="margin: 5px 0; font-size: 11px; color: #999;">
                        基于 ES-Search 实际路由配置的测试工具
                    </div>
                </div>

                <!-- 权限管理测试 -->
                <div class="control-section">
                    <h3>🔐 权限管理测试</h3>
                    <div style="margin-bottom: 15px;">
                        <h4 style="font-size: 14px; margin-bottom: 8px;">📋 测试场景</h4>
                        <button class="btn" onclick="testScenario('search')" style="font-size: 11px; padding: 6px 12px; margin: 2px;">搜索页面测试</button>
                        <button class="btn" onclick="testScenario('permissions')" style="font-size: 11px; padding: 6px 12px; margin: 2px;">权限管理测试</button>
                        <button class="btn" onclick="testScenario('full')" style="font-size: 11px; padding: 6px 12px; margin: 2px;">完整流程测试</button>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <h4 style="font-size: 14px; margin-bottom: 8px;">🎭 角色切换测试</h4>
                        <button class="btn" onclick="testAsAdmin()" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);">以Admin身份测试</button>
                        <button class="btn" onclick="testAsDev()" style="background: linear-gradient(135deg, #0abde3 0%, #006ba6 100%);">以Dev身份测试</button>
                    </div>

                    <div style="margin: 5px 0; font-size: 11px; color: #999;">
                        完整的权限管理流程测试：申请→审批→验证
                    </div>
                </div>

                <!-- 系统测试 -->
                <div class="control-section">
                    <h3>🧪 系统测试</h3>
                    <button class="btn" onclick="testConnectivity()">连通性测试</button>
                    <button class="btn" onclick="testSecurity()">安全测试</button>
                </div>
            </div>

            <!-- 右侧显示区域 -->
            <div class="display-area">
                <!-- iframe容器 -->
                <div class="iframe-container">
                    <div class="iframe-header">
                        <h4>📱 服务B集成窗口 (容器)</h4>
                        <div>
                            <button onclick="refreshIframe()" style="background: none; border: 1px solid #6c757d; color: white; padding: 5px 10px; border-radius: 4px; cursor: pointer;">刷新</button>
                            <button onclick="debugIframe()" style="background: none; border: 1px solid #ff6b6b; color: white; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-left: 5px;">调试</button>
                        </div>
                    </div>
                    <div id="iframe-placeholder" style="padding: 40px; text-align: center; color: #666; font-size: 1.2em;">
                        <p>🔄 点击"加载服务B"按钮开始测试</p>
                        <p style="margin-top: 10px; font-size: 1em; color: #999;" id="iframe-url-hint">
                            将会创建iframe加载服务B前端 (动态配置)
                        </p>
                    </div>
                </div>

                <!-- 日志面板 -->
                <div class="log-panel">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid #4a5568;">
                        <h4>📋 操作日志</h4>
                        <button onclick="clearLogs()" style="background: none; border: 1px solid #4a5568; color: #e2e8f0; padding: 5px 10px; border-radius: 4px; cursor: pointer;">清空</button>
                    </div>
                    <div id="log-container">
                        <div class="log-entry">
                            <span style="color: #a0aec0;">[2025-06-26 10:30:00]</span>
                            <span style="color: #4facfe;">[INFO]</span>
                            <span>服务A宿主机版本已加载</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentToken = null;
        let serviceBIframe = null;
        let currentUserId = '1';
        let currentUserInfo = null;
        let currentRoute = '/'; // 当前iframe路由
        let NETWORK_CONFIG = null; // 动态网络配置

        // 用户配置
        const USER_CONFIGS = {
            '1': {
                user_id: 1,
                user_name: 'admin',
                nick_name: '系统管理员',
                dept_id: 100,
                dept_name: '总公司',
                roles: ['admin'],
                email: '<EMAIL>',
                phone: '13800138001'
            },
            '7': {
                user_id: 7,
                user_name: 'dev',
                nick_name: '开发人员',
                dept_id: 105,
                dept_name: '技术部',
                roles: ['user'],
                email: '<EMAIL>',
                phone: '13800138007'
            },
            "2": {
                user_id: 2,
                user_name: 'dept_admin',
                nick_name: '部门管理员',
                dept_id: 105,
                dept_name: '技术部',
                roles: ["deptAdmin", "user"],
                email: '<EMAIL>',
                phone: '13800138008'
            }
        };

        // 动态获取网络配置
        async function loadNetworkConfig() {
            try {
                const response = await fetch('/api/network-config');
                if (response.ok) {
                    NETWORK_CONFIG = await response.json();
                    addLog('✅ 网络配置加载成功', 'info');
                    updateNetworkDisplay();
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addLog(`❌ 网络配置加载失败: ${error.message}`, 'error');
                // 容器环境fallback：使用当前域名+不同端口
                const currentHost = window.location.hostname;
                const currentProtocol = window.location.protocol;

                NETWORK_CONFIG = {
                    service_a: `${currentProtocol}//${currentHost}:8081`,  // service_a 在8081端口
                    service_b_frontend: `${currentProtocol}//***********:8080`,  // front_end 在8080端口
                    service_b_backend: `${currentProtocol}//***********:18888`,
                    is_public: !currentHost.match(/^(localhost|127\.0\.0\.1|192\.168\.|10\.)/),
                    headers: {}
                };
                addLog('🔄 使用容器环境动态配置', 'info');
                addLog(`当前域名: ${currentHost}`, 'info');
                updateNetworkDisplay();
                return false;
            }
        }

        // 更新网络环境显示
        function updateNetworkDisplay() {
            if (!NETWORK_CONFIG) return;

            document.getElementById('service-a-url').textContent = NETWORK_CONFIG.service_a;
            document.getElementById('service-b-frontend-url').textContent = NETWORK_CONFIG.service_b_frontend;
            document.getElementById('service-b-backend-url').textContent = NETWORK_CONFIG.service_b_backend;
            document.getElementById('deployment-mode').textContent = NETWORK_CONFIG.is_public ? '公网模式' : '本地模式';
            document.getElementById('iframe-url-hint').textContent = `将会创建iframe加载 ${NETWORK_CONFIG.service_b_frontend}`;

            // 更新页面标题信息
            const deploymentInfo = document.getElementById('deployment-info');
            if (deploymentInfo) {
                deploymentInfo.textContent = `动态配置 - ${NETWORK_CONFIG.is_public ? '公网访问' : '本地访问'}`;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            addLog('🚀 宿主机版本页面初始化完成', 'info');

            // 首先加载网络配置
            await loadNetworkConfig();

            if (NETWORK_CONFIG) {
                addLog(`服务A: ${NETWORK_CONFIG.service_a}`, 'info');
                addLog(`服务B前端: ${NETWORK_CONFIG.service_b_frontend}`, 'info');
                addLog(`服务B后端: ${NETWORK_CONFIG.service_b_backend}`, 'info');
            }
            addLog('🎭 权限管理测试环境已就绪', 'info');

            checkServiceConnectivity();
            loadUserInfo(); // 默认加载Admin用户

            // 初始化路由显示
            updateCurrentRouteDisplay();
            updateRouteButtonStates();

            // 设置postMessage监听器
            setupMessageListener();

            // 显示测试提示
            setTimeout(() => {
                addLog('💡 权限管理测试提示:', 'info');
                addLog('1. 选择用户角色 (Admin/Dev)', 'info');
                addLog('2. 生成Token', 'info');
                addLog('3. 选择测试场景或直接访问页面', 'info');
                addLog('4. 体验完整的权限申请→审批→验证流程', 'info');
            }, 2000);
        });
        
        // 检查服务连通性
        async function checkServiceConnectivity() {
            addLog('检查服务连通性...', 'info');
            
            try {
                const response = await fetch('/api/service-b/health');
                
                if (response.ok) {
                    updateStatus('service-b-status', '连接正常', 'success');
                    addLog('服务B (容器) 连接正常', 'success');
                } else {
                    updateStatus('service-b-status', '连接异常', 'error');
                    addLog('服务B (容器) 连接异常', 'error');
                }
            } catch (error) {
                updateStatus('service-b-status', '连接失败', 'error');
                addLog(`服务B连接失败: ${error.message}`, 'error');
            }
        }
        
        // 其他JavaScript函数与原版本相同，但使用宿主机网络配置
        // ... (为了节省空间，这里省略了重复的函数)
        
        // 加载用户信息
        async function loadUserInfo() {
            const userId = document.getElementById('mock-user-select').value;
            const oldUserId = currentUserId;
            currentUserId = userId;
            currentUserInfo = USER_CONFIGS[userId];

            if (currentUserInfo) {
                addLog(`👤 切换到用户: ${currentUserInfo.nick_name} (${currentUserInfo.user_name})`, 'info');

                // 更新用户信息显示
                const userInfoDiv = document.getElementById('current-user-info');
                userInfoDiv.innerHTML = `
                    <strong>${currentUserInfo.nick_name}</strong> (${currentUserInfo.user_name})<br>
                    部门: ${currentUserInfo.dept_name} (ID: ${currentUserInfo.dept_id})<br>
                    角色: ${currentUserInfo.roles.join(', ')}<br>
                    邮箱: ${currentUserInfo.email}<br>
                    <span style="color: #28a745;">✓ 用户信息已加载</span>
                `;

                addLog(`✅ 用户信息加载成功: ${currentUserInfo.nick_name}`, 'success');
                addLog(`📋 部门: ${currentUserInfo.dept_name}, 角色: ${currentUserInfo.roles.join(', ')}`, 'info');

                // 如果切换了用户且有旧token，需要清除并重新生成
                if (oldUserId !== userId && currentToken) {
                    addLog('🔄 检测到用户切换，清除旧Token...', 'warning');
                    currentToken = null;
                    updateStatus('token-status', '已清除', 'warning');
                    addLog('⚠️ 请重新生成Token以匹配新用户', 'warning');

                    // 如果有iframe，也需要销毁重建
                    if (serviceBIframe) {
                        addLog('🔄 销毁旧iframe，用户切换后需要重新加载', 'warning');
                        destroyIframe();
                    }
                }
            } else {
                addLog(`❌ 未找到用户配置: ${userId}`, 'error');
                currentUserInfo = null;
            }
        }

        // 快速切换用户
        async function quickSwitchUser(userType) {
            let userId, userName;

            switch(userType) {
                case 'admin':
                    userId = '1';
                    userName = '系统管理员';
                    break;
                case 'dept_admin':
                    userId = '2';
                    userName = '部门管理员';
                    break;
                case 'dev':
                    userId = '7';
                    userName = '开发人员';
                    break;
                default:
                    userId = '1';
                    userName = '系统管理员';
            }

            addLog(`🔄 快速切换到${userName}...`, 'info');

            document.getElementById('mock-user-select').value = userId;
            await loadUserInfo();

            addLog(`✅ 已切换到${userName} (${currentUserInfo?.user_name})`, 'success');

            // 提示用户重新生成token
            if (!currentToken) {
                addLog('💡 提示: 请点击"生成Token"按钮为新用户生成Token', 'warning');
            }
        }
        
        // 生成Token
        async function generateToken() {
            if (!currentUserInfo) {
                addLog('请先加载用户信息', 'warning');
                await loadUserInfo(); // 尝试加载用户信息
                if (!currentUserInfo) {
                    addLog('无法获取用户信息，Token生成失败', 'error');
                    return;
                }
            }

            addLog(`🔑 为用户 ${currentUserInfo.nick_name} (${currentUserInfo.user_name}) 生成Token...`, 'info');
            addLog(`📋 用户详情: 部门=${currentUserInfo.dept_name}, 角色=${currentUserInfo.roles.join(', ')}`, 'info');

            try {
                // 使用完整的用户信息生成Token
                const tokenData = {
                    ...currentUserInfo,
                    expire_minutes: 60
                };

                addLog(`📤 发送Token请求: ${JSON.stringify(tokenData, null, 2)}`, 'info');

                const response = await fetch('/api/service-b/generate-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(tokenData)
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog(`📥 Token API响应: ${JSON.stringify(result, null, 2)}`, 'info');

                    // 处理不同的响应格式
                    let token = null;
                    if (result.token) {
                        token = result.token;
                    } else if (result.data && result.data.token) {
                        token = result.data.token;
                    } else {
                        throw new Error('响应中未找到token字段');
                    }

                    currentToken = token;
                    updateStatus('token-status', '已生成', 'success');
                    addLog('✅ Token生成成功 (宿主机→容器)', 'success');
                    addLog(`👤 Token用户: ${currentUserInfo.nick_name} (${currentUserInfo.user_name})`, 'success');
                    addLog(`🔑 Token: ${currentToken.substring(0, 30)}...`, 'info');

                    // 检查是否有撤销旧token的信息
                    const revokedCount = result.revoked_old_tokens || (result.data && result.data.revoked_old_tokens) || 0;
                    if (revokedCount > 0) {
                        addLog(`🗑️ 撤销了 ${revokedCount} 个旧Token`, 'warning');
                    }

                    // 验证token中的用户信息
                    try {
                        const tokenParts = currentToken.split('.');
                        if (tokenParts.length === 3) {
                            const payload = JSON.parse(atob(tokenParts[1]));
                            addLog(`🔍 Token验证: 用户ID=${payload.user_id}, 用户名=${payload.user_name}`, 'info');

                            if (payload.user_id !== currentUserInfo.user_id) {
                                addLog(`⚠️ 警告: Token中的用户ID (${payload.user_id}) 与当前用户ID (${currentUserInfo.user_id}) 不匹配!`, 'warning');
                            }
                        }
                    } catch (e) {
                        addLog(`🔍 Token解析失败: ${e.message}`, 'warning');
                    }

                } else {
                    const error = await response.text();
                    throw new Error(`HTTP ${response.status}: ${error}`);
                }
            } catch (error) {
                updateStatus('token-status', '生成失败', 'error');
                addLog(`❌ Token生成失败: ${error.message}`, 'error');
                console.error('Token生成错误详情:', error);
            }
        }
        
        // 撤销Token
        async function revokeToken() {
            addLog('撤销Token (通过宿主机代理)...', 'info');
            
            try {
                data = {"user_id": currentUserId};
                const response = await fetch('/api/service-b/revoke-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: data
                });
                
                if (response.ok) {
                    const result = await response.json();
                    currentToken = null;
                    updateStatus('token-status', '已撤销', 'warning');
                    addLog('Token撤销成功 (宿主机→容器)', 'success');
                    addLog(`撤销了 ${result.revoked_count} 个Token`, 'info');
                } else {
                    const error = await response.text();
                    throw new Error(`HTTP ${response.status}: ${error}`);
                }
            } catch (error) {
                addLog(`Token撤销失败: ${error.message}`, 'error');
            }
        }
        
        // 加载服务B
        async function loadServiceB(route = '/') {
            if (!currentToken) {
                addLog('❌ 请先生成Token', 'warning');
                return;
            }

            if (!currentUserInfo) {
                addLog('❌ 请先选择用户', 'warning');
                return;
            }

            currentRoute = route;
            addLog(`🚀 加载服务B (容器) - 路由: ${route}`, 'info');
            addLog(`👤 当前用户: ${currentUserInfo.nick_name} (${currentUserInfo.user_name})`, 'info');

            try {
                destroyIframe();

                const iframeContainer = document.querySelector('.iframe-container');
                const placeholder = document.getElementById('iframe-placeholder');

                if (!iframeContainer) {
                    addLog('错误: 找不到iframe容器', 'error');
                    return;
                }

                if (placeholder) {
                    placeholder.remove();
                    addLog('移除占位符', 'info');
                }

                serviceBIframe = document.createElement('iframe');
                serviceBIframe.id = 'service-b-iframe';
                // 使用redirect参数指定目标路由
                const redirectPath = route === '/' ? '/' : route;
                serviceBIframe.src = `${NETWORK_CONFIG.service_b_frontend}/?token=${currentToken}&redirect=${redirectPath}`;

                addLog(`🔗 iframe URL: ${serviceBIframe.src}`, 'info');
                addLog(`🎯 目标路由: ${redirectPath}`, 'info');
                addLog(`👤 Token用户: ${currentUserInfo.user_name} (ID: ${currentUserInfo.user_id})`, 'info');

                // 强制设置正确的尺寸
                serviceBIframe.style.width = '100%';
                serviceBIframe.style.maxWidth = '100%';
                serviceBIframe.style.height = '600px';
                serviceBIframe.style.border = '2px solid #007bff';
                serviceBIframe.style.display = 'block';
                serviceBIframe.style.position = 'relative';
                serviceBIframe.style.boxSizing = 'border-box';

                // 完全移除sandbox限制
                // serviceBIframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation');

                serviceBIframe.onload = function() {
                    updateStatus('iframe-status', '已加载', 'success');
                    addLog('服务B iframe加载成功 (容器)', 'success');
                    addLog(`iframe内容窗口: ${serviceBIframe.contentWindow ? '可访问' : '不可访问'}`, 'info');
                };

                serviceBIframe.onerror = function(error) {
                    updateStatus('iframe-status', '加载失败', 'error');
                    addLog('服务B iframe加载失败', 'error');
                    addLog(`错误详情: ${error}`, 'error');
                };

                // 添加额外的事件监听来调试
                serviceBIframe.addEventListener('load', function() {
                    addLog('iframe load事件触发', 'info');
                    try {
                        const doc = serviceBIframe.contentDocument;
                        if (doc) {
                            addLog(`iframe文档标题: ${doc.title || '无标题'}`, 'info');
                            addLog(`iframe文档URL: ${doc.URL}`, 'info');
                        } else {
                            addLog('无法访问iframe文档（可能是跨域限制）', 'warning');
                        }
                    } catch (e) {
                        addLog(`访问iframe文档时出错: ${e.message}`, 'warning');
                    }
                });

                // 确保iframe被正确添加
                iframeContainer.appendChild(serviceBIframe);

                // 立即强制修复尺寸
                setTimeout(() => {
                    serviceBIframe.style.width = '100%';
                    serviceBIframe.style.maxWidth = '100%';
                    serviceBIframe.style.minWidth = '0';
                    serviceBIframe.style.height = '600px';
                    serviceBIframe.style.position = 'relative';
                    serviceBIframe.style.left = '0';
                    serviceBIframe.style.top = '0';
                    addLog('立即强制修复iframe尺寸', 'info');

                    // 更新当前路由显示
                    updateCurrentRouteDisplay();
                }, 10);

                updateStatus('iframe-status', '加载中...', 'warning');
                addLog(`iframe URL: ${serviceBIframe.src}`, 'info');
                addLog('iframe已添加到容器', 'info');

                // 验证iframe是否真的被添加了
                setTimeout(() => {
                    const addedIframe = document.getElementById('service-b-iframe');
                    if (addedIframe) {
                        addLog('确认: iframe元素已存在于DOM中', 'success');

                        // 详细检查iframe状态
                        const rect = addedIframe.getBoundingClientRect();
                        addLog(`iframe位置: x=${rect.x}, y=${rect.y}, width=${rect.width}, height=${rect.height}`, 'info');
                        addLog(`iframe可见性: display=${getComputedStyle(addedIframe).display}, visibility=${getComputedStyle(addedIframe).visibility}`, 'info');
                        addLog(`iframe透明度: opacity=${getComputedStyle(addedIframe).opacity}`, 'info');
                        addLog(`iframe z-index: ${getComputedStyle(addedIframe).zIndex}`, 'info');

                        // 检查iframe内容
                        if (addedIframe.contentWindow) {
                            addLog('iframe contentWindow 存在', 'success');
                        } else {
                            addLog('iframe contentWindow 不存在', 'error');
                        }

                        // 强制修复尺寸和样式
                        addedIframe.style.width = '100%';
                        addedIframe.style.maxWidth = '100%';
                        addedIframe.style.height = '600px';
                        addedIframe.style.border = '3px solid red';
                        addedIframe.style.background = 'yellow';
                        addedIframe.style.position = 'relative';
                        addedIframe.style.boxSizing = 'border-box';
                        addLog('已强制修复iframe尺寸和样式', 'info');
                    } else {
                        addLog('错误: iframe元素未找到', 'error');
                    }
                }, 100);

                // 持续修复iframe尺寸
                let fixCount = 0;
                const fixInterval = setInterval(() => {
                    const iframe = document.getElementById('service-b-iframe');
                    if (iframe && fixCount < 10) {
                        const rect = iframe.getBoundingClientRect();
                        if (rect.width > 1000) { // 如果宽度异常
                            iframe.style.width = '100%';
                            iframe.style.maxWidth = '100%';
                            iframe.style.minWidth = '0';
                            iframe.style.position = 'relative';
                            iframe.style.left = '0';
                            iframe.style.transform = 'none';
                            addLog(`修复异常宽度: ${rect.width} -> 100%`, 'warning');
                        }
                        fixCount++;
                    } else {
                        clearInterval(fixInterval);
                    }
                }, 500);

                // 5秒后再次检查
                setTimeout(() => {
                    const iframe = document.getElementById('service-b-iframe');
                    if (iframe) {
                        addLog('5秒后检查: iframe仍然存在', 'info');
                        const rect = iframe.getBoundingClientRect();
                        addLog(`5秒后检查: iframe尺寸 = ${rect.width}x${rect.height}`, 'info');
                        addLog(`5秒后检查: iframe readyState = ${iframe.contentDocument ? iframe.contentDocument.readyState : '无法访问'}`, 'info');
                    }
                }, 5000);

            } catch (error) {
                updateStatus('iframe-status', '创建失败', 'error');
                addLog(`iframe创建失败: ${error.message}`, 'error');
                console.error('iframe创建错误:', error);
            }
        }
        
        // 销毁iframe
        function destroyIframe() {
            if (serviceBIframe) {
                addLog('销毁iframe...', 'info');
                serviceBIframe.remove();
                serviceBIframe = null;
                currentRoute = '/'; // 重置路由
                updateStatus('iframe-status', '已销毁', 'warning');
                
                const iframeContainer = document.querySelector('.iframe-container');
                const placeholder = document.createElement('div');
                placeholder.id = 'iframe-placeholder';
                placeholder.style.cssText = 'padding: 40px; text-align: center; color: #666; font-size: 1.2em;';
                placeholder.innerHTML = `
                    <p>🔄 iframe已销毁</p>
                    <p style="margin-top: 10px; font-size: 1em; color: #999;">
                        点击"加载服务B"重新创建
                    </p>
                `;
                iframeContainer.appendChild(placeholder);

                // 更新路由显示
                updateCurrentRouteDisplay();
                updateRouteButtonStates();

                addLog('iframe销毁完成', 'success');
            }
        }
        
        // 刷新iframe
        function refreshIframe() {
            if (serviceBIframe) {
                addLog('刷新iframe...', 'info');
                serviceBIframe.src = serviceBIframe.src;
            }
        }

        // 调试iframe
        function debugIframe() {
            addLog('=== iframe调试信息 ===', 'info');

            const iframe = document.getElementById('service-b-iframe');
            if (iframe) {
                addLog('✓ iframe元素存在', 'success');
                addLog(`iframe src: ${iframe.src}`, 'info');

                const rect = iframe.getBoundingClientRect();
                addLog(`iframe尺寸: ${rect.width}x${rect.height}`, 'info');
                addLog(`iframe位置: (${rect.x}, ${rect.y})`, 'info');

                const styles = getComputedStyle(iframe);
                addLog(`display: ${styles.display}`, 'info');
                addLog(`visibility: ${styles.visibility}`, 'info');
                addLog(`opacity: ${styles.opacity}`, 'info');

                // 强制修复尺寸并高亮显示iframe
                iframe.style.width = '100%';
                iframe.style.maxWidth = '100%';
                iframe.style.height = '600px';
                iframe.style.border = '5px solid red';
                iframe.style.background = 'rgba(255, 0, 0, 0.1)';
                iframe.style.position = 'relative';
                iframe.style.boxSizing = 'border-box';
                addLog('已强制修复iframe尺寸并添加红色边框', 'info');

            } else {
                addLog('✗ iframe元素不存在', 'error');
            }

            addLog('=== 调试信息结束 ===', 'info');
        }

        // ========== postMessage 通信功能 ==========

        // 设置消息监听器
        function setupMessageListener() {
            window.addEventListener('message', function(event) {
                // 验证消息来源
                const allowedOrigins = [
                    window.location.origin,
                    'http://***********:2222', // 兼容旧配置
                    'http://localhost:2222'     // 本地开发
                ];

                if (NETWORK_CONFIG && NETWORK_CONFIG.service_b_frontend) {
                    const serviceBOrigin = new URL(NETWORK_CONFIG.service_b_frontend).origin;
                    allowedOrigins.push(serviceBOrigin);
                }

                if (!allowedOrigins.includes(event.origin)) {
                    addLog(`⚠️ 拒绝来自未知源的消息: ${event.origin}`, 'warning');
                    addLog(`允许的来源: ${allowedOrigins.join(', ')}`, 'info');
                    return;
                }

                addLog(`📨 收到iframe消息: ${JSON.stringify(event.data)}`, 'info');

                // 处理消息（仅核心功能）
                switch (event.data.type) {
                    case 'SESSION_CLEARED':
                        handleSessionCleared(event.data);
                        break;
                    default:
                        addLog(`📨 收到消息: ${event.data.type}`, 'info');
                }
            });

            addLog('📡 postMessage监听器已设置', 'info');
        }

        // 消息处理函数（精简版）
        function handleSessionCleared(data) {
            addLog('🧹 服务B会话已清理完成', 'success');
            updateStatus('iframe-status', '会话已清理', 'warning');
        }

        // 发送消息给iframe
        function sendMessageToIframe(message) {
            if (serviceBIframe && serviceBIframe.contentWindow) {
                try {
                    // 发送到服务B前端
                    const targetOrigin = NETWORK_CONFIG ? new URL(NETWORK_CONFIG.service_b_frontend).origin : '*';
                    serviceBIframe.contentWindow.postMessage(message, targetOrigin);
                    addLog(`📤 已发送消息: ${message.type}`, 'info');
                    return true;
                } catch (e) {
                    addLog(`📤 发送消息失败: ${e.message}`, 'error');
                    return false;
                }
            } else {
                addLog('❌ iframe未准备就绪，无法发送消息', 'warning');
                return false;
            }
        }

        // 模拟用户退出（完整版）
        async function simulateUserLogout() {
            if (!serviceBIframe) {
                addLog('请先加载服务B', 'warning');
                return;
            }

            addLog('🚪 模拟用户退出...', 'info');

            try {
                // 1. 先发送清理会话消息给服务B
                const message = {
                    type: 'CLEAR_SESSION',
                    timestamp: Date.now(),
                    serviceAOrigin: window.location.origin
                };

                if (sendMessageToIframe(message)) {
                    addLog('📤 已发送CLEAR_SESSION消息给服务B', 'info');
                    updateStatus('iframe-status', '清理中...', 'warning');
                }

                // 2. 调用服务A的token撤销接口
                if (currentToken) {
                    addLog('🔑 撤销服务A的Token...', 'info');
                    await revokeToken();
                    addLog('✅ Token撤销完成', 'success');
                } else {
                    addLog('⚠️ 没有Token需要撤销', 'warning');
                }

                // 3. 清理本地状态
                currentToken = null;
                updateStatus('token-status', '已清理', 'warning');
                updateStatus('iframe-status', '用户已退出', 'warning');

                addLog('🎉 用户退出流程完成', 'success');

            } catch (error) {
                addLog(`❌ 用户退出过程中出错: ${error.message}`, 'error');
                // 即使出错也要清理本地状态
                currentToken = null;
                updateStatus('token-status', '已清理', 'warning');
            }
        }

        // ES-Search 路由配置
        const ES_SEARCH_ROUTES = {
            '/': { name: '首页', description: '搜索入口页面', requiresAuth: false },
            '/search': { name: '搜索页面', description: '文档搜索和权限检查', requiresAuth: true },
            '/assembly': { name: '汇编页面', description: '文档汇编功能', requiresAuth: true },
            '/audit': { name: '审核页面', description: '权限申请审核', requiresAuth: true },
            '/permissions': { name: '权限管理', description: '权限管理和申请', requiresAuth: true },
            '/preview': { name: '预览页面', description: '文档预览', requiresAuth: true },
            '/info': { name: '项目信息', description: '项目信息搜索', requiresAuth: true },
            '/desc': { name: '项目详情', description: '项目详情展示', requiresAuth: true }
        };

        // 路由切换功能
        function switchRoute(route) {
            if (!serviceBIframe) {
                addLog('❌ 请先加载服务B', 'warning');
                return;
            }

            if (!currentToken) {
                addLog('❌ 请先生成Token', 'warning');
                return;
            }

            // 验证路由是否存在
            if (!ES_SEARCH_ROUTES[route]) {
                addLog(`❌ 未知路由: ${route}`, 'error');
                return;
            }

            const routeInfo = ES_SEARCH_ROUTES[route];
            addLog(`🔄 切换到路由: ${route} (${routeInfo.name})`, 'info');
            addLog(`📋 路由描述: ${routeInfo.description}`, 'info');

            // 检查权限要求
            if (routeInfo.requiresAuth && !currentToken) {
                addLog(`⚠️ 路由 ${route} 需要认证，请先生成Token`, 'warning');
                return;
            }

            currentRoute = route;

            // 构建目标URL，确保路由正确传递
            const redirectPath = route;
            let newUrl;

            // 对于所有路由，都使用直接路径访问的方式
            // 这样前端可以从 window.location.pathname 中正确提取目标路由
            newUrl = `${NETWORK_CONFIG.service_b_frontend}${redirectPath}?token=${currentToken}`;

            addLog(`🔗 构建URL: ${redirectPath} (直接路径访问)`, 'info');

            // 更新iframe的src
            serviceBIframe.src = newUrl;
            addLog(`🔗 iframe URL已更新`, 'info');
            addLog(`🎯 目标路由: ${redirectPath}`, 'info');
            addLog(`👤 当前用户: ${currentUserInfo?.nick_name} (${currentUserInfo?.user_name})`, 'info');

            // 更新显示
            updateCurrentRouteDisplay();
            updateRouteButtonStates();

            // 更新iframe状态
            updateStatus('iframe-status', '路由切换中...', 'warning');
            document.getElementById('iframe-route-status').textContent = `切换到 ${routeInfo.name}`;

            addLog(`✅ 路由切换完成: ${route}`, 'success');
        }

        // 更新当前路由显示
        function updateCurrentRouteDisplay() {
            const routeDisplay = document.getElementById('current-route');
            const routeStatusDisplay = document.getElementById('iframe-route-status');

            if (routeDisplay) {
                const routeInfo = ES_SEARCH_ROUTES[currentRoute];
                const displayText = routeInfo ? `${currentRoute} (${routeInfo.name})` : currentRoute;
                routeDisplay.textContent = displayText;
                routeDisplay.style.color = serviceBIframe ? '#28a745' : '#6c757d';
            }

            if (routeStatusDisplay) {
                if (serviceBIframe) {
                    const routeInfo = ES_SEARCH_ROUTES[currentRoute];
                    routeStatusDisplay.textContent = routeInfo ? `已加载 ${routeInfo.name}` : '已加载';
                    routeStatusDisplay.style.color = '#28a745';
                } else {
                    routeStatusDisplay.textContent = '未创建';
                    routeStatusDisplay.style.color = '#6c757d';
                }
            }
        }

        // 更新路由按钮状态
        function updateRouteButtonStates() {
            // 所有路由按钮ID列表
            const routeButtons = [
                'route-index', 'route-search', 'route-assembly', 'route-audit',
                'route-info', 'route-desc', 'route-preview'
            ];

            // 重置所有按钮样式
            routeButtons.forEach(buttonId => {
                const button = document.getElementById(buttonId);
                if (button) {
                    button.className = 'btn';
                    button.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                }
            });

            // 高亮当前路由按钮
            let activeButtonId = null;
            switch (currentRoute) {
                case '/':
                    activeButtonId = 'route-index';
                    break;
                case '/search':
                    activeButtonId = 'route-search';
                    break;
                case '/assembly':
                    activeButtonId = 'route-assembly';
                    break;
                case '/audit':
                    activeButtonId = 'route-audit';
                    break;
                case '/info':
                    activeButtonId = 'route-info';
                    break;
                case '/desc':
                    activeButtonId = 'route-desc';
                    break;
                case '/preview':
                    activeButtonId = 'route-preview';
                    break;
            }

            if (activeButtonId) {
                const activeButton = document.getElementById(activeButtonId);
                if (activeButton) {
                    activeButton.className = 'btn success';
                    activeButton.style.background = 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)';
                }
            }
        }

        // 强制路由切换（重新加载iframe）
        function forceRouteSwitch(route) {
            if (!currentToken) {
                addLog('❌ 请先生成Token', 'warning');
                return;
            }

            // 验证路由是否存在
            if (!ES_SEARCH_ROUTES[route]) {
                addLog(`❌ 未知路由: ${route}`, 'error');
                return;
            }

            const routeInfo = ES_SEARCH_ROUTES[route];
            addLog(`🔄 强制切换到路由: ${route} (${routeInfo.name})`, 'info');
            addLog(`📋 路由描述: ${routeInfo.description}`, 'info');
            addLog(`🔄 将重新创建iframe以确保完全刷新`, 'warning');

            // 检查权限要求
            if (routeInfo.requiresAuth && !currentToken) {
                addLog(`⚠️ 路由 ${route} 需要认证，请先生成Token`, 'warning');
                return;
            }

            // 销毁当前iframe并重新加载指定路由
            destroyIframe();

            // 延迟一点时间再加载新路由
            setTimeout(() => {
                loadServiceB(route);
                addLog(`✅ 强制路由切换完成: ${route} (${routeInfo.name})`, 'success');
            }, 500);
        }

        // ES-Search 页面快捷访问函数
        function openPermissionsPage() {
            addLog('🔐 打开审核页面 (权限管理)...', 'info');
            switchRoute('/audit');
        }



        function openSearchPage() {
            addLog('🔍 打开搜索页面...', 'info');
            switchRoute('/search');
        }

        function openAssemblyPage() {
            addLog('📋 打开汇编页面...', 'info');
            switchRoute('/assembly');
        }

        function openHomePage() {
            addLog('🏠 打开首页...', 'info');
            switchRoute('/');
        }

        function openProjectInfoPage() {
            addLog('📊 打开项目信息页面...', 'info');
            switchRoute('/info');
        }

        function openPreviewPage() {
            addLog('👁️ 打开预览页面...', 'info');
            addLog('⚠️ 注意: 预览页面通常需要从搜索页面跳转', 'warning');
            switchRoute('/preview');
        }

        // 路由测试函数
        function testAllRoutes() {
            if (!currentToken) {
                addLog('❌ 请先生成Token', 'warning');
                return;
            }

            addLog('🧪 开始测试所有ES-Search路由...', 'info');

            const routes = Object.keys(ES_SEARCH_ROUTES);
            let currentIndex = 0;

            function testNextRoute() {
                if (currentIndex >= routes.length) {
                    addLog('✅ 所有路由测试完成', 'success');
                    return;
                }

                const route = routes[currentIndex];
                const routeInfo = ES_SEARCH_ROUTES[route];

                addLog(`🔄 测试路由 ${currentIndex + 1}/${routes.length}: ${route} (${routeInfo.name})`, 'info');
                switchRoute(route);

                currentIndex++;

                // 3秒后测试下一个路由
                setTimeout(testNextRoute, 3000);
            }

            testNextRoute();
        }

        // 获取当前路由信息
        function getCurrentRouteInfo() {
            if (!currentRoute) {
                addLog('❌ 当前没有活动路由', 'warning');
                return;
            }

            const routeInfo = ES_SEARCH_ROUTES[currentRoute];
            if (routeInfo) {
                addLog('📋 当前路由信息:', 'info');
                addLog(`   路径: ${currentRoute}`, 'info');
                addLog(`   名称: ${routeInfo.name}`, 'info');
                addLog(`   描述: ${routeInfo.description}`, 'info');
                addLog(`   需要认证: ${routeInfo.requiresAuth ? '是' : '否'}`, 'info');
                addLog(`   iframe状态: ${serviceBIframe ? '已加载' : '未加载'}`, 'info');

                // 检查iframe的实际URL
                if (serviceBIframe) {
                    addLog(`   iframe URL: ${serviceBIframe.src}`, 'info');

                    // 尝试检查iframe内容
                    try {
                        if (serviceBIframe.contentWindow && serviceBIframe.contentWindow.location) {
                            addLog(`   iframe实际路径: ${serviceBIframe.contentWindow.location.pathname}`, 'info');
                        }
                    } catch (e) {
                        addLog(`   iframe内容检查失败: ${e.message}`, 'warning');
                    }
                }
            } else {
                addLog(`❌ 未知路由: ${currentRoute}`, 'error');
            }
        }

        // 专门用于调试审核页面的函数
        function debugAuditPage() {
            addLog('🔍 ===== 审核页面调试 =====', 'info');

            if (!currentToken) {
                addLog('❌ 没有Token，无法访问审核页面', 'error');
                return;
            }

            if (!currentUserInfo) {
                addLog('❌ 没有用户信息', 'error');
                return;
            }

            addLog(`👤 当前用户: ${currentUserInfo.nick_name} (${currentUserInfo.user_name})`, 'info');
            addLog(`🔑 用户角色: ${currentUserInfo.roles.join(', ')}`, 'info');
            addLog(`🔐 Token状态: ${currentToken ? '有效' : '无效'}`, 'info');

            // 检查用户是否有管理员权限
            const isAdmin = currentUserInfo.roles.includes('admin');
            addLog(`👑 管理员权限: ${isAdmin ? '是' : '否'}`, 'info');

            if (isAdmin) {
                addLog('✅ 用户有管理员权限，应该能看到"待审批"标签页', 'success');
            } else {
                addLog('⚠️ 用户没有管理员权限，只能看到"我的申请"标签页', 'warning');
            }

            // 强制跳转到审核页面
            addLog('🔄 强制跳转到审核页面...', 'info');
            forceRouteSwitch('/audit');

            addLog('🔍 ===== 调试结束 =====', 'info');
        }

        // 权限管理测试场景
        async function testScenario(scenario) {
            if (!currentUserInfo) {
                addLog('请先选择用户', 'warning');
                return;
            }

            addLog(`🎭 开始测试场景: ${scenario}`, 'info');

            switch (scenario) {
                case 'search':
                    await testSearchScenario();
                    break;
                case 'permissions':
                    await testPermissionsScenario();
                    break;
                case 'full':
                    await testFullWorkflowScenario();
                    break;
                default:
                    addLog(`未知测试场景: ${scenario}`, 'error');
            }
        }

        // 搜索页面测试场景
        async function testSearchScenario() {
            addLog('📋 搜索页面测试场景开始...', 'info');
            addLog(`当前用户: ${currentUserInfo.nick_name} (${currentUserInfo.user_name})`, 'info');

            // 1. 生成Token
            if (!currentToken) {
                await generateToken();
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // 2. 加载搜索页面
            if (currentToken) {
                addLog('🔍 加载搜索页面，测试文档权限检查...', 'info');
                switchRoute('/search');

                addLog('💡 测试提示:', 'info');
                addLog('1. 在搜索页面点击文档链接', 'info');
                addLog('2. 系统会自动检查权限', 'info');
                addLog('3. 如果没有权限，会弹出申请对话框', 'info');
                addLog('4. 填写申请理由并提交', 'info');
            }
        }

        // 权限管理测试场景
        async function testPermissionsScenario() {
            addLog('📋 权限管理测试场景开始...', 'info');
            addLog(`当前用户: ${currentUserInfo.nick_name} (${currentUserInfo.user_name})`, 'info');

            // 1. 生成Token
            if (!currentToken) {
                await generateToken();
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // 2. 加载权限管理页面
            if (currentToken) {
                addLog('🔐 加载权限管理页面...', 'info');
                switchRoute('/permissions');

                if (currentUserInfo.roles.includes('admin')) {
                    addLog('💡 管理员测试提示:', 'info');
                    addLog('1. 查看"待审批"标签页', 'info');
                    addLog('2. 审批用户的权限申请', 'info');
                    addLog('3. 可以批准或拒绝申请', 'info');
                } else {
                    addLog('💡 普通用户测试提示:', 'info');
                    addLog('1. 查看"我的申请"标签页', 'info');
                    addLog('2. 查看申请状态和历史', 'info');
                    addLog('3. 可以撤回待审批的申请', 'info');
                }
            }
        }

        // 完整工作流测试场景
        async function testFullWorkflowScenario() {
            addLog('📋 完整权限管理工作流测试开始...', 'info');

            addLog('🎯 测试步骤:', 'info');
            addLog('1. 先以Dev用户身份申请权限', 'info');
            addLog('2. 再以Admin用户身份审批权限', 'info');
            addLog('3. 最后验证权限生效', 'info');

            // 自动切换到Dev用户开始测试
            await quickSwitchUser('dev');
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testSearchScenario();

            addLog('⏳ 请在搜索页面提交权限申请后，点击"以Admin身份测试"按钮继续...', 'warning');
        }

        // 以Admin身份测试
        async function testAsAdmin() {
            addLog('👑 切换到Admin身份进行测试...', 'info');
            await quickSwitchUser('admin');
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testPermissionsScenario();
        }

        // 以Dev身份测试
        async function testAsDev() {
            addLog('👨‍💻 切换到Dev身份进行测试...', 'info');
            await quickSwitchUser('dev');
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testSearchScenario();
        }

        // 测试文档权限功能
        async function testDocumentPermission() {
            if (!currentToken) {
                addLog('请先生成Token', 'warning');
                return;
            }

            addLog('🔍 测试文档权限检查...', 'info');

            try {
                // 模拟权限检查请求
                const response = await fetch('/api/service-b/check-permission', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-User-ID': currentUserId
                    },
                    body: JSON.stringify({
                        document_urls: ['/test/sample_document.pdf'],
                        permission_type: 'read'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('✅ 权限检查成功', 'success');
                    addLog(`可访问文档: ${result.accessible_urls?.length || 0} 个`, 'info');
                    addLog(`拒绝访问文档: ${result.denied_urls?.length || 0} 个`, 'info');
                } else {
                    const error = await response.text();
                    addLog(`❌ 权限检查失败: ${error}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 权限检查异常: ${error.message}`, 'error');
            }
        }
        
        // 连通性测试
        async function testConnectivity() {
            addLog('开始连通性测试 (宿主机模式)...', 'info');
            
            const services = [
                { name: '服务A健康检查', url: '/health' },
                { name: '服务B健康检查 (代理)', url: '/api/service-b/health' },
                { name: '服务B前端 (直连)', url: NETWORK_CONFIG ? NETWORK_CONFIG.service_b_frontend : '/service-b' }
            ];
            
            for (const service of services) {
                try {
                    const response = await fetch(service.url);
                    if (response.ok) {
                        addLog(`${service.name}: 连接正常`, 'success');
                    } else {
                        addLog(`${service.name}: HTTP ${response.status}`, 'warning');
                    }
                } catch (error) {
                    addLog(`${service.name}: 连接失败`, 'error');
                }
            }
        }
        
        // 安全测试
        async function testSecurity() {
            addLog('开始安全测试 (宿主机→容器)...', 'info');
            
            // 测试Token生成和撤销
            await generateToken();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await revokeToken();
            
            addLog('安全测试完成', 'info');
        }
        
        // 工具函数
        function addLog(message, level = 'info') {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            const timestamp = new Date().toLocaleString();
            const colors = {
                info: '#4facfe',
                success: '#48bb78',
                warning: '#ed8936',
                error: '#f56565'
            };
            
            logEntry.innerHTML = `
                <span style="color: #a0aec0;">[${timestamp}]</span>
                <span style="color: ${colors[level]};">[${level.toUpperCase()}]</span>
                <span>${message}</span>
            `;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function updateStatus(elementId, text, status) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = text;
                element.className = `status-value status-${status}`;
            }
        }
        
        function clearLogs() {
            const logContainer = document.getElementById('log-container');
            logContainer.innerHTML = '';
            addLog('日志已清空', 'info');
        }

        // ===== 简化的 iframe 通信支持 =====

        // 监听来自 front_end iframe 的消息
        window.addEventListener('message', function(event) {
            // 验证消息来源
            if (event.origin === 'http://***********:8080' || event.origin === 'http://localhost:8080') {
                addLog(`📥 收到front_end消息: ${JSON.stringify(event.data)}`, 'info');

                // 处理不同类型的消息
                switch(event.data.type) {
                    case 'route-changed':
                        addLog(`🔄 front_end路由已切换: ${event.data.route}`, 'success');
                        updateCurrentRouteDisplay();
                        break;

                    case 'user-status':
                        addLog(`👤 front_end用户状态: ${event.data.user}`, 'info');
                        break;

                    case 'iframe-ready':
                        addLog(`✅ front_end iframe已就绪`, 'success');
                        updateStatus('iframe-status', '已就绪', 'success');
                        break;

                    case 'error':
                        addLog(`❌ front_end错误: ${event.data.message}`, 'error');
                        break;

                    default:
                        addLog(`❓ 未知消息类型: ${event.data.type}`, 'warning');
                }
            } else {
                addLog(`⚠️ 收到来自未知源的消息: ${event.origin}`, 'warning');
            }
        });

        // 向 front_end iframe 发送消息的辅助函数
        function sendMessageToFrontEnd(message) {
            if (serviceBIframe && serviceBIframe.contentWindow) {
                serviceBIframe.contentWindow.postMessage(message, 'http://***********:8080');
                addLog(`📤 发送消息到front_end: ${JSON.stringify(message)}`, 'info');
            } else {
                addLog(`❌ 无法发送消息，iframe未就绪`, 'error');
            }
        }

        // 测试通信的函数
        function testIframeCommunication() {
            sendMessageToFrontEnd({
                type: 'ping',
                timestamp: Date.now(),
                message: 'Hello from service_a'
            });
        }

    </script>
</body>
</html>
