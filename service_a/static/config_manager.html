<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service A - 配置管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .config-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ Service A 配置管理器</h1>
            <p>管理服务B的前后端配置参数</p>
        </div>

        <div class="content">
            <!-- 当前配置显示 -->
            <div class="section">
                <h3>📋 当前配置</h3>
                <button class="btn" onclick="loadConfig()">🔄 刷新配置</button>
                <button class="btn success" onclick="reloadConfig()">♻️ 重新加载配置文件</button>
                <div id="config-display" class="config-display">点击"刷新配置"加载当前配置...</div>
            </div>

            <!-- 服务B配置更新 -->
            <div class="section">
                <h3>🔧 更新服务B配置</h3>
                <div class="grid">
                    <div>
                        <h4>前端配置</h4>
                        <div class="form-group">
                            <label>主机地址:</label>
                            <input type="text" id="frontend-host" placeholder="***********">
                        </div>
                        <div class="form-group">
                            <label>端口:</label>
                            <input type="number" id="frontend-port" placeholder="2222">
                        </div>
                        <div class="form-group">
                            <label>协议:</label>
                            <input type="text" id="frontend-protocol" placeholder="http">
                        </div>
                    </div>
                    <div>
                        <h4>后端配置</h4>
                        <div class="form-group">
                            <label>主机地址:</label>
                            <input type="text" id="backend-host" placeholder="localhost">
                        </div>
                        <div class="form-group">
                            <label>端口:</label>
                            <input type="number" id="backend-port" placeholder="18888">
                        </div>
                        <div class="form-group">
                            <label>协议:</label>
                            <input type="text" id="backend-protocol" placeholder="http">
                        </div>
                    </div>
                </div>
                <button class="btn warning" onclick="updateServiceBConfig()">💾 更新配置</button>
                <button class="btn" onclick="fillCurrentConfig()">📝 填入当前配置</button>
            </div>

            <!-- 网络配置测试 -->
            <div class="section">
                <h3>🌐 网络配置测试</h3>
                <button class="btn" onclick="testNetworkConfig()">🧪 测试网络配置</button>
                <button class="btn" onclick="testServiceBHealth()">💚 测试服务B连通性</button>
                <div id="test-results" class="config-display" style="margin-top: 15px;">测试结果将显示在这里...</div>
            </div>

            <!-- 状态显示 -->
            <div id="status-message"></div>
        </div>
    </div>

    <script>
        let currentConfig = null;

        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-message');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            
            // 3秒后自动隐藏
            setTimeout(() => {
                statusDiv.textContent = '';
                statusDiv.className = '';
            }, 3000);
        }

        // 加载当前配置
        async function loadConfig() {
            try {
                const response = await fetch('/api/config');
                const data = await response.json();
                currentConfig = data;
                
                document.getElementById('config-display').textContent = JSON.stringify(data, null, 2);
                showStatus('配置加载成功', 'success');
            } catch (error) {
                showStatus(`加载配置失败: ${error.message}`, 'error');
            }
        }

        // 重新加载配置文件
        async function reloadConfig() {
            try {
                const response = await fetch('/api/config/reload', { method: 'POST' });
                const data = await response.json();
                
                showStatus(data.message, 'success');
                await loadConfig(); // 重新加载显示
            } catch (error) {
                showStatus(`重新加载配置失败: ${error.message}`, 'error');
            }
        }

        // 填入当前配置到表单
        function fillCurrentConfig() {
            if (!currentConfig) {
                showStatus('请先加载当前配置', 'error');
                return;
            }

            const serviceBConfig = currentConfig.config.service_b;
            
            // 填入前端配置
            document.getElementById('frontend-host').value = serviceBConfig.frontend.host;
            document.getElementById('frontend-port').value = serviceBConfig.frontend.port;
            document.getElementById('frontend-protocol').value = serviceBConfig.frontend.protocol;
            
            // 填入后端配置
            document.getElementById('backend-host').value = serviceBConfig.backend.host;
            document.getElementById('backend-port').value = serviceBConfig.backend.port;
            document.getElementById('backend-protocol').value = serviceBConfig.backend.protocol;
            
            showStatus('当前配置已填入表单', 'success');
        }

        // 更新服务B配置
        async function updateServiceBConfig() {
            const configUpdate = {
                frontend: {
                    host: document.getElementById('frontend-host').value,
                    port: parseInt(document.getElementById('frontend-port').value),
                    protocol: document.getElementById('frontend-protocol').value
                },
                backend: {
                    host: document.getElementById('backend-host').value,
                    port: parseInt(document.getElementById('backend-port').value),
                    protocol: document.getElementById('backend-protocol').value
                }
            };

            // 构建URL
            configUpdate.frontend.url = `${configUpdate.frontend.protocol}://${configUpdate.frontend.host}:${configUpdate.frontend.port}`;
            configUpdate.backend.url = `${configUpdate.backend.protocol}://${configUpdate.backend.host}:${configUpdate.backend.port}`;

            try {
                const response = await fetch('/api/config/service-b', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(configUpdate)
                });

                const data = await response.json();
                showStatus(data.message, 'success');
                await loadConfig(); // 重新加载显示
            } catch (error) {
                showStatus(`更新配置失败: ${error.message}`, 'error');
            }
        }

        // 测试网络配置
        async function testNetworkConfig() {
            try {
                const response = await fetch('/api/network-config');
                const data = await response.json();
                
                document.getElementById('test-results').textContent = JSON.stringify(data, null, 2);
                showStatus('网络配置测试完成', 'success');
            } catch (error) {
                showStatus(`网络配置测试失败: ${error.message}`, 'error');
            }
        }

        // 测试服务B连通性
        async function testServiceBHealth() {
            try {
                const response = await fetch('/api/service-b/health');
                const data = await response.json();
                
                document.getElementById('test-results').textContent = JSON.stringify(data, null, 2);
                showStatus('服务B连通性测试成功', 'success');
            } catch (error) {
                document.getElementById('test-results').textContent = `连通性测试失败: ${error.message}`;
                showStatus(`服务B连通性测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动加载配置
        window.onload = function() {
            loadConfig();
        };
    </script>
</body>
</html>
