# 🔄 ES-Search 路由集成文档

## 📋 概述

为 Service_A 测试页面添加了完整的 ES-Search 路由跳转功能，支持所有 ES-Search 项目中的页面路由。

## 🗺️ ES-Search 路由映射

### 📊 完整路由表

| 路由路径 | 页面名称 | 功能描述 | 权限要求 | 对应组件 |
|---------|---------|---------|---------|---------|
| `/` | 首页 | 搜索入口页面 | 无 | `index.vue` |
| `/search` | 搜索页面 | 文档搜索和权限检查 | 需要登录 | `search.vue` |
| `/assembly` | 汇编页面 | 文档汇编功能 | 需要登录 | `assembly.vue` |
| `/audit` | 审核页面 | 权限申请审核 | 需要登录 | `audit.vue` |
| `/preview` | 预览页面 | 文档预览 | 需要权限 | `preview.vue` |
| `/info` | 项目信息 | 项目信息搜索 | 需要登录 | `projectInfo.vue` |
| `/desc` | 项目详情 | 项目详情展示 | 需要登录 | `projectInfoDesc.vue` |

## 🔧 新增功能

### 1. **智能路由切换**

#### 普通切换 (`switchRoute`)
- 使用 `redirect` 参数指定目标路由
- 保持 iframe 状态，快速切换
- 自动验证路由有效性和权限要求

#### 强制切换 (`forceRouteSwitch`)
- 重新创建 iframe，完全刷新
- 适用于需要清理状态的场景
- 确保页面完全重新加载

### 2. **路由状态管理**

#### 按钮状态同步
- 自动高亮当前活动路由按钮
- 支持所有 ES-Search 路由的按钮状态
- 视觉反馈清晰明确

#### 路由信息显示
- 实时显示当前路由和页面名称
- iframe 状态监控
- 路由切换进度提示

### 3. **便捷访问函数**

```javascript
// 快速访问函数
openHomePage()          // 打开首页
openSearchPage()        // 打开搜索页面
openAssemblyPage()      // 打开汇编页面
openPermissionsPage()   // 打开审核页面 (权限管理)
openProjectInfoPage()   // 打开项目信息页面
openPreviewPage()       // 打开预览页面

// 测试工具函数
testAllRoutes()         // 自动测试所有路由
getCurrentRouteInfo()   // 获取当前路由详细信息
```

### 4. **路由验证机制**

#### 权限检查
- 自动检查路由是否需要认证
- Token 状态验证
- 用户权限确认

#### 路由有效性验证
- 检查路由是否在 ES-Search 中存在
- 防止无效路由跳转
- 错误提示和日志记录

## 🎯 使用方法

### 基本操作流程

1. **选择用户角色**
   ```
   选择用户 → Admin 或 Dev
   加载用户信息 → 确认用户详情
   ```

2. **生成认证Token**
   ```
   点击"生成Token" → 获取JWT Token
   验证Token有效性 → 确认用户身份
   ```

3. **路由切换测试**
   ```
   选择目标路由 → 点击对应按钮
   iframe自动跳转 → 验证页面加载
   检查功能正常 → 确认路由工作
   ```

### 高级测试功能

#### 自动化路由测试
```javascript
// 测试所有路由（每个路由停留3秒）
testAllRoutes()
```

#### 路由信息查询
```javascript
// 获取当前路由的详细信息
getCurrentRouteInfo()
```

#### 强制刷新测试
```javascript
// 强制重新加载特定路由
forceRouteSwitch('/search')
```

## 📊 界面布局

### 路由切换区域

#### 主要功能按钮
- 首页 (/)
- 搜索 (/search) 
- 汇编 (/assembly)
- 审核 (/audit)

#### 项目信息按钮
- 项目信息 (/info)
- 项目详情 (/desc)
- 预览 (/preview)

#### 强制切换按钮
- 强制首页、强制搜索、强制汇编、强制审核

### 路由状态显示
```
当前路由: /search (搜索页面)
iframe状态: 已加载 搜索页面
```

### 路由测试工具
- 快速访问按钮
- 路由信息查询
- 自动化测试工具

## 🔍 技术实现

### 路由配置对象
```javascript
const ES_SEARCH_ROUTES = {
    '/': { name: '首页', description: '搜索入口页面', requiresAuth: false },
    '/search': { name: '搜索页面', description: '文档搜索和权限检查', requiresAuth: true },
    '/assembly': { name: '汇编页面', description: '文档汇编功能', requiresAuth: true },
    '/audit': { name: '审核页面', description: '权限申请审核', requiresAuth: true },
    '/preview': { name: '预览页面', description: '文档预览', requiresAuth: true },
    '/info': { name: '项目信息', description: '项目信息搜索', requiresAuth: true },
    '/desc': { name: '项目详情', description: '项目详情展示', requiresAuth: true }
};
```

### iframe URL 构建
```javascript
const newUrl = `${NETWORK_CONFIG.service_b_frontend}/?token=${currentToken}&redirect=${encodeURIComponent(redirectPath)}`;
```

### 按钮状态管理
- 动态更新按钮样式
- 高亮当前活动路由
- 支持所有路由按钮

## 🎯 测试场景

### 1. **基础路由测试**
- 测试每个路由的正常跳转
- 验证页面加载和显示
- 确认路由参数传递

### 2. **权限验证测试**
- 不同用户角色的路由访问
- Token 过期时的路由行为
- 无权限路由的处理

### 3. **状态同步测试**
- 路由切换时的按钮状态
- iframe 状态的实时更新
- 路由信息的准确显示

### 4. **错误处理测试**
- 无效路由的处理
- 网络错误时的降级
- iframe 加载失败的恢复

## 📝 注意事项

1. **路由同步**: 基于 ES-Search 实际路由配置，确保一致性
2. **权限控制**: 自动检查路由权限要求，防止无权限访问
3. **状态管理**: 实时同步路由状态和按钮显示
4. **错误处理**: 完善的错误提示和日志记录
5. **用户体验**: 清晰的视觉反馈和操作指引

## 🚀 后续优化

1. **路由历史**: 添加路由访问历史记录
2. **书签功能**: 支持常用路由的快速访问
3. **批量测试**: 支持自定义路由测试序列
4. **性能监控**: 添加路由切换性能统计
5. **深度链接**: 支持直接访问特定路由的深度链接

---

**更新时间**: 2025-01-04  
**版本**: v1.0  
**兼容性**: ES-Search 路由系统 v1.0
