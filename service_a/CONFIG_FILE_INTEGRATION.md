# ⚙️ Service A 配置文件集成文档

## 📋 概述

将 Service A 的 `/api/network-config` 接口修改为基于配置文件的方式，实现服务B前后端参数的集中配置管理。

## 🔧 主要修改

### 1. **新增配置文件**

**文件**: `service_a/config.json`

```json
{
  "service_a": {
    "name": "Service A - 配置化安全对接系统",
    "version": "3.0.0-host",
    "host": "0.0.0.0",
    "port": 8080,
    "base_url": "http://***********:8080"
  },
  "service_b": {
    "frontend": {
      "host": "***********",
      "port": 2222,
      "protocol": "http",
      "url": "http://***********:2222"
    },
    "backend": {
      "host": "localhost",
      "port": 18888,
      "protocol": "http",
      "url": "http://localhost:18888",
      "api_key": "service_a_to_b_secret_key_2024"
    }
  },
  "network": {
    "deployment_mode": "host",
    "environment": "development",
    "cors_origins": ["*"],
    "timeout": 30
  }
}
```

### 2. **配置加载机制**

#### 配置文件加载函数
```python
def load_config() -> Dict[str, Any]:
    """加载配置文件，支持默认配置回退"""
    config_path = Path(__file__).parent / "config.json"
    
    if not config_path.exists():
        return get_default_config()
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"加载配置文件失败: {e}")
        return get_default_config()
```

#### 环境变量支持
支持以下环境变量覆盖配置文件：
- `SERVICE_B_BASE_URL` - 覆盖后端URL
- `SERVICE_B_API_KEY` - 覆盖API密钥
- `SERVICE_B_FRONTEND_URL` - 覆盖前端URL
- `SERVICE_B_BACKEND_URL` - 覆盖后端URL

### 3. **API 接口增强**

#### `/api/network-config` 接口
**修改前**：动态计算URL
**修改后**：基于配置文件返回

```python
@app.get("/api/network-config")
async def get_network_config(request: Request):
    """获取网络配置，供前端使用 - 基于配置文件"""
    service_b_urls = get_service_b_urls()
    
    return {
        "service_a": {...},
        "service_b_frontend": service_b_urls['frontend'],
        "service_b_backend": service_b_urls['backend'],
        "service_b": {
            "frontend": {...},
            "backend": {...}
        },
        "network": {...},
        "config_source": "config.json"
    }
```

#### 新增配置管理接口

| 接口 | 方法 | 功能 |
|------|------|------|
| `/api/config` | GET | 查看当前配置 |
| `/api/config/reload` | POST | 重新加载配置文件 |
| `/api/config/service-b` | PUT | 更新服务B配置 |

### 4. **配置管理界面**

**文件**: `service_a/static/config_manager.html`

#### 功能特性
- 📋 **配置查看**: 实时显示当前配置
- 🔄 **配置重载**: 重新加载配置文件
- ✏️ **配置编辑**: 在线修改服务B配置
- 🧪 **连通性测试**: 测试服务B前后端连通性
- 💾 **配置保存**: 自动保存到配置文件

#### 界面布局
```
┌─────────────────────────────────────────┐
│ ⚙️ Service A 配置管理器                  │
├─────────────────────────────────────────┤
│ 📋 当前配置                             │
│ [🔄 刷新配置] [♻️ 重新加载配置文件]      │
│ {JSON配置显示区域}                      │
├─────────────────────────────────────────┤
│ 🔧 更新服务B配置                        │
│ 前端配置    │ 后端配置                  │
│ 主机: ___   │ 主机: ___                │
│ 端口: ___   │ 端口: ___                │
│ 协议: ___   │ 协议: ___                │
│ [💾 更新配置] [📝 填入当前配置]          │
├─────────────────────────────────────────┤
│ 🌐 网络配置测试                         │
│ [🧪 测试网络配置] [💚 测试服务B连通性]   │
│ {测试结果显示区域}                      │
└─────────────────────────────────────────┘
```

## 🚀 使用方法

### 1. **启动服务**
```bash
cd service_a
python app_host.py
```

### 2. **访问配置管理**
- 主页: http://localhost:8080/
- 配置管理: http://localhost:8080/config
- API文档: http://localhost:8080/docs

### 3. **配置管理操作**

#### 查看当前配置
```bash
curl http://localhost:8080/api/config
```

#### 更新服务B配置
```bash
curl -X PUT http://localhost:8080/api/config/service-b \
  -H "Content-Type: application/json" \
  -d '{
    "frontend": {
      "host": "***********",
      "port": 3333,
      "protocol": "https"
    }
  }'
```

#### 重新加载配置
```bash
curl -X POST http://localhost:8080/api/config/reload
```

### 4. **环境变量覆盖**
```bash
export SERVICE_B_FRONTEND_URL="https://example.com:8443"
export SERVICE_B_BACKEND_URL="https://api.example.com:9443"
python app_host.py
```

## 📊 配置优先级

1. **环境变量** (最高优先级)
2. **配置文件** (config.json)
3. **默认配置** (代码中硬编码)

## 🔍 测试验证

### 1. **配置文件测试**
```bash
# 修改 config.json 中的服务B配置
# 访问 /api/network-config 验证配置生效
curl http://localhost:8080/api/network-config
```

### 2. **环境变量测试**
```bash
export SERVICE_B_FRONTEND_URL="http://test.example.com:8080"
python app_host.py
# 验证环境变量覆盖配置文件
```

### 3. **配置管理界面测试**
1. 访问 http://localhost:8080/config
2. 点击"刷新配置"查看当前配置
3. 修改服务B配置并保存
4. 测试网络连通性

## 📝 配置文件说明

### service_a 配置
- `name`: 服务名称
- `version`: 服务版本
- `host/port`: 服务监听地址
- `base_url`: 服务基础URL

### service_b 配置
- `frontend`: 前端服务配置 (ES-Search 前端)
- `backend`: 后端服务配置 (ES-Search 后端API)
- 每个服务包含: host, port, protocol, url

### network 配置
- `deployment_mode`: 部署模式 (host/container)
- `environment`: 环境 (development/production)
- `cors_origins`: CORS允许的源
- `timeout`: 请求超时时间

### security 配置
- `api_key`: 服务间认证密钥
- `token_expiry_minutes`: Token过期时间

## 🎯 优势

1. **集中管理**: 所有配置集中在一个文件中
2. **灵活配置**: 支持环境变量覆盖
3. **实时更新**: 支持运行时重新加载配置
4. **可视化管理**: 提供Web界面管理配置
5. **向后兼容**: 保持原有API接口不变
6. **错误处理**: 配置文件损坏时自动回退到默认配置

## 🔄 迁移指南

### 从硬编码到配置文件
1. 备份原始 `app_host.py`
2. 创建 `config.json` 配置文件
3. 重启服务验证配置生效
4. 使用配置管理界面调整参数

### 生产环境部署
1. 根据实际环境修改 `config.json`
2. 设置相应的环境变量
3. 确保配置文件权限正确
4. 监控配置加载日志

---

**更新时间**: 2025-01-04  
**版本**: v1.0  
**兼容性**: 向后兼容，保持原有API接口
