# 🔍 审核页面 (/audit) 问题排查指南

## 📋 问题描述

通过 service_a 页面调用 `/audit` 路由时，显示效果和首页一样，没有显示预期的审核页面内容。

## 🎯 审核页面应该显示的内容

### 📊 正常的审核页面界面

```
┌─────────────────────────────────────────┐
│ ← 文档权限审核                           │
├─────────────────────────────────────────┤
│ [我的申请] [待审批]                      │
├─────────────────────────────────────────┤
│ 申请人 │ 文档 │ 申请类型 │ 理由 │ 状态    │
├─────────────────────────────────────────┤
│ 张三   │ xxx  │ 查看     │ xxx  │ 待审核  │
│ 李四   │ yyy  │ 下载     │ yyy  │ 已批准  │
└─────────────────────────────────────────┘
```

### 🔑 关键功能

1. **我的申请标签页**：
   - 显示当前用户提交的权限申请
   - 支持撤销待审核的申请
   - 支持删除申请记录

2. **待审批标签页**（仅管理员可见）：
   - 显示需要审核的申请
   - 支持同意/拒绝操作
   - 需要填写审核意见

## 🔍 可能的问题原因

### 1. **路由守卫重定向问题**

**问题**：ES-Search 的路由守卫可能导致重定向循环

<augment_code_snippet path="es-search/src/router/index.js" mode="EXCERPT">
```javascript
router.beforeEach(async (to, from, next) => {
  if (to.query?.token) {
    store.dispatch('setToken', to.query?.token)
    let path = to.query.redirect || to.path  // ← 可能的问题点
    next({ path, replace: true })
  } else {
    if (!(await localforage.getItem('token'))) {
      showLogin()  // ← 可能阻止路由跳转
    } else {
      next()
    }
  }
})
```
</augment_code_snippet>

### 2. **Token 状态问题**

- Token 无效或过期
- Token 中的用户信息不正确
- Vuex store 中的用户状态未正确设置

### 3. **用户权限问题**

- 用户角色信息缺失
- 管理员权限验证失败
- 用户状态未正确加载到 Vuex store

### 4. **API 调用问题**

- 权限申请列表 API 调用失败
- 网络请求被拦截或超时
- 后端权限验证失败

## 🔧 排查步骤

### 步骤1: 使用调试工具

1. **访问 service_a 测试页面**
2. **选择管理员用户** (Admin)
3. **生成 Token**
4. **点击"调试审核页面"按钮**

### 步骤2: 检查浏览器控制台

打开浏览器开发者工具，查看：

#### Console 日志
```
🔍 ===== 审核页面调试 =====
👤 当前用户: 系统管理员 (admin)
🔑 用户角色: admin
🔐 Token状态: 有效
👑 管理员权限: 是
✅ 用户有管理员权限，应该能看到"待审批"标签页
🔄 强制跳转到审核页面...
```

#### Network 标签
检查以下 API 调用：
- `/api/permissions/requests` (我的申请)
- `/api/permissions/pending-reviews` (待审批)

#### Application 标签
检查 LocalStorage 中的：
- `token` 值
- 用户信息

### 步骤3: 手动验证

#### 直接访问审核页面
```
http://***********:2222/audit?token=YOUR_TOKEN
```

#### 检查 iframe 实际 URL
在 service_a 页面的日志中查看：
```
iframe URL已更新
目标路由: /audit
```

## 🛠️ 解决方案

### 方案1: 修复路由跳转逻辑

已在 service_a 中实现特殊处理：

```javascript
if (route === '/audit') {
    // 审核页面需要特殊处理，确保token和路由都正确传递
    newUrl = `${NETWORK_CONFIG.service_b_frontend}${redirectPath}?token=${currentToken}`;
} else {
    newUrl = `${NETWORK_CONFIG.service_b_frontend}/?token=${currentToken}&redirect=${encodeURIComponent(redirectPath)}`;
}
```

### 方案2: 检查用户状态

确保用户信息正确加载到 Vuex store：

```javascript
// 检查 store 中的用户信息
console.log('Store user:', this.$store.state.user);
console.log('User roles:', this.$store.state.user?.roles);
```

### 方案3: 验证 API 调用

检查审核页面的 API 调用是否正常：

```javascript
// 在 audit.vue 中添加调试日志
async getApplyList(isInit = false) {
    console.log('获取申请列表:', this.activeName);
    let res;
    if (this.activeName == '我的申请') {
        res = await reqApplyList(this.pageNum, this.pageSize);
    } else {
        res = await reqReviewList(this.pageNum, this.pageSize);
    }
    console.log('API响应:', res);
    this.tableData = res.data || [];
}
```

## 🎯 快速测试方法

### 1. 使用调试按钮
```
1. 选择 Admin 用户
2. 生成 Token
3. 点击"调试审核页面"
4. 观察日志输出
```

### 2. 手动检查
```
1. 打开浏览器开发者工具
2. 切换到 Console 标签
3. 访问审核页面
4. 查看是否有错误信息
```

### 3. 对比测试
```
1. 先访问搜索页面 (确认正常)
2. 再访问审核页面 (对比差异)
3. 检查两者的 URL 和加载过程
```

## 📝 常见问题

### Q1: 审核页面显示空白
**A**: 检查 Token 是否有效，用户是否已登录

### Q2: 只显示"我的申请"标签页
**A**: 检查用户是否有管理员权限 (`roles.includes('admin')`)

### Q3: 表格显示"暂无记录"
**A**: 检查 API 调用是否成功，数据库中是否有申请记录

### Q4: 页面显示和首页一样
**A**: 可能是路由重定向问题，检查 URL 参数和路由守卫逻辑

## 🚀 预期结果

修复后，审核页面应该：

1. ✅ 显示正确的页面标题："文档权限审核"
2. ✅ 显示两个标签页（管理员用户）
3. ✅ 正确加载申请列表数据
4. ✅ 支持审核操作（同意/拒绝）
5. ✅ 显示正确的用户权限状态

---

**调试工具位置**: service_a 测试页面 → 路由测试工具 → "调试审核页面"按钮  
**更新时间**: 2025-01-04
