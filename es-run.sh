docker run -d \
  --name elasticsearch-optimized \
  -p 9200:9200 -p 9300:9300 \
  -e "discovery.type=single-node" \  # 单节点模式
  -e "ES_JAVA_OPTS=-Xms512m -Xmx512m" \  # 降低堆内存
  -e "bootstrap.memory_lock=false" \  # 禁用内存锁定
  -e "cluster.routing.allocation.disk.threshold_enabled=false" \  # 禁用磁盘阈值检查
  --memory=1g \  # 限制容器内存
  --cpus=1.0 \  # 限制CPU使用
  -v esdata:/usr/share/elasticsearch/data \
  docker.elastic.co/elasticsearch/elasticsearch:8.12.0