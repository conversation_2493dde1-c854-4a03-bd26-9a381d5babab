from datetime import datetime, timed<PERSON>ta
from typing import Optional, TYPE_CHECKING, List # Import TYPE_CHECKING
from passlib.context import CryptContext
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession # 导入 AsyncSession
# Keep this import, but we'll use string hints for models.User
from . import models, schemas # Adjust imports as needed


from utils.config import config

# Add this block for type checking tools like MyPy
if TYPE_CHECKING:
    from .models import User

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT configuration - 从配置文件读取，与simple-auth统一
from utils.config import config

# 优先使用service_auth.jwt配置（与simple-auth统一），回退到auth配置
SECRET_KEY = config.get("service_auth.jwt.secret_key", config.get("auth.secret_key", "hngpt-secret-key-2024-very-secure"))
ALGORITHM = config.get("service_auth.jwt.algorithm", config.get("auth.algorithm", "HS256"))
ACCESS_TOKEN_EXPIRE_MINUTES = config.get("service_auth.jwt.expire_minutes", config.get("auth.access_token_expire_minutes", 30))

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)

# Use string 'models.User' for the return type hint
async def get_user_by_username(db: AsyncSession, username: str) -> Optional['models.User']:
    """Get user by username using SQLAlchemy Session with eager loading"""
    from sqlalchemy import select
    from sqlalchemy.orm import selectinload
    result = await db.execute(
        select(models.User)
        .where(models.User.user_name == username)
    )
    return result.scalars().first()


async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional['models.User']:
    """Get user by user_id using SQLAlchemy Session with eager loading"""
    from sqlalchemy import select
    from sqlalchemy.orm import selectinload
    result = await db.execute(
        select(models.User)
        .where(models.User.user_id == user_id)
    )
    return result.scalars().first()


async def authenticate_user(db: AsyncSession, username: str, password: str) -> Optional['models.User']:
    """Authenticate user with username and password using SQLAlchemy Session"""
    user = await get_user_by_username(db, username)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token - 使用统一JWT工具类"""
    from utils.jwt_utils import encode_jwt_token

    # 计算过期时间（分钟）
    if expires_delta:
        expire_minutes = int(expires_delta.total_seconds() / 60)
    else:
        expire_minutes = 15

    # 使用统一JWT工具生成token
    return encode_jwt_token(data, expire_minutes)

# Use string 'models.User' for the return type hint
async def get_current_user(db: AsyncSession, token: str) -> Optional['models.User']:
    """Get current user from JWT token using SQLAlchemy Session"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        # 使用统一JWT工具类解码token
        from utils.jwt_utils import decode_jwt_token

        payload = decode_jwt_token(token)
        if not payload:
            raise credentials_exception

        # 优先使用user_id查找（新格式）
        user_id = payload.get("user_id")
        if user_id:
            user = await get_user_by_id(db, user_id=user_id)
            if user:
                return user

        # 备用：使用sub字段作为username查找（兼容旧格式）
        username: str = payload.get("sub")
        if username is None:
            # 如果没有sub字段，尝试使用user_name字段
            username = payload.get("user_name")

        if username is None:
            raise credentials_exception

    except Exception:
        raise credentials_exception

    user = await get_user_by_username(db, username=username)
    if user is None:
        raise credentials_exception
    return user

# --- 更新会话历史相关函数 ---
# No changes needed here as it uses schemas and runtime model access
async def add_conversation_turn(db: AsyncSession, conversation: schemas.ConversationCreate):
    """Adds a new conversation turn using SQLAlchemy async Session."""
    from sqlalchemy import select
    from sqlalchemy.exc import IntegrityError
    
    # 验证用户存在
    user_result = await db.execute(
        select(models.User).where(models.User.user_id == conversation.user_id)
    )
    db_user = user_result.scalar_one_or_none()
    if not db_user:
        raise HTTPException(
            status_code=404,
            detail=f"User with id {conversation.user_id} not found"
        )

    try:
        db_conversation = models.ConversationHistory(
            user_id=conversation.user_id,
            role=conversation.role,
            content=conversation.content
        )
        db.add(db_conversation)
        await db.commit()
        await db.refresh(db_conversation)
        return db_conversation
    except IntegrityError as e:
        await db.rollback()
        raise HTTPException(
            status_code=400,
            detail=f"Failed to add conversation: {str(e)}"
        )

async def get_user_conversations(db: AsyncSession, user_id: int, skip: int = 0, limit: int = 100):
    """Retrieves conversation history using SQLAlchemy Session."""
    from sqlalchemy import select
    result = await db.execute(
        select(models.ConversationHistory)
        .where(models.ConversationHistory.user_id == user_id)
        .order_by(models.ConversationHistory.timestamp.desc())
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

# --- 部门管理相关函数 ---
async def create_department(db: AsyncSession, department: schemas.DepartmentCreate) -> models.Department:
    """创建部门"""
    from sqlalchemy import select

    # 检查部门名称是否已存在
    result = await db.execute(
        select(models.Department).where(models.Department.dept_name == department.dept_name)
    )
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=400,
            detail="Department name already exists"
        )

    db_department = models.Department(**department.model_dump())
    db.add(db_department)
    await db.commit()
    await db.refresh(db_department)
    return db_department

async def get_departments(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[models.Department]:
    """获取部门列表"""
    from sqlalchemy import select
    result = await db.execute(
        select(models.Department)
        .where(models.Department.del_flag == '0')
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

async def get_department_by_id(db: AsyncSession, department_id: int) -> Optional[models.Department]:
    """根据ID获取部门"""
    from sqlalchemy import select
    result = await db.execute(
        select(models.Department).where(models.Department.dept_id == department_id)
    )
    return result.scalar_one_or_none()

async def update_department(db: AsyncSession, department_id: int, department_update: schemas.DepartmentUpdate) -> Optional[models.Department]:
    """更新部门信息"""
    from sqlalchemy import select

    result = await db.execute(
        select(models.Department).where(models.Department.dept_id == department_id)
    )
    db_department = result.scalar_one_or_none()

    if not db_department:
        return None

    update_data = department_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_department, field, value)

    await db.commit()
    await db.refresh(db_department)
    return db_department

# --- 用户部门关联管理 ---
async def assign_user_to_department(db: AsyncSession, user_id: int, department_id: int) -> bool:
    """将用户分配到部门（直接更新用户表的dept_id字段）"""
    from sqlalchemy import select, text

    # 检查用户和部门是否存在
    user_result = await db.execute(select(models.User).where(models.User.user_id == user_id))
    user = user_result.scalar_one_or_none()

    dept_result = await db.execute(select(models.Department).where(models.Department.dept_id == department_id))
    department = dept_result.scalar_one_or_none()

    if not user or not department:
        return False

    # 直接更新用户表的dept_id字段
    update_sql = text("""
        UPDATE users SET dept_id = :dept_id, update_time = NOW()
        WHERE user_id = :user_id
    """)
    await db.execute(update_sql, {"user_id": user_id, "dept_id": department_id})
    await db.commit()

    return True

async def remove_user_from_department(db: AsyncSession, user_id: int, department_id: int) -> bool:
    """从部门中移除用户（将用户的dept_id设为NULL）"""
    from sqlalchemy import select, text

    # 检查用户是否存在且属于指定部门
    user_result = await db.execute(
        select(models.User).where(
            models.User.user_id == user_id,
            models.User.dept_id == department_id
        )
    )
    user = user_result.scalar_one_or_none()

    if not user:
        return False

    # 将用户的dept_id设为NULL
    update_sql = text("""
        UPDATE users SET dept_id = NULL, update_time = NOW()
        WHERE user_id = :user_id
    """)
    await db.execute(update_sql, {"user_id": user_id})
    await db.commit()

    return True

# --- 文档管理相关函数 ---
async def create_document(db: AsyncSession, document: schemas.DocumentCreate, file_info: dict, uploader_id: int) -> models.Document:
    """创建文档记录"""
    db_document = models.Document(
        title=document.title,
        file_path=file_info["file_path"],
        owner_id=uploader_id,
        department_id=document.department_id
    )
    db.add(db_document)
    await db.commit()
    await db.refresh(db_document)
    return db_document

async def get_documents_by_department(db: AsyncSession, department_id: int, skip: int = 0, limit: int = 100) -> List[models.Document]:
    """获取部门文档列表"""
    from sqlalchemy import select
    result = await db.execute(
        select(models.Document)
        .where(models.Document.department_id == department_id)
        .where(models.Document.status == models.DocumentStatus.active)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

async def get_document_by_id(db: AsyncSession, document_id: int) -> Optional[models.Document]:
    """根据ID获取文档"""
    from sqlalchemy import select
    result = await db.execute(
        select(models.Document).where(models.Document.id == document_id)
    )
    return result.scalar_one_or_none()

# --- 权限申请相关函数 ---
async def create_permission_request(db: AsyncSession, request: schemas.PermissionRequestCreate, requester_id: int) -> models.PermissionRequest:
    """创建权限申请"""
    # 检查是否已有待处理的申请
    from sqlalchemy import select, and_
    existing_result = await db.execute(
        select(models.PermissionRequest).where(
            and_(
                models.PermissionRequest.requester_id == requester_id,
                models.PermissionRequest.document_id == request.document_id,
                models.PermissionRequest.permission_type == request.permission_type,
                models.PermissionRequest.status == models.RequestStatus.pending
            )
        )
    )

    if existing_result.scalar_one_or_none():
        raise HTTPException(
            status_code=400,
            detail="You already have a pending request for this document and permission type"
        )

    db_request = models.PermissionRequest(
        requester_id=requester_id,
        document_id=request.document_id,
        permission_type=request.permission_type,
        reason=request.reason
    )
    db.add(db_request)
    await db.commit()
    await db.refresh(db_request)
    return db_request

async def get_pending_requests_for_department(db: AsyncSession, department_id: int) -> List[models.PermissionRequest]:
    """获取部门待审核的权限申请"""
    from sqlalchemy import select, join
    result = await db.execute(
        select(models.PermissionRequest)
        .join(models.Document)
        .where(models.Document.department_id == department_id)
        .where(models.PermissionRequest.status == models.RequestStatus.pending)
    )
    return result.scalars().all()

async def approve_permission_request(db: AsyncSession, request_id: int, reviewer_id: int, comment: Optional[str] = None) -> Optional[models.PermissionRequest]:
    """审批权限申请"""
    from sqlalchemy import select

    result = await db.execute(
        select(models.PermissionRequest).where(models.PermissionRequest.id == request_id)
    )
    db_request = result.scalar_one_or_none()

    if not db_request:
        return None

    db_request.status = models.RequestStatus.approved
    db_request.reviewer_id = reviewer_id
    db_request.review_comment = comment
    db_request.reviewed_at = datetime.utcnow()

    # 创建文档权限记录
    db_permission = models.DocumentPermission(
        user_id=db_request.requester_id,
        document_id=db_request.document_id,
        permission_type=db_request.permission_type,
        granted_by=reviewer_id
    )
    db.add(db_permission)

    await db.commit()
    await db.refresh(db_request)
    return db_request

async def reject_permission_request(db: AsyncSession, request_id: int, reviewer_id: int, comment: str) -> Optional[models.PermissionRequest]:
    """拒绝权限申请"""
    from sqlalchemy import select

    result = await db.execute(
        select(models.PermissionRequest).where(models.PermissionRequest.id == request_id)
    )
    db_request = result.scalar_one_or_none()

    if not db_request:
        return None

    db_request.status = models.RequestStatus.rejected
    db_request.reviewer_id = reviewer_id
    db_request.review_comment = comment
    db_request.reviewed_at = datetime.utcnow()

    await db.commit()
    await db.refresh(db_request)
    return db_request

# --- 审计日志相关函数 ---
async def create_audit_log(db: AsyncSession, log_data: schemas.AuditLogCreate, user_id: Optional[int] = None) -> models.AuditLog:
    """创建审计日志"""
    db_log = models.AuditLog(
        user_id=user_id,
        action=log_data.action,
        resource_type=log_data.resource_type,
        resource_id=log_data.resource_id,
        ip_address=log_data.ip_address,
        user_agent=log_data.user_agent,
        details=log_data.details,
        status=log_data.status
    )
    db.add(db_log)
    await db.commit()
    await db.refresh(db_log)
    return db_log

async def get_audit_logs(db: AsyncSession, user_id: Optional[int] = None, action: Optional[str] = None,
                        skip: int = 0, limit: int = 100) -> List[models.AuditLog]:
    """获取审计日志"""
    from sqlalchemy import select

    query = select(models.AuditLog)

    if user_id:
        query = query.where(models.AuditLog.user_id == user_id)

    if action:
        query = query.where(models.AuditLog.action == action)

    query = query.order_by(models.AuditLog.created_at.desc()).offset(skip).limit(limit)

    result = await db.execute(query)
    return result.scalars().all()

# --- 权限检查相关函数 ---
async def check_user_document_permission(db: AsyncSession, user_id: int, document_id: int, permission_type: str) -> bool:
    """检查用户对文档的权限"""
    from sqlalchemy import select, and_

    # 1. 检查是否是文档上传者
    doc_result = await db.execute(
        select(models.Document).where(
            and_(
                models.Document.id == document_id,
                models.Document.owner_id == user_id
            )
        )
    )
    if doc_result.scalar_one_or_none():
        return True

    # 2. 检查是否有明确的权限授权
    perm_result = await db.execute(
        select(models.DocumentPermission).where(
            and_(
                models.DocumentPermission.user_id == user_id,
                models.DocumentPermission.document_id == document_id,
                models.DocumentPermission.permission_type == permission_type,
                models.DocumentPermission.is_active == True
            )
        )
    )
    if perm_result.scalar_one_or_none():
        return True

    # 3. 检查是否是部门管理员
    user_result = await db.execute(
        select(models.User).where(models.User.user_id == user_id)
    )
    user = user_result.scalar_one_or_none()

    if user:
        for role in user.roles:
            if role.name == "deptAdmin":
                # 检查用户是否在文档所属部门
                doc_result = await db.execute(
                    select(models.Document).where(models.Document.id == document_id)
                )
                document = doc_result.scalar_one_or_none()

                if document:
                    for dept in user.departments:
                        if dept.dept_id == document.department_id:
                            return True

    return False

async def is_department_admin(db: AsyncSession, user_id: int, department_id: int) -> bool:
    """检查用户是否是指定部门的管理员"""
    from sqlalchemy import select

    user_result = await db.execute(
        select(models.User).where(models.User.user_id == user_id)
    )
    user = user_result.scalar_one_or_none()

    if not user:
        return False

    # 检查是否有部门管理员角色
    has_admin_role = any(role.name == "deptAdmin" for role in user.roles)
    if not has_admin_role:
        return False

    # 检查是否在指定部门
    is_in_department = any(dept.dept_id == department_id for dept in user.departments)

    return is_in_department