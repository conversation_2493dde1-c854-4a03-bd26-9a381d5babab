"""
简化的服务A认证路由
处理来自服务A的JWT Token认证和相关API
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any, Optional, Union
from pydantic import BaseModel, Field
import jwt
from jwt.exceptions import InvalidTokenError as JWTError
from datetime import datetime, timezone, timedelta
import uuid
import time
import sys
import os

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utils.log import log
# 服务认证器已删除 - 简化安全验证
# from middleware.service_authenticator import service_authenticator
import json
from middleware.enhanced_service_auth import verify_enhanced_service_request
try:
    from auth.token_blacklist import add_token_to_user, is_token_revoked, revoke_user_tokens
except ImportError:
    # 如果导入失败，使用简单的内存实现
    _revoked_tokens = set()
    _user_tokens = {}

    def add_token_to_user(token, user_id, jti=None):
        if user_id not in _user_tokens:
            _user_tokens[user_id] = set()
        _user_tokens[user_id].add(jti or token)

    def is_token_revoked(token, jti=None):
        return (jti or token) in _revoked_tokens

    def revoke_user_tokens(user_id):
        if user_id in _user_tokens:
            for token_id in _user_tokens[user_id]:
                _revoked_tokens.add(token_id)
            count = len(_user_tokens[user_id])
            _user_tokens[user_id].clear()
            return count
        return 0

# 精确的IP白名单配置
ALLOWED_SERVICE_IPS = [
    "************",    # 服务A后端IP (需要根据实际环境调整)
    "***********",     # 服务B本机IP (用于本地测试)
    "127.0.0.1",       # 本地回环
    "localhost",       # 本地主机名
    "**********",      # Docker默认网关
    "**********",      # Docker自定义网关
]

def verify_client_ip(client_ip: str) -> bool:
    """
    验证客户端IP是否在白名单中

    注意：当前已禁用IP限制以简化开发和测试

    Args:
        client_ip: 客户端IP地址

    Returns:
        bool: True表示IP在白名单中，False表示不在
    """
    # 临时禁用IP白名单限制，便于开发和测试
    log.info(f"Token generation request from IP: {client_ip} (IP restriction disabled)")
    return True

    # 原IP白名单验证逻辑（已禁用）
    # if client_ip in ALLOWED_SERVICE_IPS:
    #     return True
    # log.warning(f"Unauthorized IP access attempt: {client_ip}")
    # return False

# 使用统一JWT工具类
from utils.jwt_utils import unified_jwt_service, create_user_jwt_token, decode_jwt_token
from utils.config import config
from middleware.jwt_auth import get_current_user_from_jwt
from auth.models import User
from database import get_db
from sqlalchemy.ext.asyncio import AsyncSession

security = HTTPBearer(auto_error=False)

def user_to_simple_service_a_user(user: User) -> 'SimpleServiceAUser':
    """将User对象转换为SimpleServiceAUser对象"""
    # 创建一个模拟的token payload
    token_payload = {
        "user_id": user.user_id,
        "user_name": user.user_name,
        "nick_name": user.nick_name,
        "dept_id": user.dept_id,
        "roles": [],  # 可以根据需要从user.roles中提取
        "iss": "ServiceA",
        "aud": "ServiceB",
        "iat": int(time.time()),
        "exp": int(time.time()) + 3600  # 1小时后过期
    }
    return SimpleServiceAUser(token_payload)

class SimpleServiceAUser:
    """简化的服务A用户类"""
    
    def __init__(self, token_payload: Dict[str, Any]):
        # 统一使用user_id字段，不再使用sub字段
        user_id_raw = token_payload.get("user_id")
        try:
            self.user_id = int(user_id_raw) if user_id_raw and str(user_id_raw).isdigit() else user_id_raw
        except (ValueError, TypeError):
            self.user_id = user_id_raw

        # 统一使用user_name字段
        self.user_name = token_payload.get("user_name")
        self.nick_name = token_payload.get("nick_name")
        self.email = token_payload.get("email")
        self.phone = token_payload.get("phone")

        # 安全地处理部门ID
        dept_id_raw = token_payload.get("dept_id")
        try:
            self.dept_id = int(dept_id_raw) if dept_id_raw and str(dept_id_raw).isdigit() else dept_id_raw
        except (ValueError, TypeError):
            self.dept_id = dept_id_raw

        self.dept_name = token_payload.get("dept_name")
        self.roles = token_payload.get("roles", [])
        self.issued_at = token_payload.get("iat")
        self.expires_at = token_payload.get("exp")
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "user_id": self.user_id,
            "user_name": self.user_name,
            "nick_name": self.nick_name,
            "email": self.email,
            "phone": self.phone,
            "dept_id": self.dept_id,
            "dept_name": self.dept_name,
            "roles": self.roles
        }
    
    def has_role(self, role: str) -> bool:
        """检查是否有指定角色"""
        return role in self.roles


# Pydantic模型用于API文档
class TokenRevokeRequest(BaseModel):
    """
    Token撤销请求模型

    - user_id: 可选，撤销指定用户的所有Token
    - 如果不提供user_id，则撤销Header中Authorization Bearer Token
    """
    user_id: Optional[int] = Field(
        None,
        description="可选的用户ID，如果提供则撤销该用户的所有Token；如果不提供则撤销Header中的Token",
        example=1
    )


class TokenRevokeResponse(BaseModel):
    """Token撤销响应模型"""
    status: str = Field(description="操作状态", example="success")
    message: str = Field(description="操作结果消息", example="已撤销用户的所有Token")
    revoked_count: Optional[int] = Field(None, description="撤销的Token数量（仅在按用户撤销时返回）", example=3)

async def get_simple_service_a_user_unified(
    request: Request,
    current_user: User = Depends(get_current_user_from_jwt)
) -> SimpleServiceAUser:
    """统一认证版本：使用get_current_user_from_jwt并转换为SimpleServiceAUser"""
    return user_to_simple_service_a_user(current_user)

async def get_simple_service_a_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> SimpleServiceAUser:
    """简化的服务A用户认证依赖"""
    
    # 尝试从Authorization header获取token
    token = credentials.credentials if credentials else None
    
    # 如果header中没有token，尝试从URL参数获取
    if not token:
        token = request.query_params.get("token")
    
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No authentication token provided"
        )
    
    try:
        # 验证JWT Token - 使用统一JWT工具类
        log.debug(f"验证Token，使用统一JWT服务...")

        payload = decode_jwt_token(token)
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )

        log.debug(f"Token解码成功，payload: {payload}")

        # 检查Token是否已被撤销
        jti = payload.get('jti')
        if is_token_revoked(token, jti):
            log.warning(f"Token已被撤销: {jti}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has been revoked"
            )

        # 验证签发方
        expected_issuer = config.get("service_auth.jwt.issuer", "hngpt")
        if payload.get("iss") != expected_issuer:
            log.warning(f"无效的签发方: {payload.get('iss')} != {expected_issuer}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token issuer"
            )

        # 暂时不验证接收方(audience)，简化token验证
        # if payload.get("aud") != JWT_AUDIENCE:
        #     log.warning(f"无效的接收方: {payload.get('aud')} != {JWT_AUDIENCE}")
        #     raise HTTPException(
        #         status_code=status.HTTP_401_UNAUTHORIZED,
        #         detail="Invalid token audience"
        #     )
        
        # 创建用户对象
        user = SimpleServiceAUser(payload)
        
        # 记录访问日志
        log.info(f"服务A用户认证成功: {user.user_name} ({user.dept_name})")
        
        return user
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired"
        )
    except jwt.InvalidAudienceError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token audience"
        )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    except Exception as e:
        log.error(f"Token验证失败: {str(e)}")
        log.error(f"错误类型: {type(e)}")
        import traceback
        log.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Token validation failed: {str(e)}"
        )

router = APIRouter(prefix="/simple-auth", tags=["simple-auth"])

@router.get("/verify-token")
async def verify_service_a_token(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    验证服务A的JWT Token
    对任意无效token返回401

    Returns:
        用户信息和验证状态，或401错误
    """
    # 检查是否提供了token
    if not credentials:
        # 尝试从URL参数获取token
        token = request.query_params.get("token")
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No token provided",
                headers={"WWW-Authenticate": "Bearer"}
            )
    else:
        token = credentials.credentials

    # 验证token
    try:
        service_a_user = await get_simple_service_a_user(request, credentials)

        # 构建完整的用户信息，包含email、phone和roles
        user_info = service_a_user.to_dict()

        # 确保包含email和phone字段（如果token中没有，则提供默认值）
        if not user_info.get('email'):
            user_info['email'] = f"{service_a_user.user_name}@company.com"

        if not user_info.get('phone'):
            user_info['phone'] = ""

        return {
            "status": "success",
            "message": "Token验证成功",
            "user_info": user_info
        }
    except HTTPException as e:
        # 对于任何认证失败，都返回401
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        # 对于任何其他错误，也返回401
        log.warning(f"Token验证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"}
        )

@router.get("/user-info")
async def get_service_a_user_info(
    service_a_user: SimpleServiceAUser = Depends(get_simple_service_a_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取服务A用户详细信息

    验证用户是否存在于数据库中，如果不存在则返回401错误

    Returns:
        用户详细信息
    """
    # 验证用户是否存在于数据库中（使用原生SQL避免ORM关系问题）
    try:
        from sqlalchemy import text

        # 使用原生SQL查询用户是否存在
        sql = text("""
            SELECT user_id, user_name, nick_name, status
            FROM users
            WHERE user_id = :user_id
        """)

        result = await db.execute(sql, {"user_id": service_a_user.user_id})
        db_user_row = result.fetchone()

        if not db_user_row:
            log.warning(f"用户 {service_a_user.user_name} (ID: {service_a_user.user_id}) 不存在于数据库中")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在于数据库中，请联系管理员",
                headers={"WWW-Authenticate": "Bearer"}
            )

        # 检查用户是否激活（status='0'表示正常，'1'表示停用）
        if db_user_row[3] != '0':  # status字段
            log.warning(f"用户 {service_a_user.user_name} (ID: {service_a_user.user_id}) 已被禁用")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户账号已被禁用，请联系管理员",
                headers={"WWW-Authenticate": "Bearer"}
            )

        # 用户存在且激活，返回Token中的信息（保持兼容性）
        log.info(f"用户信息获取成功: {service_a_user.user_name}")
        return service_a_user.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        log.error(f"验证用户存在性时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证用户信息失败"
        )

@router.post("/check-role")
async def check_service_a_role(
    role_data: Dict[str, str],
    service_a_user: SimpleServiceAUser = Depends(get_simple_service_a_user)
) -> Dict[str, Any]:
    """
    检查服务A用户角色
    
    Args:
        role_data: {"role": "角色标识"}
        
    Returns:
        角色检查结果
    """
    role = role_data.get("role")
    if not role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少角色参数"
        )
    
    has_role = service_a_user.has_role(role)
    
    return {
        "user_id": service_a_user.user_id,
        "username": service_a_user.user_name,
        "role": role,
        "has_role": has_role,
        "all_roles": service_a_user.roles
    }

@router.get("/dashboard")
async def service_a_dashboard(
    service_a_user: SimpleServiceAUser = Depends(get_simple_service_a_user)
) -> Dict[str, Any]:
    """
    服务A用户仪表板信息
    
    Returns:
        仪表板数据
    """
    # 记录访问日志
    log.info(f"服务A用户 {service_a_user.user_name} 访问仪表板")
    
    return {
        "welcome_message": f"欢迎，{service_a_user.nick_name or service_a_user.user_name}！",
        "user_info": service_a_user.to_dict(),
        "system_info": {
            "login_source": "服务A",
            "issued_at": service_a_user.issued_at,
            "expires_at": service_a_user.expires_at
        },
        "available_features": [
            "文档搜索",
            "文档查看"
        ] + (["管理功能"] if service_a_user.has_role("admin") else [])
    }

@router.post("/generate-token")
async def generate_service_a_token(
    request: Request,
    user_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    为测试页面生成真实的JWT Token

    Args:
        user_data: 用户数据字典

    Returns:
        生成的Token和用户信息
    """
    try:
        # 安全验证 - 验证请求来源IP
        client_ip = request.client.host

        # 记录客户端IP用于调试和审计
        log.info(f"Token generation request from IP: {client_ip}")

        # 精确的IP白名单验证
        if not verify_client_ip(client_ip):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="IP address not allowed"
            )

        # 验证必要字段
        required_fields = ['user_id', 'user_name', 'nick_name', 'dept_id', 'dept_name', 'roles']
        for field in required_fields:
            if field not in user_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"缺少必要字段: {field}"
                )

        # 撤销用户的旧Token（实现用户切换时的安全清理）
        revoked_count = revoke_user_tokens(str(user_data['user_id']))
        if revoked_count > 0:
            log.info(f"已撤销用户 {user_data['user_name']} 的 {revoked_count} 个旧Token")

        # 使用统一JWT工具类生成Token
        expire_minutes = user_data.get("expire_minutes", config.get("service_auth.jwt.expire_minutes", 30))
        # 准备用户数据（统一格式）
        token_user_data = {
            'user_id': user_data['user_id'],
            'user_name': user_data['user_name'],
            'nick_name': user_data['nick_name'],
            'email': user_data.get('email', f"{user_data['user_name']}@company.com"),
            'phone': user_data.get('phone', '13800138000'),
            'dept_id': user_data['dept_id'],
            'dept_name': user_data['dept_name'],
            'roles': user_data['roles'],
            'source': 'iframe',
            'service_version': '1.0'
        }

        # 生成JWT Token
        token = create_user_jwt_token(token_user_data, expire_minutes)

        # 解码token获取jti用于映射
        payload = decode_jwt_token(token)

        # 将新Token添加到用户映射
        add_token_to_user(token, str(user_data['user_id']), payload['jti'])

        log.info(f"为测试用户 {user_data['user_name']} 生成Token")

        return {
            "token": token,
            "user_info": {
                "user_name": user_data['user_name'],
                "nick_name": user_data['nick_name'],
                "dept_name": user_data['dept_name'],
                "roles": user_data['roles']
            },
            "expires_in": expire_minutes*60, 
            "revoked_old_tokens": revoked_count
        }

    except HTTPException as e:
        log.error(f"生成Token失败: {e.detail}")
        raise
    except Exception as e:
        log.error(f"生成Token失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成Token失败: {str(e)}"
        )

@router.get("/health")
async def service_a_health_check() -> Dict[str, Any]:
    """
    服务A认证健康检查

    Returns:
        健康状态
    """
    return {
        "status": "healthy",
        "service": "simple-service-a-auth",
        "timestamp": "2024-01-01T00:00:00Z"
    }

@router.post(
    "/revoke-token",
    response_model=TokenRevokeResponse,
    summary="撤销JWT Token",
    description="撤销用户的JWT Token，支持按用户ID撤销所有Token或撤销Header中的Token",
    responses={
        200: {
            "description": "Token撤销成功",
            "content": {
                "application/json": {
                    "examples": {
                        "revoke_by_user": {
                            "summary": "按用户ID撤销",
                            "description": "撤销指定用户的所有Token",
                            "value": {
                                "status": "success",
                                "message": "已撤销用户的所有Token",
                                "revoked_count": 3
                            }
                        },
                        "revoke_header_token": {
                            "summary": "撤销Header中的Token",
                            "description": "撤销Authorization Header中的Token（请求体为空对象）",
                            "value": {
                                "status": "success",
                                "message": "Token已撤销"
                            }
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "examples": {
                        "missing_token": {
                            "summary": "缺少Token",
                            "value": {
                                "detail": "必须提供user_id或Authorization Header中的Token"
                            }
                        },
                        "invalid_token": {
                            "summary": "无效Token",
                            "value": {
                                "detail": "Invalid token"
                            }
                        }
                    }
                }
            }
        },
        401: {
            "description": "未提供认证Token",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Authorization header required"
                    }
                }
            }
        },
        403: {
            "description": "IP地址不在白名单中",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "IP address not allowed"
                    }
                }
            }
        }
    }
)
async def revoke_service_a_token(
    request: Request,
    revoke_data: TokenRevokeRequest = TokenRevokeRequest(),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> TokenRevokeResponse:
    """
    撤销JWT Token

    **功能说明：**
    - 支持两种撤销方式：按用户ID撤销所有Token，或撤销Header中的Token
    - 具有IP白名单验证，只允许授权的IP地址调用
    - 撤销后的Token将被加入黑名单，无法再次使用

    **撤销方式：**
    1. **按用户ID撤销**：提供`user_id`参数，撤销该用户的所有有效Token
    2. **撤销Header中的Token**：不提供`user_id`，撤销Authorization Header中的Bearer Token

    **安全机制：**
    - IP白名单验证：只允许预配置的IP地址调用此接口
    - Token黑名单：撤销的Token会被加入黑名单，防止重复使用
    - 操作审计：所有撤销操作都会被记录到日志中

    **使用场景：**
    - 用户退出登录时撤销当前Token
    - 用户切换账号时撤销旧Token
    - 管理员强制下线用户（按用户ID撤销）
    - 安全事件响应（如Token泄露）

    **请求示例：**
    ```bash
    # 撤销用户的所有Token
    curl -X POST "http://localhost:18888/api/simple-auth/revoke-token" \
         -H "Content-Type: application/json" \
         -d '{"user_id": 1}'

    # 撤销Header中的Token（用户退出登录，请求体默认为空对象）
    curl -X POST "http://localhost:18888/api/simple-auth/revoke-token" \
         -H "Content-Type: application/json" \
         -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    ```

    **注意事项：**
    - 如果不提供`user_id`，必须在Authorization Header中提供Bearer Token
    - 调用此接口的客户端IP必须在白名单中
    - 撤销操作不可逆，被撤销的Token无法恢复
    """
    try:
        # 安全验证 - 验证请求来源IP
        client_ip = request.client.host

        # 记录客户端IP用于调试和审计
        log.info(f"Token revocation request from IP: {client_ip}")

        # 精确的IP白名单验证
        if not verify_client_ip(client_ip):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="IP address not allowed"
            )

        user_id = revoke_data.user_id

        # 如果提供了user_id，按用户ID撤销所有Token
        if user_id:
            # 撤销用户的所有Token
            revoked_count = revoke_user_tokens(str(user_id))
            log.info(f"已撤销用户 {user_id} 的 {revoked_count} 个Token")

            return TokenRevokeResponse(
                status="success",
                message="已撤销用户的所有Token",
                revoked_count=revoked_count
            )
        else:
            # 如果没有提供user_id，则撤销Header中的Token
            if not credentials:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="必须提供user_id或Authorization Header中的Token"
                )

            token = credentials.credentials
            # 撤销Header中的Token
            from auth.token_blacklist import revoke_token
            try:
                payload = decode_jwt_token(token)
                if payload:
                    jti = payload.get('jti')
                    revoke_token(token, jti)
                    log.info(f"已撤销Header中的Token: {token[:20]}...")
                else:
                    log.warning("无法解码Header中的token进行撤销")

                return TokenRevokeResponse(
                    status="success",
                    message="Token已撤销"
                )
            except jwt.InvalidTokenError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid token"
                )

    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Token撤销时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token撤销失败"
        )

# 移除router级别的异常处理器，因为APIRouter不支持
# 异常处理将在各个端点中直接处理
