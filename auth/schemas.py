from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List # Add this import
from datetime import datetime
from enum import Enum
from fastapi import Form

class Token(BaseModel):
    """JWT token response schema"""
    access_token: str = Field(..., description="JWT访问令牌", example="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    token_type: str = Field(..., description="令牌类型", example="bearer")

    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJqb2huX2RvZSIsImV4cCI6MTY0MDk5NTIwMH0.signature",
                "token_type": "bearer"
            }
        }

# 自定义OAuth2密码表单，设置grant_type默认值
class OAuth2PasswordRequestFormCustom:
    """自定义OAuth2密码请求表单，grant_type默认为password"""

    def __init__(
        self,
        grant_type: str = Form(default="password", regex="password", description="授权类型，必须是'password'"),
        username: str = Form(..., description="用户名"),
        password: str = Form(..., description="密码"),
        scope: str = Form("", description="权限范围（可选）"),
        client_id: Optional[str] = Form(None, description="客户端ID（可选）"),
        client_secret: Optional[str] = Form(None, description="客户端密钥（可选）"),
    ):
        self.grant_type = grant_type
        self.username = username
        self.password = password
        self.scope = scope
        self.client_id = client_id
        self.client_secret = client_secret

class TokenData(BaseModel):
    """JWT token data schema"""
    username: Optional[str] = None

class UserBase(BaseModel):
    """Base user schema"""
    user_name: str
    email: EmailStr

class UserCreate(UserBase):
    """User creation schema"""
    password: str

class UserUpdate(BaseModel):
    """User update schema"""
    email: Optional[EmailStr] = None

class UserInDB(UserBase):
    """User database schema"""
    user_id: int
    password: str

    model_config = {
        "from_attributes": True
    }

# 移除RBAC相关模型 - 使用简化的角色系统

# Schema for creating a new conversation entry
class ConversationCreate(BaseModel):
    user_id: int = Field(..., description="用户ID", example=1)
    role: str = Field(..., description="角色类型: user(用户) 或 assistant(AI助手)", example="user")
    content: str = Field(..., description="对话内容", min_length=1, max_length=10000, example="你好，我想搜索关于AI的文档")

    class Config:
        schema_extra = {
            "example": {
                "user_id": 1,
                "role": "user",
                "content": "你好，我想搜索关于人工智能的文档"
            }
        }

# Schema for reading a conversation entry
class ConversationRead(BaseModel):
    id: int
    user_id: int
    role: str
    content: str
    timestamp: datetime

    model_config = {
        "from_attributes": True  # Enable ORM mode for automatic mapping
    }

# Schema for reading a list of conversations
class ConversationList(BaseModel):
    history: List[ConversationRead]

# 枚举类型
class RequestStatusEnum(str, Enum):
    pending = "pending"
    approved = "approved"
    rejected = "rejected"

class DocumentStatusEnum(str, Enum):
    active = "active"
    archived = "archived"
    deleted = "deleted"

class PermissionTypeEnum(str, Enum):
    read = "read"
    download = "download"

# 部门相关Schema
class DepartmentBase(BaseModel):
    dept_name: str
    description: Optional[str] = None
    parent_id: Optional[int] = None

class DepartmentCreate(DepartmentBase):
    pass

class DepartmentUpdate(BaseModel):
    dept_name: Optional[str] = None
    parent_id: Optional[int] = None
    leader: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    status: Optional[str] = None

class DepartmentInDB(BaseModel):
    dept_id: int
    dept_name: str
    parent_id: Optional[int] = None
    ancestors: Optional[str] = None
    order_num: Optional[int] = None
    leader: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    status: Optional[str] = None
    del_flag: Optional[str] = None
    create_by: Optional[str] = None
    create_time: Optional[datetime] = None
    update_by: Optional[str] = None
    update_time: Optional[datetime] = None

    model_config = {
        "from_attributes": True
    }

# 文档相关Schema
class DocumentBase(BaseModel):
    title: str
    description: Optional[str] = None

class DocumentCreate(DocumentBase):
    department_id: int

class DocumentUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    department_id: Optional[int] = None
    status: Optional[DocumentStatusEnum] = None

class DocumentInDB(DocumentBase):
    id: int
    file_path: str
    file_name: str
    file_type: str
    file_size: int
    uploader_id: int
    department_id: int
    status: DocumentStatusEnum
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

# 权限申请相关Schema
class PermissionRequestBase(BaseModel):
    document_id: int = Field(..., description="文档ID", example=123)
    permission_type: PermissionTypeEnum = Field(..., description="权限类型", example="download")
    reason: str = Field(..., description="申请理由", min_length=5, max_length=500, example="项目开发需要参考该技术文档")

class PermissionRequestCreate(PermissionRequestBase):
    class Config:
        schema_extra = {
            "example": {
                "document_id": 123,
                "permission_type": "download",
                "reason": "项目开发需要参考该技术架构进行系统设计"
            }
        }

class PermissionRequestUpdate(BaseModel):
    status: Optional[RequestStatusEnum] = Field(None, description="申请状态")
    review_comment: Optional[str] = Field(None, description="审核意见", max_length=1000, example="同意，用于项目开发")

# 权限申请审核相关Schema
class PermissionRequestApproval(BaseModel):
    comment: str = Field(..., description="审核意见", min_length=1, max_length=1000, example="同意，用于项目开发")

    class Config:
        schema_extra = {
            "example": {
                "comment": "同意，该用户确实需要此文档进行项目开发"
            }
        }

class PermissionRequestRejection(BaseModel):
    comment: str = Field(..., description="拒绝理由", min_length=1, max_length=1000, example="该文档涉及机密信息，暂不对外开放")

    class Config:
        schema_extra = {
            "example": {
                "comment": "该文档涉及机密信息，暂不对外开放"
            }
        }

class PermissionRequestInDB(PermissionRequestBase):
    id: int
    requester_id: int
    status: RequestStatusEnum
    reviewer_id: Optional[int] = None
    review_comment: Optional[str] = None
    reviewed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

# 文档权限相关Schema
class DocumentPermissionBase(BaseModel):
    user_id: int
    document_id: int
    permission_type: PermissionTypeEnum

class DocumentPermissionCreate(DocumentPermissionBase):
    expires_at: Optional[datetime] = None

class DocumentPermissionInDB(DocumentPermissionBase):
    id: int
    granted_by: int
    granted_at: datetime
    expires_at: Optional[datetime] = None
    is_active: bool

    model_config = {
        "from_attributes": True
    }

# 审计日志Schema
class AuditLogCreate(BaseModel):
    action: str
    resource_type: Optional[str] = None
    resource_id: Optional[int] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    details: Optional[str] = None
    status: str

class AuditLogInDB(AuditLogCreate):
    id: int
    user_id: Optional[int] = None
    created_at: datetime

    model_config = {
        "from_attributes": True
    }

# 服务A用户同步相关Schema
class ServiceAUserSync(BaseModel):
    """服务A用户同步数据Schema"""
    user_id: int = Field(..., description="服务A用户ID")
    user_name: str = Field(..., description="用户名")
    nick_name: Optional[str] = Field(None, description="昵称")
    email: Optional[str] = Field(None, description="邮箱")
    phonenumber: Optional[str] = Field(None, description="手机号")
    dept_id: Optional[int] = Field(None, description="部门ID")
    status: Optional[str] = Field("0", description="状态（0正常 1停用）")
    password: Optional[str] = Field(None, description="密码")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")

    model_config = {
        "from_attributes": True
    }

# 服务间认证相关Schema
class ServiceAuthData(BaseModel):
    """服务间认证数据Schema"""
    service_name: str = Field(..., description="服务名称")
    timestamp: str = Field(..., description="时间戳")
    signature: str = Field(..., description="签名")
    client_ip: Optional[str] = Field(None, description="客户端IP")

    model_config = {
        "from_attributes": True
    }

# Assembly conversation schemas
class AssemblyConversationCreate(BaseModel):
    """创建汇编对话记录"""
    session_id: str = Field(..., description="会话ID", example="session_20241201_123456")
    assembly_type: str = Field(..., description="汇编类型：custom 或 template", example="custom")
    request_data: dict = Field(..., description="请求参数", example={
        "project_type": "文书档案",
        "keywords": ["AI", "技术"],
        "question": "请汇编关于AI技术的文档"
    })
    response_data: Optional[dict] = Field(None, description="响应结果")
    echart_data: Optional[dict] = Field(None, description="图表数据")
    image_data: Optional[str] = Field(None, description="图片数据base64")
    status: Optional[str] = Field("completed", description="状态：pending, completed, failed")

class AssemblyConversationUpdate(BaseModel):
    """更新汇编对话记录"""
    response_data: Optional[dict] = Field(None, description="响应结果")
    echart_data: Optional[dict] = Field(None, description="图表数据")
    image_data: Optional[str] = Field(None, description="图片数据base64")
    status: Optional[str] = Field(None, description="状态")

class AssemblyConversationInDB(BaseModel):
    """数据库中的汇编对话记录"""
    id: int
    user_id: int
    session_id: str
    assembly_type: str
    request_data: dict
    response_data: Optional[dict]
    echart_data: Optional[dict]
    image_data: Optional[str]
    status: str
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

class AssemblyConversationListResponse(BaseModel):
    """汇编对话列表响应"""
    conversations: List[AssemblyConversationInDB]
    total: int
    page: int
    page_size: int

class AssemblyHistoryQuery(BaseModel):
    """汇编历史查询参数"""
    assembly_type: Optional[str] = Field(None, description="汇编类型过滤")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")