from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload
from typing import List, Optional, Any
from . import service, schemas, models
from .dependencies import get_db
from .models import Department, User, PermissionRequest, AuditLog, DocumentPermission, Document, SyncRecord
# from fastapi.security import OAuth2PasswordRequestForm  # 使用自定义表单替代
from utils.log import log
import sys
import os

# Add project root to sys.path for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import JWT auth middleware for multi-key support
try:
    from middleware.jwt_auth import get_current_user_from_jwt
except ImportError:
    # Fallback to original auth if middleware not available
    from .dependencies import get_current_user_from_jwt
    get_current_user_from_jwt = get_current_user_from_jwt

# Import simple-auth functions for unified implementation
try:
    from auth.simple_service_a_router import (
        get_simple_service_a_user,
        verify_service_a_token,
        revoke_service_a_token
    )
    SIMPLE_AUTH_AVAILABLE = True
except ImportError:
    SIMPLE_AUTH_AVAILABLE = False

# Unified auth service removed - functionality consolidated into simple-auth

# Import service auth after path setup
try:
    from middleware.service_auth import verify_service_request, user_sync_service
except ImportError:
    # Fallback if middleware not available
    verify_service_request = None
    user_sync_service = None

router = APIRouter(tags=["auth"])

# Auth routes
@router.post(
    "/token",
    response_model=schemas.Token,
    summary="用户登录获取访问令牌",
    description="使用用户名和密码进行身份验证，成功后返回JWT访问令牌",
    responses={
        200: {
            "description": "登录成功，返回访问令牌",
            "content": {
                "application/json": {
                    "example": {
                        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                        "token_type": "bearer"
                    }
                }
            }
        },
        401: {
            "description": "认证失败 - 用户名或密码错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Incorrect username or password"
                    }
                }
            }
        },
        422: {
            "description": "请求参数验证失败",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "username"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            }
        }
    }
)
async def login_for_access_token(
    form_data: schemas.OAuth2PasswordRequestFormCustom = Depends(),
    db: AsyncSession = Depends(get_db)
):
    # 使用原auth服务验证用户名密码
    user = await service.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 服务B使用自己的token生成方式（主要方式）
    # simple-auth主要用于外部iframe嵌入场景，需要完整部门信息
    # 服务B独立使用时，使用自己的用户管理系统
    access_token_expires = timedelta(minutes=service.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = service.create_access_token(
        data={
            "user_id": user.user_id,  # 使用user_id作为主要标识
            "user_name": user.user_name,  # 统一使用user_name字段
            "iss": "hngpt"  # 统一签发方
        },
        expires_delta=access_token_expires
    )
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "refresh_token": None,  # 暂时不实现refresh token
        "user_info": {
            "user_id": user.user_id,
            "user_name": user.user_name,  # 统一使用user_name
            "nick_name": user.nick_name or user.user_name,
            "email": user.email,
            "dept_id": user.dept_id,
            "is_active": user.is_active
        },
        "source": "service-b-auth"
    }

@router.post(
    "/register",
    response_model=schemas.UserInDB,
    summary="用户注册",
    description="创建新用户账户，需要提供用户名、邮箱和密码",
    responses={
        200: {
            "description": "注册成功，返回用户信息",
            "content": {
                "application/json": {
                    "example": {
                        "user_id": 1,
                        "user_name": "john_doe",
                        "nick_name": "john_doe",
                        "email": "<EMAIL>",
                        "is_active": True,
                        "created_at": "2024-01-01T12:00:00Z"
                    }
                }
            }
        },
        400: {
            "description": "注册失败 - 用户名已存在",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Username already registered"
                    }
                }
            }
        },
        422: {
            "description": "请求参数验证失败",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "email"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            }
        }
    }
)
async def register_user(
    user: schemas.UserCreate,
    db: AsyncSession = Depends(get_db)
):
    db_user = await service.get_user_by_username(db, username=user.user_name)
    if db_user:
        raise HTTPException(status_code=400, detail="Username already registered")
    hashed_password = service.get_password_hash(user.password)
    db_user = models.User(
        user_name=user.user_name,
        nick_name=user.user_name,  # 使用用户名作为默认昵称
        email=user.email,
        password=hashed_password,
        status='0'
    )
    # db.add(db_user)
    # await db.commit()
    # await db.refresh(db_user)
    return db_user

@router.get(
    "/users/me",
    # response_model=schemas.UserInDB,  # 移除响应模型限制，支持灵活的返回格式
    summary="获取当前用户信息",
    description="获取当前已认证用户的详细信息，需要有效的JWT令牌",
    responses={
        200: {
            "description": "成功返回用户信息",
            "content": {
                "application/json": {
                    "example": {
                        "user_id": 1,
                        "user_name": "john_doe",
                        "nick_name": "John Doe",
                        "email": "<EMAIL>",
                        "is_active": True,
                        "created_at": "2024-01-01T12:00:00Z",
                        "dept_id": 1,
                        "phone": "+86-13800138000"
                    }
                }
            }
        },
        401: {
            "description": "未认证 - 缺少或无效的JWT令牌",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Not authenticated"
                    }
                }
            }
        },
        403: {
            "description": "用户账户已被禁用",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Inactive user"
                    }
                }
            }
        }
    }
)
async def read_users_me(
    current_user: models.User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户信息 - 使用simple-auth兼容格式"""
    if SIMPLE_AUTH_AVAILABLE:
        try:
            # 获取用户完整信息
            result = await db.execute(
                select(models.User)
                .options(
                    selectinload(models.User.department)
                )
                .where(models.User.user_id == current_user.user_id)
            )
            complete_user = result.scalar_one_or_none()

            if complete_user:
                # 返回simple-auth兼容格式
                user_info = {
                    "user_id": complete_user.user_id,
                    "user_name": complete_user.user_name,
                    "nick_name": complete_user.nick_name or complete_user.user_name,
                    "email": complete_user.email,
                    "dept_id": complete_user.dept_id,
                    "dept_name": complete_user.department.dept_name if complete_user.department else None,
                    "roles": [role.name for role in complete_user.roles] if complete_user.roles else [],
                    "is_active": complete_user.is_active,
                    "source": "simple-auth-compatible"
                }

                log.info(f"用户信息获取使用simple-auth兼容格式: {complete_user.user_name}")
                return user_info

        except Exception as e:
            log.warning(f"使用simple-auth格式获取用户信息失败，回退到传统方式: {str(e)}")

    # 传统方式返回
    return current_user

@router.post("/logout")
async def logout(
    request: Request,
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """用户退出登录 - 使用simple-auth的token撤销逻辑"""
    if SIMPLE_AUTH_AVAILABLE:
        try:
            # 尝试使用simple-auth的token撤销功能
            from auth.simple_service_a_router import revoke_user_tokens

            # 撤销用户的所有token
            revoked_count = revoke_user_tokens(str(current_user.user_id))

            log.info(f"用户 {current_user.user_name} 退出登录，撤销了 {revoked_count} 个token")

            return {
                "message": "Successfully logged out",
                "revoked_tokens": revoked_count,
                "source": "simple-auth"
            }

        except Exception as e:
            log.warning(f"使用simple-auth撤销token失败: {str(e)}")

    # 传统退出方式
    log.info(f"用户 {current_user.user_name} 退出登录（传统方式）")
    return {
        "message": "Successfully logged out",
        "source": "auth-fallback"
    }

# Conversation routes
conversation_router = APIRouter(
    prefix="/conversations",
    tags=["conversations"],
    dependencies=[Depends(get_current_user_from_jwt)],
)

@conversation_router.post(
    "/",
    response_model=schemas.ConversationRead,
    summary="添加对话记录",
    description="为当前用户添加一条对话记录，支持用户消息和AI助手回复",
    responses={
        200: {
            "description": "对话记录添加成功",
            "content": {
                "application/json": {
                    "example": {
                        "id": 1,
                        "user_id": 1,
                        "role": "user",
                        "content": "你好，我想搜索关于AI的文档",
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                }
            }
        },
        403: {
            "description": "权限不足 - 不能为其他用户添加对话记录",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Cannot add conversation turn for another user"
                    }
                }
            }
        },
        422: {
            "description": "请求参数验证失败",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "content"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    }
                }
            }
        }
    }
)
async def create_conversation_turn(
    conversation: schemas.ConversationCreate,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    if conversation.user_id != current_user.user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Cannot add conversation turn for another user"
        )
    return await service.add_conversation_turn(db=db, conversation=conversation)

@conversation_router.get(
    "/{user_id}",
    response_model=List[schemas.ConversationRead],
    summary="获取用户对话历史",
    description="获取指定用户的对话历史记录，支持分页查询，用户只能查看自己的对话记录",
    responses={
        200: {
            "description": "成功返回对话历史列表",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "id": 1,
                            "user_id": 1,
                            "role": "user",
                            "content": "你好，我想搜索关于AI的文档",
                            "timestamp": "2024-01-01T12:00:00Z"
                        },
                        {
                            "id": 2,
                            "user_id": 1,
                            "role": "assistant",
                            "content": "您好！我可以帮您搜索人工智能相关的文档...",
                            "timestamp": "2024-01-01T12:00:05Z"
                        }
                    ]
                }
            }
        },
        403: {
            "description": "权限不足 - 不能查看其他用户的对话记录",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Not authorized to view this conversation history"
                    }
                }
            }
        },
        422: {
            "description": "请求参数验证失败",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["query", "skip"],
                                "msg": "ensure this value is greater than or equal to 0",
                                "type": "value_error.number.not_ge"
                            }
                        ]
                    }
                }
            }
        }
    }
)
async def read_user_conversations(
    user_id: int,
    skip: int = Query(0, ge=0, description="跳过的记录数，用于分页"),
    limit: int = Query(100, ge=1, le=1000, description="返回的最大记录数"),
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    if user_id != current_user.user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view this conversation history"
        )
    return await service.get_user_conversations(db, user_id=user_id, skip=skip, limit=limit)

# Include conversation router
router.include_router(conversation_router)

# Department management routes
@router.get("/departments", response_model=List[schemas.DepartmentInDB])
async def get_departments(
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """获取部门列表"""
    try:
        result = await db.execute(select(Department))
        departments = result.scalars().all()
        return departments
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取部门列表失败: {str(e)}")

@router.post("/departments", response_model=schemas.DepartmentInDB)
async def create_department(
    department: schemas.DepartmentCreate,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """创建部门"""
    try:
        db_department = Department(
            dept_name=department.dept_name,
            description=department.description,
            parent_id=department.parent_id
        )
        db.add(db_department)
        await db.commit()
        await db.refresh(db_department)
        return db_department
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"创建部门失败: {str(e)}")

@router.put("/departments/{department_id}", response_model=schemas.DepartmentInDB)
async def update_department(
    department_id: int,
    department: schemas.DepartmentUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """更新部门"""
    try:
        result = await db.execute(select(Department).where(Department.id == department_id))
        db_department = result.scalars().first()

        if not db_department:
            raise HTTPException(status_code=404, detail="部门不存在")

        if department.dept_name is not None:
            db_department.dept_name = department.dept_name
        if department.description is not None:
            db_department.description = department.description
        if department.is_active is not None:
            db_department.is_active = department.is_active

        await db.commit()
        await db.refresh(db_department)
        return db_department
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"更新部门失败: {str(e)}")

@router.delete("/departments/{department_id}")
async def delete_department(
    department_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """删除部门"""
    try:
        result = await db.execute(select(Department).where(Department.id == department_id))
        db_department = result.scalars().first()

        if not db_department:
            raise HTTPException(status_code=404, detail="部门不存在")

        await db.delete(db_department)
        await db.commit()
        return {"message": "部门删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"删除部门失败: {str(e)}")

# User management routes
@router.get("/users", response_model=List[schemas.UserInDB])
async def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """获取用户列表"""
    try:
        result = await db.execute(
            select(User)
            .offset(skip)
            .limit(limit)
        )
        users = result.scalars().all()
        return users
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")

@router.get("/users/{user_id}", response_model=schemas.UserInDB)
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """获取用户详情"""
    try:
        result = await db.execute(select(User).where(User.user_id == user_id))
        user = result.scalars().first()

        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户详情失败: {str(e)}")

@router.put("/users/{user_id}", response_model=schemas.UserInDB)
async def update_user(
    user_id: int,
    user_update: schemas.UserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """更新用户信息"""
    try:
        result = await db.execute(select(User).where(User.user_id == user_id))
        db_user = result.scalars().first()

        if not db_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        if user_update.email is not None:
            db_user.email = user_update.email
        if user_update.is_active is not None:
            db_user.is_active = user_update.is_active

        await db.commit()
        await db.refresh(db_user)
        return db_user
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"更新用户失败: {str(e)}")

@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """删除用户"""
    try:
        if user_id == current_user.user_id:
            raise HTTPException(status_code=400, detail="不能删除自己")

        result = await db.execute(select(User).where(User.user_id == user_id))
        db_user = result.scalars().first()

        if not db_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        await db.delete(db_user)
        await db.commit()
        return {"message": "用户删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"删除用户失败: {str(e)}")

# Permission request routes
@router.post(
    "/permission-requests",
    summary="提交权限申请",
    description="用户提交文档访问权限申请，支持查看(read)和下载(download)权限类型",
    responses={
        200: {
            "description": "权限申请提交成功",
            "content": {
                "application/json": {
                    "example": {
                        "id": 1,
                        "requester_id": 1,
                        "document_id": 123,
                        "permission_type": "download",
                        "reason": "项目开发需要参考该技术文档",
                        "status": "pending",
                        "created_at": "2024-01-01T12:00:00Z"
                    }
                }
            }
        },
        400: {
            "description": "申请失败 - 已存在相同申请或已拥有权限",
            "content": {
                "application/json": {
                    "examples": {
                        "duplicate_request": {
                            "summary": "重复申请",
                            "value": {"detail": "已存在相同的待处理申请"}
                        },
                        "already_has_permission": {
                            "summary": "已有权限",
                            "value": {"detail": "您已拥有该权限"}
                        }
                    }
                }
            }
        },
        404: {
            "description": "文档不存在",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "文档不存在"
                    }
                }
            }
        },
        422: {
            "description": "请求参数验证失败",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["query", "permission_type"],
                                "msg": "权限类型必须是 'read' 或 'download'",
                                "type": "value_error"
                            }
                        ]
                    }
                }
            }
        }
    }
)
async def create_permission_request(
    document_id: int = Query(..., description="文档ID"),
    permission_type: str = Query(..., description="权限类型: read(查看) 或 download(下载)"),
    reason: str = Query(..., description="申请理由"),
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """用户提交权限申请"""
    try:
        # 检查文档是否存在
        doc_result = await db.execute(
            select(Document).where(Document.id == document_id)
        )
        document = doc_result.scalars().first()
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 检查是否已有相同的待处理申请
        existing_result = await db.execute(
            select(PermissionRequest).where(
                PermissionRequest.requester_id == current_user.user_id,
                PermissionRequest.document_id == document_id,
                PermissionRequest.permission_type == permission_type,
                PermissionRequest.status == "pending"
            )
        )
        existing_request = existing_result.scalars().first()
        if existing_request:
            raise HTTPException(status_code=400, detail="已存在相同的待处理申请")

        # 检查用户是否已有该权限
        try:
            perm_result = await db.execute(
                select(DocumentPermission).where(
                    DocumentPermission.user_id == current_user.user_id,
                    DocumentPermission.document_id == document_id,
                    DocumentPermission.permission_type == permission_type,
                    DocumentPermission.is_active == True
                )
            )
            existing_permission = perm_result.scalars().first()
            if existing_permission:
                raise HTTPException(status_code=400, detail="您已拥有该权限")
        except HTTPException:
            raise
        except Exception as e:
            # 如果权限检查失败，继续创建申请
            print(f"权限检查失败，继续创建申请: {e}")

        # 创建权限申请
        permission_request = PermissionRequest(
            requester_id=current_user.user_id,
            document_id=document_id,
            permission_type=permission_type,
            reason=reason,
            status="pending"
        )

        db.add(permission_request)
        await db.commit()

        # 权限申请记录到日志文件
        log.info(f"权限申请: user_id={current_user.user_id}, document_id={document_id}, "
                f"permission_type={permission_type}, reason={reason}")

        # 返回简化的响应，避免序列化问题
        return {
            "id": permission_request.id,
            "requester_id": permission_request.requester_id,
            "document_id": permission_request.document_id,
            "permission_type": permission_request.permission_type,
            "reason": permission_request.reason,
            "status": permission_request.status,
            "created_at": permission_request.created_at
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"提交权限申请失败: {str(e)}")

@router.get(
    "/permission-requests",
    summary="获取权限申请列表",
    description="获取权限申请列表，支持按状态过滤和分页查询。不同角色用户看到的申请范围不同：\n"
                "- 系统管理员：可查看所有申请\n"
                "- 部门管理员：只能查看本部门用户的申请\n"
                "- 普通用户：只能查看自己提交的申请",
    responses={
        200: {
            "description": "成功返回权限申请列表",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "id": 1,
                            "requester_id": 2,
                            "requester_name": "张三",
                            "document_id": 123,
                            "document_title": "技术架构设计文档",
                            "permission_type": "download",
                            "reason": "项目开发需要参考该技术架构",
                            "status": "pending",
                            "reviewer_id": None,
                            "reviewer_name": "李四 (部门管理员)",
                            "review_comment": None,
                            "created_at": "2024-01-01T12:00:00Z",
                            "reviewed_at": None
                        },
                        {
                            "id": 2,
                            "requester_id": 3,
                            "requester_name": "王五",
                            "document_id": 124,
                            "document_title": "API接口文档",
                            "permission_type": "read",
                            "reason": "学习接口调用方法",
                            "status": "approved",
                            "reviewer_id": 1,
                            "reviewer_name": "李四",
                            "review_comment": "同意，用于学习目的",
                            "created_at": "2024-01-01T10:00:00Z",
                            "reviewed_at": "2024-01-01T11:00:00Z"
                        }
                    ]
                }
            }
        },
        403: {
            "description": "权限不足 - 用户无权查看权限申请",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Not authorized to view permission requests"
                    }
                }
            }
        },
        422: {
            "description": "请求参数验证失败",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["query", "status_filter"],
                                "msg": "状态必须是 pending, approved 或 rejected 之一",
                                "type": "value_error"
                            }
                        ]
                    }
                }
            }
        }
    }
)
async def get_permission_requests(
    status_filter: Optional[str] = Query(None, description="状态过滤: pending(待审核), approved(已批准), rejected(已拒绝)"),
    skip: int = Query(0, ge=0, description="跳过的记录数，用于分页"),
    limit: int = Query(100, ge=1, le=1000, description="返回的最大记录数"),
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """获取权限申请列表 - 只返回当前用户需要审核的申请"""
    try:
        # 检查当前用户的角色
        from services.user_service import UserService
        role_names = await UserService.get_user_roles(db, current_user.user_id)

        # 构建基础查询
        query = select(PermissionRequest)

        # 如果是系统管理员，可以看到所有申请
        if "admin" in role_names:
            # 系统管理员可以看到所有申请
            pass
        elif "deptAdmin" in role_names:
            # 部门管理员只能看到自己部门用户的申请
            # 获取当前用户管理的部门
            user_departments_result = await db.execute(
                select(Department)
                .join(models.user_department)
                .where(models.user_department.c.user_id == current_user.user_id)
            )
            user_departments = user_departments_result.scalars().all()
            department_ids = [dept.id for dept in user_departments]

            if department_ids:
                # 查找这些部门中用户的申请
                dept_user_ids_result = await db.execute(
                    select(models.user_department.c.user_id)
                    .where(models.user_department.c.department_id.in_(department_ids))
                )
                dept_user_ids = [row[0] for row in dept_user_ids_result.fetchall()]

                if dept_user_ids:
                    query = query.where(PermissionRequest.requester_id.in_(dept_user_ids))
                else:
                    # 如果部门没有用户，返回空结果
                    return []
            else:
                # 如果用户不属于任何部门，返回空结果
                return []
        else:
            # 普通用户只能看到自己提交的申请
            query = query.where(PermissionRequest.requester_id == current_user.user_id)

        if status_filter:
            query = query.where(PermissionRequest.status == status_filter)

        query = query.offset(skip).limit(limit).order_by(PermissionRequest.created_at.desc())
        result = await db.execute(query)
        requests = result.scalars().all()

        # 手动加载关联数据以避免异步问题
        enriched_requests = []
        for req in requests:
            # 获取申请人信息
            requester_result = await db.execute(
                select(User).where(User.user_id == req.requester_id)
            )
            requester = requester_result.scalars().first()

            # 获取文档信息
            document_result = await db.execute(
                select(Document).where(Document.id == req.document_id)
            )
            document = document_result.scalars().first()

            # 获取审核人信息
            reviewer = None
            if req.reviewer_id:
                reviewer_result = await db.execute(
                    select(User).where(User.user_id == req.reviewer_id)
                )
                reviewer = reviewer_result.scalars().first()

            # 确定应该由谁审核（如果还未分配审核人）
            assigned_reviewer = None
            if not req.reviewer_id and requester:
                # 获取申请人的部门
                user_dept_result = await db.execute(
                    select(Department)
                    .join(models.user_department)
                    .where(models.user_department.c.user_id == requester.id)
                )
                user_department = user_dept_result.scalars().first()

                if user_department:
                    # 查找部门管理员
                    dept_admin_result = await db.execute(
                        select(User)
                        .join(models.user_department)
                        .where(
                            models.user_department.c.department_id == user_department.id,
                        )
                    )
                    assigned_reviewer = dept_admin_result.scalars().first()

                # 如果没有找到部门管理员，使用系统管理员
                if not assigned_reviewer:
                    admin_result = await db.execute(
                        select(User)
                    )
                    assigned_reviewer = admin_result.scalars().first()

            enriched_requests.append({
                "id": req.id,
                "requester_id": req.requester_id,
                "document_id": req.document_id,
                "permission_type": req.permission_type,
                "reason": req.reason,
                "status": req.status,
                "reviewer_id": req.reviewer_id,
                "review_comment": req.review_comment,
                "reviewed_at": req.reviewed_at.isoformat() if req.reviewed_at else None,
                "created_at": req.created_at.isoformat() if req.created_at else None,
                "updated_at": req.updated_at.isoformat() if req.updated_at else None,
                "requester": {
                    "user_id": requester.user_id if requester else None,
                    "user_name": requester.user_name if requester else "未知用户",
                    "email": requester.email if requester else ""
                } if requester else {"user_name": "未知用户", "email": ""},
                "document": {
                    "id": document.id if document else None,
                    "title": document.title if document else "未知文档",
                    "file_name": document.file_name if document else ""
                } if document else {"title": "未知文档", "file_name": ""},
                "reviewer": {
                    "user_id": reviewer.user_id if reviewer else None,
                    "user_name": reviewer.user_name if reviewer else None,
                    "email": reviewer.email if reviewer else None
                } if reviewer else None,
                "assigned_reviewer": {
                    "user_id": assigned_reviewer.user_id if assigned_reviewer else None,
                    "user_name": assigned_reviewer.user_name if assigned_reviewer else "系统管理员",
                    "email": assigned_reviewer.email if assigned_reviewer else ""
                } if assigned_reviewer else {"user_name": "系统管理员", "email": ""}
            })

        return enriched_requests
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取权限申请失败: {str(e)}")

@router.post(
    "/permission-requests/{request_id}/approve",
    summary="批准权限申请",
    description="部门管理员或系统管理员批准用户的文档权限申请。批准后会自动为用户授予相应的文档访问权限",
    responses={
        200: {
            "description": "权限申请批准成功",
            "content": {
                "application/json": {
                    "example": {
                        "id": 1,
                        "requester_id": 2,
                        "document_id": 123,
                        "permission_type": "download",
                        "reason": "项目开发需要参考该技术架构",
                        "status": "approved",
                        "reviewer_id": 1,
                        "review_comment": "同意，用于项目开发",
                        "reviewed_at": "2024-01-01T12:30:00Z",
                        "created_at": "2024-01-01T12:00:00Z"
                    }
                }
            }
        },
        403: {
            "description": "权限不足 - 用户无权批准此申请",
            "content": {
                "application/json": {
                    "examples": {
                        "not_reviewer": {
                            "summary": "非指定审核人",
                            "value": {"detail": "您不是此申请的指定审核人"}
                        },
                        "not_department_admin": {
                            "summary": "非部门管理员",
                            "value": {"detail": "只有部门管理员可以审核申请"}
                        }
                    }
                }
            }
        },
        404: {
            "description": "权限申请不存在",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "权限申请不存在"
                    }
                }
            }
        },
        400: {
            "description": "申请状态错误 - 申请已被处理",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "申请已被处理，无法重复操作"
                    }
                }
            }
        }
    }
)
async def approve_permission_request(
    request_id: int,
    request_body: dict,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """批准权限申请"""
    try:
        comment = request_body.get("comment", "申请已批准")

        # 获取权限申请详情
        result = await db.execute(
            select(PermissionRequest).where(PermissionRequest.id == request_id)
        )
        request = result.scalars().first()

        if not request:
            raise HTTPException(status_code=404, detail="权限申请不存在")

        if request.status != models.RequestStatus.pending:
            raise HTTPException(status_code=400, detail="只能处理待审核的申请")

        # 创建新的文档权限
        doc_permission = DocumentPermission(
            user_id=request.requester_id,
            document_id=request.document_id,
            permission_type=request.permission_type,
            granted_by=current_user.user_id,
            is_active=True
        )
        db.add(doc_permission)

        # 更新申请状态
        request.status = models.RequestStatus.approved
        request.reviewer_id = current_user.user_id
        request.review_comment = comment
        request.reviewed_at = func.now()

        await db.commit()

        await db.commit()

        # 记录审计日志
        try:
            audit_log = AuditLog(
                user_id=current_user.user_id,
                action="approve_permission_request",
                resource_type="permission_request",
                resource_id=request_id,
                details=f"批准用户{request.requester_id}对文档{request.document_id}的{request.permission_type}权限申请",
                status="success"
            )
            db.add(audit_log)
            await db.commit()
        except Exception as audit_error:
            # 审计日志失败不影响主要功能
            print(f"审计日志记录失败: {audit_error}")

        return {"message": "权限申请已批准，用户已获得相应权限"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"批准权限申请失败: {str(e)}")

@router.post(
    "/permission-requests/{request_id}/reject",
    summary="拒绝权限申请",
    description="部门管理员或系统管理员拒绝用户的文档权限申请。拒绝时必须提供拒绝理由",
    responses={
        200: {
            "description": "权限申请拒绝成功",
            "content": {
                "application/json": {
                    "example": {
                        "message": "权限申请已拒绝",
                        "request_details": {
                            "id": 1,
                            "requester_id": 2,
                            "document_id": 123,
                            "permission_type": "download",
                            "status": "rejected",
                            "reviewer_id": 1,
                            "review_comment": "该文档涉及机密信息，暂不对外开放",
                            "reviewed_at": "2024-01-01T12:30:00Z"
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "examples": {
                        "missing_comment": {
                            "summary": "缺少拒绝理由",
                            "value": {"detail": "拒绝申请必须提供理由"}
                        },
                        "already_processed": {
                            "summary": "申请已被处理",
                            "value": {"detail": "申请已被处理，无法重复操作"}
                        }
                    }
                }
            }
        },
        403: {
            "description": "权限不足 - 用户无权拒绝此申请",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "您不是此申请的指定审核人"
                    }
                }
            }
        },
        404: {
            "description": "权限申请不存在",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "权限申请不存在"
                    }
                }
            }
        }
    }
)
async def reject_permission_request(
    request_id: int,
    request_body: dict,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """拒绝权限申请"""
    try:
        comment = request_body.get("comment", "")
        if not comment:
            raise HTTPException(status_code=400, detail="拒绝申请必须提供理由")

        # 获取权限申请详情
        result = await db.execute(
            select(PermissionRequest).where(PermissionRequest.id == request_id)
        )
        request = result.scalars().first()

        if not request:
            raise HTTPException(status_code=404, detail="权限申请不存在")

        if request.status != models.RequestStatus.pending:
            raise HTTPException(status_code=400, detail="只能处理待审核的申请")

        # 更新申请状态
        request.status = models.RequestStatus.rejected
        request.reviewer_id = current_user.user_id
        request.review_comment = comment
        request.reviewed_at = func.now()

        await db.commit()

        # 记录审计日志
        audit_log = AuditLog(
            user_id=current_user.user_id,
            action="reject_permission_request",
            resource_type="permission_request",
            resource_id=request_id,
            details=f"拒绝用户{request.requester_id}对文档{request.document_id}的{request.permission_type}权限申请，理由: {comment}",
            status="success"
        )
        db.add(audit_log)
        await db.commit()

        return {"message": "权限申请已拒绝"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"拒绝权限申请失败: {str(e)}")


@router.post(
    "/permission-requests/{request_id}/withdraw",
    summary="撤回权限申请",
    description="用户撤回自己提交的权限申请。只有申请人本人可以撤回，且只能撤回状态为pending(待审核)的申请",
    responses={
        200: {
            "description": "权限申请撤回成功",
            "content": {
                "application/json": {
                    "example": {
                        "message": "权限申请已撤回",
                        "request_details": {
                            "id": 1,
                            "requester_id": 2,
                            "document_id": 123,
                            "permission_type": "download",
                            "status": "withdrawn",
                            "reason": "项目开发需要参考该技术架构",
                            "created_at": "2024-01-01T12:00:00Z",
                            "withdrawn_at": "2024-01-01T12:30:00Z"
                        }
                    }
                }
            }
        },
        403: {
            "description": "权限不足",
            "content": {
                "application/json": {
                    "examples": {
                        "not_owner": {
                            "summary": "非申请人",
                            "value": {"detail": "只能撤回自己提交的申请"}
                        },
                        "already_processed": {
                            "summary": "申请已被处理",
                            "value": {"detail": "申请已被审核，无法撤回"}
                        }
                    }
                }
            }
        },
        404: {
            "description": "权限申请不存在",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "权限申请不存在"
                    }
                }
            }
        },
        400: {
            "description": "申请状态错误 - 申请已被处理无法撤回",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "只能撤回状态为pending的申请"
                    }
                }
            }
        }
    }
)
async def withdraw_permission_request(
    request_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """撤销权限申请 - 只有申请人可以撤销自己的待审核申请"""
    try:
        # 获取权限申请详情
        result = await db.execute(
            select(PermissionRequest).where(PermissionRequest.id == request_id)
        )
        request = result.scalars().first()

        if not request:
            raise HTTPException(status_code=404, detail="权限申请不存在")

        # 检查是否是申请人本人
        if request.requester_id != current_user.user_id:
            raise HTTPException(status_code=403, detail="只能撤销自己的申请")

        # 检查申请状态
        if request.status != models.RequestStatus.pending:
            raise HTTPException(status_code=400, detail="只能撤销待审核的申请")

        # 更新申请状态为已撤销
        request.status = models.RequestStatus.rejected  # 使用rejected状态表示撤销
        request.reviewer_id = current_user.user_id
        request.review_comment = "申请人主动撤销"
        request.reviewed_at = func.now()

        await db.commit()

        return {"message": "权限申请已撤销"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"撤销权限申请失败: {str(e)}")


@router.delete(
    "/permission-requests/{request_id}",
    summary="删除权限申请",
    description="用户删除自己提交的权限申请记录。只有申请人本人可以删除，通常用于清理历史申请记录",
    responses={
        200: {
            "description": "权限申请删除成功",
            "content": {
                "application/json": {
                    "example": {
                        "message": "权限申请已删除",
                        "deleted_request_id": 1
                    }
                }
            }
        },
        403: {
            "description": "权限不足 - 只能删除自己的申请",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "只能删除自己提交的申请"
                    }
                }
            }
        },
        404: {
            "description": "权限申请不存在",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "权限申请不存在"
                    }
                }
            }
        },
        400: {
            "description": "删除失败 - 数据库操作错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "删除权限申请失败: 数据库约束错误"
                    }
                }
            }
        }
    }
)
async def delete_permission_request(
    request_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """删除权限申请 - 只有申请人可以删除自己的申请"""
    try:
        # 获取权限申请详情
        result = await db.execute(
            select(PermissionRequest).where(PermissionRequest.id == request_id)
        )
        request = result.scalars().first()

        if not request:
            raise HTTPException(status_code=404, detail="权限申请不存在")

        # 检查是否是申请人本人
        if request.requester_id != current_user.user_id:
            raise HTTPException(status_code=403, detail="只能删除自己的申请")

        # 删除申请
        await db.delete(request)
        await db.commit()

        return {"message": "权限申请已删除"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"删除权限申请失败: {str(e)}")

# Audit log routes - 已禁用，改为使用日志文件
# @router.get("/audit-logs", response_model=List[schemas.AuditLogInDB])
# async def get_audit_logs(
#     action_filter: Optional[str] = Query(None, description="操作类型过滤"),
#     user_id_filter: Optional[int] = Query(None, description="用户ID过滤"),
#     skip: int = Query(0, ge=0),
#     limit: int = Query(100, ge=1, le=1000),
#     db: AsyncSession = Depends(get_db),
#     current_user: models.User = Depends(get_current_user_from_jwt)
# ):
#     """获取审计日志"""
#     try:
#         query = select(AuditLog).order_by(AuditLog.created_at.desc())
#
#         if action_filter:
#             query = query.where(AuditLog.action == action_filter)
#
#         if user_id_filter:
#             query = query.where(AuditLog.user_id == user_id_filter)
#
#         query = query.offset(skip).limit(limit)
#         result = await db.execute(query)
#         logs = result.scalars().all()
#         return logs
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"获取审计日志失败: {str(e)}")

# Document management routes
@router.get("/documents", response_model=List[schemas.DocumentInDB])
async def get_documents(
    department_id: Optional[int] = Query(None, description="部门ID过滤"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """获取文档列表"""
    try:
        query = select(Document).where(Document.status == models.DocumentStatus.active)

        if department_id:
            query = query.where(Document.department_id == department_id)

        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        documents = result.scalars().all()
        return documents
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")

@router.delete("/documents/{document_id}")
async def delete_document(
    document_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """删除文档"""
    try:
        result = await db.execute(select(Document).where(Document.id == document_id))
        document = result.scalars().first()

        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 软删除
        document.status = models.DocumentStatus.deleted
        await db.commit()
        return {"message": "文档删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"删除文档失败: {str(e)}")

# Document permission management routes
@router.get("/documents/{document_id}/permissions", response_model=List[schemas.DocumentPermissionInDB])
async def get_document_permissions(
    document_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """获取文档权限列表"""
    try:
        result = await db.execute(
            select(DocumentPermission)
            .where(DocumentPermission.document_id == document_id)
        )
        permissions = result.scalars().all()
        return permissions
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档权限失败: {str(e)}")

@router.post("/documents/{document_id}/permissions")
async def grant_document_permission(
    document_id: int,
    user_id: int,
    permission_type: str,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """授予文档权限"""
    try:
        # 检查是否已存在相同权限
        result = await db.execute(
            select(DocumentPermission)
            .where(
                DocumentPermission.document_id == document_id,
                DocumentPermission.user_id == user_id,
                DocumentPermission.permission_type == permission_type
            )
        )
        existing_permission = result.scalars().first()

        if existing_permission:
            raise HTTPException(status_code=400, detail="权限已存在")

        # 创建新权限
        doc_permission = DocumentPermission(
            user_id=user_id,
            document_id=document_id,
            permission_type=permission_type,
            granted_by=current_user.user_id
        )
        db.add(doc_permission)
        await db.commit()

        # 记录审计日志
        audit_log = AuditLog(
            user_id=current_user.user_id,
            action="grant_document_permission",
            resource_type="document",
            resource_id=document_id,
            details=f"授予用户{user_id}对文档{document_id}的{permission_type}权限",
            status="success"
        )
        db.add(audit_log)
        await db.commit()

        return {"message": "权限授予成功"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"授予权限失败: {str(e)}")

@router.delete("/documents/{document_id}/permissions/{permission_id}")
async def revoke_document_permission(
    document_id: int,
    permission_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """撤销文档权限"""
    try:
        result = await db.execute(
            select(DocumentPermission)
            .where(
                DocumentPermission.id == permission_id,
                DocumentPermission.document_id == document_id
            )
        )
        permission = result.scalars().first()

        if not permission:
            raise HTTPException(status_code=404, detail="权限不存在")

        await db.delete(permission)
        await db.commit()

        # 记录审计日志
        audit_log = AuditLog(
            user_id=current_user.user_id,
            action="revoke_document_permission",
            resource_type="document",
            resource_id=document_id,
            details=f"撤销用户{permission.user_id}对文档{document_id}的{permission.permission_type}权限",
            status="success"
        )
        db.add(audit_log)
        await db.commit()

        return {"message": "权限撤销成功"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"撤销权限失败: {str(e)}")

# 移除用户角色管理路由 - 使用简化的角色系统
        if existing_result.fetchone():
            raise HTTPException(status_code=400, detail="用户已拥有该角色")

        # 分配角色
        await db.execute(
            {"user_id": user_id, "role_id": role_id}
        )
        await db.commit()

        # 记录审计日志
        audit_log = AuditLog(
            user_id=current_user.user_id,
            resource_type="user",
            resource_id=user_id,
            details=f"为用户{user_id}分配角色{role.name}",
            status="success"
        )
        db.add(audit_log)
        await db.commit()

        return {"message": "角色分配成功"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=f"分配角色失败: {str(e)}")

# 移除角色删除路由 - 使用简化的角色系统

# 重复的部门管理路由已删除 - 使用前面第369-433行定义的版本

# 文档管理路由
@router.get("/departments/{department_id}/documents", response_model=List[schemas.DocumentInDB])
async def get_department_documents(
    department_id: int,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    # 检查权限：用户必须是该部门成员或管理员
    is_admin = any(role.name in ["admin"] for role in current_user.roles)
    is_dept_member = any(dept.id == department_id for dept in current_user.departments)

    if not (is_admin or is_dept_member):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view documents from this department"
        )

    return await service.get_documents_by_department(db, department_id, skip=skip, limit=limit)

# 重复的权限申请路由已删除 - 使用前面第554行定义的版本

@router.get("/departments/{department_id}/permission-requests", response_model=List[schemas.PermissionRequestInDB])
async def get_department_permission_requests(
    department_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    # 检查权限：只有部门管理员可以查看权限申请
    is_dept_admin = await service.is_department_admin(db, current_user.user_id, department_id)
    if not is_dept_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view permission requests for this department"
        )

    return await service.get_pending_requests_for_department(db, department_id)

# 重复的权限申请审批路由已删除 - 使用前面第947行和第1075行定义的POST方法版本

# 服务间认证和用户同步API
@router.post("/service/sync-user")
async def sync_user_from_service_a(
    user_data: schemas.ServiceAUserSync,
    db: AsyncSession = Depends(get_db),
    service_auth: Any = Depends(verify_service_request)
):
    """从服务A同步用户信息"""
    if not user_sync_service:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Service authentication not available"
        )

    # 验证请求来源
    if service_auth["service_name"] != "service_a":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only service A can sync users"
        )

    user = await user_sync_service.sync_user_from_service_a(user_data.model_dump())
    if user:
        return {
            "code": 200,
            "message": "User synced successfully",
            "data": {
                "id": user.user_id,
                "user_name": user.user_name,  # 统一使用user_name
                "email": user.email,
                "is_active": user.is_active
            }
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to sync user"
        )

@router.get("/service/user/{user_id}")
async def get_user_for_service_a(
    user_id: int,
    service_auth: Any = Depends(verify_service_request),
    db: AsyncSession = Depends(get_db)
):
    """为服务A提供用户信息"""
    if not user_sync_service:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Service authentication not available"
        )

    # 验证请求来源
    if service_auth["service_name"] != "service_a":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only service A can access user information"
        )

    user_info = await user_sync_service.get_user_info_for_service_a(user_id)
    if user_info:
        return {
            "code": 200,
            "message": "User information retrieved successfully",
            "data": user_info
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

@router.post("/service/verify-document-access")
async def verify_document_access_for_service_a(
    request_data: Any,
    service_auth: Any = Depends(verify_service_request),
    db: AsyncSession = Depends(get_db)
):
    """为服务A验证文档访问权限"""
    # 验证请求来源
    if service_auth["service_name"] != "service_a":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only service A can verify document access"
        )

    user_id = request_data.get("user_id")
    document_path = request_data.get("document_path")
    permission_type = request_data.get("permission_type", "read")

    if not all([user_id, document_path]):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing required parameters: user_id, document_path"
        )

    # 这里可以根据document_path查找对应的文档ID，然后检查权限
    # 暂时使用简化的权限检查
    from app import check_file_permission
    has_permission = await check_file_permission(user_id, document_path, permission_type)

    return {
        "code": 200,
        "message": "Document access verification completed",
        "data": {
            "user_id": user_id,
            "document_path": document_path,
            "permission_type": permission_type,
            "has_permission": has_permission
        }
    }

# 用户同步管理API
@router.post("/sync/users/trigger")
async def trigger_user_sync(
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """手动触发用户同步"""
    try:
        from sync.user_sync_service import user_sync_service

        # 检查用户权限（只有管理员可以触发同步）
        from services.user_service import UserService
        roles = await UserService.get_user_roles(db, current_user.user_id)
        is_admin = 'admin' in roles

        if not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以触发用户同步"
            )

        # 执行同步
        synced_count = await user_sync_service.sync_users()

        return {
            "message": "用户同步完成",
            "synced_count": synced_count
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"用户同步失败: {str(e)}")

@router.post("/sync/users/full")
async def trigger_full_user_sync(
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """手动触发全量用户同步"""
    try:
        from sync.user_sync_service import user_sync_service

        # 检查用户权限
        from services.user_service import UserService
        roles = await UserService.get_user_roles(db, current_user.user_id)
        is_admin = 'admin' in roles

        if not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以触发全量同步"
            )

        # 执行全量同步
        synced_count = await user_sync_service.full_sync_users()

        return {
            "message": "全量用户同步完成",
            "synced_count": synced_count
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"全量用户同步失败: {str(e)}")

@router.get("/sync/records")
async def get_sync_records(
    limit: int = 20,
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """获取同步记录"""
    try:
        # 检查用户权限
        from services.user_service import UserService
        roles = await UserService.get_user_roles(db, current_user.user_id)
        is_admin = 'admin' in roles

        if not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以查看同步记录"
            )

        # 获取同步记录
        result = await db.execute(
            select(SyncRecord)
            .order_by(SyncRecord.created_at.desc())
            .limit(limit)
        )
        sync_records = result.scalars().all()

        return [
            {
                "id": record.id,
                "sync_type": record.sync_type,
                "sync_time": record.sync_time,
                "synced_count": record.synced_count,
                "status": record.status,
                "details": record.details,
                "created_at": record.created_at
            }
            for record in sync_records
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取同步记录失败: {str(e)}")

@router.get("/sync/status")
async def get_sync_status(
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(get_current_user_from_jwt)
):
    """获取同步状态"""
    try:
        from sync.user_sync_service import user_sync_service
        from sync.service_a_database import service_a_db

        # 检查用户权限
        from services.user_service import UserService
        roles = await UserService.get_user_roles(db, current_user.user_id)
        is_admin = 'admin' in roles

        if not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以查看同步状态"
            )

        # 获取最后同步记录
        result = await db.execute(
            select(SyncRecord)
            .where(SyncRecord.sync_type == 'user_sync')
            .order_by(SyncRecord.created_at.desc())
            .limit(1)
        )
        last_sync = result.scalars().first()

        # 测试服务A连接
        service_a_connected = await service_a_db.test_connection()

        # 统计用户数量
        local_users_result = await db.execute(select(func.count(models.user.user_id)))
        local_users_count = local_users_result.scalar()

        synced_users_result = await db.execute(
            select(func.count(models.user.user_id))
            .where(models.User.service_a_user_id.isnot(None))
        )
        synced_users_count = synced_users_result.scalar()

        return {
            "sync_enabled": user_sync_service.sync_enabled,
            "sync_interval_minutes": user_sync_service.sync_interval,
            "service_a_connected": service_a_connected,
            "last_sync": {
                "sync_time": last_sync.sync_time if last_sync else None,
                "synced_count": last_sync.synced_count if last_sync else 0,
                "status": last_sync.status if last_sync else "never"
            } if last_sync else None,
            "statistics": {
                "local_users_count": local_users_count,
                "synced_users_count": synced_users_count,
                "sync_coverage": f"{(synced_users_count/local_users_count*100):.1f}%" if local_users_count > 0 else "0%"
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取同步状态失败: {str(e)}")
