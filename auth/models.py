import sys
import os

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from sqlalchemy import (Column, Integer, String, Boolean, DateTime, Text, BigInteger, Enum, JSON)
from sqlalchemy.sql import func
from database import Base  # 从统一的database模块导入Base
import enum

# Enum definitions
class RequestStatus(enum.Enum):
    pending = "pending"
    approved = "approved"
    rejected = "rejected"
    withdrawn = "withdrawn"

class DocumentStatus(enum.Enum):
    active = "active"
    archived = "archived"
    deleted = "deleted"

class PermissionType(enum.Enum):
    read = "read"
    download = "download"

class AssemblyType(enum.Enum):
    custom = "custom"
    template = "template"

class AssemblyStatus(enum.Enum):
    pending = "pending"
    completed = "completed"
    failed = "failed"

# 移除RBAC关联表 - 使用简化的角色系统

class User(Base):
    __table_args__ = {'extend_existing': True}
    __tablename__ = "users"

    # 主键和基本信息
    user_id = Column(BigInteger, primary_key=True, index=True, comment='用户ID')
    dept_id = Column(BigInteger, comment='部门ID')
    user_name = Column(String(255), nullable=False, comment='用户账号')
    nick_name = Column(String(255), nullable=False, comment='用户昵称')
    user_type = Column(String(2), comment='用户类型（00系统用户）')
    email = Column(String(50), comment='用户邮箱')
    phonenumber = Column(String(11), comment='手机号码')
    sex = Column(String(1), comment='用户性别（0男 1女 2未知）')
    avatar = Column(String(100), comment='头像地址')
    password = Column(String(100), comment='密码')
    status = Column(String(1), comment='帐号状态（0正常 1停用）')
    del_flag = Column(String(1), comment='删除标志（0代表存在 2代表删除）')
    login_ip = Column(String(128), comment='最后登录IP')
    login_date = Column(DateTime, comment='最后登录时间')
    create_by = Column(String(64), comment='创建者')
    create_time = Column(DateTime, comment='创建时间')
    update_by = Column(String(64), comment='更新者')
    update_time = Column(DateTime, comment='更新时间')
    remark = Column(String(500), comment='备注')




# 移除RBAC模型 - 使用简化的角色系统（admin, deptAdmin, user）

class ConversationHistory(Base):
    __table_args__ = {'extend_existing': True}
    __tablename__ = "conversation_history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False)
    role = Column(String(20), nullable=False)  # e.g., 'user' or 'assistant'/'llm'
    content = Column(Text, nullable=False) # The actual message content
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

class Department(Base):
    __table_args__ = {'extend_existing': True}
    __tablename__ = "departments"

    dept_id = Column(BigInteger, primary_key=True, index=True, comment='部门ID')
    parent_id = Column(BigInteger, comment='父部门ID')
    ancestors = Column(Text, comment='祖级列表')
    dept_name = Column(String(30), comment='部门名称')
    order_num = Column(Integer, comment='显示顺序')
    leader = Column(String(20), comment='负责人')
    phone = Column(String(11), comment='联系电话')
    email = Column(String(50), comment='邮箱')
    status = Column(String(1), comment='部门状态（0正常 1停用）')
    del_flag = Column(String(1), comment='删除标志（0代表存在 2代表删除）')
    create_by = Column(String(64), comment='创建者')
    create_time = Column(DateTime, comment='创建时间')
    update_by = Column(String(64), comment='更新者')
    update_time = Column(DateTime, comment='更新时间')

class Document(Base):
    __table_args__ = {'extend_existing': True}
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    doc_id = Column(String(32), unique=True, nullable=False, index=True, comment="文档唯一标识（MD5哈希值）")
    title = Column(String(255), nullable=False, comment="文档标题")
    file_path = Column(String(500), nullable=False, comment="文件路径")
    project_name = Column(String(255), comment="项目名称")
    owner_id = Column(Integer, nullable=False, comment="文档所有者ID")
    department_id = Column(Integer, comment="所属部门ID")
    file_size = Column(Integer, comment="文件大小（字节）")
    file_type = Column(String(50), comment="文件类型")
    status = Column(Enum(DocumentStatus, native_enum=False), default=DocumentStatus.active, comment="文档状态")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

class PermissionRequest(Base):
    __table_args__ = {'extend_existing': True}
    __tablename__ = "permission_requests"

    id = Column(Integer, primary_key=True, index=True)
    requester_id = Column(Integer, nullable=False)
    document_id = Column(Integer, nullable=False)
    permission_type = Column(Enum(PermissionType, native_enum=False), nullable=False)
    reason = Column(Text, nullable=False)
    status = Column(Enum(RequestStatus, native_enum=False), default=RequestStatus.pending)
    reviewer_id = Column(Integer)
    review_comment = Column(Text)
    reviewed_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class DocumentPermission(Base):
    __table_args__ = {'extend_existing': True}
    __tablename__ = "document_permissions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False)
    document_id = Column(Integer, nullable=False)
    permission_type = Column(Enum(PermissionType, native_enum=False), nullable=False)
    granted_by = Column(Integer, nullable=False)
    granted_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True))
    is_active = Column(Boolean, default=True)

class AuditLog(Base):
    __table_args__ = {'extend_existing': True}
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer)
    action = Column(String(100), nullable=False)
    resource_type = Column(String(50))
    resource_id = Column(Integer)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    details = Column(Text)
    status = Column(String(20), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class SyncRecord(Base):
    __table_args__ = {'extend_existing': True}
    __tablename__ = "sync_records"

    id = Column(Integer, primary_key=True, index=True)
    sync_type = Column(String(50), nullable=False)  # 'user', 'department', etc.
    source_id = Column(Integer)  # ID in source system
    target_id = Column(Integer)  # ID in target system
    sync_status = Column(String(20), default='pending')  # 'pending', 'success', 'failed'
    sync_direction = Column(String(20))  # 'to_service_a', 'from_service_a'
    error_message = Column(Text)
    last_sync_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class AssemblyConversation(Base):
    __table_args__ = {'extend_existing': True}
    __tablename__ = "assembly_conversations"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    session_id = Column(String(64), nullable=False, index=True)
    assembly_type = Column(Enum(AssemblyType, native_enum=False), nullable=False)
    request_data = Column(JSON, nullable=False)  # 存储汇编参数
    response_data = Column(JSON)  # 存储汇编结果
    echart_data = Column(JSON)  # 存储图表数据
    image_data = Column(Text)  # 存储图片base64
    status = Column(Enum(AssemblyStatus, native_enum=False), default=AssemblyStatus.completed)
    created_at = Column(DateTime, nullable=False)  # 由应用程序设置时间
    updated_at = Column(DateTime, nullable=False)  # 由应用程序设置时间

class ProjectExtract(Base):
    """项目提取表-基于项目维度"""
    __table_args__ = {'extend_existing': True}
    __tablename__ = "project_extract"

    id = Column(BigInteger, primary_key=True, index=True, comment='自增主键')
    project_name = Column(String(500), nullable=False, comment='项目名称')
    project_key = Column(String(255), nullable=False, unique=True, comment='项目唯一标识（基于项目名称生成）')
    project_no = Column(String(100), comment='项目编号')
    start_date = Column(DateTime, comment='开始日期')
    end_date = Column(DateTime, comment='结束日期')
    total_investment = Column(BigInteger, comment='总投资金额（单位：万元）')
    responsible_unit = Column(Text, comment='承担单位')
    leader = Column(String(200), comment='项目负责人')
    research_points = Column(Text, comment='主要研究内容')
    innovation = Column(Text, comment='创新点')
    main_deliverables = Column(Text, comment='主要交付成果')
    patent = Column(Text, comment='专利信息')
    created_at = Column(DateTime, server_default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment='更新时间')

class ConferenceExtract(Base):
    """会议文书提取表-基于文书维度"""
    __table_args__ = {'extend_existing': True}
    __tablename__ = "conference_extract"

    id = Column(BigInteger, primary_key=True, index=True, comment='自增主键')
    conference_name = Column(String(500), nullable=False, comment='文书名称')
    conference_key = Column(String(255), nullable=False, unique=True, comment='文书唯一标识（基于文书名称生成）')
    conference_no = Column(String(100), comment='文书编号')
    date = Column(DateTime, comment='文书日期')
    type = Column(String(100), comment='文书类型')
    organizer = Column(Text, comment='发起组织')
    participants = Column(Text, comment='参与者/接收者')
    summary = Column(Text, comment='文书摘要')
    created_at = Column(DateTime, server_default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment='更新时间')

class ProjectArchive(Base):
    """项目档案表"""
    __table_args__ = {'extend_existing': True}
    __tablename__ = "project_archives"

    id = Column(Integer, primary_key=True, index=True)
    project_key = Column(String(255), nullable=False, index=True, comment="项目唯一标识")
    project_name = Column(String(255), nullable=False, comment="项目名称")
    description = Column(Text, comment="项目描述")
    owner_id = Column(Integer, nullable=False, comment="项目所有者ID")
    department_id = Column(Integer, comment="所属部门ID")
    status = Column(String(20), default='active', comment="状态")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

class DocumentArchive(Base):
    """文书档案表"""
    __table_args__ = {'extend_existing': True}
    __tablename__ = "document_archives"

    id = Column(Integer, primary_key=True, index=True)
    document_key = Column(String(255), nullable=False, index=True, comment="文书唯一标识")
    document_name = Column(String(255), nullable=False, comment="文书名称")
    document_type = Column(String(100), comment="文书类型")
    content = Column(Text, comment="文书内容")
    owner_id = Column(Integer, nullable=False, comment="文书所有者ID")
    department_id = Column(Integer, comment="所属部门ID")
    status = Column(String(20), default='active', comment="状态")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")