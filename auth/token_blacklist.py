"""
JWT Token黑名单管理
实现Token撤销机制，解决用户切换时的安全问题
"""

import time
import threading
from typing import Set, Dict
from utils.log import log

class TokenBlacklist:
    """Token黑名单管理器"""
    
    def __init__(self):
        self._blacklist: Set[str] = set()
        self._user_tokens: Dict[str, Set[str]] = {}  # user_id -> set of tokens
        self._token_users: Dict[str, str] = {}       # token -> user_id
        self._lock = threading.RLock()
        
        # 定期清理过期Token
        self._cleanup_thread = threading.Thread(target=self._periodic_cleanup, daemon=True)
        self._cleanup_thread.start()
        
        log.info("Token黑名单管理器已初始化")
    
    def add_token(self, token: str, user_id: str, jti: str = None):
        """添加Token到用户映射"""
        with self._lock:
            # 使用jti作为Token标识，如果没有jti则使用完整token
            token_id = jti if jti else token
            
            if user_id not in self._user_tokens:
                self._user_tokens[user_id] = set()
            
            self._user_tokens[user_id].add(token_id)
            self._token_users[token_id] = user_id
            
            log.debug(f"Token已添加到用户 {user_id} 的映射中")
    
    def revoke_token(self, token: str, jti: str = None):
        """撤销单个Token"""
        with self._lock:
            token_id = jti if jti else token
            self._blacklist.add(token_id)
            
            # 从用户映射中移除
            if token_id in self._token_users:
                user_id = self._token_users[token_id]
                if user_id in self._user_tokens:
                    self._user_tokens[user_id].discard(token_id)
                del self._token_users[token_id]
            
            log.info(f"Token {token_id[:20]}... 已被撤销")
    
    def revoke_user_tokens(self, user_id: str):
        """撤销用户的所有Token"""
        with self._lock:
            if user_id in self._user_tokens:
                tokens_to_revoke = self._user_tokens[user_id].copy()
                
                for token_id in tokens_to_revoke:
                    self._blacklist.add(token_id)
                    if token_id in self._token_users:
                        del self._token_users[token_id]
                
                # 清空用户的Token集合
                self._user_tokens[user_id].clear()
                
                log.info(f"用户 {user_id} 的 {len(tokens_to_revoke)} 个Token已被撤销")
                return len(tokens_to_revoke)
            
            return 0
    
    def is_token_revoked(self, token: str, jti: str = None) -> bool:
        """检查Token是否已被撤销"""
        with self._lock:
            token_id = jti if jti else token
            return token_id in self._blacklist
    
    def get_user_token_count(self, user_id: str) -> int:
        """获取用户当前有效Token数量"""
        with self._lock:
            if user_id in self._user_tokens:
                return len(self._user_tokens[user_id])
            return 0
    
    def get_blacklist_size(self) -> int:
        """获取黑名单大小"""
        with self._lock:
            return len(self._blacklist)
    
    def clear_expired_tokens(self, expired_jtis: Set[str]):
        """清理过期的Token"""
        with self._lock:
            # 从黑名单中移除过期Token
            self._blacklist -= expired_jtis
            
            # 从用户映射中移除过期Token
            for jti in expired_jtis:
                if jti in self._token_users:
                    user_id = self._token_users[jti]
                    if user_id in self._user_tokens:
                        self._user_tokens[user_id].discard(jti)
                    del self._token_users[jti]
            
            if expired_jtis:
                log.info(f"已清理 {len(expired_jtis)} 个过期Token")
    
    def _periodic_cleanup(self):
        """定期清理过期Token（每小时执行一次）"""
        while True:
            try:
                time.sleep(3600)  # 1小时
                
                # 这里可以添加更复杂的清理逻辑
                # 例如：检查JWT的过期时间，清理已过期的Token
                current_size = self.get_blacklist_size()
                if current_size > 10000:  # 如果黑名单过大，进行清理
                    log.warning(f"Token黑名单过大 ({current_size})，建议实现更精确的过期清理机制")
                
            except Exception as e:
                log.error(f"定期清理Token时出错: {str(e)}")

# 全局Token黑名单实例
token_blacklist = TokenBlacklist()

def revoke_token(token: str, jti: str = None):
    """撤销Token的便捷函数"""
    token_blacklist.revoke_token(token, jti)

def revoke_user_tokens(user_id: str) -> int:
    """撤销用户所有Token的便捷函数"""
    return token_blacklist.revoke_user_tokens(user_id)

def is_token_revoked(token: str, jti: str = None) -> bool:
    """检查Token是否被撤销的便捷函数"""
    return token_blacklist.is_token_revoked(token, jti)

def add_token_to_user(token: str, user_id: str, jti: str = None):
    """添加Token到用户映射的便捷函数"""
    token_blacklist.add_token(token, user_id, jti)
