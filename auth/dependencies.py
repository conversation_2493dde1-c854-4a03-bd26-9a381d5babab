import sys
import os

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import JWTError, jwt
from sqlalchemy.ext.asyncio import AsyncSession
from . import service, models, schemas
from database import SessionLocal, get_db, Base  # 从统一的database模块导入
from utils.config import config

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token") # Relative path from app root

# --- Database Dependency ---
# The get_db function is now imported from database.py
# def get_db():
#    ...

# --- Authentication Dependencies ---
async def get_current_user(
    db: AsyncSession = Depends(get_db), token: str = Depends(oauth2_scheme)
) -> models.User:
    """Dependency to get the current user from JWT token."""
    user = await service.get_current_user(db, token=token)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user

async def get_current_active_user(
    current_user: models.User = Depends(get_current_user)
) -> models.User:
    """Dependency to get the current active user."""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# --- RBAC Dependencies (Example) ---
    """Dependency factory to check if the user has a specific role."""
    async def role_checker(current_user: models.User = Depends(get_current_active_user)):
        if required_role not in [role.name for role in current_user.roles]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"User does not have the required role: {required_role}"
            )
        return current_user
    return role_checker

# 移除RBAC权限检查依赖 - 使用简化的角色系统