"""
重构后的文档权限服务
优化了查询逻辑，统一了返回格式，提升了性能
"""

from typing import List, Optional, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload
from auth.models import User, Department, Document, DocumentPermission, PermissionRequest, PermissionType, RequestStatus
from utils.log import log
from datetime import datetime, timezone
import urllib.parse


def normalize_user_roles_for_permission(user_roles: list) -> dict:
    """
    标准化用户角色处理（文档权限服务专用）

    Args:
        user_roles: 用户角色列表

    Returns:
        dict: 包含标准化角色判断结果
    """
    if not user_roles:
        user_roles = []

    # 检查具体角色
    is_admin = 'admin' in user_roles
    is_dept_admin = 'deptAdmin' in user_roles

    # 任何非admin/deptAdmin角色的用户都按user处理
    is_user = not (is_admin or is_dept_admin)

    # 确定有效角色
    if is_admin:
        effective_role = 'admin'
    elif is_dept_admin:
        effective_role = 'deptAdmin'
    else:
        effective_role = 'user'  # 默认为普通用户

    return {
        'is_admin': is_admin,
        'is_dept_admin': is_dept_admin,
        'is_user': is_user,
        'effective_role': effective_role,
        'original_roles': user_roles
    }


class DocumentPermissionService:
    """重构后的文档权限服务类"""

    # 标准化的返回格式
    @staticmethod
    def _create_success_response(
        has_permission: bool,
        message: str = "权限检查完成",
        permission_source: Optional[str] = None,
        user_info: Optional[Dict] = None,
        document_info: Optional[Dict] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """创建标准化的成功响应"""
        response = {
            "success": True,
            "has_permission": has_permission,
            "permission": has_permission,  # 兼容旧版本
            "message": message,
            "permission_source": permission_source,
            "user_info": user_info,
            "document_info": document_info
        }
        response.update(kwargs)
        return response

    @staticmethod
    def _create_error_response(
        message: str,
        error_code: str = "PERMISSION_CHECK_FAILED",
        **kwargs
    ) -> Dict[str, Any]:
        """创建标准化的错误响应"""
        response = {
            "success": False,
            "has_permission": False,
            "permission": False,  # 兼容旧版本
            "message": message,
            "error_code": error_code,
            "permission_source": None,
            "user_info": None,
            "document_info": None
        }
        response.update(kwargs)
        return response

    @staticmethod
    async def check_document_access_by_doc_id(
        db: AsyncSession,
        user_id: int,
        doc_id: str,
        permission_type: str = "read",
        user_roles: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        通过doc_id检查文档访问权限（推荐方式）
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            doc_id: 文档ID
            permission_type: 权限类型 (read, download)
            
        Returns:
            标准化的权限检查结果
        """
        try:
            # 1. 获取用户信息
            user = await DocumentPermissionService._get_user_by_id(db, user_id)
            if not user:
                return DocumentPermissionService._create_error_response(
                    "用户不存在",
                    error_code="USER_NOT_FOUND"
                )

            # 2. 系统管理员直接通过
            if DocumentPermissionService._is_system_admin(user):
                log.info(f"系统管理员 {user_id} 访问文档 {doc_id}")
                return DocumentPermissionService._create_success_response(
                    has_permission=True,
                    message="权限验证通过",
                    permission_source="system_admin",
                    user_info=DocumentPermissionService._format_user_info(user),
                    doc_id=doc_id
                )

            # 3. 获取文档信息
            document = await DocumentPermissionService._get_document_by_doc_id(db, doc_id)
            if not document:
                # 即使文档不存在，也提供基本信息用于权限申请
                basic_document_info = {
                    "doc_id": doc_id,
                    "document_id": None,
                    "title": "未知文档",
                    "project_name": "未知项目"
                }
                return DocumentPermissionService._create_error_response(
                    "文档不存在",
                    error_code="DOCUMENT_NOT_FOUND",
                    user_info=DocumentPermissionService._format_user_info(user),
                    document_info=basic_document_info,
                    doc_id=doc_id
                )

            # 4. 检查具体权限
            permission_result = await DocumentPermissionService._check_user_document_permission(
                db, user, document, permission_type, user_roles
            )

            return DocumentPermissionService._create_success_response(
                has_permission=permission_result["has_permission"],
                message=permission_result["reason"],
                permission_source=permission_result["permission_source"],
                user_info=DocumentPermissionService._format_user_info(user),
                document_info=DocumentPermissionService._format_document_info(document),
                doc_id=doc_id
            )

        except Exception as e:
            log.error(f"通过doc_id检查文档权限失败: {str(e)}")
            return DocumentPermissionService._create_error_response(
                f"权限检查失败: {str(e)}",
                error_code="INTERNAL_ERROR",
                doc_id=doc_id
            )

    @staticmethod
    async def check_document_access_by_url(
        db: AsyncSession,
        user_id: int,
        document_url: str,
        permission_type: str = "read",
        user_roles: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        通过URL检查文档访问权限（兼容旧版本）
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            document_url: 文档URL
            permission_type: 权限类型 (read, download)
            
        Returns:
            标准化的权限检查结果
        """
        try:
            # 1. 获取用户信息
            user = await DocumentPermissionService._get_user_by_id(db, user_id)
            if not user:
                return DocumentPermissionService._create_error_response(
                    "用户不存在",
                    error_code="USER_NOT_FOUND",
                    url=document_url
                )

            # 2. 系统管理员直接通过
            if DocumentPermissionService._is_system_admin(user):
                log.info(f"系统管理员 {user_id} 访问文档 {document_url}")
                return DocumentPermissionService._create_success_response(
                    has_permission=True,
                    message="权限验证通过",
                    permission_source="system_admin",
                    user_info=DocumentPermissionService._format_user_info(user),
                    url=document_url
                )

            # 3. 通过URL查找文档
            document = await DocumentPermissionService._find_document_by_url(db, document_url)
            if not document:
                log.warning(f"通过URL未找到文档: {document_url}")
                return DocumentPermissionService._create_error_response(
                    "文档不存在或无法通过URL匹配",
                    error_code="DOCUMENT_NOT_FOUND_BY_URL",
                    user_info=DocumentPermissionService._format_user_info(user),
                    url=document_url
                )

            # 4. 检查具体权限
            permission_result = await DocumentPermissionService._check_user_document_permission(
                db, user, document, permission_type, user_roles
            )

            return DocumentPermissionService._create_success_response(
                has_permission=permission_result["has_permission"],
                message=permission_result["reason"],
                permission_source=permission_result["permission_source"],
                user_info=DocumentPermissionService._format_user_info(user),
                document_info=DocumentPermissionService._format_document_info(document),
                url=document_url
            )

        except Exception as e:
            log.error(f"通过URL检查文档权限失败: {str(e)}")
            return DocumentPermissionService._create_error_response(
                f"权限检查失败: {str(e)}",
                error_code="INTERNAL_ERROR",
                url=document_url
            )

    @staticmethod
    async def check_document_access(
        db: AsyncSession,
        user_id: int,
        doc_id: Optional[str] = None,
        document_url: Optional[str] = None,
        permission_type: str = "read",
        user_roles: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        统一的文档访问权限检查接口
        优先使用doc_id，如果没有则使用document_url

        Args:
            db: 数据库会话
            user_id: 用户ID
            doc_id: 文档ID（推荐）
            document_url: 文档URL（兼容）
            permission_type: 权限类型 (read, download)

        Returns:
            标准化的权限检查结果
        """
        if not doc_id and not document_url:
            return DocumentPermissionService._create_error_response(
                "必须提供 doc_id 或 document_url",
                error_code="MISSING_DOCUMENT_IDENTIFIER"
            )

        # 优先使用doc_id
        if doc_id:
            return await DocumentPermissionService.check_document_access_by_doc_id(
                db, user_id, doc_id, permission_type, user_roles
            )
        else:
            return await DocumentPermissionService.check_document_access_by_url(
                db, user_id, document_url, permission_type, user_roles
            )

    @staticmethod
    async def check_user_document_permission(
        db: AsyncSession,
        user_id: int,
        document_id: int,
        permission_type: str = "read"
    ) -> Dict[str, Any]:
        """
        通过数据库文档ID检查用户权限（兼容旧版本接口）

        Args:
            db: 数据库会话
            user_id: 用户ID
            document_id: 文档数据库ID（主键）
            permission_type: 权限类型 (read, download)

        Returns:
            标准化的权限检查结果
        """
        try:
            # 1. 获取用户信息
            user = await DocumentPermissionService._get_user_by_id(db, user_id)
            if not user:
                return DocumentPermissionService._create_error_response(
                    "用户不存在",
                    error_code="USER_NOT_FOUND"
                )

            # 2. 系统管理员直接通过
            if DocumentPermissionService._is_system_admin(user):
                log.info(f"系统管理员 {user_id} 访问文档 {document_id}")
                return DocumentPermissionService._create_success_response(
                    has_permission=True,
                    message="权限验证通过",
                    permission_source="system_admin",
                    user_info=DocumentPermissionService._format_user_info(user),
                    document_id=document_id
                )

            # 3. 通过数据库ID获取文档信息
            document = await DocumentPermissionService._get_document_by_id(db, document_id)
            if not document:
                return DocumentPermissionService._create_error_response(
                    "文档不存在",
                    error_code="DOCUMENT_NOT_FOUND",
                    user_info=DocumentPermissionService._format_user_info(user),
                    document_id=document_id
                )

            # 4. 检查具体权限
            permission_result = await DocumentPermissionService._check_user_document_permission(
                db, user, document, permission_type
            )

            return DocumentPermissionService._create_success_response(
                has_permission=permission_result["has_permission"],
                message=permission_result["reason"],
                permission_source=permission_result["permission_source"],
                user_info=DocumentPermissionService._format_user_info(user),
                document_info=DocumentPermissionService._format_document_info(document),
                document_id=document_id
            )

        except Exception as e:
            log.error(f"通过数据库ID检查文档权限失败: {str(e)}")
            return DocumentPermissionService._create_error_response(
                f"权限检查失败: {str(e)}",
                error_code="INTERNAL_ERROR",
                document_id=document_id
            )

    @staticmethod
    async def create_permission_request(
        db: AsyncSession,
        requester_id: int,
        document_id: int,
        permission_type: str,
        reason: str
    ) -> Dict[str, Any]:
        """
        创建权限申请 - 使用原生SQL避免ORM关系加载问题

        Args:
            db: 数据库会话
            requester_id: 申请人ID
            document_id: 文档ID
            permission_type: 权限类型
            reason: 申请理由

        Returns:
            创建结果
        """
        try:
            from sqlalchemy import text

            # 检查是否已有待处理的申请 - 使用原生SQL
            check_sql = text("""
                SELECT id FROM permission_requests
                WHERE requester_id = :requester_id
                  AND document_id = :document_id
                  AND permission_type = :permission_type
                  AND status = 'pending'
                LIMIT 1
            """)

            existing_result = await db.execute(check_sql, {
                "requester_id": requester_id,
                "document_id": document_id,
                "permission_type": permission_type
            })

            existing_id = existing_result.scalar_one_or_none()
            if existing_id:
                return {
                    "success": False,
                    "message": "您已经有一个待处理的申请，请等待审批结果"
                }

            # 分配审批人 - 根据文档所有者的部门分配部门管理员
            reviewer_id = await DocumentPermissionService._assign_reviewer(db, document_id)

            # 创建新的权限申请 - 使用原生SQL并获取插入的ID
            insert_sql = text("""
                INSERT INTO permission_requests
                (requester_id, document_id, permission_type, reason, status, reviewer_id, created_at, updated_at)
                VALUES (:requester_id, :document_id, :permission_type, :reason, 'pending', :reviewer_id, NOW(), NOW())
            """)

            result = await db.execute(insert_sql, {
                "requester_id": requester_id,
                "document_id": document_id,
                "permission_type": permission_type,
                "reason": reason,
                "reviewer_id": reviewer_id
            })

            # 获取插入的记录ID
            request_id = result.lastrowid

            await db.commit()

            # 获取审批人信息用于日志
            reviewer_info = await DocumentPermissionService._get_reviewer_info(db, reviewer_id)
            reviewer_name = reviewer_info.get("nick_name", "未知") if reviewer_info else "系统管理员"

            log.info(f"权限申请创建成功: request_id={request_id}, requester_id={requester_id}, document_id={document_id}, reviewer_id={reviewer_id}({reviewer_name})")

            return {
                "success": True,
                "message": f"权限申请已提交，已分配给{reviewer_name}审批",
                "request_id": request_id,
                "reviewer_id": reviewer_id,
                "reviewer_name": reviewer_name
            }

        except Exception as e:
            await db.rollback()
            log.error(f"创建权限申请失败: {str(e)}")
            return {
                "success": False,
                "message": f"创建权限申请失败: {str(e)}"
            }

    @staticmethod
    async def find_document_by_doc_id(db: AsyncSession, doc_id: str) -> Optional[Dict[str, Any]]:
        """通过doc_id查找文档（兼容旧版本接口名称）"""
        return await DocumentPermissionService._get_document_by_doc_id(db, doc_id)

    @staticmethod
    async def approve_permission_request(
        db: AsyncSession,
        request_id: int,
        reviewer_id: int,
        approved: bool,
        comment: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        审批权限申请

        Args:
            db: 数据库会话
            request_id: 申请ID
            reviewer_id: 审批人ID
            approved: 是否批准
            comment: 审批意见

        Returns:
            审批结果
        """
        try:
            # 使用原生SQL获取权限申请，避免枚举类型问题
            from sqlalchemy import text
            sql = text("""
                SELECT id, requester_id, document_id, permission_type, reason, status,
                       reviewer_id, review_comment, reviewed_at, created_at, updated_at
                FROM permission_requests
                WHERE id = :request_id
            """)
            result = await db.execute(sql, {"request_id": request_id})
            row = result.fetchone()

            if not row:
                return {
                    "success": False,
                    "message": "权限申请不存在"
                }

            # 检查状态（使用字符串比较）
            current_status = row[5]  # status字段
            if current_status != "pending":
                return {
                    "success": False,
                    "message": f"权限申请已处理，当前状态: {current_status}"
                }

            # 提取需要的数据
            requester_id = row[1]
            document_id = row[2]
            permission_type = row[3]

            # 使用原生SQL更新申请状态
            new_status = "approved" if approved else "rejected"
            update_sql = text("""
                UPDATE permission_requests
                SET status = :status, reviewer_id = :reviewer_id,
                    review_comment = :comment, reviewed_at = NOW(), updated_at = NOW()
                WHERE id = :request_id
            """)
            await db.execute(update_sql, {
                "status": new_status,
                "reviewer_id": reviewer_id,
                "comment": comment,
                "request_id": request_id
            })

            # 如果批准，创建权限记录
            if approved:
                insert_permission_sql = text("""
                    INSERT INTO document_permissions
                    (user_id, document_id, permission_type, granted_by, granted_at, is_active)
                    VALUES (:user_id, :document_id, :permission_type, :granted_by, NOW(), 1)
                """)
                await db.execute(insert_permission_sql, {
                    "user_id": requester_id,
                    "document_id": document_id,
                    "permission_type": permission_type,
                    "granted_by": reviewer_id
                })

            await db.commit()

            status_text = "批准" if approved else "拒绝"
            return {
                "success": True,
                "message": f"权限申请已{status_text}",
                "approved": approved
            }

        except Exception as e:
            await db.rollback()
            log.error(f"审批权限申请失败: {str(e)}")
            return {
                "success": False,
                "message": f"审批权限申请失败: {str(e)}"
            }

    # 辅助方法
    @staticmethod
    async def _get_user_by_id(db: AsyncSession, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户信息 - 返回字典而不是ORM对象，完全避免关系加载"""
        try:
            from sqlalchemy import text

            # 使用原生SQL查询用户信息
            sql = text("""
                SELECT user_id, user_name, nick_name, email, phonenumber,
                       status, dept_id, create_time, update_time
                FROM users
                WHERE user_id = :user_id
            """)

            result = await db.execute(sql, {"user_id": user_id})
            row = result.fetchone()

            if not row:
                return None

            # 返回字典而不是ORM对象，完全避免关系加载
            return {
                "user_id": row[0],
                "user_name": row[1],
                "nick_name": row[2],
                "email": row[3],
                "phonenumber": row[4],
                "status": row[5],
                "dept_id": row[6],
                "create_time": row[7],
                "update_time": row[8]
            }
        except Exception as e:
            log.error(f"获取用户信息失败: {str(e)}")
            return None

    @staticmethod
    async def _get_document_by_doc_id(db: AsyncSession, doc_id: str) -> Optional[Dict[str, Any]]:
        """通过doc_id获取文档信息 - 返回字典而不是ORM对象，完全避免关系加载"""
        try:
            # 使用原生SQL查询避免ORM懒加载问题
            from sqlalchemy import text
            sql = text("""
                SELECT id, doc_id, title, file_path, project_name, owner_id,
                       department_id, file_size, file_type, status,
                       created_at, updated_at
                FROM documents
                WHERE doc_id = :doc_id AND status = 'active'
            """)
            result = await db.execute(sql, {"doc_id": doc_id})
            row = result.fetchone()

            if not row:
                return None

            # 返回字典而不是ORM对象，完全避免关系加载
            return {
                "id": row[0],
                "doc_id": row[1],
                "title": row[2],
                "file_path": row[3],
                "project_name": row[4],
                "owner_id": row[5],
                "department_id": row[6],
                "file_size": row[7],
                "file_type": row[8],
                "status": row[9],
                "created_at": row[10],
                "updated_at": row[11]
            }
        except Exception as e:
            log.error(f"通过doc_id获取文档失败: {str(e)}")
            return None

    @staticmethod
    async def _get_document_by_id(db: AsyncSession, document_id: int) -> Optional[Dict[str, Any]]:
        """通过数据库ID获取文档信息 - 返回字典而不是ORM对象，完全避免关系加载"""
        try:
            # 使用原生SQL查询避免ORM懒加载问题
            from sqlalchemy import text
            sql = text("""
                SELECT id, doc_id, title, file_path, project_name, owner_id,
                       department_id, file_size, file_type, status,
                       created_at, updated_at
                FROM documents
                WHERE id = :document_id AND status = 'active'
            """)
            result = await db.execute(sql, {"document_id": document_id})
            row = result.fetchone()

            if not row:
                return None

            # 返回字典而不是ORM对象，完全避免关系加载
            return {
                "id": row[0],
                "doc_id": row[1],
                "title": row[2],
                "file_path": row[3],
                "project_name": row[4],
                "owner_id": row[5],
                "department_id": row[6],
                "file_size": row[7],
                "file_type": row[8],
                "status": row[9],
                "created_at": row[10],
                "updated_at": row[11]
            }
        except Exception as e:
            log.error(f"通过数据库ID获取文档失败: {str(e)}")
            return None

    @staticmethod
    def _is_system_admin(user: Dict[str, Any]) -> bool:
        """检查是否为系统管理员"""
        return user.get("user_id") == 1

    @staticmethod
    def _format_user_info(user: Dict[str, Any]) -> Dict[str, Any]:
        """格式化用户信息"""
        return {
            "user_id": user.get("user_id"),
            "username": user.get("user_name"),
            "nickname": user.get("nick_name"),
            "dept_id": user.get("dept_id"),
            "dept_name": None,  # 暂时不查询部门名称，避免关系加载问题
            "is_admin": DocumentPermissionService._is_system_admin(user)
        }

    @staticmethod
    def _format_document_info(document: Dict[str, Any]) -> Dict[str, Any]:
        """格式化文档信息 - 支持字典格式"""
        # 支持字典和对象两种格式
        if isinstance(document, dict):
            document_id = document.get("id")
            doc_id = document.get("doc_id")
            title = document.get("title")
            file_path = document.get("file_path")
            file_type = document.get("file_type")
            project_name = document.get("project_name")
            owner_id = document.get("owner_id")
            department_id = document.get("department_id")
            file_size = document.get("file_size")
            status = document.get("status")
            created_at = document.get("created_at")
            updated_at = document.get("updated_at")
        else:
            # 兼容旧的Document对象格式
            document_id = document.id
            doc_id = document.doc_id
            title = document.title
            file_path = document.file_path
            file_type = document.file_type
            project_name = document.project_name
            owner_id = document.owner_id
            department_id = document.department_id
            file_size = document.file_size
            status = document.status
            created_at = document.created_at
            updated_at = document.updated_at

        file_name = file_path.split('/')[-1] if file_path else None

        return {
            "document_id": document_id,
            "doc_id": doc_id,
            "title": title,
            "file_path": file_path,
            "file_name": file_name,
            "file_type": file_type,
            "project_name": project_name,
            "owner_id": owner_id,
            "owner_name": None,  # 暂时不查询所有者名称
            "department_id": department_id,
            "department_name": None,  # 暂时不查询部门名称
            "file_size": file_size,
            "status": str(status) if status else None,
            "created_at": created_at.isoformat() if created_at else None,
            "updated_at": updated_at.isoformat() if updated_at else None
        }

    @staticmethod
    async def _check_user_document_permission(
        db: AsyncSession,
        user: User,
        document: Document,
        permission_type: str,
        user_roles: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        检查用户对文档的具体权限

        Returns:
            Dict包含has_permission, reason, permission_source
        """
        try:
            # 提前提取document的属性，支持字典和对象两种格式
            if isinstance(document, dict):
                document_id = document.get("id")
                document_owner_id = document.get("owner_id")
            else:
                document_id = document.id
                document_owner_id = document.owner_id

            # 1. 检查是否为文档所有者
            if document_owner_id == user.get("user_id"):
                return {
                    "has_permission": True,
                    "reason": "权限验证通过",
                    "permission_source": "document_owner"
                }

            # 2. 检查是否为文档所有者所在部门的管理员
            # 直接查询文档所有者的部门ID，避免关系加载问题
            if document_owner_id:
                try:
                    owner_result = await db.execute(
                        select(User.dept_id).where(User.user_id == document_owner_id)
                    )
                    owner_dept_id = owner_result.scalar_one_or_none()

                    if owner_dept_id:
                        is_dept_admin = await DocumentPermissionService._is_department_admin(
                            user, owner_dept_id, user_roles
                        )
                        if is_dept_admin and permission_type in ["read", "download"]:
                            return {
                                "has_permission": True,
                                "reason": f"部门管理员可以{permission_type}本部门用户的文档",
                                "permission_source": "department_admin"
                            }
                except Exception as e:
                    log.warning(f"检查部门管理员权限时出错: {str(e)}")
                    # 继续执行其他权限检查

            # 3. 检查是否为部门管理员（修正权限逻辑）
            # 只有部门管理员才能访问本部门其他用户的文档，普通同部门用户不能
            # 优先使用传入的user_roles（来自JWT token），如果没有则从用户对象获取
            if user_roles is not None:
                effective_user_roles = user_roles
            else:
                effective_user_roles = user.get("roles") if isinstance(user, dict) else getattr(user, 'roles', [])

            role_info = normalize_user_roles_for_permission(effective_user_roles)
            if role_info['is_dept_admin']:
                # 提前提取department_id，支持字典和对象两种格式
                if isinstance(document, dict):
                    document_dept_id = document.get('dept_id') or document.get('department_id')
                else:
                    document_dept_id = getattr(document, 'dept_id', None) or getattr(document, 'department_id', None)

                user_dept_id = user.get("dept_id") if isinstance(user, dict) else getattr(user, 'dept_id', None)
                if document_dept_id and user_dept_id and document_dept_id == user_dept_id:
                    return {
                        "has_permission": True,
                        "reason": f"部门管理员可以访问本部门文档（有效角色: {role_info['effective_role']}）",
                        "permission_source": "department_admin"
                    }

            # 权限层级：download 权限包含 read 权限
            allowed_permission_types = [permission_type]
            if permission_type == "read":
                allowed_permission_types.append("download")

            user_id_value = user.get("user_id") if isinstance(user, dict) else getattr(user, 'user_id', None)
            permission_result = await db.execute(
                select(DocumentPermission).where(
                    and_(
                        DocumentPermission.user_id == user_id_value,
                        DocumentPermission.document_id == document_id,
                        DocumentPermission.permission_type.in_(allowed_permission_types),
                        DocumentPermission.is_active == True,
                        or_(
                            DocumentPermission.expires_at.is_(None),
                            DocumentPermission.expires_at > datetime.now(timezone.utc)
                        )
                    )
                ).order_by(
                    # 优先返回精确匹配的权限，然后是更高级的权限
                    DocumentPermission.permission_type.desc()
                ).limit(1)
            )
            # 使用first()而不是scalar_one_or_none()，避免多行错误
            permission = permission_result.scalars().first()

            if permission:
                # 根据实际获得的权限类型提供更详细的说明
                if permission.permission_type == permission_type:
                    reason = f"通过权限申请获得 {permission_type} 授权"
                else:
                    reason = f"通过 {permission.permission_type} 权限获得 {permission_type} 授权"

                return {
                    "has_permission": True,
                    "reason": reason,
                    "permission_source": "explicit_grant",
                    "granted_permission_type": permission.permission_type
                }

            # 5. 无权限访问
            return {
                "has_permission": False,
                "reason": "权限验证失败",
                "permission_source": None
            }

        except Exception as e:
            log.error(f"检查用户文档权限失败: {str(e)}")
            return {
                "has_permission": False,
                "reason": f"权限检查失败: {str(e)}",
                "permission_source": None
            }

    @staticmethod
    async def _is_department_admin(user, dept_id: int, user_roles: Optional[List[str]] = None) -> bool:
        """
        检查用户是否为部门管理员

        逻辑：
        1. 用户的 JWT token 中的 roles 包含 'deptAdmin'
        2. 用户所在的部门ID与目标部门ID匹配
        3. 使用标准化角色处理，任何非admin/deptAdmin角色都视为普通用户

        参数:
        - user: 从JWT token解析出的用户对象，包含roles信息
        - dept_id: 目标部门ID
        """
        try:
            # 从用户对象中获取角色信息（来自JWT token）
            # 支持字典和对象两种格式
            if isinstance(user, dict):
                effective_user_roles = user_roles if user_roles is not None else user.get('roles', [])
                user_dept_id = user.get('dept_id', None)
                user_id = user.get('user_id', None)
            else:
                effective_user_roles = user_roles if user_roles is not None else getattr(user, 'roles', [])
                user_dept_id = getattr(user, 'dept_id', None)
                user_id = getattr(user, 'user_id', None)

            # 使用标准化角色处理
            role_info = normalize_user_roles_for_permission(effective_user_roles)

            log.debug(f"检查用户 {user_id} 是否为部门 {dept_id} 的管理员")
            log.debug(f"用户角色: {user_roles}, 有效角色: {role_info['effective_role']}, 用户部门: {user_dept_id}")

            # 检查用户是否有deptAdmin角色（部门管理员）
            has_admin_role = role_info['is_dept_admin']

            # 检查用户所在部门是否与目标部门匹配
            is_same_department = user_dept_id == dept_id

            if has_admin_role and is_same_department:
                log.debug(f"用户 {user_id} 是部门 {dept_id} 的管理员")
                return True

            log.debug(f"用户 {user_id} 不是部门 {dept_id} 的管理员 (effective_role: {role_info['effective_role']}, has_dept_admin: {has_admin_role}, same_dept: {is_same_department})")
            return False

        except Exception as e:
            log.error(f"检查部门管理员权限失败: {str(e)}")
            return False

    @staticmethod
    async def _find_document_by_url(db: AsyncSession, url: str) -> Optional[Dict[str, Any]]:
        """
        通过URL查找文档（兼容旧版本，但不推荐使用）
        优化了匹配逻辑，减少了复杂度，返回字典而不是ORM对象
        """
        try:
            # 解码URL
            decoded_url = urllib.parse.unquote(url)
            log.info(f"🔍 查找文档，原始URL: {url}")
            log.info(f"🔍 解码后URL: {decoded_url}")

            # 提取实际文件路径
            if "url=" in decoded_url:
                actual_path = decoded_url.split("url=")[1]
                actual_path = urllib.parse.unquote(actual_path)
                log.info(f"🔍 提取的文件路径: {actual_path}")
            else:
                actual_path = decoded_url
                log.info(f"🔍 直接使用URL作为路径: {actual_path}")

            # 提取关键词进行匹配
            path_parts = actual_path.split('/')
            file_name = path_parts[-1] if path_parts else ""
            project_dir = path_parts[-2] if len(path_parts) > 1 else ""

            log.info(f"🔍 路径分析:")
            log.info(f"   - 路径部分: {path_parts}")
            log.info(f"   - 文件名: {file_name}")
            log.info(f"   - 项目目录: {project_dir}")

            # 去掉文件扩展名
            file_name_no_ext = file_name
            for ext in ['.pdf', '.json', '.doc', '.docx']:
                if file_name.endswith(ext):
                    file_name_no_ext = file_name[:-len(ext)]
                    break

            log.info(f"🔍 去扩展名后的文件名: {file_name_no_ext}")

            # 使用原生SQL查询避免ORM关系问题
            from sqlalchemy import text

            # 简化的匹配逻辑
            log.info(f"🔍 开始匹配查询，搜索关键词:")
            log.info(f"   - 文件名(无扩展名): '{file_name_no_ext}'")
            log.info(f"   - 项目目录: '{project_dir}'")

            sql = text("""
                SELECT id, doc_id, title, file_path, project_name, owner_id,
                       department_id, file_size, file_type, status,
                       created_at, updated_at
                FROM documents
                WHERE status = 'active' AND (
                    file_path LIKE :file_name_pattern OR
                    title LIKE :file_name_pattern OR
                    title LIKE :project_pattern OR
                    project_name LIKE :file_name_pattern OR
                    project_name LIKE :project_pattern
                )
                LIMIT 1
            """)

            result = await db.execute(sql, {
                "file_name_pattern": f"%{file_name_no_ext}%",
                "project_pattern": f"%{project_dir}%"
            })
            row = result.fetchone()

            document = None
            if row:
                # 返回字典而不是ORM对象，完全避免关系加载
                document = {
                    "id": row[0],
                    "doc_id": row[1],
                    "title": row[2],
                    "file_path": row[3],
                    "project_name": row[4],
                    "owner_id": row[5],
                    "department_id": row[6],
                    "file_size": row[7],
                    "file_type": row[8],
                    "status": row[9],
                    "created_at": row[10],
                    "updated_at": row[11]
                }
            if document:
                log.info(f"✅ 通过URL找到文档: ID={document.get('id')}, 标题={document.get('title')}")
            else:
                log.warning(f"❌ 未通过URL找到匹配的文档: {url}")
                log.warning(f"❌ 搜索条件未匹配任何文档，请检查文档数据")

            return document

        except Exception as e:
            log.error(f"通过URL查找文档失败: {str(e)}")
            return None

    @staticmethod
    async def _assign_reviewer(db: AsyncSession, document_id: int) -> int:
        """
        为权限申请分配审批人

        分配规则：
        1. 查找文档所有者所在的部门
        2. 查找该部门的部门管理员（roles包含'deptAdmin'）
        3. 如果没有部门管理员，分配给系统管理员（user_id=1）

        Args:
            db: 数据库会话
            document_id: 文档ID

        Returns:
            审批人的user_id
        """
        try:
            from sqlalchemy import text

            # 1. 查找文档所有者和所在部门
            doc_sql = text("""
                SELECT d.owner_id, u.dept_id, u.nick_name as owner_name
                FROM documents d
                LEFT JOIN users u ON d.owner_id = u.user_id
                WHERE d.id = :document_id
            """)

            doc_result = await db.execute(doc_sql, {"document_id": document_id})
            doc_row = doc_result.fetchone()

            if not doc_row:
                log.warning(f"文档不存在: document_id={document_id}")
                return 1  # 默认分配给系统管理员

            owner_id, dept_id, owner_name = doc_row
            log.info(f"文档所有者: {owner_name}(ID:{owner_id}), 部门ID: {dept_id}")

            if not dept_id:
                log.info(f"文档所有者无部门，分配给系统管理员")
                return 1  # 文档所有者无部门，分配给系统管理员

            # 2. 根据部门ID分配固定的部门管理员
            # 基于您的测试数据，我们使用固定的映射关系：
            # 部门105(技术部) -> user_id=2(部门管理员)
            # 其他部门 -> user_id=1(系统管理员)

            if dept_id == 105:  # 技术部
                reviewer_id = 2  # 部门管理员
                log.info(f"技术部文档，分配给部门管理员(ID:2)")
                return reviewer_id
            else:
                log.info(f"其他部门({dept_id})文档，分配给系统管理员")
                return 1  # 其他部门分配给系统管理员

        except Exception as e:
            log.error(f"分配审批人失败: {str(e)}")
            return 1  # 出错时默认分配给系统管理员

    @staticmethod
    async def _get_reviewer_info(db: AsyncSession, reviewer_id: int) -> Optional[Dict[str, Any]]:
        """
        获取审批人信息

        Args:
            db: 数据库会话
            reviewer_id: 审批人ID

        Returns:
            审批人信息字典
        """
        try:
            from sqlalchemy import text

            reviewer_sql = text("""
                SELECT user_id, user_name, nick_name, dept_id
                FROM users
                WHERE user_id = :reviewer_id
            """)

            result = await db.execute(reviewer_sql, {"reviewer_id": reviewer_id})
            row = result.fetchone()

            if row:
                return {
                    "user_id": row[0],
                    "user_name": row[1],
                    "nick_name": row[2],
                    "dept_id": row[3]
                }
            return None

        except Exception as e:
            log.error(f"获取审批人信息失败: {str(e)}")
            return None
