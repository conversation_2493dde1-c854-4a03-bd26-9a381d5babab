"""
用户服务 - 优化数据库查询，避免greenlet问题
"""
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text

from auth.models import User
import logging

log = logging.getLogger(__name__)


class UserService:
    """用户服务类 - 提供优化的用户查询方法"""
    
    @staticmethod
    async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional[User]:
        """
        通过用户ID获取用户信息（基础查询，避免关系加载）
        """
        try:
            result = await db.execute(
                select(User).where(User.user_id == user_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            log.error(f"通过user_id获取用户失败: {str(e)}")
            return None
    
    @staticmethod
    async def get_user_by_username(db: AsyncSession, username: str) -> Optional[User]:
        """
        通过用户名获取用户信息（基础查询，避免关系加载）
        """
        try:
            result = await db.execute(
                select(User).where(User.user_name == username)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            log.error(f"通过用户名获取用户失败: {str(e)}")
            return None
    
    @staticmethod
    async def get_user_roles(db: AsyncSession, user_id: int) -> List[str]:
        """
        获取用户角色列表（简化版本，基于业务逻辑，不依赖数据库表）
        """
        # 系统管理员
        if user_id == 1:
            return ['admin']

        try:
            # 查询用户信息以确定角色
            user = await UserService.get_user_by_id(db, user_id)
            if not user:
                return ['user']  # 默认角色

            # 这里可以根据业务逻辑确定部门管理员
            # 例如：检查用户是否为某个部门的负责人
            # 暂时简化为：如果有部门ID且不是普通用户，可能是部门管理员
            # 实际业务中可以根据具体需求调整这个逻辑

            return ['user']  # 默认为普通用户

        except Exception as e:
            log.error(f"获取用户角色失败: {str(e)}")
            # 出错时的默认角色逻辑
            if user_id == 1:
                return ['admin']
            return ['user']
    
    @staticmethod
    async def get_user_basic_info(db: AsyncSession, user_id: int) -> Optional[Dict[str, Any]]:
        """
        使用原生SQL获取用户基础信息，避免ORM关系加载问题
        """
        try:
            sql = text("""
                SELECT user_id, user_name, nick_name, email, phonenumber,
                       dept_id, status
                FROM users
                WHERE user_id = :user_id
            """)
            result = await db.execute(sql, {"user_id": user_id})
            row = result.fetchone()
            
            if row:
                return {
                    "user_id": row[0],
                    "user_name": row[1],
                    "nick_name": row[2],
                    "email": row[3],
                    "phonenumber": row[4],
                    "dept_id": row[5],
                    "status": row[6]
                }
            return None
        except Exception as e:
            log.error(f"获取用户基础信息失败: {str(e)}")
            return None
    
    @staticmethod
    async def check_user_exists(db: AsyncSession, user_id: int) -> bool:
        """
        检查用户是否存在（轻量级查询）
        """
        try:
            sql = text("SELECT 1 FROM users WHERE user_id = :user_id LIMIT 1")
            result = await db.execute(sql, {"user_id": user_id})
            return result.fetchone() is not None
        except Exception as e:
            log.error(f"检查用户存在性失败: {str(e)}")
            return False
    
    # 移除get_user_permissions方法 - 使用简化的角色系统
    
    # 移除旧的get_user_roles方法 - 已在上面重新实现为简化版本
    
    @staticmethod
    async def update_user_login_info(db: AsyncSession, user_id: int, login_ip: str = None) -> bool:
        """
        更新用户登录信息（使用原生SQL避免对象加载）
        """
        try:
            from datetime import datetime
            current_time = datetime.now()

            sql = text("""
                UPDATE users
                SET login_date = :login_date, login_ip = :login_ip
                WHERE user_id = :user_id
            """)
            await db.execute(sql, {
                "user_id": user_id,
                "login_ip": login_ip,
                "login_date": current_time
            })
            await db.commit()
            return True
        except Exception as e:
            log.error(f"更新用户登录信息失败: {str(e)}")
            await db.rollback()
            return False


class UserCache:
    """用户信息缓存类 - 减少重复数据库查询"""
    
    def __init__(self):
        self._cache: Dict[int, Dict[str, Any]] = {}
        self._max_size = 1000  # 最大缓存数量
    
    def get(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取缓存的用户信息"""
        return self._cache.get(user_id)
    
    def set(self, user_id: int, user_info: Dict[str, Any]):
        """设置用户信息缓存"""
        if len(self._cache) >= self._max_size:
            # 简单的LRU：删除第一个元素
            first_key = next(iter(self._cache))
            del self._cache[first_key]
        
        self._cache[user_id] = user_info
    
    def remove(self, user_id: int):
        """移除用户缓存"""
        self._cache.pop(user_id, None)
    
    def clear(self):
        """清空缓存"""
        self._cache.clear()


# 全局用户缓存实例
user_cache = UserCache()
