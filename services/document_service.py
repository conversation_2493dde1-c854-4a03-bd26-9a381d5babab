"""
文档管理服务
处理文档的创建、更新、查询等操作
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from auth.models import Document, DocumentStatus
from utils.log import log
import hashlib
import os
import re


class DocumentService:
    """文档管理服务类"""

    @staticmethod
    def generate_doc_id(file_path: str, content: bytes = None, strategy: str = "path_based") -> str:
        """
        生成文档ID

        Args:
            file_path: 文件路径
            content: 文件内容（可选）
            strategy: 生成策略
                - "path_based": 基于标准化文件路径（推荐，稳定）
                - "content_based": 基于文件内容（不推荐，OCR结果不稳定）
                - "hybrid": 混合策略，优先路径，内容作为辅助

        Returns:
            str: 32位MD5哈希值作为doc_id
        """
        try:
            if strategy == "path_based":
                # 策略1：基于标准化文件路径（推荐）
                # 标准化路径：去除多余空格、统一分隔符、转小写
                normalized_path = file_path.strip().replace('\\', '/').lower()
                # 移除可能的时间戳或版本号后缀
                normalized_path = re.sub(r'_\d{10,}', '', normalized_path)  # 移除时间戳
                normalized_path = re.sub(r'_v\d+', '', normalized_path)     # 移除版本号
                return hashlib.md5(normalized_path.encode('utf-8')).hexdigest()

            elif strategy == "content_based":
                # 策略2：基于文件内容（不推荐用于OCR结果）
                if content:
                    return hashlib.md5(content).hexdigest()
                else:
                    # 回退到路径策略
                    return DocumentService.generate_doc_id(file_path, content, "path_based")

            elif strategy == "hybrid":
                # 策略3：混合策略
                # 使用文件路径 + 文件大小（如果有内容的话）
                path_part = file_path.strip().replace('\\', '/').lower()
                if content:
                    size_part = str(len(content))
                    combined = f"{path_part}|{size_part}"
                else:
                    combined = path_part
                return hashlib.md5(combined.encode('utf-8')).hexdigest()

            else:
                raise ValueError(f"未知的生成策略: {strategy}")

        except Exception as e:
            log.error(f"生成doc_id失败: {e}")
            # 使用最简单的后备方案：文件路径的MD5
            fallback = file_path.strip().replace('\\', '/').lower()
            return hashlib.md5(fallback.encode('utf-8')).hexdigest()

    @staticmethod
    async def create_document(
        db: AsyncSession,
        doc_id: str,
        title: str,
        file_path: str,
        owner_id: int,
        department_id: int = None,
        project_name: str = None,
        file_size: int = None,
        file_content: bytes = None
    ) -> Document:
        """创建文档记录"""
        try:
            # 如果没有提供doc_id，则生成一个
            if not doc_id:
                doc_id = DocumentService.generate_doc_id(file_path, file_content)
            
            # 检查doc_id是否已存在
            existing_doc = await DocumentService.get_document_by_doc_id(db, doc_id)
            if existing_doc:
                log.warning(f"文档doc_id已存在: {doc_id}")
                return existing_doc
            
            # 从文件路径提取文件类型
            file_type = os.path.splitext(file_path)[1].lstrip('.') if file_path else None
            
            # 创建文档记录
            document = Document(
                doc_id=doc_id,
                title=title,
                file_path=file_path,
                project_name=project_name,
                owner_id=owner_id,
                department_id=department_id,
                file_size=file_size,
                file_type=file_type,
                status=DocumentStatus.active
            )
            
            db.add(document)
            await db.commit()
            await db.refresh(document)
            
            log.info(f"文档创建成功: doc_id={doc_id}, title={title}")
            return document
            
        except Exception as e:
            log.error(f"创建文档失败: {e}")
            await db.rollback()
            raise

    @staticmethod
    async def get_document_by_id(db: AsyncSession, document_id: int) -> Optional[Document]:
        """根据ID获取文档"""
        try:
            result = await db.execute(
                select(Document)
                .where(Document.id == document_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            log.error(f"根据ID获取文档失败: {e}")
            return None

    @staticmethod
    async def get_document_by_doc_id(db: AsyncSession, doc_id: str) -> Optional[Document]:
        """根据doc_id获取文档"""
        try:
            result = await db.execute(
                select(Document)
                .where(Document.doc_id == doc_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            log.error(f"根据doc_id获取文档失败: {e}")
            return None

    @staticmethod
    async def get_document_by_path(db: AsyncSession, file_path: str) -> Optional[Document]:
        """根据文件路径获取文档"""
        try:
            result = await db.execute(
                select(Document)
                .where(Document.file_path == file_path)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            log.error(f"根据文件路径获取文档失败: {e}")
            return None

    @staticmethod
    async def search_documents(
        db: AsyncSession,
        title: str = None,
        project_name: str = None,
        owner_id: int = None,
        department_id: int = None,
        file_type: str = None,
        status: DocumentStatus = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Document]:
        """搜索文档"""
        try:
            query = select(Document)

            conditions = []

            if title:
                conditions.append(Document.title.contains(title))

            if project_name:
                conditions.append(Document.project_name.contains(project_name))

            if owner_id:
                conditions.append(Document.owner_id == owner_id)

            if department_id:
                conditions.append(Document.department_id == department_id)

            if file_type:
                conditions.append(Document.file_type == file_type)

            if status:
                conditions.append(Document.status == status)
            else:
                # 默认只查询活跃状态的文档
                conditions.append(Document.status == DocumentStatus.active)

            if conditions:
                query = query.where(and_(*conditions))

            query = query.order_by(Document.created_at.desc()).limit(limit).offset(offset)

            result = await db.execute(query)
            return result.scalars().all()

        except Exception as e:
            log.error(f"搜索文档失败: {e}")
            return []

    @staticmethod
    async def update_document(
        db: AsyncSession,
        document_id: int,
        title: str = None,
        project_name: str = None,
        department_id: int = None,
        status: DocumentStatus = None
    ) -> Optional[Document]:
        """更新文档信息"""
        try:
            document = await DocumentService.get_document_by_id(db, document_id)
            if not document:
                return None
            
            if title is not None:
                document.title = title
            
            if project_name is not None:
                document.project_name = project_name
            
            if department_id is not None:
                document.department_id = department_id
            
            if status is not None:
                document.status = status
            
            await db.commit()
            await db.refresh(document)
            
            log.info(f"文档更新成功: doc_id={document.doc_id}")
            return document
            
        except Exception as e:
            log.error(f"更新文档失败: {e}")
            await db.rollback()
            raise

    @staticmethod
    async def delete_document(db: AsyncSession, document_id: int, soft_delete: bool = True) -> bool:
        """删除文档"""
        try:
            document = await DocumentService.get_document_by_id(db, document_id)
            if not document:
                return False
            
            if soft_delete:
                # 软删除：只更新状态
                document.status = DocumentStatus.deleted
                await db.commit()
                log.info(f"文档软删除成功: doc_id={document.doc_id}")
            else:
                # 硬删除：从数据库中删除
                await db.delete(document)
                await db.commit()
                log.info(f"文档硬删除成功: doc_id={document.doc_id}")
            
            return True
            
        except Exception as e:
            log.error(f"删除文档失败: {e}")
            await db.rollback()
            return False

    @staticmethod
    async def get_user_documents(
        db: AsyncSession,
        user_id: int,
        status: DocumentStatus = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Document]:
        """获取用户的文档列表"""
        try:
            query = select(Document).where(Document.owner_id == user_id)

            if status:
                query = query.where(Document.status == status)
            else:
                query = query.where(Document.status == DocumentStatus.active)

            query = query.order_by(Document.created_at.desc()).limit(limit).offset(offset)

            result = await db.execute(query)
            return result.scalars().all()

        except Exception as e:
            log.error(f"获取用户文档列表失败: {e}")
            return []

    @staticmethod
    async def get_department_documents(
        db: AsyncSession,
        department_id: int,
        status: DocumentStatus = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Document]:
        """获取部门的文档列表"""
        try:
            query = select(Document).where(Document.department_id == department_id)

            if status:
                query = query.where(Document.status == status)
            else:
                query = query.where(Document.status == DocumentStatus.active)

            query = query.order_by(Document.created_at.desc()).limit(limit).offset(offset)

            result = await db.execute(query)
            return result.scalars().all()

        except Exception as e:
            log.error(f"获取部门文档列表失败: {e}")
            return []

    @staticmethod
    async def get_document_with_details(db: AsyncSession, document_id: int) -> Optional[Dict[str, Any]]:
        """获取文档详情，包含用户和部门信息"""
        try:
            from sqlalchemy import text

            # 使用JOIN查询获取文档、用户和部门信息
            sql = text("""
                SELECT
                    d.id, d.doc_id, d.title, d.file_path, d.project_name,
                    d.owner_id, d.department_id, d.file_size, d.file_type,
                    d.status, d.created_at, d.updated_at,
                    u.nick_name as owner_name,
                    dept.dept_name as department_name
                FROM documents d
                LEFT JOIN users u ON d.owner_id = u.user_id
                LEFT JOIN departments dept ON d.department_id = dept.dept_id
                WHERE d.id = :document_id
            """)

            result = await db.execute(sql, {"document_id": document_id})
            row = result.fetchone()

            if not row:
                return None

            file_name = row.file_path.split('/')[-1] if row.file_path else None

            return {
                "id": row.id,
                "doc_id": row.doc_id,
                "title": row.title,
                "file_path": row.file_path,
                "file_name": file_name,
                "file_type": row.file_type,
                "project_name": row.project_name,
                "owner_id": row.owner_id,
                "owner_name": row.owner_name,
                "department_id": row.department_id,
                "department_name": row.department_name,
                "file_size": row.file_size,
                "status": row.status,
                "created_at": row.created_at.isoformat() if row.created_at else None,
                "updated_at": row.updated_at.isoformat() if row.updated_at else None
            }

        except Exception as e:
            log.error(f"获取文档详情失败: {e}")
            return None

    @staticmethod
    async def get_document_by_doc_id_with_details(db: AsyncSession, doc_id: str) -> Optional[Dict[str, Any]]:
        """根据doc_id获取文档详情，包含用户和部门信息"""
        try:
            from sqlalchemy import text

            # 使用JOIN查询获取文档、用户和部门信息
            sql = text("""
                SELECT
                    d.id, d.doc_id, d.title, d.file_path, d.project_name,
                    d.owner_id, d.department_id, d.file_size, d.file_type,
                    d.status, d.created_at, d.updated_at,
                    u.nick_name as owner_name,
                    dept.dept_name as department_name
                FROM documents d
                LEFT JOIN users u ON d.owner_id = u.user_id
                LEFT JOIN departments dept ON d.department_id = dept.dept_id
                WHERE d.doc_id = :doc_id
            """)

            result = await db.execute(sql, {"doc_id": doc_id})
            row = result.fetchone()

            if not row:
                return None

            file_name = row.file_path.split('/')[-1] if row.file_path else None

            return {
                "id": row.id,
                "doc_id": row.doc_id,
                "title": row.title,
                "file_path": row.file_path,
                "file_name": file_name,
                "file_type": row.file_type,
                "project_name": row.project_name,
                "owner_id": row.owner_id,
                "owner_name": row.owner_name,
                "department_id": row.department_id,
                "department_name": row.department_name,
                "file_size": row.file_size,
                "status": row.status,
                "created_at": row.created_at.isoformat() if row.created_at else None,
                "updated_at": row.updated_at.isoformat() if row.updated_at else None
            }

        except Exception as e:
            log.error(f"根据doc_id获取文档详情失败: {e}")
            return None

    @staticmethod
    def format_document_info(document: Document, owner_name: str = None, department_name: str = None) -> Dict[str, Any]:
        """格式化文档信息"""
        file_name = document.file_path.split('/')[-1] if document.file_path else None

        return {
            "id": document.id,
            "doc_id": document.doc_id,
            "title": document.title,
            "file_path": document.file_path,
            "file_name": file_name,
            "file_type": document.file_type,
            "project_name": document.project_name,
            "owner_id": document.owner_id,
            "owner_name": owner_name,
            "department_id": document.department_id,
            "department_name": department_name,
            "file_size": document.file_size,
            "status": document.status.value if document.status else None,
            "created_at": document.created_at.isoformat() if document.created_at else None,
            "updated_at": document.updated_at.isoformat() if document.updated_at else None
        }
