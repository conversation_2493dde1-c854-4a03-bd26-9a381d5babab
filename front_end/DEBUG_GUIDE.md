# 认证系统调试指南

## 问题诊断

如果首次访问没有跳转到登录页面，请按以下步骤进行调试：

### 1. 检查浏览器控制台

打开浏览器开发者工具（F12），查看控制台输出：

```
路由守卫检查: {
  to: "/",
  isLoggedIn: false,
  hasToken: false,
  requiresAuth: true,
  hideForAuth: false
}
未登录用户访问受保护页面，重定向到登录页
```

如果看到这样的输出，说明路由守卫正常工作。

### 2. 检查localStorage

在浏览器控制台执行以下命令：

```javascript
// 检查是否有残留的认证数据
console.log('Token:', localStorage.getItem('access_token'));
console.log('User Info:', localStorage.getItem('user_info'));

// 清除所有认证数据
localStorage.removeItem('access_token');
localStorage.removeItem('refresh_token');
localStorage.removeItem('user_info');
localStorage.removeItem('email');
localStorage.removeItem('userName');

// 刷新页面
location.reload();
```

### 3. 检查Vuex状态

在浏览器控制台执行：

```javascript
// 检查store状态（需要Vue DevTools）
$vm.$store.state.user
$vm.$store.getters['user/isLoggedIn']
$vm.$store.getters['user/hasToken']
```

### 4. 手动测试路由跳转

在浏览器控制台执行：

```javascript
// 手动跳转到登录页
$vm.$router.push('/login');

// 手动跳转到首页（应该被重定向到登录页）
$vm.$router.push('/');
```

## 常见问题及解决方案

### 问题1: 页面空白或无限重定向

**原因**: 可能是路由守卫逻辑错误或store状态异常

**解决方案**:
1. 清除所有localStorage数据
2. 检查控制台错误信息
3. 确保Login.vue组件能正常加载

### 问题2: 路由守卫不执行

**原因**: 可能是router配置问题

**解决方案**:
1. 检查router/index.js是否正确导入store
2. 确保beforeEach守卫已正确注册
3. 检查是否有其他路由守卫冲突

### 问题3: 登录后仍然跳转到登录页

**原因**: token或用户状态未正确设置

**解决方案**:
1. 检查登录API响应格式
2. 确保token正确存储到localStorage
3. 检查isLoggedIn状态是否正确更新

## 测试步骤

### 步骤1: 清空状态测试

```javascript
// 1. 清除所有认证数据
localStorage.clear();

// 2. 刷新页面
location.reload();

// 3. 应该自动跳转到 /login
```

### 步骤2: 模拟登录状态测试

```javascript
// 1. 模拟登录状态
const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IlRlc3QgVXNlciIsImV4cCI6' + (Math.floor(Date.now() / 1000) + 3600) + '}.test';
localStorage.setItem('access_token', mockToken);
localStorage.setItem('user_info', JSON.stringify({
  id: 1,
  username: 'test',
  email: '<EMAIL>'
}));

// 2. 刷新页面
location.reload();

// 3. 应该能正常访问首页
```

### 步骤3: 路由保护测试

```javascript
// 1. 在未登录状态下尝试访问受保护页面
localStorage.clear();
$vm.$router.push('/search');

// 2. 应该被重定向到 /login?redirect=/search
```

## 开发环境启动

确保按正确顺序启动：

```bash
# 1. 安装依赖
npm install

# 2. 启动开发服务器
npm run dev

# 3. 访问 http://localhost:5173
```

## 生产环境部署

```bash
# 1. 构建项目
npm run build

# 2. 部署dist目录到web服务器

# 3. 确保服务器配置了正确的路由回退
# nginx配置示例:
# location / {
#   try_files $uri $uri/ /index.html;
# }
```

## 后端API要求

确保后端提供以下API端点：

```
POST /auth/login
POST /auth/register
POST /auth/refresh
GET  /auth/user/info
POST /auth/logout
```

响应格式示例：

```json
// 登录成功响应
{
  "access_token": "eyJ...",
  "refresh_token": "eyJ...",
  "user_info": {
    "id": 1,
    "username": "test",
    "email": "<EMAIL>",
    "nick_name": "测试用户"
  }
}
```

## 联系支持

如果问题仍然存在，请提供以下信息：

1. 浏览器控制台的完整输出
2. 网络请求的详细信息
3. localStorage的内容
4. 具体的错误步骤

这将帮助快速定位和解决问题。
