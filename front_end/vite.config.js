import { fileURLToPath, URL } from 'node:url'
import fs from 'fs'
import path from 'path'

import { defineConfig, loadEnv } from 'vite'
import legacy from '@vitejs/plugin-legacy'
import vue2 from '@vitejs/plugin-vue2'

// 简单的环境配置
const ENV_CONFIG = {
  development: {
    SERVICE_A_URL: 'http://***********:8081',
    SERVICE_B_URL: 'http://***********:8080',
    SERVICE_B_API: 'http://***********:18888'
  },
  production: {
    SERVICE_A_URL: 'http://*************:81',
    SERVICE_B_URL: 'http://*************:80',
    SERVICE_B_API: 'http://*************:18888'
  }
}

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  // 获取环境配置
  const envName = process.env.BUILD_ENV || 'development'
  const config = ENV_CONFIG[envName] || ENV_CONFIG.development

  return {
    plugins: [
      vue2(),
      // 简单的配置注入插件
      {
        name: 'inject-config',
        configureServer(server) {
          // 开发环境：提供虚拟的config.js文件
          server.middlewares.use('/config.js', (req, res) => {
            const configContent = `window.BUILD_CONFIG = ${JSON.stringify({
              ENVIRONMENT: envName,
              ...config
            }, null, 2)};`

            res.setHeader('Content-Type', 'application/javascript')
            res.end(configContent)
          })
        },
        generateBundle() {
          // 生产环境：生成配置文件
          const configContent = `window.BUILD_CONFIG = ${JSON.stringify({
            ENVIRONMENT: envName,
            ...config
          }, null, 2)};`

          this.emitFile({
            type: 'asset',
            fileName: 'config.js',
            source: configContent
          })
        },
        transformIndexHtml(html) {
          // 在HTML中注入配置文件引用
          return html.replace(
            '<head>',
            '<head>\n  <script src="./config.js"></script>'
          )
        }
      },
      legacy({
        targets: ['ie >= 11'],
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
        // 调试友好配置
        modernPolyfills: true,
        renderLegacyChunks: false,  // 禁用legacy chunks以简化输出
      }),
      // 自定义插件来彻底移除iframe限制
      {
        name: 'remove-iframe-restrictions',
        configureServer(server) {
          server.middlewares.use((req, res, next) => {
            // 彻底移除所有可能阻止iframe的headers
            res.removeHeader('X-Frame-Options');
            res.removeHeader('Content-Security-Policy');
            res.removeHeader('X-Content-Type-Options');

            // 设置开发环境的iframe支持headers
            res.setHeader('X-Frame-Options', 'SAMEORIGIN');
            res.setHeader('Content-Security-Policy', "frame-ancestors 'self' http://localhost:* https://localhost:* http://***********:* https://***********:* http://*************:* https://*************:* http://*************:* https://*************:*; default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';");
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', '*');
            res.setHeader('Access-Control-Allow-Headers', '*');
            res.setHeader('Access-Control-Allow-Credentials', 'true');

            // 处理OPTIONS预检请求
            if (req.method === 'OPTIONS') {
              res.statusCode = 200;
              res.end();
              return;
            }

            next();
          });
        }
      }
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    server: {
      open: false,  // 容器环境不需要自动打开浏览器
      host: "0.0.0.0",
      port: 22,
      headers: {
        // iframe嵌入支持 - 开发环境宽松策略
        'Content-Security-Policy': "frame-ancestors 'self' http://localhost:* https://localhost:* http://***********:* https://***********:* http://*************:* https://*************:* http://*************:* https://*************:*; default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';",
        'X-Frame-Options': 'SAMEORIGIN',
        // CORS设置
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-User-ID',
        'Access-Control-Allow-Credentials': 'true',
      },
      proxy: {
        // 静态HTML文件使用的认证API代理
        '/auth': {
          target: env.VITE_BACKEND_URL || 'http://***********:18888',
          changeOrigin: true,
          rewrite: (path) => path // 保持 /auth 路径不变
        },
        // Vue组件使用的认证API代理
        '/api-s': {
          target: env.VITE_BACKEND_URL || 'http://***********:18888',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api-s/, '') // 移除 /api-s 前缀
        },
        // Vue组件使用的主要API代理 (assembly_keywords等)
        '/dev-api': {
          target: env.VITE_BACKEND_URL || 'http://***********:18888',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dev-api/, '') // 移除 /dev-api 前缀
        },
        // 文件下载代理
        '/download': {
          target: env.VITE_BACKEND_URL || 'http://***********:18888',
          changeOrigin: true,
          rewrite: (path) => path // 保持 /download 路径不变
        },
      },
    },
    css: {
      // 修复 CSS 嵌套语法警告 - 设置更现代的目标环境
      postcss: {
        plugins: []
      }
    },
    build: {
      // 构建配置
      outDir: 'dist',
      assetsDir: 'assets',
      // 生产环境构建配置 - 优化内存使用
      minify: 'esbuild',  // 使用更快的esbuild压缩
      sourcemap: false,  // 不生成源码映射
      // 调整 chunk 大小警告限制，因为我们有一些大的第三方库
      chunkSizeWarningLimit: 3000,  // 3MB 限制，适应 PDF 等大型库
      // 确保构建后的文件支持iframe嵌入
      rollupOptions: {
        output: {
          // 启用哈希文件名，解决缓存问题
          // 这样每次构建都会生成新的文件名，避免浏览器缓存旧版本
          entryFileNames: '[name].[hash].js',
          chunkFileNames: '[name].[hash].js',
          assetFileNames: '[name].[hash].[ext]'
          // 移除手动分包，让 Vite 自动处理依赖关系，避免循环依赖问题
        }
      },
      // 确保 public 目录中的文件被正确复制
      copyPublicDir: true
    },
    define: {
      'process.env': process.env,
      // 定义全局变量，用于运行时检测iframe环境
      __IFRAME_SUPPORT__: true
    },
  }
})
