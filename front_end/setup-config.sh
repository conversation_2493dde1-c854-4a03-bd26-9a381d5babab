#!/bin/bash

# 前端配置快速设置脚本
# 用法: ./setup-config.sh

echo "🔧 前端配置快速设置"
echo "===================="

# 获取当前目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查是否存在配置文件
if [ -f ".env.development" ]; then
    echo "📋 发现现有开发环境配置:"
    echo "   .env.development 已存在"
    read -p "是否要重新配置? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "跳过开发环境配置"
    else
        rm .env.development
    fi
fi

if [ -f ".env.production" ]; then
    echo "📋 发现现有生产环境配置:"
    echo "   .env.production 已存在"
    read -p "是否要重新配置? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "跳过生产环境配置"
    else
        rm .env.production
    fi
fi

# 配置开发环境
if [ ! -f ".env.development" ]; then
    echo
    echo "⚙️ 配置开发环境"
    echo "=================="
    
    # 获取后端服务器IP
    read -p "请输入后端服务器IP (默认: ***********): " BACKEND_IP
    BACKEND_IP=${BACKEND_IP:-***********}
    
    # 获取后端端口
    read -p "请输入后端服务端口 (默认: 18888): " BACKEND_PORT
    BACKEND_PORT=${BACKEND_PORT:-18888}
    
    # 获取Elasticsearch IP
    read -p "请输入Elasticsearch IP (默认: ${BACKEND_IP}): " ES_IP
    ES_IP=${ES_IP:-$BACKEND_IP}
    
    # 获取Elasticsearch端口
    read -p "请输入Elasticsearch端口 (默认: 9200): " ES_PORT
    ES_PORT=${ES_PORT:-9200}
    
    # 生成开发环境配置
    cat > .env.development << EOF
# =================================
# 开发环境配置
# 自动生成于: $(date)
# =================================

# 后端服务地址 (用于Vite代理)
VITE_BACKEND_URL = 'http://${BACKEND_IP}:${BACKEND_PORT}'

# 基础API路径 (开发环境使用代理)
VITE_VUE_APP_BASE_API = '/dev-api'

# AI/LLM API路径 (开发环境使用代理)
VITE_VUE_APP_OTHER_API = '/api-s'
VITE_VUE_APP_OTHER_PATH_API = 'http://${BACKEND_IP}:${BACKEND_PORT}/'

# Elasticsearch地址
VITE_VUE_APP_API = 'http://${ES_IP}:${ES_PORT}'

# 文件下载服务 (开发环境使用代理)
VITE_FILE_BASE_URL = '/download?url='

# =================================
# 开发环境特殊配置
# =================================

# 环境标识
VITE_ENV_NAME = '开发环境'
VITE_ENV_MODE = 'development'

# 调试模式
VITE_DEBUG_MODE = 'true'

# iframe允许的来源
VITE_IFRAME_ALLOWED_ORIGINS = 'http://localhost:8080,http://${BACKEND_IP}:8080'
EOF
    
    echo "✅ 开发环境配置已生成: .env.development"
fi

# 配置生产环境
if [ ! -f ".env.production" ]; then
    echo
    echo "🚀 配置生产环境"
    echo "=================="
    
    # 获取生产域名
    read -p "请输入生产环境域名 (例如: your-domain.com): " PROD_DOMAIN
    
    if [ -z "$PROD_DOMAIN" ]; then
        echo "⚠️  跳过生产环境配置 (未输入域名)"
    else
        # 获取协议
        read -p "使用HTTPS? (Y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Nn]$ ]]; then
            PROTOCOL="http"
        else
            PROTOCOL="https"
        fi
        
        # 生成生产环境配置
        cat > .env.production << EOF
# =================================
# 生产环境配置
# 自动生成于: $(date)
# =================================

# 后端服务地址 (生产环境不使用代理，直接访问)
VITE_BACKEND_URL = '${PROTOCOL}://${PROD_DOMAIN}'

# 基础API路径 (生产环境使用相对路径)
VITE_VUE_APP_BASE_API = '/api'

# AI/LLM API路径 (生产环境使用相对路径)
VITE_VUE_APP_OTHER_API = '/api-s'
VITE_VUE_APP_OTHER_PATH_API = '${PROTOCOL}://${PROD_DOMAIN}/'

# Elasticsearch地址 (生产环境)
VITE_VUE_APP_API = '${PROTOCOL}://${PROD_DOMAIN}:9200'

# 文件下载服务 (生产环境使用完整URL)
VITE_FILE_BASE_URL = '${PROTOCOL}://${PROD_DOMAIN}/download?url='

# =================================
# 生产环境特殊配置
# =================================

# 环境标识
VITE_ENV_NAME = '生产环境'
VITE_ENV_MODE = 'production'

# 调试模式 (生产环境关闭)
VITE_DEBUG_MODE = 'false'

# iframe允许的来源 (生产环境限制)
VITE_IFRAME_ALLOWED_ORIGINS = '${PROTOCOL}://${PROD_DOMAIN}'

# =================================
# 性能优化配置
# =================================

# 启用压缩
VITE_ENABLE_GZIP = 'true'

# 启用CDN (可选)
# VITE_CDN_URL = 'https://cdn.${PROD_DOMAIN}'
EOF
        
        echo "✅ 生产环境配置已生成: .env.production"
    fi
fi

echo
echo "🎉 配置设置完成!"
echo "=================="

# 显示配置摘要
if [ -f ".env.development" ]; then
    echo "📋 开发环境配置:"
    echo "   后端服务: $(grep VITE_BACKEND_URL .env.development | cut -d"'" -f2)"
    echo "   Elasticsearch: $(grep VITE_VUE_APP_API .env.development | cut -d"'" -f2)"
fi

if [ -f ".env.production" ]; then
    echo "📋 生产环境配置:"
    echo "   后端服务: $(grep VITE_BACKEND_URL .env.production | cut -d"'" -f2)"
    echo "   文件下载: $(grep VITE_FILE_BASE_URL .env.production | cut -d"'" -f2)"
fi

echo
echo "🚀 使用方法:"
echo "   开发环境: npm run dev"
echo "   生产构建: npm run build"
echo "   预览构建: npm run preview"
echo
echo "📖 详细说明请查看: CONFIG_GUIDE.md"
