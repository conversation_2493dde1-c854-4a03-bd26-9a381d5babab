<template>
  <div class="user-menu">
    <el-dropdown @command="handleCommand" trigger="click">
      <span class="user-info">
        <el-avatar :size="32" :src="userAvatar">
          <i class="el-icon-user-solid"></i>
        </el-avatar>
        <span class="username">{{ userName }}</span>
        <i class="el-icon-arrow-down"></i>
      </span>
      
      <el-dropdown-menu slot="dropdown">
        <!-- 用户信息 -->
        <el-dropdown-item disabled>
          <div class="user-detail">
            <div class="name">{{ userName }}</div>
            <div class="email">{{ userEmail }}</div>
            <div class="dept">{{ userDept }}</div>
          </div>
        </el-dropdown-item>
        
        <el-dropdown-item divided command="profile">
          <i class="el-icon-user"></i> 个人资料
        </el-dropdown-item>
        
        <!-- 权限相关菜单 -->
        <el-dropdown-item command="permission-request">
          <i class="el-icon-document-add"></i> 权限申请
        </el-dropdown-item>
        
        <!-- 管理员菜单 -->
        <el-dropdown-item 
          v-if="canReviewPermissions" 
          command="permission-approval"
        >
          <i class="el-icon-s-check"></i> 权限审核
          <el-badge 
            v-if="pendingRequestsCount > 0" 
            :value="pendingRequestsCount" 
            class="badge"
          />
        </el-dropdown-item>
        
        <el-dropdown-item divided command="settings">
          <i class="el-icon-setting"></i> 设置
        </el-dropdown-item>
        
        <el-dropdown-item command="logout">
          <i class="el-icon-switch-button"></i> 退出登录
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { canReviewPermissions } from '@/utils/auth'

export default {
  name: 'UserMenu',
  data() {
    return {
      pendingRequestsCount: 0
    }
  },
  computed: {
    ...mapGetters('user', ['userName', 'userEmail', 'userDept', 'userInfo', 'avatar']),
    
    userAvatar() {
      return this.avatar || ''
    },
    
    canReviewPermissions() {
      return canReviewPermissions()
    }
  },
  mounted() {
    if (this.canReviewPermissions) {
      this.loadPendingRequestsCount()
    }
  },
  methods: {
    ...mapActions('user', ['logout']),
    
    async handleCommand(command) {
      switch (command) {
        case 'profile':
          this.goToProfile()
          break
        case 'permission-request':
          this.goToPermissionRequest()
          break
        case 'permission-approval':
          this.goToPermissionApproval()
          break
        case 'settings':
          this.goToSettings()
          break
        case 'logout':
          await this.handleLogout()
          break
      }
    },
    
    goToProfile() {
      // 跳转到个人资料页面
      this.$message.info('个人资料功能待实现')
    },
    
    goToPermissionRequest() {
      this.$router.push('/permission-request')
    },
    
    goToPermissionApproval() {
      this.$router.push('/permission-approval')
    },
    
    goToSettings() {
      // 跳转到设置页面
      this.$message.info('设置功能待实现')
    },
    
    async handleLogout() {
      try {
        await this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await this.logout()
        this.$message.success('已退出登录')
        this.$router.push('/login')
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('退出登录失败:', error)
        }
      }
    },
    
    async loadPendingRequestsCount() {
      try {
        const response = await this.$http.get('/auth/permission-requests/review', {
          params: { status: 'pending', size: 1 }
        })
        this.pendingRequestsCount = response.data?.total || 0
      } catch (error) {
        console.error('获取待审核申请数量失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.user-menu {
  .user-info {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    .username {
      margin: 0 8px;
      font-size: 14px;
      color: #333;
    }
    
    .el-icon-arrow-down {
      font-size: 12px;
      color: #999;
    }
  }
  
  .user-detail {
    padding: 8px 0;
    text-align: center;
    
    .name {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }
    
    .email {
      font-size: 12px;
      color: #666;
      margin-bottom: 2px;
    }
    
    .dept {
      font-size: 12px;
      color: #999;
    }
  }
  
  .badge {
    margin-left: 8px;
  }
}

// 全局样式覆盖
:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  
  i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
  }
}
</style>
