<template>
  <el-dialog
    title="申请文档权限"
    :visible.sync="visible"
    width="500px"
    :before-close="handleClose"
  >
    <div class="permission-request-form">
      <!-- 文档信息 -->
      <div class="document-info">
        <h4>文档信息</h4>
        <p><strong>标题:</strong> {{ documentInfo.title || '未知文档' }}</p>
        <p><strong>项目:</strong> {{ documentInfo.project_name || '未知项目' }}</p>
        <p v-if="documentInfo.owner_name"><strong>所有者:</strong> {{ documentInfo.owner_name }}</p>
      </div>

      <!-- 权限类型选择 -->
      <div class="permission-type">
        <h4>申请权限类型</h4>
        <el-radio-group v-model="form.permission_type">
          <el-radio label="read">查看权限</el-radio>
          <el-radio label="download">下载权限</el-radio>
        </el-radio-group>
      </div>

      <!-- 申请理由 -->
      <div class="reason">
        <h4>申请理由 <span class="required">*</span></h4>
        <el-input
          v-model="form.reason"
          type="textarea"
          :rows="4"
          placeholder="请详细说明您申请此权限的理由，例如：用于项目开发、学习参考等"
          maxlength="500"
          show-word-limit
        />
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleSubmit"
        :loading="submitting"
        :disabled="!form.reason.trim()"
      >
        提交申请
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { submitPermissionRequest } from '@/api/permission'

export default {
  name: 'PermissionRequestDialog',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    documentInfo: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      submitting: false,
      form: {
        permission_type: 'read',
        reason: ''
      }
    }
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        // 对话框打开时重置表单
        this.resetForm()
      }
    }
  },

  methods: {
    resetForm() {
      this.form = {
        permission_type: 'read',
        reason: ''
      }
    },

    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    async handleSubmit() {
      if (!this.form.reason.trim()) {
        this.$message.error('请填写申请理由')
        return
      }

      if (this.form.reason.trim().length < 10) {
        this.$message.error('申请理由至少需要10个字符')
        return
      }

      this.submitting = true

      try {
        console.log('提交权限申请，文档信息:', this.documentInfo)

        const requestData = {
          ...this.form,
          reason: this.form.reason.trim()
        }

        // 根据文档信息类型选择不同的请求参数（优先级：doc_id > document_id > document_url）
        if (this.documentInfo.doc_id && this.documentInfo.doc_id.trim()) {
          requestData.doc_id = this.documentInfo.doc_id.trim()
          console.log('使用 doc_id:', requestData.doc_id)
        } else if (this.documentInfo.id) {
          requestData.document_id = this.documentInfo.id
          console.log('使用 document_id:', requestData.document_id)
        } else if (this.documentInfo.url && this.documentInfo.url.trim()) {
          requestData.document_url = this.documentInfo.url.trim()
          console.log('使用 document_url:', requestData.document_url)
        } else {
          console.error('缺少文档标识信息，documentInfo:', this.documentInfo)
          console.error('检查的字段: doc_id=', this.documentInfo.doc_id, ', id=', this.documentInfo.id, ', url=', this.documentInfo.url)
          throw new Error('缺少文档标识信息，无法提交权限申请')
        }

        console.log('最终请求数据:', requestData)

        const response = await submitPermissionRequest(requestData)

        if (response.code === 200) {
          this.$message.success('权限申请提交成功，请等待审批')
          this.$emit('success', response.data)
          this.handleClose()
        } else {
          this.$message.error(response.msg || '申请提交失败')
        }
      } catch (error) {
        console.error('提交权限申请失败:', error)
        this.$message.error('申请提交失败，请稍后重试')
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.permission-request-form {
  .document-info {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
    }

    p {
      margin: 5px 0;
      color: #606266;
      font-size: 13px;
    }
  }

  .permission-type {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
    }
  }

  .reason {
    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;

      .required {
        color: #f56c6c;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
