<template>
  <div v-if="showWarning" class="storage-warning">
    <el-alert
      :title="warningTitle"
      :description="warningDescription"
      type="warning"
      :closable="true"
      @close="dismissWarning"
      show-icon
    >
      <template slot="title">
        <i class="el-icon-warning"></i>
        {{ warningTitle }}
      </template>
    </el-alert>
  </div>
</template>

<script>
import { 
  isSessionStorageAvailable, 
  isLocalStorageAvailable, 
  isInIframe, 
  isCrossOriginIframe 
} from '@/utils/storage'

export default {
  name: 'StorageCompatibility',
  data() {
    return {
      showWarning: false,
      warningTitle: '',
      warningDescription: '',
      hasChecked: false
    }
  },
  mounted() {
    this.checkStorageCompatibility()
  },
  methods: {
    checkStorageCompatibility() {
      if (this.hasChecked) return
      this.hasChecked = true

      const sessionAvailable = isSessionStorageAvailable()
      const localAvailable = isLocalStorageAvailable()
      const inIframe = isInIframe()
      const crossOrigin = isCrossOriginIframe()

      console.log('存储兼容性检查:', {
        sessionStorage: sessionAvailable,
        localStorage: localAvailable,
        inIframe,
        crossOrigin
      })

      // 如果在跨域iframe中且sessionStorage不可用
      if (crossOrigin && !sessionAvailable) {
        this.showWarning = true
        this.warningTitle = '存储功能受限'
        this.warningDescription = '当前在跨域环境中运行，部分功能可能受到限制。系统将自动使用替代方案确保正常运行。'
      }
      // 如果localStorage也不可用
      else if (!localAvailable) {
        this.showWarning = true
        this.warningTitle = '存储功能不可用'
        this.warningDescription = '浏览器存储功能被禁用，可能影响用户体验。请检查浏览器设置或尝试刷新页面。'
      }
      // 如果只是sessionStorage不可用
      else if (!sessionAvailable && localAvailable) {
        console.warn('sessionStorage不可用，将使用localStorage作为替代')
      }
    },

    dismissWarning() {
      this.showWarning = false
      // 记录用户已经看过警告
      try {
        localStorage.setItem('storage_warning_dismissed', Date.now().toString())
      } catch (e) {
        console.warn('无法记录警告状态:', e.message)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.storage-warning {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
  
  .el-alert {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .storage-warning {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
}
</style>
