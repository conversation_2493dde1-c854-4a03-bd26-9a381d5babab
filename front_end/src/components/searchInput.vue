<template>
  <div>
    <div class="search-input" :class="{ row: isRow, column: !isRow }">
      <h1 :class="{ title: isRow }" @click="isRow && $router.push('/')">
        档案检索
      </h1>
      <el-input
        placeholder="请输入关键字"
        v-model="queryForm.keyword"
        class="input"
        @change="handleInput"
        @keydown.enter.native="toSearch"
        size="large"
      >
        <el-select
          slot="prepend"
          v-model="queryForm.type"
          placeholder="请选择"
          @change="handleSelectorChange"
          style="width: 112px"
        >
          <el-option label="综合搜索" value="1"></el-option>
          <!-- <el-option label="部门搜索" value="2"></el-option> -->
          <el-option label="精确搜索" value="3"></el-option>
        </el-select>
        <el-button slot="append" icon="el-icon-search" @click="toSearch">
        </el-button>
      </el-input>
    </div>

  </div>
</template>

<script>
export default {
  name: 'searchInput',
  props: {
    value: {
      type: Object,
      default: () => ({
        type: '1',
        keyword: '',
      }),
    },
    isRow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      queryForm: {},
    }
  },
  mounted() {
    this.queryForm = this.value
  },
  watch: {
    // 监听props变化
    value: {
      handler(newValue) {
        this.queryForm = newValue
      },
      deep: true
    }
  },
  methods: {
    handleSelectorChange(val) {
      this.$emit('input', this.queryForm)
      if (val == 3) {
        this.$router.push({
          path: '/info',
        })
      }
    },
    handleInput() {
      this.$emit('input', this.queryForm)
    },
    toSearch() {
      if (!this.queryForm.keyword.trim()) return
      this.$emit('search', this.queryForm)
    },
  },
}
</script>

<style lang="scss" scoped>
.search-input {
  display: flex;
  &.column {
    flex-direction: column;
    align-items: center;
  }
  &.row {
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;  // 改为左对齐
    h1 {
      font-size: 26px;
      margin: 0 20px 0 0;  // 只保留右边距
      flex-shrink: 0;      // 防止标题被压缩
    }
  }
  .title {
    cursor: pointer;
  }
  h1 {
    color: #3cb3ff;
    margin: 30px;
  }
  .input {
    width: 40%;
  }

  // 行模式下的特殊处理 - 让搜索框自适应
  &.row {
    width: 100%;

    .input {
      flex: 1;           // 占据剩余空间
      min-width: 300px;  // 最小宽度
      max-width: none;   // 移除最大宽度限制
    }
  }
}
</style>
