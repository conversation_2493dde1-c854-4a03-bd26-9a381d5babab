<template>
  <div class="user-info">
    <el-dropdown @command="handleCommand" trigger="click">
      <div class="user-avatar-wrapper">
        <el-avatar
          :size="32"
          :src="userInfo.avatar"
          :style="{ backgroundColor: avatarBgColor }"
        >
          {{ avatarText }}
        </el-avatar>
        <span class="username">{{ displayName }}</span>
        <i class="el-icon-arrow-down el-icon--right"></i>
      </div>
      
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="profile">
          <i class="el-icon-user"></i>
          个人信息
        </el-dropdown-item>
        <el-dropdown-item command="settings">
          <i class="el-icon-setting"></i>
          账户设置
        </el-dropdown-item>
        <el-dropdown-item command="changePassword">
          <i class="el-icon-key"></i>
          修改密码
        </el-dropdown-item>
        <el-dropdown-item divided command="logout">
          <i class="el-icon-switch-button"></i>
          退出登录
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    
    <!-- 个人信息对话框 -->
    <el-dialog
      title="个人信息"
      :visible.sync="profileVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="profileForm"
        :model="profileForm"
        :rules="profileRules"
        label-width="80px"
      >
        <el-form-item label="用户名">
          <el-input v-model="userInfo.username" disabled />
        </el-form-item>
        
        <el-form-item label="昵称" prop="nickName">
          <el-input
            v-model="profileForm.nickName"
            placeholder="请输入昵称"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="profileForm.email"
            placeholder="请输入邮箱"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="profileForm.phone"
            placeholder="请输入手机号"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="部门">
          <el-input v-model="userInfo.dept_name" disabled />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="profileVisible = false">取消</el-button>
        <el-button type="primary" @click="updateProfile">保存</el-button>
      </div>
    </el-dialog>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      :visible.sync="passwordVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordForm"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入原密码"
            show-password
            clearable
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
            clearable
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
            clearable
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordVisible = false">取消</el-button>
        <el-button type="primary" @click="updatePassword">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import { updateUserInfo, changePassword } from '@/api/auth'

export default {
  name: 'UserInfo',
  data() {
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    
    return {
      profileVisible: false,
      passwordVisible: false,
      profileForm: {
        nickName: '',
        email: '',
        phone: ''
      },
      profileRules: {
        email: [
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
        ]
      },
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters('user', ['userInfo', 'userName', 'userEmail']),
    
    displayName() {
      return this.userInfo.nick_name || this.userInfo.username || '用户'
    },
    
    avatarText() {
      const name = this.displayName
      return name.length > 0 ? name.charAt(0).toUpperCase() : 'U'
    },
    
    avatarBgColor() {
      // 根据用户名生成颜色
      const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
      const index = this.displayName.charCodeAt(0) % colors.length
      return colors[index]
    }
  },
  methods: {
    ...mapActions('user', ['logout', 'getUserInfo']),
    
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.showProfile()
          break
        case 'settings':
          this.$message.info('功能开发中...')
          break
        case 'changePassword':
          this.showChangePassword()
          break
        case 'logout':
          this.handleLogout()
          break
      }
    },
    
    showProfile() {
      this.profileForm = {
        nickName: this.userInfo.nick_name || '',
        email: this.userInfo.email || '',
        phone: this.userInfo.phone || ''
      }
      this.profileVisible = true
    },
    
    showChangePassword() {
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.passwordVisible = true
    },
    
    async updateProfile() {
      try {
        await this.$refs.profileForm.validate()
        await updateUserInfo(this.profileForm)
        
        this.$message.success('个人信息更新成功')
        this.profileVisible = false
        
        // 重新获取用户信息
        await this.getUserInfo()
      } catch (error) {
        this.$message.error(error.message || '更新失败')
      }
    },
    
    async updatePassword() {
      try {
        await this.$refs.passwordForm.validate()
        await changePassword(this.passwordForm)
        
        this.$message.success('密码修改成功')
        this.passwordVisible = false
        
        // 重置表单
        this.passwordForm = {
          oldPassword: '',
          newPassword: '',
          confirmPassword: ''
        }
      } catch (error) {
        this.$message.error(error.message || '密码修改失败')
      }
    },
    
    async handleLogout() {
      try {
        await this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await this.logout()
        this.$router.push('/login')
      } catch (error) {
        // 用户取消操作
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.user-info {
  .user-avatar-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    .username {
      margin-left: 8px;
      margin-right: 4px;
      font-size: 14px;
      color: #606266;
      max-width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .el-icon-arrow-down {
      font-size: 12px;
      color: #909399;
    }
  }
}

::v-deep .el-dropdown-menu__item {
  padding: 8px 16px;
  
  i {
    margin-right: 8px;
    width: 16px;
  }
}
</style>
