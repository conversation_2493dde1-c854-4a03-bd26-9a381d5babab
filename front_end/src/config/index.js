/**
 * 简单的配置访问接口
 */

// 默认配置
const DEFAULT_CONFIG = {
  SERVICE_A_URL: 'http://192.168.1.2:8081',
  SERVICE_B_URL: 'http://192.168.1.2:8080',
  SERVICE_B_API: 'http://192.168.1.2:18888'
};

function getConfig() {
  return (typeof window !== 'undefined' && window.BUILD_CONFIG) || DEFAULT_CONFIG;
}

export function getServiceAUrl() {
  return getConfig().SERVICE_A_URL;
}

export function getServiceBUrl() {
  return getConfig().SERVICE_B_URL;
}

export function getServiceBApi() {
  return getConfig().SERVICE_B_API;
}

export default {
  getServiceAUrl,
  getServiceBUrl,
  getServiceBApi
};
