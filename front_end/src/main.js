import Vue from 'vue'

import App from './App.vue'
import router, { setAppInitialized } from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
Vue.use(ElementUI)

// 引入全局plugin
import plugin from './plugin'
Vue.use(plugin)
// localforage
import 'localforage'

import './assets/style/index.scss'
import './assets/css/search-input-fix.css'
import { initAuth } from '@/utils/simple_auth'
import { initIframeSecurity } from '@/utils/iframe-security'
import { setupGlobalModuleErrorHandlers } from '@/utils/module-error-handler'

// 导入组件和工具
import UserMenu from '@/components/UserMenu.vue'
import request from '@/utils/request'

// 注册全局组件
Vue.component('UserMenu', UserMenu)

// 将request挂载到Vue原型上
Vue.prototype.$http = request

// 将store挂载到全局，供utils函数使用
window.store = store

// ========== 简化的应用初始化 ==========

// 根据实际业务场景分析：
// Service A通过API生成token，iframe URL传递给Service B
// Service B接收token验证并登录，Service A离开时API回收token
// 结论：不需要复杂的postMessage通信机制

// 设置iframe安全管理器的事件监听
function setupIframeSecurityListeners() {
  // 监听用户切换事件
  window.addEventListener('iframe-user-switch', async (event) => {
    const { user, token } = event.detail
    console.log('🔄 iframe安全管理器：用户切换', user.user_name)

    try {
      // 更新store中的用户信息
      await store.dispatch('user/setUserInfo', user)
      await store.dispatch('user/setToken', token)

      // 重新初始化认证状态
      await store.dispatch('user/initAuth')

      console.log('✅ 用户切换完成')
    } catch (error) {
      console.error('❌ 用户切换失败:', error)
    }
  })

  // 监听用户退出事件
  window.addEventListener('iframe-logout', async () => {
    console.log('🚪 iframe安全管理器：用户退出')

    try {
      // 清理用户状态
      await store.dispatch('user/logout')

      // 跳转到登录页面或首页
      router.push('/login')

      console.log('✅ 用户退出完成')
    } catch (error) {
      console.error('❌ 用户退出失败:', error)
      // 确保即使出错也能跳转到登录页
      try {
        router.push('/login')
      } catch (routerError) {
        console.error('❌ 路由跳转失败，使用备用方案:', routerError)
        window.location.href = '/login'
      }
    }
  })

  // 监听路由变更事件
  window.addEventListener('iframe-route-change', (event) => {
    const { route } = event.detail
    console.log('🔄 iframe安全管理器：路由变更', route)

    // 如果当前路由与目标路由不同，则跳转
    if (router.currentRoute.path !== route) {
      router.push(route)
    }
  })

  // 监听token刷新事件
  window.addEventListener('iframe-token-refresh', async (event) => {
    const { token } = event.detail
    console.log('🔄 iframe安全管理器：token刷新')

    try {
      // 更新token
      await store.dispatch('user/setToken', token)
      console.log('✅ token刷新完成')
    } catch (error) {
      console.error('❌ token刷新失败:', error)
    }
  })

  console.log('🔧 iframe安全管理器事件监听已设置')
}

// 应用初始化
async function initApp() {
  try {
    // 设置全局模块错误处理器（优先设置）
    setupGlobalModuleErrorHandlers()

    // 初始化认证（从URL参数获取token并验证）
    await initAuth(store, router)

    // 初始化用户认证状态
    await store.dispatch('user/initAuth')

    // 标记应用初始化完成
    setAppInitialized()

    // 设置iframe安全管理器的事件监听
    setupIframeSecurityListeners()

    // 初始化iframe安全管理器
    const iframeManager = initIframeSecurity()

    // 将管理器实例挂载到全局，供调试使用
    window.iframeSecurityManager = iframeManager

    // 创建Vue实例
    const app = new Vue({
      router,
      store,
      render: (h) => h(App),
      mounted() {
        // 设置全局引用，供request拦截器等使用
        window.vueApp = this;
        window.$vm = this;
      }
    })

    app.$mount('#app')
  } catch (error) {
    console.error('应用初始化失败:', error)
    // 确保标记初始化完成，避免无限等待
    setAppInitialized()

    // 创建备用Vue实例
    const fallbackApp = new Vue({
      router,
      store,
      render: (h) => h(App),
      mounted() {
        window.vueApp = this;
      }
    })
    fallbackApp.$mount('#app')
  }
}

// ===== iframe 通信现在由 iframe-security.js 管理 =====


// 全局存储错误处理（保留iframe环境的特殊处理）
window.addEventListener('error', function(event) {
  // 处理存储访问错误
  if (event.error && event.error.message &&
      (event.error.message.includes('sessionStorage') ||
       event.error.message.includes('localStorage'))) {
    console.warn('检测到存储访问错误:', event.error.message);

    // 检查是否为iframe环境
    const isIframe = window.self !== window.top;
    if (isIframe) {
      console.warn('当前在iframe环境中，存储访问可能受限');
    }

    // 阻止错误冒泡，避免影响用户体验
    event.preventDefault();
    return false;
  }

  // 模块加载错误现在由 module-error-handler.js 统一处理
});

// 启动应用
initApp().then(() => {
  console.log('🚀 应用启动完成，iframe安全管理器已接管通信');
});
