import request from '@/utils/request'

/**
 * Simple Auth API - 统一使用/api/simple-auth接口
 * 用于服务A和服务B的统一认证管理
 */

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/api/simple-auth/user-info',
    method: 'get'
  })
}

// 验证token
export function verifyToken() {
  return request({
    url: '/api/simple-auth/verify-token',
    method: 'get'
  })
}

// 生成token（管理员功能）
export function generateToken(userData) {
  return request({
    url: '/api/simple-auth/generate-token',
    method: 'post',
    data: userData
  })
}

// 获取仪表板信息
export function getDashboard() {
  return request({
    url: '/api/simple-auth/dashboard',
    method: 'get'
  })
}

// 刷新token
export function refreshSimpleAuthToken() {
  return request({
    url: '/api/simple-auth/refresh-token',
    method: 'post'
  })
}

// 登出（清理token）
export function logoutSimpleAuth() {
  return request({
    url: '/api/simple-auth/logout',
    method: 'post'
  })
}
