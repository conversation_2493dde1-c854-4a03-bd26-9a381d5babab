import request from '@/utils/request'
import requestAI from '@/utils/request_ai'
let searchAbortController = null
let similarAbortController = null
let explainAbortController = null
export function search(data) {
  if (searchAbortController) {
    searchAbortController.abort()
  }
  searchAbortController = new AbortController()
  return request({
    url: '/api/search/docs',
    method: 'post',
    data,
    signal: searchAbortController.signal,
  })
}
export function searchProjectInfo(data) {
  return request({
    url: '/api/search/projects',
    method: 'post',
    data,
  })
}

export function similarQuery(data) {
  if (similarAbortController) {
    similarAbortController.abort()
  }
  similarAbortController = new AbortController()
  return requestAI({
    url: 'similar_query',
    method: 'post',
    data,
    signal: similarAbortController.signal,
  })
}
export function explainQuery(data) {
  if (explainAbortController) {
    explainAbortController.abort()
  }
  explainAbortController = new AbortController()
  return requestAI({
    url: 'explain_query',
    method: 'post',
    data,
    signal: explainAbortController.signal,
  })
}
