import request from '@/utils/request'

/**
 * 权限管理相关API
 * 这里演示Vue.js的核心概念：模块化和API封装
 */

// 检查用户对单个文档的访问权限（通过文档ID）
export function checkDocumentPermissionById(documentId, permissionType = 'read') {
  return request({
    url: `/api/permissions/check/${documentId}`,
    method: 'get',
    params: { permission_type: permissionType }
  })
}

// 检查用户对文档的访问权限（通过文档URL）
// 返回格式: { code, msg, url, permission, user_info }
// permission: true/false 表示是否有权限
export function checkDocumentPermission(data) {
  return request({
    url: '/api/permissions/check-document-access',
    method: 'post',
    data
  })
}



// 根据文档URL获取文档信息（用于权限申请）
export function getDocumentByUrl(url) {
  return request({
    url: '/api/documents/by-url',
    method: 'get',
    params: { url }
  })
}

// 根据doc_id获取文档信息
export function getDocumentByDocId(docId) {
  return request({
    url: `/api/documents/by-doc-id/${docId}`,
    method: 'get'
  })
}

// 提交权限申请
export function submitPermissionRequest(data) {
  return request({
    url: '/api/permissions/request',
    method: 'post',
    data
  })
}

// 获取用户的权限申请列表
export function getUserPermissionRequests(params = {}) {
  return request({
    url: '/api/permissions/requests',
    method: 'get',
    params
  })
}

// 获取待审批的权限申请（管理员用）
export function getPendingReviews() {
  return request({
    url: '/api/permissions/pending-reviews',
    method: 'get'
  })
}

// 审批权限申请（统一接口，支持批准和拒绝）
export function approvePermissionRequest(requestId, data) {
  return request({
    url: `/api/permissions/approve/${requestId}`,
    method: 'post',
    data: data  // 不强制覆盖approved字段
  })
}

// 拒绝权限申请（保持向后兼容）
export function rejectPermissionRequest(requestId, data) {
  return request({
    url: `/api/permissions/approve/${requestId}`,
    method: 'post',
    data: {
      ...data,
      approved: false
    }
  })
}

// 撤回权限申请
export function withdrawPermissionRequest(requestId) {
  return request({
    url: `/api/permissions/requests/${requestId}/withdraw`,
    method: 'post'
  })
}





// 删除权限申请
export function deletePermissionRequest(requestId) {
  return request({
    url: `/api/permissions/requests/${requestId}`,
    method: 'delete'
  })
}

// 获取系统管理员已审批的记录
export function getMyApprovedRequests() {
  return request({
    url: '/api/permissions/my-approved',
    method: 'get'
  })
}

// 获取全部权限申请记录（系统管理员专用）- 支持分页和过滤
export function getAllPermissionRecords(params = {}) {
  return request({
    url: '/api/permissions/all-records',
    method: 'get',
    params
  })
}

// 获取部门统计数据（系统管理员专用）
export function getDepartmentStats() {
  return request({
    url: '/api/permissions/department-stats',
    method: 'get'
  })
}

// 获取当前用户信息（用于权限判断）
export function getCurrentUser() {
  return request({
    url: '/api/simple-auth/user-info',
    method: 'get'
  })
}

// 撤销权限（系统管理员专用）
export function revokePermission(data) {
  return request({
    url: '/api/permissions/revoke',
    method: 'delete',
    data
  })
}
