import request from '@/utils/request'

/**
 * 汇编对话历史管理 API
 */

// 创建汇编对话记录
export function createAssemblyConversation(data) {
  return request({
    url: '/api/assembly-history/conversations',
    method: 'post',
    data: data
  })
}

// 获取汇编对话历史列表
export function getAssemblyConversations(params = {}) {
  return request({
    url: '/api/assembly-history/conversations',
    method: 'get',
    params: params
  })
}

// 获取单个汇编对话详情（暂时不可用，需要认证）
export function getAssemblyConversation(conversationId) {
  return request({
    url: `/api/assembly-history/conversations/${conversationId}`,
    method: 'get'
  })
}

// 更新汇编对话记录（暂时不可用，需要认证）
export function updateAssemblyConversation(conversationId, data) {
  return request({
    url: `/api/assembly-history/conversations/${conversationId}`,
    method: 'put',
    data: data
  })
}

// 删除汇编对话记录
export function deleteAssemblyConversation(conversationId) {
  return request({
    url: `/api/assembly-history/conversations/${conversationId}`,
    method: 'delete'
  })
}

// 生成新的会话ID
export function generateSessionId() {
  return request({
    url: '/api/assembly-history/sessions/generate',
    method: 'post'
  })
}

// 批量删除汇编对话记录
export function batchDeleteAssemblyConversations(conversationIds) {
  const deletePromises = conversationIds.map(id => deleteAssemblyConversation(id))
  return Promise.all(deletePromises)
}

// 导出汇编历史数据
export function exportAssemblyHistory(params = {}) {
  const exportParams = {
    page_size: 1000, // 导出时获取更多数据
    ...params
  }
  return request({
    url: '/api/assembly-history/conversations',
    method: 'get',
    params: exportParams
  })
}

// 根据会话ID获取对话记录
export function getConversationsBySession(sessionId) {
  return request({
    url: '/api/assembly-history/conversations',
    method: 'get',
    params: {
      session_id: sessionId
    }
  })
}

// 获取汇编类型统计
export function getAssemblyTypeStats() {
  return request({
    url: '/api/assembly-history/conversations',
    method: 'get',
    params: {
      page_size: 1000
    }
  }).then(response => {
    if (response.code === 200 && response.data.conversations) {
      const stats = {}
      response.data.conversations.forEach(conv => {
        const type = conv.assembly_type
        stats[type] = (stats[type] || 0) + 1
      })
      return {
        code: 200,
        data: stats
      }
    }
    return response
  })
}
