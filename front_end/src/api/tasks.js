import request from '@/utils/request'

/**
 * 任务管理相关API
 */

// 获取所有任务列表
export function getTasks() {
  return request({
    url: '/tasks',
    method: 'get'
  })
}

// 获取单个任务状态
export function getTaskStatus(taskId) {
  return request({
    url: `/task/${taskId}`,
    method: 'get'
  })
}

// 取消任务
export function cancelTask(taskId) {
  return request({
    url: `/task/${taskId}`,
    method: 'delete'
  })
}

// SSE连接任务进度流 (调试模式，不需要token)
export function createTasksSSE() {
  const baseURL = process.env.VUE_APP_BASE_API || '/dev-api'
  return new EventSource(`${baseURL}/tasks/stream`)
}
