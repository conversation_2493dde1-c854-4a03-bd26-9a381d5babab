import request from '@/utils/request'

// 用户登录
export function login(data) {
  return request({
    url: '/auth/token',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: new URLSearchParams({
      username: data.username,
      password: data.password
    })
  })
}

// 用户注册
export function register(data) {
  return request({
    url: '/auth/register',
    method: 'post',
    data: {
      user_name: data.username,
      password: data.password,
      email: data.email
    }
  })
}

// 刷新token
export function refreshToken() {
  return request({
    url: '/auth/refresh',
    method: 'post'
  })
}

// 获取用户信息 - 使用simple-auth接口
export function getUserInfo() {
  return request({
    url: '/api/simple-auth/user-info',
    method: 'get'
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/auth/user/password',
    method: 'put',
    data: {
      old_password: data.oldPassword,
      new_password: data.newPassword
    }
  })
}

// 更新用户信息
export function updateUserInfo(data) {
  return request({
    url: '/auth/user/info',
    method: 'put',
    data: {
      nick_name: data.nickName,
      phone: data.phone,
      email: data.email
    }
  })
}

// 用户登出 - 使用simple-auth接口
export function logout() {
  return request({
    url: '/api/simple-auth/logout',
    method: 'post'
  })
}

// 检查用户名是否存在
export function checkUsername(username) {
  return request({
    url: '/auth/check/username',
    method: 'get',
    params: { username }
  })
}

// 检查邮箱是否存在
export function checkEmail(email) {
  return request({
    url: '/auth/check/email',
    method: 'get',
    params: { email }
  })
}

// 发送验证码（如果需要邮箱验证）
export function sendVerificationCode(email) {
  return request({
    url: '/auth/verification/send',
    method: 'post',
    data: { email }
  })
}

// 验证邮箱验证码
export function verifyCode(data) {
  return request({
    url: '/auth/verification/verify',
    method: 'post',
    data: {
      email: data.email,
      code: data.code
    }
  })
}

// 重置密码
export function resetPassword(data) {
  return request({
    url: '/auth/password/reset',
    method: 'post',
    data: {
      email: data.email,
      code: data.code,
      new_password: data.newPassword
    }
  })
}



// 获取部门列表
export function getDepartments() {
  return request({
    url: '/auth/departments',
    method: 'get'
  })
}

// 创建权限申请
export function createPermissionRequest(data) {
  return request({
    url: '/auth/permission-requests',
    method: 'post',
    data
  })
}

// 获取我的权限申请
export function getMyPermissionRequests(params) {
  return request({
    url: '/auth/permission-requests/my',
    method: 'get',
    params
  })
}

// 获取待审核的权限申请（管理员）
export function getPermissionRequestsForReview(params) {
  return request({
    url: '/auth/permission-requests/review',
    method: 'get',
    params
  })
}

// 审核权限申请
export function reviewPermissionRequest(id, data) {
  return request({
    url: `/auth/permission-requests/${id}/review`,
    method: 'post',
    data
  })
}

// 撤回权限申请
export function withdrawPermissionRequest(id) {
  return request({
    url: `/auth/permission-requests/${id}/withdraw`,
    method: 'post'
  })
}

// 删除权限申请
export function deletePermissionRequest(id) {
  return request({
    url: `/auth/permission-requests/${id}`,
    method: 'delete'
  })
}

// 检查文档权限（使用auth路由）
export function checkDocumentPermissionAuth(documentId, permissionType) {
  return request({
    url: `/auth/documents/${documentId}/permissions/${permissionType}`,
    method: 'get'
  })
}

// 获取用户权限列表 - 使用simple-auth接口
export function getUserPermissions() {
  return request({
    url: '/api/simple-auth/user-info',
    method: 'get'
  })
}

// 获取文档权限（来自es-search）
export function getDocPermission(document_id, type) {
  // ========== API 调用日志 ==========
  const apiCallId = Math.random().toString(36).substr(2, 9);
  const requestData = {
    doc_id: document_id,
    permission_type: type,
  };

  // console.log('🌐 ===== API 调用: getDocPermission =====');
  // console.log('🆔 API调用ID:', apiCallId);
  // console.log('📅 调用时间:', new Date().toISOString());
  // console.log('🔑 document_id:', document_id);
  // console.log('📝 document_id 类型:', typeof document_id);
  // console.log('📝 document_id 是否为空:', !document_id);
  // console.log('🔐 type:', type);
  // console.log('📦 请求数据:', JSON.stringify(requestData, null, 2));
  // console.log('🚀 发送请求到: /api/permissions/check-document-access');

  const requestPromise = request({
    url: `/api/permissions/check-document-access`,
    method: 'post',
    data: requestData,
  });

  // 添加响应日志
  return requestPromise.then(response => {
    // console.log('📨 ===== API 响应: getDocPermission =====');
    // console.log('🆔 API调用ID:', apiCallId);
    // console.log('📅 响应时间:', new Date().toISOString());
    // console.log('✅ 响应成功');
    // console.log('📦 响应数据:', JSON.stringify(response, null, 2));
    // console.log('🏁 ===== API 响应结束 =====');
    // console.log('');
    return response;
  }).catch(error => {
    // console.error('💥 ===== API 错误: getDocPermission =====');
    // console.error('🆔 API调用ID:', apiCallId);
    // console.error('📅 错误时间:', new Date().toISOString());
    // console.error('❌ 请求失败');
    console.error('权限检查API错误:', error.message);
    // console.error('📦 错误详情:', error);
    // console.error('🏁 ===== API 错误结束 =====');
    // console.error('');
    throw error;
  });
}

// 提交权限申请（来自es-search）
export function reqApply(data) {
  return request({
    url: '/api/permissions/request',
    method: 'post',
    data,
  })
}
