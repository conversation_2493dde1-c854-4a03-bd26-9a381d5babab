import requestAI from '@/utils/request_ai'

export function reqAssemblyQuery(queryParams) {
  return requestAI.post('/explain_query', queryParams)
}

// 异步汇编任务相关API
export function createAssemblyTask(queryParams) {
  return requestAI.post('/assembly_task', queryParams)
}

export function getAssemblyTaskResult(taskId) {
  return requestAI.get(`/assembly_task/${taskId}`)
}

export function getUserAssemblyTasks() {
  return requestAI.get('/assembly_tasks')
}

export function cancelAssemblyTask(taskId) {
  return requestAI.delete(`/task/${taskId}`)
}

// 获取最新任务参数
export function getLatestTaskParams() {
  return requestAI.get('/assembly_task/latest_params')
}
export function reqKeyword() {
  return requestAI.post('/assembly_keywords')
}

export function reqPromptNames() {
  return requestAI.post('/prompt_names')
}

export function getPromtDetail(name) {
  return requestAI.post(
    '/get_prompt',
    {},
    {
      params: {
        prompt_name: name,
      },
    }
  )
}
