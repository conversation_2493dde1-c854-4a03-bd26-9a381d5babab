import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '../store'
import { isModuleLoadError, handleModuleLoadError } from '@/utils/module-error-handler'
Vue.use(VueRouter)
const router = new VueRouter({
  mode: 'history',
  scrollBehavior(to, from, savedPosition) {
    return { x: 0, y: 0 }
  },
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/auth/Login.vue'),
      meta: {
        title: '用户登录',
        requiresAuth: false,
        hideForAuth: true // 已登录用户隐藏
      }
    },

    {
      path: '/register',
      name: 'register',
      component: () => import('@/views/auth/Register.vue'),
      meta: {
        title: '用户注册',
        requiresAuth: false,
        hideForAuth: true // 已登录用户隐藏
      }
    },
    {
      path: '/',
      name: 'home',
      component: () => import('@/views/index.vue'),
      meta: {
        title: '首页',
        requiresAuth: true
      }
    },
    {
      path: '/search',
      name: 'search',
      component: () => import('@/views/search.vue'),
      meta: {
        title: '搜索结果',
        requiresAuth: true
      }
    },
    {
      path: '/info',
      name: 'info',
      component: () => import('@/views/projectInfo.vue'),
      meta: {
        title: '项目信息',
        requiresAuth: true
      }
    },
    {
      path: '/desc',
      name: 'desc',
      component: () => import('@/views/projectInfoDesc.vue'),
      meta: {
        title: '项目详情',
        requiresAuth: true
      }
    },
    {
      path: '/preview',
      name: 'preview',
      component: () => import('@/views/preview.vue'),
      meta: {
        title: '文档预览',
        requiresAuth: true
      }
    },
    {
      path: '/assembly',
      name: 'assembly',
      component: () => import('@/views/assembly.vue'),
      meta: {
        title: '组装查询',
        requiresAuth: true
      }
    },
    {
      path: '/permission-request',
      name: 'permissionRequest',
      component: () => import('@/views/auth/PermissionRequest.vue'),
      meta: {
        title: '权限申请',
        requiresAuth: true
      }
    },
    {
      path: '/permission-approval',
      name: 'permissionApproval',
      component: () => import('@/views/auth/PermissionApproval.vue'),
      meta: {
        title: '权限审核',
        requiresAuth: true,
        requiresAdmin: true
      }
    },

    {
      path: '/permissions',
      name: 'permissions',
      component: () => import('@/views/permissions.vue'),
      meta: {
        title: '权限管理',
        requiresAuth: true
      }
    },
    {
      path: '/tasks',
      name: 'tasks',
      component: () => import('@/views/tasks.vue'),
      meta: {
        title: '任务管理',
        requiresAuth: false  // 调试模式：不需要认证
      }
    },
    {
      path: '/test-module-error',
      name: 'testModuleError',
      component: () => import('@/views/test-module-error.vue'),
      meta: {
        title: '模块错误测试',
        requiresAuth: false  // 测试页面：不需要认证
      }
    },





    {
      path: '*',
      name: 'notFound',
      redirect: '/',
    },
  ],
})
// 添加一个标志来跟踪初始化状态
let isAppInitialized = false

// Token验证函数
async function validateToken(token) {
  if (!token) return null

  try {
    // 尝试调用用户信息API来验证token
    const response = await fetch('/dev-api/auth/users/me', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const userInfo = await response.json()
      return userInfo
    } else {
      // console.log('Token验证失败:', response.status)
      return null
    }
  } catch (error) {
    // console.error('Token验证异常:', error)
    return null
  }
}

// 添加路由错误处理，解决动态导入模块失败问题
router.onError((error, to) => {
  console.error('🚨 路由错误:', error)

  // 使用统一的模块错误处理器
  if (isModuleLoadError(error)) {
    console.warn('🔄 路由器检测到模块加载失败，使用统一处理器')

    // 安全地获取目标路径
    const targetPath = to && to.fullPath ? to.fullPath : window.location.pathname
    console.warn('🎯 目标路径:', targetPath)

    handleModuleLoadError(error, {
      targetPath: targetPath,
      retryDelay: 500
    })
    return
  }

  // 其他未知错误，记录并继续
  console.error('❌ 未处理的路由错误:', error)
})

router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 文档搜索系统`
  }

  // 如果应用还没有初始化完成，等待一下
  if (!isAppInitialized) {
    // console.log('应用还在初始化中，继续路由...')
  }

  // 检查路由是否需要认证
  const requiresAuth = to.meta?.requiresAuth !== false
  const hideForAuth = to.meta?.hideForAuth === true

  // 如果页面明确设置了不需要认证，直接通过
  if (requiresAuth === false) {
    // console.log('页面不需要认证，直接通过:', to.path)
    next()
    return
  }

  // 获取用户登录状态
  const hasToken = store.getters['user/hasToken']
  const userInfo = store.getters['user/userInfo']

  // 简化的登录状态检查
  let isLoggedIn = false

  if (hasToken && userInfo && (userInfo.user_id || userInfo.id)) {
    // 有token且有用户信息，认为是登录状态
    isLoggedIn = true
  } else if (hasToken) {
    // 有token但没有store中的用户信息，检查localStorage
    try {
      const localUser = localStorage.getItem('currentUser')
      if (localUser) {
        const parsedUser = JSON.parse(localUser)
        if (parsedUser && parsedUser.user_id) {
          isLoggedIn = true
          // 更新store中的用户信息
          store.commit('user/SET_USER_INFO', parsedUser)
          store.commit('user/SET_LOGIN_STATUS', true)
        }
      }
    } catch (error) {
      // console.error('解析用户信息失败:', error)
      isLoggedIn = false
    }
  }

  // console.log('🛡️ 路由守卫检查:', {
    // to: to.path,
    // from: from.path,
    // isLoggedIn,
    // hasToken,
    // username: userInfo?.username || userInfo?.user_name,
    // userId: userInfo?.user_id || userInfo?.id,
    // requiresAuth,
    // hideForAuth,
    // isAppInitialized,
    // isInIframe: window.self !== window.top,
    // hasUrlToken: !!new URLSearchParams(window.location.search).get('token')
  // })

  // 已登录用户访问登录/注册页面，重定向到首页
  if (isLoggedIn && hideForAuth) {
    // console.log('已登录用户访问登录页，重定向到首页')
    next('/')
    return
  }

  // 未登录用户访问需要认证的页面，重定向到登录页
  if (!isLoggedIn && requiresAuth) {
    // console.log('未登录用户访问受保护页面，重定向到登录页')
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 兼容原有的email设置
  const email = store.state.email || store.getters['user/userEmail']
  if (email) {
    window.email = email
  } else {
    window.email = '保密文档 请勿外传'
  }

   next()
})

// 导出设置初始化状态的函数
export function setAppInitialized() {
  isAppInitialized = true
  // console.log('应用初始化完成')
}

export default router
