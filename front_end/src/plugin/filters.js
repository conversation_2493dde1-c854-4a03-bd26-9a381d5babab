//全局过滤器
//日期格式化
import dayjs from 'dayjs'
export function time(val) {
  // return dateFmt(new Date(val));
  return dayjs(val) > dayjs()
    ? dayjs(val.slice(0, 4)).format('YYYY-MM-DD HH:mm:ss')
    : dayjs(val).format('YYYY-MM-DD HH:mm:ss')
}
export function timeFmt(val, fmt) {
  return dateFmt(new Date(val), fmt)
}
export function dateFmt(date, fmt) {
  if (!date) {
    date = new Date()
  }
  // date = new Date()
  // console.log(typeof date)
  if (typeof date == 'string') {
    console.log('时间:' + date)
    date = new Date(date.replace(/-/g, '/'))
  }
  if (!fmt) {
    fmt = 'yyyy-MM-dd hh:mm:ss'
  }
  var o = {
    'M+': date.getMonth() + 1, //月份
    'd+': date.getDate(), //日
    'h+': date.getHours(), //小时
    'm+': date.getMinutes(), //分
    's+': date.getSeconds(), //秒
    'q+': Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds(), //毫秒
  }
  console.log(fmt)
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    )
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      )
    }
  }
  return fmt
}
export function strToData(data) {
  if (typeof data == 'string') {
    return new Date(data.replace(/-/g, '/'))
  }
  return data
}
