<template>
  <div id="app">
    <router-view />
    <!-- 存储兼容性检查组件 -->
    <StorageCompatibility />
  </div>
</template>

<script>
import localforage from 'localforage'
import StorageCompatibility from '@/components/StorageCompatibility.vue'

export default {
  components: {
    StorageCompatibility
  },
  name: 'App',
  async mounted() {
    // 版本标识 - 用于调试
    localforage.config({
      name: 'assembly',
      storeName: 'assemblyHistory',
      description: '用于存储汇编搜索的历史记录',
    })

    // 设置iframe通信监听器
    this.setupIframeMessageListener()

    // 设置自定义事件监听器
    this.setupCustomEventListeners()

    // 处理URL参数中的Token（服务A集成）
    await this.handleServiceAToken()

    // 初始化认证状态
    await this.$store.dispatch('user/initAuth')

    // 设置定期Token检查
    this.setupTokenValidityCheck()
  },
  methods: {
    async handleServiceAToken() {
      try {
        const urlParams = new URLSearchParams(window.location.search)
        const token = urlParams.get('token')
        const redirect = urlParams.get('redirect') || '/'
        const sessionId = urlParams.get('session_id')

        if (token) {
          console.log('检测到URL中的Token，开始处理服务A认证...')

          // 检查是否是新会话
          if (sessionId) {
            const lastSessionId = localStorage.getItem('lastSessionId')

            if (lastSessionId && lastSessionId !== sessionId) {
              console.log('检测到新会话，清理旧会话数据')
              await this.clearLocalSession()
            }

            // 保存新会话ID
            localStorage.setItem('lastSessionId', sessionId)
          }

          // 验证Token
          const response = await fetch('/api-s/api/simple-auth/verify-token', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })

          if (response.ok) {
            const result = await response.json()
            console.log('Token验证成功:', result)

            // 存储Token和用户信息
            localStorage.setItem('authToken', token)
            localStorage.setItem('loginSource', 'service_a')
            localStorage.setItem('currentUser', JSON.stringify(result.user_info))

            // 更新Vuex状态
            this.$store.commit('user/SET_TOKEN', token)
            this.$store.commit('user/SET_USER_INFO', result.user_info)
            this.$store.commit('user/SET_LOGIN_STATUS', true)

            // 显示成功消息
            console.log('欢迎使用系统！')

            // 清理URL参数并重定向
            const newUrl = window.location.origin + window.location.pathname + redirect
            window.history.replaceState({}, '', newUrl)

            // 如果不是根路径，进行路由跳转
            if (redirect !== '/' && redirect !== window.location.pathname) {
              this.$router.push(redirect)
            }

            console.log('服务A用户认证完成，用户:', result.user_info.username)

            // 通知服务A已准备就绪
            if (sessionId) {
              // 获取服务A的地址
              let serviceAUrl = 'http://***********:8081'; // 默认值
              if (typeof window !== 'undefined' && window.BUILD_CONFIG) {
                serviceAUrl = window.BUILD_CONFIG.SERVICE_A_URL;
              }

              window.parent.postMessage({
                type: 'SERVICE_B_READY',
                sessionId: sessionId,
                timestamp: Date.now()
              }, serviceAUrl)
            }
          } else {
            console.error('Token验证失败:', response.status, response.statusText)
            // Token无效，清理URL参数
            const newUrl = window.location.origin + window.location.pathname
            window.history.replaceState({}, '', newUrl)
          }
        }
      } catch (error) {
        console.error('处理服务A Token时出错:', error)
        // 出错时清理URL参数
        const newUrl = window.location.origin + window.location.pathname
        window.history.replaceState({}, '', newUrl)
      }
    },

    // 设置iframe通信监听器
    setupIframeMessageListener() {
      window.addEventListener('message', (event) => {
        // 过滤掉不必要的位置检测消息
        if (event.data && (event.data.type === 'iframe-pos' || event.data.type === 'get-iframe-pos')) {
          // 静默处理位置检测消息，不输出日志
          return
        }

        // 过滤PDF查看器消息，避免重复处理
        if (event.data && event.data.source === 'pdf-viewer') {
          console.log('📨 App: PDF查看器消息已由iframe-security.js处理，跳过')
          return
        }

        // 详细日志其他重要消息
        console.log('📨 App: 收到消息详情:')
        console.log('  - 数据:', event.data)
        console.log('  - 来源:', event.origin)
        console.log('  - 类型检查:', {
          hasData: !!event.data,
          hasAction: event.data?.action,
          hasType: event.data?.type,
          hasSource: event.data?.source
        })

        // 特殊处理来自iframe预览页面的返回消息
        if (event.data && event.data.type === 'IFRAME_GO_BACK' && event.data.source === 'preview-page') {
          console.log('✅ App: 匹配预览页面返回消息格式!')
          console.log('📨 App: 收到iframe预览页面返回消息:', event.data)
          this.handleIframeGoBack(event.data)
          return
        }

        // 取消origin限制，允许所有来源的消息
        console.log('🔓 App: 允许来自任意源的消息:', event.origin)

        // 原来的限制代码（已注释）
        // const allowedOrigins = this.getAllowedOrigins();
        // if (!allowedOrigins.includes(event.origin)) {
        //   console.warn('收到来自未授权域名的消息:', event.origin, '允许的域名:', allowedOrigins);
        //   return
        // }

        switch (event.data.type) {
          case 'CLEAR_SESSION':
            this.handleClearSession(event.data)
            break
        }
      })

      // 监听localStorage变化（同域名下其他标签页的变化）
      window.addEventListener('storage', (event) => {
        if (event.key === 'authToken' && event.newValue === null) {
          console.log('检测到Token被清除，清理当前会话')
          this.clearLocalSession()
        }
      })
    },

    // 获取允许的域名列表
    getAllowedOrigins() {
      const allowedOrigins = [];

      // 优先使用构建时配置
      if (typeof window !== 'undefined' && window.BUILD_CONFIG) {
        allowedOrigins.push(window.BUILD_CONFIG.SERVICE_A_URL);
        // 也允许当前页面的origin
        allowedOrigins.push(window.location.origin);
      } else {
        // 降级方案：使用默认地址
        const hostname = window.location.hostname;
        const protocol = window.location.protocol;

        allowedOrigins.push(
          window.location.origin,
          `${protocol}//${hostname}:8081`,  // 服务A默认端口
          `${protocol}//${hostname}:8080`,
          `${protocol}//${hostname}:2222`,
          `${protocol}//${hostname}:18888`,
          `${protocol}//${hostname}:3000`,
          `${protocol}//${hostname}`,
          'http://***********:8081',  // 开发环境默认
          'http://localhost:8081'
        );
      }

      return [...new Set(allowedOrigins)]; // 去重
    },

    // 设置自定义事件监听器
    setupCustomEventListeners() {
      // 监听PDF查看器返回事件
      window.addEventListener('pdf-viewer-go-back', (event) => {
        console.log('📨 App: 收到PDF查看器返回事件:', event.detail)
        this.handlePdfViewerGoBack(event.detail)
      })

      // 监听预览页面返回事件
      window.addEventListener('preview-page-go-back', (event) => {
        console.log('📨 App: 收到预览页面返回事件:', event.detail)
        this.handlePreviewPageGoBack(event.detail)
      })

      // 监听路由变更事件
      window.addEventListener('iframe-route-change', (event) => {
        console.log('📨 App: 收到路由变更事件:', event.detail)
        this.handleIframeRouteChange(event.detail)
      })
    },

    // 处理PDF查看器返回请求
    handlePdfViewerGoBack(data) {
      console.log('📨 App: 处理PDF查看器返回请求:', data)

      // 防止无限递归：检查事件源
      if (data && data.source === 'app-message-handler') {
        console.log('⚠️ App: 检测到循环调用，跳过处理')
        return
      }

      // 如果当前在预览页面，直接处理返回
      if (this.$route && this.$route.path === '/preview') {
        console.log('📨 App: 当前在预览页面，直接执行返回')
        this.executeGoBack()
      } else {
        // 不在预览页面时，执行路由跳转到搜索页面
        console.log('🔄 App: 不在预览页面，跳转到搜索页面')
        this.$router.push('/search').catch(err => {
          console.warn('路由跳转警告:', err)
        })
      }
    },

    // 处理iframe预览页面返回请求
    handleIframeGoBack(data) {
      console.log('📨 App: 处理iframe预览页面返回请求:', data)

      // 清理预览相关数据（但保留搜索参数）
      localStorage.removeItem('url')
      localStorage.removeItem('docId')
      localStorage.removeItem('permissionChecked')
      localStorage.removeItem('permissionResult')

      console.log('🧹 App: 已清理预览相关数据，保留搜索参数')

      // 跳转到目标路由
      const targetRoute = data.targetRoute || '/search'
      console.log('🔄 App: 跳转到:', targetRoute)

      try {
        this.$router.push(targetRoute)
      } catch (error) {
        console.error('❌ App: 跳转失败，降级到搜索页面', error)
        this.$router.push('/search')
      }
    },

    // 处理预览页面返回请求（通过自定义事件）
    handlePreviewPageGoBack(data) {
      console.log('📨 App: 处理预览页面返回请求（自定义事件）:', data)

      // 执行路由跳转到目标路由
      const targetRoute = data.targetRoute || '/search'
      console.log('🔄 App: 跳转到目标路由:', targetRoute)

      try {
        this.$router.push(targetRoute)
      } catch (error) {
        console.error('❌ App: 跳转失败，降级到搜索页面', error)
        this.$router.push('/search')
      }

      console.log('📨 App: 预览页面返回处理完成')
    },

    // 处理iframe路由变更请求（通过自定义事件）
    handleIframeRouteChange(data) {
      console.log('📨 App: 处理iframe路由变更请求（自定义事件）:', data)

      // 执行路由跳转
      const targetRoute = data.route || '/search'
      console.log('🔄 App: 跳转到目标路由:', targetRoute)

      try {
        this.$router.push(targetRoute)
      } catch (error) {
        console.error('❌ App: 跳转失败，降级到搜索页面', error)
        this.$router.push('/search')
      }

      console.log('📨 App: iframe路由变更处理完成')
    },

    // 执行返回操作
    executeGoBack() {
      console.log('🔙 App: 执行返回操作')

      // 清理预览相关数据（但保留搜索参数）
      localStorage.removeItem('url')
      localStorage.removeItem('docId')
      localStorage.removeItem('permissionChecked')
      localStorage.removeItem('permissionResult')

      console.log('🧹 App: 已清理预览相关数据，保留搜索参数')

      // 检查是否有搜索参数，决定返回方式
      const queryParams = localStorage.getItem('queryParams')

      if (queryParams) {
        try {
          const params = JSON.parse(queryParams)
          if (params.keyword && params.type) {
            console.log('📚 App: 检测到搜索参数，直接跳转到搜索页面以恢复搜索结果')
            this.$router.push('/search')
            return
          }
        } catch (e) {
          console.warn('解析搜索参数失败:', e)
        }
      }

      // 没有搜索参数时使用浏览器历史返回
      try {
        if (window.history.length > 1) {
          console.log('📚 App: 使用浏览器历史返回')
          this.$router.go(-1)
        } else {
          console.log('📚 App: 无历史记录，跳转到搜索页面')
          this.$router.push('/search')
        }
      } catch (error) {
        console.error('❌ App: 返回失败，降级到搜索页面', error)
        this.$router.push('/search')
      }
    },

    // 处理会话清理请求
    async handleClearSession(data) {
      console.log('收到会话清理请求:', data.reason)

      try {
        // 清理本地会话
        await this.clearLocalSession()

        // 通知服务A清理完成
        // 获取服务A的地址
        let serviceAUrl = 'http://***********:8081'; // 默认值
        if (typeof window !== 'undefined' && window.BUILD_CONFIG) {
          serviceAUrl = window.BUILD_CONFIG.SERVICE_A_URL;
        }

        window.parent.postMessage({
          type: 'SESSION_CLEARED',
          timestamp: Date.now()
        }, serviceAUrl)

      } catch (error) {
        console.error('清理会话失败:', error)
      }
    },

    // 清理本地会话
    async clearLocalSession() {
      console.log('清理本地会话数据')

      try {
        // 导入安全存储工具
        const { safeLocalStorage, safeSessionStorage } = await import('@/utils/storage')

        // 清理所有用户相关的localStorage数据
        const userDataKeys = [
          'authToken',
          'loginSource',
          'currentUser',
          'lastSessionId',
          'queryParams',  // 搜索参数
          'email',
          'userName',
          'access_token',
          'refresh_token',
          'user_info'
        ]

        userDataKeys.forEach(key => {
          safeLocalStorage.removeItem(key)
        })

        // 清理sessionStorage数据
        safeSessionStorage.clear()

        // 清理Vuex状态
        this.$store.commit('user/CLEAR_AUTH_DATA')

        // 清理根级别的email状态
        this.$store.commit('SET_EMAIL', '')

        // 重定向到首页
        if (this.$route.path !== '/') {
          this.$router.push('/')
        }

        console.log('本地会话已清理，包括搜索参数和用户数据')
      } catch (error) {
        console.error('清理会话时出错:', error)

        // 降级处理：直接使用原生API
        try {
          const userDataKeys = [
            'authToken', 'loginSource', 'currentUser', 'lastSessionId',
            'queryParams', 'email', 'userName', 'access_token', 'refresh_token', 'user_info'
          ]

          userDataKeys.forEach(key => {
            try {
              localStorage.removeItem(key)
            } catch (e) {
              console.warn(`清理localStorage[${key}]失败:`, e.message)
            }
          })

          try {
            sessionStorage.clear()
          } catch (e) {
            console.warn('清理sessionStorage失败:', e.message)
          }

          this.$store.commit('user/CLEAR_AUTH_DATA')
          this.$store.commit('SET_EMAIL', '')

          if (this.$route.path !== '/') {
            this.$router.push('/')
          }

          console.log('使用降级方案清理会话完成')
        } catch (fallbackError) {
          console.error('降级清理也失败了:', fallbackError)
        }
      }
    },

    // 设置定期Token检查
    setupTokenValidityCheck() {
      // 每5分钟检查一次Token有效性
      setInterval(async () => {
        await this.checkTokenValidity()
      }, 5 * 60 * 1000)

      // 页面获得焦点时也检查一次
      window.addEventListener('focus', async () => {
        await this.checkTokenValidity()
      })
    },

    // 检查Token有效性
    async checkTokenValidity() {
      const token = localStorage.getItem('authToken')
      const loginSource = localStorage.getItem('loginSource')

      // 只检查服务A的Token
      if (!token || loginSource !== 'service_a') {
        return
      }

      try {
        const response = await fetch('/api-s/api/simple-auth/verify-token', {
          method: 'GET',
          headers: { 'Authorization': `Bearer ${token}` }
        })

        if (!response.ok) {
          console.log('Token已失效，清理会话')
          await this.clearLocalSession()
        }
      } catch (error) {
        console.error('Token验证失败:', error)
        await this.clearLocalSession()
      }
    }
  }
}
</script>
