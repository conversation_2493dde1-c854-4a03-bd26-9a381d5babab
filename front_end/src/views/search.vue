<template>
  <div>
    <div>
      <!-- 权限申请 -->
      <el-dialog :visible.sync="applyForm.show" title="提示">
        <p class="font-bold">文档：{{ applyForm.currentDoc.project_name }}</p>

        <p>
          如需查看或下载此文档，请先提交申请，管理员审核通过之后方可查看或下载
        </p>
        <br />
        <el-form label-position="left" class="apply-form">
          <el-form-item label="申请类型">
            <el-radio-group v-model="applyForm.permission_type">
              <el-radio label="read">查看</el-radio>
              <el-radio label="download">下载</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="申请原因">
            <el-input v-model="applyForm.reason" autocomplete="off" type="textarea" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="applyForm.show = false">取消</el-button>
            <el-button type="primary" @click="apply"> 提交申请 </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
    <div class="search-container">
      <div class="top">
        <div class="top-content">
          <div class="search-header">
            <h1 class="search-title" @click="$router.push('/')">档案检索</h1>
            <div class="search-input-group">
              <el-input
                placeholder="请输入关键字"
                v-model="queryParams.keyword"
                class="search-input"
                @keydown.enter.native="toSearch"
                size="large"
              >
                <el-select
                  slot="prepend"
                  v-model="queryParams.type"
                  placeholder="请选择"
                  style="width: 112px"
                >
                  <el-option label="综合搜索" value="1"></el-option>
                  <el-option label="精确搜索" value="3"></el-option>
                </el-select>
                <template slot="append">
                  <div class="button-group">
                    <el-button
                      icon="el-icon-search"
                      @click="toSearch"
                      class="search-btn"
                      :loading="searchInProgress"
                      :disabled="searchInProgress"
                    >
                    </el-button>
                    <el-button
                      icon="el-icon-s-order"
                      @click="goToTasks"
                      class="tasks-btn"
                      title="任务查看"
                    >
                    </el-button>
                  </div>
                </template>
              </el-input>
            </div>
          </div>
        </div>
      </div>
      <div class="placeholder" style="height: 72px"></div>
      <div class="search">
        <div
          class="list-container"
          v-loading="loading.search"
          element-loading-text="搜索中"
          ref="listContainer"
        >
          <div class="list">
            <!-- 数量统计 -->
            <span class="total" v-if="pageNum == 1 && !loading.search"
              >本次共检索出{{ total }}条结果</span
            >
            <!-- 搜索结果 -->
            <el-card shadow="never" v-if="explain.length && pageNum == 1">
              <template #header>
                <span>词条解释</span>
              </template>
              <div class="card-body explain">
                <div class="markdown ai-response-content" v-html="md.render(processHistoryResponse(explain || ''))"></div>
              </div>
            </el-card>
            <!-- 相似搜索 -->
            <el-card
              shadow="never"
              v-else-if="!explain.length && !loading.similar && hits.length == 0"
            >
              <template #header>
                <span>您是否要搜索</span>
              </template>
              <div class="similar-list card-body">
                <el-tag
                  type="success"
                  v-for="(str, index) in similar"
                  :key="index"
                  class="similar-item"
                  @click="handleSimilarClick(str)"
                >
                  {{ str }}
                </el-tag>
              </div>
            </el-card>
            <!-- 列表 -->
            <template v-if="hits.length > 0">
              <div class="res-card" v-for="item in hits" :key="item._id">
                <div class="title" @click="previewOrDownload(item, 'read')">
                  <span v-if="item.highlight['project_name.text']">
                    <span
                      v-for="(item, index) in item.highlight['project_name.text']"
                      :key="index"
                      v-html="item"
                    ></span>
                  </span>
                  <span v-else>{{ item._source.project_name }}</span>
                </div>

                <div class="body">
                  <span class="time">{{ item._source.year }}</span>
                  <span v-if="item.highlight.content">
                    <span
                      v-for="(item, k) in item.highlight.content"
                      :key="k"
                      v-html="item"
                    ></span>
                  </span>
                  <span v-else>{{ item._source.content }}</span>
                </div>
                <div class="footer">
                  <!-- 下载 -->
                  <span class="download" @click="previewOrDownload(item, 'download')">下载</span>
                  <span v-if="item.highlight.action">
                    <span
                      v-for="(item, k) in item.highlight.action"
                      :key="k"
                      v-html="item"
                    ></span>
                  </span>
                  <span v-else>{{ item._source.action }}</span>
                  <!-- 文档类别 -->
                  <span v-for="type in item._source.metadata?.doc_type || []" :key="type">{{
                    type
                  }}</span>
                </div>
              </div>
            </template>

            <template v-else-if="!loading.search && !hits.length">
              <el-empty description="无搜索结果" class="empty"></el-empty>
            </template>
            <el-pagination
              background
              layout="prev, pager, next, total"
              :page-size="pageSize"
              :total="total"
              :current-page="pageNum"
              @current-change="handlePageChange"
              hide-on-single-page
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限申请对话框 -->
    <PermissionRequestDialog
      :visible.sync="permissionDialog.visible"
      :document-info="permissionDialog.documentInfo"
      @success="onPermissionRequestSuccess"
    />
  </div>
</template>

<script>
import PermissionRequestDialog from '@/components/PermissionRequestDialog.vue'
import { search, similarQuery, explainQuery } from '@/api/search'
import { checkDocumentPermission } from '@/api/permission'
import { getDocPermission, reqApply } from '@/api/auth'
import { downloadFile } from '@/utils'
import { Message } from 'element-ui'
import MarkdownIt from 'markdown-it'

export default {
  name: 'search',
  components: {
    PermissionRequestDialog
  },
  data() {
    return {
      // Markdown渲染器实例
      md: new MarkdownIt({ html: true }),
      queryParams: {},
      loading: {
        search: true,
        similar: true,
        explain: true,
      },
      hits: [],
      similar: [],
      explain: '',
      pageNum: 1,
      pageSize: 10,
      total: 0,
      // 权限申请对话框状态
      permissionDialog: {
        visible: false,
        documentInfo: {}
      },
      applyForm: {
        show: false,
        currentDoc: {},
        reason: '',
        permission_type: 'read',
      },
      // 防重复调用的处理队列
      processingCalls: new Set(),

      // 搜索防抖和资源管理
      searchInProgress: false, // 搜索是否正在进行中
      lastSearchTime: 0, // 上次搜索时间
      searchDebounceDelay: 1000, // 搜索防抖延迟（毫秒）
      consecutiveSearchCount: 0, // 连续搜索计数
      searchCooldownTime: 3000, // 搜索冷却时间（毫秒）
    }
  },
  created() {
    // 调试：检查登录状态
    // console.log('🔍 搜索页面登录状态调试:')
    // console.log('登录状态:', this.$store.getters['user/isLoggedIn'])
    // console.log('Token存在:', this.$store.getters['user/hasToken'])
    // console.log('用户信息:', this.$store.getters['user/userInfo'])
    // console.log('localStorage Token:', localStorage.getItem('authToken'))
    // console.log('localStorage 用户:', localStorage.getItem('currentUser'))

    // 检查用户是否已登录，如果未登录则清理queryParams
    const isLoggedIn = this.$store.getters['user/isLoggedIn']
    const hasToken = this.$store.getters['user/hasToken']

    if (!isLoggedIn || !hasToken) {
      // 用户未登录，清理搜索参数
      localStorage.removeItem('queryParams')
      this.queryParams = {}
      this.loading.search = false
      this.$router.replace('/')
      return
    }

    this.queryParams = JSON.parse(localStorage.getItem('queryParams')) || {}

    // 检查搜索参数是否属于当前用户
    const currentUser = this.$store.getters['user/userInfo']
    const currentUserKey = currentUser?.user_id || currentUser?.id || 'anonymous'
    const queryUserKey = this.queryParams._userId

    if (queryUserKey && queryUserKey !== currentUserKey) {
      // 搜索参数属于其他用户，清理并跳转到首页
      console.log('🔄 检测到用户切换，清理上一用户的搜索参数')
      localStorage.removeItem('queryParams')
      this.queryParams = {}
      this.loading.search = false
      this.$router.replace('/')
      return
    }

    const { keyword, type } = this.queryParams
    if (!keyword || !type || !['1', '2'].includes(type)) {
      // 没有有效搜索参数，停止loading并跳转到首页
      this.loading.search = false
      this.$router.replace('/')
    } else {
      // 有有效搜索参数，执行搜索
      this.search()
    }
  },
  activated() {
    this.loading.search = false

    // 页面激活时同步按钮高度，使用多次重试确保成功
    this.$nextTick(() => {
      this.syncButtonHeights()
      // iframe环境可能需要额外时间，添加延迟重试
      setTimeout(() => this.syncButtonHeights(), 100)
      setTimeout(() => this.syncButtonHeights(), 300)
    })
  },

  mounted() {
    // 页面加载完成后同步按钮高度，使用多次重试确保成功
    this.$nextTick(() => {
      this.syncButtonHeights()
      // iframe环境可能需要额外时间，添加延迟重试
      setTimeout(() => this.syncButtonHeights(), 100)
      setTimeout(() => this.syncButtonHeights(), 300)
      setTimeout(() => this.syncButtonHeights(), 500)
    })

    // 监听窗口大小变化
    window.addEventListener('resize', this.syncButtonHeights)

    // 添加全局的思考过程切换函数
    window.toggleThinking = function(element) {
      const container = element.parentElement
      const content = container.querySelector('.ai-thinking-content')
      const toggle = container.querySelector('.ai-thinking-toggle')

      if (content.style.display === 'none' || content.style.display === '') {
        content.style.display = 'block'
        toggle.classList.add('expanded')
      } else {
        content.style.display = 'none'
        toggle.classList.remove('expanded')
      }
    }
  },

  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('resize', this.syncButtonHeights)
  },
  deactivated() {
    // 组件停用时重置loading状态
    this.loading.search = false
    this.loading.similar = false
    this.loading.explain = false
  },
  methods: {
    // 验证按钮对齐 - 现在两个按钮都在同一个容器内，应该自动对齐
    syncButtonHeights() {
      this.$nextTick(() => {
        //console.log('搜索结果页 - 按钮现在都在同一个Element UI容器内，应该自动对齐')
      })
    },

    // 跳转到任务管理页面
    goToTasks() {
      this.$router.push('/tasks')
    },
    handlePageChange(page) {
      this.pageNum = page
      this.search()
    },
    // 搜索
    async search() {
      this.$refs.listContainer?.scrollTo({ top: 0 })
      this.loading.search = true
      const { type, keyword } = this.queryParams
      let query = {}
      if (type === '1') {
        //综合搜索
        query = {
          bool: {
            should: [
              {
                match: {
                  'project_name.text': keyword,
                },
              },
              {
                match: {
                  content: keyword,
                },
              },
            ],
          },
        }
      } else if (type === '2') {
        //  部门搜索
        query = {
          match: {
            depart: keyword,
          },
        }
      }
      try {
        const res = await search({
          query: query,
          highlight: {
            fields: {
              'project_name.text': {},
              content: {},
            },
          },
          _source: {
            excludes: ['embedding'],
          },
          from: (this.pageNum - 1) * this.pageSize,
          size: this.pageSize,
        })

        this.hits = res.hits?.hits || []
        this.loading.search = false
        this.total = res.hits?.total.value
        if (!this.hits.length) {
          this.similarSearch(keyword)
        }
      } catch (error) {
        console.error('搜索请求失败:', error)

        // 根据错误类型提供智能提示
        let errorMessage = '搜索失败，请稍后再试'

        if (error.name === 'AbortError') {
          // 请求被取消，不显示错误提示
          return
        } else if (error.status === 400) {
          errorMessage = '搜索参数有误，请检查搜索条件'
        } else if (error.status === 429) {
          errorMessage = '搜索请求过于频繁，请稍后再试'
        } else if (error.status >= 500) {
          errorMessage = '搜索服务暂时不可用，请稍后重试'
        } else if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
          errorMessage = '网络连接异常，请检查网络后重试'
        } else if (error.message?.includes('timeout')) {
          errorMessage = '搜索请求超时，请简化搜索条件或稍后重试'
        }

        this.$message.error({
          message: errorMessage,
          duration: 4000,
          showClose: true
        })

        this.hits = []
      } finally {
        this.loading.search = false
      }
    },
    // 相似搜索
    async similarSearch() {
      this.loading.similar = true
      const res = await similarQuery({ question: this.queryParams.keyword })
      this.similar = res.source_documents
      this.loading.similar = false
    },
    // 内容解释
    async explainSearch() {
      this.loading.explain = true
      const res = await explainQuery({
        question: this.queryParams.keyword,
        prompt: '名词解释',
        keywords: [],
      })
      if (res.response?.includes('无相关信息')) {
        this.explain = ''
      } else {
        // this.explain = res.response.replace(
        //   new RegExp(res.question),
        //   '<em>$&</em>'
        // )
        this.explain = res.response
      }
      this.loading.explain = false
    },
    toSearch() {
      // 检查用户是否已登录
      const isLoggedIn = this.$store.getters['user/isLoggedIn']
      const hasToken = this.$store.getters['user/hasToken']

      if (!isLoggedIn || !hasToken) {
        // 用户未登录，清理搜索参数并跳转到登录页
        localStorage.removeItem('queryParams')
        this.$router.push('/login')
        return
      }

      // 检查是否有搜索关键字
      if (!this.queryParams.keyword.trim()) {
        return
      }

      // 智能搜索防抖和资源管理
      const currentTime = Date.now()

      // 检查是否正在搜索中
      if (this.searchInProgress) {
        this.showResourceBusyMessage()
        return
      }

      // 检查搜索频率
      const timeSinceLastSearch = currentTime - this.lastSearchTime
      if (timeSinceLastSearch < this.searchDebounceDelay) {
        // 搜索过于频繁，显示友好提示
        this.consecutiveSearchCount++
        this.showSearchThrottleMessage(timeSinceLastSearch)
        return
      }

      // 重置连续搜索计数
      this.consecutiveSearchCount = 0
      this.lastSearchTime = currentTime

      // 更新queryParams并保存到localStorage（关联当前用户）
      const currentUser = this.$store.getters['user/userInfo']
      const userKey = currentUser?.user_id || currentUser?.id || 'anonymous'

      this.queryParams = { ...this.queryParams, _userId: userKey }
      localStorage.setItem('queryParams', JSON.stringify(this.queryParams))

      this.pageNum = 1
      this.similar = []
      this.explain = ''

      // 执行搜索
      this.performSearch()
    },

    // 显示资源繁忙提示
    showResourceBusyMessage() {
      this.$message.warning({
        message: '搜索正在进行中，请稍候...',
        duration: 2000,
        showClose: true
      })
    },

    // 显示搜索频率限制提示
    showSearchThrottleMessage(timeSinceLastSearch) {
      const remainingTime = Math.ceil((this.searchDebounceDelay - timeSinceLastSearch) / 1000)

      let message = ''
      if (this.consecutiveSearchCount <= 2) {
        message = `搜索请求过于频繁，请等待 ${remainingTime} 秒后再试`
      } else if (this.consecutiveSearchCount <= 5) {
        message = `系统资源有限，请等待 ${remainingTime} 秒后再搜索`
      } else {
        message = `为保证服务质量，请等待 ${remainingTime} 秒后再搜索`
      }

      this.$message.warning({
        message: message,
        duration: 3000,
        showClose: true
      })
    },

    // 执行实际的搜索操作
    async performSearch() {
      try {
        this.searchInProgress = true
        await this.search()
        await this.explainSearch()
      } catch (error) {
        console.error('搜索执行失败:', error)
      } finally {
        this.searchInProgress = false
      }
    },
    // 处理文档点击事件（带权限检查）
    async handleDocumentClick(sourceUrl, documentTitle, docId) {
      try {
        // console.log('=== 开始处理文档点击 ===')
        // console.log('原始参数:', { sourceUrl, documentTitle, docId })



        // 构建完整的文档URL
        let fullUrl = encodeURIComponent(sourceUrl)
        fullUrl = import.meta.env.VITE_FILE_BASE_URL + fullUrl
        fullUrl = fullUrl.replace('.json', '.pdf')

        // console.log('构建的完整URL:', fullUrl)
        // console.log('传递的doc_id:', docId)
        // console.log('doc_id类型:', typeof docId, '是否为空:', !docId)

        // 使用权限检查API，同时传递doc_id和document_url
        const response = await checkDocumentPermission({
          doc_id: docId,
          document_url: fullUrl,
          permission_type: 'read'
        })

        // console.log('权限检查响应:', response)

        if (response.code === 200) {
          if (response.permission === true) {
            // 有权限，直接预览
            // console.log('用户有权限，直接预览文档')
            this.preview(sourceUrl, docId)  // 传递 docId 给预览页面
          } else {
            // 没有权限，显示申请对话框
            // console.log('用户没有权限，显示申请对话框')

            // 构造完整的文档信息，优先使用权限检查响应中的信息
            const documentInfo = {
              url: response.url || fullUrl,
              title: documentTitle,
              project_name: documentTitle,
              source_url: sourceUrl
            }

            // 只有当 docId 存在且不为空时才添加
            if (docId && docId.trim()) {
              documentInfo.doc_id = docId.trim()
            }

            // 如果权限检查响应中包含文档信息，使用更完整的信息
            if (response.document_info) {
              Object.assign(documentInfo, {
                id: response.document_info.document_id,  // 数据库主键ID
                doc_id: response.document_info.doc_id || docId,
                title: response.document_info.title || documentTitle,
                project_name: response.document_info.project_name || documentTitle,
                owner_name: response.document_info.owner_name,
                department_name: response.document_info.department_name
              })
            }

            // 确保至少有一个文档标识可用
            if (!documentInfo.doc_id && !documentInfo.id && !documentInfo.url) {
              console.error('警告：没有可用的文档标识，使用原始URL作为备选')
              documentInfo.url = fullUrl
            }

            this.showPermissionRequestDialog(documentInfo)
          }
        } else {
          console.error('权限检查失败:', response.msg)

        }
      } catch (error) {
        console.error('权限检查异常:', error)
        this.$message.error('权限检查失败，请稍后重试')
      }
    },

    // 从URL中提取doc_id的辅助方法
    extractDocIdFromUrl(sourceUrl) {
      // 这里需要根据实际的URL格式来提取doc_id
      // 假设sourceUrl格式类似: "docs/项目名/文档名.json"
      // 我们可以使用文档路径生成doc_id，或者从后端获取映射关系

      // 临时方案：使用路径生成简单的标识
      try {
        // 移除文件扩展名并生成简单的标识
        const pathWithoutExt = sourceUrl.replace(/\.(json|pdf)$/, '')
        // 这里应该调用后端API来获取真正的doc_id
        // 暂时返回一个基于路径的标识
        return btoa(pathWithoutExt).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32)
      } catch (error) {
        console.error('生成doc_id失败:', error)
        return null
      }
    },

    // 带认证的权限检查方法
    async checkDocumentPermissionWithAuth(docId, permissionType) {
      try {
        // 首先检查是否有认证信息
        const token = this.$store.getters['user/token']
        if (!token) {
          // 没有token，提示登录
          this.$message.warning('请先登录后再查看文档')
          this.$router.push('/login')
          return null
        }

        // 调用权限检查API
        return await checkDocumentPermission({
          document_url: docId,
          permission_type: permissionType
        })
      } catch (error) {
        if (error.response?.status === 401) {
          this.$message.warning('登录已过期，请重新登录')
          this.$router.push('/login')
          return null
        }
        throw error
      }
    },

    // 显示权限申请对话框
    showPermissionRequestDialog(documentInfo) {
      // console.log('显示权限申请对话框，文档信息:', documentInfo)
      // console.log('doc_id:', documentInfo.doc_id)
      // console.log('document_id/id:', documentInfo.id)
      // console.log('document_url/url:', documentInfo.url)

      this.permissionDialog.documentInfo = documentInfo
      this.permissionDialog.visible = true
    },

    // 权限申请成功回调
    onPermissionRequestSuccess() {
      this.$message.success('权限申请已提交，请等待管理员审核')
    },

    // 简化的预览方法 - 使用安全的JWT认证下载接口
    preview(url, docId = null) {
      // 构建安全的文件URL
      let sourceUrl = url
      url = encodeURIComponent(url)
      url = import.meta.env.VITE_FILE_BASE_URL + url
      url = url.replace('.json', '.pdf')

      // console.log('🔗 预览URL构建:')
      // console.log('   - 原始路径:', sourceUrl)
      // console.log('   - 编码后URL:', url)
      // console.log('   - docId:', docId)

      // 存储文档URL供预览页面使用
      localStorage.setItem('url', url)

      // 如果有 docId，也存储起来供预览页面使用
      if (docId) {
        localStorage.setItem('docId', docId)
        // console.log('存储 docId 到 localStorage:', docId)

        // 存储权限检查结果，避免预览页面重复检查
        localStorage.setItem('permissionChecked', 'true')
        localStorage.setItem('permissionResult', JSON.stringify({
          hasPermission: true,
          timestamp: Date.now(),
          docId: docId
        }))
        // console.log('存储权限检查结果到 localStorage')
      }

      // 在当前iframe中跳转到预览页面，利用现有认证状态
      this.$router.push({
        path: '/preview',
        query: {
          url: url,
          docId: docId,  // 通过 query 参数传递 docId
          timestamp: Date.now()
        }
      })
    },
    handleSimilarClick(str) {
      this.similar = []
      this.explain = ''
      this.queryParams.keyword = str
      this.toSearch(this.queryParams)
    },

    // 新增：预览或下载方法（来自es-search）
    async previewOrDownload(doc, type) {
      // ========== 详细调试日志开始 ==========
      const timestamp = new Date().toISOString();
      const callId = Math.random().toString(36).substr(2, 9);

      // console.log('🔍 ===== previewOrDownload 调用开始 =====');
      // console.log('📅 调用时间:', timestamp);
      // console.log('🆔 调用ID:', callId);
      // console.log('📄 文档ID:', doc._id);
      // console.log('🔑 doc_id:', doc._source.doc_id);
      // console.log('📝 doc_id 类型:', typeof doc._source.doc_id);
      // console.log('📝 doc_id 是否为空:', !doc._source.doc_id);
      // console.log('🔐 权限类型:', type);
      // console.log('📂 文档源路径:', doc._source.source);
      // console.log('📋 完整文档数据:', JSON.stringify(doc, null, 2));

      // 防止重复调用检查
      const callKey = `${doc._id}_${type}`;
      if (!this.processingCalls) {
        this.processingCalls = new Set();
      }

      if (this.processingCalls.has(callKey)) {
        // console.log('⚠️ 检测到重复调用，已阻止:', callKey);
        // console.log('🔄 当前处理中的调用:', Array.from(this.processingCalls));
        return;
      }

      this.processingCalls.add(callKey);
      // console.log('✅ 添加到处理队列:', callKey);
      // console.log('📊 当前处理队列:', Array.from(this.processingCalls));

      try {


        // 准备调用参数
        const docId = doc._source.doc_id;
        const sourceUrl = doc._source.source;

        // console.log('🚀 准备调用 getDocPermission:');
        // console.log('   - docId:', docId);
        // console.log('   - type:', type);
        // console.log('   - sourceUrl:', sourceUrl);

        // 查看用户是否有阅读的权限
        // console.log('📡 开始权限检查请求...');
        const requestStartTime = Date.now();

        const permisssions = await getDocPermission(doc._source.doc_id, type);

        const requestEndTime = Date.now();
        const requestDuration = requestEndTime - requestStartTime;

        // console.log('📨 权限检查响应完成:');
        // console.log('   - 响应时间:', new Date().toISOString());
        // console.log('   - 请求耗时:', requestDuration + 'ms');
        // console.log('   - 响应结果:', JSON.stringify(permisssions, null, 2));



        if (permisssions.has_permission) {
          // console.log('✅ 用户有权限，开始处理文档访问');

          // 构建文件URL
          let sourceUrl = doc._source.source
          let url = encodeURIComponent(sourceUrl)
          url = import.meta.env.VITE_FILE_BASE_URL + url
          url = url.replace('.json', '.pdf')

          // console.log('🔗 构建的文档URL:', url);
          // console.log('🌐 环境变量:', import.meta.env);

          if (type == 'read') {
            // console.log('👁️ 执行预览操作');
            localStorage.setItem('url', url)
            // console.log('💾 已保存到localStorage:', url);
            // console.log('🪟 准备跳转到预览页面...');
            // 在当前窗口中跳转到预览页面，保持认证状态
            this.$router.push('/preview')
            // console.log('✅ 已跳转到预览页面');
          } else {
            // console.log('⬇️ 执行下载操作');
            // 从原始路径提取文件名，并转换为PDF
            let fileName = sourceUrl.split("/").pop();
            fileName = fileName.replace('.json', '.pdf');
            // console.log('📁 下载文件名:', fileName);
            downloadFile(url, fileName)
            // console.log('✅ 下载已开始');
          }
        } else {
          // console.log('❌ 用户无权限，显示申请对话框');
          this.applyForm.currentDoc = doc._source
          this.applyForm.permission_type = type
          this.applyForm.show = true
          // console.log('📝 申请表单数据:', this.applyForm)
        }

      } catch (error) {
        console.error('previewOrDownload 执行出错:', error.message);
        // console.error('   - 错误堆栈:', error.stack);
        // console.error('   - 完整错误:', error);
        this.$message.error('操作失败，请稍后重试');
      } finally {
        // 清理处理队列
        this.processingCalls.delete(callKey);
        // console.log('🧹 从处理队列移除:', callKey);
        // console.log('📊 剩余处理队列:', Array.from(this.processingCalls));
        // console.log('🏁 ===== previewOrDownload 调用结束 =====');
        // console.log('');
      }
    },

    // 新增：权限申请方法（来自es-search）
    async apply() {
      try {
        if (!this.applyForm.reason.trim()) {
          return this.$message.error('申请原因不能为空')
        }
        const res = await reqApply({
          doc_id: this.applyForm.currentDoc.doc_id,
          permission_type: this.applyForm.permission_type,
          reason: this.applyForm.reason,
        })
        this.$message.success(res.msg)
        this.applyForm.reason = ''
        this.applyForm.show = false
      } catch (error) {
        console.error('权限申请失败:', error.message || error)
        this.$message.error('出错了 请稍后再试')
      }
    },

    // 处理响应内容中的思考过程，将<think>标签转换为折叠HTML
    processHistoryResponse(responseText) {
      if (!responseText || typeof responseText !== 'string') {
        return responseText
      }

      try {
        // 检查是否包含<think>标签
        if (responseText.includes('<think>')) {
          // 提取思考内容和最终答案
          const thinkPattern = /<think>([\s\S]*?)<\/think>/
          const thinkMatch = responseText.match(thinkPattern)

          if (thinkMatch) {
            const thinkContent = thinkMatch[1].trim()
            // 移除<think>标签，保留其他内容作为最终答案
            const finalAnswer = responseText.replace(thinkPattern, '').trim()

            // 构建可折叠的HTML结构
            const collapsibleHtml = `
<div class="ai-thinking-container">
    <div class="ai-thinking-toggle" onclick="toggleThinking(this)">
        <svg class="ai-thinking-icon" width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.786 4.167L2.765 1.259c-.416-.4-.985-.482-1.273-.183-.287.298-.183.864.233 1.264l3.021 2.908c.416.4.986.482 1.273.184.287-.299.183-.865-.233-1.265z" fill="currentColor"></path>
            <path d="M8.197 1.206L5.288 4.208c-.4.413-.484.982-.187 1.27.298.289.864.187 1.265-.227L9.274 2.25c.401-.414.485-.983.187-1.271-.297-.288-.863-.187-1.264.227z" fill="currentColor"></path>
        </svg>
        <span class="ai-thinking-label">思考过程</span>
    </div>
    <div class="ai-thinking-content" style="display: none;">
        <div class="ai-thinking-text">${thinkContent}</div>
    </div>
</div>

${finalAnswer}`

            return collapsibleHtml
          }
        }

        // 如果不包含<think>标签，直接返回原文本
        return responseText

      } catch (error) {
        console.error('处理响应内容时出错:', error)
        return responseText
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.search-container {
  .top {
    padding: 16px;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    background-color: #fff;
    z-index: 999;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    @media (max-width: 768px) {
      padding: 12px 8px;
    }

    .top-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 16px;

      .search-header {
        display: flex;
        align-items: center;
        gap: 20px; // 标题和搜索框之间的间距

        .search-title {
          font-size: 26px;
          margin: 0;
          flex-shrink: 0;
          color: #3cb3ff;
          cursor: pointer;
        }

        .search-input-group {
          flex: 1;

          .search-input {
            // 按钮组样式 - 两个按钮都在同一个append容器内
            :deep(.el-input-group__append) {
              padding: 0;

              .button-group {
                display: flex;
                height: 100%;

                .search-btn {
                  border-radius: 0;
                  border-right: 1px solid #dcdfe6; // 添加右边框分隔
                  margin: 0;
                  position: relative;

                  // 添加微妙的右侧阴影增强分隔效果
                  &::after {
                    content: '';
                    position: absolute;
                    right: -1px;
                    top: 2px;
                    bottom: 2px;
                    width: 1px;
                    background: rgba(0, 0, 0, 0.06);
                    pointer-events: none;
                  }

                  &:hover {
                    border-right-color: #c6e2ff; // hover时边框颜色也要变化

                    &::after {
                      background: rgba(64, 158, 255, 0.1); // hover时阴影也变化
                    }
                  }
                }

                .tasks-btn {
                  border-radius: 0 4px 4px 0;
                  border-left: 1px solid #dcdfe6; // 添加左边框分隔
                  margin: 0;
                  width: 40px;
                  padding: 0;

                  &:hover {
                    color: #409eff;
                    border-color: #c6e2ff;
                    border-left-color: #c6e2ff; // hover时左边框也要变化
                    background-color: #ecf5ff;
                  }

                  &:focus {
                    outline: none;
                    color: #409eff;
                    border-color: #409eff;
                    border-left-color: #409eff; // focus时左边框也要变化
                  }
                }
              }
            }
          }
        }

        @media (max-width: 768px) {
          gap: 10px;

          .search-title {
            font-size: 20px;
          }

          .search-input-group {
            .search-input {
              .button-group {
                .tasks-btn {
                  height: 36px !important;
                  width: 36px !important;
                }
              }
            }
          }
        }
      }

      .search-input {
        width: 100%;
      }
    }
  }
}
.search {
  .list-container {
    box-sizing: border-box;
    border-radius: 4px;
    height: calc(100vh - 72px);
    overflow-y: scroll;
    padding-bottom: 20px;
    .list {
      margin: 0 auto;
      max-width: 1200px;
      padding: 0 16px;
      display: grid;
      /* grid-template-columns: repeat(2, 1fr); */
      grid-template-columns: repeat(1, 1fr);
      gap: 10px;

      @media (max-width: 768px) {
        padding: 0 12px;
      }

      @media (max-width: 480px) {
        padding: 0 8px;
      }

      .total {
        color: #909399;
        font-size: 14px;
        margin-left: 10px;

        @media (max-width: 480px) {
          margin-left: 0;
          font-size: 13px;
        }
      }
    }
  }
}
.res-card {
  border: 1px solid #ebeef5;
  margin-bottom: 10px;
  padding: 16px;
  border-radius: 4px;
  .title {
    font-size: 16px;
    color: #2440b3;
    cursor: pointer;
    display: inline-block;
    text-decoration: underline;

    &:hover {
      color: #315efb;
    }
  }
  .body {
    font-size: 14px;
    line-height: 20px;
    margin: 8px 0;
    color: #333333;
    font-size: 13px;

    .time {
      color: #626675;
      margin-right: 10px;
      font-size: 13px;
    }
    display: -webkit-box;
    -webkit-line-clamp: 4; /* 最多显示的行数 */
    -webkit-box-orient: vertical;
    text-overflow: ellipsis; /* 使用省略号显示溢出部分 */
    overflow: hidden;
  }
  .footer {
    display: flex;
    font-size: 13px;
    color: #626675;
    gap: 8px;

    .projectNo {
      margin-left: 10px;
    }
  }
}
.explain {
  color: #333;
  font-size: 13px;
  ::v-deep em {
    color: #333;
    font-weight: bold;
    font-size: 16px;
  }
}
.similar-item {
  cursor: pointer;
  margin: 0 10px 10px 0;
}
.empty {
  /* grid-column: span 2; */
}
::v-deep .el-card {
  /* grid-column: span 2; */
}
.explain {
  line-height: 1.4;
}

/* 思考过程折叠样式 */
::v-deep .ai-thinking-container {
  margin: 12px 0;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #fafafa;
}

::v-deep .ai-thinking-toggle {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

::v-deep .ai-thinking-toggle:hover {
  background-color: #f0f0f0;
}

::v-deep .ai-thinking-icon {
  margin-right: 6px;
  transition: transform 0.2s;
  color: #666;
}

::v-deep .ai-thinking-toggle.expanded .ai-thinking-icon {
  transform: rotate(180deg);
}

::v-deep .ai-thinking-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

::v-deep .ai-thinking-content {
  border-top: 1px solid #e8e8e8;
  padding: 12px;
  background-color: #f9f9f9;
}

::v-deep .ai-thinking-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #333;
  white-space: pre-wrap;
  line-height: 1.4;
}

.download {
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
</style>
