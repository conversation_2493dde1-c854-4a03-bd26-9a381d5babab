<template>
  <div class="tasks-container">
    <div class="header">
      <div class="header-left">
        <h2>任务管理</h2>
      </div>
      <div class="header-right">
        <el-button
          type="text"
          icon="el-icon-arrow-left"
          @click="goBack"
          class="back-button"
        >
          返回上一页
        </el-button>
      </div>
    </div>

    <div class="connection-status">
      <el-tag 
        :type="connectionStatus === 'connected' ? 'success' : 'danger'"
        size="small"
      >
        {{ connectionStatus === 'connected' ? '实时连接正常' : '连接断开' }}
      </el-tag>
      <span class="last-update">最后更新: {{ lastUpdateTime }}</span>
    </div>

    <div class="tasks-table">
      <el-table
        :data="filteredTasks"
        style="width: 100%"
        :row-class-name="getTableRowClass"
        @expand-change="handleExpandChange"
        row-key="task_id"
      >
        <!-- 展开列 -->
        <el-table-column type="expand">
          <template slot-scope="props">
            <div class="task-details-expanded">
              <!-- 简化的任务信息 - 已隐藏 -->
              <!--
              <div v-if="props.row.task_params" class="task-summary">
                <h4><i class="el-icon-setting"></i> 任务配置</h4>
                <div class="summary-content">
                  <div class="summary-item" v-if="props.row.task_params.workflow_mode">
                    <el-tag
                      :type="props.row.task_params.workflow_mode === 'agent_based' ? 'primary' : 'info'"
                      size="small"
                    >
                      <i class="el-icon-cpu"></i>
                      {{ props.row.task_params.workflow_mode === 'agent_based' ? 'Agent工作流' : '标准处理' }}
                    </el-tag>
                  </div>
                  <div class="summary-item" v-if="props.row.task_params.user_name">
                    <el-tag type="info" size="small">
                      <i class="el-icon-user"></i>
                      {{ props.row.task_params.user_name }}
                    </el-tag>
                  </div>
                  <div class="summary-item" v-if="props.row.task_params.action">
                    <el-tag type="warning" size="small">
                      <i class="el-icon-document"></i>
                      {{ props.row.task_params.action }}
                    </el-tag>
                  </div>
                </div>
              </div>
              -->

              <!-- Agent级别进度 - 已隐藏 -->
              <!--
              <div v-if="props.row.agent_progress && Object.keys(props.row.agent_progress).length > 0" class="agent-progress-section">
                <h4><i class="el-icon-cpu"></i> Agent执行进度 ({{ Object.keys(props.row.agent_progress).length }} 个Agent)</h4>
                <div class="agent-progress-list">
                  <div
                    v-for="(agentInfo, agentId) in props.row.agent_progress"
                    :key="agentId"
                    class="agent-progress-item"
                    :class="getAgentStatusClass(agentInfo.status)"
                  >
                    <div class="agent-header">
                      <span class="agent-name">{{ agentInfo.agent_name }}</span>
                      <span class="agent-status" :class="agentInfo.status">{{ getAgentStatusText(agentInfo.status) }}</span>
                      <span class="agent-level">层级 {{ agentInfo.level + 1 }}</span>
                    </div>
                    <div class="agent-progress-bar">
                      <el-progress
                        :percentage="agentInfo.progress || 0"
                        :stroke-width="4"
                        :show-text="false"
                        :class="getAgentProgressClass(agentInfo.status)"
                      />
                    </div>
                    <div class="agent-details">
                      <span class="agent-message">{{ agentInfo.message || '等待执行' }}</span>
                      <span v-if="agentInfo.execution_time > 0" class="agent-time">{{ agentInfo.execution_time.toFixed(2) }}s</span>
                    </div>
                    <div v-if="agentInfo.error" class="agent-error">
                      <i class="el-icon-warning"></i>
                      {{ agentInfo.error }}
                    </div>
                  </div>
                </div>
              </div>
              -->

              <!-- 文件级别进度 -->
              <div v-if="props.row.file_progress && Object.keys(props.row.file_progress).length > 0" class="file-progress-section">
                <h4><i class="el-icon-document"></i> 文件处理进度 ({{ Object.keys(props.row.file_progress).length }} 个文件)</h4>

                <!-- HNGPT项目的文件进度显示 -->
                <div v-if="props.row.task_type === 'hngpt_workflow'" class="hngpt-file-progress">
                  <div
                    v-for="(fileProgress, fileUrl) in props.row.file_progress"
                    :key="fileUrl"
                    class="hngpt-file-item"
                  >
                    <div class="file-header">
                      <span class="file-name">{{ getFileName(fileUrl) }}</span>
                      <span class="file-status" :class="fileProgress.status">{{ getFileStatusText(fileProgress.status) }}</span>
                    </div>

                    <!-- 文件处理工作流进度条 -->
                    <div class="file-workflow-progress">
                      <div class="file-stage-segments">
                        <div
                          v-for="(stage, index) in getFileStages(fileProgress)"
                          :key="index"
                          class="file-stage-segment"
                          :class="getFileStageClass(stage.status)"
                          :title="`${stage.name}: ${stage.status}`"
                        >
                          <div class="file-stage-fill" :style="getFileStageFillStyle(stage)"></div>
                        </div>
                      </div>
                      <div class="file-stage-text">{{ getFileStageText(fileProgress) }}</div>
                    </div>

                    <div v-if="fileProgress.error" class="file-error">
                      <i class="el-icon-warning"></i>
                      {{ fileProgress.error }}
                    </div>
                  </div>
                </div>

                <!-- 传统文件进度表格 -->
                <div v-else>
                <el-table
                  :data="getFileProgressData(props.row.file_progress)"
                  size="small"
                  style="margin-top: 10px;"
                >
                  <el-table-column prop="fileName" label="文件名" min-width="180">
                    <template slot-scope="scope">
                      <div class="file-name-cell">
                        <i :class="getFileIcon(scope.row.status)"></i>
                        <span :title="scope.row.fullPath">{{ scope.row.fileName }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="doc_id" label="文档ID" width="120">
                    <template slot-scope="scope">
                      <div v-if="scope.row.doc_id" class="doc-id-cell">
                        <el-tooltip :content="scope.row.doc_id" placement="top">
                          <span class="doc-id-text">{{ scope.row.doc_id.substring(0, 8) }}...</span>
                        </el-tooltip>
                      </div>
                      <span v-else class="doc-id-empty">-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="90">
                    <template slot-scope="scope">
                      <el-tag
                        :type="getFileStatusType(scope.row.status)"
                        size="mini"
                        effect="plain"
                      >
                        {{ getFileStatusText(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="progress" label="进度" width="120">
                    <template slot-scope="scope">
                      <el-progress
                        :percentage="Number(scope.row.progress) || 0"
                        :stroke-width="6"
                        :show-text="true"
                        :text-inside="false"
                        :class="getFileProgressClass(scope.row.status)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="duration" label="耗时" width="80">
                    <template slot-scope="scope">
                      <div v-if="scope.row.duration !== undefined" class="duration-cell">
                        <span class="duration-text">{{ formatDuration(scope.row.duration) }}</span>
                      </div>
                      <span v-else class="duration-empty">-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="stage" label="处理阶段" width="120">
                    <template slot-scope="scope">
                      <div v-if="scope.row.stage_detail" class="stage-info">
                        <span class="stage-detail">{{ scope.row.stage_detail }}</span>
                      </div>
                      <div v-else-if="scope.row.stage" class="stage-info">
                        <span class="stage-name">{{ getStageText(scope.row.stage) }}</span>
                      </div>
                      <span v-else class="stage-empty">-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="error" label="错误信息" min-width="150">
                    <template slot-scope="scope">
                      <div v-if="scope.row.error" class="error-cell">
                        <el-tooltip :content="scope.row.error" placement="top">
                          <span class="error-text">{{ scope.row.error.substring(0, 30) }}{{ scope.row.error.length > 30 ? '...' : '' }}</span>
                        </el-tooltip>
                      </div>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                </el-table>
                </div>
              </div>

              <!-- 无文件进度信息提示 -->
              <div v-else class="no-file-progress">
                <h4><i class="el-icon-document"></i> 文件处理进度</h4>
                <el-alert
                  title="暂无文件处理进度信息"
                  type="info"
                  :closable="false"
                  show-icon
                />
              </div>

              <!-- 错误信息 -->
              <div v-if="props.row.error" class="error-info">
                <h4><i class="el-icon-warning"></i> 错误信息</h4>
                <el-alert
                  :title="props.row.error"
                  type="error"
                  :closable="false"
                  show-icon
                />
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 项目名称 -->
        <el-table-column prop="project_name" label="项目名称" min-width="300">
          <template slot-scope="scope">
            <div class="project-name-cell">
              <strong>{{ getProjectName(scope.row) }}</strong>
            </div>
          </template>
        </el-table-column>

        <!-- 任务状态 -->
        <el-table-column prop="status" label="状态" width="120">
          <template slot-scope="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
              effect="dark"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 任务类型 -->
        <el-table-column prop="task_type" label="类型" width="120">
          <template slot-scope="scope">
            <el-tag
              :type="getTaskTypeTagColor(scope.row)"
              size="small"
            >
              {{ getTaskTypeText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 进度 -->
        <el-table-column prop="progress" label="进度" width="175">
          <template slot-scope="scope">
            <div class="progress-cell">
              <!-- HNGPT项目级工作流进度 -->
              <div v-if="scope.row.task_type === 'hngpt_workflow' || (scope.row.agent_progress && Object.keys(scope.row.agent_progress).length > 0)" class="hngpt-progress-container">
                <!-- 统一项目进度条 -->
                <div class="unified-project-progress">
                  <el-progress
                    :percentage="getUnifiedProjectProgress(scope.row)"
                    :stroke-width="6"
                    :show-text="true"
                    :text-inside="false"
                    :class="getProgressClass(scope.row.status)"
                  />
                  <div class="unified-progress-info">
                    {{ getUnifiedProgressInfo(scope.row) }}
                  </div>
                </div>
              </div>

              <!-- 传统文件进度 -->
              <div v-else class="traditional-progress">
                <el-progress
                  :percentage="getTraditionalProgress(scope.row)"
                  :stroke-width="6"
                  :show-text="true"
                  :text-inside="false"
                  :class="getProgressClass(scope.row.status)"
                />
                <div class="file-count-info">
                  {{ getFileCount(scope.row) }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 创建时间 -->
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>

        <!-- 完成时间 -->
        <el-table-column prop="completed_at" label="完成时间" width="160">
          <template slot-scope="scope">
            <span v-if="(scope.row.status === 'completed' || scope.row.status === 'failed') && scope.row.updated_at">
              {{ formatTime(scope.row.updated_at) }}
            </span>
            <span v-else class="completion-empty">-</span>
          </template>
        </el-table-column>


      </el-table>



      <div v-if="filteredTasks.length === 0" class="empty-tasks">
        <el-empty description="暂无进行中或24小时内完成的任务" />
      </div>
    </div>
  </div>
</template>

<script>
import { getTasks, createTasksSSE } from '@/api/tasks'

export default {
  name: 'Tasks',
  data() {
    return {
      tasks: [],
      loading: false,
      autoRefresh: true,
      connectionStatus: 'disconnected',
      lastUpdateTime: '',
      eventSource: null,
      refreshTimer: null
    }
  },

  computed: {
    // 过滤任务：只显示正在进行的任务和24小时内完成/失败的任务
    filteredTasks() {
      const now = new Date()
      const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)

      return this.tasks.filter(task => {
        // 显示正在进行的任务
        if (task.status === 'running' || task.status === 'pending') {
          return true
        }

        // 显示24小时内完成或失败的任务
        if (task.status === 'completed' || task.status === 'failed') {
          const taskTime = new Date(task.updated_at || task.created_at)
          return taskTime >= twentyFourHoursAgo
        }

        return false
      })
    }
  },

  mounted() {
    this.initializeConnection()
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 获取文件计数信息
    getFileCount(row) {
      const processedCount = (row.processed_files ? row.processed_files.length : 0)
      const failedCount = (row.failed_files ? row.failed_files.length : 0)
      const totalProcessed = processedCount + failedCount
      const totalFiles = row.total_files || Object.keys(row.file_progress || {}).length || 1

      return `${totalProcessed} / ${totalFiles}`
    },

    // 表格行类名
    getTableRowClass({ row }) {
      const statusClass = {
        'running': 'table-row-running',
        'completed': 'table-row-completed',
        'failed': 'table-row-failed',
        'cancelled': 'table-row-cancelled'
      }
      return statusClass[row.status] || 'table-row-default'
    },

    // 处理表格展开变化
    handleExpandChange(row, expandedRows) {
      // 可以在这里添加展开时的逻辑，比如加载详细数据
      console.log('展开状态变化:', row.task_id, expandedRows.length > 0)
    },

    // 获取文件进度数据（转换为表格数据格式）
    getFileProgressData(fileProgress) {
      if (!fileProgress) return []

      return Object.entries(fileProgress).map(([fileUrl, progress]) => ({
        fullPath: fileUrl,
        fileName: this.getFileName(fileUrl),
        doc_id: progress.doc_id || '',
        status: progress.status || 'unknown',
        progress: progress.progress || 0,
        duration: progress.duration,
        stage: progress.stage || '',
        stage_detail: progress.stage_detail || '',
        error: progress.error || '',
        message: progress.message || '',
        updated_at: progress.updated_at || ''
      }))
    },

    // Agent状态相关方法 - 已注释（不再显示Agent进度）
    /*
    getAgentStatusText(status) {
      const statusMap = {
        'pending': '等待中',
        'running': '执行中',
        'completed': '已完成',
        'failed': '失败',
        'skipped': '已跳过'
      }
      return statusMap[status] || status
    },

    getAgentStatusClass(status) {
      return `agent-status-${status}`
    },

    getAgentProgressClass(status) {
      const classMap = {
        'pending': 'agent-progress-pending',
        'running': 'agent-progress-running',
        'completed': 'agent-progress-completed',
        'failed': 'agent-progress-failed',
        'skipped': 'agent-progress-skipped'
      }
      return classMap[status] || ''
    },
    */

    // Agent工作流进度相关方法
    getOrderedAgentProgress(agentProgress) {
      if (!agentProgress) return []

      // 按层级排序Agent
      const agents = Object.entries(agentProgress).map(([agentId, agentInfo]) => ({
        agentId,
        ...agentInfo
      }))

      return agents.sort((a, b) => (a.level || 0) - (b.level || 0))
    },

    getAgentSegmentClass(status) {
      return `agent-segment-${status}`
    },

    getAgentSegmentStyle(agentProgress) {
      const totalAgents = Object.keys(agentProgress).length
      const segmentWidth = totalAgents > 0 ? (100 / totalAgents) : 100
      return {
        width: `${segmentWidth}%`
      }
    },

    getAgentSegmentFillStyle(agentInfo) {
      const progress = agentInfo.progress || 0
      const status = agentInfo.status || 'pending'

      let backgroundColor = '#e9ecef' // 默认灰色

      switch (status) {
        case 'running':
          backgroundColor = '#ffc107' // 黄色
          break
        case 'completed':
          backgroundColor = '#28a745' // 绿色
          break
        case 'failed':
          backgroundColor = '#dc3545' // 红色
          break
        case 'skipped':
          backgroundColor = '#17a2b8' // 青色
          break
      }

      return {
        width: `${progress}%`,
        backgroundColor: backgroundColor
      }
    },

    getAgentProgressText(agentProgress) {
      if (!agentProgress) return '0%'

      const agents = Object.values(agentProgress)
      const completedAgents = agents.filter(agent => agent.status === 'completed').length
      const totalAgents = agents.length
      const overallProgress = totalAgents > 0 ? Math.round((completedAgents / totalAgents) * 100) : 0

      // 找到当前正在执行的Agent
      const runningAgent = agents.find(agent => agent.status === 'running')
      if (runningAgent) {
        return `${runningAgent.agent_name} (${overallProgress}%)`
      }

      // 找到最后完成的Agent
      const lastCompletedAgent = agents
        .filter(agent => agent.status === 'completed')
        .sort((a, b) => (b.level || 0) - (a.level || 0))[0]

      if (lastCompletedAgent && overallProgress === 100) {
        return `已完成 (100%)`
      } else if (lastCompletedAgent) {
        return `${lastCompletedAgent.agent_name}完成 (${overallProgress}%)`
      }

      return `${overallProgress}%`
    },

    // 项目级工作流进度方法
    getProjectStages(taskRow) {
      // HNGPT项目处理的主要阶段
      const stages = [
        { name: '文件处理', key: 'file_processing' },
        { name: '向量化存储', key: 'vectorization' },
        { name: '信息检索', key: 'retrieval' },
        { name: '信息提取', key: 'extraction' },
        { name: '数据存储', key: 'storage' }
      ]

      // 根据任务状态和Agent进度计算各阶段状态
      const agentProgress = taskRow.agent_progress || {}
      const fileProgress = taskRow.file_progress || {}
      const totalFiles = Math.max(Object.keys(fileProgress).length, 1) // 至少为1

      // 获取Agent状态信息
      const agents = Object.values(agentProgress)
      const initAgent = agents.find(a => a.agent_name === 'InitAgent')
      const downloadAgent = agents.find(a => a.agent_name === 'DownloadAgent')
      const parseAgent = agents.find(a => a.agent_name === 'ParseAgent')
      const extractionAgent = agents.find(a => a.agent_name === 'ExtractionAgent')
      const storageAgent = agents.find(a => a.agent_name === 'StorageAgent')

      return stages.map((stage, index) => {
        let status = 'pending'
        let progress = 0

        switch (stage.key) {
          case 'file_processing':
            // 文件处理阶段：基于InitAgent、DownloadAgent、ParseAgent
            if (parseAgent && parseAgent.status === 'completed') {
              status = 'completed'
              progress = 100
            } else if (parseAgent && parseAgent.status === 'running') {
              status = 'running'
              progress = parseAgent.progress || 50
            } else if (downloadAgent && downloadAgent.status === 'completed') {
              status = 'running'
              progress = 75
            } else if (downloadAgent && downloadAgent.status === 'running') {
              status = 'running'
              progress = downloadAgent.progress || 25
            } else if (initAgent && initAgent.status === 'completed') {
              status = 'running'
              progress = 25
            } else if (initAgent && initAgent.status === 'running') {
              status = 'running'
              progress = initAgent.progress || 10
            }
            break

          case 'vectorization':
            // 向量化阶段：基于文件进度或ExtractionAgent早期状态
            if (extractionAgent && extractionAgent.status === 'running') {
              status = 'running'
              progress = 50
            } else if (parseAgent && parseAgent.status === 'completed') {
              status = 'running'
              progress = 25
            }
            break

          case 'retrieval':
            // 信息检索阶段：基于ExtractionAgent
            if (extractionAgent) {
              if (extractionAgent.status === 'completed') {
                status = 'completed'
                progress = 100
              } else if (extractionAgent.status === 'running') {
                status = 'running'
                progress = extractionAgent.progress || 50
              }
            }
            break

          case 'extraction':
            // 信息提取阶段：基于ExtractionAgent
            if (extractionAgent) {
              if (extractionAgent.status === 'completed') {
                status = 'completed'
                progress = 100
              } else if (extractionAgent.status === 'running') {
                status = 'running'
                progress = extractionAgent.progress || 50
              }
            }
            break

          case 'storage':
            // 数据存储阶段：基于StorageAgent
            if (storageAgent) {
              if (storageAgent.status === 'completed') {
                status = 'completed'
                progress = 100
              } else if (storageAgent.status === 'running') {
                status = 'running'
                progress = storageAgent.progress || 50
              }
            }
            break
        }

        return { ...stage, status, progress }
      })
    },

    getProjectStageClass(status) {
      return `project-stage-${status}`
    },

    getProjectStageStyle(taskRow) {
      const stageWidth = 20 // 5个阶段，每个20%
      return {
        width: `${stageWidth}%`
      }
    },

    getProjectStageFillStyle(stage) {
      const progress = stage.progress || 0
      const status = stage.status || 'pending'

      let backgroundColor = '#e9ecef' // 默认灰色

      switch (status) {
        case 'running':
          backgroundColor = '#ffc107' // 黄色
          break
        case 'completed':
          backgroundColor = '#28a745' // 绿色
          break
        case 'failed':
          backgroundColor = '#dc3545' // 红色
          break
      }

      return {
        width: `${progress}%`,
        backgroundColor: backgroundColor
      }
    },

    getProjectProgressText(taskRow) {
      const stages = this.getProjectStages(taskRow)
      const completedStages = stages.filter(stage => stage.status === 'completed').length

      // 计算整体进度百分比
      const overallProgress = Math.round((completedStages / stages.length) * 100)

      // 获取文件计数
      const fileProgress = taskRow.file_progress || {}
      const totalFiles = Object.keys(fileProgress).length
      const completedFiles = Object.values(fileProgress).filter(fp =>
        fp.status === 'completed'
      ).length

      // 只显示百分比和文件计数
      return `${overallProgress}% ${completedFiles}/${totalFiles}`
    },

    // 文件级工作流进度方法
    getFileStages(fileProgress) {
      const stages = [
        { name: '下载', key: 'download' },
        { name: '解析', key: 'parse' },
        { name: '向量化', key: 'vectorize' },
        { name: 'ES存储', key: 'es_storage' }
      ]

      const currentStage = fileProgress.stage || 'download'

      return stages.map((stage, index) => {
        let status = 'pending'

        if (fileProgress.status === 'failed') {
          status = currentStage === stage.key ? 'failed' : 'pending'
        } else if (fileProgress.status === 'completed') {
          status = 'completed'
        } else {
          // 根据当前阶段确定状态
          const stageOrder = ['download', 'parse', 'vectorize', 'es_storage']
          const currentIndex = stageOrder.indexOf(currentStage)
          const stageIndex = stageOrder.indexOf(stage.key)

          if (stageIndex < currentIndex) {
            status = 'completed'
          } else if (stageIndex === currentIndex) {
            status = 'running'
          } else {
            status = 'pending'
          }
        }

        return { ...stage, status }
      })
    },

    getFileStageClass(status) {
      return `file-stage-${status}`
    },

    getFileStageFillStyle(stage) {
      const status = stage.status || 'pending'
      let backgroundColor = '#e9ecef'
      let width = '0%'

      switch (status) {
        case 'running':
          backgroundColor = '#ffc107'
          width = '50%'
          break
        case 'completed':
          backgroundColor = '#28a745'
          width = '100%'
          break
        case 'failed':
          backgroundColor = '#dc3545'
          width = '100%'
          break
      }

      return {
        width: width,
        backgroundColor: backgroundColor
      }
    },

    getFileStageText(fileProgress) {
      const stage = fileProgress.stage || 'download'
      const status = fileProgress.status || 'processing'

      const stageNames = {
        'download': '下载',
        'parse': '解析',
        'vectorize': '向量化',
        'es_storage': 'ES存储'
      }

      const stageName = stageNames[stage] || stage

      if (status === 'completed') {
        return '处理完成'
      } else if (status === 'failed') {
        return `${stageName}失败`
      } else {
        return `${stageName}中...`
      }
    },

    getFileStatusText(status) {
      const statusMap = {
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败'
      }
      return statusMap[status] || status
    },

    // 统一项目进度相关方法
    getUnifiedProjectProgress(taskRow) {
      const agentProgress = taskRow.agent_progress || {}
      const fileProgress = taskRow.file_progress || {}

      // 如果任务已完成，直接返回100%
      if (taskRow.status === 'completed') {
        return 100
      }

      // 如果有Agent进度，基于Agent状态计算
      if (Object.keys(agentProgress).length > 0) {
        const agents = Object.values(agentProgress)
        const completedAgents = agents.filter(a => a.status === 'completed').length
        const runningAgents = agents.filter(a => a.status === 'running').length
        const totalAgents = agents.length

        if (totalAgents === 0) return 0

        // 计算整体进度：完成的Agent + 运行中Agent的平均进度
        let totalProgress = completedAgents * 100

        if (runningAgents > 0) {
          const runningProgress = agents
            .filter(a => a.status === 'running')
            .reduce((sum, a) => sum + (a.progress || 0), 0)
          totalProgress += runningProgress
        }

        return Math.round(totalProgress / totalAgents)
      }

      // 如果没有Agent进度，基于文件进度计算
      if (Object.keys(fileProgress).length > 0) {
        const totalFiles = Object.keys(fileProgress).length
        const completedFiles = Object.values(fileProgress).filter(fp =>
          fp.status === 'completed'
        ).length

        return Math.round((completedFiles / totalFiles) * 100)
      }

      // 默认返回任务进度，如果任务完成但progress为0，则返回100%
      const progress = Number(taskRow.progress) || 0
      if (progress === 0 && taskRow.status === 'completed') {
        return 100
      }
      return progress
    },

    getTraditionalProgress(taskRow) {
      // 如果任务已完成，直接返回100%
      if (taskRow.status === 'completed') {
        return 100
      }

      // 如果任务失败，返回0%
      if (taskRow.status === 'failed') {
        return 0
      }

      // 基于文件进度计算
      const fileProgress = taskRow.file_progress || {}
      if (Object.keys(fileProgress).length > 0) {
        const totalFiles = Object.keys(fileProgress).length
        const completedFiles = Object.values(fileProgress).filter(fp =>
          fp.status === 'completed'
        ).length

        return Math.round((completedFiles / totalFiles) * 100)
      }

      // 默认返回任务进度，如果任务完成但progress为0，则返回100%
      const progress = Number(taskRow.progress) || 0
      if (progress === 0 && taskRow.status === 'completed') {
        return 100
      }
      return progress
    },

    getUnifiedProgressInfo(taskRow) {
      const fileProgress = taskRow.file_progress || {}
      const totalFiles = Object.keys(fileProgress).length
      const completedFiles = Object.values(fileProgress).filter(fp =>
        fp.status === 'completed'
      ).length

      if (totalFiles === 0) return '0/0'

      return `${completedFiles}/${totalFiles}`
    },

    // 文件处理进度相关方法
    getFileProcessingPercentage(taskRow) {
      const fileProgress = taskRow.file_progress || {}
      const totalFiles = Object.keys(fileProgress).length

      if (totalFiles === 0) return 0

      // 计算文件处理进度
      let totalProgress = 0
      Object.values(fileProgress).forEach(fp => {
        const stage = fp.stage || 'download'
        const status = fp.status || 'processing'

        // 根据阶段和状态计算单个文件的进度
        let fileProgress = 0
        if (status === 'completed') {
          fileProgress = 100
        } else if (status === 'processing') {
          const stageProgress = {
            'download': 25,
            'parse': 50,
            'vectorize': 75,
            'es_storage': 100
          }
          fileProgress = stageProgress[stage] || 0
        }

        totalProgress += fileProgress
      })

      return Math.round(totalProgress / totalFiles)
    },

    getFileProgressClass(taskRow) {
      const status = taskRow.status || 'running'
      return `file-progress-${status}`
    },

    getFileProgressInfo(taskRow) {
      const fileProgress = taskRow.file_progress || {}
      const totalFiles = Object.keys(fileProgress).length

      if (totalFiles === 0) return '0/0 文件'

      const completedFiles = Object.values(fileProgress).filter(fp =>
        fp.status === 'completed'
      ).length

      const processingFiles = Object.values(fileProgress).filter(fp =>
        fp.status === 'processing'
      ).length

      if (completedFiles === totalFiles) {
        return `${totalFiles}/${totalFiles} 文件完成`
      } else if (processingFiles > 0) {
        return `${completedFiles}/${totalFiles} 文件 (${processingFiles}处理中)`
      } else {
        return `${completedFiles}/${totalFiles} 文件`
      }
    },

    // 格式化耗时显示
    formatDuration(seconds) {
      if (seconds === undefined || seconds === null) return '-'

      const duration = parseFloat(seconds)
      if (isNaN(duration)) return '-'

      if (duration < 1) {
        return `${Math.round(duration * 1000)}ms`
      } else if (duration < 60) {
        return `${duration.toFixed(1)}s`
      } else if (duration < 3600) {
        const minutes = Math.floor(duration / 60)
        const remainingSeconds = Math.round(duration % 60)
        return `${minutes}m${remainingSeconds}s`
      } else {
        const hours = Math.floor(duration / 3600)
        const minutes = Math.floor((duration % 3600) / 60)
        return `${hours}h${minutes}m`
      }
    },

    initializeConnection() {
      if (this.autoRefresh) {
        this.connectSSE()
      } else {
        this.refreshTasks()
      }
    },

    connectSSE() {
      this.cleanup()

      try {
        // 调试模式，不需要token
        this.eventSource = createTasksSSE()

        this.eventSource.onopen = () => {
          this.connectionStatus = 'connected'
          console.log('SSE连接已建立')
        }

        this.eventSource.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            if (data.error) {
              console.error('SSE错误:', data.error)
              this.connectionStatus = 'disconnected'
              return
            }

            this.tasks = data.tasks || []
            this.lastUpdateTime = new Date().toLocaleTimeString()
            this.connectionStatus = 'connected'
          } catch (error) {
            console.error('解析SSE数据失败:', error)
          }
        }

        this.eventSource.onerror = (error) => {
          console.error('SSE连接错误:', error)
          this.connectionStatus = 'disconnected'

          // 重连逻辑
          setTimeout(() => {
            if (this.autoRefresh) {
              this.connectSSE()
            }
          }, 5000)
        }
      } catch (error) {
        console.error('创建SSE连接失败:', error)
        this.connectionStatus = 'disconnected'
      }
    },

    async refreshTasks() {
      this.loading = true
      try {
        const response = await getTasks()
        if (response.code === 200) {
          this.tasks = response.data || []
          this.lastUpdateTime = new Date().toLocaleTimeString()
        }
      } catch (error) {
        console.error('获取任务列表失败:', error)
        this.$message.error('获取任务列表失败')
      } finally {
        this.loading = false
      }
    },



    cleanup() {
      if (this.eventSource) {
        this.eventSource.close()
        this.eventSource = null
      }
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },





    getTaskCardClass(status) {
      return {
        'task-running': status === 'running',
        'task-completed': status === 'completed',
        'task-failed': status === 'failed',
        'task-pending': status === 'pending'
      }
    },

    getStatusType(status) {
      const types = {
        pending: 'info',
        running: 'warning',
        completed: 'success',
        failed: 'danger'
      }
      return types[status] || 'info'
    },

    getStatusText(status) {
      const texts = {
        pending: '等待中',
        running: '运行中',
        completed: '已完成',
        failed: '失败'
      }
      return texts[status] || status
    },

    // 返回进度条的CSS类名，用于颜色区分，不显示状态图标
    getProgressClass(status) {
      switch(status) {
        case 'failed':
          return 'progress-failed'
        case 'completed':
          return 'progress-completed'
        case 'running':
        case 'processing':
          return 'progress-running'
        default:
          return 'progress-default'
      }
    },

    getFileStatusType(status) {
      // 与进度条颜色保持一致：运行状态用蓝色，失败用红色，成功用绿色
      switch(status) {
        case 'failed':
        case 'error':
          return 'danger'     // 红色
        case 'completed':
        case 'success':
          return 'success'    // 绿色
        case 'processing':
        case 'running':
          return 'primary'    // 蓝色
        case 'pending':
        case 'waiting':
          return 'info'       // 灰色
        default:
          return 'info'
      }
    },

    getFileStatusText(status) {
      const texts = {
        processing: '处理中',
        running: '运行中',
        pending: '等待中',
        waiting: '等待中',
        completed: '已完成',
        success: '成功',
        failed: '失败',
        error: '错误'
      }
      return texts[status] || status
    },

    // 返回文件进度条的CSS类名，用于颜色区分，不显示状态图标
    getFileProgressClass(status) {
      switch(status) {
        case 'failed':
        case 'error':
          return 'progress-failed'
        case 'completed':
        case 'success':
          return 'progress-completed'
        case 'processing':
        case 'running':
          return 'progress-running'
        case 'pending':
        default:
          return 'progress-default'
      }
    },

    getFileName(url) {
      return url.split('/').pop() || url
    },

    formatTime(timeStr) {
      if (!timeStr) return ''
      return new Date(timeStr).toLocaleString()
    },

    formatParamValue(value) {
      if (typeof value === 'object') {
        return JSON.stringify(value)
      }
      return String(value)
    },

    getProjectName(task) {
      // 优先从task_params中获取project_name
      if (task.task_params && task.task_params.project_name) {
        return task.task_params.project_name
      }
      // 如果没有，使用task_id作为备用
      return `任务 ${task.task_id.substring(0, 8)}`
    },

    getTaskTypeText(task) {
      // 根据action字段决定类型显示
      if (task.task_params && task.task_params.action) {
        const action = task.task_params.action
        // 只显示两种类型：项目档案 或 文书档案
        if (action === '项目档案') {
          return '项目档案'
        } else if (action === '文书档案') {
          return '文书档案'
        } else {
          // 如果action是其他值，根据内容判断或默认为项目档案
          return action.includes('文书') ? '文书档案' : '项目档案'
        }
      }

      // 如果没有action字段，默认显示项目档案
      return '项目档案'
    },

    getTaskTypeTagColor(task) {
      // 根据任务类型设置标签颜色
      const taskType = this.getTaskTypeText(task)
      if (taskType === '项目档案') {
        return 'primary'  // 蓝色
      } else if (taskType === '文书档案') {
        return 'success'  // 绿色
      } else {
        return 'info'     // 灰色（备用）
      }
    },

    getActionText(task) {
      if (task.task_params && task.task_params.action) {
        return task.task_params.action
      }
      return ''
    },

    getFileItemClass(status) {
      return {
        'file-processing': status === 'processing',
        'file-completed': status === 'completed',
        'file-failed': status === 'failed'
      }
    },

    getFileIcon(status) {
      const icons = {
        processing: 'el-icon-loading',
        completed: 'el-icon-circle-check',
        failed: 'el-icon-circle-close'
      }
      return icons[status] || 'el-icon-document'
    },

    getStageText(stage) {
      const stages = {
        init: '初始化',
        download: '文件下载',
        validate: '文件验证',
        parse: '文档解析',
        extract_basic: '基础信息提取',
        extract_structure: '结构化信息提取',
        extract_entities: '实体信息提取',
        extract_relations: '关系信息提取',
        merge_info: '信息合并',
        validate_result: '结果验证',
        store: '存储结果',
        complete: '处理完成'
      }
      return stages[stage] || stage
    }
  }
}
</script>

<style scoped>
.tasks-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-between !important;
  align-items: center !important;
  align-content: center !important;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  min-height: 40px !important;
  width: 100% !important;

  /* 调试样式 - 可以临时启用查看对齐 */
  /* border: 2px solid red; */
}

.header-left {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  height: 100% !important;
  flex: 0 0 auto !important;

  /* 调试样式 */
  /* border: 2px solid blue; */
}

.header h2 {
  margin: 0 !important;
  padding: 0 !important;
  color: #303133;
  font-size: 24px;
  font-weight: 500;
  line-height: 1.2 !important;
  display: flex !important;
  align-items: center !important;
}

.header-right {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end !important;
  gap: 10px;
  height: 100% !important;
  flex: 0 0 auto !important;

  /* 调试样式 */
  /* border: 2px solid green; */
}

.back-button {
  font-size: 14px;
  color: #409eff;
  padding: 8px 12px !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1.2 !important;

  &:hover {
    color: #66b1ff;
    background-color: #ecf5ff;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 100%; /* 占满父容器高度 */
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.last-update {
  color: #909399;
  font-size: 12px;
}

.tasks-table {
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 表格行样式 */
.tasks-table ::v-deep .table-row-running {
  background-color: #ecf5ff;
}

.tasks-table ::v-deep .table-row-completed {
  background-color: #f0f9ff;
}

.tasks-table ::v-deep .table-row-failed {
  background-color: #fef0f0;
}

.tasks-table ::v-deep .table-row-cancelled {
  background-color: #f4f4f5;
}

.tasks-table ::v-deep .table-row-default {
  background-color: #ffffff;
}

/* 表格展开行样式 */
.task-details-expanded {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 4px;
  margin: 10px 0;
}

.task-details-expanded h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 项目名称单元格 */
.project-name-cell strong {
  color: #303133;
  font-size: 14px;
}

/* 进度单元格 */
.progress-cell {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0px;
  width: 100%;
  min-width: 150px;
  overflow: visible !important;
}

.progress-cell .el-progress {
  flex: 1;
  min-width: 80px;
  max-width: calc(100% - 60px) !important;
  width: auto !important;
  display: flex !important;
  align-items: center !important;
  overflow: visible !important;
}

/* 确保进度条基本样式 */
.progress-cell ::v-deep .el-progress-bar {
  flex: 1;
  margin-right: 0px;
}



.file-count-info {
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 40px;
  width: auto;
  text-align: left;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  padding: 2px 4px;
  margin-left: 6px;
}

/* 文件名单元格 */
.file-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name-cell i {
  font-size: 16px;
}

/* 阶段信息 */
.stage-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stage-detail {
  color: #606266;
  font-size: 12px;
}

.stage-name {
  color: #303133;
  font-size: 12px;
}

.stage-empty {
  color: #c0c4cc;
}

/* 完成时间样式 */
.completion-empty {
  color: #c0c4cc;
  font-style: italic;
}

/* 错误信息单元格 */
.error-cell {
  max-width: 150px;
}

.error-text {
  color: #f56c6c;
  font-size: 12px;
  cursor: pointer;
}

/* 文档ID单元格 */
.doc-id-cell {
  display: flex;
  align-items: center;
}

.doc-id-text {
  color: #606266;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  cursor: pointer;
}

.doc-id-empty {
  color: #c0c4cc;
  font-size: 12px;
}

/* 耗时单元格 */
.duration-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.duration-text {
  color: #606266;
  font-size: 12px;
  font-weight: 500;
}

.duration-empty {
  color: #c0c4cc;
  font-size: 12px;
}

/* 任务摘要样式 */
.task-summary {
  margin-bottom: 20px;
}

.summary-content {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 10px;
}

.summary-item {
  display: inline-flex;
  align-items: center;
}

/* 文件进度部分 */
.file-progress-section {
  margin-bottom: 20px;
}

.file-progress-section h4 {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}



/* 错误信息 */
.error-info {
  margin-top: 15px;
}

.error-info h4 {
  color: #f56c6c;
}

/* 进度条颜色样式 - 使用更简单的选择器 */
.progress-cell ::v-deep .progress-failed .el-progress-bar__inner {
  background-color: #f56c6c !important; /* 红色 - 失败 */
}

.progress-cell ::v-deep .progress-completed .el-progress-bar__inner {
  background-color: #67c23a !important; /* 绿色 - 完成 */
}

.progress-cell ::v-deep .progress-running .el-progress-bar__inner {
  background-color: #409eff !important; /* 蓝色 - 运行中 */
}

.progress-cell ::v-deep .progress-default .el-progress-bar__inner {
  background-color: #e6e6e6 !important; /* 灰色 - 默认 */
}

/* 确保进度条内部元素可见 */
.progress-cell ::v-deep .el-progress-bar__inner {
  height: 100% !important;
  border-radius: 3px !important;
  transition: width 0.6s ease !important;
  background-color: #409eff !important; /* 默认蓝色 */
}



/* 确保进度条文本正确显示 */
.progress-cell ::v-deep .el-progress__text {
  font-size: 14px !important;
  color: #606266 !important;
  margin: 0px !important;
  padding: 0px !important;
  white-space: nowrap !important;
  flex-shrink: 0 !important;
  width: 45px !important;
  text-align: left !important;
  display: inline-block !important;
}

/* 确保进度条外层容器样式 */
.progress-cell ::v-deep .el-progress-bar__outer {
  background-color: #ebeef5 !important;
  border-radius: 3px !important;
  overflow: hidden !important;
  height: 6px !important;
  position: relative !important;
}

/* 确保进度条在所有状态下都有一致的布局 */
.progress-cell ::v-deep .el-progress {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
}

/* 让进度条和百分比文本紧密相连 */
.progress-cell ::v-deep .el-progress .el-progress-bar {
  margin-right: 0 !important;
  padding-right: 0 !important;
}

.progress-cell ::v-deep .el-progress .el-progress__text {
  margin-left: 0 !important;
  padding-left: 0 !important;
  margin-right: 0 !important;
  padding-right: 0 !important;
}

/* 强制移除Element UI的默认间距 */
.progress-cell ::v-deep .el-progress--line .el-progress__text {
  margin-left: 0 !important;
}

.progress-cell ::v-deep .el-progress--line .el-progress-bar {
  padding-right: 0 !important;
  margin-right: 0 !important;
}

/* 强制设置进度条内部背景色 - 使用属性选择器确保覆盖内联样式 */
.progress-cell ::v-deep .el-progress-bar__inner[style*="width"] {
  background: #409eff !important;
}

.progress-cell ::v-deep .progress-completed .el-progress-bar__inner[style*="width"] {
  background: #67c23a !important;
}

.progress-cell ::v-deep .progress-failed .el-progress-bar__inner[style*="width"] {
  background: #f56c6c !important;
}

.progress-cell ::v-deep .progress-running .el-progress-bar__inner[style*="width"] {
  background: #409eff !important;
}

/* 特殊处理0%进度的情况 - 确保即使width为0%也能看到一点颜色提示 */
.progress-cell ::v-deep .progress-failed .el-progress-bar__inner[style*="width: 0%"] {
  background: #f56c6c !important;
  min-width: 2px !important;
  opacity: 0.3 !important;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 15px;
}

.task-title-section {
  flex: 1;
  min-width: 0;
}

.task-main-title {
  margin-bottom: 8px;
}

.project-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  word-break: break-word;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.status-tag {
  font-weight: 500;
}

.task-type-badge {
  background: #e1f5fe;
  color: #0277bd;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.task-action-badge {
  background: #f3e5f5;
  color: #7b1fa2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.task-id-inline {
  font-family: monospace;
  background: #f5f7fa;
  color: #909399;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  margin-left: 8px;
}

.task-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.task-content {
  padding-top: 15px;
}

.overall-progress {
  margin-bottom: 15px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.file-count {
  color: #909399;
  font-size: 12px;
}

.time-info {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  color: #909399;
  font-size: 12px;
}

.error-info {
  margin-bottom: 15px;
}

.task-details {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.task-summary {
  margin-bottom: 15px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.summary-item {
  display: inline-flex;
  align-items: center;
}



.file-progress-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.section-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.file-count-badge {
  background: #f0f9ff;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.file-progress-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-progress-item {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  transition: all 0.3s ease;
}

.file-progress-item:hover {
  background: #f5f7fa;
  border-color: #d3d4d6;
}

.file-progress-item.file-processing {
  border-left: 3px solid #e6a23c;
  background: #fdf6ec;
}

.file-progress-item.file-completed {
  border-left: 3px solid #67c23a;
  background: #f0f9ff;
}

.file-progress-item.file-failed {
  border-left: 3px solid #f56c6c;
  background: #fef0f0;
}

.file-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.file-name-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.file-name-section i {
  font-size: 14px;
  flex-shrink: 0;
}

.file-name {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
  word-break: break-all;
  flex: 1;
}

.file-status-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.file-progress-percent {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
  min-width: 35px;
  text-align: right;
}

.file-progress-bar {
  margin-bottom: 8px;
}

.file-message {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
  padding: 4px 8px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
}

.file-error {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #f56c6c;
  margin-bottom: 5px;
  padding: 4px 8px;
  background: rgba(245, 108, 108, 0.1);
  border-radius: 4px;
}

.file-update-time {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #909399;
  margin-top: 5px;
}

.file-stage-info {
  margin: 8px 0;
  padding: 6px 10px;
  background: rgba(64, 158, 255, 0.05);
  border-left: 3px solid #409eff;
  border-radius: 0 4px 4px 0;
}

.stage-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stage-name {
  font-size: 12px;
  font-weight: 600;
  color: #409eff;
}

.stage-detail {
  font-size: 11px;
  color: #606266;
  background: rgba(96, 98, 102, 0.1);
  padding: 1px 6px;
  border-radius: 8px;
}

.task-results h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
}

.empty-result {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
}

.result-item.success {
  background: #f0f9ff;
  color: #67c23a;
  border: 1px solid #b3e19d;
}

.result-item.failed {
  background: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.empty-tasks {
  text-align: center;
  padding: 60px 20px;
}

/* Agent进度样式 */
.agent-progress-section {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.agent-progress-section h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.agent-progress-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.agent-progress-item {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.agent-progress-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.agent-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.agent-name {
  font-weight: 600;
  color: #495057;
  flex: 1;
}

.agent-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.agent-status.pending {
  background: #e9ecef;
  color: #6c757d;
}

.agent-status.running {
  background: #fff3cd;
  color: #856404;
}

.agent-status.completed {
  background: #d4edda;
  color: #155724;
}

.agent-status.failed {
  background: #f8d7da;
  color: #721c24;
}

.agent-status.skipped {
  background: #d1ecf1;
  color: #0c5460;
}

.agent-level {
  font-size: 12px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.agent-progress-bar {
  margin: 8px 0;
}

.agent-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6c757d;
}

.agent-message {
  flex: 1;
}

.agent-time {
  font-weight: 500;
  color: #495057;
}

.agent-error {
  margin-top: 8px;
  padding: 8px;
  background: #f8d7da;
  color: #721c24;
  border-radius: 4px;
  font-size: 12px;
}

.agent-error i {
  margin-right: 4px;
}

/* Agent进度条样式 */
.agent-progress-pending .el-progress-bar__outer {
  background-color: #e9ecef;
}

.agent-progress-running .el-progress-bar__inner {
  background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.agent-progress-completed .el-progress-bar__inner {
  background: linear-gradient(90deg, #28a745, #20c997);
}

.agent-progress-failed .el-progress-bar__inner {
  background: linear-gradient(90deg, #dc3545, #e83e8c);
}

.agent-progress-skipped .el-progress-bar__inner {
  background: linear-gradient(90deg, #17a2b8, #6f42c1);
}

/* Agent状态项样式 */
.agent-status-pending {
  border-left: 4px solid #6c757d;
}

.agent-status-running {
  border-left: 4px solid #ffc107;
  animation: pulse 2s infinite;
}

.agent-status-completed {
  border-left: 4px solid #28a745;
}

.agent-status-failed {
  border-left: 4px solid #dc3545;
}

.agent-status-skipped {
  border-left: 4px solid #17a2b8;
}

@keyframes pulse {
  0% { border-left-color: #ffc107; }
  50% { border-left-color: #fd7e14; }
  100% { border-left-color: #ffc107; }
}

/* Agent工作流进度条样式 */
.agent-workflow-progress {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.agent-progress-segments {
  display: flex;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  overflow: hidden;
  background-color: #ebeef5;
}

.agent-segment {
  position: relative;
  height: 100%;
  border-right: 1px solid #fff;
  background-color: #ebeef5;
  transition: all 0.3s ease;
}

.agent-segment:last-child {
  border-right: none;
}

.agent-segment-fill {
  height: 100%;
  transition: width 0.6s ease, background-color 0.3s ease;
  border-radius: 0;
}

.agent-progress-text {
  font-size: 12px;
  color: #606266;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Agent段状态样式 */
.agent-segment-pending {
  background-color: #e9ecef;
}

.agent-segment-running {
  background-color: #fff3cd;
  animation: agentPulse 2s infinite;
}

.agent-segment-completed {
  background-color: #d4edda;
}

.agent-segment-failed {
  background-color: #f8d7da;
}

.agent-segment-skipped {
  background-color: #d1ecf1;
}

@keyframes agentPulse {
  0% { background-color: #fff3cd; }
  50% { background-color: #ffeaa7; }
  100% { background-color: #fff3cd; }
}

/* 传统进度条样式保持不变 */
.traditional-progress {
  width: 100%;
}

/* HNGPT进度容器样式 */
.hngpt-progress-container {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0px;
}

/* 统一项目进度条样式 */
.unified-project-progress {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
}

.unified-progress-info {
  font-size: 12px;
  color: #909399;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  padding: 2px 4px;
  margin-left: 6px;
}

/* 文件处理进度条样式 */
.file-processing-progress {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
}

.file-progress-info {
  font-size: 11px;
  color: #909399;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  padding: 2px 4px;
}

/* 文件进度条状态样式 */
.file-progress-running .el-progress-bar__inner {
  background: linear-gradient(90deg, #409eff, #67c23a);
}

.file-progress-completed .el-progress-bar__inner {
  background: linear-gradient(90deg, #67c23a, #85ce61);
}

.file-progress-failed .el-progress-bar__inner {
  background: linear-gradient(90deg, #f56c6c, #f78989);
}

/* 项目级工作流进度条样式 */
.project-workflow-progress {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
}

.project-progress-segments {
  display: flex;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  overflow: hidden;
  background-color: #ebeef5;
}

.project-stage-segment {
  position: relative;
  height: 100%;
  border-right: 1px solid #fff;
  background-color: #ebeef5;
  transition: all 0.3s ease;
}

.project-stage-segment:last-child {
  border-right: none;
}

.project-stage-fill {
  height: 100%;
  transition: width 0.6s ease, background-color 0.3s ease;
  border-radius: 0;
}

.project-progress-text {
  font-size: 12px;
  color: #606266;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  padding: 2px 4px;
}

/* 项目阶段状态样式 */
.project-stage-pending {
  background-color: #e9ecef;
}

.project-stage-running {
  background-color: #fff3cd;
  animation: projectPulse 2s infinite;
}

.project-stage-completed {
  background-color: #d4edda;
}

.project-stage-failed {
  background-color: #f8d7da;
}

@keyframes projectPulse {
  0% { background-color: #fff3cd; }
  50% { background-color: #ffeaa7; }
  100% { background-color: #fff3cd; }
}

/* HNGPT文件进度样式 */
.hngpt-file-progress {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.hngpt-file-item {
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.file-name {
  font-weight: 600;
  color: #495057;
  flex: 1;
}

.file-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.file-status.processing {
  background: #fff3cd;
  color: #856404;
}

.file-status.completed {
  background: #d4edda;
  color: #155724;
}

.file-status.failed {
  background: #f8d7da;
  color: #721c24;
}

.file-workflow-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-stage-segments {
  display: flex;
  width: 100%;
  height: 4px;
  border-radius: 2px;
  overflow: hidden;
  background-color: #ebeef5;
}

.file-stage-segment {
  position: relative;
  height: 100%;
  width: 25%; /* 4个阶段，每个25% */
  border-right: 1px solid #fff;
  background-color: #ebeef5;
  transition: all 0.3s ease;
}

.file-stage-segment:last-child {
  border-right: none;
}

.file-stage-fill {
  height: 100%;
  transition: width 0.6s ease, background-color 0.3s ease;
  border-radius: 0;
}

.file-stage-text {
  font-size: 11px;
  color: #6c757d;
  text-align: left;
}

/* 文件阶段状态样式 */
.file-stage-pending {
  background-color: #e9ecef;
}

.file-stage-running {
  background-color: #fff3cd;
  animation: filePulse 1.5s infinite;
}

.file-stage-completed {
  background-color: #d4edda;
}

.file-stage-failed {
  background-color: #f8d7da;
}

@keyframes filePulse {
  0% { background-color: #fff3cd; }
  50% { background-color: #ffeaa7; }
  100% { background-color: #fff3cd; }
}

.file-error {
  margin-top: 8px;
  padding: 8px;
  background: #f8d7da;
  color: #721c24;
  border-radius: 4px;
  font-size: 12px;
}

.file-error i {
  margin-right: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tasks-container {
    padding: 10px;
  }

  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .task-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .task-info {
    justify-content: center;
  }

  .time-info {
    flex-direction: column;
    gap: 5px;
  }

  .connection-status {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  /* 头部响应式设计 */
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header-left {
    align-self: flex-start;
  }

  .header-right {
    align-self: flex-end;
  }
}

/* 确保表格单元格不会截断内容 */
::v-deep .el-table__body-wrapper {
  overflow-x: auto;
}

::v-deep .el-table__cell {
  overflow: visible !important;
}

::v-deep .cell {
  overflow: visible !important;
  white-space: nowrap;
}
</style>
