<template>
  <div class="permission-approval">
    <div class="page-header">
      <h2>权限审核管理</h2>
      <p>管理用户的文档访问权限申请</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filterForm" inline size="small">
        <el-form-item label="申请状态">
          <el-select v-model="filterForm.status" placeholder="全部状态" clearable>
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="权限类型">
          <el-select v-model="filterForm.permission_type" placeholder="全部类型" clearable>
            <el-option label="查看权限" value="read" />
            <el-option label="下载权限" value="download" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="申请人">
          <el-input 
            v-model="filterForm.requester_name" 
            placeholder="输入申请人姓名"
            clearable
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="loadPermissionRequests">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 权限申请列表 -->
    <el-card class="table-card" shadow="never">
      <el-table 
        :data="permissionRequests" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="申请ID" width="80" />
        
        <el-table-column label="申请人" width="120">
          <template slot-scope="scope">
            <div>
              <div>{{ scope.row.requester_name }}</div>
              <div class="text-muted">{{ scope.row.requester_email }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="文档信息" min-width="200">
          <template slot-scope="scope">
            <div>
              <div class="document-title">{{ scope.row.document_title }}</div>
              <div class="text-muted">ID: {{ scope.row.document_id }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="权限类型" width="100">
          <template slot-scope="scope">
            <el-tag 
              :type="scope.row.permission_type === 'read' ? 'info' : 'warning'"
              size="small"
            >
              {{ getPermissionTypeText(scope.row.permission_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="申请理由" min-width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.reason" placement="top">
              <div class="reason-text">{{ scope.row.reason }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag 
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="申请时间" width="150">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="审核人" width="120">
          <template slot-scope="scope">
            {{ scope.row.reviewer_name || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <div v-if="scope.row.status === 'pending'">
              <el-button 
                type="success" 
                size="mini"
                @click="handleApprove(scope.row)"
              >
                通过
              </el-button>
              <el-button 
                type="danger" 
                size="mini"
                @click="handleReject(scope.row)"
              >
                拒绝
              </el-button>
            </div>
            <div v-else>
              <el-button 
                type="text" 
                size="mini"
                @click="viewDetails(scope.row)"
              >
                查看详情
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>

    <!-- 审核对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      @close="resetDialog"
    >
      <el-form :model="reviewForm" :rules="reviewRules" ref="reviewForm">
        <el-form-item label="审核意见" prop="comment">
          <el-input
            v-model="reviewForm.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见（可选）"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          :type="reviewForm.action === 'approve' ? 'success' : 'danger'"
          @click="confirmReview"
          :loading="reviewLoading"
        >
          确认{{ reviewForm.action === 'approve' ? '通过' : '拒绝' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PermissionApproval',
  data() {
    return {
      filterForm: {
        status: '',
        permission_type: '',
        requester_name: ''
      },
      permissionRequests: [],
      loading: false,
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      dialogVisible: false,
      dialogTitle: '',
      reviewForm: {
        id: null,
        action: '', // 'approve' or 'reject'
        comment: ''
      },
      reviewRules: {
        comment: [
          { max: 500, message: '审核意见不能超过500个字符', trigger: 'blur' }
        ]
      },
      reviewLoading: false
    }
  },
  mounted() {
    this.loadPermissionRequests()
  },
  methods: {
    async loadPermissionRequests() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.page,
          size: this.pagination.size,
          ...this.filterForm
        }
        
        const response = await this.$http.get('/api/permissions/pending-requests', { params })
        
        this.permissionRequests = response.data.records || []
        this.pagination.total = response.data.total || 0
        
      } catch (error) {
        console.error('获取权限申请列表失败:', error)
        this.$message.error('获取权限申请列表失败')
      } finally {
        this.loading = false
      }
    },
    
    handleApprove(row) {
      this.dialogTitle = '通过权限申请'
      this.reviewForm.id = row.id
      this.reviewForm.action = 'approve'
      this.reviewForm.comment = ''
      this.dialogVisible = true
    },
    
    handleReject(row) {
      this.dialogTitle = '拒绝权限申请'
      this.reviewForm.id = row.id
      this.reviewForm.action = 'reject'
      this.reviewForm.comment = ''
      this.dialogVisible = true
    },
    
    async confirmReview() {
      try {
        await this.$refs.reviewForm.validate()
        this.reviewLoading = true
        
        const data = {
          action: this.reviewForm.action,
          comment: this.reviewForm.comment
        }
        
        await this.$http.post(`/auth/permission-requests/${this.reviewForm.id}/review`, data)
        
        this.$message.success(`权限申请已${this.reviewForm.action === 'approve' ? '通过' : '拒绝'}`)
        this.dialogVisible = false
        this.loadPermissionRequests()
        
      } catch (error) {
        console.error('审核失败:', error)
        this.$message.error('审核失败，请重试')
      } finally {
        this.reviewLoading = false
      }
    },
    
    viewDetails(row) {
      // 查看详情逻辑
      this.$message.info('查看详情功能待实现')
    },
    
    resetFilter() {
      this.filterForm = {
        status: '',
        permission_type: '',
        requester_name: ''
      }
      this.pagination.page = 1
      this.loadPermissionRequests()
    },
    
    resetDialog() {
      this.reviewForm = {
        id: null,
        action: '',
        comment: ''
      }
      this.$refs.reviewForm?.resetFields()
    },
    
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.loadPermissionRequests()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadPermissionRequests()
    },
    
    getPermissionTypeText(type) {
      const map = {
        'read': '查看',
        'download': '下载'
      }
      return map[type] || type
    },
    
    getStatusText(status) {
      const map = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝'
      }
      return map[status] || status
    },
    
    getStatusType(status) {
      const map = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger'
      }
      return map[status] || 'info'
    },
    
    formatDate(dateString) {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.permission-approval {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  
  h2 {
    margin: 0 0 8px 0;
    color: #333;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  .document-title {
    font-weight: 500;
    color: #333;
  }
  
  .text-muted {
    color: #999;
    font-size: 12px;
  }
  
  .reason-text {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
