<template>
  <div class="register-container">
    <div class="register-wrapper">
      <!-- 左侧信息区域 -->
      <div class="register-info">
        <div class="logo">
          <i class="el-icon-document"></i>
          <h1>文档管理系统</h1>
        </div>
        <p class="description">
          加入我们的文档管理平台<br>
          享受安全、高效的文档协作体验
        </p>
      </div>

      <!-- 右侧注册表单 -->
      <div class="register-form-wrapper">
        <div class="register-form">
          <h2>用户注册</h2>
          
          <el-form 
            ref="registerForm" 
            :model="registerForm" 
            :rules="registerRules"
            @submit.native.prevent="handleRegister"
          >
            <el-form-item prop="username">
              <el-input
                v-model="registerForm.username"
                placeholder="请输入用户名"
                prefix-icon="el-icon-user"
                size="large"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="email">
              <el-input
                v-model="registerForm.email"
                placeholder="请输入邮箱"
                prefix-icon="el-icon-message"
                size="large"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="registerForm.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="el-icon-lock"
                size="large"
                show-password
              />
            </el-form-item>
            
            <el-form-item prop="confirmPassword">
              <el-input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="请确认密码"
                prefix-icon="el-icon-lock"
                size="large"
                show-password
                @keyup.enter.native="handleRegister"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                size="large" 
                :loading="loading"
                @click="handleRegister"
                class="register-btn"
              >
                {{ loading ? '注册中...' : '立即注册' }}
              </el-button>
            </el-form-item>
          </el-form>

          <div class="register-footer">
            <span>已有账号？</span>
            <el-button type="text" @click="goToLogin">立即登录</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  name: 'Register',
  data() {
    // 自定义验证规则
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.registerForm.password) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    
    return {
      registerForm: {
        username: '',
        email: '',
        password: '',
        confirmPassword: ''
      },
      registerRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 30, message: '用户名长度在 3 到 30 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  methods: {
    ...mapActions('user', ['register']),
    
    async handleRegister() {
      try {
        await this.$refs.registerForm.validate()
        this.loading = true

        const { confirmPassword, ...registerData } = this.registerForm
        await this.register(registerData)

        this.$message.success('注册成功！请登录')
        this.$router.push('/login')

      } catch (error) {
        console.error('注册失败:', error)
        const errorMessage = error.response?.data?.detail || error.message || '注册失败，请重试'
        this.$message.error(errorMessage)
      } finally {
        this.loading = false
      }
    },
    
    goToLogin() {
      this.$router.push('/login')
    }
  }
}
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-wrapper {
  display: flex;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
  min-height: 600px;
}

.register-info {
  flex: 1;
  background: linear-gradient(135deg, #3cb3ff 0%, #2196f3 100%);
  color: white;
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  
  .logo {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    
    i {
      font-size: 48px;
      margin-right: 15px;
    }
    
    h1 {
      font-size: 28px;
      margin: 0;
      font-weight: 300;
    }
  }
  
  .description {
    font-size: 16px;
    line-height: 1.6;
    opacity: 0.9;
  }
}

.register-form-wrapper {
  flex: 1.2;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
}

.register-form {
  width: 100%;
  max-width: 400px;
  
  h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-weight: 400;
  }
  
  .register-btn {
    width: 100%;
    height: 48px;
    font-size: 16px;
  }
  
  .register-footer {
    text-align: center;
    margin-top: 20px;
    color: #666;
    
    span {
      margin-right: 8px;
    }
  }
}

@media (max-width: 768px) {
  .register-wrapper {
    flex-direction: column;
    max-width: 400px;
  }
  
  .register-info {
    padding: 30px 20px;
    text-align: center;
    
    .logo h1 {
      font-size: 24px;
    }
    
    .description {
      font-size: 14px;
    }
  }
  
  .register-form-wrapper {
    padding: 30px 20px;
  }
}
</style>
