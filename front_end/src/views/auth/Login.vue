<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- 左侧信息区域 -->
      <div class="login-info">
        <div class="logo">
          <i class="el-icon-document"></i>
          <h1>文档管理系统</h1>
        </div>
        <p class="description">
          安全、高效的企业级文档管理解决方案<br>
          支持多部门协作，细粒度权限控制
        </p>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-form-wrapper">
        <div class="login-form">
          <h2>用户登录</h2>
          
          <el-form 
            ref="loginForm" 
            :model="loginForm" 
            :rules="loginRules"
            @submit.native.prevent="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                prefix-icon="el-icon-user"
                size="large"
                clearable
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="el-icon-lock"
                size="large"
                show-password
                @keyup.enter.native="handleLogin"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                size="large" 
                :loading="loading"
                @click="handleLogin"
                class="login-btn"
              >
                {{ loading ? '登录中...' : '立即登录' }}
              </el-button>
            </el-form-item>
          </el-form>

          <div class="login-footer">
            <span>还没有账号？</span>
            <el-button type="text" @click="goToRegister">立即注册</el-button>
          </div>


        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 30, message: '用户名长度在 3 到 30 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  methods: {
    ...mapActions('user', ['login']),
    
    async handleLogin() {
      try {
        await this.$refs.loginForm.validate()
        this.loading = true
        
        await this.login(this.loginForm)
        
        this.$message.success('登录成功！')
        
        // 跳转到首页或之前访问的页面
        const redirect = this.$route.query.redirect || '/'
        // 使用replace避免路由守卫重复检查
        this.$router.replace(redirect)
        
      } catch (error) {
        console.error('登录失败:', error)
        this.$message.error(error.message || '登录失败，请检查用户名和密码')
      } finally {
        this.loading = false
      }
    },
    

    
    goToRegister() {
      this.$router.push('/register')
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-wrapper {
  display: flex;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 900px;
  width: 100%;
  min-height: 500px;
}

.login-info {
  flex: 1;
  background: linear-gradient(135deg, #3cb3ff 0%, #2196f3 100%);
  color: white;
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  
  .logo {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    
    i {
      font-size: 48px;
      margin-right: 15px;
    }
    
    h1 {
      font-size: 28px;
      margin: 0;
      font-weight: 300;
    }
  }
  
  .description {
    font-size: 16px;
    line-height: 1.6;
    opacity: 0.9;
  }
}

.login-form-wrapper {
  flex: 1;
  padding: 60px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-form {
  width: 100%;
  max-width: 320px;
  
  h2 {
    text-align: center;
    margin-bottom: 40px;
    color: #333;
    font-weight: 400;
  }
  
  .login-btn {
    width: 100%;
    height: 48px;
    font-size: 16px;
  }
  
  .login-footer {
    text-align: center;
    margin-top: 20px;
    color: #666;
    
    span {
      margin-right: 8px;
    }
  }
  

}

@media (max-width: 768px) {
  .login-wrapper {
    flex-direction: column;
    max-width: 400px;
  }
  
  .login-info {
    padding: 40px 30px;
    text-align: center;
    
    .logo h1 {
      font-size: 24px;
    }
    
    .description {
      font-size: 14px;
    }
  }
  
  .login-form-wrapper {
    padding: 40px 30px;
  }
}
</style>
