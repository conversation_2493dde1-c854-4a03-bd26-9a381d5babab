<template>
  <div class="permission-request">
    <div class="page-header">
      <h2>权限申请</h2>
      <p>申请文档访问权限</p>
    </div>

    <!-- 申请表单 -->
    <el-card class="request-card" shadow="never">
      <div slot="header">
        <span>申请文档访问权限</span>
      </div>
      
      <el-form 
        :model="requestForm" 
        :rules="requestRules" 
        ref="requestForm"
        label-width="120px"
      >
        <el-form-item label="文档ID" prop="document_id">
          <el-input
            v-model="requestForm.document_id"
            placeholder="请输入文档ID"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="权限类型" prop="permission_type">
          <el-radio-group v-model="requestForm.permission_type">
            <el-radio label="read">查看权限</el-radio>
            <el-radio label="download">下载权限</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="申请理由" prop="reason">
          <el-input
            v-model="requestForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请详细说明申请该权限的理由"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="submitRequest"
            :loading="submitting"
          >
            提交申请
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 我的申请记录 -->
    <el-card class="history-card" shadow="never">
      <div slot="header">
        <span>我的申请记录</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text"
          @click="loadMyRequests"
        >
          刷新
        </el-button>
      </div>
      
      <el-table 
        :data="myRequests" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="申请ID" width="80" />
        
        <el-table-column label="文档信息" min-width="200">
          <template slot-scope="scope">
            <div>
              <div class="document-title">{{ scope.row.document_title || `文档ID: ${scope.row.document_id}` }}</div>
              <div class="text-muted">ID: {{ scope.row.document_id }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="权限类型" width="100">
          <template slot-scope="scope">
            <el-tag 
              :type="scope.row.permission_type === 'read' ? 'info' : 'warning'"
              size="small"
            >
              {{ getPermissionTypeText(scope.row.permission_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="申请理由" min-width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.reason" placement="top">
              <div class="reason-text">{{ scope.row.reason }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag 
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="申请时间" width="150">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="审核人" width="120">
          <template slot-scope="scope">
            {{ scope.row.reviewer_name || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="审核意见" min-width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.review_comment" placement="top">
              <div class="comment-text">{{ scope.row.review_comment || '-' }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <div v-if="scope.row.status === 'pending'">
              <el-button 
                type="text" 
                size="mini"
                @click="withdrawRequest(scope.row)"
              >
                撤回
              </el-button>
              <el-button 
                type="text" 
                size="mini"
                @click="deleteRequest(scope.row)"
                style="color: #f56c6c"
              >
                删除
              </el-button>
            </div>
            <div v-else>
              <el-button 
                type="text" 
                size="mini"
                @click="deleteRequest(scope.row)"
                style="color: #f56c6c"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'PermissionRequest',
  data() {
    return {
      requestForm: {
        document_id: '',
        permission_type: 'read',
        reason: ''
      },
      requestRules: {
        document_id: [
          { required: true, message: '请输入文档ID', trigger: 'blur' },
          { pattern: /^\d+$/, message: '文档ID必须是数字', trigger: 'blur' }
        ],
        permission_type: [
          { required: true, message: '请选择权限类型', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入申请理由', trigger: 'blur' },
          { min: 10, max: 500, message: '申请理由长度在 10 到 500 个字符', trigger: 'blur' }
        ]
      },
      submitting: false,
      myRequests: [],
      loading: false,
      pagination: {
        page: 1,
        size: 20,
        total: 0
      }
    }
  },
  mounted() {
    this.loadMyRequests()
    
    // 如果URL中有document_id参数，自动填入
    const documentId = this.$route.query.document_id
    if (documentId) {
      this.requestForm.document_id = documentId
    }
  },
  methods: {
    async submitRequest() {
      try {
        await this.$refs.requestForm.validate()
        this.submitting = true
        
        await this.$http.post('/api/permissions/request', this.requestForm)
        
        this.$message.success('权限申请提交成功，请等待审核')
        this.resetForm()
        this.loadMyRequests()
        
      } catch (error) {
        console.error('提交申请失败:', error)
        this.$message.error(error.response?.data?.detail || '提交申请失败，请重试')
      } finally {
        this.submitting = false
      }
    },
    
    resetForm() {
      this.$refs.requestForm.resetFields()
      this.requestForm = {
        document_id: '',
        permission_type: 'read',
        reason: ''
      }
    },
    
    async loadMyRequests() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.page,
          size: this.pagination.size
        }
        
        const response = await this.$http.get('/api/permissions/my-requests', { params })
        
        this.myRequests = response.data.records || []
        this.pagination.total = response.data.total || 0
        
      } catch (error) {
        console.error('获取申请记录失败:', error)
        this.$message.error('获取申请记录失败')
      } finally {
        this.loading = false
      }
    },
    
    async withdrawRequest(row) {
      try {
        await this.$confirm('确定要撤回这个申请吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await this.$http.post(`/auth/permission-requests/${row.id}/withdraw`)
        
        this.$message.success('申请已撤回')
        this.loadMyRequests()
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('撤回申请失败:', error)
          this.$message.error('撤回申请失败')
        }
      }
    },
    
    async deleteRequest(row) {
      try {
        await this.$confirm('确定要删除这个申请记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await this.$http.delete(`/auth/permission-requests/${row.id}`)
        
        this.$message.success('申请记录已删除')
        this.loadMyRequests()
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除申请失败:', error)
          this.$message.error('删除申请失败')
        }
      }
    },
    
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.loadMyRequests()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadMyRequests()
    },
    
    getPermissionTypeText(type) {
      const map = {
        'read': '查看',
        'download': '下载'
      }
      return map[type] || type
    },
    
    getStatusText(status) {
      const map = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝',
        'withdrawn': '已撤回'
      }
      return map[status] || status
    },
    
    getStatusType(status) {
      const map = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'withdrawn': 'info'
      }
      return map[status] || 'info'
    },
    
    formatDate(dateString) {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.permission-request {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  
  h2 {
    margin: 0 0 8px 0;
    color: #333;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.request-card {
  margin-bottom: 20px;
}

.history-card {
  .document-title {
    font-weight: 500;
    color: #333;
  }
  
  .text-muted {
    color: #999;
    font-size: 12px;
  }
  
  .reason-text, .comment-text {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
