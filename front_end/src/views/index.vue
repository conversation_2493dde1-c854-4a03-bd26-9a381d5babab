<template>
  <div class="index">
    <div class="search-container">
      <div class="search-with-tasks">
        <div class="search-title">
          <h1>档案检索</h1>
        </div>
        <div class="search-input-group">
          <el-input
            placeholder="请输入关键字"
            v-model="queryForm.keyword"
            class="search-input"
            @keydown.enter.native="toSearch"
            size="large"
          >
            <el-select
              slot="prepend"
              v-model="queryForm.type"
              placeholder="请选择"
              style="width: 112px"
            >
              <el-option label="综合搜索" value="1"></el-option>
              <el-option label="精确搜索" value="3"></el-option>
            </el-select>
            <template slot="append">
              <div class="button-group">
                <el-button icon="el-icon-search" @click="toSearch" class="search-btn">
                </el-button>
                <el-button
                  icon="el-icon-s-order"
                  @click="goToTasks"
                  class="tasks-btn"
                  title="任务查看"
                >
                </el-button>
              </div>
            </template>
          </el-input>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import searchInput from '@/components/searchInput.vue'
export default {
  name: 'index',
  components: {
    searchInput,
  },
  data() {
    return {
      queryForm: {
        keyword: '',
        type: '1',
      },
    }
  },

  mounted() {
    let { userName, email } = this.$route.query
    if (userName && email) {
      localStorage.setItem('userName', userName)
      localStorage.setItem('email', email)
      this.$store.dispatch('setEamil', email)
    }

    // 页面加载完成后同步按钮高度，使用多次重试确保成功
    this.$nextTick(() => {
      this.syncButtonHeights()
      // iframe环境可能需要额外时间，添加延迟重试
      setTimeout(() => this.syncButtonHeights(), 100)
      setTimeout(() => this.syncButtonHeights(), 300)
      setTimeout(() => this.syncButtonHeights(), 500)
    })

    // 监听窗口大小变化
    window.addEventListener('resize', this.syncButtonHeights)
  },

  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('resize', this.syncButtonHeights)
  },
  methods: {
    // 跳转到任务管理页面
    goToTasks() {
      this.$router.push('/tasks')
    },
    // 验证按钮对齐 - 现在两个按钮都在同一个容器内，应该自动对齐
    syncButtonHeights() {
      this.$nextTick(() => {
        //console.log('按钮现在都在同一个Element UI容器内，应该自动对齐')
      })
    },

    toSearch() {
      // 检查用户是否已登录
      const isLoggedIn = this.$store.getters['user/isLoggedIn']
      const hasToken = this.$store.getters['user/hasToken']

      if (!isLoggedIn || !hasToken) {
        // 用户未登录，跳转到登录页
        this.$router.push('/login')
        return
      }

      // 检查是否有搜索关键字
      if (!this.queryForm.keyword.trim()) {
        return
      }

      // 关联当前用户ID到搜索参数
      const currentUser = this.$store.getters['user/userInfo']
      const userKey = currentUser?.user_id || currentUser?.id || 'anonymous'

      const queryWithUser = { ...this.queryForm, _userId: userKey }
      localStorage.setItem('queryParams', JSON.stringify(queryWithUser))

      this.$router.push({
        path: '/search',
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.index {
  height: 100vh;
  position: relative;

  .search-container {
    padding-top: 30vh;
    display: flex;
    flex-direction: column;
    align-items: center;

    .search-with-tasks {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      max-width: 800px;

      .search-title {
        h1 {
          color: #3cb3ff;
          margin: 30px;
          font-size: 32px;
        }
      }

      .search-input-group {
        .search-input {
          width: 500px;

          // 按钮组样式 - 两个按钮都在同一个append容器内
          :deep(.el-input-group__append) {
            padding: 0;

            .button-group {
              display: flex;
              height: 100%;

              .search-btn {
                border-radius: 0;
                border-right: 1px solid #dcdfe6; // 添加右边框分隔
                margin: 0;
                position: relative;

                // 添加微妙的右侧阴影增强分隔效果
                &::after {
                  content: '';
                  position: absolute;
                  right: -1px;
                  top: 2px;
                  bottom: 2px;
                  width: 1px;
                  background: rgba(0, 0, 0, 0.06);
                  pointer-events: none;
                }

                &:hover {
                  border-right-color: #c6e2ff; // hover时边框颜色也要变化

                  &::after {
                    background: rgba(64, 158, 255, 0.1); // hover时阴影也变化
                  }
                }
              }

              .tasks-btn {
                border-radius: 0 4px 4px 0;
                border-left: 1px solid #dcdfe6; // 添加左边框分隔
                margin: 0;
                width: 40px;
                padding: 0;

                &:hover {
                  color: #409eff;
                  border-color: #c6e2ff;
                  border-left-color: #c6e2ff; // hover时左边框也要变化
                  background-color: #ecf5ff;
                }

                &:focus {
                  outline: none;
                  color: #409eff;
                  border-color: #409eff;
                  border-left-color: #409eff; // focus时左边框也要变化
                }
              }
            }
          }
        }

        @media (max-width: 768px) {
          .search-input {
            width: 350px;

            :deep(.el-input-group__append) {
              .button-group {
                .tasks-btn {
                  height: 36px !important;
                  width: 36px !important;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
