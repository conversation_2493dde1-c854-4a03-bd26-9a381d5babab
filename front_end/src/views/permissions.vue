<template>
  <div class="permissions-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>权限管理</h1>
      <p>管理您的文档访问权限申请</p>
    </div>

    <!-- 登录状态提示 -->
    <div v-if="!hasValidAuth" class="auth-notice">
      <el-alert
        title="需要登录"
        type="warning"
        :closable="false"
        show-icon>
        <template slot="default">
          <p>您需要登录才能使用权限管理功能。</p>
          <p>请通过服务A登录进行认证。</p>
        </template>
      </el-alert>
    </div>

    <!-- 用户角色信息显示 -->
    <div class="user-role-info" v-if="currentUser">
      <el-tag :type="userRoleType === 'system_admin' ? 'danger' : userRoleType === 'department_admin' ? 'warning' : 'info'">
        <i :class="userRoleType === 'system_admin' ? 'el-icon-crown' : userRoleType === 'department_admin' ? 'el-icon-user-solid' : 'el-icon-user'"></i>
        {{ userRoleDisplayName }}
      </el-tag>
      <span class="user-name">{{ currentUser.nick_name || currentUser.user_name }}</span>
      <span class="user-dept" v-if="currentUser.dept_name">({{ currentUser.dept_name }})</span>

      <!-- 权限说明 -->
      <div class="permission-hint">
        <el-tooltip :content="permissionHint" placement="top">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
    </div>

    <!-- 动态标签页切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 系统管理员：待审批 -->
      <el-tab-pane
        v-if="isSystemAdmin"
        label="待审批"
        name="pending-approval"
      >
        <div class="tab-content">
          <div class="filter-bar">
            <el-button @click="loadPendingApproval" icon="el-icon-refresh">刷新</el-button>
            <el-tag type="warning" class="stats-tag">
              等待我审批: {{ pendingApproval.length }} 项
            </el-tag>
          </div>

          <el-table
            :data="pendingApproval"
            v-loading="loading.pendingApproval"
            style="width: 100%"
            empty-text="暂无待审批申请"
          >
            <el-table-column prop="requester_name" label="申请人" width="120">
              <template slot-scope="scope">
                <div>
                  <div>{{ scope.row.requester_name }}</div>
                  <el-tag size="mini" type="info" v-if="scope.row.requester_dept">
                    {{ scope.row.requester_dept }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="document_title" label="文档名称" min-width="200"></el-table-column>
            <el-table-column prop="permission_type" label="权限类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.permission_type === 'read' ? 'primary' : 'success'">
                  {{ scope.row.permission_type === 'read' ? '查看' : '下载' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="申请理由" min-width="150"></el-table-column>
            <el-table-column prop="created_at" label="申请时间" width="150">
              <template slot-scope="scope">
                {{ formatTime(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="success"
                  @click="approveRequest(scope.row.id, true)"
                >
                  批准
                </el-button>
                <el-button
                  size="mini"
                  type="danger"
                  @click="approveRequest(scope.row.id, false)"
                >
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 系统管理员：已审批 -->
      <el-tab-pane
        v-if="isSystemAdmin"
        label="已审批"
        name="my-approved"
      >
        <div class="tab-content">
          <div class="filter-bar">
            <el-button @click="loadMyApproved" icon="el-icon-refresh">刷新</el-button>
            <el-tag type="success" class="stats-tag">
              我已审批: {{ myApproved.length }} 项
            </el-tag>
          </div>

          <el-table
            :data="myApproved"
            v-loading="loading.myApproved"
            style="width: 100%"
            empty-text="暂无已审批记录"
          >
            <el-table-column prop="requester_name" label="申请人" width="120">
              <template slot-scope="scope">
                <div>
                  <div>{{ scope.row.requester_name }}</div>
                  <el-tag size="mini" type="info" v-if="scope.row.requester_dept">
                    {{ scope.row.requester_dept }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="document_title" label="文档名称" min-width="200"></el-table-column>
            <el-table-column prop="permission_type" label="权限类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.permission_type === 'read' ? 'primary' : 'success'">
                  {{ scope.row.permission_type === 'read' ? '查看' : '下载' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="审批结果" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 'approved' ? 'success' : 'danger'">
                  {{ scope.row.status === 'approved' ? '已批准' : '已拒绝' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reviewed_at" label="审批时间" width="150">
              <template slot-scope="scope">
                {{ formatTime(scope.row.reviewed_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="danger"
                  @click="deleteMyApprovedRecord(scope.row.id)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 系统管理员：全部审批记录 -->
      <el-tab-pane
        v-if="isSystemAdmin"
        label="全部审批记录"
        name="all-records"
      >
        <div class="tab-content">
          <!-- 过滤器区域 -->
          <div class="filter-section">
            <el-form :inline="true" :model="allRecordsFilter" class="filter-form">
              <el-form-item label="状态">
                <el-select v-model="allRecordsFilter.status" placeholder="全部状态" clearable style="width: 120px">
                  <el-option label="待审批" value="pending"></el-option>
                  <el-option label="已批准" value="approved"></el-option>
                  <el-option label="已拒绝" value="rejected"></el-option>
                  <el-option label="已撤销" value="withdrawn"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="权限类型">
                <el-select v-model="allRecordsFilter.permission_type" placeholder="全部类型" clearable style="width: 120px">
                  <el-option label="查看" value="read"></el-option>
                  <el-option label="下载" value="download"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="审批人">
                <el-select v-model="allRecordsFilter.reviewer_id" placeholder="全部审批人" clearable style="width: 140px">
                  <el-option label="系统管理员" value="1"></el-option>
                  <el-option label="部门管理员" value="2"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="申请人">
                <el-input
                  v-model="allRecordsFilter.requester_name"
                  placeholder="输入申请人姓名"
                  clearable
                  style="width: 150px"
                ></el-input>
              </el-form-item>

              <el-form-item label="申请时间">
                <el-date-picker
                  v-model="allRecordsFilter.start_date"
                  type="date"
                  placeholder="开始日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 140px"
                ></el-date-picker>
                <span style="margin: 0 10px">至</span>
                <el-date-picker
                  v-model="allRecordsFilter.end_date"
                  type="date"
                  placeholder="结束日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 140px"
                ></el-date-picker>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="searchAllRecords" icon="el-icon-search">搜索</el-button>
                <el-button @click="resetAllRecordsFilter" icon="el-icon-refresh-left">重置</el-button>
                <el-button @click="loadAllRecords" icon="el-icon-refresh">刷新</el-button>
              </el-form-item>
            </el-form>

            <div class="stats-info">
              <el-tag type="info" class="stats-tag">
                共 {{ allRecordsPagination.total }} 条记录，第 {{ allRecordsPagination.page }} / {{ allRecordsPagination.pages }} 页
              </el-tag>
            </div>
          </div>

          <el-table
            :data="allRecords"
            v-loading="loading.allRecords"
            style="width: 100%"
            empty-text="暂无记录"
          >
            <el-table-column prop="requester_name" label="申请人" width="120">
              <template slot-scope="scope">
                <div>
                  <div>{{ scope.row.requester_name }}</div>
                  <el-tag size="mini" type="info" v-if="scope.row.requester_dept">
                    {{ scope.row.requester_dept }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="document_title" label="文档名称" min-width="200"></el-table-column>
            <el-table-column prop="permission_type" label="权限类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.permission_type === 'read' ? 'primary' : 'success'">
                  {{ scope.row.permission_type === 'read' ? '查看' : '下载' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusTagType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reviewer_name" label="审批人" width="120">
              <template slot-scope="scope">
                {{ scope.row.reviewer_name || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="申请时间" width="150">
              <template slot-scope="scope">
                {{ formatTime(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template slot-scope="scope">
                <!-- 待审批状态显示审批按钮 -->
                <template v-if="scope.row.status === 'pending'">
                  <el-button
                    size="mini"
                    type="success"
                    @click="approveRequest(scope.row.id, true)"
                  >
                    批准
                  </el-button>
                  <el-button
                    size="mini"
                    type="danger"
                    @click="approveRequest(scope.row.id, false)"
                  >
                    拒绝
                  </el-button>
                </template>
                <!-- 已批准状态显示撤销和删除按钮 -->
                <template v-else-if="scope.row.status === 'approved'">
                  <el-button
                    size="mini"
                    type="warning"
                    @click="revokePermission(scope.row)"
                  >
                    撤销
                  </el-button>
                  <el-button
                    size="mini"
                    type="danger"
                    @click="deleteRecord(scope.row.id)"
                  >
                    删除
                  </el-button>
                </template>
                <!-- 其他状态只显示删除按钮 -->
                <template v-else>
                  <el-button
                    size="mini"
                    type="danger"
                    @click="deleteRecord(scope.row.id)"
                  >
                    删除
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="pagination-wrapper" v-if="allRecordsPagination.total > 0">
            <el-pagination
              @size-change="handleAllRecordsSizeChange"
              @current-change="handleAllRecordsPageChange"
              :current-page="allRecordsPagination.page"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="allRecordsPagination.size"
              :total="allRecordsPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              background
            ></el-pagination>
          </div>
        </div>
      </el-tab-pane>

      <!-- 系统管理员：部门统计 -->
      <el-tab-pane
        v-if="isSystemAdmin"
        label="部门统计"
        name="department-stats"
      >
        <div class="tab-content">
          <div class="filter-bar">
            <el-button @click="loadDepartmentStats" icon="el-icon-refresh">刷新</el-button>
          </div>

          <el-table
            :data="departmentStats"
            v-loading="loading.departmentStats"
            style="width: 100%"
            empty-text="暂无统计数据"
          >
            <el-table-column prop="dept_name" label="部门名称" width="200"></el-table-column>
            <el-table-column prop="pending_count" label="待审批" width="100" align="center">
              <template slot-scope="scope">
                <el-tag type="warning">{{ scope.row.pending_count || 0 }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="approved_count" label="已批准" width="100" align="center">
              <template slot-scope="scope">
                <el-tag type="success">{{ scope.row.approved_count || 0 }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="rejected_count" label="已拒绝" width="100" align="center">
              <template slot-scope="scope">
                <el-tag type="danger">{{ scope.row.rejected_count || 0 }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="total_count" label="总计" width="100" align="center">
              <template slot-scope="scope">
                <el-tag type="info">{{ scope.row.total_count || 0 }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 部门管理员：待审批（系统管理员不显示） -->
      <el-tab-pane
        v-if="isDepartmentAdmin && !isSystemAdmin"
        label="待审批"
        name="dept-pending"
      >
        <div class="tab-content">
          <div class="filter-bar">
            <el-button @click="loadPendingReviews" icon="el-icon-refresh">刷新</el-button>
            <el-tag type="info" class="stats-tag">
              本部门待审批: {{ pendingReviews.length }} 项
            </el-tag>
          </div>

          <el-table
            :data="pendingReviews"
            v-loading="loading.pendingReviews"
            style="width: 100%"
            empty-text="暂无待审批申请"
          >
            <el-table-column prop="requester_name" label="申请人" width="120"></el-table-column>
            <el-table-column prop="document_title" label="文档名称" min-width="200"></el-table-column>
            <el-table-column prop="permission_type" label="权限类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.permission_type === 'read' ? 'primary' : 'success'">
                  {{ scope.row.permission_type === 'read' ? '查看' : '下载' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="申请理由" min-width="150"></el-table-column>
            <el-table-column prop="created_at" label="申请时间" width="150">
              <template slot-scope="scope">
                {{ formatTime(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="success"
                  @click="approveRequest(scope.row.id, true)"
                >
                  批准
                </el-button>
                <el-button
                  size="mini"
                  type="danger"
                  @click="approveRequest(scope.row.id, false)"
                >
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 部门管理员：已审批（系统管理员不显示） -->
      <el-tab-pane
        v-if="isDepartmentAdmin && !isSystemAdmin"
        label="已审批"
        name="dept-approved-records"
      >
        <div class="tab-content">
          <div class="filter-bar">
            <el-button @click="loadDeptApprovedRecords" icon="el-icon-refresh">刷新</el-button>
            <el-tag type="success" class="stats-tag">
              本部门已审批: {{ deptApprovedRecords.length }} 项
            </el-tag>
          </div>

          <el-table
            :data="deptApprovedRecords"
            v-loading="loading.deptApprovedRecords"
            style="width: 100%"
            empty-text="暂无已审批记录"
          >
            <el-table-column prop="requester_name" label="申请人" width="120"></el-table-column>
            <el-table-column prop="document_title" label="文档名称" min-width="200"></el-table-column>
            <el-table-column prop="permission_type" label="权限类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.permission_type === 'read' ? 'primary' : 'success'">
                  {{ scope.row.permission_type === 'read' ? '查看' : '下载' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="审批结果" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 'approved' ? 'success' : 'danger'">
                  {{ scope.row.status === 'approved' ? '已批准' : '已拒绝' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reviewed_at" label="审批时间" width="150">
              <template slot-scope="scope">
                {{ formatTime(scope.row.reviewed_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="danger"
                  @click="deleteDeptApprovedRecord(scope.row.id)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 我的申请（部门管理员和普通用户有，系统管理员没有） -->
      <el-tab-pane
        v-if="!isSystemAdmin"
        label="我的申请"
        name="my-requests"
      >
        <div class="tab-content">
          <!-- 搜索和筛选 -->
          <div class="filter-bar">
            <el-select v-model="statusFilter" placeholder="状态筛选" clearable @change="loadMyRequests">
              <el-option label="待审批" value="pending"></el-option>
              <el-option label="已批准" value="approved"></el-option>
              <el-option label="已拒绝" value="rejected"></el-option>
            </el-select>
            <el-button @click="loadMyRequests" icon="el-icon-refresh">刷新</el-button>
          </div>

          <!-- 申请列表 -->
          <el-table 
            :data="myRequests" 
            v-loading="loading.myRequests"
            style="width: 100%"
            empty-text="暂无申请记录"
          >
            <el-table-column prop="document_title" label="文档名称" min-width="200">
              <template slot-scope="scope">
                <span :title="scope.row.document_title">{{ scope.row.document_title }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="permission_type" label="权限类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.permission_type === 'read' ? 'primary' : 'success'">
                  {{ scope.row.permission_type === 'read' ? '查看' : '下载' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="申请理由" min-width="150">
              <template slot-scope="scope">
                <span :title="scope.row.reason">{{ scope.row.reason }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag 
                  :type="getStatusType(scope.row.status)"
                  size="small"
                >
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="申请时间" width="150">
              <template slot-scope="scope">
                {{ formatTime(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.status === 'pending'"
                  size="mini"
                  type="warning"
                  @click="withdrawRequest(scope.row.id)"
                >
                  撤销
                </el-button>
                <el-button
                  size="mini"
                  type="danger"
                  @click="deleteRequest(scope.row.id)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>


    </el-tabs>

    <!-- 审批对话框 -->
    <el-dialog
      title="权限申请审批"
      :visible.sync="approvalDialog.visible"
      width="500px"
    >
      <el-form :model="approvalDialog.form" label-width="80px">
        <el-form-item label="审批结果">
          <el-tag :type="approvalDialog.form.approved ? 'success' : 'danger'">
            {{ approvalDialog.form.approved ? '批准' : '拒绝' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="审批意见">
          <el-input
            v-model="approvalDialog.form.comment"
            type="textarea"
            :rows="3"
            placeholder="请输入审批意见（可选）"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="approvalDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitApproval" :loading="loading.approval">确定</el-button>
      </div>
    </el-dialog>

    <!-- 权限申请对话框 -->
    <el-dialog
      title="申请文档访问权限"
      :visible.sync="requestDialog.visible"
      width="600px"
    >
      <el-form :model="requestDialog.form" :rules="requestRules" ref="requestForm" label-width="100px">
        <el-form-item label="文档名称">
          <el-input v-model="requestDialog.documentTitle" disabled></el-input>
        </el-form-item>
        <el-form-item label="权限类型" prop="permission_type">
          <el-radio-group v-model="requestDialog.form.permission_type">
            <el-radio label="read">查看权限</el-radio>
            <el-radio label="download">下载权限</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="申请理由" prop="reason">
          <el-input
            v-model="requestDialog.form.reason"
            type="textarea"
            :rows="4"
            placeholder="请详细说明申请该文档权限的理由"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="requestDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitPermissionRequest" :loading="loading.request">提交申请</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
/**
 * 权限管理页面组件
 * 
 * Vue.js核心概念演示：
 * 1. 组件结构：template + script + style
 * 2. 数据绑定：v-model, v-if, v-for
 * 3. 事件处理：@click, @change
 * 4. 生命周期：created, mounted
 * 5. 计算属性：computed
 * 6. 方法：methods
 */

import {
  getUserPermissionRequests,
  getPendingReviews,
  approvePermissionRequest,
  withdrawPermissionRequest,
  deletePermissionRequest,
  getCurrentUser,
  submitPermissionRequest,
  getDocumentByUrl,
  getMyApprovedRequests,
  getAllPermissionRecords,
  getDepartmentStats
} from '@/api/permission'
import { formatTime } from '@/utils'

export default {
  name: 'Permissions',
  
  // Vue.js核心概念1：数据（data）
  // 组件的响应式数据，当数据变化时，视图会自动更新
  data() {
    return {
      // 当前激活的标签页（将在mounted中根据用户角色动态设置）
      activeTab: 'my-requests',
      
      // 状态筛选
      statusFilter: '',
      
      // 数据列表
      myRequests: [],
      pendingReviews: [], // 部门管理员的待审批申请
      pendingApproval: [], // 系统管理员待审批的申请
      myApproved: [], // 系统管理员已审批的记录
      allRecords: [], // 全部审批记录（待审批+已审批）

      // 全部记录的分页和过滤
      allRecordsFilter: {
        page: 1,
        size: 20,
        status: '',
        permission_type: '',
        reviewer_id: '',
        requester_name: '',
        start_date: '',
        end_date: ''
      },
      allRecordsPagination: {
        page: 1,
        size: 20,
        total: 0,
        pages: 0
      },
      deptApprovedRecords: [], // 部门管理员的已审批记录
      departmentStats: [], // 部门统计数据

      // 加载状态
      loading: {
        myRequests: false,
        pendingReviews: false,
        pendingApproval: false,
        myApproved: false,
        allRecords: false,
        deptApprovedRecords: false,
        departmentStats: false,
        approval: false,
        request: false
      },
      
      // 用户信息
      currentUser: null,
      
      // 审批对话框
      approvalDialog: {
        visible: false,
        requestId: null,
        form: {
          approved: true,
          comment: ''
        }
      },

      // 权限申请对话框
      requestDialog: {
        visible: false,
        documentId: null,
        documentTitle: '',
        documentUrl: '',
        form: {
          permission_type: 'read',
          reason: ''
        }
      },

      // 表单验证规则
      requestRules: {
        permission_type: [
          { required: true, message: '请选择权限类型', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入申请理由', trigger: 'blur' },
          { min: 10, message: '申请理由至少10个字符', trigger: 'blur' }
        ]
      }
    }
  },
  
  // Vue.js核心概念2：计算属性（computed）
  // 基于响应式数据计算得出的值，具有缓存特性
  computed: {
    // 判断当前用户是否为系统管理员
    isSystemAdmin() {
      if (!this.currentUser) return false
      const userRoles = this.currentUser.roles || []
      return userRoles.includes('admin')
    },

    // 判断当前用户是否为部门管理员
    isDepartmentAdmin() {
      if (!this.currentUser || !this.currentUser.dept_id) {
        return false
      }

      // 检查用户是否有deptAdmin角色
      const userRoles = this.currentUser.roles || []
      return userRoles.includes('deptAdmin')
    },

    // 判断当前用户是否为管理员（系统管理员或部门管理员）
    isAdmin() {
      return this.isSystemAdmin || this.isDepartmentAdmin
    },

    // 获取用户角色类型
    userRoleType() {
      if (this.isSystemAdmin) {
        return 'system_admin'
      } else if (this.isDepartmentAdmin) {
        return 'department_admin'
      } else {
        return 'normal_user'
      }
    },

    // 获取用户角色显示名称
    userRoleDisplayName() {
      const roleMap = {
        'system_admin': '系统管理员',
        'department_admin': '部门管理员',
        'normal_user': '普通用户'
      }
      return roleMap[this.userRoleType] || '未知角色'
    },

    // 判断是否有有效的认证
    hasValidAuth() {
      const token = localStorage.getItem('authToken')
      return token && this.currentUser && this.currentUser.user_id
    },

    // 获取当前用户可见的标签页配置
    availableTabs() {
      const baseTabs = []

      if (this.isSystemAdmin) {
        // 系统管理员标签页
        baseTabs.push(
          {
            name: 'pending-approval',
            label: '待审批',
            icon: 'el-icon-time',
            description: '等待自己审批的权限申请'
          },
          {
            name: 'my-approved',
            label: '已审批',
            icon: 'el-icon-check',
            description: '自己已经审批的权限申请记录'
          },
          {
            name: 'all-records',
            label: '全部审批记录',
            icon: 'el-icon-document',
            description: '所有部门的待审批和已审批记录'
          },
          {
            name: 'department-stats',
            label: '部门统计',
            icon: 'el-icon-pie-chart',
            description: '查看各部门的权限申请统计'
          }
        )
      } else if (this.isDepartmentAdmin && !this.isSystemAdmin) {
        // 部门管理员标签页（系统管理员不显示）
        baseTabs.push(
          {
            name: 'dept-pending',
            label: '待审批',
            icon: 'el-icon-time',
            description: '处理本部门用户的权限申请'
          },
          {
            name: 'dept-approved-records',
            label: '已审批',
            icon: 'el-icon-check',
            description: '查看和管理本部门已审批的权限申请记录'
          },
          {
            name: 'my-requests',
            label: '我的申请',
            icon: 'el-icon-document',
            description: '查看自己提交的权限申请'
          }
        )
      } else {
        // 普通用户标签页
        baseTabs.push(
          {
            name: 'my-requests',
            label: '我的申请',
            icon: 'el-icon-document',
            description: '查看和管理自己的权限申请'
          }
        )
      }

      return baseTabs
    },

    // 获取默认标签页
    defaultTab() {
      if (this.isSystemAdmin) {
        return 'pending-approval'  // 系统管理员默认显示待审批
      } else if (this.isDepartmentAdmin) {
        return 'dept-pending'      // 部门管理员默认显示部门待审批
      } else {
        return 'my-requests'       // 普通用户默认显示我的申请
      }
    },

    // 获取权限提示信息（计算属性）
    permissionHint() {
      if (this.isSystemAdmin) {
        return '系统管理员：可以查看和审批所有用户的权限申请，管理全局权限设置，删除已审批记录'
      } else if (this.isDepartmentAdmin) {
        return '部门管理员：可以查看和审批本部门用户的权限申请，管理本部门已审批记录'
      } else {
        return '普通用户：可以查看和管理自己提交的权限申请'
      }
    }
  },
  
  // Vue.js核心概念3：生命周期钩子（created）
  // 组件创建完成后立即执行，此时可以访问数据和方法
  created() {
    // 检查是否有来自搜索页面的权限申请参数
    this.checkRouteParams()

    // 尝试加载用户信息（如果有认证的话）
    this.loadCurrentUser()
  },

  // Vue.js核心概念4：生命周期钩子（mounted）
  // DOM挂载完成后执行，确保标签页正确显示
  mounted() {
    // console.log('🔄 mounted钩子执行')
    // console.log('   - 当前activeTab:', this.activeTab)
    // console.log('   - 当前isAdmin:', this.isAdmin)

    // 确保标签页根据用户角色正确设置
    this.$nextTick(() => {
      // console.log('🔄 $nextTick执行')
      // console.log('   - 当前activeTab:', this.activeTab)
      // console.log('   - 当前isAdmin:', this.isAdmin)

      if (this.isAdmin && this.activeTab !== 'pending-reviews') {
        // console.log('🔄 管理员用户，强制设置为待审批标签页')
        this.activeTab = 'pending-reviews'
        // console.log('🔄 DOM挂载后确认管理员默认标签页: pending-reviews')
      }
    })
  },
  
  // Vue.js核心概念4：方法（methods）
  // 组件的行为逻辑，处理用户交互和业务逻辑
  methods: {
    // 加载当前用户信息
    async loadCurrentUser() {
      try {
        // 首先检查本地存储的用户信息
        const localUser = localStorage.getItem('currentUser')
        if (localUser) {
          this.currentUser = JSON.parse(localUser)
          // console.log('从本地存储加载用户信息:', this.currentUser)
        }

        // 检查是否有认证token
        const authToken = localStorage.getItem('authToken')
        if (authToken) {
          // 有token时才尝试从API获取用户信息
          try {
            const response = await getCurrentUser()
            this.currentUser = response.data || response
            // console.log('从API加载用户信息:', this.currentUser)

            // 保存到本地存储
            localStorage.setItem('currentUser', JSON.stringify(this.currentUser))
          } catch (apiError) {
            console.warn('API获取用户信息失败，使用本地信息:', apiError.message || apiError)

            // 如果API失败但有本地用户信息，继续使用本地信息
            if (!this.currentUser) {
              // 如果token无效，清除它
              if (apiError.response && apiError.response.status === 401) {
                localStorage.removeItem('authToken')
                // console.log('Token无效，已清除')
              }

              // 使用默认测试用户
              this.currentUser = {
                user_id: 1,
                username: 'admin',
                nick_name: '系统管理员',
                email: '<EMAIL>',
                dept_id: 100,
                is_dept_admin: true
              }
              // console.log('使用默认管理员用户:', this.currentUser)
            }
          }
        } else {
          // 没有token时使用默认用户或本地存储的用户
          if (!this.currentUser) {
            this.currentUser = {
              user_id: 1,
              username: 'admin',
              nick_name: '系统管理员',
              email: '<EMAIL>',
              dept_id: 100,
              is_dept_admin: true
            }
            // console.log('无认证token，使用默认管理员用户:', this.currentUser)
          }
        }

        // 根据用户角色设置默认标签页
        // console.log('🔍 用户角色判断:')
        // console.log('   - currentUser:', this.currentUser)
        // console.log('   - user_id:', this.currentUser?.user_id)
        // console.log('   - is_dept_admin:', this.currentUser?.is_dept_admin)
        // console.log('   - isAdmin计算结果:', this.isAdmin)

        // 根据用户角色设置默认标签页和加载数据
        if (this.isSystemAdmin) {
          // 系统管理员默认显示待审批标签页
          this.activeTab = 'pending-approval'
          // console.log('👤 系统管理员，默认显示待审批标签页')
          // console.log('   - 设置activeTab为:', this.activeTab)

          // 加载系统管理员相关数据（不包含我的申请）
          this.loadPendingApproval()
          this.loadMyApproved()
          this.loadAllRecords()
          this.loadDepartmentStats()
        } else if (this.isDepartmentAdmin && !this.isSystemAdmin) {
          // 部门管理员（非系统管理员）默认显示待审批标签页
          this.activeTab = 'dept-pending'
          // console.log('👤 部门管理员，默认显示待审批标签页')
          // console.log('   - 设置activeTab为:', this.activeTab)

          // 加载部门管理员相关数据
          this.loadPendingReviews()
          this.loadDeptApprovedRecords()
          this.loadMyRequests()
        } else {
          // 普通用户默认显示我的申请标签页
          this.activeTab = 'my-requests'
          // console.log('👤 普通用户，默认显示我的申请标签页')
          // console.log('   - 设置activeTab为:', this.activeTab)

          // 只加载我的申请
          this.loadMyRequests()
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)

        // 设置默认用户以确保页面能正常工作
        if (!this.currentUser) {
          this.currentUser = {
            user_id: 1,
            username: 'admin',
            nick_name: '系统管理员',
            email: '<EMAIL>',
            dept_id: 100,
            is_dept_admin: true
          }
          // console.log('异常情况下使用默认管理员用户:', this.currentUser)
        }

        // 显示友好的提示信息而不是错误
        this.$message.info('正在使用测试模式，部分功能可能受限')
      }
    },
    
    // 加载我的申请列表
    async loadMyRequests() {
      this.loading.myRequests = true
      try {
        const params = {}
        if (this.statusFilter) {
          params.status_filter = this.statusFilter
        }

        const response = await getUserPermissionRequests(params)
        // console.log('获取申请列表响应:', response)

        if (response && response.data) {
          this.myRequests = response.data
          // console.log('申请列表数据:', this.myRequests)

          // 验证数据完整性
          this.myRequests.forEach((item, index) => {
            if (!item.id) {
              console.error(`申请项${index}缺少id字段:`, item)
            }
          })
        } else {
          console.warn('API响应格式异常:', response)
          this.myRequests = []
        }
      } catch (error) {
        console.error('加载申请列表失败:', error)
        // 如果是认证错误，显示提示信息而不是错误
        if (error.response && error.response.status === 401) {
          // console.log('需要登录才能查看申请列表')
          this.myRequests = []
        } else {
          this.$message.error('加载申请列表失败')
        }
      } finally {
        this.loading.myRequests = false
      }
    },

    // 加载系统管理员待审批的申请（等待自己审批的）
    async loadPendingApproval() {
      if (!this.isSystemAdmin) {
        console.warn('非系统管理员无权查看待审批申请')
        return
      }

      this.loading.pendingApproval = true
      try {
        const response = await getPendingReviews()
        // 过滤出等待系统管理员审批的申请
        this.pendingApproval = response.data || []
        // console.log('系统管理员加载待审批申请:', this.pendingApproval.length, '项')
      } catch (error) {
        console.error('加载待审批列表失败:', error)
        this.$message.error('加载待审批列表失败')
        this.pendingApproval = []
      } finally {
        this.loading.pendingApproval = false
      }
    },

    // 加载系统管理员已审批的记录（自己审批的）
    async loadMyApproved() {
      if (!this.isSystemAdmin) {
        console.warn('非系统管理员无权查看已审批记录')
        return
      }

      this.loading.myApproved = true
      try {
        // 调用API获取自己已审批的记录
        const response = await getMyApprovedRequests()
        this.myApproved = response.data || []
        // console.log('系统管理员加载已审批记录:', this.myApproved.length, '项')
      } catch (error) {
        console.error('加载已审批记录失败:', error)
        this.$message.error('加载已审批记录失败')
        this.myApproved = []
      } finally {
        this.loading.myApproved = false
      }
    },

    // 加载全部审批记录（包含待审批和已审批）- 支持分页和过滤
    async loadAllRecords() {
      if (!this.isSystemAdmin) {
        console.warn('非系统管理员无权查看全部记录')
        return
      }

      this.loading.allRecords = true
      try {
        // 构建查询参数
        const params = {
          page: this.allRecordsFilter.page,
          size: this.allRecordsFilter.size
        }

        // 添加过滤条件（只有非空值才添加）
        if (this.allRecordsFilter.status) params.status = this.allRecordsFilter.status
        if (this.allRecordsFilter.permission_type) params.permission_type = this.allRecordsFilter.permission_type
        if (this.allRecordsFilter.reviewer_id) params.reviewer_id = this.allRecordsFilter.reviewer_id
        if (this.allRecordsFilter.requester_name) params.requester_name = this.allRecordsFilter.requester_name
        if (this.allRecordsFilter.start_date) params.start_date = this.allRecordsFilter.start_date
        if (this.allRecordsFilter.end_date) params.end_date = this.allRecordsFilter.end_date

        // 调用API获取全部记录
        const response = await getAllPermissionRecords(params)

        if (response.data && typeof response.data === 'object') {
          // 新的分页格式
          this.allRecords = response.data.records || []
          this.allRecordsPagination = response.data.pagination || {
            page: 1, size: 20, total: 0, pages: 0
          }
        } else {
          // 兼容旧格式
          this.allRecords = response.data || []
          this.allRecordsPagination.total = this.allRecords.length
        }

        console.log('系统管理员加载全部记录:', this.allRecords.length, '项，总计:', this.allRecordsPagination.total)
      } catch (error) {
        console.error('加载全部记录失败:', error)
        this.$message.error('加载全部记录失败')
        this.allRecords = []
        this.allRecordsPagination = { page: 1, size: 20, total: 0, pages: 0 }
      } finally {
        this.loading.allRecords = false
      }
    },

    // 加载部门管理员的待审批申请
    async loadPendingReviews() {
      this.loading.pendingReviews = true
      try {
        const response = await getPendingReviews()
        this.pendingReviews = response.data || []
        // console.log('部门管理员加载待审批申请:', this.pendingReviews.length, '项')
      } catch (error) {
        console.error('加载待审批列表失败:', error)
        // 如果是认证错误，显示提示信息而不是错误
        if (error.response && error.response.status === 401) {
          // console.log('需要管理员权限才能查看待审批列表')
          this.pendingReviews = []
        } else {
          this.$message.error('加载待审批列表失败')
        }
      } finally {
        this.loading.pendingReviews = false
      }
    },



    // 加载部门管理员的已审批记录
    async loadDeptApprovedRecords() {
      if (!this.isDepartmentAdmin) {
        console.warn('非部门管理员无权查看部门已审批记录')
        return
      }

      this.loading.deptApprovedRecords = true
      try {
        // 调用真实API获取已审批记录
        const response = await getMyApprovedRequests()
        // console.log('获取已审批记录响应:', response)

        if (response && response.code === 200) {
          this.deptApprovedRecords = response.data || []
          // console.log('部门管理员加载已审批记录:', this.deptApprovedRecords.length, '项')
        } else {
          console.warn('获取已审批记录失败:', response?.msg || '未知错误')
          this.deptApprovedRecords = []
          this.$message.warning('获取已审批记录失败')
        }
      } catch (error) {
        console.error('加载部门已审批记录失败:', error)

        // 如果是认证错误，显示提示信息而不是错误
        if (error.response && error.response.status === 401) {
          // console.log('需要登录才能查看已审批记录')
          this.deptApprovedRecords = []
        } else if (error.response && error.response.status === 403) {
          // console.log('无权限查看已审批记录')
          this.deptApprovedRecords = []
          this.$message.warning('无权限查看已审批记录')
        } else {
          this.$message.error('加载部门已审批记录失败')
          this.deptApprovedRecords = []
        }
      } finally {
        this.loading.deptApprovedRecords = false
      }
    },

    // 搜索全部记录
    searchAllRecords() {
      this.allRecordsFilter.page = 1 // 重置到第一页
      this.loadAllRecords()
    },

    // 重置全部记录过滤器
    resetAllRecordsFilter() {
      this.allRecordsFilter = {
        page: 1,
        size: 20,
        status: '',
        permission_type: '',
        reviewer_id: '',
        requester_name: '',
        start_date: '',
        end_date: ''
      }
      this.loadAllRecords()
    },

    // 处理全部记录分页大小变化
    handleAllRecordsSizeChange(newSize) {
      this.allRecordsFilter.size = newSize
      this.allRecordsFilter.page = 1 // 重置到第一页
      this.loadAllRecords()
    },

    // 处理全部记录页码变化
    handleAllRecordsPageChange(newPage) {
      this.allRecordsFilter.page = newPage
      this.loadAllRecords()
    },

    // 加载部门统计数据（系统管理员专用）
    async loadDepartmentStats() {
      if (!this.isSystemAdmin) {
        console.warn('非系统管理员无权查看部门统计')
        return
      }

      this.loading.departmentStats = true
      try {
        // 调用API获取部门统计数据
        const response = await getDepartmentStats()
        this.departmentStats = response.data?.departments || []
        // console.log('加载部门统计数据:', this.departmentStats.length, '个部门')
      } catch (error) {
        console.error('加载部门统计失败:', error)
        this.$message.error('加载部门统计失败')
        this.departmentStats = []
      } finally {
        this.loading.departmentStats = false
      }
    },
    
    // 标签页切换处理
    handleTabClick(tab) {
      // console.log('🔄 标签页切换:', tab.name)
      // console.log('👤 当前用户角色:', this.userRoleDisplayName)

      switch (tab.name) {
        case 'my-requests':
          this.loadMyRequests()
          break
        case 'pending-approval':
          this.loadPendingApproval()
          break
        case 'my-approved':
          this.loadMyApproved()
          break
        case 'all-records':
          this.loadAllRecords()
          break
        case 'dept-pending':
          this.loadPendingReviews()
          break
        case 'dept-approved-records':
          this.loadDeptApprovedRecords()
          break
        case 'department-stats':
          this.loadDepartmentStats()
          break
        default:
          console.warn('未知的标签页:', tab.name)
      }
    },
    
    // 撤销申请
    async withdrawRequest(requestId) {
      // 验证requestId
      if (!requestId || requestId === 'undefined' || requestId === 'null') {
        console.error('撤销申请失败: requestId无效', requestId)
        this.$message.error('申请ID无效，请刷新页面重试')
        return
      }

      try {
        await this.$confirm('确定要撤销这个申请吗？', '确认撤销', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await withdrawPermissionRequest(requestId)
        this.$message.success('申请已撤销')
        this.loadMyRequests()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('撤销申请失败:', error)
          this.$message.error('撤销申请失败，请稍后重试')
        }
      }
    },
    
    // 删除申请
    async deleteRequest(requestId) {
      // 验证requestId
      if (!requestId || requestId === 'undefined' || requestId === 'null') {
        console.error('删除申请失败: requestId无效', requestId)
        this.$message.error('申请ID无效，请刷新页面重试')
        return
      }

      try {
        await this.$confirm('确定要删除这个申请吗？删除后无法恢复。', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await deletePermissionRequest(requestId)
        this.$message.success('申请已删除')
        this.loadMyRequests()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除申请失败:', error)
          this.$message.error('删除申请失败，请稍后重试')
        }
      }
    },

    // 撤销权限
    async revokePermission(record) {
      if (!this.isSystemAdmin) {
        this.$message.error('只有系统管理员可以撤销权限')
        return
      }

      if (record.status !== 'approved') {
        this.$message.error('只能撤销已批准的权限')
        return
      }

      try {
        await this.$confirm(
          `确定要撤销用户 "${record.requester_name}" 对文档 "${record.document_title}" 的${record.permission_type === 'read' ? '查看' : '下载'}权限吗？\n\n撤销后用户将无法继续访问该文档。`,
          '确认撤销权限',
          {
            confirmButtonText: '确定撤销',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: false
          }
        )

        // 调用后端API撤销权限
        await this.revokePermissionAPI(record)

        this.$message.success('权限已撤销')

        // 刷新全部记录列表
        this.loadAllRecords()

        // 如果当前在已审批标签页，也刷新该列表
        if (this.activeTab === 'my-approved') {
          this.loadMyApproved()
        }

      } catch (error) {
        if (error !== 'cancel') {
          console.error('撤销权限失败:', error)
          this.$message.error('撤销权限失败，请稍后重试')
        }
      }
    },

    // 调用后端API撤销权限
    async revokePermissionAPI(record) {
      // 构建撤销权限的请求参数
      const revokeData = {
        user_id: record.requester_id,
        document_id: record.document_id,
        permission_type: record.permission_type,
        granted_by: record.reviewer_id
      }

      // 调用撤销权限的API
      const { revokePermission } = await import('@/api/permission')
      return await revokePermission(revokeData)
    },

    // 审批申请
    approveRequest(requestId, approved) {
      this.approvalDialog.requestId = requestId
      this.approvalDialog.form.approved = approved
      this.approvalDialog.form.comment = ''
      this.approvalDialog.visible = true
    },
    
    // 提交审批
    async submitApproval() {
      this.loading.approval = true
      try {
        await approvePermissionRequest(this.approvalDialog.requestId, {
          approved: this.approvalDialog.form.approved,
          comment: this.approvalDialog.form.comment
        })
        
        this.$message.success('审批完成')
        this.approvalDialog.visible = false

        // 根据当前用户角色和当前标签页刷新相应的数据
        if (this.isSystemAdmin) {
          // 刷新系统管理员的相关数据
          this.loadPendingApproval()
          this.loadMyApproved()
          this.loadAllRecords()
        } else if (this.isDepartmentAdmin && !this.isSystemAdmin) {
          this.loadPendingReviews()
          this.loadDeptApprovedRecords()
        }
      } catch (error) {
        console.error('审批失败:', error)
        this.$message.error('审批失败')
      } finally {
        this.loading.approval = false
      }
    },
    
    // 获取状态类型（用于标签颜色）
    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'withdrawn': 'info'
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待审批',
        'approved': '已批准',
        'rejected': '已拒绝',
        'withdrawn': '已撤销'
      }
      return statusMap[status] || status
    },

    // 格式化时间
    formatTime(time) {
      return formatTime(time)
    },

    // 删除系统管理员已审批的记录
    async deleteMyApprovedRecord(recordId) {
      if (!this.isSystemAdmin) {
        this.$message.error('无权限删除记录')
        return
      }

      try {
        await this.$confirm('确定要删除这条已审批记录吗？', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用后端API删除记录
        await deletePermissionRequest(recordId)

        // 从本地数组中删除
        this.myApproved = this.myApproved.filter(record => record.id !== recordId)

        this.$message.success('删除成功')
        // console.log('系统管理员删除已审批记录:', recordId)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除已审批记录失败:', error)
          this.$message.error('删除失败')
        }
      }
    },

    // 删除全部记录中的记录
    async deleteRecord(recordId) {
      if (!this.isSystemAdmin) {
        this.$message.error('无权限删除记录')
        return
      }

      try {
        await this.$confirm('确定要删除这条记录吗？', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用后端API删除记录
        await deletePermissionRequest(recordId)

        // 从本地数组中删除
        this.allRecords = this.allRecords.filter(record => record.id !== recordId)

        this.$message.success('删除成功')
        // console.log('系统管理员删除记录:', recordId)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除记录失败:', error)
          this.$message.error('删除失败')
        }
      }
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'withdrawn': 'info'
      }
      return typeMap[status] || 'info'
    },

    // 删除部门管理员的已审批记录
    async deleteDeptApprovedRecord(recordId) {
      if (!this.isDepartmentAdmin) {
        this.$message.error('无权限删除记录')
        return
      }

      try {
        await this.$confirm('确定要删除这条已审批记录吗？', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 这里应该调用后端API删除记录
        // await deleteDeptApprovedRecord(recordId)

        // 暂时从本地数组中删除
        this.deptApprovedRecords = this.deptApprovedRecords.filter(record => record.id !== recordId)

        this.$message.success('删除成功')
        // console.log('部门管理员删除已审批记录:', recordId)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除部门已审批记录失败:', error)
          this.$message.error('删除失败')
        }
      }
    },



    // 显示权限申请对话框
    showRequestDialog(documentUrl, documentTitle = '') {
      this.requestDialog.documentUrl = documentUrl
      this.requestDialog.documentTitle = documentTitle || documentUrl
      this.requestDialog.form.permission_type = 'read'
      this.requestDialog.form.reason = ''
      this.requestDialog.visible = true
    },

    // 检查路由参数
    checkRouteParams() {
      const { action, documentUrl, documentTitle } = this.$route.query

      if (action === 'request' && documentUrl) {
        // 从搜索页面跳转过来的权限申请
        this.showRequestDialog(documentUrl, documentTitle)
      }
    },

    // 提交权限申请
    async submitPermissionRequest() {
      try {
        // 表单验证
        await this.$refs.requestForm.validate()

        this.loading.request = true

        // 首先根据URL获取文档信息
        let documentId = this.requestDialog.documentId
        if (!documentId) {
          try {
            const docResponse = await getDocumentByUrl(this.requestDialog.documentUrl)
            documentId = docResponse.data.id
          } catch (error) {
            this.$message.error('无法找到对应的文档信息')
            return
          }
        }

        // 提交权限申请
        await submitPermissionRequest({
          document_id: documentId,
          permission_type: this.requestDialog.form.permission_type,
          reason: this.requestDialog.form.reason
        })

        this.$message.success('权限申请已提交，请等待审批')
        this.requestDialog.visible = false
        this.loadMyRequests()

        // 清除路由参数
        this.$router.replace({ path: '/permissions' })

      } catch (error) {
        if (error !== 'validation failed') {
          console.error('提交权限申请失败:', error)
          this.$message.error('提交权限申请失败')
        }
      } finally {
        this.loading.request = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.permissions-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;

  h1 {
    margin: 0 0 10px 0;
    color: #303133;
    font-size: 28px;
    font-weight: 500;
  }

  p {
    margin: 0;
    color: #606266;
    font-size: 14px;
  }
}

// 用户角色信息样式
.user-role-info {
  margin-bottom: 20px;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .user-name {
    font-weight: 600;
    color: #303133;
    font-size: 16px;
  }

  .user-dept {
    color: #606266;
    font-size: 14px;
  }

  .permission-hint {
    margin-left: auto;
    color: #909399;
    cursor: help;

    &:hover {
      color: #409eff;
    }
  }
}

.auth-notice {
  margin-bottom: 30px;
}

.tab-content {
  margin-top: 20px;
}

.filter-bar {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  align-items: center;

  .stats-tag {
    margin-left: auto;
  }
}

/* 过滤器样式 */
.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.filter-form {
  margin-bottom: 10px;
}

.filter-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 20px;
}

.stats-info {
  text-align: right;
}

/* 分页样式 */
.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  text-align: right;
}

// Element UI 样式覆盖
::v-deep .el-table {
  .el-table__empty-text {
    color: #909399;
  }
}

::v-deep .el-tabs__item {
  font-size: 16px;
  font-weight: 500;
}
</style>
