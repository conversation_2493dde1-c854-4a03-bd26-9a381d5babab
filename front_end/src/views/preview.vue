<template>
  <div
    class="preview"
    ref="previewRef"
    @contextmenu.prevent
    @copy.prevent
    v-loading.fullscreen="loading"
    element-loading-text="拼命加载中"
    element-loading-background="rgb(255, 255, 255)"
  >
    <!-- 权限检查失败时显示申请界面 -->
    <div v-if="!hasPermission && !loading" class="permission-denied">
      <div class="permission-card">
        <div class="permission-icon">
          <i class="el-icon-lock"></i>
        </div>
        <h2>需要访问权限</h2>
        <p>您没有查看此文档的权限，请申请访问权限。</p>
        <div class="permission-actions">
          <el-button type="primary" @click="showPermissionRequest">申请权限</el-button>
          <el-button @click="goBack">返回</el-button>
        </div>
      </div>
    </div>

    <!-- 有权限时显示文档内容 -->
    <div class="file" v-if="src && hasPermission">
      <!-- <vue-office-pdf
        v-if="type == 'pdf'"
        :src="src"
        style="height: 100vh"
        @rendered="renderedHandler"
      /> -->
      <iframe
        v-if="type == 'pdf'"
        :src="getPdfViewerUrl()"
        id="pdf-iframe"
      ></iframe>
      <component
        v-else-if="type == 'docx' && docxComponent"
        :is="docxComponent"
        :src="src"
        style="height: 100vh"
        @rendered="renderedHandler"
      />
      <component
        v-else-if="type == 'xlsx' && excelComponent"
        :is="excelComponent"
        :src="src"
        style="height: 100vh"
        @rendered="renderedHandler"
      />
      <!-- 加载中状态 -->
      <div v-else-if="type == 'docx' || type == 'xlsx'" class="component-loading">
        <el-loading-spinner></el-loading-spinner>
        <p>正在加载文档组件...</p>
      </div>
    </div>

    <!-- 权限申请对话框 -->
    <el-dialog
      title="申请文档访问权限"
      :visible.sync="requestDialog.visible"
      width="600px"
    >
      <el-form :model="requestDialog.form" :rules="requestRules" ref="requestForm" label-width="100px">
        <el-form-item label="文档名称">
          <el-input v-model="documentTitle" disabled></el-input>
        </el-form-item>
        <el-form-item label="权限类型" prop="permission_type">
          <el-radio-group v-model="requestDialog.form.permission_type">
            <el-radio label="read">查看权限</el-radio>
            <el-radio label="download">下载权限</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="申请理由" prop="reason">
          <el-input
            v-model="requestDialog.form.reason"
            type="textarea"
            :rows="4"
            placeholder="请详细说明申请该文档权限的理由"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="requestDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitPermissionRequest" :loading="requestLoading">提交申请</el-button>
      </div>
    </el-dialog>



  </div>
</template>

<script>
// 使用动态导入来按需加载大型 Office 组件库，减少初始包大小
import NProgress from 'nprogress' // 导入 nprogress模块
import 'nprogress/nprogress.css' // 导入样式，否则看不到效果
import { checkDocumentPermission, submitPermissionRequest, getDocumentByUrl } from '@/api/permission'
export default {
  data() {
    return {
      type: '',
      src: '',
      loading: true,
      hasPermission: false,
      documentUrl: '',
      documentTitle: '',
      requestLoading: false,

      // 动态加载的组件
      docxComponent: null,
      excelComponent: null,
      pdfComponent: null,

      // 权限申请对话框
      requestDialog: {
        visible: false,
        form: {
          permission_type: 'read',
          reason: ''
        }
      },

      // 表单验证规则
      requestRules: {
        permission_type: [
          { required: true, message: '请选择权限类型', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入申请理由', trigger: 'blur' },
          { min: 10, message: '申请理由至少10个字符', trigger: 'blur' }
        ]
      }
    }
  },
  components: {
    // 动态组件将在需要时加载
  },
  created() {
    NProgress.configure({ showSpinner: false }) // 显示右上角螺旋加载提示
    NProgress.start()
  },
  async mounted() {
    // 处理文件
    const fileUrl = localStorage.getItem('url')
    this.documentUrl = fileUrl
    this.documentTitle = this.extractDocumentTitle(fileUrl)

    // console.log('🔍 预览页面调试信息:')
    // console.log('📁 文档URL:', fileUrl)
    // console.log('🔑 Token存在:', !!this.$store.getters['user/hasToken'])
    // console.log('🔑 Token长度:', this.$store.state.user.token ? this.$store.state.user.token.length : 0)
    // console.log('🔑 localStorage Token:', !!localStorage.getItem('authToken'))

    // 检查文件URL格式
    const fileReg = /.*\.(?<fileType>pdf|docx|xlsx)$/i
    if (!fileReg.test(fileUrl)) {
      this.$message.error('文件格式不支持')
      this.loading = false
      return
    }

    // 检查用户权限
    await this.checkPermission(fileUrl)

    // 如果有权限，继续加载文件
    if (this.hasPermission) {
      const { fileType } = fileUrl.match(fileReg).groups
      this.src = fileUrl
      this.type = fileType

      // console.log('✅ 权限验证通过，文件类型:', fileType)
      // console.log('📄 文件源URL:', this.src)

      // 根据文件类型动态加载相应的组件
      if (this.type == 'pdf') {
        this.loading = false
        NProgress.done()
        return
      } else if (this.type == 'docx') {
        await this.loadDocxComponent()
      } else if (this.type == 'xlsx') {
        await this.loadExcelComponent()
      }

      this.loading = false
      NProgress.done()
    } else {
      // console.log('❌ 权限验证失败')
      this.loading = false
      NProgress.done()
    }

    // 使用最简单的返回机制，无需消息监听器
    console.log('📱 预览页面: 使用简化返回机制，PDF查看器直接使用history.back()')
  },

  beforeUnmount() {
    // 无需清理消息监听器，已移除复杂的消息机制
  },
  methods: {
    // 动态加载 DOCX 组件
    async loadDocxComponent() {
      if (!this.docxComponent) {
        try {
          const { default: VueOfficeDocx } = await import('@vue-office/docx')
          await import('@vue-office/docx/lib/index.css')
          this.docxComponent = VueOfficeDocx
        } catch (error) {
          console.error('❌ 加载 DOCX 组件失败:', error)
          this.$message.error('加载文档组件失败')
        }
      }
    },

    // 动态加载 Excel 组件
    async loadExcelComponent() {
      if (!this.excelComponent) {
        try {
          const { default: VueOfficeExcel } = await import('@vue-office/excel')
          await import('@vue-office/excel/lib/index.css')
          this.excelComponent = VueOfficeExcel
        } catch (error) {
          console.error('❌ 加载 Excel 组件失败:', error)
          this.$message.error('加载文档组件失败')
        }
      }
    },

    // 动态加载 PDF 组件
    async loadPdfComponent() {
      if (!this.pdfComponent) {
        try {
          const { default: VueOfficePdf } = await import('@vue-office/pdf')
          this.pdfComponent = VueOfficePdf
        } catch (error) {
          console.error('❌ 加载 PDF 组件失败:', error)
          this.$message.error('加载文档组件失败')
        }
      }
    },

    // 检查文档权限
    async checkPermission(documentUrl) {
      try {
        console.log('🔍 检查文档权限:', documentUrl)

        const response = await checkDocumentPermission({
          document_url: documentUrl,
          permission_type: 'read'
        })

        console.log('🔍 权限检查结果:', response)

        if (response.code === 200) {
          this.hasPermission = response.permission === true

          if (!this.hasPermission) {
            // console.log('用户没有访问权限，文档:', documentUrl)
          }
        } else {
          console.error('❌ 权限检查失败:', response.msg)
          this.hasPermission = false

        }
      } catch (error) {
        console.error('❌ 权限检查异常:', error)
        this.hasPermission = false

        // 检查是否是403错误（权限不足）
        if (error.response && error.response.status === 403) {
          console.error('❌ 403权限错误，可能是Token过期或权限不足')

        } else if (error.response && error.response.status === 401) {
          console.error('❌ 401认证错误，Token可能已过期')

          // 可以跳转到登录页面
          // this.$router.push('/login')
        } else {

        }
      }
    },

    // 提取文档标题
    extractDocumentTitle(url) {
      try {
        const decodedUrl = decodeURIComponent(url)
        const parts = decodedUrl.split('/')
        const filename = parts[parts.length - 1]
        return filename.replace(/\.(pdf|docx|xlsx)$/i, '')
      } catch (error) {
        return '未知文档'
      }
    },

    // 显示权限申请对话框
    showPermissionRequest() {
      this.requestDialog.form.permission_type = 'read'
      this.requestDialog.form.reason = ''
      this.requestDialog.visible = true
    },

    // 提交权限申请
    async submitPermissionRequest() {
      try {
        // 表单验证
        await this.$refs.requestForm.validate()

        this.requestLoading = true

        // 首先根据URL获取文档信息
        let documentId
        try {
          const docResponse = await getDocumentByUrl(this.documentUrl)
          documentId = docResponse.data.id
        } catch (error) {
          this.$message.error('无法找到对应的文档信息')
          return
        }

        // 提交权限申请
        await submitPermissionRequest({
          document_id: documentId,
          permission_type: this.requestDialog.form.permission_type,
          reason: this.requestDialog.form.reason
        })

        this.$message.success('权限申请已提交，请等待审批')
        this.requestDialog.visible = false

        // 可以选择跳转到权限管理页面
        this.$confirm('是否跳转到权限管理页面查看申请状态？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          this.$router.push('/permissions')
        }).catch(() => {
          // 用户取消，不做任何操作
        })

      } catch (error) {
        if (error !== 'validation failed') {
          console.error('提交权限申请失败:', error)
          this.$message.error('提交权限申请失败')
        }
      } finally {
        this.requestLoading = false
      }
    },



    // 获取PDF查看器URL（包含认证token）
    getPdfViewerUrl() {
      if (!this.src) return ''

      // 获取JWT token
      const token = this.$store.getters['user/hasToken'] ?
        this.$store.state.user.token :
        localStorage.getItem('authToken')

      console.log('🔗 构建PDF查看器URL')
      console.log('📁 原始文件URL:', this.src)
      console.log('🔑 Token存在:', !!token)

      // 确保文件URL通过认证下载接口访问
      let fileUrl = this.src

      // 如果URL包含docs/但不是下载接口，转换为下载接口
      if (fileUrl.includes('docs/') && !fileUrl.includes('/download?url=')) {
        // 提取相对路径
        let relativePath = fileUrl
        if (relativePath.startsWith('http')) {
          try {
            const url = new URL(relativePath)
            relativePath = url.pathname.replace(/^\//, '') + url.search
          } catch (e) {
            console.warn('⚠️ 无法解析URL，使用原始路径:', relativePath)
          }
        }

        // 构建完整的后端API下载URL，使用当前域名避免跨域问题
        const apiBaseUrl = window.location.origin // 使用当前页面的域名
        fileUrl = `${apiBaseUrl}/download?url=${encodeURIComponent(relativePath)}`
        if (token) {
          fileUrl += `&token=${encodeURIComponent(token)}`
        }
        console.log('🔄 转换为带认证的完整下载URL:', fileUrl)
      }

      // 构建PDF查看器URL，传递文件URL和token
      let viewerUrl = `/libs/pdfjs/web/viewer.html?file=${encodeURIComponent(fileUrl)}`

      if (token) {
        viewerUrl += `&token=${encodeURIComponent(token)}`
      }

      console.log('✅ 最终PDF查看器URL:', viewerUrl)
      console.log('🔑 Token长度:', token ? token.length : 0)

      return viewerUrl
    },

    // 渲染完成
    renderedHandler() {
      const types = {
        docx: '.docx-wrapper',
        pdf: '.vue-office-pdf-wrapper',
        xlsx: '.x-spreadsheet-sheet',
      }
      const doc = document.querySelectorAll(types[this.type])
      doc.forEach((item) => {
        const watermark = new WatermarkPlus.Watermark({
          rotate: 15,
          width: item.clientWidth / 3,
          content: window.email,
          globalAlpha: 0.2,
          parent: item,
          fontSize: '14px',
        })
        watermark.create()
      })
      this.loading = false
      NProgress.done()
    },

    // 移除复杂的消息监听机制，使用最简单的返回方式
    // PDF查看器现在直接使用 window.history.back() 返回

    // 返回上一页
    goBack() {
      console.log('🔙 预览页面: 执行返回操作')
      console.log('🔍 预览页面: 当前URL:', window.location.href)
      console.log('🔍 预览页面: 当前路由:', this.$route.path)

      // 清理localStorage中的预览相关数据（但保留搜索参数）
      localStorage.removeItem('url')
      localStorage.removeItem('docId')
      localStorage.removeItem('permissionChecked')
      localStorage.removeItem('permissionResult')

      console.log('🧹 预览页面: 已清理localStorage数据，保留搜索参数')

      // 检查是否在iframe中
      const isInIframe = window.self !== window.top
      console.log('🪟 预览页面: 是否在iframe中:', isInIframe)

      if (isInIframe) {
        // 在iframe中，发送返回消息给父窗口
        console.log('📤 预览页面: 在iframe中，发送返回消息给父窗口')
        try {
          window.parent.postMessage({
            type: 'IFRAME_GO_BACK',
            source: 'preview-page',
            targetRoute: '/search',
            timestamp: Date.now()
          }, '*')
          console.log('✅ 预览页面: 返回消息已发送')
        } catch (error) {
          console.error('❌ 预览页面: 发送返回消息失败:', error)
          // 降级处理：直接跳转
          this.$router.push('/search')
        }
      } else {
        // 不在iframe中，发送消息给当前窗口以触发App.vue的处理逻辑
        console.log('📤 预览页面: 不在iframe中，发送消息给当前窗口')
        try {
          window.postMessage({
            action: 'goBack',
            source: 'pdf-viewer',
            timestamp: Date.now()
          }, '*')
          console.log('✅ 预览页面: 返回消息已发送给当前窗口')
        } catch (error) {
          console.error('❌ 预览页面: 发送消息失败，直接跳转:', error)
          // 降级处理：直接跳转
          this.$router.push('/search')
        }
      }
    }

  },
}
</script>

<style lang="scss" scoped>
.preview {
  height: 100vh;
  overflow: hidden;
}
iframe {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
::v-deep .vue-office-docx,
.vue-office-excel,
.vue-office-pdf {
  user-select: none;
}

// 权限拒绝页面样式 - 与搜索页面配色一致
.permission-denied {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.permission-card {
  background: white;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 90%;
  border: 1px solid #ebeef5;
}

.permission-icon {
  font-size: 64px;
  color: #f56c6c;
  margin-bottom: 20px;
}

.permission-card h2 {
  color: #333333;
  margin: 0 0 15px 0;
  font-size: 24px;
  font-weight: 500;
}

.permission-card p {
  color: #626675;
  margin: 0 0 30px 0;
  font-size: 16px;
  line-height: 1.5;
}

.permission-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

// 统一按钮样式
.permission-actions .el-button--primary {
  background-color: #409eff;
  border-color: #409eff;

  &:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
  }
}

.dialog-footer {
  text-align: right;
}
::v-deep .docx-wrapper {
  background-color: #f5f5f5;
}
::v-deep .vue-office-pdf-wrapper {
  background-color: #f5f5f5 !important;
}

// PDF.js 背景色统一
#pdf-iframe {
  background-color: #f5f5f5;
}
::v-deep .x-spreadsheet-selectors {
  display: none !important;
}
</style>
