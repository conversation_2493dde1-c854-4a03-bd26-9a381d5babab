<template>
  <div class="test-module-error">
    <el-card>
      <div slot="header">
        <span>模块错误处理测试</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h3>测试场景</h3>
          <el-button @click="testDynamicImportError" type="danger">
            模拟动态导入失败
          </el-button>
          <br><br>
          <el-button @click="testChunkLoadError" type="warning">
            模拟代码块加载失败
          </el-button>
          <br><br>
          <el-button @click="testRetryableImport" type="primary">
            测试重试机制
          </el-button>
          <br><br>
          <el-button @click="clearCache" type="info">
            清理应用缓存
          </el-button>
        </el-col>
        
        <el-col :span="12">
          <h3>错误日志</h3>
          <el-input
            type="textarea"
            :rows="10"
            v-model="errorLog"
            readonly
            placeholder="错误信息将显示在这里..."
          />
          <br><br>
          <el-button @click="clearLog" size="small">清空日志</el-button>
        </el-col>
      </el-row>
      
      <el-divider />
      
      <el-alert
        title="使用说明"
        type="info"
        :closable="false"
        show-icon
      >
        <p>这个页面用于测试生产环境中的模块加载错误处理机制：</p>
        <ul>
          <li><strong>模拟动态导入失败：</strong>模拟 "Failed to fetch dynamically imported module" 错误</li>
          <li><strong>模拟代码块加载失败：</strong>模拟 "Loading chunk" 错误</li>
          <li><strong>测试重试机制：</strong>测试带重试功能的动态导入</li>
          <li><strong>清理应用缓存：</strong>清理可能导致缓存问题的本地存储</li>
        </ul>
        <p><strong>注意：</strong>在生产环境中，这些错误通常会触发页面刷新以解决缓存问题。</p>
      </el-alert>
    </el-card>
  </div>
</template>

<script>
import { 
  isModuleLoadError, 
  handleModuleLoadError, 
  createRetryableImport,
  clearApplicationCache 
} from '@/utils/module-error-handler'

export default {
  name: 'TestModuleError',
  data() {
    return {
      errorLog: ''
    }
  },
  methods: {
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.errorLog += `[${timestamp}] ${message}\n`
    },
    
    clearLog() {
      this.errorLog = ''
    },
    
    testDynamicImportError() {
      this.addLog('开始测试动态导入失败...')
      
      // 创建一个模拟的错误
      const mockError = new Error('Failed to fetch dynamically imported module: http://example.com/assets/test.js')
      
      this.addLog(`错误类型检查: ${isModuleLoadError(mockError) ? '✅ 识别为模块错误' : '❌ 未识别'}`)
      
      // 测试错误处理（不实际刷新页面，只记录日志）
      try {
        this.addLog('调用错误处理器...')
        // 注意：在测试环境中我们不实际刷新页面
        console.warn('测试模式：模拟处理动态导入错误')
        this.addLog('✅ 错误处理器调用成功（测试模式下不会实际刷新）')
      } catch (error) {
        this.addLog(`❌ 错误处理失败: ${error.message}`)
      }
    },
    
    testChunkLoadError() {
      this.addLog('开始测试代码块加载失败...')
      
      const mockError = new Error('Loading chunk 123 failed.')
      
      this.addLog(`错误类型检查: ${isModuleLoadError(mockError) ? '✅ 识别为模块错误' : '❌ 未识别'}`)
      
      try {
        this.addLog('调用错误处理器...')
        console.warn('测试模式：模拟处理代码块加载错误')
        this.addLog('✅ 错误处理器调用成功（测试模式下不会实际刷新）')
      } catch (error) {
        this.addLog(`❌ 错误处理失败: ${error.message}`)
      }
    },
    
    async testRetryableImport() {
      this.addLog('开始测试重试机制...')
      
      let attemptCount = 0
      
      // 创建一个会失败几次然后成功的模拟导入函数
      const mockImport = () => {
        attemptCount++
        this.addLog(`第${attemptCount}次尝试导入...`)
        
        if (attemptCount < 3) {
          throw new Error('Failed to fetch dynamically imported module: mock error')
        }
        
        return Promise.resolve({ default: 'Mock Component' })
      }
      
      // 创建带重试的导入函数
      const retryableImport = createRetryableImport(mockImport, 3, 500)
      
      try {
        const result = await retryableImport()
        this.addLog(`✅ 重试成功！结果: ${JSON.stringify(result)}`)
      } catch (error) {
        this.addLog(`❌ 重试失败: ${error.message}`)
      }
    },
    
    clearCache() {
      this.addLog('开始清理应用缓存...')
      
      try {
        clearApplicationCache()
        this.addLog('✅ 应用缓存清理完成')
      } catch (error) {
        this.addLog(`❌ 缓存清理失败: ${error.message}`)
      }
    }
  }
}
</script>

<style scoped>
.test-module-error {
  padding: 20px;
}

.el-button {
  margin-bottom: 10px;
  width: 200px;
}

.el-alert {
  margin-top: 20px;
}

.el-alert ul {
  margin: 10px 0;
  padding-left: 20px;
}

.el-alert li {
  margin: 5px 0;
}
</style>
