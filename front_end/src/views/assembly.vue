<template>
  <div class="assembly">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="自定义汇编" name="1"></el-tab-pane>
      <el-tab-pane label="模板汇编" name="2"></el-tab-pane>
    </el-tabs>
    <!-- 自定义汇编 -->
    <el-form size="small" class="query-form" v-if="activeTab == '1'">
      <el-form-item label="类型">
        <el-select
          v-model="queryParam.project_type"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关键词">
        <el-cascader
          v-model="queryParam.keywords"
          :options="keywordOptions"
          clearable
          collapse-tags
          filterable
          :props="{ multiple: true, emitPath: false }"
          @change="handleKeywordChange"
          :show-all-levels="false"
          popper-class="assembly-keyword"
        >
        </el-cascader>
      </el-form-item>
      <el-form-item label="年份">
        <el-date-picker
          type="year"
          v-model="queryParam.year"
          value-format="yyyy"
          placeholder="请选择"
          clearable
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <!-- 模板汇编 -->
    <el-form
      size="small"
      class="query-form template-query-form"
      style="border-right: 1px solid #dcdfe6"
      v-else
    >
      <el-form-item label="模板">
        <el-select
          v-model="queryParam.promptName"
          placeholder="请选择"
          clearable
          @change="handlePromptNameChange"
        >
          <el-option
            v-for="item in promptNames"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="main">
      <div class="card content">
        <el-alert
          title="您好，您提供的信息越详细、越丰富，汇编效果越好"
          type="info"
          :closable="false"
          show-icon
        >
        </el-alert>
        <!-- <el-input
          v-if="activeTab == '2' && queryParam.prompt"
          v-model="queryParam.prompt"
          type="textarea"
          resize="none"
          readonly
          rows="3"
        /> -->
        <el-input
          class="input"
          v-model="queryParam.question"
          placeholder="请输入自定义条件"
          type="textarea"
          resize="none"
        />
        <el-button-group class="btns">
          <el-button @click="reset" block> 重置 </el-button>
          <el-button
            v-if="!assemblyTaskRunning"
            type="primary"
            :disabled="!queryParam.question"
            @click="assembly"
            block
          >
            开始汇编
          </el-button>
          <el-button
            v-else
            type="danger"
            @click="cancelCurrentTask"
            block
          >
            取消汇编
          </el-button>
        </el-button-group>
      </div>
      <div class="card history">
        <span class="header">历史记录</span>
        <div class="body" v-if="newHistory.length">
          <div
            class="item"
            v-for="item in newHistory"
            :key="item.id || item.time"
            @click="handleHistoryItemClick(item)"
          >
            <div class="top">
              <el-button
                class="btn"
                type="text"
                icon="el-icon-delete"
                @click.stop="handleDeleteHistory(item.id || item.time)"
              ></el-button>
            </div>
            <div class="value">{{ item.req.question }}</div>
            <el-divider class="divider"></el-divider>
          </div>
        </div>
        <el-empty v-else></el-empty>
      </div>
    </div>
    <!-- 汇编结果 -->
    <el-dialog
      :title="activeTab == '1' ? '自定义汇编' : '模板汇编'"
      width="70%"
      :visible.sync="resDialogVisible"
      :close-on-click-modal="!loading"
      :close-on-press-escape="!loading"
      :show-close="!loading"
      @close="handleDialogClose"
      class="res_dialog"
    >
      <el-descriptions border :column="2" :labelStyle="{ width: '100px' }">
        <el-descriptions-item label="类型" v-if="activeTab == '1'">
          {{ queryParam.project_type || '--' }}
        </el-descriptions-item>

        <el-descriptions-item label="年份" v-if="activeTab == '1'">
          {{ queryParam.year || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="关键词" span="2" v-if="activeTab == '1'">
          {{ queryParam.keywords?.join('、') || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="自定义条件" span="2">
          <div v-html="queryParam?.question.replace(/\n/gi, '<br/>')"></div>
        </el-descriptions-item>
        <el-descriptions-item label="汇编结果" span="2">
          <!-- 任务进度显示 -->
          <div v-if="assemblyTaskRunning && currentTaskId" class="task-progress">
            <el-progress
              :percentage="taskProgress"
              :status="progressStatus"
              :stroke-width="8"
            ></el-progress>
            <p class="progress-text">
              {{ taskProgressText }}
            </p>
          </div>
          <div v-loading="loading">
            <div class="markdown ai-response-content" v-html="md.render(processHistoryResponse(res || ''))"></div>
            <!-- 图表 -->
            <iframe
              v-if="echart"
              frameborder="0"
              @load="resizeIframe"
              :srcdoc="'<style>*{margin:0;padding:0}</style>' + echart"
            ></iframe>
            <!-- 图片 -->
            <img v-if="image" :src="'data:image/png;base64,' + image" />
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
    <!-- history详情 -->
    <el-dialog
      title="详情"
      width="70%"
      :visible.sync="historyItemDialogVisible"
    >
      <el-descriptions border :column="2" :labelStyle="{ width: '100px' }">
        <el-descriptions-item label="汇编方式">
          {{ historyItem.type == '1' ? '自定义汇编' : '模板汇编' }}
        </el-descriptions-item>
        <el-descriptions-item label="汇编时间">
          {{ formatTime(historyItem.time) }}
        </el-descriptions-item>
        <el-descriptions-item label="类型" v-if="historyItem.type == '1'">
          {{ historyItem.req?.project_type || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="年份" v-if="historyItem.type == '1'">
          {{ historyItem.req?.year || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="关键词" span="2" v-if="historyItem.type == '1'">
          {{ historyItem.req?.keywords?.join('、') || '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="自定义条件" span="2">
          <div class="pre">{{ historyItem.req?.question }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="汇编结果" span="2">
          <div
            class="markdown ai-response-content"
            v-html="md.render(processHistoryResponse(historyItem?.res || ''))"
          ></div>
          <!-- 图表 -->
          <iframe
            v-if="echart"
            frameborder="0"
            @load="resizeIframe"
            :srcdoc="
              '<style>*{margin:0;padding:0}</style>' + historyItem.echart
            "
          ></iframe>
          <!-- 图片 -->
          <img
            v-if="image"
            :src="'data:image/png;base64,' + historyItem.image"
          />
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPromtDetail,
  reqAssemblyQuery,
  reqKeyword,
  reqPromptNames,
  createAssemblyTask,
  getAssemblyTaskResult,
  getUserAssemblyTasks,
  cancelAssemblyTask,
  getLatestTaskParams,
} from '@/api/assembly'
import { findDifferentItems, formatTime } from '@/utils/index'
import { mapState } from 'vuex'
import _ from 'lodash'
import markdownit from 'markdown-it'

export default {
  name: 'assembly',
  data() {
    return {
      queryParam: {
        project_type: '',
        year: '',
        keywords: [],
        question: '',
        prompt: '自定义汇编',
        // 模板名
        promptName: '',
      },
      typeOptions: [
        {
          value: '文书档案',
          label: '文书档案',
        },
        {
          value: '科技项目',
          label: '科技项目',
        },
        {
          value: '信息化项目',
          label: '信息化项目',
        },
      ],
      keywordOptions: [],
      activeTab: '1',
      checkedKeyword: [],
      resDialogVisible: false,
      loading: true,
      historyItem: {},
      historyItemDialogVisible: false,
      promptNames: [],
      res: '',
      echart: '',
      image: '',
      md: '',
      assemblyTaskRunning: false, // 跟踪汇编任务状态
      // 异步任务相关状态
      currentTaskId: null,
      taskPollingTimer: null,
      taskPollingInterval: 2000, // 2秒轮询一次
      taskProgress: 0, // 任务进度 0-100
      taskProgressText: '正在初始化...', // 进度文本
      taskCompleted: false, // 标记任务是否已完成，防止重复保存历史记录
      pollingErrorCount: 0, // 轮询错误计数器
    }
  },
  async created() {
    this.setTitle()
    this.getKeyword()
    this.md = markdownit({
      html: true,
      linkify: true,
      typographer: true,
    })
  },
  async mounted() {
    console.log('🚀 Assembly页面mounted开始')

    // 设置全局函数供HTML中使用
    window.toggleThinking = this.toggleThinking

    try {
      await this.$store.dispatch('initHistory')
      console.log('✅ initHistory完成，当前历史记录:', this.history?.length || 0)

      // 等待一个tick确保computed属性更新
      await this.$nextTick()
      console.log('📊 当前newHistory:', this.newHistory?.length || 0)

    } catch (error) {
      console.error('❌ initHistory失败:', error)
    }

    // 检查是否有正在进行的汇编任务
    await this.checkRunningTasks()

    // 尝试恢复最新的任务参数
    await this.restoreLatestTaskParams()

    // 测试用
    // axios.get('/echart.txt').then((res) => {
    //   this.echart = res.data
    // })
    // axios.get('/base64.txt').then((res) => {
    //   this.image = res.data
    // })
  },

  beforeDestroy() {
    // 清理轮询定时器
    this.stopTaskPolling()
    // 清理全局函数
    if (window.toggleThinking) {
      delete window.toggleThinking
    }
  },

  computed: {
    ...mapState(['history']),
    newHistory() {
      const filtered = this.history?.filter((item) => {
        return item.type == this.activeTab
      }) || []

      return filtered
    },

    progressStatus() {
      return this.taskProgress === 100 ? 'success' : undefined
    },
  },

  methods: {
    setTitle() {
      document.title = '汇编搜索'
    },
    // 全局函数：切换思考过程显示
    toggleThinking(toggleElement) {
      const container = toggleElement.parentElement
      const content = container.querySelector('.ai-thinking-content')
      const icon = toggleElement.querySelector('.ai-thinking-icon')

      if (content.style.display === 'none') {
        content.style.display = 'block'
        icon.classList.add('expanded')
      } else {
        content.style.display = 'none'
        icon.classList.remove('expanded')
      }
    },
    reset() {
      this.queryParam = this.$options.data().queryParam
    },
    async getKeyword() {
      const res = await reqKeyword()
      this.keywordOptions = res.data.options
    },
    async getPromptNames() {
      const res = await reqPromptNames()
      this.promptNames = res.data?.map((item) => {
        return {
          label: item,
          value: item,
        }
      })
    },
    handleTabClick(val) {
      if (!val || !val.name) {
        console.warn('handleTabClick received invalid parameter:', val)
        return
      }

      this.reset()
      if (val.name == '1') {
        this.queryParam.prompt = '自定义汇编'
      } else if (val.name == '2') {
        this.queryParam.prompt = ''
        this.getPromptNames()
      }
    },
    handleKeywordChange(val) {
      if (this.checkedKeyword.length < val.length) {
        // 新增
        // 找出变化的项
        const [res] = findDifferentItems(val, this.checkedKeyword)
        this.queryParam.question += res + '\n'
      } else {
        // 删除
        // 找出变化的项
        const [res] = findDifferentItems(val, this.checkedKeyword)
        this.queryParam.question = this.queryParam.question.replace(
          new RegExp(res + '.*?' + '\n', 'ig'),
          ''
        )
      }
      this.checkedKeyword = val
    },
    async assembly() {
      // 允许多个任务并发，但限制同一个问题的重复提交
      if (this.assemblyTaskRunning && this.currentTaskId) {
        this.$message.warning('当前汇编任务正在进行中，请等待完成或取消后再提交新任务')
        return
      }

      // 保存当前的汇编参数，避免用户在等待期间修改
      const currentQueryParam = _.cloneDeep(this.queryParam)
      const currentActiveTab = this.activeTab

      try {
        this.res = ''
        this.resDialogVisible = true
        this.loading = true
        this.assemblyTaskRunning = true
        this.taskProgress = 0
        this.taskProgressText = '正在创建汇编任务...'
        this.taskCompleted = false // 重置任务完成标志
        this.pollingErrorCount = 0 // 重置轮询错误计数器

        // 保存参数到缓存（在提交任务时）
        this.saveParamsToCache(currentQueryParam)

        // 创建异步汇编任务
        const taskResponse = await createAssemblyTask(currentQueryParam)

        if (taskResponse.code === 202) {
          this.currentTaskId = taskResponse.data.task_id
          this.taskProgress = 10
          this.taskProgressText = '汇编任务已创建，正在后台处理...'
          this.$message.success('汇编任务已创建，正在后台处理...')

          // 开始轮询任务状态
          this.startTaskPolling(currentQueryParam, currentActiveTab)
        } else {
          throw new Error(taskResponse.msg || '创建汇编任务失败')
        }

      } catch (error) {
        this.loading = false
        this.assemblyTaskRunning = false
        this.currentTaskId = null

        // 根据错误类型提供智能提示
        let errorMessage = '创建汇编任务失败'
        if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
          errorMessage = '网络连接失败，请检查网络连接后重试'
        } else if (error.response?.status === 401) {
          errorMessage = '身份验证失败，请重新登录后重试'
        } else if (error.response?.status === 403) {
          errorMessage = '权限不足，请联系管理员'
        } else if (error.response?.status >= 500) {
          errorMessage = '服务器内部错误，请稍后重试'
        } else if (error.message) {
          errorMessage = `创建任务失败：${error.message}`
        } else {
          errorMessage = '创建任务失败，请稍后重试'
        }

        this.$message.error(errorMessage)
      }
    },

    // 开始任务状态轮询
    startTaskPolling(queryParam, activeTab) {
      if (this.taskPollingTimer) {
        clearInterval(this.taskPollingTimer)
      }

      this.taskPollingTimer = setInterval(async () => {
        try {
          await this.checkTaskStatus(queryParam, activeTab)
        } catch (error) {
          console.error('轮询任务状态失败:', error)
        }
      }, this.taskPollingInterval)

      // 立即检查一次
      this.checkTaskStatus(queryParam, activeTab)
    },

    // 检查任务状态
    async checkTaskStatus(queryParam, activeTab) {
      if (!this.currentTaskId) return

      try {
        const response = await getAssemblyTaskResult(this.currentTaskId)

        // 更新进度显示
        if (response.code === 200) {
          const status = response.data.status
          const progress = response.data.progress || 0

          this.taskProgress = progress

          if (status === 'pending') {
            this.taskProgressText = '任务排队中...'
          } else if (status === 'running') {
            this.taskProgressText = '正在处理汇编请求...'
          } else if (status === 'completed') {
            this.taskProgressText = '汇编完成！'
            this.taskProgress = 100
          } else if (status === 'failed') {
            this.taskProgressText = '汇编失败'
          }
        }

        if (response.code === 200 && response.data.status === 'completed') {
          // 任务完成
          this.stopTaskPolling()
          this.loading = false
          this.assemblyTaskRunning = false

          const result = response.data.result
          this.res = result.response
          this.echart = result.echart
          this.image = result.image

          // 只在第一次完成时保存到历史记录，避免重复保存
          if (!this.taskCompleted) {
            this.taskCompleted = true
            this.$store.dispatch(
              'setHistory',
              _.cloneDeep({
                time: Date.now(),
                req: queryParam,
                res: result.response,
                echart: result.echart,
                image: result.image,
                type: activeTab,
              })
            )
          }

          // 如果对话框已关闭，重新打开显示结果
          if (!this.resDialogVisible) {
            this.resDialogVisible = true
            this.$message.success('汇编完成！')
          } else {
            this.$message.success('汇编完成！')
          }

        } else if (response.code === 500 || response.data.status === 'failed') {
          // 任务失败
          this.stopTaskPolling()
          this.loading = false
          this.assemblyTaskRunning = false

          // 根据错误类型显示智能提示
          const errorMessage = this.getSmartErrorMessage(response.data)
          this.$message.error(errorMessage)
        }
        // 如果是pending或running状态，继续轮询

      } catch (error) {
        console.error('检查任务状态失败:', error)

        // 根据错误类型决定是否显示提示
        this.handlePollingError(error)

        // 网络错误不停止轮询，继续尝试
      }
    },

    // 停止任务轮询
    stopTaskPolling() {
      if (this.taskPollingTimer) {
        clearInterval(this.taskPollingTimer)
        this.taskPollingTimer = null
      }
      this.currentTaskId = null
      this.taskProgress = 0
      this.taskProgressText = '正在初始化...'
    },

    // 检查正在进行的任务
    async checkRunningTasks() {
      try {
        const response = await getUserAssemblyTasks()
        if (response.code === 200 && response.data.length > 0) {
          // 找到最新的正在进行的任务
          const runningTask = response.data[0]
          this.currentTaskId = runningTask.task_id
          this.assemblyTaskRunning = true
          this.loading = true

          // 从任务参数中恢复查询参数和tab状态
          const taskParams = runningTask.task_params || {}
          const queryParam = {
            question: taskParams.question || '',
            prompt: taskParams.prompt || '名词解释',
            promptName: taskParams.promptName || '自定义汇编',
            project_type: taskParams.project_type || '科技项目',
            year: taskParams.year || '2023',
            keywords: taskParams.keywords || [],
            history: taskParams.history || null
          }

          // 确定activeTab
          const activeTab = taskParams.promptName === '自定义汇编' ? '1' : '2'

          console.log('🔄 发现正在进行的汇编任务:', runningTask.task_id)
          this.$message.info('发现正在进行的汇编任务，正在恢复状态...')

          // 显示结果对话框
          this.resDialogVisible = true

          // 开始轮询任务状态
          this.startTaskPolling(queryParam, activeTab)
        }
      } catch (error) {
        console.error('检查正在进行的任务失败:', error)
      }
    },

    // 取消当前任务
    async cancelCurrentTask() {
      if (!this.currentTaskId) return

      try {
        const response = await cancelAssemblyTask(this.currentTaskId)
        if (response.code === 200) {
          this.stopTaskPolling()
          this.loading = false
          this.assemblyTaskRunning = false
          this.$message.success('汇编任务已取消')
        } else {
          this.$message.error('取消任务失败：' + (response.msg || '未知错误'))
        }
      } catch (error) {
        console.error('取消任务失败:', error)
        this.$message.error('取消任务失败：' + (error.message || '网络错误'))
      }
    },

    formatTime,
    handleDeleteHistory(time) {
      // 使用新的删除方法，支持数据库删除
      this.$store.dispatch('deleteHistory', time)
    },

    handleHistoryItemClick(item) {
      this.historyItemDialogVisible = true
      this.historyItem = item
    },

    // 处理历史记录中的响应内容，将<think>标签转换为折叠HTML
    processHistoryResponse(responseText) {
      if (!responseText || typeof responseText !== 'string') {
        return responseText
      }

      try {
        // 检查是否包含<think>标签
        if (responseText.includes('<think>')) {
          // 提取思考内容和最终答案
          const thinkPattern = /<think>([\s\S]*?)<\/think>/
          const thinkMatch = responseText.match(thinkPattern)

          if (thinkMatch) {
            const thinkContent = thinkMatch[1].trim()
            // 移除<think>标签，保留其他内容作为最终答案
            const finalAnswer = responseText.replace(thinkPattern, '').trim()

            // 构建可折叠的HTML结构
            const collapsibleHtml = `
<div class="ai-thinking-container">
    <div class="ai-thinking-toggle" onclick="toggleThinking(this)">
        <svg class="ai-thinking-icon" width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.786 4.167L2.765 1.259c-.416-.4-.985-.482-1.273-.183-.287.298-.183.864.233 1.264l3.021 2.908c.416.4.986.482 1.273.184.287-.299.183-.865-.233-1.265z" fill="currentColor"></path>
            <path d="M8.197 1.206L5.288 4.208c-.4.413-.484.982-.187 1.27.298.289.864.187 1.265-.227L9.274 2.25c.401-.414.485-.983.187-1.271-.297-.288-.863-.187-1.264.227z" fill="currentColor"></path>
        </svg>
        <span class="ai-thinking-label">思考过程</span>
    </div>
    <div class="ai-thinking-content" style="display: none;">
        <div class="ai-thinking-text">${thinkContent}</div>
    </div>
</div>

${finalAnswer}`

            return collapsibleHtml
          }
        }

        // 如果不包含<think>标签，直接返回原文本
        return responseText

      } catch (error) {
        console.error('处理历史记录响应内容时出错:', error)
        return responseText
      }
    },

    // 根据任务状态和错误类型生成智能错误提示
    getSmartErrorMessage(taskData) {
      if (!taskData) {
        return '汇编任务异常，请稍后重试'
      }

      const status = taskData.status
      const error = taskData.error || ''
      const errorType = taskData.error_type || 'unknown'

      // 根据任务状态提供基础提示
      let baseMessage = ''
      switch (status) {
        case 'failed':
          baseMessage = '汇编任务执行失败'
          break
        case 'timeout':
          baseMessage = '汇编任务执行超时'
          break
        default:
          baseMessage = '汇编任务异常'
      }

      // 根据错误类型提供具体的解决建议
      let suggestion = ''
      switch (errorType) {
        case 'network':
          suggestion = '网络连接不稳定，请检查网络连接后重试'
          break
        case 'timeout':
          suggestion = '服务响应超时，可能是查询内容过于复杂，请简化查询条件或稍后重试'
          break
        case 'llm_service':
          suggestion = 'AI服务暂时不可用，请稍后重试或联系管理员'
          break
        case 'search_service':
          suggestion = '文档搜索服务异常，请稍后重试'
          break
        case 'database':
          suggestion = '数据库服务异常，请稍后重试或联系管理员'
          break
        case 'processing':
          suggestion = '数据处理异常，请检查输入内容或稍后重试'
          break
        default:
          // 尝试从错误信息中提取更多信息
          if (error.includes('连接') || error.includes('connection')) {
            suggestion = '服务连接异常，请检查网络连接或稍后重试'
          } else if (error.includes('超时') || error.includes('timeout')) {
            suggestion = '请求超时，请简化查询条件或稍后重试'
          } else if (error.includes('权限') || error.includes('permission')) {
            suggestion = '权限不足，请联系管理员检查权限设置'
          } else if (error.includes('not found') || error.includes('找不到')) {
            suggestion = '请求的资源不存在，请检查查询条件'
          } else {
            suggestion = '系统异常，请稍后重试或联系管理员'
          }
      }

      // 组合完整的错误提示
      return `${baseMessage}：${suggestion}`
    },

    // 处理轮询过程中的网络错误
    handlePollingError(error) {
      // 记录连续错误次数
      if (!this.pollingErrorCount) {
        this.pollingErrorCount = 0
      }
      this.pollingErrorCount++

      // 只在连续错误达到一定次数时显示提示，避免频繁弹窗
      if (this.pollingErrorCount >= 3) {
        let errorMessage = '任务状态检查失败'

        if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
          errorMessage = '网络连接不稳定，正在重试连接...'
        } else if (error.code === 'TIMEOUT' || error.message?.includes('timeout')) {
          errorMessage = '服务响应超时，正在重试连接...'
        } else if (error.response?.status === 404) {
          errorMessage = '任务不存在或已过期'
          // 404错误停止轮询
          this.stopTaskPolling()
          this.loading = false
          this.assemblyTaskRunning = false
        } else if (error.response?.status >= 500) {
          errorMessage = '服务器内部错误，正在重试连接...'
        } else {
          errorMessage = '连接异常，正在重试连接...'
        }

        // 显示警告而不是错误，因为会继续重试
        if (this.assemblyTaskRunning) {
          this.$message.warning(errorMessage)
        }

        // 重置错误计数
        this.pollingErrorCount = 0
      }
    },
    // 恢复最新的任务参数
    async restoreLatestTaskParams() {
      try {
        console.log('🔄 尝试恢复最新任务参数...')

        // 首先尝试从浏览器缓存恢复
        const cachedParams = this.getParamsFromCache()
        if (cachedParams) {
          console.log('📦 从浏览器缓存恢复参数:', cachedParams)
          this.restoreQueryParams(cachedParams)
          return
        }

        // 如果缓存中没有，尝试从服务器获取最新参数
        const response = await getLatestTaskParams()
        if (response.code === 200 && response.data) {
          console.log('🌐 从服务器恢复最新参数:', response.data)
          this.restoreQueryParams(response.data)
          // 同时保存到缓存
          this.saveParamsToCache(response.data)
        } else {
          console.log('ℹ️ 未找到可恢复的参数')
        }
      } catch (error) {
        console.warn('⚠️ 恢复参数失败:', error.message)
      }
    },

    // 从浏览器缓存获取参数
    getParamsFromCache() {
      try {
        const cached = localStorage.getItem('assembly_latest_params')
        if (cached) {
          const data = JSON.parse(cached)
          // 检查缓存是否过期（1小时）
          if (Date.now() - data.timestamp < 60 * 60 * 1000) {
            return data.params
          } else {
            localStorage.removeItem('assembly_latest_params')
          }
        }
      } catch (error) {
        console.warn('读取缓存参数失败:', error)
      }
      return null
    },

    // 保存参数到浏览器缓存
    saveParamsToCache(params) {
      try {
        const cacheData = {
          params: params,
          timestamp: Date.now()
        }
        localStorage.setItem('assembly_latest_params', JSON.stringify(cacheData))
        console.log('💾 参数已保存到缓存')
      } catch (error) {
        console.warn('保存参数到缓存失败:', error)
      }
    },

    // 恢复查询参数到界面
    restoreQueryParams(params) {
      if (!params) return

      // 恢复基本参数
      this.queryParam.question = params.question || ''
      this.queryParam.project_type = params.project_type || ''
      this.queryParam.year = params.year || ''
      this.queryParam.keywords = params.keywords || []
      this.queryParam.prompt = params.prompt || ''
      this.queryParam.promptName = params.promptName || ''

      // 根据参数类型切换到对应的tab
      if (params.promptName === '自定义汇编' || params.prompt === '自定义汇编') {
        this.activeTab = '1'
      } else if (params.promptName && params.promptName !== '自定义汇编') {
        this.activeTab = '2'
      }

      console.log('✅ 参数恢复完成:', {
        activeTab: this.activeTab,
        question: this.queryParam.question,
        promptName: this.queryParam.promptName
      })
    },

    // 智能选择有数据的tab
    selectActiveTabWithData() {
      if (!this.history || this.history.length === 0) {
        console.log('📋 没有历史记录，保持默认tab')
        return
      }

      // 统计各类型的记录数量
      const customCount = this.history.filter(item => item.type === '1').length
      const templateCount = this.history.filter(item => item.type === '2').length

      console.log('📊 历史记录统计:', {
        total: this.history.length,
        custom: customCount,
        template: templateCount,
        currentTab: this.activeTab
      })

      // 如果当前tab没有数据，切换到有数据的tab
      const currentTabCount = this.activeTab === '1' ? customCount : templateCount
      if (currentTabCount === 0) {
        if (this.activeTab === '1' && templateCount > 0) {
          console.log('🔄 切换到模板汇编tab（有数据）')
          this.activeTab = '2'
        } else if (this.activeTab === '2' && customCount > 0) {
          console.log('🔄 切换到自定义汇编tab（有数据）')
          this.activeTab = '1'
        }
      } else {
        console.log('✅ 当前tab有数据，无需切换')
      }
    },
    async handlePromptNameChange(val) {
      const res = await getPromtDetail(val)
      this.queryParam.prompt = res.data
    },
    handleDialogClose() {
      // 对话框关闭时，如果仍在加载中，给用户提示
      if (this.assemblyTaskRunning && this.currentTaskId) {
        this.$message.info('汇编任务仍在后台进行中，完成后将自动显示结果。您可以切换页面，任务不会中断。')
      }
    },
    resizeIframe() {
      const iframe = document.querySelector('iframe')
      const innerDoc = iframe.contentDocument || iframe.contentWindow.document
      iframe.style.height = innerDoc.body.scrollHeight + 'px'
      iframe.style.width = innerDoc.body.scrollWidth + 'px'
    },
  },
}
</script>

<style lang="scss" scoped>
.assembly {
  padding: 10px 16px;
  ::v-deep .query-form {
    label {
      min-width: 90px !important;
    }
  }
  .main {
    display: grid;
    grid-template-columns: 4fr 1fr;
    grid-template-rows: calc(100vh - 150px);
    gap: 10px;
    .card {
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 20px;
      &.content {
        display: flex;
        flex-direction: column;
        gap: 16px;
        .btns {
          display: flex;
          justify-content: flex-end;
          button {
            width: 100px;
          }
        }
        .input {
          flex-grow: 1;
        }
        ::v-deep .input {
          textarea {
            height: 100%;
          }
        }
      }
      &.history {
        display: flex;
        flex-direction: column;
        padding-right: 0;
        .header {
          color: #333;
          font-size: 18px;
          margin: 10px 0;
        }
        .body {
          margin-top: 15px;
          overflow-y: scroll;
          padding-right: 20px;
          .item {
            cursor: pointer;
            &:hover {
              .btn {
                opacity: 1 !important;
              }
            }
            .top {
              display: flex;
              justify-content: flex-end;
              align-items: center;
              height: 14px;
              .btn {
                padding: 0;
                color: #999;
                opacity: 0;
                transition: 0.3s ease all;
              }
            }
            .divider {
              margin: 12px 0;
            }

            .value {
              display: -webkit-box;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              -webkit-line-clamp: 2; /* 指定显示的行数 */
              margin: 4px 0 0;
              color: #333;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .template-query-form {
    border: none !important;
    &::after {
      border: none;
    }
    .el-form-item {
      border-top: 1px solid #dcdfe6;
    }
  }
}
::v-deep .el-alert {
  .el-alert__icon {
    font-size: 20px;
  }
  .el-alert__title {
    font-size: 16px !important;
  }
}
</style>
<style lang="scss">
.assembly-keyword {
  li[aria-haspopup='true'] {
    .el-checkbox {
      display: none;
    }
  }
}
.pre {
  white-space: pre;
}

/* AI思考过程样式 */
.ai-response-content {
  .ai-thinking-container {
    margin: 12px 0;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    background-color: #fafafa;

    .ai-thinking-toggle {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      cursor: pointer;
      user-select: none;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f0f0f0;
      }

      .ai-thinking-icon {
        margin-right: 6px;
        transition: transform 0.2s;
        color: #666;

        &.expanded {
          transform: rotate(180deg);
        }
      }

      .ai-thinking-label {
        font-size: 13px;
        color: #666;
        font-weight: 500;
      }
    }

    .ai-thinking-content {
      border-top: 1px solid #e8e8e8;

      .ai-thinking-text {
        padding: 12px;
        font-size: 13px;
        line-height: 1.5;
        color: #555;
        background-color: #f9f9f9;
        white-space: pre-wrap;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }
    }
  }
}
</style>
