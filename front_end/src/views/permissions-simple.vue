<template>
  <div class="permissions-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>权限管理</h1>
      <p>管理您的文档访问权限申请</p>
    </div>

    <!-- 登录状态提示 -->
    <div v-if="!hasValidAuth" class="auth-notice">
      <div class="alert info">
        <h3>演示模式</h3>
        <p>当前正在使用演示模式，显示测试数据。</p>
        <p>要使用完整功能，请通过服务A登录或使用带token的URL访问。</p>

      </div>
    </div>

    <!-- 功能区域 -->
    <div class="main-content">
      <!-- 标签页切换 -->
      <div class="tabs">
        <!-- 管理员优先显示待审批标签页 -->
        <button
          v-if="isAdmin"
          :class="{ active: activeTab === 'pending-reviews' }"
          @click="activeTab = 'pending-reviews'">
          待审批
        </button>
        <button
          :class="{ active: activeTab === 'my-requests' }"
          @click="activeTab = 'my-requests'">
          我的申请
        </button>
      </div>

      <!-- 我的申请 -->
      <div v-if="activeTab === 'my-requests'" class="tab-content">
        <div v-if="loading.myRequests" class="loading">加载中...</div>
        <div v-else-if="myRequests.length === 0" class="empty">暂无申请记录</div>
        <div v-else>
          <div v-for="request in myRequests" :key="request.id" class="request-item">
            <div class="request-info">
              <h4>{{ request.document_title || request.document_url }}</h4>
              <p>权限类型: {{ request.permission_type === 'read' ? '查看' : '下载' }}</p>
              <p>申请时间: {{ formatTime(request.created_at) }}</p>
              <p>状态: <span :class="'status-' + request.status">{{ getStatusText(request.status) }}</span></p>
            </div>
            <div class="request-actions">
              <button v-if="request.status === 'pending'" @click="withdrawRequest(request.id)">撤销</button>
              <button @click="deleteRequest(request.id)">删除</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 待审批 -->
      <div v-if="activeTab === 'pending-reviews' && isAdmin" class="tab-content">
        <div v-if="loading.pendingReviews" class="loading">加载中...</div>
        <div v-else-if="pendingReviews.length === 0" class="empty">暂无待审批申请</div>
        <div v-else>
          <div v-for="request in pendingReviews" :key="request.id" class="request-item">
            <div class="request-info">
              <h4>{{ request.document_title || request.document_url }}</h4>
              <p>申请人: {{ request.requester_name }}</p>
              <p>权限类型: {{ request.permission_type === 'read' ? '查看' : '下载' }}</p>
              <p>申请理由: {{ request.reason }}</p>
              <p>申请时间: {{ formatTime(request.created_at) }}</p>
            </div>
            <div class="request-actions">
              <button @click="approveRequest(request.id)" class="approve">批准</button>
              <button @click="rejectRequest(request.id)" class="reject">拒绝</button>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import {
  getUserPermissionRequests,
  getPendingReviews,
  approvePermissionRequest,
  rejectPermissionRequest,
  withdrawPermissionRequest,
  deletePermissionRequest
} from '@/api/permission'

export default {
  name: 'PermissionsSimple',
  
  data() {
    return {
      activeTab: 'my-requests',
      currentUser: null,
      myRequests: [],
      pendingReviews: [],
      loading: {
        myRequests: false,
        pendingReviews: false
      }
    }
  },
  
  computed: {
    isAdmin() {
      return this.currentUser && (
        this.currentUser.user_id === 1 || 
        this.currentUser.is_dept_admin
      )
    },
    
    hasValidAuth() {
      const token = localStorage.getItem('authToken')
      return token && this.currentUser && this.currentUser.user_id
    }
  },
  
  created() {
    this.loadCurrentUser()
    this.loadMyRequests()
    if (this.isAdmin) {
      this.loadPendingReviews()
    }
  },

  watch: {
    activeTab(newTab) {
      if (newTab === 'my-requests') {
        this.loadMyRequests()
      } else if (newTab === 'pending-reviews' && this.isAdmin) {
        this.loadPendingReviews()
      }
    }
  },
  
  methods: {
    loadCurrentUser() {
      try {
        const localUser = localStorage.getItem('currentUser')
        if (localUser) {
          this.currentUser = JSON.parse(localUser)
          console.log('加载用户信息:', this.currentUser)
        } else {
          // 创建默认测试用户
          this.currentUser = {
            user_id: 1,
            username: 'test_user',
            email: '<EMAIL>',
            dept_id: 1,
            is_dept_admin: false
          }
        }

        // 根据用户角色设置默认标签页
        this.setDefaultTab()
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    // 根据用户角色设置默认标签页
    setDefaultTab() {
      if (this.isAdmin) {
        console.log('👤 管理员用户，默认显示待审批标签页')
        this.activeTab = 'pending-reviews'
      } else {
        console.log('👤 普通用户，默认显示我的申请标签页')
        this.activeTab = 'my-requests'
      }
    },
    
    loadTestData() {
      this.myRequests = [
        {
          id: 1,
          document_title: '测试文档1.pdf',
          document_url: '/test/doc1.pdf',
          permission_type: 'read',
          status: 'pending',
          created_at: new Date().toISOString(),
          reason: '工作需要查看此文档'
        },
        {
          id: 2,
          document_title: '测试文档2.pdf',
          document_url: '/test/doc2.pdf',
          permission_type: 'download',
          status: 'approved',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          reason: '需要下载文档进行分析'
        }
      ]
      
      this.pendingReviews = [
        {
          id: 3,
          document_title: '待审批文档.pdf',
          document_url: '/test/pending.pdf',
          permission_type: 'read',
          requester_name: '张三',
          reason: '项目研究需要',
          created_at: new Date().toISOString()
        }
      ]
      
      this.$message?.success('测试数据加载成功')
    },

    // 加载我的权限申请
    async loadMyRequests() {
      this.loading.myRequests = true
      try {
        // 检查是否有认证token
        const hasAuth = localStorage.getItem('authToken')
        if (!hasAuth) {
          console.log('无认证token，加载测试数据')
          this.loadTestData()
          return
        }

        const response = await getUserPermissionRequests()
        console.log('获取申请列表响应:', response)

        if (response.code === 200) {
          this.myRequests = response.data || []
          console.log('申请列表数据:', this.myRequests)

          // 验证数据完整性
          this.myRequests.forEach((item, index) => {
            if (!item.id) {
              console.error(`申请项${index}缺少id字段:`, item)
            }
          })
        } else {
          console.warn('加载我的申请失败:', response.msg)
          // 如果API失败，加载测试数据
          this.loadTestData()
        }
      } catch (error) {
        // 如果是403错误（无权限），静默处理
        if (error.response && error.response.status === 403) {
          console.log('无权限访问API，使用测试数据')
        } else {
          console.warn('加载我的申请失败:', error.message)
        }
        // 加载测试数据作为降级方案
        this.loadTestData()
      } finally {
        this.loading.myRequests = false
      }
    },

    // 加载待审批申请
    async loadPendingReviews() {
      if (!this.isAdmin) return

      this.loading.pendingReviews = true
      try {
        // 检查是否有认证token
        const hasAuth = localStorage.getItem('authToken')
        if (!hasAuth) {
          console.log('无认证token，加载测试数据')
          this.loadTestData()
          return
        }

        const response = await getPendingReviews()
        if (response.code === 200) {
          this.pendingReviews = response.data || []
        } else {
          console.warn('加载待审批申请失败:', response.msg)
          // 加载测试数据作为降级方案
          this.loadTestData()
        }
      } catch (error) {
        // 如果是403错误（无权限），静默处理
        if (error.response && error.response.status === 403) {
          console.log('无权限访问API，使用测试数据')
        } else {
          console.warn('加载待审批申请失败:', error.message)
        }
        // 加载测试数据作为降级方案
        this.loadTestData()
      } finally {
        this.loading.pendingReviews = false
      }
    },
    

    
    // 撤回申请
    async withdrawRequest(requestId) {
      // 验证requestId
      if (!requestId || requestId === 'undefined' || requestId === 'null') {
        console.error('撤回申请失败: requestId无效', requestId)
        this.$message.error('申请ID无效，请刷新页面重试')
        return
      }

      try {
        const response = await withdrawPermissionRequest(requestId)
        if (response.code === 200) {
          this.$message.success('申请已撤回')
          this.loadMyRequests() // 重新加载数据
        } else {
          this.$message.error('撤回失败: ' + response.msg)
        }
      } catch (error) {
        console.error('撤回申请失败:', error)
        this.$message.error('撤回失败，请稍后重试')
      }
    },

    // 删除申请
    async deleteRequest(requestId) {
      // 验证requestId
      if (!requestId || requestId === 'undefined' || requestId === 'null') {
        console.error('删除申请失败: requestId无效', requestId)
        this.$message.error('申请ID无效，请刷新页面重试')
        return
      }

      try {
        await this.$confirm('确定要删除这个申请吗？删除后无法恢复。', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await deletePermissionRequest(requestId)
        if (response.code === 200) {
          this.$message.success('申请已删除')
          this.loadMyRequests() // 重新加载数据
        } else {
          this.$message.error('删除失败: ' + response.msg)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除申请失败:', error)
          this.$message.error('删除失败，请稍后重试')
        }
      }
    },

    // 查看申请详情
    viewRequestDetail(request) {
      this.$alert(`
        <div>
          <p><strong>文档:</strong> ${request.document_title}</p>
          <p><strong>权限类型:</strong> ${request.permission_type === 'read' ? '查看' : '下载'}</p>
          <p><strong>申请理由:</strong> ${request.reason}</p>
          <p><strong>申请时间:</strong> ${this.formatTime(request.created_at)}</p>
          <p><strong>状态:</strong> ${this.getStatusText(request.status)}</p>
          ${request.reviewed_at ? `<p><strong>审批时间:</strong> ${this.formatTime(request.reviewed_at)}</p>` : ''}
          ${request.review_comment ? `<p><strong>审批意见:</strong> ${request.review_comment}</p>` : ''}
        </div>
      `, '申请详情', {
        dangerouslyUseHTMLString: true
      })
    },

    // 批准申请
    async approveRequest(requestId) {
      try {
        const { value: comment } = await this.$prompt('请输入审批意见（可选）', '批准申请', {
          confirmButtonText: '批准',
          cancelButtonText: '取消',
          inputType: 'textarea'
        })

        const response = await approvePermissionRequest(requestId, {
          approved: true,
          comment: comment || '申请已批准'
        })

        if (response.code === 200) {
          this.$message.success('申请已批准')
          this.loadPendingReviews() // 重新加载数据
        } else {
          this.$message.error('批准失败: ' + response.msg)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批准申请失败:', error)
          this.$message.error('批准失败，请稍后重试')
        }
      }
    },

    // 拒绝申请
    async rejectRequest(requestId) {
      try {
        const { value: comment } = await this.$prompt('请输入拒绝理由', '拒绝申请', {
          confirmButtonText: '拒绝',
          cancelButtonText: '取消',
          inputType: 'textarea',
          inputValidator: (value) => {
            if (!value || !value.trim()) {
              return '请输入拒绝理由'
            }
            return true
          }
        })

        const response = await rejectPermissionRequest(requestId, {
          comment: comment.trim()
        })

        if (response.code === 200) {
          this.$message.success('申请已拒绝')
          this.loadPendingReviews() // 重新加载数据
        } else {
          this.$message.error('拒绝失败: ' + response.msg)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('拒绝申请失败:', error)
          this.$message.error('拒绝失败，请稍后重试')
        }
      }
    },
    
    getStatusText(status) {
      const statusMap = {
        pending: '待审批',
        approved: '已批准',
        rejected: '已拒绝',
        withdrawn: '已撤销'
      }
      return statusMap[status] || status
    },
    
    formatTime(time) {
      return new Date(time).toLocaleString()
    },
    

  }
}
</script>

<style scoped>
.permissions-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.auth-notice {
  margin-bottom: 30px;
}

.alert {
  padding: 20px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.alert.info {
  background-color: #f4f4f5;
  border: 1px solid #d3d4d6;
  color: #606266;
}

.alert.warning {
  background-color: #fdf6ec;
  border: 1px solid #f5dab1;
  color: #e6a23c;
}

.tabs {
  border-bottom: 2px solid #e4e7ed;
  margin-bottom: 20px;
}

.tabs button {
  padding: 10px 20px;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  margin-right: 20px;
}

.tabs button.active {
  color: #409eff;
  border-bottom-color: #409eff;
}

.tab-content {
  min-height: 300px;
}

.request-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 5px;
  margin-bottom: 10px;
}

.request-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.request-info p {
  margin: 2px 0;
  color: #606266;
  font-size: 14px;
}

.status-pending { color: #e6a23c; }
.status-approved { color: #67c23a; }
.status-rejected { color: #f56c6c; }
.status-withdrawn { color: #909399; }

.request-actions button {
  margin-left: 10px;
  padding: 5px 15px;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  background: white;
  cursor: pointer;
}

.request-actions button.approve {
  background-color: #67c23a;
  color: white;
  border-color: #67c23a;
}

.request-actions button.reject {
  background-color: #f56c6c;
  color: white;
  border-color: #f56c6c;
}

.loading, .empty {
  text-align: center;
  padding: 40px;
  color: #909399;
}


</style>
