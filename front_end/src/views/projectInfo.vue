<template>
  <div class="project-info">
    <el-page-header @back="back" content="精确搜索" class="header">
    </el-page-header>
    <el-form size="small" class="query-form">
      <el-form-item label="项目名称">
        <el-input
          v-model="queryData.queryParam.projectName"
          placeholder="请输入项目名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="项目年份">
        <el-date-picker
          type="years"
          v-model="queryData.queryParam.projectYear"
          value-format="yyyy"
          placeholder="选择一个或多个年"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="投资金额（万元）">
        <div class="money">
          <el-input-number
            v-model="queryData.queryParam.gtMoney"
            :controls="false"
            placeholder="请输入"
            @change="handleGtChange"
          />
          <span>至</span>
          <el-input-number
            v-model="queryData.queryParam.ltMoney"
            :controls="false"
            placeholder="请输入"
            @change="handleLtChange"
          />
        </div>
      </el-form-item>

      <el-form-item label="关键技术">
        <el-input
          v-model="queryData.queryParam.skill"
          placeholder="请输入关键技术"
          clearable
        />
      </el-form-item>

      <el-form-item label="研究内容">
        <el-input
          v-model="queryData.queryParam.content"
          placeholder="请输入研究内容"
          clearable
        />
      </el-form-item>
      <el-form-item label="交付成果">
        <el-input
          v-model="queryData.queryParam.result"
          placeholder="请输入交付成果"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" @click="getList">
          搜索
        </el-button>
        <el-button size="small" type="default" @click="reset"> 重置 </el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="queryData.loading"
      :data="list"
      stripe
      border
      element-loading-text="加载中..."
    >
      <el-table-column type="index" label="序号" align="center" width="50" />
      <el-table-column
        label="项目名称"
        prop="_source.projectName"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        label="项目编号"
        prop="_source.projectNo"
        align="center"
        width="170"
        show-overflow-tooltip
      />
      <el-table-column
        label="项目年份"
        prop="_source.projectYear"
        align="center"
        width="80"
      />
      <el-table-column
        label="起止年限"
        prop="_source.projectBeginEndYear"
        show-overflow-tooltip
        align="center"
        width="200"
      />
      <el-table-column
        label="主要完成单位"
        prop="_source.mainCompany"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        label="协作完成单位"
        prop="_source.auxiliaryCompany"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        label="投资金额"
        prop="_source.money"
        align="center"
        width="100"
        show-overflow-tooltip
      >
        <template v-slot:default="scope">
          {{ scope.row._source.money }}万元
        </template>
      </el-table-column>
      <el-table-column
        label="验收时间"
        prop="_source.checkTime"
        align="center"
        width="150"
        show-overflow-tooltip
      />
      <!-- <el-table-column
        label="关键技术"
        prop="_source.skill"
        align="center"
        width="150"
        show-overflow-tooltip
      /> -->
      <!-- <el-table-column
        label="研究内容"
        prop="_source.content"
        align="center"
        min-width="150"
        show-overflow-tooltip
      /> -->
      <!-- <el-table-column
        label="交付成果"
        prop="_source.result"
        align="center"
        min-width="120"
        show-overflow-tooltip
      /> -->
      <el-table-column label="操作" align="center" width="80" fixed="right">
        <template slot-scope="scope">
          <el-link
            type="primary"
            style="margin-right: 10px"
            @click="show(scope.row)"
          >
            详情
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next, total"
      :page-size="queryData.queryParam.pageSize"
      :total="queryData.queryParam.total"
      :current-page="queryData.queryParam.pageNum"
      @current-change="handlePageChange"
      style="text-align: right; margin-top: 10px"
    >
    </el-pagination>
    <project-info-desc ref="desc"></project-info-desc>
  </div>
</template>

<script>
import { searchProjectInfo } from '@/api/search'
import ProjectInfoDesc from '@/views/projectInfoDesc.vue'

export default {
  name: 'projectInfo',
  components: { ProjectInfoDesc },
  data() {
    return {
      queryData: {
        loading: false,
        queryParam: {
          projectYear: '',
          projectName: '',
          skill: '',
          content: '',
          result: '',
          gtMoney: undefined,
          ltMoney: undefined,
          total: 0,
          pageSize: 8,
          pageNum: 1,
        },
      },
      list: [],
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 详情
    show(row) {
      this.$refs.desc.show(row)
    },
    back() {
      this.$router.back()
    },
    // 重置
    reset() {
      this.queryData.queryParam = this.$options.data().queryData.queryParam
      this.getList()
    },
    // 处理大小于
    handleGtChange(val) {
      const { ltMoney, gtMoney } = this.queryData.queryParam
      if (ltMoney && gtMoney >= ltMoney) {
        this.$nextTick(() => {
          this.queryData.queryParam.gtMoney = undefined
        })
      }
    },
    handleLtChange(val) {
      const { ltMoney, gtMoney } = this.queryData.queryParam
      if (gtMoney && gtMoney >= ltMoney) {
        this.$nextTick(() => {
          this.queryData.queryParam.ltMoney = undefined
        })
      }
    },
    handlePageChange(val) {
      this.queryData.queryParam.pageNum = val
      this.getList()
    },

    getList() {
      this.queryData.loading = true
      let queryParam = this.queryData.queryParam
      console.log(queryParam)
      let param = {
        query: {
          bool: {
            // "minimum_should_match":1,
            should: [],
            // "filter":{
            //   "range":{}
            // }
          },
        },
        highlight: {
          fields: {
            skill: {},
            content: {},
            result: {},
          },
        },
        from: queryParam.pageSize * (queryParam.pageNum - 1),
        size: queryParam.pageSize,
      }
      //项目名称
      if (queryParam.projectName && queryParam.projectName != '') {
        param.query.bool.should.push({
          match: {
            projectName: queryParam.projectName,
          },
        })
      }
      //项目年限
      if (queryParam.projectYear && queryParam.projectYear != '') {
        queryParam.projectYear.forEach((e) => {
          param.query.bool.should.push({
            match: {
              projectYear: e,
            },
          })
        })
      }
      //投资金额小于
      console.log(queryParam.ltMoney, queryParam.gtMoney)
      if (queryParam.ltMoney !== undefined) {
        param.query.bool.filter = {
          range: {
            money: {
              lt: queryParam.ltMoney,
            },
          },
        }
        //投资金额大于
        if (queryParam.gtMoney !== undefined) {
          param.query.bool.filter.range.money.gt = queryParam.gtMoney
        }
      }
      if (queryParam.gtMoney !== undefined) {
        param.query.bool.filter = {
          range: {
            money: {
              gt: queryParam.gtMoney,
            },
          },
        }
        //投资金额大于
        if (queryParam.ltMoney !== undefined) {
          param.query.bool.filter.range.money.lt = queryParam.ltMoney
        }
      }

      //研究内容
      if (queryParam.content && queryParam.content != '') {
        param.query.bool.should.push({
          match: {
            content: queryParam.content,
          },
        })
      }
      //交付成果
      if (queryParam.result && queryParam.result != '') {
        param.query.bool.should.push({
          match: {
            result: queryParam.result,
          },
        })
      }
      //关键技术
      if (queryParam.skill && queryParam.skill != '') {
        param.query.bool.should.push({
          match: {
            skill: queryParam.skill,
          },
        })
      }
      searchProjectInfo(param).then((r) => {
        console.log('查询参数', param)
        this.queryData.loading = false
        this.list = r.hits.hits
        this.queryData.queryParam.total = r.hits?.total.value
      })
    },
  },
}
</script>

<style scoped lang="scss">
.project-info {
  padding: 16px;
  .query-form {
    margin-bottom: 20px;
  }
}
.header {
  margin-bottom: 20px;
}
.money {
  display: flex;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  span {
    color: #303133;
    font-size: 14px;
    padding: 0 5px;
  }
  ::v-deep input {
    border: none;
    text-align: center;
  }
}
</style>
