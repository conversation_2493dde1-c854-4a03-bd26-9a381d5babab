<template>
  <div>
    <el-dialog
      :visible="dialogFormVisible"
      title="详情"
      :close-on-click-modal="false"
      width="80%"
      @close="closeDialog"
    >
      <el-descriptions border :column="3" v-if="detail._source?.projectName">
        <el-descriptions-item label="项目名称">
          {{ detail._source.projectName }}
        </el-descriptions-item>
        <el-descriptions-item label="项目编号">
          {{ detail._source.projectNo }}
        </el-descriptions-item>
        <el-descriptions-item label="项目年份">
          {{ detail._source.projectYear }}
        </el-descriptions-item>
        <el-descriptions-item label="起止年限">
          {{ detail._source.projectBeginEndYear }}
        </el-descriptions-item>
        <el-descriptions-item label="主要完成单位">
          {{ detail._source.mainCompany }}
        </el-descriptions-item>
        <el-descriptions-item label="协作完成单位">
          {{ detail._source.auxiliaryCompany }}
        </el-descriptions-item>
        <el-descriptions-item label="投资金额">
          {{ detail._source.money }}万元
        </el-descriptions-item>
        <el-descriptions-item label="验收时间" :span="2">
          {{ detail._source.checkTime }}
        </el-descriptions-item>
        <el-descriptions-item label="关键技术" :span="3">
          <span v-if="detail.highlight && detail.highlight.skill">
            <span
              v-for="(item, k) in detail.highlight.skill"
              :key="k"
              v-html="item"
            ></span>
          </span>
          <span class="detail" v-else v-html="detail._source.skill"></span>
        </el-descriptions-item>
        <el-descriptions-item label="研究内容" :span="3">
          <span v-if="detail.highlight && detail.highlight.content">
            <span
              v-for="(item, k) in detail.highlight.content"
              :key="k"
              v-html="item"
            ></span>
          </span>
          <span v-else class="detail" v-html="detail._source.content"></span>
        </el-descriptions-item>
        <el-descriptions-item label="交付成果" :span="3">
          <span v-if="detail.highlight && detail.highlight.result">
            <span
              v-for="(item, k) in detail.highlight.result"
              :key="k"
              v-html="item"
            ></span>
          </span>
          <span v-else class="detail" v-html="detail._source.result"></span>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'projectInfoDesc',
  data() {
    return {
      detail: {},
      dialogFormVisible: false,
    }
  },
  methods: {
    show(totalInfo = {}) {
      this.dialogFormVisible = true
      console.log(totalInfo)
      this.detail = totalInfo
    },
    closeDialog() {
      this.dialogFormVisible = false
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-descriptions-item__label {
  width: 8rem;
}
.detail {
  line-height: 25px;
}
</style>
