import localforage from 'localforage'
import Vue from 'vue'
import Vuex from 'vuex'
import user from './modules/user'
import {
  createAssemblyConversation,
  getAssemblyConversations,
  deleteAssemblyConversation,
  generateSessionId
} from '@/api/assemblyHistory'

Vue.use(Vuex)

const state = {
  email: localStorage.getItem('email'),
  history: [],
  currentSessionId: null,
}

const mutations = {
  SET_EMAIL(state, email) {
    state.email = email
  },
  SET_HISTORY(state, history) {
    if (Array.isArray(history)) {
      state.history = history
    } else {
      state.history.unshift(history)
    }
  },
  SET_CURRENT_SESSION_ID(state, sessionId) {
    state.currentSessionId = sessionId
  },
  REMOVE_HISTORY_ITEM(state, timeOrId) {
    state.history = state.history.filter(item => {
      // 兼容旧的时间戳和新的ID
      return item.time !== timeOrId && item.id !== timeOrId
    })
  },
}

const actions = {
  setEamil({ commit }, email) {
    commit('SET_EMAIL', email)
  },

  // 设置历史记录，新增记录时同时保存到数据库
  async setHistory({ commit, dispatch }, history) {
    commit('SET_HISTORY', history)

    // 如果是新增单个历史记录，保存到数据库
    if (!Array.isArray(history)) {
      try {
        await dispatch('saveHistoryToDatabase', history)
      } catch (error) {
        console.error('保存历史记录到数据库失败:', error)
      }
    }
  },

  // 从数据库加载历史记录
  async initHistory({ commit, dispatch }) {
    try {
      console.log('🚀 从数据库加载历史记录...')
      await dispatch('loadHistoryFromDatabase')
      console.log('✅ 历史记录加载成功')
    } catch (error) {
      console.error('❌ 从数据库加载历史记录失败:', error)

      // 回退到本地存储（仅作为紧急备份）
      try {
        const localHistory = await localforage.getItem('history')
        if (localHistory && localHistory.length > 0) {
          commit('SET_HISTORY', localHistory)
          console.log('📦 使用本地备份历史记录:', localHistory.length, '条')

          // 尝试迁移到数据库
          console.log('🔄 尝试迁移本地历史记录到数据库...')
          for (const item of localHistory) {
            try {
              await dispatch('saveHistoryToDatabase', item)
            } catch (migrateError) {
              console.warn('迁移单条记录失败:', migrateError)
            }
          }
        } else {
          commit('SET_HISTORY', [])
        }
      } catch (localError) {
        console.error('❌ 本地存储也失败了:', localError)
        commit('SET_HISTORY', [])
      }
    }
  },

  // 从数据库加载历史记录
  async loadHistoryFromDatabase({ commit }) {
    try {
      const response = await getAssemblyConversations({
        page: 1,
        page_size: 100 // 加载最近100条记录
      })

      if (response.code === 200) {
        // 转换数据库格式到前端格式
        const history = response.data.conversations.map(conv => {
          // 处理response_data，可能是字符串或对象
          let responseData = conv.response_data
          if (typeof responseData === 'string') {
            try {
              responseData = JSON.parse(responseData)
            } catch (e) {
              console.warn('解析response_data失败:', e)
              responseData = { response: responseData }
            }
          }

          // 处理request_data，可能是字符串或对象
          let requestData = conv.request_data
          if (typeof requestData === 'string') {
            try {
              requestData = JSON.parse(requestData)
            } catch (e) {
              console.warn('解析request_data失败:', e)
              requestData = {}
            }
          }

          // 处理echart_data，可能是字符串或对象
          let echartData = conv.echart_data
          if (typeof echartData === 'string') {
            try {
              echartData = JSON.parse(echartData)
            } catch (e) {
              console.warn('解析echart_data失败:', e)
              echartData = null
            }
          }

          return {
            id: conv.id,
            time: new Date(conv.created_at).getTime(),
            req: requestData,
            res: responseData?.response || responseData,
            echart: echartData,
            image: conv.image_data,
            type: conv.assembly_type === 'custom' ? '1' : '2',
            session_id: conv.session_id
          }
        })

        commit('SET_HISTORY', history)
      }
    } catch (error) {
      console.error('从数据库加载历史记录失败:', error)
      throw error
    }
  },

  // 保存单个历史记录到数据库
  async saveHistoryToDatabase({ state }, historyItem) {
    try {
      // 生成会话ID（如果没有）
      let sessionId = state.currentSessionId
      if (!sessionId) {
        const sessionResponse = await generateSessionId()
        if (sessionResponse.code === 200) {
          sessionId = sessionResponse.data.session_id
        }
      }

      const conversationData = {
        session_id: sessionId,
        assembly_type: historyItem.type === '1' ? 'custom' : 'template',
        request_data: historyItem.req,
        response_data: {
          response: historyItem.res
        },
        echart_data: historyItem.echart,
        image_data: historyItem.image,
        status: 'completed'
      }

      const response = await createAssemblyConversation(conversationData)
      if (response.code === 200) {
        console.log('历史记录已保存到数据库:', response.data.id)
        return response.data.id
      }
    } catch (error) {
      console.error('保存历史记录到数据库失败:', error)
      throw error
    }
  },



  // 删除历史记录
  async deleteHistory({ commit, state }, timeOrId) {
    try {
      // 找到要删除的记录
      const historyItem = state.history.find(item =>
        item.time === timeOrId || item.id === timeOrId
      )

      // 如果有数据库ID，从数据库删除
      if (historyItem && historyItem.id) {
        await deleteAssemblyConversation(historyItem.id)
      }

      // 从本地状态删除
      commit('REMOVE_HISTORY_ITEM', timeOrId)
    } catch (error) {
      console.error('删除历史记录失败:', error)
      // 即使数据库删除失败，也从本地状态删除
      commit('REMOVE_HISTORY_ITEM', timeOrId)
    }
  },
}

const store = new Vuex.Store({
  state,
  mutations,
  actions,
  modules: {
    user
  }
})

export default store
