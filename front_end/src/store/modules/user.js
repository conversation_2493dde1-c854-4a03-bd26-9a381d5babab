import { login, logout, refreshToken, register } from '@/api/auth'
import { getUserInfo as getSimpleAuthUserInfo } from '@/api/simple-auth'
import { Message } from 'element-ui'

const TOKEN_KEY = 'authToken'
const REFRESH_TOKEN_KEY = 'refresh_token'
const USER_INFO_KEY = 'currentUser'

const state = {
  token: localStorage.getItem(TOKEN_KEY) || '',
  refreshToken: localStorage.getItem(REFRESH_TOKEN_KEY) || '',
  userInfo: JSON.parse(localStorage.getItem(USER_INFO_KEY) || '{}'),
  isLoggedIn: false, // 初始为false，会在initAuth中检查
  loginLoading: false
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
    if (token) {
      localStorage.setItem(TOKEN_KEY, token)
    } else {
      localStorage.removeItem(TOKEN_KEY)
    }
  },
  
  SET_REFRESH_TOKEN(state, refreshToken) {
    state.refreshToken = refreshToken
    if (refreshToken) {
      localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
    } else {
      localStorage.removeItem(REFRESH_TOKEN_KEY)
    }
  },
  
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo || {}
    if (userInfo && Object.keys(userInfo).length > 0) {
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))

      // 当设置新的用户信息时，清除之前用户的搜索参数
      console.log('🔄 设置新用户信息，清除搜索参数缓存')
      localStorage.removeItem('queryParams')
    } else {
      localStorage.removeItem(USER_INFO_KEY)
    }
  },
  
  SET_LOGIN_STATUS(state, status) {
    state.isLoggedIn = status
  },
  
  SET_LOGIN_LOADING(state, loading) {
    state.loginLoading = loading
  },
  
  CLEAR_AUTH_DATA(state) {
    state.token = ''
    state.refreshToken = ''
    state.userInfo = {}
    state.isLoggedIn = false
    localStorage.removeItem(TOKEN_KEY)
    localStorage.removeItem(REFRESH_TOKEN_KEY)
    localStorage.removeItem(USER_INFO_KEY)
    localStorage.removeItem('queryParams')  // 清除搜索参数
  }
}

const actions = {
  // 用户登录
  async login({ commit, dispatch }, loginForm) {
    commit('SET_LOGIN_LOADING', true)
    try {
      const response = await login(loginForm)
      const { access_token, refresh_token, user_info } = response.data || response
      
      // 保存token和用户信息
      commit('SET_TOKEN', access_token)
      commit('SET_REFRESH_TOKEN', refresh_token)
      commit('SET_USER_INFO', user_info)
      commit('SET_LOGIN_STATUS', true)
      
      // 兼容原有的email设置
      if (user_info?.email) {
        commit('SET_EMAIL', user_info.email, { root: true })
        localStorage.setItem('email', user_info.email)
      }
      if (user_info?.username) {
        localStorage.setItem('userName', user_info.username)
      }
      
      Message.success('登录成功')
      return Promise.resolve(response)
    } catch (error) {
      Message.error(error.message || '登录失败')
      return Promise.reject(error)
    } finally {
      commit('SET_LOGIN_LOADING', false)
    }
  },

  // 用户注册
  async register({ commit }, userData) {
    try {
      const response = await register(userData)
      Message.success('注册成功')
      return Promise.resolve(response)
    } catch (error) {
      Message.error(error.message || '注册失败')
      return Promise.reject(error)
    }
  },



  // 用户登出
  async logout({ commit, state }) {
    // 统一处理：所有用户都使用相同的退出逻辑
    if (state.token) {
      try {
        await logout()
        console.log('Logout API调用成功')
      } catch (error) {
        console.warn('Logout API调用失败，但继续清理本地数据:', error.message)
        // 不抛出错误，继续执行清理操作
      }
    } else {
      console.log('无token状态退出登录，跳过API调用')
    }

    // 清除本地数据
    commit('CLEAR_AUTH_DATA')

    // 清除所有用户相关的localStorage数据
    const userDataKeys = [
      'email',
      'userName',
      'queryParams',  // 搜索参数
      'access_token',
      'refresh_token',
      'user_info',
      'loginSource',
      'lastSessionId'
    ]

    userDataKeys.forEach(key => {
      localStorage.removeItem(key)
    })

    // 清除根级别的email状态
    commit('SET_EMAIL', '', { root: true })

    // 在iframe环境中不显示退出成功消息，避免干扰
    if (window.parent === window) {
      Message.success('已退出登录')
    } else {
      console.log('🚪 iframe环境中退出登录完成')
    }
  },
  
  // 获取用户信息
  async getUserInfo({ commit, state }) {
    if (!state.token) {
      return Promise.reject(new Error('No token'))
    }

    try {
      // 统一使用simple-auth接口获取用户信息
      const response = await getSimpleAuthUserInfo()
      const userInfo = response.data || response.user_info || response

      commit('SET_USER_INFO', userInfo)
      commit('SET_LOGIN_STATUS', true)

      // 兼容原有的email设置
      if (userInfo?.email) {
        commit('SET_EMAIL', userInfo.email, { root: true })
        localStorage.setItem('email', userInfo.email)
      }
      if (userInfo?.user_name) {
        localStorage.setItem('userName', userInfo.user_name)
      }

      console.log('用户信息获取成功:', userInfo.user_name || userInfo.username)
      return Promise.resolve(userInfo)
    } catch (error) {
      console.error('获取用户信息失败:', error)
      commit('CLEAR_AUTH_DATA')
      return Promise.reject(error)
    }
  },
  
  // 刷新token
  async refreshToken({ commit, state }) {
    if (!state.refreshToken) {
      return Promise.reject(new Error('No refresh token'))
    }
    
    try {
      const response = await refreshToken()
      const { access_token, refresh_token } = response.data || response
      
      commit('SET_TOKEN', access_token)
      if (refresh_token) {
        commit('SET_REFRESH_TOKEN', refresh_token)
      }
      
      return Promise.resolve(access_token)
    } catch (error) {
      commit('CLEAR_AUTH_DATA')
      return Promise.reject(error)
    }
  },
  
  // 初始化认证状态
  async initAuth({ commit, dispatch, state }) {
    if (state.token) {
      try {
        // 统一验证：所有用户都需要验证数据库中是否存在
        console.log('验证用户是否在数据库中存在')
        try {
          // 调用getUserInfo验证用户是否在数据库中存在
          await dispatch('getUserInfo')
          console.log('用户数据库验证成功')
        } catch (error) {
          console.error('用户数据库验证失败:', error)
          // 如果数据库中不存在用户，清除认证状态
          commit('CLEAR_AUTH_DATA')
          throw new Error('用户不存在于数据库中，请联系管理员')
        }
      } catch (error) {
        console.error('Init auth failed:', error)
        // 认证失败时清除认证数据
        commit('CLEAR_AUTH_DATA')
      }
    }
  },
  
  // 检查token是否有效
  checkTokenValidity({ state }) {
    if (!state.token) return false
    
    try {
      // 简单的JWT token过期检查
      const payload = JSON.parse(atob(state.token.split('.')[1]))
      const currentTime = Date.now() / 1000
      return payload.exp > currentTime
    } catch (error) {
      return false
    }
  }
}

const getters = {
  isLoggedIn: state => state.isLoggedIn && !!state.token,
  userInfo: state => state.userInfo,
  userName: state => state.userInfo?.username || state.userInfo?.nick_name || '',
  userEmail: state => state.userInfo?.email || '',
  userDept: state => state.userInfo?.dept_name || '',
  hasToken: state => !!state.token,
  avatar: state => state.userInfo?.avatar || ''
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
