/**
 * 认证工具类 - 重构版本
 * 保留有用的权限检查功能，移除无用的token刷新功能
 */

import store from '@/store'

/**
 * 检查token是否已过期
 * @param {string} token JWT token
 * @returns {boolean} 是否已过期
 */
export function isTokenExpired(token) {
  if (!token) return true
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    return payload.exp < currentTime
  } catch (error) {
    console.error('解析token失败:', error)
    return true
  }
}

/**
 * 获取token剩余有效时间（秒）
 * @param {string} token JWT token
 * @returns {number} 剩余秒数，-1表示无效token
 */
export function getTokenRemainingTime(token) {
  if (!token) return -1
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    const remainingTime = payload.exp - currentTime
    return Math.max(0, remainingTime)
  } catch (error) {
    console.error('解析token失败:', error)
    return -1
  }
}

/**
 * 格式化剩余时间显示
 * @param {number} seconds 剩余秒数
 * @returns {string} 格式化的时间字符串
 */
export function formatRemainingTime(seconds) {
  if (seconds <= 0) return '已过期'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

/**
 * 检查用户是否有特定权限
 */
export function hasPermission(permission) {
  const userInfo = store.getters['user/userInfo']
  const userRoles = userInfo?.roles || []

  // 管理员拥有所有权限
  if (userRoles.some(role => role.name === 'admin')) {
    return true
  }

  // 检查用户角色是否包含所需权限
  return userRoles.some(role =>
    role.permissions?.some(perm => perm.name === permission)
  )
}

/**
 * 检查用户是否是系统管理员
 */
export function isSystemAdmin() {
  const userInfo = store.getters['user/userInfo']
  const userRoles = userInfo?.roles || []
  return userRoles.includes('admin')
}

/**
 * 检查用户是否是部门管理员
 */
export function isDepartmentAdmin() {
  const userInfo = store.getters['user/userInfo']
  const userRoles = userInfo?.roles || []
  return userRoles.includes('deptAdmin')
}

/**
 * 检查用户是否是普通用户
 */
export function isNormalUser() {
  const userInfo = store.getters['user/userInfo']
  const userRoles = userInfo?.roles || []
  return userRoles.includes('user') && !userRoles.includes('admin') && !userRoles.includes('deptAdmin')
}

/**
 * 检查用户是否是管理员 (兼容旧代码)
 */
export function isAdmin() {
  return isSystemAdmin()
}

/**
 * 获取用户部门ID
 */
export function getUserDepartmentId() {
  const userInfo = store.getters['user/userInfo']
  return userInfo?.dept_id || null
}

/**
 * 检查是否可以审核权限申请
 */
export function canReviewPermissions() {
  return isAdmin() || isDepartmentAdmin()
}

/**
 * 格式化权限类型显示文本
 */
export function formatPermissionType(type) {
  const map = {
    'read': '查看权限',
    'download': '下载权限',
    'write': '编辑权限',
    'delete': '删除权限',
    'share': '分享权限'
  }
  return map[type] || type
}

/**
 * 格式化申请状态显示文本
 */
export function formatRequestStatus(status) {
  const map = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝',
    'withdrawn': '已撤回'
  }
  return map[status] || status
}

/**
 * 获取状态对应的Element UI标签类型
 */
export function getStatusTagType(status) {
  const map = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'withdrawn': 'info'
  }
  return map[status] || 'info'
}

/**
 * 处理无权限访问的情况
 */
export function handleNoPermission(documentId, permissionType = 'read') {
  const message = `您没有${formatPermissionType(permissionType)}，是否申请权限？`

  return new Promise((resolve, reject) => {
    import('element-ui').then(({ MessageBox }) => {
      MessageBox.confirm(message, '权限不足', {
        confirmButtonText: '申请权限',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 跳转到权限申请页面
        import('@/router').then(({ default: router }) => {
          router.push({
            path: '/permission-request',
            query: {
              document_id: documentId,
              permission_type: permissionType
            }
          })
          resolve(true)
        })
      }).catch(() => {
        reject(new Error('用户取消申请'))
      })
    })
  })
}
