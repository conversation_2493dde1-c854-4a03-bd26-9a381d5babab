/**
 * 动态导入模块错误处理工具
 * 解决生产环境中 "Failed to fetch dynamically imported module" 错误
 */

/**
 * 检查是否为模块加载错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为模块加载错误
 */
export function isModuleLoadError(error) {
  if (!error || !error.message) return false
  
  const errorMessage = error.message.toLowerCase()
  return (
    errorMessage.includes('failed to fetch dynamically imported module') ||
    errorMessage.includes('loading chunk') ||
    errorMessage.includes('loading css chunk') ||
    errorMessage.includes('failed to import')
  )
}

/**
 * 处理模块加载错误
 * @param {Error} error - 错误对象
 * @param {Object} options - 选项
 * @param {string} options.targetPath - 目标路径（用于路由跳转）
 * @param {boolean} options.forceReload - 是否强制刷新页面
 * @param {number} options.retryDelay - 重试延迟（毫秒）
 */
export function handleModuleLoadError(error, options = {}) {
  const {
    targetPath,
    forceReload = false,
    retryDelay = 1000
  } = options

  console.warn('🔄 检测到模块加载失败:', error.message)
  console.warn('📝 这通常是由于以下原因之一:')
  console.warn('   1. 浏览器缓存了旧版本的模块引用')
  console.warn('   2. 网络连接不稳定')
  console.warn('   3. 服务器上的文件已更新但浏览器未刷新')
  console.warn('🔧 处理选项:', { targetPath, forceReload, retryDelay })
  
  if (forceReload) {
    console.warn('🔄 强制刷新页面以解决问题')
    setTimeout(() => {
      window.location.reload()
    }, retryDelay)
    return
  }
  
  if (targetPath) {
    console.warn('🔄 跳转到目标路径以解决问题:', targetPath)
    setTimeout(() => {
      try {
        window.location = targetPath
      } catch (locationError) {
        console.warn('⚠️ 路径跳转失败，改为刷新页面:', locationError.message)
        window.location.reload()
      }
    }, retryDelay)
    return
  }
  
  // 默认行为：刷新当前页面
  console.warn('🔄 刷新当前页面以解决问题')
  setTimeout(() => {
    window.location.reload()
  }, retryDelay)
}

/**
 * 创建带重试机制的动态导入函数
 * @param {Function} importFn - 动态导入函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} retryDelay - 重试延迟（毫秒）
 * @returns {Promise} 导入结果
 */
export function createRetryableImport(importFn, maxRetries = 3, retryDelay = 1000) {
  return async function retryableImport(...args) {
    let lastError
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`📦 尝试动态导入 (第${attempt}次)`)
        return await importFn(...args)
      } catch (error) {
        lastError = error
        
        if (isModuleLoadError(error)) {
          console.warn(`⚠️ 第${attempt}次导入失败:`, error.message)
          
          if (attempt < maxRetries) {
            console.log(`🔄 ${retryDelay}ms后重试...`)
            await new Promise(resolve => setTimeout(resolve, retryDelay))
            continue
          } else {
            console.error('❌ 所有重试都失败了，执行错误处理')
            handleModuleLoadError(error, { forceReload: true })
            throw error
          }
        } else {
          // 非模块加载错误，直接抛出
          throw error
        }
      }
    }
    
    throw lastError
  }
}

/**
 * 设置全局模块错误处理器
 */
export function setupGlobalModuleErrorHandlers() {
  // 处理全局错误事件
  window.addEventListener('error', function(event) {
    if (event.error && isModuleLoadError(event.error)) {
      console.warn('🌐 全局错误处理器捕获到模块加载错误')
      handleModuleLoadError(event.error, { forceReload: true, retryDelay: 2000 })
      event.preventDefault()
      return false
    }
  })
  
  // 处理未捕获的Promise拒绝
  window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && isModuleLoadError(event.reason)) {
      console.warn('🌐 Promise拒绝处理器捕获到模块加载错误')
      handleModuleLoadError(event.reason, { forceReload: true, retryDelay: 2000 })
      event.preventDefault()
      return false
    }
  })
  
  console.log('✅ 全局模块错误处理器已设置')
}

/**
 * 清理浏览器缓存相关的存储
 * 注意：这只能清理应用层面的缓存，不能清理浏览器的HTTP缓存
 */
export function clearApplicationCache() {
  try {
    // 清理localStorage中可能的缓存数据
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (key.includes('cache') || key.includes('version'))) {
        keysToRemove.push(key)
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    // 清理sessionStorage
    const sessionKeysToRemove = []
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i)
      if (key && (key.includes('cache') || key.includes('version'))) {
        sessionKeysToRemove.push(key)
      }
    }
    sessionKeysToRemove.forEach(key => sessionStorage.removeItem(key))
    
    console.log('🧹 应用缓存已清理')
  } catch (error) {
    console.warn('⚠️ 清理应用缓存时出错:', error)
  }
}

export default {
  isModuleLoadError,
  handleModuleLoadError,
  createRetryableImport,
  setupGlobalModuleErrorHandlers,
  clearApplicationCache
}
