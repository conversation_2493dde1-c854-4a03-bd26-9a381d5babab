import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'

const env = import.meta.env

// create an axios instance for AI/LLM APIs
const aiService = axios.create({
  baseURL: env.VITE_VUE_APP_OTHER_API, // '/api-s'
  timeout: 1200000, // 20分钟的超时时间
})

// request interceptor
aiService.interceptors.request.use(
  async (config) => {
    // 检查是否是登录请求（不需要token）
    const isLoginRequest = config.url?.includes('/auth/token')

    if (!isLoginRequest) {
      // AI API也需要JWT token认证
      const token = store.getters['user/hasToken'] ? store.state.user.token : localStorage.getItem('authToken')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
    }

    // 添加请求时间戳，防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  (error) => {
    console.log('AI Request error:', error)
    return Promise.reject(error)
  }
)

// response interceptor
aiService.interceptors.response.use(
  (response) => {
    const res = response.data
    return res
  },
  async (error) => {
    const { response } = error

    if (response) {
      const { status, data } = response

      switch (status) {
        case 401:
          // token过期或无效
          console.log('AI服务收到401错误，清除认证状态并跳转到登录页')
          Message.error('登录已过期，请重新登录')

          // 清除store中的认证信息
          await store.dispatch('user/logout')

          // 使用更好的路由跳转方式
          try {
            // 尝试使用Vue Router进行跳转（不刷新页面）
            const router = window.vueApp?.$router || window.$vm?.$router
            if (router) {
              console.log('使用Vue Router跳转到登录页')
              router.push('/login')
            } else {
              // 备用方案：直接修改URL
              console.log('Vue Router不可用，使用window.location跳转到登录页')
              window.location.href = '/login'
            }
          } catch (routerError) {
            console.error('路由跳转失败，使用备用方案:', routerError)
            window.location.href = '/login'
          }
          break
        case 403:

          break
        case 404:
          Message.error('请求的资源不存在')
          break
        case 500:
          Message.error('AI服务内部错误')
          break
        default:
          Message.error(data?.message || data?.detail || 'AI服务请求失败')
      }
    } else {
      // 网络错误
      if (error.code === 'ECONNABORTED') {
        Message.error('AI服务请求超时，请稍后重试')
      } else {
        Message.error('AI服务网络连接失败，请检查网络')
      }
    }

    return Promise.reject(error)
  }
)

export default aiService
