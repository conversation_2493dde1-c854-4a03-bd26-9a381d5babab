import dayjs from 'dayjs'
import { Message } from 'element-ui'

export function findDifferentItems(arr1, arr2) {
  const uniqueItems = []

  // 遍历第一个数组
  for (let item of arr1) {
    // 如果第二个数组中不包含当前项，则将其添加到结果数组中
    if (!arr2.includes(item)) {
      uniqueItems.push(item)
    }
  }

  // 遍历第二个数组
  for (let item of arr2) {
    // 如果第一个数组中不包含当前项，并且结果数组中也不包含当前项，则将其添加到结果数组中
    if (!arr1.includes(item) && !uniqueItems.includes(item)) {
      uniqueItems.push(item)
    }
  }

  return uniqueItems
}

export function formatTime(val) {
  return dayjs(val).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 下载文档函数（带JWT认证）
 * @param {string} url - 文件URL
 * @param {string} [filename] - 可选，自定义文件名
 */
export async function downloadFile(url, filename) {
  Message.info('下载中……')

  try {
    // 获取JWT token
    const token = localStorage.getItem('authToken') ||
                  (window.store && window.store.state.user.token)

    if (!filename) {
      filename = url.substring(url.lastIndexOf('/') + 1)
      // 移除URL参数部分
      filename = filename.split('?')[0]
      // 确保有文件扩展名
      if (!filename.includes('.')) {
        filename += '.pdf'
      }
    }

    console.log('🔗 下载URL:', url)
    console.log('📁 文件名:', filename)
    console.log('🔑 Token存在:', !!token)

    if (token) {
      // 使用fetch下载，携带认证token
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error(`下载失败: ${response.status} ${response.statusText}`)
      }

      // 获取文件blob
      const blob = await response.blob()

      // 创建下载链接
      const downloadUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = filename
      a.style.display = 'none'

      // 添加到DOM中并触发点击
      document.body.appendChild(a)
      a.click()

      // 清理
      setTimeout(() => {
        document.body.removeChild(a)
        window.URL.revokeObjectURL(downloadUrl)
      }, 100)

      console.log('✅ 下载完成！')
      Message.success('下载已开始')
    } else {
      // 没有token，使用传统方式（可能会失败）
      console.warn('⚠️ 没有找到认证token，使用传统下载方式')
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      a.style.display = 'none'
      a.target = '_blank'

      document.body.appendChild(a)
      a.click()

      setTimeout(() => {
        document.body.removeChild(a)
      }, 100)

      Message.warning('下载已开始，如果失败请检查登录状态')
    }
  } catch (error) {
    console.error('💥 下载失败:', error)
    Message.error(`下载失败: ${error.message}`)
  }
}
