/**
 * 安全的存储访问工具
 * 解决iframe环境下sessionStorage访问被拒绝的问题
 */

/**
 * 检查sessionStorage是否可用
 * @returns {boolean} 是否可用
 */
export function isSessionStorageAvailable() {
  try {
    const test = '__sessionStorage_test__'
    sessionStorage.setItem(test, test)
    sessionStorage.removeItem(test)
    return true
  } catch (e) {
    console.warn('sessionStorage不可用:', e.message)
    return false
  }
}

/**
 * 检查localStorage是否可用
 * @returns {boolean} 是否可用
 */
export function isLocalStorageAvailable() {
  try {
    const test = '__localStorage_test__'
    localStorage.setItem(test, test)
    localStorage.removeItem(test)
    return true
  } catch (e) {
    console.warn('localStorage不可用:', e.message)
    return false
  }
}

/**
 * 安全的sessionStorage操作
 */
export const safeSessionStorage = {
  getItem(key) {
    try {
      if (isSessionStorageAvailable()) {
        return sessionStorage.getItem(key)
      }
      // 降级到localStorage
      return localStorage.getItem(`session_${key}`)
    } catch (e) {
      console.warn(`获取sessionStorage[${key}]失败:`, e.message)
      return null
    }
  },

  setItem(key, value) {
    try {
      if (isSessionStorageAvailable()) {
        sessionStorage.setItem(key, value)
        return true
      }
      // 降级到localStorage
      localStorage.setItem(`session_${key}`, value)
      return true
    } catch (e) {
      console.warn(`设置sessionStorage[${key}]失败:`, e.message)
      return false
    }
  },

  removeItem(key) {
    try {
      if (isSessionStorageAvailable()) {
        sessionStorage.removeItem(key)
      }
      // 同时清理localStorage中的降级数据
      localStorage.removeItem(`session_${key}`)
      return true
    } catch (e) {
      console.warn(`删除sessionStorage[${key}]失败:`, e.message)
      return false
    }
  },

  clear() {
    try {
      if (isSessionStorageAvailable()) {
        sessionStorage.clear()
      }
      // 清理localStorage中的session数据
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('session_')) {
          localStorage.removeItem(key)
        }
      })
      return true
    } catch (e) {
      console.warn('清理sessionStorage失败:', e.message)
      return false
    }
  }
}

/**
 * 安全的localStorage操作
 */
export const safeLocalStorage = {
  getItem(key) {
    try {
      if (isLocalStorageAvailable()) {
        return localStorage.getItem(key)
      }
      return null
    } catch (e) {
      console.warn(`获取localStorage[${key}]失败:`, e.message)
      return null
    }
  },

  setItem(key, value) {
    try {
      if (isLocalStorageAvailable()) {
        localStorage.setItem(key, value)
        return true
      }
      return false
    } catch (e) {
      console.warn(`设置localStorage[${key}]失败:`, e.message)
      return false
    }
  },

  removeItem(key) {
    try {
      if (isLocalStorageAvailable()) {
        localStorage.removeItem(key)
        return true
      }
      return false
    } catch (e) {
      console.warn(`删除localStorage[${key}]失败:`, e.message)
      return false
    }
  },

  clear() {
    try {
      if (isLocalStorageAvailable()) {
        localStorage.clear()
        return true
      }
      return false
    } catch (e) {
      console.warn('清理localStorage失败:', e.message)
      return false
    }
  }
}

/**
 * 内存存储降级方案
 */
class MemoryStorage {
  constructor() {
    this.data = new Map()
  }

  getItem(key) {
    return this.data.get(key) || null
  }

  setItem(key, value) {
    this.data.set(key, value)
  }

  removeItem(key) {
    this.data.delete(key)
  }

  clear() {
    this.data.clear()
  }

  get length() {
    return this.data.size
  }

  key(index) {
    const keys = Array.from(this.data.keys())
    return keys[index] || null
  }
}

// 创建内存存储实例作为最后的降级方案
export const memoryStorage = new MemoryStorage()

/**
 * 智能存储选择器
 * 根据环境自动选择最佳的存储方案
 */
export function getStorageAdapter(preferSession = false) {
  if (preferSession && isSessionStorageAvailable()) {
    return sessionStorage
  }
  
  if (isLocalStorageAvailable()) {
    return localStorage
  }
  
  console.warn('浏览器存储不可用，使用内存存储')
  return memoryStorage
}

/**
 * 检测当前环境是否为iframe
 * @returns {boolean} 是否在iframe中
 */
export function isInIframe() {
  try {
    return window.self !== window.top
  } catch (e) {
    return true
  }
}

/**
 * 检测是否为跨域iframe
 * @returns {boolean} 是否为跨域iframe
 */
export function isCrossOriginIframe() {
  try {
    if (!isInIframe()) {
      return false
    }

    // 尝试访问父窗口的location，如果抛出异常则说明是跨域
    const parentOrigin = window.parent.location.origin
    const currentOrigin = window.location.origin

    return parentOrigin !== currentOrigin
  } catch (e) {
    // 如果访问父窗口location抛出异常，说明是跨域
    return true
  }
}

/**
 * 获取当前环境信息
 * @returns {object} 环境信息
 */
export function getEnvironmentInfo() {
  return {
    isIframe: isInIframe(),
    isCrossOrigin: isCrossOriginIframe(),
    sessionStorageAvailable: isSessionStorageAvailable(),
    localStorageAvailable: isLocalStorageAvailable(),
    hostname: window.location.hostname,
    origin: window.location.origin,
    userAgent: navigator.userAgent
  }
}



/**
 * 获取存储错误的详细信息
 * @param {Error} error 存储错误
 * @returns {object} 错误详情
 */
export function getStorageErrorInfo(error) {
  const info = {
    message: error.message,
    isQuotaExceeded: false,
    isSecurityError: false,
    isCrossOrigin: false,
    isInIframe: isInIframe(),
    isCrossOriginIframe: isCrossOriginIframe()
  }

  // 检查是否为配额超出错误
  if (error.name === 'QuotaExceededError' || error.code === 22) {
    info.isQuotaExceeded = true
  }

  // 检查是否为安全错误
  if (error.name === 'SecurityError' || error.message.includes('Access is denied')) {
    info.isSecurityError = true
  }

  // 检查是否为跨域相关错误
  if (error.message.includes('cross-origin') || error.message.includes('Access is denied')) {
    info.isCrossOrigin = true
  }

  return info
}
