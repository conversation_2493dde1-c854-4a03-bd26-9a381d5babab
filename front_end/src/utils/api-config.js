/**
 * API配置工具
 * 使用构建时注入的配置来设置API地址
 */

import { getServiceAUrl, getServiceBUrl, isDebugMode } from '@/config';

/**
 * 获取API基础URL
 */
export function getApiBaseUrl() {
  return getServiceBUrl();
}

/**
 * 获取服务A的URL（前端页面）
 */
export function getFrontendUrl() {
  return getServiceAUrl();
}

/**
 * 获取完整的API URL
 * @param {string} path - API路径
 * @returns {string} 完整的API URL
 */
export function getApiUrl(path) {
  const baseUrl = getApiBaseUrl();
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
}

/**
 * 获取iframe配置
 */
export function getIframeConfig() {
  const serviceAUrl = getServiceAUrl();
  const serviceBUrl = getServiceBUrl();
  
  return {
    src: serviceAUrl,
    allowedOrigins: [serviceAUrl, serviceBUrl],
    sandbox: 'allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation-by-user-activation'
  };
}

/**
 * 获取axios配置
 */
export function getAxiosConfig() {
  return {
    baseURL: getApiBaseUrl(),
    timeout: getApiTimeout(),
    headers: {
      'Content-Type': 'application/json'
    }
  };
}

/**
 * 日志工具（根据环境决定是否输出）
 */
export function log(...args) {
  if (isDebugMode()) {
    console.log('[API-CONFIG]', ...args);
  }
}

/**
 * 错误日志（总是输出）
 */
export function logError(...args) {
  console.error('[API-CONFIG]', ...args);
}

// 在调试模式下输出配置信息
if (isDebugMode()) {
  log('API配置已加载:');
  log('- 服务A URL:', getServiceAUrl());
  log('- 服务B URL:', getServiceBUrl());
  log('- API超时:', getApiTimeout());
}
