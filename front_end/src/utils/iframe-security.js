/**
 * iframe安全管理模块
 * 在服务B前端页面中实现安全的iframe通信和会话管理
 */

import { getServiceAUrl } from '@/config'

class IframeSecurityManager {
  constructor() {
    this.currentUser = null
    this.currentToken = null
    this.sessionId = null
    this.parentOrigin = null
    this.isInIframe = window.self !== window.top
    
    this.init()
  }

  init() {
    // 检测是否在iframe中运行
    if (!this.isInIframe) {
      console.log('🔍 当前不在iframe中运行')
      return
    }

    // 设置消息监听
    this.setupMessageListener()
    
    // 检测父窗口来源
    this.detectParentOrigin()
    
    // 从URL参数中获取初始信息
    this.parseUrlParams()
    
    // 通知父窗口iframe已就绪
    this.notifyIframeReady()
  }

  /**
   * 检测父窗口来源
   */
  detectParentOrigin() {
    try {
      // 尝试获取父窗口的origin
      this.parentOrigin = document.referrer ? new URL(document.referrer).origin : null
    } catch (e) {
      console.warn('无法检测父窗口来源:', e.message)
    }

    // 如果无法检测，使用配置中的服务A地址
    if (!this.parentOrigin) {
      this.parentOrigin = getServiceAUrl()
    }

    console.log('🔍 检测到父窗口来源:', this.parentOrigin)
  }

  /**
   * 解析URL参数获取token和会话信息
   */
  parseUrlParams() {
    const urlParams = new URLSearchParams(window.location.search)
    
    this.currentToken = urlParams.get('token')
    this.sessionId = urlParams.get('session_id') || `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    
    if (this.currentToken) {
      console.log('🔑 从URL获取到token:', this.currentToken.substring(0, 20) + '...')
      console.log('🆔 会话ID:', this.sessionId)
      
      // 解析token获取用户信息
      this.parseTokenInfo()
    }
  }

  /**
   * 解析token获取用户信息
   */
  parseTokenInfo() {
    try {
      if (!this.currentToken) return

      // 简单的JWT解析（仅解析payload部分）
      const parts = this.currentToken.split('.')
      if (parts.length === 3) {
        const payload = JSON.parse(atob(parts[1]))
        this.currentUser = {
          user_id: payload.user_id,
          user_name: payload.user_name,
          nick_name: payload.nick_name,
          dept_id: payload.dept_id,
          roles: payload.roles || []
        }
        console.log('👤 解析到用户信息:', this.currentUser.user_name)
      }
    } catch (e) {
      console.warn('Token解析失败:', e.message)
    }
  }

  /**
   * 设置消息监听器
   */
  setupMessageListener() {
    window.addEventListener('message', (event) => {
      console.log('📨 收到来自父窗口的消息:', event.data)
      // 特殊处理来自PDF查看器的返回消息
      if (event.data && event.data.action === 'goBack' && event.data.source === 'pdf-viewer') {
        console.log('📨 收到PDF查看器返回消息:', event.data)
        this.handlePdfViewerGoBack(event.data)
        return
      }

      // 特殊处理来自预览页面的返回消息
      if (event.data && event.data.type === 'IFRAME_GO_BACK' && event.data.source === 'preview-page') {
        console.log('📨 收到预览页面返回消息:', event.data)
        this.handlePreviewPageGoBack(event.data)
        return
      }

      // 特殊处理来自PDF查看器的返回消息（新格式）
      if (event.data && event.data.type === 'IFRAME_GO_BACK' && event.data.source === 'pdf-viewer') {
        console.log('📨 收到PDF查看器返回消息（新格式）:', event.data)
        this.handlePdfViewerGoBack(event.data)
        return
      }

      // 验证消息来源
      if (!this.isValidOrigin(event.origin)) {
        console.warn('🚫 收到来自未授权源的消息:', event.origin)
        return
      }

      // 过滤掉不必要的位置检测消息
      if (event.data && (event.data.type === 'iframe-pos' || event.data.type === 'get-iframe-pos')) {
        // 静默处理位置检测消息，不输出日志
        return
      }

      switch (event.data.type) {
        case 'ping':
          this.handlePing(event)
          break
        case 'user-switch':
          this.handleUserSwitch(event.data)
          break
        case 'logout':
          this.handleLogout(event.data)
          break
        case 'route-change':
          this.handleRouteChange(event.data)
          break
        case 'token-refresh':
          this.handleTokenRefresh(event.data)
          break
        default:
          console.log('🔄 未知消息类型:', event.data.type)
      }
    })
  }

  /**
   * 验证消息来源是否合法 - 已取消限制，允许所有来源
   */
  isValidOrigin(origin) {
    // 取消origin限制，允许所有来源的消息
    //console.log('🔓 允许来自任意源的消息:', origin)
    return true

    // 原来的限制代码（已注释）
    // const allowedOrigins = [
    //   this.parentOrigin,
    //   getServiceAUrl(),
    //   'http://***********:8081',  // 开发环境后备
    //   'http://localhost:8081'
    // ].filter(Boolean)
    // return allowedOrigins.includes(origin)
  }

  /**
   * 处理ping消息
   */
  handlePing(event) {
    this.sendMessageToParent({
      type: 'pong',
      timestamp: Date.now(),
      sessionId: this.sessionId,
      user: this.currentUser?.user_name
    }, event.origin)
  }

  /**
   * 处理用户切换
   */
  handleUserSwitch(data) {
    console.log('🔄 处理用户切换:', data.newUser?.user_name)
    
    // 清理当前会话
    this.clearCurrentSession()
    
    // 更新用户信息
    this.currentUser = data.newUser
    this.currentToken = data.newToken
    this.sessionId = data.sessionId
    
    // 通知应用层用户已切换
    this.notifyUserSwitch(data.newUser)
    
    // 确认切换完成
    this.sendMessageToParent({
      type: 'user-switch-complete',
      sessionId: this.sessionId,
      user: this.currentUser?.user_name
    })
  }

  /**
   * 处理退出登录
   */
  handleLogout(data) {
    console.log('🚪 处理用户退出')

    try {
      // 清理会话
      this.clearCurrentSession()

      // 通知应用层用户已退出
      this.notifyLogout()

      // 确认退出完成
      this.sendMessageToParent({
        type: 'logout-complete',
        sessionId: this.sessionId
      })
    } catch (error) {
      console.error('❌ 处理退出登录时发生错误:', error)
      // 即使出错也要通知应用层
      this.notifyLogout()
    }
  }

  /**
   * 处理路由变更
   */
  handleRouteChange(data) {
    console.log('🔄 处理路由变更:', data.route)
    
    // 通知应用层路由变更
    this.notifyRouteChange(data.route)
  }

  /**
   * 处理token刷新
   */
  handleTokenRefresh(data) {
    console.log('🔄 处理token刷新')

    this.currentToken = data.newToken

    // 通知应用层token已刷新
    this.notifyTokenRefresh(data.newToken)
  }

  /**
   * 处理PDF查看器返回消息
   */
  handlePdfViewerGoBack(data) {
    console.log('🔙 处理PDF查看器返回请求:', data)

    // 通知应用层执行返回操作
    this.notifyPdfViewerGoBack()
  }

  /**
   * 处理预览页面返回消息
   */
  handlePreviewPageGoBack(data) {
    console.log('🔙 处理预览页面返回请求:', data)

    // 通知应用层执行返回操作
    this.notifyPreviewPageGoBack(data.targetRoute || '/search')
  }

  /**
   * 向父窗口发送消息
   */
  sendMessageToParent(message, targetOrigin = null) {
    if (!this.isInIframe) return

    const origin = targetOrigin || this.parentOrigin
    if (!origin) {
      console.warn('🚫 无法发送消息：未知的父窗口来源')
      return
    }

    try {
      window.parent.postMessage(message, origin)
      console.log('📤 发送消息到父窗口:', message.type)
    } catch (e) {
      console.error('📤 发送消息失败:', e.message)
    }
  }

  /**
   * 通知iframe就绪
   */
  notifyIframeReady() {
    this.sendMessageToParent({
      type: 'iframe-ready',
      timestamp: Date.now(),
      url: window.location.href,
      sessionId: this.sessionId,
      user: this.currentUser?.user_name
    })
  }

  /**
   * 清理当前会话
   */
  clearCurrentSession() {
    // 清理localStorage
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith('hngpt_') || key.startsWith('auth_')) {
        localStorage.removeItem(key)
      }
    })

    // 清理sessionStorage
    sessionStorage.clear()

    console.log('🧹 会话数据已清理')
  }

  /**
   * 通知应用层用户切换（需要应用层实现）
   */
  notifyUserSwitch(newUser) {
    window.dispatchEvent(new CustomEvent('iframe-user-switch', {
      detail: { user: newUser, token: this.currentToken }
    }))
  }

  /**
   * 通知应用层用户退出（需要应用层实现）
   */
  notifyLogout() {
    window.dispatchEvent(new CustomEvent('iframe-logout'))
  }

  /**
   * 通知应用层路由变更（需要应用层实现）
   */
  notifyRouteChange(route) {
    window.dispatchEvent(new CustomEvent('iframe-route-change', {
      detail: { route }
    }))
  }

  /**
   * 通知应用层token刷新（需要应用层实现）
   */
  notifyTokenRefresh(newToken) {
    window.dispatchEvent(new CustomEvent('iframe-token-refresh', {
      detail: { token: newToken }
    }))
  }

  /**
   * 通知应用层PDF查看器返回请求（需要应用层实现）
   */
  notifyPdfViewerGoBack() {
    window.dispatchEvent(new CustomEvent('pdf-viewer-go-back', {
      detail: { timestamp: Date.now() }
    }))
  }

  /**
   * 通知应用层预览页面返回请求（需要应用层实现）
   */
  notifyPreviewPageGoBack(targetRoute) {
    window.dispatchEvent(new CustomEvent('preview-page-go-back', {
      detail: {
        targetRoute,
        timestamp: Date.now()
      }
    }))
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return this.currentUser
  }

  /**
   * 获取当前token
   */
  getCurrentToken() {
    return this.currentToken
  }

  /**
   * 获取会话ID
   */
  getSessionId() {
    return this.sessionId
  }

  /**
   * 检查是否在iframe中
   */
  isRunningInIframe() {
    return this.isInIframe
  }
}

// 导出类，让调用方决定何时初始化
export default IframeSecurityManager

// 也导出一个便捷的初始化函数
export function initIframeSecurity() {
  const manager = new IframeSecurityManager()
  console.log('🔧 iframe安全管理器已初始化')
  return manager
}
