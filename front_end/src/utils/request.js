import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
const env = import.meta.env
// create an axios instance
const service = axios.create({
  baseURL: env.VITE_VUE_APP_BASE_API, // url = base url + request url
  timeout: 1200000, // 20分钟的超时时间
})

// request interceptor
service.interceptors.request.use(
  async (config) => {
    // 检查是否是登录请求（不需要token）
    const isLoginRequest = config.url?.includes('/auth/token')

    if (!isLoginRequest) {
      // API请求使用JWT token
      const token = store.getters['user/hasToken'] ? store.state.user.token : localStorage.getItem('authToken')
      if (token) {
        // 调试：检查token格式
        // console.log('🔍 Token调试信息:')
        // console.log('- hasToken:', store.getters['user/hasToken'])
        // console.log('- token来源:', store.getters['user/hasToken'] ? 'store' : 'localStorage')
        // console.log('- token长度:', token.length)
        // console.log('- token段数:', token.split('.').length)
        // console.log('- token前20字符:', token.substring(0, 20))
        // console.log('- 请求URL:', config.url)

        config.headers.Authorization = `Bearer ${token}`
      } else {
        // console.log('⚠️ 没有找到token:', {
        //   hasToken: store.getters['user/hasToken'],
        //   storeToken: store.state.user.token,
        //   localToken: localStorage.getItem('authToken')
        // })
      }
    }

    // 添加请求时间戳，防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  (error) => {
    // console.log('Request error:', error)
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  (response) => {
    const res = response.data
    return res
  },
  async (error) => {
    const { response } = error

    if (response) {
      const { status, data } = response

      switch (status) {
        case 401:
          // token过期或无效
          // console.log('收到401错误，清除认证状态并跳转到登录页')
          Message.error('登录已过期，请重新登录')

          // 清除store中的认证信息
          await store.dispatch('user/logout')

          // 使用更好的路由跳转方式
          try {
            // 尝试使用Vue Router进行跳转（不刷新页面）
            const router = window.vueApp?.$router
            if (router) {
              // console.log('使用Vue Router跳转到登录页')
              router.push('/login')
            } else {
              // 备用方案：直接修改URL
              // console.log('Vue Router不可用，使用window.location跳转到登录页')
              window.location.href = '/login'
            }
          } catch (routerError) {
            console.error('路由跳转失败，使用备用方案:', routerError)
            window.location.href = '/login'
          }
          break
        case 403:

          break
        case 404:
          Message.error('请求的资源不存在')
          break
        case 500:
          Message.error('服务器内部错误')
          break
        default:
          Message.error(data?.message || data?.detail || '请求失败')
      }
    } else {
      // 网络错误
      if (error.code === 'ECONNABORTED') {
        Message.error('请求超时，请稍后重试')
      } else {
        Message.error('网络连接失败，请检查网络')
      }
    }

    return Promise.reject(error)
  }
)
export default service
