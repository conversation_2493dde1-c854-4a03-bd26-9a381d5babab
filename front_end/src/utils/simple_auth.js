/**
 * 简化的服务A认证工具类
 * 处理来自服务A的JWT Token接收、验证和存储
 */

import { Message } from 'element-ui'

/**
 * 从URL参数中获取服务A传递的Token
 * @returns {string|null} JWT Token或null
 */
export function getTokenFromUrl() {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('token')
}

/**
 * 从URL参数中获取重定向路径
 * @returns {string|null} 重定向路径或null
 */
export function getRedirectFromUrl() {
  const urlParams = new URLSearchParams(window.location.search)
  const redirectParam = urlParams.get('redirect')

  // 如果URL参数中有redirect，使用它
  if (redirectParam) {
    return redirectParam
  }

  // 如果没有redirect参数，但当前路径不是根路径，使用当前路径作为目标
  const currentPath = window.location.pathname
  if (currentPath && currentPath !== '/' && currentPath !== '/login') {
    // console.log('🎯 从当前路径提取重定向目标:', currentPath)
    return currentPath
  }

  return null
}

/**
 * 解析JWT Token载荷（不验证签名，仅用于提取信息）
 * @param {string} token JWT Token
 * @returns {object|null} Token载荷或null
 */
export function parseJwtPayload(token) {
  if (!token) return null

  try {
    const parts = token.split('.')
    if (parts.length !== 3) return null

    let payload = parts[1]
    // 处理Base64 URL编码
    payload = payload.replace(/-/g, '+').replace(/_/g, '/')

    // 添加必要的padding
    while (payload.length % 4) {
      payload += '='
    }

    const decoded = atob(payload)
    return JSON.parse(decoded)
  } catch (error) {
    console.error('解析JWT载荷失败:', error, 'Token:', token)
    return null
  }
}

/**
 * 验证Token基本格式
 * @param {string} token JWT Token
 * @returns {boolean} 是否有效
 */
export function validateTokenFormat(token) {
  if (!token) return false
  
  const payload = parseJwtPayload(token)
  if (!payload) return false
  
  // 检查必要字段 - 更新为统一token格式
  const requiredFields = ['user_id', 'user_name', 'iss', 'iat', 'exp']
  const missingFields = requiredFields.filter(field => !payload[field])

  if (missingFields.length > 0) {
    console.error('Token缺少必要字段:', missingFields)
    return false
  }
  
  // 检查Token是否过期
  const currentTime = Math.floor(Date.now() / 1000)
  if (payload.exp < currentTime) {
    console.error('Token已过期')
    return false
  }
  
  // 检查签发方 - 更新为统一签发方
  if (payload.iss !== 'hngpt') {
    console.error('无效的Token签发方:', payload.iss)
    return false
  }

  // 不再验证接收方(aud)字段，已移除
  
  return true
}

/**
 * 处理JWT Token登录
 * @param {string} token JWT Token
 * @param {object} store Vuex store实例
 * @returns {Promise<boolean>} 是否登录成功
 */
export async function handleTokenLogin(token, store = null) {
  try {
    // console.log('handleTokenLogin 开始处理:', {
    //   tokenLength: token ? token.length : 0,
    //   hasStore: !!store
    // })

    // 验证Token格式
    if (!validateTokenFormat(token)) {
      throw new Error('无效的认证Token')
    }

    // 解析用户信息
    const payload = parseJwtPayload(token)
    // console.log('解析的Token载荷:', payload)
    const userInfo = {
      id: parseInt(payload.user_id),
      user_id: parseInt(payload.user_id),
      user_name: payload.user_name,  // 修改：使用 user_name 字段
      nick_name: payload.nick_name,
      email: payload.email || `${payload.user_name}@company.com`,  // 修改：使用 user_name 字段
      phone: payload.phone || '',
      dept_id: payload.dept_id ? parseInt(payload.dept_id) : null,
      dept_name: payload.dept_name,
      roles: payload.roles || [],
      issued_at: payload.iat,
      expires_at: payload.exp
    }

    // 存储Token和用户信息到localStorage
    localStorage.setItem('authToken', token)
    localStorage.setItem('currentUser', JSON.stringify(userInfo))

    // 兼容原有系统的存储格式
    localStorage.setItem('email', userInfo.email)
    localStorage.setItem('userName', userInfo.user_name)

    // 如果有store实例，更新Vuex状态
    if (store) {
      // 设置token
      store.commit('user/SET_TOKEN', token)
      // 设置用户信息
      store.commit('user/SET_USER_INFO', userInfo)
      // 设置登录状态
      store.commit('user/SET_LOGIN_STATUS', true)
      // 设置email（兼容原有系统）
      store.commit('SET_EMAIL', userInfo.email)
    }

    // console.log('用户登录成功:', userInfo.user_name)
    return true

  } catch (error) {
    console.error('登录失败:', error)
    Message.error('认证失败: ' + error.message)
    return false
  }
}

/**
 * 检查是否有有效的认证token
 * @returns {boolean} 是否有有效认证
 */
export function hasValidAuth() {
  return !!localStorage.getItem('authToken')
}

/**
 * 获取当前用户信息
 * @returns {object|null} 用户信息或null
 */
export function getCurrentUserInfo() {
  try {
    const userInfoStr = localStorage.getItem('currentUser')
    return userInfoStr ? JSON.parse(userInfoStr) : null
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

/**
 * 获取当前Token
 * @returns {string|null} Token或null
 */
export function getCurrentToken() {
  return localStorage.getItem('authToken')
}

/**
 * 清除认证信息
 */
export function clearAuth() {
  localStorage.removeItem('authToken')
  localStorage.removeItem('currentUser')
  // 清除兼容性字段
  localStorage.removeItem('email')
  localStorage.removeItem('userName')
}

/**
 * 检查Token是否即将过期
 * @returns {boolean} 是否即将过期
 */
export function isTokenExpiring() {
  const token = getCurrentToken()
  if (!token) return false
  
  const payload = parseJwtPayload(token)
  if (!payload) return true
  
  const currentTime = Math.floor(Date.now() / 1000)
  const timeUntilExpiry = payload.exp - currentTime
  
  // 如果在5分钟内过期，认为即将过期
  return timeUntilExpiry < 300
}

/**
 * 处理Token过期
 */
export function handleTokenExpired() {
  Message.warning('登录已过期')
  clearServiceAAuth()
  
  // 如果是iframe环境，通知父窗口
  if (window.parent !== window) {
    try {
      window.parent.postMessage({
        type: 'TOKEN_EXPIRED',
        message: '登录已过期，请重新登录'
      }, '*')
    } catch (error) {
      console.error('通知父窗口失败:', error)
    }
  }
}

/**
 * 初始化认证
 * 在应用启动时调用，检查URL参数中的Token
 * @param {object} store Vuex store实例
 * @param {object} router Vue router实例
 */
export async function initAuth(store = null, router = null) {
  try {
    // 检查URL中是否有Token
    const token = getTokenFromUrl()
    const redirectPath = getRedirectFromUrl()

    // console.log('🚀 initAuth 调用:', {
    //   hasToken: !!token,
    //   tokenLength: token ? token.length : 0,
    //   redirectPath,
    //   currentUrl: window.location.href,
    //   isInIframe: window.self !== window.top
    // })

    if (token) {
      // console.log('🎯 检测到JWT Token，正在处理登录...')

      // 处理登录
      const loginSuccess = await handleTokenLogin(token, store)

      if (loginSuccess) {
        // console.log('✅ 登录处理成功')

        // 清除URL中的Token参数（安全考虑）
        const url = new URL(window.location)
        url.searchParams.delete('token')
        url.searchParams.delete('redirect')
        window.history.replaceState({}, '', url.toString())
        // console.log('🧹 已清理URL参数')

        // 验证store状态
        if (store) {
          // const finalState = {
          //   isLoggedIn: store.getters['user/isLoggedIn'],
          //   hasToken: store.getters['user/hasToken'],
          //   username: store.getters['user/userName']
          // }
          // console.log('📊 最终Store状态:', finalState)
        }

        //Message.success('欢迎使用系统！')

        // 如果有router实例，进行页面跳转
        if (router) {
          // 给一点时间让store状态完全更新
          await new Promise(resolve => setTimeout(resolve, 100))

          if (redirectPath && redirectPath !== '/') {
            // console.log('🔄 重定向到:', redirectPath)
            await router.push(redirectPath)
          } else {
            // console.log('🏠 重定向到首页')
            await router.push('/')
          }
        }

        return true
      } else {
        console.error('❌ 登录处理失败')
        return false
      }
    } else {
      // 检查是否已有有效的认证
      const existingToken = getCurrentToken()
      if (existingToken) {
        // console.log('🔍 检查现有认证...')
        if (validateTokenFormat(existingToken)) {
          // console.log('✅ 发现有效的认证')
          // 如果有store，恢复登录状态
          if (store) {
            const userInfo = getCurrentUserInfo()
            if (userInfo) {
              store.commit('user/SET_TOKEN', existingToken)
              store.commit('user/SET_USER_INFO', userInfo)
              store.commit('user/SET_LOGIN_STATUS', true)
              if (userInfo.email) {
                store.commit('SET_EMAIL', userInfo.email)
              }
              // console.log('📊 已恢复Store状态:', {
              //   username: userInfo.user_name,
              //   isLoggedIn: store.getters['user/isLoggedIn']
              // })
            }
          }
          return true
        } else {
          // console.log('⚠️ Token已失效，清除认证信息')
          clearAuth()
        }
      } else {
        // console.log('ℹ️ 未发现认证信息')
      }

      return true
    }
  } catch (error) {
    console.error('💥 初始化认证失败:', error)
    Message.error('认证初始化失败')
    return false
  }
}

/**
 * 检查用户角色
 * @param {string} role 角色标识
 * @returns {boolean} 是否有角色
 */
export function hasRole(role) {
  const userInfo = getCurrentUserInfo()
  if (!userInfo) return false

  return userInfo.roles && userInfo.roles.includes(role)
}

/**
 * 检查是否是系统管理员
 * @returns {boolean} 是否是系统管理员
 */
export function isSystemAdmin() {
  return hasRole('admin')
}

/**
 * 检查是否是部门管理员
 * @returns {boolean} 是否是部门管理员
 */
export function isDepartmentAdmin() {
  return hasRole('deptAdmin')
}

/**
 * 检查是否是普通用户
 * 任何非admin/deptAdmin角色的用户都视为普通用户
 * @returns {boolean} 是否是普通用户
 */
export function isNormalUser() {
  return !hasRole('admin') && !hasRole('deptAdmin')
}

/**
 * 获取用户的有效角色
 * 标准化角色处理：任何非admin/deptAdmin角色都视为user
 * @returns {string} 有效角色：'admin', 'deptAdmin', 'user'
 */
export function getEffectiveRole() {
  if (hasRole('admin')) {
    return 'admin'
  } else if (hasRole('deptAdmin')) {
    return 'deptAdmin'
  } else {
    return 'user'  // 默认为普通用户，包括其他任何角色
  }
}

/**
 * 检查用户是否有管理员权限（系统管理员或部门管理员）
 * @returns {boolean} 是否有管理员权限
 */
export function hasAdminPrivileges() {
  return hasRole('admin') || hasRole('deptAdmin')
}

/**
 * 检查是否是管理员 (兼容旧代码)
 * @returns {boolean} 是否是管理员
 */
export function isAdmin() {
  return isSystemAdmin()
}

/**
 * 获取Token剩余时间（秒）
 * @returns {number} 剩余秒数，-1表示无效
 */
export function getTokenRemainingTime() {
  const token = getCurrentToken()
  if (!token) return -1
  
  const payload = parseJwtPayload(token)
  if (!payload) return -1
  
  const currentTime = Math.floor(Date.now() / 1000)
  return Math.max(0, payload.exp - currentTime)
}

/**
 * 格式化剩余时间显示
 * @param {number} seconds 剩余秒数
 * @returns {string} 格式化的时间字符串
 */
export function formatRemainingTime(seconds) {
  if (seconds <= 0) return '已过期'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}
