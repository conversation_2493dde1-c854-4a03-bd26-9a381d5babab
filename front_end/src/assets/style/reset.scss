p,
body,
h1,
h2,
h3,
h4,
h5,
h6,
ul,
li,
ol,
iframe {
  margin: 0;
  padding: 0;
}

/* 解决图片下间隙 */
img {
  vertical-align: middle;
}

/* 去掉下划线 */
a {
  text-decoration: none;
}

/* 去掉自带列表符 */
ul,
ol {
  list-style: none;
}
// 全局滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
  border-radius: 10px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}
::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(51, 51, 51, 0.1);
}
::-webkit-scrollbar-thumb:hover {
  background-color: #c4c9cb;
}
::-webkit-scrollbar-thumb:active {
  background-color: #c4c9cb;
}
em {
  color: red;
  font-style: normal;
}
body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}
