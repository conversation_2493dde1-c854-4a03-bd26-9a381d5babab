@import './reset.scss';
$border: 1px solid #dcdfe6;
// 查询表单样式
.query-form {
  --columns: 3;
  position: relative;
  display: grid;
  grid-template-columns: repeat(var(--columns), 1fr);

  margin-bottom: 20px;

  border: {
    left: $border;
    top: $border;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    display: block;
    width: 100%;
    border-bottom: $border;
  }

  .el-form-item {
    .el-form-item__content {
      display: flex;
      align-items: center;
    }
    &:not(:first-child):last-child {
      .el-form-item__content {
        justify-content: flex-end;
      }

      grid-column: var(--columns);
    }
    border: {
      bottom: $border;
      right: $border;
    }

    display: flex;
    margin: 0;

    label {
      white-space: nowrap;
      min-width: 160px;
      background-color: #f6f6f6;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-size: 13px;
      font-weight: 400;
      color: #333333;

      border: {
        right: $border;
      }
    }

    .el-form-item__content {
      padding: 8px;
      width: 100%;
    }
    .el-date-editor,
    .el-select,
    .el-cascader {
      width: 100%;
    }
    .el-cascader {
      height: 32px !important;
      .el-input {
        height: 32px !important;
      }
    }
  }
}
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
#nprogress .bar {
  z-index: 99999 !important;
}

.markdown {
  // Variables
  $primary-color: #409eff;
  $secondary-color: #2ecc71;
  $text-color: #333;
  $background-color: #fff;
  $heading-color: #555;
  $code-color: #333;
  $link-color: #1e87f0;

  // padding: 20px;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    SF Pro SC,
    SF Pro Display,
    SF Pro Icons,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Helvetica Neue,
    Helvetica,
    Arial,
    sans-serif !important;
  font-size: 14px;
  line-height: 1.6;
  color: $text-color;
  background-color: $background-color;

  // Heading Styles
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: $heading-color;
    font-weight: bold;
  }

  h1 {
    font-size: 28px;
  }

  h2 {
    font-size: 24px;
  }

  h3 {
    font-size: 20px;
  }

  h4 {
    font-size: 18px;
  }

  h5 {
    font-size: 16px;
  }

  h6 {
    font-size: 14px;
  }

  // Link Styles
  a {
    color: $link-color;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  // Paragraph Styles
  p {
    // margin-bottom: 10px;
  }
  em {
    padding: 0 3px;
  }

  // Code Styles
  code {
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    background-color: #f9f9f9;
    padding: 4px;
    color: $code-color;
    border: 1px solid #ccc;
    border-radius: 3px;
    line-height: 34px;
  }

  // Blockquote Styles
  blockquote {
    margin: 0;
    padding: 10px 20px;
    background-color: #f5f5f5;
    border-left: 5px solid $primary-color;
    color: #666;
  }

  // List Styles
  ul,
  ol {
    margin-bottom: 10px;
    margin-left: 20px;
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  li {
    // margin-bottom: 5px;
  }

  // Table Styles
  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 10px;
  }

  th,
  td {
    border: 1px solid #ddd;
    padding: 8px;
  }

  th {
    background-color: #f2f2f2;
    font-weight: bold;
    text-align: left;
  }

  // Image Styles
  img {
    max-width: 100%;
    height: auto;
    margin-bottom: 10px;
  }

  // Horizontal Rule Style
  hr {
    border: none;
    border-top: 1px solid #ccc;
    margin: 10px 0;
  }

  // Code Block Styles
  pre {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 3px;
    overflow: auto;
    word-wrap: break-word;
    // word-break: break-all;
    white-space: pre-wrap;
    code {
      background-color: transparent !important;
      padding: 0 !important;
      border: none !important;
    }
  }

  // Inline Code Styles
  p code,
  li code {
    background-color: #f9f9f9;
    padding: 2px 4px;
    color: $code-color;
    border: 1px solid #ccc;
    border-radius: 3px;
  }

  // Responsive Styles
  @media (max-width: 768px) {
    body {
      font-size: 14px;
      padding: 10px;
    }

    h1 {
      font-size: 24px;
    }

    h2 {
      font-size: 20px;
    }

    h3 {
      font-size: 18px;
    }
  }
}
