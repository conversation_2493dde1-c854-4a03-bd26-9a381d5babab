/* 搜索输入框自适应修复 - 全局样式 */

/* 强制覆盖搜索页面中的搜索框样式 */
.search-container .top-content {
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 0 16px !important;
}

.search-container .top-content > div {
  width: 100% !important;
}

/* 行模式搜索框样式 - 左对齐布局 */
.search-input.row {
  width: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;  /* 左对齐 */
}

.search-input.row h1 {
  flex-shrink: 0 !important;  /* 防止标题被压缩 */
  margin: 0 20px 0 0 !important;  /* 只保留右边距 */
}

.search-input.row .input {
  flex: 1 !important;           /* 占据剩余空间 */
  min-width: 300px !important;  /* 最小宽度 */
  max-width: none !important;   /* 移除最大宽度限制 */
}

/* Element UI 组件强制自适应 */
.search-input.row .input.el-input,
.search-input.row .input.el-input-group,
.search-input.row .input.el-input--large,
.search-input.row .input.el-input-group--append,
.search-input.row .input.el-input-group--prepend {
  width: 100% !important;
  flex: 1 !important;
}

/* 确保内部输入框也自适应 */
.search-input.row .input .el-input__inner {
  width: 100% !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .search-container .top-content {
    padding: 0 12px !important;
  }
  
  .search-input.row .input {
    min-width: 250px !important;
  }
  
  .search-input.row h1 {
    font-size: 20px !important;
    margin: 0 12px !important;
  }
}

@media (max-width: 480px) {
  .search-container .top-content {
    padding: 0 8px !important;
  }
  
  .search-input.row .input {
    min-width: 200px !important;
  }
  
  .search-input.row h1 {
    font-size: 18px !important;
    margin: 0 8px !important;
  }
}

/* 调试样式 - 可以临时启用来查看布局 */
/*
.search-container .top-content {
  border: 2px solid red !important;
}

.search-input.row {
  border: 2px solid blue !important;
}

.search-input.row .input {
  border: 2px solid green !important;
}
*/
