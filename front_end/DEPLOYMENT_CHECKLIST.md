# 🚀 生产环境部署检查清单

## 📦 构建状态
- ✅ 前端应用已重新构建 (npm run build:prod)
- ✅ 生成了新的文件哈希，避免缓存问题
- ✅ 模块错误处理系统已集成
- ✅ iframe 通信错误处理已增强

## 🔧 修复内容

### 1. 模块加载错误处理
- ✅ 路由级别错误处理 (router.onError)
- ✅ 全局错误处理器 (module-error-handler.js)
- ✅ 文件名哈希化防止缓存问题

### 2. iframe 通信错误修复
- ✅ 增强了 iframe-security.js 中的错误处理
- ✅ 改进了退出登录流程的错误处理
- ✅ 添加了备用路由跳转机制

### 3. JavaScript 错误修复
- ✅ 增强了异步函数的错误处理
- ✅ 添加了 try-catch 包装
- ✅ 改进了日志记录

## 📋 部署步骤

### 1. 备份当前版本
```bash
# 在生产服务器上备份当前版本
cp -r /path/to/current/dist /path/to/backup/dist_$(date +%Y%m%d_%H%M%S)
```

### 2. 部署新版本
```bash
# 复制新构建的文件到生产环境
scp -r front_end/dist/* user@*************:/path/to/production/
```

### 3. 更新 nginx 配置
确保 nginx 配置包含正确的 CSP 策略：
```nginx
add_header Content-Security-Policy "frame-ancestors 'self' http://*************:81 http://*************:80; frame-src 'self' http://*************:80 http://*************:81; ...";
```

### 4. 重启服务
```bash
# 重启 nginx
sudo systemctl reload nginx

# 如果需要，重启后端服务
sudo systemctl restart your-backend-service
```

## 🧪 测试清单

### 1. 基本功能测试
- [ ] 应用能正常加载
- [ ] 登录功能正常
- [ ] 搜索功能正常
- [ ] 文档预览正常

### 2. 错误处理测试
- [ ] 模块加载错误时页面能自动刷新
- [ ] iframe 通信正常，没有 origin 错误
- [ ] 退出登录功能正常，没有 JavaScript 错误

### 3. iframe 集成测试
- [ ] 在服务A中嵌入服务B iframe 正常
- [ ] 用户切换功能正常
- [ ] 退出登录后正确跳转

## 🔍 监控要点

### 1. 浏览器控制台
检查是否还有以下错误：
- ❌ `Failed to fetch dynamically imported module`
- ❌ `Failed to execute 'postMessage' on 'DOMWindow'`
- ❌ `ReferenceError: res is not defined`

### 2. 网络请求
检查是否有：
- ❌ 404 错误（文件未找到）
- ❌ CORS 错误
- ❌ CSP 违规错误

### 3. 功能验证
- ✅ 模块动态加载正常
- ✅ iframe 通信正常
- ✅ 用户认证流程正常

## 🚨 回滚计划

如果部署后出现问题：

### 1. 快速回滚
```bash
# 恢复备份版本
cp -r /path/to/backup/dist_TIMESTAMP/* /path/to/production/
sudo systemctl reload nginx
```

### 2. 问题排查
- 检查浏览器控制台错误
- 检查服务器日志
- 检查 nginx 错误日志

## 📞 联系信息

如果遇到问题，请检查：
1. 浏览器开发者工具控制台
2. 网络请求状态
3. 服务器错误日志

## ✅ 部署完成确认

部署完成后，请确认：
- [ ] 所有测试项目都通过
- [ ] 没有控制台错误
- [ ] 用户反馈正常
- [ ] 系统运行稳定

---

**部署时间**: ___________  
**部署人员**: ___________  
**版本标识**: ___________  
**备注**: ___________
