# 动态导入模块错误处理

## 问题描述

在生产环境中，经常会遇到以下错误：
```
TypeError: Failed to fetch dynamically imported module http://example.com/assets/preview.ea6a5e9e.js
```

这个错误通常发生在以下情况：
1. 浏览器缓存了旧版本的模块引用，但服务器上的文件已经更新
2. 网络连接不稳定导致模块加载失败
3. 服务器重新部署后，文件名哈希值发生变化

## 解决方案

### 1. 路由级别的错误处理

在 `router/index.js` 中添加了 `router.onError` 处理器：

```javascript
router.onError((error, to) => {
  if (error.message.includes('Failed to fetch dynamically imported module')) {
    window.location = to.fullPath
  }
})
```

### 2. 全局错误处理器

创建了 `utils/module-error-handler.js` 工具，提供：

- **错误识别**：`isModuleLoadError(error)` - 识别是否为模块加载错误
- **错误处理**：`handleModuleLoadError(error, options)` - 统一处理模块错误
- **重试机制**：`createRetryableImport(importFn, maxRetries)` - 创建带重试的导入函数
- **全局设置**：`setupGlobalModuleErrorHandlers()` - 设置全局错误监听器

### 3. 构建配置优化

在 `vite.config.js` 中启用了文件名哈希化：

```javascript
rollupOptions: {
  output: {
    entryFileNames: '[name].[hash].js',
    chunkFileNames: '[name].[hash].js',
    assetFileNames: '[name].[hash].[ext]'
  }
}
```

这确保每次构建都生成新的文件名，避免缓存问题。

## 使用方法

### 基本使用

错误处理器会自动工作，无需额外配置。当检测到模块加载错误时，会：

1. 记录详细的错误信息
2. 自动刷新页面或跳转到目标路由
3. 清理可能的应用缓存

### 手动处理

如果需要手动处理模块错误：

```javascript
import { isModuleLoadError, handleModuleLoadError } from '@/utils/module-error-handler'

try {
  const module = await import('@/some-module.vue')
} catch (error) {
  if (isModuleLoadError(error)) {
    handleModuleLoadError(error, {
      targetPath: '/fallback-route',
      forceReload: true,
      retryDelay: 2000
    })
  }
}
```

### 创建重试导入

对于关键模块，可以使用重试机制：

```javascript
import { createRetryableImport } from '@/utils/module-error-handler'

const retryableImport = createRetryableImport(
  () => import('@/critical-module.vue'),
  3, // 最大重试次数
  1000 // 重试延迟（毫秒）
)

const module = await retryableImport()
```

## 测试

访问 `/test-module-error` 页面可以测试错误处理机制：

1. **模拟动态导入失败** - 测试基本错误识别
2. **模拟代码块加载失败** - 测试chunk加载错误
3. **测试重试机制** - 验证重试功能
4. **清理应用缓存** - 清理可能的缓存问题

## 监控和调试

错误处理器会在控制台输出详细的调试信息：

```
🔄 检测到模块加载失败: Failed to fetch dynamically imported module
📝 这通常是由于以下原因之一:
   1. 浏览器缓存了旧版本的模块引用
   2. 网络连接不稳定
   3. 服务器上的文件已更新但浏览器未刷新
🔄 跳转到目标路径以解决问题: /target-route
```

## 最佳实践

1. **启用文件名哈希化** - 确保每次构建生成唯一文件名
2. **设置合适的缓存策略** - 在服务器端配置适当的HTTP缓存头
3. **监控错误频率** - 如果错误频繁发生，可能需要检查网络或服务器配置
4. **用户体验优化** - 考虑在刷新前显示加载提示

## 注意事项

- 错误处理器会自动刷新页面，这会导致用户当前的操作状态丢失
- 在开发环境中，这类错误较少发生，主要影响生产环境
- 如果错误持续发生，可能需要检查服务器配置或网络连接

## 相关文件

- `src/router/index.js` - 路由错误处理
- `src/main.js` - 全局错误处理设置
- `src/utils/module-error-handler.js` - 错误处理工具
- `src/views/test-module-error.vue` - 测试页面
- `vite.config.js` - 构建配置优化
