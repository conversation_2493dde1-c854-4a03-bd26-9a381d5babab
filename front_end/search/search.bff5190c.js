import{submitPermissionRequest as p,checkDocumentPermission as m}from"./permission.aa52f4aa.js";import{n as d,g,r as f}from"./index.87b88fdf.js";import{s as _,a as y,e as v}from"./search.a1e21804.js";import{d as w}from"./index.d17f4061.js";import"./element.65d3a407.js";import{M as k}from"./index.d186ba97.js";import"./vendor.056885aa.js";import"./utils.7bf54f36.js";import"./request_ai.f9c15611.js";const b={name:"PermissionRequestDialog",props:{visible:{type:Boolean,default:!1},documentInfo:{type:Object,default:()=>({})}},data(){return{submitting:!1,form:{permission_type:"read",reason:""}}},watch:{visible(t){t&&this.resetForm()}},methods:{resetForm(){this.form={permission_type:"read",reason:""}},handleClose(){this.$emit("update:visible",!1),this.$emit("close")},async handleSubmit(){if(!this.form.reason.trim()){this.$message.error("\u8BF7\u586B\u5199\u7533\u8BF7\u7406\u7531");return}if(this.form.reason.trim().length<10){this.$message.error("\u7533\u8BF7\u7406\u7531\u81F3\u5C11\u9700\u898110\u4E2A\u5B57\u7B26");return}this.submitting=!0;try{console.log("\u63D0\u4EA4\u6743\u9650\u7533\u8BF7\uFF0C\u6587\u6863\u4FE1\u606F:",this.documentInfo);const t={...this.form,reason:this.form.reason.trim()};if(this.documentInfo.doc_id&&this.documentInfo.doc_id.trim())t.doc_id=this.documentInfo.doc_id.trim(),console.log("\u4F7F\u7528 doc_id:",t.doc_id);else if(this.documentInfo.id)t.document_id=this.documentInfo.id,console.log("\u4F7F\u7528 document_id:",t.document_id);else if(this.documentInfo.url&&this.documentInfo.url.trim())t.document_url=this.documentInfo.url.trim(),console.log("\u4F7F\u7528 document_url:",t.document_url);else throw console.error("\u7F3A\u5C11\u6587\u6863\u6807\u8BC6\u4FE1\u606F\uFF0CdocumentInfo:",this.documentInfo),console.error("\u68C0\u67E5\u7684\u5B57\u6BB5: doc_id=",this.documentInfo.doc_id,", id=",this.documentInfo.id,", url=",this.documentInfo.url),new Error("\u7F3A\u5C11\u6587\u6863\u6807\u8BC6\u4FE1\u606F\uFF0C\u65E0\u6CD5\u63D0\u4EA4\u6743\u9650\u7533\u8BF7");console.log("\u6700\u7EC8\u8BF7\u6C42\u6570\u636E:",t);const e=await p(t);e.code===200?(this.$message.success("\u6743\u9650\u7533\u8BF7\u63D0\u4EA4\u6210\u529F\uFF0C\u8BF7\u7B49\u5F85\u5BA1\u6279"),this.$emit("success",e.data),this.handleClose()):this.$message.error(e.msg||"\u7533\u8BF7\u63D0\u4EA4\u5931\u8D25")}catch(t){console.error("\u63D0\u4EA4\u6743\u9650\u7533\u8BF7\u5931\u8D25:",t),this.$message.error("\u7533\u8BF7\u63D0\u4EA4\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5")}finally{this.submitting=!1}}}};var C=function(){var e=this,s=e._self._c;return s("el-dialog",{attrs:{title:"\u7533\u8BF7\u6587\u6863\u6743\u9650",visible:e.visible,width:"500px","before-close":e.handleClose},on:{"update:visible":function(i){e.visible=i}}},[s("div",{staticClass:"permission-request-form"},[s("div",{staticClass:"document-info"},[s("h4",[e._v("\u6587\u6863\u4FE1\u606F")]),s("p",[s("strong",[e._v("\u6807\u9898:")]),e._v(" "+e._s(e.documentInfo.title||"\u672A\u77E5\u6587\u6863"))]),s("p",[s("strong",[e._v("\u9879\u76EE:")]),e._v(" "+e._s(e.documentInfo.project_name||"\u672A\u77E5\u9879\u76EE"))]),e.documentInfo.owner_name?s("p",[s("strong",[e._v("\u6240\u6709\u8005:")]),e._v(" "+e._s(e.documentInfo.owner_name))]):e._e()]),s("div",{staticClass:"permission-type"},[s("h4",[e._v("\u7533\u8BF7\u6743\u9650\u7C7B\u578B")]),s("el-radio-group",{model:{value:e.form.permission_type,callback:function(i){e.$set(e.form,"permission_type",i)},expression:"form.permission_type"}},[s("el-radio",{attrs:{label:"read"}},[e._v("\u67E5\u770B\u6743\u9650")]),s("el-radio",{attrs:{label:"download"}},[e._v("\u4E0B\u8F7D\u6743\u9650")])],1)],1),s("div",{staticClass:"reason"},[s("h4",[e._v("\u7533\u8BF7\u7406\u7531 "),s("span",{staticClass:"required"},[e._v("*")])]),s("el-input",{attrs:{type:"textarea",rows:4,placeholder:"\u8BF7\u8BE6\u7EC6\u8BF4\u660E\u60A8\u7533\u8BF7\u6B64\u6743\u9650\u7684\u7406\u7531\uFF0C\u4F8B\u5982\uFF1A\u7528\u4E8E\u9879\u76EE\u5F00\u53D1\u3001\u5B66\u4E60\u53C2\u8003\u7B49",maxlength:"500","show-word-limit":""},model:{value:e.form.reason,callback:function(i){e.$set(e.form,"reason",i)},expression:"form.reason"}})],1)]),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:e.handleClose}},[e._v("\u53D6\u6D88")]),s("el-button",{attrs:{type:"primary",loading:e.submitting,disabled:!e.form.reason.trim()},on:{click:e.handleSubmit}},[e._v(" \u63D0\u4EA4\u7533\u8BF7 ")])],1)])},$=[],S=d(b,C,$,!1,null,"f82f910c",null,null);const P=S.exports;const q={name:"search",components:{PermissionRequestDialog:P},data(){return{md:new k({html:!0}),queryParams:{},loading:{search:!0,similar:!0,explain:!0},hits:[],similar:[],explain:"",pageNum:1,pageSize:10,total:0,permissionDialog:{visible:!1,documentInfo:{}},applyForm:{show:!1,currentDoc:{},reason:"",permission_type:"read"},processingCalls:new Set,searchInProgress:!1,lastSearchTime:0,searchDebounceDelay:1e3,consecutiveSearchCount:0,searchCooldownTime:3e3}},created(){const t=this.$store.getters["user/isLoggedIn"],e=this.$store.getters["user/hasToken"];if(!t||!e){localStorage.removeItem("queryParams"),this.queryParams={},this.loading.search=!1,this.$router.replace("/");return}this.queryParams=JSON.parse(localStorage.getItem("queryParams"))||{};const s=this.$store.getters["user/userInfo"],i=(s==null?void 0:s.user_id)||(s==null?void 0:s.id)||"anonymous",r=this.queryParams._userId;if(r&&r!==i){console.log("\u{1F504} \u68C0\u6D4B\u5230\u7528\u6237\u5207\u6362\uFF0C\u6E05\u7406\u4E0A\u4E00\u7528\u6237\u7684\u641C\u7D22\u53C2\u6570"),localStorage.removeItem("queryParams"),this.queryParams={},this.loading.search=!1,this.$router.replace("/");return}const{keyword:o,type:a}=this.queryParams;!o||!a||!["1","2"].includes(a)?(this.loading.search=!1,this.$router.replace("/")):this.search()},activated(){this.loading.search=!1,this.$nextTick(()=>{this.syncButtonHeights(),setTimeout(()=>this.syncButtonHeights(),100),setTimeout(()=>this.syncButtonHeights(),300)})},mounted(){this.$nextTick(()=>{this.syncButtonHeights(),setTimeout(()=>this.syncButtonHeights(),100),setTimeout(()=>this.syncButtonHeights(),300),setTimeout(()=>this.syncButtonHeights(),500)}),window.addEventListener("resize",this.syncButtonHeights),window.toggleThinking=function(t){const e=t.parentElement,s=e.querySelector(".ai-thinking-content"),i=e.querySelector(".ai-thinking-toggle");s.style.display==="none"||s.style.display===""?(s.style.display="block",i.classList.add("expanded")):(s.style.display="none",i.classList.remove("expanded"))}},beforeDestroy(){window.removeEventListener("resize",this.syncButtonHeights)},deactivated(){this.loading.search=!1,this.loading.similar=!1,this.loading.explain=!1},methods:{syncButtonHeights(){this.$nextTick(()=>{})},goToTasks(){this.$router.push("/tasks")},handlePageChange(t){this.pageNum=t,this.search()},async search(){var i,r,o,a,h;(i=this.$refs.listContainer)==null||i.scrollTo({top:0}),this.loading.search=!0;const{type:t,keyword:e}=this.queryParams;let s={};t==="1"?s={bool:{should:[{match:{"project_name.text":e}},{match:{content:e}}]}}:t==="2"&&(s={match:{depart:e}});try{const n=await _({query:s,highlight:{fields:{"project_name.text":{},content:{}}},_source:{excludes:["embedding"]},from:(this.pageNum-1)*this.pageSize,size:this.pageSize});this.hits=((r=n.hits)==null?void 0:r.hits)||[],this.loading.search=!1,this.total=(o=n.hits)==null?void 0:o.total.value,this.hits.length||this.similarSearch(e)}catch(n){console.error("\u641C\u7D22\u8BF7\u6C42\u5931\u8D25:",n);let l="\u641C\u7D22\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5";if(n.name==="AbortError")return;n.status===400?l="\u641C\u7D22\u53C2\u6570\u6709\u8BEF\uFF0C\u8BF7\u68C0\u67E5\u641C\u7D22\u6761\u4EF6":n.status===429?l="\u641C\u7D22\u8BF7\u6C42\u8FC7\u4E8E\u9891\u7E41\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5":n.status>=500?l="\u641C\u7D22\u670D\u52A1\u6682\u65F6\u4E0D\u53EF\u7528\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5":n.code==="NETWORK_ERROR"||((a=n.message)==null?void 0:a.includes("Network Error"))?l="\u7F51\u7EDC\u8FDE\u63A5\u5F02\u5E38\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u540E\u91CD\u8BD5":(h=n.message)!=null&&h.includes("timeout")&&(l="\u641C\u7D22\u8BF7\u6C42\u8D85\u65F6\uFF0C\u8BF7\u7B80\u5316\u641C\u7D22\u6761\u4EF6\u6216\u7A0D\u540E\u91CD\u8BD5"),this.$message.error({message:l,duration:4e3,showClose:!0}),this.hits=[]}finally{this.loading.search=!1}},async similarSearch(){this.loading.similar=!0;const t=await y({question:this.queryParams.keyword});this.similar=t.source_documents,this.loading.similar=!1},async explainSearch(){var e;this.loading.explain=!0;const t=await v({question:this.queryParams.keyword,prompt:"\u540D\u8BCD\u89E3\u91CA",keywords:[]});(e=t.response)!=null&&e.includes("\u65E0\u76F8\u5173\u4FE1\u606F")?this.explain="":this.explain=t.response,this.loading.explain=!1},toSearch(){const t=this.$store.getters["user/isLoggedIn"],e=this.$store.getters["user/hasToken"];if(!t||!e){localStorage.removeItem("queryParams"),this.$router.push("/login");return}if(!this.queryParams.keyword.trim())return;const s=Date.now();if(this.searchInProgress){this.showResourceBusyMessage();return}const i=s-this.lastSearchTime;if(i<this.searchDebounceDelay){this.consecutiveSearchCount++,this.showSearchThrottleMessage(i);return}this.consecutiveSearchCount=0,this.lastSearchTime=s;const r=this.$store.getters["user/userInfo"],o=(r==null?void 0:r.user_id)||(r==null?void 0:r.id)||"anonymous";this.queryParams={...this.queryParams,_userId:o},localStorage.setItem("queryParams",JSON.stringify(this.queryParams)),this.pageNum=1,this.similar=[],this.explain="",this.performSearch()},showResourceBusyMessage(){this.$message.warning({message:"\u641C\u7D22\u6B63\u5728\u8FDB\u884C\u4E2D\uFF0C\u8BF7\u7A0D\u5019...",duration:2e3,showClose:!0})},showSearchThrottleMessage(t){const e=Math.ceil((this.searchDebounceDelay-t)/1e3);let s="";this.consecutiveSearchCount<=2?s=`\u641C\u7D22\u8BF7\u6C42\u8FC7\u4E8E\u9891\u7E41\uFF0C\u8BF7\u7B49\u5F85 ${e} \u79D2\u540E\u518D\u8BD5`:this.consecutiveSearchCount<=5?s=`\u7CFB\u7EDF\u8D44\u6E90\u6709\u9650\uFF0C\u8BF7\u7B49\u5F85 ${e} \u79D2\u540E\u518D\u641C\u7D22`:s=`\u4E3A\u4FDD\u8BC1\u670D\u52A1\u8D28\u91CF\uFF0C\u8BF7\u7B49\u5F85 ${e} \u79D2\u540E\u518D\u641C\u7D22`,this.$message.warning({message:s,duration:3e3,showClose:!0})},async performSearch(){try{this.searchInProgress=!0,await this.search(),await this.explainSearch()}catch(t){console.error("\u641C\u7D22\u6267\u884C\u5931\u8D25:",t)}finally{this.searchInProgress=!1}},async handleDocumentClick(t,e,s){try{let i=encodeURIComponent(t);i="/download?url="+i,i=i.replace(".json",".pdf");const r=await m({doc_id:s,document_url:i,permission_type:"read"});if(r.code===200)if(r.permission===!0)this.preview(t,s);else{const o={url:r.url||i,title:e,project_name:e,source_url:t};s&&s.trim()&&(o.doc_id=s.trim()),r.document_info&&Object.assign(o,{id:r.document_info.document_id,doc_id:r.document_info.doc_id||s,title:r.document_info.title||e,project_name:r.document_info.project_name||e,owner_name:r.document_info.owner_name,department_name:r.document_info.department_name}),!o.doc_id&&!o.id&&!o.url&&(console.error("\u8B66\u544A\uFF1A\u6CA1\u6709\u53EF\u7528\u7684\u6587\u6863\u6807\u8BC6\uFF0C\u4F7F\u7528\u539F\u59CBURL\u4F5C\u4E3A\u5907\u9009"),o.url=i),this.showPermissionRequestDialog(o)}else console.error("\u6743\u9650\u68C0\u67E5\u5931\u8D25:",r.msg)}catch(i){console.error("\u6743\u9650\u68C0\u67E5\u5F02\u5E38:",i),this.$message.error("\u6743\u9650\u68C0\u67E5\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5")}},extractDocIdFromUrl(t){try{const e=t.replace(/\.(json|pdf)$/,"");return btoa(e).replace(/[^a-zA-Z0-9]/g,"").substring(0,32)}catch(e){return console.error("\u751F\u6210doc_id\u5931\u8D25:",e),null}},async checkDocumentPermissionWithAuth(t,e){var s;try{return this.$store.getters["user/token"]?await m({document_url:t,permission_type:e}):(this.$message.warning("\u8BF7\u5148\u767B\u5F55\u540E\u518D\u67E5\u770B\u6587\u6863"),this.$router.push("/login"),null)}catch(i){if(((s=i.response)==null?void 0:s.status)===401)return this.$message.warning("\u767B\u5F55\u5DF2\u8FC7\u671F\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"),this.$router.push("/login"),null;throw i}},showPermissionRequestDialog(t){this.permissionDialog.documentInfo=t,this.permissionDialog.visible=!0},onPermissionRequestSuccess(){this.$message.success("\u6743\u9650\u7533\u8BF7\u5DF2\u63D0\u4EA4\uFF0C\u8BF7\u7B49\u5F85\u7BA1\u7406\u5458\u5BA1\u6838")},preview(t,e=null){t=encodeURIComponent(t),t="/download?url="+t,t=t.replace(".json",".pdf"),localStorage.setItem("url",t),e&&(localStorage.setItem("docId",e),localStorage.setItem("permissionChecked","true"),localStorage.setItem("permissionResult",JSON.stringify({hasPermission:!0,timestamp:Date.now(),docId:e}))),this.$router.push({path:"/preview",query:{url:t,docId:e,timestamp:Date.now()}})},handleSimilarClick(t){this.similar=[],this.explain="",this.queryParams.keyword=t,this.toSearch(this.queryParams)},async previewOrDownload(t,e){new Date().toISOString(),Math.random().toString(36).substr(2,9);const s=`${t._id}_${e}`;if(this.processingCalls||(this.processingCalls=new Set),!this.processingCalls.has(s)){this.processingCalls.add(s);try{const i=t._source.doc_id,r=t._source.source,o=Date.now(),a=await g(t._source.doc_id,e),n=Date.now()-o;if(a.has_permission){let l=t._source.source,c=encodeURIComponent(l);if(c="/download?url="+c,c=c.replace(".json",".pdf"),e=="read")localStorage.setItem("url",c),this.$router.push("/preview");else{let u=l.split("/").pop();u=u.replace(".json",".pdf"),w(c,u)}}else this.applyForm.currentDoc=t._source,this.applyForm.permission_type=e,this.applyForm.show=!0}catch(i){console.error("previewOrDownload \u6267\u884C\u51FA\u9519:",i.message),this.$message.error("\u64CD\u4F5C\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5")}finally{this.processingCalls.delete(s)}}},async apply(){try{if(!this.applyForm.reason.trim())return this.$message.error("\u7533\u8BF7\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A");const t=await f({doc_id:this.applyForm.currentDoc.doc_id,permission_type:this.applyForm.permission_type,reason:this.applyForm.reason});this.$message.success(t.msg),this.applyForm.reason="",this.applyForm.show=!1}catch(t){console.error("\u6743\u9650\u7533\u8BF7\u5931\u8D25:",t.message||t),this.$message.error("\u51FA\u9519\u4E86 \u8BF7\u7A0D\u540E\u518D\u8BD5")}},processHistoryResponse(t){if(!t||typeof t!="string")return t;try{if(t.includes("<think>")){const e=/<think>([\s\S]*?)<\/think>/,s=t.match(e);if(s){const i=s[1].trim(),r=t.replace(e,"").trim();return`
<div class="ai-thinking-container">
    <div class="ai-thinking-toggle" onclick="toggleThinking(this)">
        <svg class="ai-thinking-icon" width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.786 4.167L2.765 1.259c-.416-.4-.985-.482-1.273-.183-.287.298-.183.864.233 1.264l3.021 2.908c.416.4.986.482 1.273.184.287-.299.183-.865-.233-1.265z" fill="currentColor"></path>
            <path d="M8.197 1.206L5.288 4.208c-.4.413-.484.982-.187 1.27.298.289.864.187 1.265-.227L9.274 2.25c.401-.414.485-.983.187-1.271-.297-.288-.863-.187-1.264.227z" fill="currentColor"></path>
        </svg>
        <span class="ai-thinking-label">\u601D\u8003\u8FC7\u7A0B</span>
    </div>
    <div class="ai-thinking-content" style="display: none;">
        <div class="ai-thinking-text">${i}</div>
    </div>
</div>

${r}`}}return t}catch(e){return console.error("\u5904\u7406\u54CD\u5E94\u5185\u5BB9\u65F6\u51FA\u9519:",e),t}}}};var I=function(){var e=this,s=e._self._c;return s("div",[s("div",[s("el-dialog",{attrs:{visible:e.applyForm.show,title:"\u63D0\u793A"},on:{"update:visible":function(i){return e.$set(e.applyForm,"show",i)}},scopedSlots:e._u([{key:"footer",fn:function(){return[s("div",{staticClass:"dialog-footer"},[s("el-button",{on:{click:function(i){e.applyForm.show=!1}}},[e._v("\u53D6\u6D88")]),s("el-button",{attrs:{type:"primary"},on:{click:e.apply}},[e._v(" \u63D0\u4EA4\u7533\u8BF7 ")])],1)]},proxy:!0}])},[s("p",{staticClass:"font-bold"},[e._v("\u6587\u6863\uFF1A"+e._s(e.applyForm.currentDoc.project_name))]),s("p",[e._v(" \u5982\u9700\u67E5\u770B\u6216\u4E0B\u8F7D\u6B64\u6587\u6863\uFF0C\u8BF7\u5148\u63D0\u4EA4\u7533\u8BF7\uFF0C\u7BA1\u7406\u5458\u5BA1\u6838\u901A\u8FC7\u4E4B\u540E\u65B9\u53EF\u67E5\u770B\u6216\u4E0B\u8F7D ")]),s("br"),s("el-form",{staticClass:"apply-form",attrs:{"label-position":"left"}},[s("el-form-item",{attrs:{label:"\u7533\u8BF7\u7C7B\u578B"}},[s("el-radio-group",{model:{value:e.applyForm.permission_type,callback:function(i){e.$set(e.applyForm,"permission_type",i)},expression:"applyForm.permission_type"}},[s("el-radio",{attrs:{label:"read"}},[e._v("\u67E5\u770B")]),s("el-radio",{attrs:{label:"download"}},[e._v("\u4E0B\u8F7D")])],1)],1),s("el-form-item",{attrs:{label:"\u7533\u8BF7\u539F\u56E0"}},[s("el-input",{attrs:{autocomplete:"off",type:"textarea"},model:{value:e.applyForm.reason,callback:function(i){e.$set(e.applyForm,"reason",i)},expression:"applyForm.reason"}})],1)],1)],1)],1),s("div",{staticClass:"search-container"},[s("div",{staticClass:"top"},[s("div",{staticClass:"top-content"},[s("div",{staticClass:"search-header"},[s("h1",{staticClass:"search-title",on:{click:function(i){return e.$router.push("/")}}},[e._v("\u6863\u6848\u68C0\u7D22")]),s("div",{staticClass:"search-input-group"},[s("el-input",{staticClass:"search-input",attrs:{placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u5B57",size:"large"},nativeOn:{keydown:function(i){return!i.type.indexOf("key")&&e._k(i.keyCode,"enter",13,i.key,"Enter")?null:e.toSearch.apply(null,arguments)}},model:{value:e.queryParams.keyword,callback:function(i){e.$set(e.queryParams,"keyword",i)},expression:"queryParams.keyword"}},[s("el-select",{staticStyle:{width:"112px"},attrs:{slot:"prepend",placeholder:"\u8BF7\u9009\u62E9"},slot:"prepend",model:{value:e.queryParams.type,callback:function(i){e.$set(e.queryParams,"type",i)},expression:"queryParams.type"}},[s("el-option",{attrs:{label:"\u7EFC\u5408\u641C\u7D22",value:"1"}}),s("el-option",{attrs:{label:"\u7CBE\u786E\u641C\u7D22",value:"3"}})],1),s("template",{slot:"append"},[s("div",{staticClass:"button-group"},[s("el-button",{staticClass:"search-btn",attrs:{icon:"el-icon-search",loading:e.searchInProgress,disabled:e.searchInProgress},on:{click:e.toSearch}}),s("el-button",{staticClass:"tasks-btn",attrs:{icon:"el-icon-s-order",title:"\u4EFB\u52A1\u67E5\u770B"},on:{click:e.goToTasks}})],1)])],2)],1)])])]),s("div",{staticClass:"placeholder",staticStyle:{height:"72px"}}),s("div",{staticClass:"search"},[s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading.search,expression:"loading.search"}],ref:"listContainer",staticClass:"list-container",attrs:{"element-loading-text":"\u641C\u7D22\u4E2D"}},[s("div",{staticClass:"list"},[e.pageNum==1&&!e.loading.search?s("span",{staticClass:"total"},[e._v("\u672C\u6B21\u5171\u68C0\u7D22\u51FA"+e._s(e.total)+"\u6761\u7ED3\u679C")]):e._e(),e.explain.length&&e.pageNum==1?s("el-card",{attrs:{shadow:"never"},scopedSlots:e._u([{key:"header",fn:function(){return[s("span",[e._v("\u8BCD\u6761\u89E3\u91CA")])]},proxy:!0}],null,!1,3381986460)},[s("div",{staticClass:"card-body explain"},[s("div",{staticClass:"markdown ai-response-content",domProps:{innerHTML:e._s(e.md.render(e.processHistoryResponse(e.explain||"")))}})])]):!e.explain.length&&!e.loading.similar&&e.hits.length==0?s("el-card",{attrs:{shadow:"never"},scopedSlots:e._u([{key:"header",fn:function(){return[s("span",[e._v("\u60A8\u662F\u5426\u8981\u641C\u7D22")])]},proxy:!0}])},[s("div",{staticClass:"similar-list card-body"},e._l(e.similar,function(i,r){return s("el-tag",{key:r,staticClass:"similar-item",attrs:{type:"success"},on:{click:function(o){return e.handleSimilarClick(i)}}},[e._v(" "+e._s(i)+" ")])}),1)]):e._e(),e.hits.length>0?e._l(e.hits,function(i){var r;return s("div",{key:i._id,staticClass:"res-card"},[s("div",{staticClass:"title",on:{click:function(o){return e.previewOrDownload(i,"read")}}},[i.highlight["project_name.text"]?s("span",e._l(i.highlight["project_name.text"],function(o,a){return s("span",{key:a,domProps:{innerHTML:e._s(o)}})}),0):s("span",[e._v(e._s(i._source.project_name))])]),s("div",{staticClass:"body"},[s("span",{staticClass:"time"},[e._v(e._s(i._source.year))]),i.highlight.content?s("span",e._l(i.highlight.content,function(o,a){return s("span",{key:a,domProps:{innerHTML:e._s(o)}})}),0):s("span",[e._v(e._s(i._source.content))])]),s("div",{staticClass:"footer"},[s("span",{staticClass:"download",on:{click:function(o){return e.previewOrDownload(i,"download")}}},[e._v("\u4E0B\u8F7D")]),i.highlight.action?s("span",e._l(i.highlight.action,function(o,a){return s("span",{key:a,domProps:{innerHTML:e._s(o)}})}),0):s("span",[e._v(e._s(i._source.action))]),e._l(((r=i._source.metadata)==null?void 0:r.doc_type)||[],function(o){return s("span",{key:o},[e._v(e._s(o))])})],2)])}):!e.loading.search&&!e.hits.length?[s("el-empty",{staticClass:"empty",attrs:{description:"\u65E0\u641C\u7D22\u7ED3\u679C"}})]:e._e(),s("el-pagination",{attrs:{background:"",layout:"prev, pager, next, total","page-size":e.pageSize,total:e.total,"current-page":e.pageNum,"hide-on-single-page":""},on:{"current-change":e.handlePageChange}})],2)])])]),s("PermissionRequestDialog",{attrs:{visible:e.permissionDialog.visible,"document-info":e.permissionDialog.documentInfo},on:{"update:visible":function(i){return e.$set(e.permissionDialog,"visible",i)},success:e.onPermissionRequestSuccess}})],1)},x=[],D=d(q,I,x,!1,null,"0e28eb96",null,null);const O=D.exports;export{O as default};
