<head>      <title>Awesome-pyecharts</title>      <script src="/js/echarts.min.js"></script>    </head>    <body>      <div id="main" style="width: 600px;height:400px;"></div>      <script>        var myChart = echarts.init(document.getElementById("main"));        var option = {    "animation": true,    "animationThreshold": 2000,    "animationDuration": 1000,    "animationEasing": "cubicOut",    "animationDelay": 0,    "animationDurationUpdate": 300,    "animationEasingUpdate": "cubicOut",    "animationDelayUpdate": 0,    "aria": {        "enabled": false    },    "color": [        "#5470c6",        "#91cc75",        "#fac858",        "#ee6666",        "#73c0de",        "#3ba272",        "#fc8452",        "#9a60b4",        "#ea7ccc"    ],    "series": [        {            "type": "bar",            "name": "Sales",            "legendHoverLink": true,            "data": [                569.23,                460.74,                541.36,                682.78,                159.35,                357.12,                852.46,                741.32,                369.54,                523.98,                412.69,                785.95            ],            "realtimeSort": false,            "showBackground": false,            "stackStrategy": "samesign",            "cursor": "pointer",            "barMinHeight": 0,            "barCategoryGap": "20%",            "barGap": "30%",            "large": false,            "largeThreshold": 400,            "seriesLayoutBy": "column",            "datasetIndex": 0,            "clip": true,            "zlevel": 0,            "z": 2,            "label": {                "show": true,                "margin": 8            }        }    ],    "legend": [        {            "data": [                "Sales"            ],            "selected": {}        }    ],    "tooltip": {        "show": true,        "trigger": "item",        "triggerOn": "mousemove|click",        "axisPointer": {            "type": "line"        },        "showContent": true,        "alwaysShowContent": false,        "showDelay": 0,        "hideDelay": 100,        "enterable": false,        "confine": false,        "appendToBody": false,        "transitionDuration": 0.4,        "textStyle": {            "fontSize": 14        },        "borderWidth": 0,        "padding": 5,        "order": "seriesAsc"    },    "xAxis": [        {            "show": true,            "scale": false,            "nameLocation": "end",            "nameGap": 15,            "gridIndex": 0,            "inverse": false,            "offset": 0,            "splitNumber": 5,            "minInterval": 0,            "splitLine": {                "show": true,                "lineStyle": {                    "show": true,                    "width": 1,                    "opacity": 1,                    "curveness": 0,                    "type": "solid"                }            },            "data": [                "January",                "February",                "March",                "April",                "May",                "June",                "July",                "August",                "September",                "October",                "November",                "December"            ]        }    ],    "yAxis": [        {            "show": true,            "scale": false,            "nameLocation": "end",            "nameGap": 15,            "gridIndex": 0,            "inverse": false,            "offset": 0,            "splitNumber": 5,            "minInterval": 0,            "splitLine": {                "show": true,                "lineStyle": {                    "show": true,                    "width": 1,                    "opacity": 1,                    "curveness": 0,                    "type": "solid"                }            }        }    ]};        myChart.setOption(option);      </script>    </body>  