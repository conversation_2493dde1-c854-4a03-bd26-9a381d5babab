import{d as t}from"./vendor.056885aa.js";import{n as i}from"./index.87b88fdf.js";import"./element.65d3a407.js";import"./utils.7bf54f36.js";const n={name:"Login",data(){return{loginForm:{username:"",password:""},loginRules:{username:[{required:!0,message:"\u8BF7\u8F93\u5165\u7528\u6237\u540D",trigger:"blur"},{min:3,max:30,message:"\u7528\u6237\u540D\u957F\u5EA6\u5728 3 \u5230 30 \u4E2A\u5B57\u7B26",trigger:"blur"}],password:[{required:!0,message:"\u8BF7\u8F93\u5165\u5BC6\u7801",trigger:"blur"},{min:6,message:"\u5BC6\u7801\u957F\u5EA6\u4E0D\u80FD\u5C11\u4E8E 6 \u4E2A\u5B57\u7B26",trigger:"blur"}]},loading:!1}},methods:{...t("user",["login"]),async handleLogin(){try{await this.$refs.loginForm.validate(),this.loading=!0,await this.login(this.loginForm),this.$message.success("\u767B\u5F55\u6210\u529F\uFF01");const s=this.$route.query.redirect||"/";this.$router.replace(s)}catch(s){console.error("\u767B\u5F55\u5931\u8D25:",s),this.$message.error(s.message||"\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7528\u6237\u540D\u548C\u5BC6\u7801")}finally{this.loading=!1}},goToRegister(){this.$router.push("/register")}}};var a=function(){var e=this,r=e._self._c;return r("div",{staticClass:"login-container"},[r("div",{staticClass:"login-wrapper"},[e._m(0),r("div",{staticClass:"login-form-wrapper"},[r("div",{staticClass:"login-form"},[r("h2",[e._v("\u7528\u6237\u767B\u5F55")]),r("el-form",{ref:"loginForm",attrs:{model:e.loginForm,rules:e.loginRules},nativeOn:{submit:function(o){return o.preventDefault(),e.handleLogin.apply(null,arguments)}}},[r("el-form-item",{attrs:{prop:"username"}},[r("el-input",{attrs:{placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D","prefix-icon":"el-icon-user",size:"large",clearable:""},model:{value:e.loginForm.username,callback:function(o){e.$set(e.loginForm,"username",o)},expression:"loginForm.username"}})],1),r("el-form-item",{attrs:{prop:"password"}},[r("el-input",{attrs:{type:"password",placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801","prefix-icon":"el-icon-lock",size:"large","show-password":""},nativeOn:{keyup:function(o){return!o.type.indexOf("key")&&e._k(o.keyCode,"enter",13,o.key,"Enter")?null:e.handleLogin.apply(null,arguments)}},model:{value:e.loginForm.password,callback:function(o){e.$set(e.loginForm,"password",o)},expression:"loginForm.password"}})],1),r("el-form-item",[r("el-button",{staticClass:"login-btn",attrs:{type:"primary",size:"large",loading:e.loading},on:{click:e.handleLogin}},[e._v(" "+e._s(e.loading?"\u767B\u5F55\u4E2D...":"\u7ACB\u5373\u767B\u5F55")+" ")])],1)],1),r("div",{staticClass:"login-footer"},[r("span",[e._v("\u8FD8\u6CA1\u6709\u8D26\u53F7\uFF1F")]),r("el-button",{attrs:{type:"text"},on:{click:e.goToRegister}},[e._v("\u7ACB\u5373\u6CE8\u518C")])],1)],1)])])])},l=[function(){var s=this,e=s._self._c;return e("div",{staticClass:"login-info"},[e("div",{staticClass:"logo"},[e("i",{staticClass:"el-icon-document"}),e("h1",[s._v("\u6587\u6863\u7BA1\u7406\u7CFB\u7EDF")])]),e("p",{staticClass:"description"},[s._v(" \u5B89\u5168\u3001\u9AD8\u6548\u7684\u4F01\u4E1A\u7EA7\u6587\u6863\u7BA1\u7406\u89E3\u51B3\u65B9\u6848"),e("br"),s._v(" \u652F\u6301\u591A\u90E8\u95E8\u534F\u4F5C\uFF0C\u7EC6\u7C92\u5EA6\u6743\u9650\u63A7\u5236 ")])])}],c=i(n,a,l,!1,null,"c88515b3",null,null);const d=c.exports;export{d as default};
