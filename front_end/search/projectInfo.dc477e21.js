import{b as l}from"./search.a1e21804.js";import o from"./projectInfoDesc.bbefed2b.js";import{n}from"./index.87b88fdf.js";import"./request_ai.f9c15611.js";import"./vendor.056885aa.js";import"./element.65d3a407.js";import"./utils.7bf54f36.js";const s={name:"projectInfo",components:{ProjectInfoDesc:o},data(){return{queryData:{loading:!1,queryParam:{projectYear:"",projectName:"",skill:"",content:"",result:"",gtMoney:void 0,ltMoney:void 0,total:0,pageSize:8,pageNum:1}},list:[]}},mounted(){this.getList()},methods:{show(a){this.$refs.desc.show(a)},back(){this.$router.back()},reset(){this.queryData.queryParam=this.$options.data().queryData.queryParam,this.getList()},handleGtChange(a){const{ltMoney:e,gtMoney:t}=this.queryData.queryParam;e&&t>=e&&this.$nextTick(()=>{this.queryData.queryParam.gtMoney=void 0})},handleLtChange(a){const{ltMoney:e,gtMoney:t}=this.queryData.queryParam;t&&t>=e&&this.$nextTick(()=>{this.queryData.queryParam.ltMoney=void 0})},handlePageChange(a){this.queryData.queryParam.pageNum=a,this.getList()},getList(){this.queryData.loading=!0;let a=this.queryData.queryParam;console.log(a);let e={query:{bool:{should:[]}},highlight:{fields:{skill:{},content:{},result:{}}},from:a.pageSize*(a.pageNum-1),size:a.pageSize};a.projectName&&a.projectName!=""&&e.query.bool.should.push({match:{projectName:a.projectName}}),a.projectYear&&a.projectYear!=""&&a.projectYear.forEach(t=>{e.query.bool.should.push({match:{projectYear:t}})}),console.log(a.ltMoney,a.gtMoney),a.ltMoney!==void 0&&(e.query.bool.filter={range:{money:{lt:a.ltMoney}}},a.gtMoney!==void 0&&(e.query.bool.filter.range.money.gt=a.gtMoney)),a.gtMoney!==void 0&&(e.query.bool.filter={range:{money:{gt:a.gtMoney}}},a.ltMoney!==void 0&&(e.query.bool.filter.range.money.lt=a.ltMoney)),a.content&&a.content!=""&&e.query.bool.should.push({match:{content:a.content}}),a.result&&a.result!=""&&e.query.bool.should.push({match:{result:a.result}}),a.skill&&a.skill!=""&&e.query.bool.should.push({match:{skill:a.skill}}),l(e).then(t=>{var r;console.log("\u67E5\u8BE2\u53C2\u6570",e),this.queryData.loading=!1,this.list=t.hits.hits,this.queryData.queryParam.total=(r=t.hits)==null?void 0:r.total.value})}}};var u=function(){var e=this,t=e._self._c;return t("div",{staticClass:"project-info"},[t("el-page-header",{staticClass:"header",attrs:{content:"\u7CBE\u786E\u641C\u7D22"},on:{back:e.back}}),t("el-form",{staticClass:"query-form",attrs:{size:"small"}},[t("el-form-item",{attrs:{label:"\u9879\u76EE\u540D\u79F0"}},[t("el-input",{attrs:{placeholder:"\u8BF7\u8F93\u5165\u9879\u76EE\u540D\u79F0",clearable:""},model:{value:e.queryData.queryParam.projectName,callback:function(r){e.$set(e.queryData.queryParam,"projectName",r)},expression:"queryData.queryParam.projectName"}})],1),t("el-form-item",{attrs:{label:"\u9879\u76EE\u5E74\u4EFD"}},[t("el-date-picker",{attrs:{type:"years","value-format":"yyyy",placeholder:"\u9009\u62E9\u4E00\u4E2A\u6216\u591A\u4E2A\u5E74"},model:{value:e.queryData.queryParam.projectYear,callback:function(r){e.$set(e.queryData.queryParam,"projectYear",r)},expression:"queryData.queryParam.projectYear"}})],1),t("el-form-item",{attrs:{label:"\u6295\u8D44\u91D1\u989D\uFF08\u4E07\u5143\uFF09"}},[t("div",{staticClass:"money"},[t("el-input-number",{attrs:{controls:!1,placeholder:"\u8BF7\u8F93\u5165"},on:{change:e.handleGtChange},model:{value:e.queryData.queryParam.gtMoney,callback:function(r){e.$set(e.queryData.queryParam,"gtMoney",r)},expression:"queryData.queryParam.gtMoney"}}),t("span",[e._v("\u81F3")]),t("el-input-number",{attrs:{controls:!1,placeholder:"\u8BF7\u8F93\u5165"},on:{change:e.handleLtChange},model:{value:e.queryData.queryParam.ltMoney,callback:function(r){e.$set(e.queryData.queryParam,"ltMoney",r)},expression:"queryData.queryParam.ltMoney"}})],1)]),t("el-form-item",{attrs:{label:"\u5173\u952E\u6280\u672F"}},[t("el-input",{attrs:{placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u6280\u672F",clearable:""},model:{value:e.queryData.queryParam.skill,callback:function(r){e.$set(e.queryData.queryParam,"skill",r)},expression:"queryData.queryParam.skill"}})],1),t("el-form-item",{attrs:{label:"\u7814\u7A76\u5185\u5BB9"}},[t("el-input",{attrs:{placeholder:"\u8BF7\u8F93\u5165\u7814\u7A76\u5185\u5BB9",clearable:""},model:{value:e.queryData.queryParam.content,callback:function(r){e.$set(e.queryData.queryParam,"content",r)},expression:"queryData.queryParam.content"}})],1),t("el-form-item",{attrs:{label:"\u4EA4\u4ED8\u6210\u679C"}},[t("el-input",{attrs:{placeholder:"\u8BF7\u8F93\u5165\u4EA4\u4ED8\u6210\u679C",clearable:""},model:{value:e.queryData.queryParam.result,callback:function(r){e.$set(e.queryData.queryParam,"result",r)},expression:"queryData.queryParam.result"}})],1),t("el-form-item",[t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.getList}},[e._v(" \u641C\u7D22 ")]),t("el-button",{attrs:{size:"small",type:"default"},on:{click:e.reset}},[e._v(" \u91CD\u7F6E ")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.queryData.loading,expression:"queryData.loading"}],attrs:{data:e.list,stripe:"",border:"","element-loading-text":"\u52A0\u8F7D\u4E2D..."}},[t("el-table-column",{attrs:{type:"index",label:"\u5E8F\u53F7",align:"center",width:"50"}}),t("el-table-column",{attrs:{label:"\u9879\u76EE\u540D\u79F0",prop:"_source.projectName",align:"center","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{label:"\u9879\u76EE\u7F16\u53F7",prop:"_source.projectNo",align:"center",width:"170","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{label:"\u9879\u76EE\u5E74\u4EFD",prop:"_source.projectYear",align:"center",width:"80"}}),t("el-table-column",{attrs:{label:"\u8D77\u6B62\u5E74\u9650",prop:"_source.projectBeginEndYear","show-overflow-tooltip":"",align:"center",width:"200"}}),t("el-table-column",{attrs:{label:"\u4E3B\u8981\u5B8C\u6210\u5355\u4F4D",prop:"_source.mainCompany",align:"center","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{label:"\u534F\u4F5C\u5B8C\u6210\u5355\u4F4D",prop:"_source.auxiliaryCompany",align:"center","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{label:"\u6295\u8D44\u91D1\u989D",prop:"_source.money",align:"center",width:"100","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(r){return[e._v(" "+e._s(r.row._source.money)+"\u4E07\u5143 ")]}}])}),t("el-table-column",{attrs:{label:"\u9A8C\u6536\u65F6\u95F4",prop:"_source.checkTime",align:"center",width:"150","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{label:"\u64CD\u4F5C",align:"center",width:"80",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-link",{staticStyle:{"margin-right":"10px"},attrs:{type:"primary"},on:{click:function(y){return e.show(r.row)}}},[e._v(" \u8BE6\u60C5 ")])]}}])})],1),t("el-pagination",{staticStyle:{"text-align":"right","margin-top":"10px"},attrs:{background:"",layout:"prev, pager, next, total","page-size":e.queryData.queryParam.pageSize,total:e.queryData.queryParam.total,"current-page":e.queryData.queryParam.pageNum},on:{"current-change":e.handlePageChange}}),t("project-info-desc",{ref:"desc"})],1)},i=[],c=n(s,u,i,!1,null,"1b6867b3",null,null);const b=c.exports;export{b as default};
