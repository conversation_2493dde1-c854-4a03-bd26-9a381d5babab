import{a as ft,V as te,b as or,c as sr,m as dt,d as ht}from"./vendor.056885aa.js";import{c as he,e as G,E as mt}from"./element.65d3a407.js";import{d as me}from"./utils.7bf54f36.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const h of document.querySelectorAll('link[rel="modulepreload"]'))u(h);new MutationObserver(h=>{for(const d of h)if(d.type==="childList")for(const w of d.addedNodes)w.tagName==="LINK"&&w.rel==="modulepreload"&&u(w)}).observe(document,{childList:!0,subtree:!0});function o(h){const d={};return h.integrity&&(d.integrity=h.integrity),h.referrerpolicy&&(d.referrerPolicy=h.referrerpolicy),h.crossorigin==="use-credentials"?d.credentials="include":h.crossorigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function u(h){if(h.ep)return;h.ep=!0;const d=o(h);fetch(h.href,d)}})();const gt="modulepreload",pt=function(e){return"/"+e},rr={},F=function(r,o,u){if(!o||o.length===0)return r();const h=document.getElementsByTagName("link");return Promise.all(o.map(d=>{if(d=pt(d),d in rr)return;rr[d]=!0;const w=d.endsWith(".css"),E=w?'[rel="stylesheet"]':"";if(!!u)for(let O=h.length-1;O>=0;O--){const _=h[O];if(_.href===d&&(!w||_.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${d}"]${E}`))return;const A=document.createElement("link");if(A.rel=w?"stylesheet":gt,w||(A.as="script",A.crossOrigin=""),A.href=d,document.head.appendChild(A),w)return new Promise((O,_)=>{A.addEventListener("load",O),A.addEventListener("error",()=>_(new Error(`Unable to preload CSS for ${d}`)))})})).then(()=>r())};function ge(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var ar={exports:{}};/*!
    localForage -- Offline Storage, Improved
    Version 1.10.0
    https://localforage.github.io/localForage
    (c) 2013-2017 Mozilla, Apache License 2.0
*/(function(e,r){(function(o){e.exports=o()})(function(){return function o(u,h,d){function w(A,O){if(!h[A]){if(!u[A]){var _=typeof ge=="function"&&ge;if(!O&&_)return _(A,!0);if(E)return E(A,!0);var S=new Error("Cannot find module '"+A+"'");throw S.code="MODULE_NOT_FOUND",S}var C=h[A]={exports:{}};u[A][0].call(C.exports,function(k){var W=u[A][1][k];return w(W||k)},C,C.exports,o,u,h,d)}return h[A].exports}for(var E=typeof ge=="function"&&ge,T=0;T<d.length;T++)w(d[T]);return w}({1:[function(o,u,h){(function(d){var w=d.MutationObserver||d.WebKitMutationObserver,E;if(w){var T=0,A=new w(k),O=d.document.createTextNode("");A.observe(O,{characterData:!0}),E=function(){O.data=T=++T%2}}else if(!d.setImmediate&&typeof d.MessageChannel<"u"){var _=new d.MessageChannel;_.port1.onmessage=k,E=function(){_.port2.postMessage(0)}}else"document"in d&&"onreadystatechange"in d.document.createElement("script")?E=function(){var B=d.document.createElement("script");B.onreadystatechange=function(){k(),B.onreadystatechange=null,B.parentNode.removeChild(B),B=null},d.document.documentElement.appendChild(B)}:E=function(){setTimeout(k,0)};var S,C=[];function k(){S=!0;for(var B,z,x=C.length;x;){for(z=C,C=[],B=-1;++B<x;)z[B]();x=C.length}S=!1}u.exports=W;function W(B){C.push(B)===1&&!S&&E()}}).call(this,typeof he<"u"?he:typeof self<"u"?self:typeof window<"u"?window:{})},{}],2:[function(o,u,h){var d=o(1);function w(){}var E={},T=["REJECTED"],A=["FULFILLED"],O=["PENDING"];u.exports=_;function _(v){if(typeof v!="function")throw new TypeError("resolver must be a function");this.state=O,this.queue=[],this.outcome=void 0,v!==w&&W(this,v)}_.prototype.catch=function(v){return this.then(null,v)},_.prototype.then=function(v,R){if(typeof v!="function"&&this.state===A||typeof R!="function"&&this.state===T)return this;var b=new this.constructor(w);if(this.state!==O){var D=this.state===A?v:R;C(b,D,this.outcome)}else this.queue.push(new S(b,v,R));return b};function S(v,R,b){this.promise=v,typeof R=="function"&&(this.onFulfilled=R,this.callFulfilled=this.otherCallFulfilled),typeof b=="function"&&(this.onRejected=b,this.callRejected=this.otherCallRejected)}S.prototype.callFulfilled=function(v){E.resolve(this.promise,v)},S.prototype.otherCallFulfilled=function(v){C(this.promise,this.onFulfilled,v)},S.prototype.callRejected=function(v){E.reject(this.promise,v)},S.prototype.otherCallRejected=function(v){C(this.promise,this.onRejected,v)};function C(v,R,b){d(function(){var D;try{D=R(b)}catch(M){return E.reject(v,M)}D===v?E.reject(v,new TypeError("Cannot resolve promise with itself")):E.resolve(v,D)})}E.resolve=function(v,R){var b=B(k,R);if(b.status==="error")return E.reject(v,b.value);var D=b.value;if(D)W(v,D);else{v.state=A,v.outcome=R;for(var M=-1,q=v.queue.length;++M<q;)v.queue[M].callFulfilled(R)}return v},E.reject=function(v,R){v.state=T,v.outcome=R;for(var b=-1,D=v.queue.length;++b<D;)v.queue[b].callRejected(R);return v};function k(v){var R=v&&v.then;if(v&&(typeof v=="object"||typeof v=="function")&&typeof R=="function")return function(){R.apply(v,arguments)}}function W(v,R){var b=!1;function D(H){b||(b=!0,E.reject(v,H))}function M(H){b||(b=!0,E.resolve(v,H))}function q(){R(M,D)}var Y=B(q);Y.status==="error"&&D(Y.value)}function B(v,R){var b={};try{b.value=v(R),b.status="success"}catch(D){b.status="error",b.value=D}return b}_.resolve=z;function z(v){return v instanceof this?v:E.resolve(new this(w),v)}_.reject=x;function x(v){var R=new this(w);return E.reject(R,v)}_.all=Ee;function Ee(v){var R=this;if(Object.prototype.toString.call(v)!=="[object Array]")return this.reject(new TypeError("must be an array"));var b=v.length,D=!1;if(!b)return this.resolve([]);for(var M=new Array(b),q=0,Y=-1,H=new this(w);++Y<b;)j(v[Y],Y);return H;function j(ae,le){R.resolve(ae).then(Ie,function(oe){D||(D=!0,E.reject(H,oe))});function Ie(oe){M[le]=oe,++q===b&&!D&&(D=!0,E.resolve(H,M))}}}_.race=ne;function ne(v){var R=this;if(Object.prototype.toString.call(v)!=="[object Array]")return this.reject(new TypeError("must be an array"));var b=v.length,D=!1;if(!b)return this.resolve([]);for(var M=-1,q=new this(w);++M<b;)Y(v[M]);return q;function Y(H){R.resolve(H).then(function(j){D||(D=!0,E.resolve(q,j))},function(j){D||(D=!0,E.reject(q,j))})}}},{1:1}],3:[function(o,u,h){(function(d){typeof d.Promise!="function"&&(d.Promise=o(2))}).call(this,typeof he<"u"?he:typeof self<"u"?self:typeof window<"u"?window:{})},{2:2}],4:[function(o,u,h){var d=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function w(t,s){if(!(t instanceof s))throw new TypeError("Cannot call a class as a function")}function E(){try{if(typeof indexedDB<"u")return indexedDB;if(typeof webkitIndexedDB<"u")return webkitIndexedDB;if(typeof mozIndexedDB<"u")return mozIndexedDB;if(typeof OIndexedDB<"u")return OIndexedDB;if(typeof msIndexedDB<"u")return msIndexedDB}catch{return}}var T=E();function A(){try{if(!T||!T.open)return!1;var t=typeof openDatabase<"u"&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),s=typeof fetch=="function"&&fetch.toString().indexOf("[native code")!==-1;return(!t||s)&&typeof indexedDB<"u"&&typeof IDBKeyRange<"u"}catch{return!1}}function O(t,s){t=t||[],s=s||{};try{return new Blob(t,s)}catch(a){if(a.name!=="TypeError")throw a;for(var n=typeof BlobBuilder<"u"?BlobBuilder:typeof MSBlobBuilder<"u"?MSBlobBuilder:typeof MozBlobBuilder<"u"?MozBlobBuilder:WebKitBlobBuilder,i=new n,c=0;c<t.length;c+=1)i.append(t[c]);return i.getBlob(s.type)}}typeof Promise>"u"&&o(3);var _=Promise;function S(t,s){s&&t.then(function(n){s(null,n)},function(n){s(n)})}function C(t,s,n){typeof s=="function"&&t.then(s),typeof n=="function"&&t.catch(n)}function k(t){return typeof t!="string"&&(console.warn(t+" used as a key, but it is not a string."),t=String(t)),t}function W(){if(arguments.length&&typeof arguments[arguments.length-1]=="function")return arguments[arguments.length-1]}var B="local-forage-detect-blob-support",z=void 0,x={},Ee=Object.prototype.toString,ne="readonly",v="readwrite";function R(t){for(var s=t.length,n=new ArrayBuffer(s),i=new Uint8Array(n),c=0;c<s;c++)i[c]=t.charCodeAt(c);return n}function b(t){return new _(function(s){var n=t.transaction(B,v),i=O([""]);n.objectStore(B).put(i,"key"),n.onabort=function(c){c.preventDefault(),c.stopPropagation(),s(!1)},n.oncomplete=function(){var c=navigator.userAgent.match(/Chrome\/(\d+)/),a=navigator.userAgent.match(/Edge\//);s(a||!c||parseInt(c[1],10)>=43)}}).catch(function(){return!1})}function D(t){return typeof z=="boolean"?_.resolve(z):b(t).then(function(s){return z=s,z})}function M(t){var s=x[t.name],n={};n.promise=new _(function(i,c){n.resolve=i,n.reject=c}),s.deferredOperations.push(n),s.dbReady?s.dbReady=s.dbReady.then(function(){return n.promise}):s.dbReady=n.promise}function q(t){var s=x[t.name],n=s.deferredOperations.pop();if(n)return n.resolve(),n.promise}function Y(t,s){var n=x[t.name],i=n.deferredOperations.pop();if(i)return i.reject(s),i.promise}function H(t,s){return new _(function(n,i){if(x[t.name]=x[t.name]||Ue(),t.db)if(s)M(t),t.db.close();else return n(t.db);var c=[t.name];s&&c.push(t.version);var a=T.open.apply(T,c);s&&(a.onupgradeneeded=function(l){var f=a.result;try{f.createObjectStore(t.storeName),l.oldVersion<=1&&f.createObjectStore(B)}catch(m){if(m.name==="ConstraintError")console.warn('The database "'+t.name+'" has been upgraded from version '+l.oldVersion+" to version "+l.newVersion+', but the storage "'+t.storeName+'" already exists.');else throw m}}),a.onerror=function(l){l.preventDefault(),i(a.error)},a.onsuccess=function(){var l=a.result;l.onversionchange=function(f){f.target.close()},n(l),q(t)}})}function j(t){return H(t,!1)}function ae(t){return H(t,!0)}function le(t,s){if(!t.db)return!0;var n=!t.db.objectStoreNames.contains(t.storeName),i=t.version<t.db.version,c=t.version>t.db.version;if(i&&(t.version!==s&&console.warn('The database "'+t.name+`" can't be downgraded from version `+t.db.version+" to version "+t.version+"."),t.version=t.db.version),c||n){if(n){var a=t.db.version+1;a>t.version&&(t.version=a)}return!0}return!1}function Ie(t){return new _(function(s,n){var i=new FileReader;i.onerror=n,i.onloadend=function(c){var a=btoa(c.target.result||"");s({__local_forage_encoded_blob:!0,data:a,type:t.type})},i.readAsBinaryString(t)})}function oe(t){var s=R(atob(t.data));return O([s],{type:t.type})}function Ne(t){return t&&t.__local_forage_encoded_blob}function dr(t){var s=this,n=s._initReady().then(function(){var i=x[s._dbInfo.name];if(i&&i.dbReady)return i.dbReady});return C(n,t,t),n}function hr(t){M(t);for(var s=x[t.name],n=s.forages,i=0;i<n.length;i++){var c=n[i];c._dbInfo.db&&(c._dbInfo.db.close(),c._dbInfo.db=null)}return t.db=null,j(t).then(function(a){return t.db=a,le(t)?ae(t):a}).then(function(a){t.db=s.db=a;for(var l=0;l<n.length;l++)n[l]._dbInfo.db=a}).catch(function(a){throw Y(t,a),a})}function J(t,s,n,i){i===void 0&&(i=1);try{var c=t.db.transaction(t.storeName,s);n(null,c)}catch(a){if(i>0&&(!t.db||a.name==="InvalidStateError"||a.name==="NotFoundError"))return _.resolve().then(function(){if(!t.db||a.name==="NotFoundError"&&!t.db.objectStoreNames.contains(t.storeName)&&t.version<=t.db.version)return t.db&&(t.version=t.db.version+1),ae(t)}).then(function(){return hr(t).then(function(){J(t,s,n,i-1)})}).catch(n);n(a)}}function Ue(){return{forages:[],db:null,dbReady:null,deferredOperations:[]}}function mr(t){var s=this,n={db:null};if(t)for(var i in t)n[i]=t[i];var c=x[n.name];c||(c=Ue(),x[n.name]=c),c.forages.push(s),s._initReady||(s._initReady=s.ready,s.ready=dr);var a=[];function l(){return _.resolve()}for(var f=0;f<c.forages.length;f++){var m=c.forages[f];m!==s&&a.push(m._initReady().catch(l))}var g=c.forages.slice(0);return _.all(a).then(function(){return n.db=c.db,j(n)}).then(function(p){return n.db=p,le(n,s._defaultConfig.version)?ae(n):p}).then(function(p){n.db=c.db=p,s._dbInfo=n;for(var y=0;y<g.length;y++){var I=g[y];I!==s&&(I._dbInfo.db=n.db,I._dbInfo.version=n.version)}})}function gr(t,s){var n=this;t=k(t);var i=new _(function(c,a){n.ready().then(function(){J(n._dbInfo,ne,function(l,f){if(l)return a(l);try{var m=f.objectStore(n._dbInfo.storeName),g=m.get(t);g.onsuccess=function(){var p=g.result;p===void 0&&(p=null),Ne(p)&&(p=oe(p)),c(p)},g.onerror=function(){a(g.error)}}catch(p){a(p)}})}).catch(a)});return S(i,s),i}function pr(t,s){var n=this,i=new _(function(c,a){n.ready().then(function(){J(n._dbInfo,ne,function(l,f){if(l)return a(l);try{var m=f.objectStore(n._dbInfo.storeName),g=m.openCursor(),p=1;g.onsuccess=function(){var y=g.result;if(y){var I=y.value;Ne(I)&&(I=oe(I));var P=t(I,y.key,p++);P!==void 0?c(P):y.continue()}else c()},g.onerror=function(){a(g.error)}}catch(y){a(y)}})}).catch(a)});return S(i,s),i}function _r(t,s,n){var i=this;t=k(t);var c=new _(function(a,l){var f;i.ready().then(function(){return f=i._dbInfo,Ee.call(s)==="[object Blob]"?D(f.db).then(function(m){return m?s:Ie(s)}):s}).then(function(m){J(i._dbInfo,v,function(g,p){if(g)return l(g);try{var y=p.objectStore(i._dbInfo.storeName);m===null&&(m=void 0);var I=y.put(m,t);p.oncomplete=function(){m===void 0&&(m=null),a(m)},p.onabort=p.onerror=function(){var P=I.error?I.error:I.transaction.error;l(P)}}catch(P){l(P)}})}).catch(l)});return S(c,n),c}function vr(t,s){var n=this;t=k(t);var i=new _(function(c,a){n.ready().then(function(){J(n._dbInfo,v,function(l,f){if(l)return a(l);try{var m=f.objectStore(n._dbInfo.storeName),g=m.delete(t);f.oncomplete=function(){c()},f.onerror=function(){a(g.error)},f.onabort=function(){var p=g.error?g.error:g.transaction.error;a(p)}}catch(p){a(p)}})}).catch(a)});return S(i,s),i}function yr(t){var s=this,n=new _(function(i,c){s.ready().then(function(){J(s._dbInfo,v,function(a,l){if(a)return c(a);try{var f=l.objectStore(s._dbInfo.storeName),m=f.clear();l.oncomplete=function(){i()},l.onabort=l.onerror=function(){var g=m.error?m.error:m.transaction.error;c(g)}}catch(g){c(g)}})}).catch(c)});return S(n,t),n}function wr(t){var s=this,n=new _(function(i,c){s.ready().then(function(){J(s._dbInfo,ne,function(a,l){if(a)return c(a);try{var f=l.objectStore(s._dbInfo.storeName),m=f.count();m.onsuccess=function(){i(m.result)},m.onerror=function(){c(m.error)}}catch(g){c(g)}})}).catch(c)});return S(n,t),n}function Sr(t,s){var n=this,i=new _(function(c,a){if(t<0){c(null);return}n.ready().then(function(){J(n._dbInfo,ne,function(l,f){if(l)return a(l);try{var m=f.objectStore(n._dbInfo.storeName),g=!1,p=m.openKeyCursor();p.onsuccess=function(){var y=p.result;if(!y){c(null);return}t===0||g?c(y.key):(g=!0,y.advance(t))},p.onerror=function(){a(p.error)}}catch(y){a(y)}})}).catch(a)});return S(i,s),i}function Er(t){var s=this,n=new _(function(i,c){s.ready().then(function(){J(s._dbInfo,ne,function(a,l){if(a)return c(a);try{var f=l.objectStore(s._dbInfo.storeName),m=f.openKeyCursor(),g=[];m.onsuccess=function(){var p=m.result;if(!p){i(g);return}g.push(p.key),p.continue()},m.onerror=function(){c(m.error)}}catch(p){c(p)}})}).catch(c)});return S(n,t),n}function Ir(t,s){s=W.apply(this,arguments);var n=this.config();t=typeof t!="function"&&t||{},t.name||(t.name=t.name||n.name,t.storeName=t.storeName||n.storeName);var i=this,c;if(!t.name)c=_.reject("Invalid arguments");else{var a=t.name===n.name&&i._dbInfo.db,l=a?_.resolve(i._dbInfo.db):j(t).then(function(f){var m=x[t.name],g=m.forages;m.db=f;for(var p=0;p<g.length;p++)g[p]._dbInfo.db=f;return f});t.storeName?c=l.then(function(f){if(!!f.objectStoreNames.contains(t.storeName)){var m=f.version+1;M(t);var g=x[t.name],p=g.forages;f.close();for(var y=0;y<p.length;y++){var I=p[y];I._dbInfo.db=null,I._dbInfo.version=m}var P=new _(function(L,$){var N=T.open(t.name,m);N.onerror=function(K){var ce=N.result;ce.close(),$(K)},N.onupgradeneeded=function(){var K=N.result;K.deleteObjectStore(t.storeName)},N.onsuccess=function(){var K=N.result;K.close(),L(K)}});return P.then(function(L){g.db=L;for(var $=0;$<p.length;$++){var N=p[$];N._dbInfo.db=L,q(N._dbInfo)}}).catch(function(L){throw(Y(t,L)||_.resolve()).catch(function(){}),L})}}):c=l.then(function(f){M(t);var m=x[t.name],g=m.forages;f.close();for(var p=0;p<g.length;p++){var y=g[p];y._dbInfo.db=null}var I=new _(function(P,L){var $=T.deleteDatabase(t.name);$.onerror=function(){var N=$.result;N&&N.close(),L($.error)},$.onblocked=function(){console.warn('dropInstance blocked for database "'+t.name+'" until all open connections are closed')},$.onsuccess=function(){var N=$.result;N&&N.close(),P(N)}});return I.then(function(P){m.db=P;for(var L=0;L<g.length;L++){var $=g[L];q($._dbInfo)}}).catch(function(P){throw(Y(t,P)||_.resolve()).catch(function(){}),P})})}return S(c,s),c}var Tr={_driver:"asyncStorage",_initStorage:mr,_support:A(),iterate:pr,getItem:gr,setItem:_r,removeItem:vr,clear:yr,length:wr,key:Sr,keys:Er,dropInstance:Ir};function Ar(){return typeof openDatabase=="function"}var Q="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",br="~~local_forage_type~",$e=/^~~local_forage_type~([^~]+)~/,fe="__lfsc__:",Te=fe.length,Ae="arbf",be="blob",Be="si08",xe="ui08",Me="uic8",Fe="si16",Ve="si32",qe="ur16",Ge="ui32",Ye="fl32",He="fl64",Ke=Te+Ae.length,We=Object.prototype.toString;function ze(t){var s=t.length*.75,n=t.length,i,c=0,a,l,f,m;t[t.length-1]==="="&&(s--,t[t.length-2]==="="&&s--);var g=new ArrayBuffer(s),p=new Uint8Array(g);for(i=0;i<n;i+=4)a=Q.indexOf(t[i]),l=Q.indexOf(t[i+1]),f=Q.indexOf(t[i+2]),m=Q.indexOf(t[i+3]),p[c++]=a<<2|l>>4,p[c++]=(l&15)<<4|f>>2,p[c++]=(f&3)<<6|m&63;return g}function Re(t){var s=new Uint8Array(t),n="",i;for(i=0;i<s.length;i+=3)n+=Q[s[i]>>2],n+=Q[(s[i]&3)<<4|s[i+1]>>4],n+=Q[(s[i+1]&15)<<2|s[i+2]>>6],n+=Q[s[i+2]&63];return s.length%3===2?n=n.substring(0,n.length-1)+"=":s.length%3===1&&(n=n.substring(0,n.length-2)+"=="),n}function Rr(t,s){var n="";if(t&&(n=We.call(t)),t&&(n==="[object ArrayBuffer]"||t.buffer&&We.call(t.buffer)==="[object ArrayBuffer]")){var i,c=fe;t instanceof ArrayBuffer?(i=t,c+=Ae):(i=t.buffer,n==="[object Int8Array]"?c+=Be:n==="[object Uint8Array]"?c+=xe:n==="[object Uint8ClampedArray]"?c+=Me:n==="[object Int16Array]"?c+=Fe:n==="[object Uint16Array]"?c+=qe:n==="[object Int32Array]"?c+=Ve:n==="[object Uint32Array]"?c+=Ge:n==="[object Float32Array]"?c+=Ye:n==="[object Float64Array]"?c+=He:s(new Error("Failed to get type for BinaryArray"))),s(c+Re(i))}else if(n==="[object Blob]"){var a=new FileReader;a.onload=function(){var l=br+t.type+"~"+Re(this.result);s(fe+be+l)},a.readAsArrayBuffer(t)}else try{s(JSON.stringify(t))}catch(l){console.error("Couldn't convert value into a JSON string: ",t),s(null,l)}}function Pr(t){if(t.substring(0,Te)!==fe)return JSON.parse(t);var s=t.substring(Ke),n=t.substring(Te,Ke),i;if(n===be&&$e.test(s)){var c=s.match($e);i=c[1],s=s.substring(c[0].length)}var a=ze(s);switch(n){case Ae:return a;case be:return O([a],{type:i});case Be:return new Int8Array(a);case xe:return new Uint8Array(a);case Me:return new Uint8ClampedArray(a);case Fe:return new Int16Array(a);case qe:return new Uint16Array(a);case Ve:return new Int32Array(a);case Ge:return new Uint32Array(a);case Ye:return new Float32Array(a);case He:return new Float64Array(a);default:throw new Error("Unkown type: "+n)}}var Pe={serialize:Rr,deserialize:Pr,stringToBuffer:ze,bufferToString:Re};function je(t,s,n,i){t.executeSql("CREATE TABLE IF NOT EXISTS "+s.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],n,i)}function Lr(t){var s=this,n={db:null};if(t)for(var i in t)n[i]=typeof t[i]!="string"?t[i].toString():t[i];var c=new _(function(a,l){try{n.db=openDatabase(n.name,String(n.version),n.description,n.size)}catch(f){return l(f)}n.db.transaction(function(f){je(f,n,function(){s._dbInfo=n,a()},function(m,g){l(g)})},l)});return n.serializer=Pe,c}function X(t,s,n,i,c,a){t.executeSql(n,i,c,function(l,f){f.code===f.SYNTAX_ERR?l.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name = ?",[s.storeName],function(m,g){g.rows.length?a(m,f):je(m,s,function(){m.executeSql(n,i,c,a)},a)},a):a(l,f)},a)}function Dr(t,s){var n=this;t=k(t);var i=new _(function(c,a){n.ready().then(function(){var l=n._dbInfo;l.db.transaction(function(f){X(f,l,"SELECT * FROM "+l.storeName+" WHERE key = ? LIMIT 1",[t],function(m,g){var p=g.rows.length?g.rows.item(0).value:null;p&&(p=l.serializer.deserialize(p)),c(p)},function(m,g){a(g)})})}).catch(a)});return S(i,s),i}function Cr(t,s){var n=this,i=new _(function(c,a){n.ready().then(function(){var l=n._dbInfo;l.db.transaction(function(f){X(f,l,"SELECT * FROM "+l.storeName,[],function(m,g){for(var p=g.rows,y=p.length,I=0;I<y;I++){var P=p.item(I),L=P.value;if(L&&(L=l.serializer.deserialize(L)),L=t(L,P.key,I+1),L!==void 0){c(L);return}}c()},function(m,g){a(g)})})}).catch(a)});return S(i,s),i}function Je(t,s,n,i){var c=this;t=k(t);var a=new _(function(l,f){c.ready().then(function(){s===void 0&&(s=null);var m=s,g=c._dbInfo;g.serializer.serialize(s,function(p,y){y?f(y):g.db.transaction(function(I){X(I,g,"INSERT OR REPLACE INTO "+g.storeName+" (key, value) VALUES (?, ?)",[t,p],function(){l(m)},function(P,L){f(L)})},function(I){if(I.code===I.QUOTA_ERR){if(i>0){l(Je.apply(c,[t,m,n,i-1]));return}f(I)}})})}).catch(f)});return S(a,n),a}function Or(t,s,n){return Je.apply(this,[t,s,n,1])}function kr(t,s){var n=this;t=k(t);var i=new _(function(c,a){n.ready().then(function(){var l=n._dbInfo;l.db.transaction(function(f){X(f,l,"DELETE FROM "+l.storeName+" WHERE key = ?",[t],function(){c()},function(m,g){a(g)})})}).catch(a)});return S(i,s),i}function Nr(t){var s=this,n=new _(function(i,c){s.ready().then(function(){var a=s._dbInfo;a.db.transaction(function(l){X(l,a,"DELETE FROM "+a.storeName,[],function(){i()},function(f,m){c(m)})})}).catch(c)});return S(n,t),n}function Ur(t){var s=this,n=new _(function(i,c){s.ready().then(function(){var a=s._dbInfo;a.db.transaction(function(l){X(l,a,"SELECT COUNT(key) as c FROM "+a.storeName,[],function(f,m){var g=m.rows.item(0).c;i(g)},function(f,m){c(m)})})}).catch(c)});return S(n,t),n}function $r(t,s){var n=this,i=new _(function(c,a){n.ready().then(function(){var l=n._dbInfo;l.db.transaction(function(f){X(f,l,"SELECT key FROM "+l.storeName+" WHERE id = ? LIMIT 1",[t+1],function(m,g){var p=g.rows.length?g.rows.item(0).key:null;c(p)},function(m,g){a(g)})})}).catch(a)});return S(i,s),i}function Br(t){var s=this,n=new _(function(i,c){s.ready().then(function(){var a=s._dbInfo;a.db.transaction(function(l){X(l,a,"SELECT key FROM "+a.storeName,[],function(f,m){for(var g=[],p=0;p<m.rows.length;p++)g.push(m.rows.item(p).key);i(g)},function(f,m){c(m)})})}).catch(c)});return S(n,t),n}function xr(t){return new _(function(s,n){t.transaction(function(i){i.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],function(c,a){for(var l=[],f=0;f<a.rows.length;f++)l.push(a.rows.item(f).name);s({db:t,storeNames:l})},function(c,a){n(a)})},function(i){n(i)})})}function Mr(t,s){s=W.apply(this,arguments);var n=this.config();t=typeof t!="function"&&t||{},t.name||(t.name=t.name||n.name,t.storeName=t.storeName||n.storeName);var i=this,c;return t.name?c=new _(function(a){var l;t.name===n.name?l=i._dbInfo.db:l=openDatabase(t.name,"","",0),t.storeName?a({db:l,storeNames:[t.storeName]}):a(xr(l))}).then(function(a){return new _(function(l,f){a.db.transaction(function(m){function g(P){return new _(function(L,$){m.executeSql("DROP TABLE IF EXISTS "+P,[],function(){L()},function(N,K){$(K)})})}for(var p=[],y=0,I=a.storeNames.length;y<I;y++)p.push(g(a.storeNames[y]));_.all(p).then(function(){l()}).catch(function(P){f(P)})},function(m){f(m)})})}):c=_.reject("Invalid arguments"),S(c,s),c}var Fr={_driver:"webSQLStorage",_initStorage:Lr,_support:Ar(),iterate:Cr,getItem:Dr,setItem:Or,removeItem:kr,clear:Nr,length:Ur,key:$r,keys:Br,dropInstance:Mr};function Vr(){try{return typeof localStorage<"u"&&"setItem"in localStorage&&!!localStorage.setItem}catch{return!1}}function Qe(t,s){var n=t.name+"/";return t.storeName!==s.storeName&&(n+=t.storeName+"/"),n}function qr(){var t="_localforage_support_test";try{return localStorage.setItem(t,!0),localStorage.removeItem(t),!1}catch{return!0}}function Gr(){return!qr()||localStorage.length>0}function Yr(t){var s=this,n={};if(t)for(var i in t)n[i]=t[i];return n.keyPrefix=Qe(t,s._defaultConfig),Gr()?(s._dbInfo=n,n.serializer=Pe,_.resolve()):_.reject()}function Hr(t){var s=this,n=s.ready().then(function(){for(var i=s._dbInfo.keyPrefix,c=localStorage.length-1;c>=0;c--){var a=localStorage.key(c);a.indexOf(i)===0&&localStorage.removeItem(a)}});return S(n,t),n}function Kr(t,s){var n=this;t=k(t);var i=n.ready().then(function(){var c=n._dbInfo,a=localStorage.getItem(c.keyPrefix+t);return a&&(a=c.serializer.deserialize(a)),a});return S(i,s),i}function Wr(t,s){var n=this,i=n.ready().then(function(){for(var c=n._dbInfo,a=c.keyPrefix,l=a.length,f=localStorage.length,m=1,g=0;g<f;g++){var p=localStorage.key(g);if(p.indexOf(a)===0){var y=localStorage.getItem(p);if(y&&(y=c.serializer.deserialize(y)),y=t(y,p.substring(l),m++),y!==void 0)return y}}});return S(i,s),i}function zr(t,s){var n=this,i=n.ready().then(function(){var c=n._dbInfo,a;try{a=localStorage.key(t)}catch{a=null}return a&&(a=a.substring(c.keyPrefix.length)),a});return S(i,s),i}function jr(t){var s=this,n=s.ready().then(function(){for(var i=s._dbInfo,c=localStorage.length,a=[],l=0;l<c;l++){var f=localStorage.key(l);f.indexOf(i.keyPrefix)===0&&a.push(f.substring(i.keyPrefix.length))}return a});return S(n,t),n}function Jr(t){var s=this,n=s.keys().then(function(i){return i.length});return S(n,t),n}function Qr(t,s){var n=this;t=k(t);var i=n.ready().then(function(){var c=n._dbInfo;localStorage.removeItem(c.keyPrefix+t)});return S(i,s),i}function Xr(t,s,n){var i=this;t=k(t);var c=i.ready().then(function(){s===void 0&&(s=null);var a=s;return new _(function(l,f){var m=i._dbInfo;m.serializer.serialize(s,function(g,p){if(p)f(p);else try{localStorage.setItem(m.keyPrefix+t,g),l(a)}catch(y){(y.name==="QuotaExceededError"||y.name==="NS_ERROR_DOM_QUOTA_REACHED")&&f(y),f(y)}})})});return S(c,n),c}function Zr(t,s){if(s=W.apply(this,arguments),t=typeof t!="function"&&t||{},!t.name){var n=this.config();t.name=t.name||n.name,t.storeName=t.storeName||n.storeName}var i=this,c;return t.name?c=new _(function(a){t.storeName?a(Qe(t,i._defaultConfig)):a(t.name+"/")}).then(function(a){for(var l=localStorage.length-1;l>=0;l--){var f=localStorage.key(l);f.indexOf(a)===0&&localStorage.removeItem(f)}}):c=_.reject("Invalid arguments"),S(c,s),c}var et={_driver:"localStorageWrapper",_initStorage:Yr,_support:Vr(),iterate:Wr,getItem:Kr,setItem:Xr,removeItem:Qr,clear:Hr,length:Jr,key:zr,keys:jr,dropInstance:Zr},rt=function(s,n){return s===n||typeof s=="number"&&typeof n=="number"&&isNaN(s)&&isNaN(n)},tt=function(s,n){for(var i=s.length,c=0;c<i;){if(rt(s[c],n))return!0;c++}return!1},Xe=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"},ie={},Ze={},se={INDEXEDDB:Tr,WEBSQL:Fr,LOCALSTORAGE:et},nt=[se.INDEXEDDB._driver,se.WEBSQL._driver,se.LOCALSTORAGE._driver],de=["dropInstance"],Le=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat(de),ot={description:"",driver:nt.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1};function st(t,s){t[s]=function(){var n=arguments;return t.ready().then(function(){return t[s].apply(t,n)})}}function De(){for(var t=1;t<arguments.length;t++){var s=arguments[t];if(s)for(var n in s)s.hasOwnProperty(n)&&(Xe(s[n])?arguments[0][n]=s[n].slice():arguments[0][n]=s[n])}return arguments[0]}var at=function(){function t(s){w(this,t);for(var n in se)if(se.hasOwnProperty(n)){var i=se[n],c=i._driver;this[n]=c,ie[c]||this.defineDriver(i)}this._defaultConfig=De({},ot),this._config=De({},this._defaultConfig,s),this._driverSet=null,this._initDriver=null,this._ready=!1,this._dbInfo=null,this._wrapLibraryMethodsWithReady(),this.setDriver(this._config.driver).catch(function(){})}return t.prototype.config=function(n){if((typeof n>"u"?"undefined":d(n))==="object"){if(this._ready)return new Error("Can't call config() after localforage has been used.");for(var i in n){if(i==="storeName"&&(n[i]=n[i].replace(/\W/g,"_")),i==="version"&&typeof n[i]!="number")return new Error("Database version must be a number.");this._config[i]=n[i]}return"driver"in n&&n.driver?this.setDriver(this._config.driver):!0}else return typeof n=="string"?this._config[n]:this._config},t.prototype.defineDriver=function(n,i,c){var a=new _(function(l,f){try{var m=n._driver,g=new Error("Custom driver not compliant; see https://mozilla.github.io/localForage/#definedriver");if(!n._driver){f(g);return}for(var p=Le.concat("_initStorage"),y=0,I=p.length;y<I;y++){var P=p[y],L=!tt(de,P);if((L||n[P])&&typeof n[P]!="function"){f(g);return}}var $=function(){for(var ce=function(ut){return function(){var lt=new Error("Method "+ut+" is not implemented by the current driver"),er=_.reject(lt);return S(er,arguments[arguments.length-1]),er}},Ce=0,ct=de.length;Ce<ct;Ce++){var Oe=de[Ce];n[Oe]||(n[Oe]=ce(Oe))}};$();var N=function(ce){ie[m]&&console.info("Redefining LocalForage driver: "+m),ie[m]=n,Ze[m]=ce,l()};"_support"in n?n._support&&typeof n._support=="function"?n._support().then(N,f):N(!!n._support):N(!0)}catch(K){f(K)}});return C(a,i,c),a},t.prototype.driver=function(){return this._driver||null},t.prototype.getDriver=function(n,i,c){var a=ie[n]?_.resolve(ie[n]):_.reject(new Error("Driver not found."));return C(a,i,c),a},t.prototype.getSerializer=function(n){var i=_.resolve(Pe);return C(i,n),i},t.prototype.ready=function(n){var i=this,c=i._driverSet.then(function(){return i._ready===null&&(i._ready=i._initDriver()),i._ready});return C(c,n,n),c},t.prototype.setDriver=function(n,i,c){var a=this;Xe(n)||(n=[n]);var l=this._getSupportedDrivers(n);function f(){a._config.driver=a.driver()}function m(y){return a._extend(y),f(),a._ready=a._initStorage(a._config),a._ready}function g(y){return function(){var I=0;function P(){for(;I<y.length;){var L=y[I];return I++,a._dbInfo=null,a._ready=null,a.getDriver(L).then(m).catch(P)}f();var $=new Error("No available storage method found.");return a._driverSet=_.reject($),a._driverSet}return P()}}var p=this._driverSet!==null?this._driverSet.catch(function(){return _.resolve()}):_.resolve();return this._driverSet=p.then(function(){var y=l[0];return a._dbInfo=null,a._ready=null,a.getDriver(y).then(function(I){a._driver=I._driver,f(),a._wrapLibraryMethodsWithReady(),a._initDriver=g(l)})}).catch(function(){f();var y=new Error("No available storage method found.");return a._driverSet=_.reject(y),a._driverSet}),C(this._driverSet,i,c),this._driverSet},t.prototype.supports=function(n){return!!Ze[n]},t.prototype._extend=function(n){De(this,n)},t.prototype._getSupportedDrivers=function(n){for(var i=[],c=0,a=n.length;c<a;c++){var l=n[c];this.supports(l)&&i.push(l)}return i},t.prototype._wrapLibraryMethodsWithReady=function(){for(var n=0,i=Le.length;n<i;n++)st(this,Le[n])},t.prototype.createInstance=function(n){return new t(n)},t}(),it=new at;u.exports=it},{3:3}]},{},[4])(4)})})(ar);const ir=ar.exports;function Z(){try{const e="__sessionStorage_test__";return sessionStorage.setItem(e,e),sessionStorage.removeItem(e),!0}catch(e){return console.warn("sessionStorage\u4E0D\u53EF\u7528:",e.message),!1}}function ee(){try{const e="__localStorage_test__";return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return console.warn("localStorage\u4E0D\u53EF\u7528:",e.message),!1}}const _t={getItem(e){try{return Z()?sessionStorage.getItem(e):localStorage.getItem(`session_${e}`)}catch(r){return console.warn(`\u83B7\u53D6sessionStorage[${e}]\u5931\u8D25:`,r.message),null}},setItem(e,r){try{return Z()?(sessionStorage.setItem(e,r),!0):(localStorage.setItem(`session_${e}`,r),!0)}catch(o){return console.warn(`\u8BBE\u7F6EsessionStorage[${e}]\u5931\u8D25:`,o.message),!1}},removeItem(e){try{return Z()&&sessionStorage.removeItem(e),localStorage.removeItem(`session_${e}`),!0}catch(r){return console.warn(`\u5220\u9664sessionStorage[${e}]\u5931\u8D25:`,r.message),!1}},clear(){try{return Z()&&sessionStorage.clear(),Object.keys(localStorage).forEach(e=>{e.startsWith("session_")&&localStorage.removeItem(e)}),!0}catch(e){return console.warn("\u6E05\u7406sessionStorage\u5931\u8D25:",e.message),!1}}},vt={getItem(e){try{return ee()?localStorage.getItem(e):null}catch(r){return console.warn(`\u83B7\u53D6localStorage[${e}]\u5931\u8D25:`,r.message),null}},setItem(e,r){try{return ee()?(localStorage.setItem(e,r),!0):!1}catch(o){return console.warn(`\u8BBE\u7F6ElocalStorage[${e}]\u5931\u8D25:`,o.message),!1}},removeItem(e){try{return ee()?(localStorage.removeItem(e),!0):!1}catch(r){return console.warn(`\u5220\u9664localStorage[${e}]\u5931\u8D25:`,r.message),!1}},clear(){try{return ee()?(localStorage.clear(),!0):!1}catch(e){return console.warn("\u6E05\u7406localStorage\u5931\u8D25:",e.message),!1}}};class yt{constructor(){this.data=new Map}getItem(r){return this.data.get(r)||null}setItem(r,o){this.data.set(r,o)}removeItem(r){this.data.delete(r)}clear(){this.data.clear()}get length(){return this.data.size}key(r){return Array.from(this.data.keys())[r]||null}}const cr=new yt;function wt(e=!1){return e&&Z()?sessionStorage:ee()?localStorage:(console.warn("\u6D4F\u89C8\u5668\u5B58\u50A8\u4E0D\u53EF\u7528\uFF0C\u4F7F\u7528\u5185\u5B58\u5B58\u50A8"),cr)}function ue(){try{return window.self!==window.top}catch{return!0}}function Se(){try{if(!ue())return!1;const e=window.parent.location.origin,r=window.location.origin;return e!==r}catch{return!0}}function St(){return{isIframe:ue(),isCrossOrigin:Se(),sessionStorageAvailable:Z(),localStorageAvailable:ee(),hostname:window.location.hostname,origin:window.location.origin,userAgent:navigator.userAgent}}function Et(e){const r={message:e.message,isQuotaExceeded:!1,isSecurityError:!1,isCrossOrigin:!1,isInIframe:ue(),isCrossOriginIframe:Se()};return(e.name==="QuotaExceededError"||e.code===22)&&(r.isQuotaExceeded=!0),(e.name==="SecurityError"||e.message.includes("Access is denied"))&&(r.isSecurityError=!0),(e.message.includes("cross-origin")||e.message.includes("Access is denied"))&&(r.isCrossOrigin=!0),r}const It=Object.freeze(Object.defineProperty({__proto__:null,isSessionStorageAvailable:Z,isLocalStorageAvailable:ee,safeSessionStorage:_t,safeLocalStorage:vt,memoryStorage:cr,getStorageAdapter:wt,isInIframe:ue,isCrossOriginIframe:Se,getEnvironmentInfo:St,getStorageErrorInfo:Et},Symbol.toStringTag,{value:"Module"}));function ke(e,r,o,u,h,d,w,E){var T=typeof e=="function"?e.options:e;r&&(T.render=r,T.staticRenderFns=o,T._compiled=!0),u&&(T.functional=!0),d&&(T._scopeId="data-v-"+d);var A;if(w?(A=function(S){S=S||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,!S&&typeof __VUE_SSR_CONTEXT__<"u"&&(S=__VUE_SSR_CONTEXT__),h&&h.call(this,S),S&&S._registeredComponents&&S._registeredComponents.add(w)},T._ssrRegister=A):h&&(A=E?function(){h.call(this,(T.functional?this.parent:this).$root.$options.shadowRoot)}:h),A)if(T.functional){T._injectStyles=A;var O=T.render;T.render=function(C,k){return A.call(k),O(C,k)}}else{var _=T.beforeCreate;T.beforeCreate=_?[].concat(_,A):[A]}return{exports:e,options:T}}const Tt={name:"StorageCompatibility",data(){return{showWarning:!1,warningTitle:"",warningDescription:"",hasChecked:!1}},mounted(){this.checkStorageCompatibility()},methods:{checkStorageCompatibility(){if(this.hasChecked)return;this.hasChecked=!0;const e=Z(),r=ee(),o=ue(),u=Se();console.log("\u5B58\u50A8\u517C\u5BB9\u6027\u68C0\u67E5:",{sessionStorage:e,localStorage:r,inIframe:o,crossOrigin:u}),u&&!e?(this.showWarning=!0,this.warningTitle="\u5B58\u50A8\u529F\u80FD\u53D7\u9650",this.warningDescription="\u5F53\u524D\u5728\u8DE8\u57DF\u73AF\u5883\u4E2D\u8FD0\u884C\uFF0C\u90E8\u5206\u529F\u80FD\u53EF\u80FD\u53D7\u5230\u9650\u5236\u3002\u7CFB\u7EDF\u5C06\u81EA\u52A8\u4F7F\u7528\u66FF\u4EE3\u65B9\u6848\u786E\u4FDD\u6B63\u5E38\u8FD0\u884C\u3002"):r?!e&&r&&console.warn("sessionStorage\u4E0D\u53EF\u7528\uFF0C\u5C06\u4F7F\u7528localStorage\u4F5C\u4E3A\u66FF\u4EE3"):(this.showWarning=!0,this.warningTitle="\u5B58\u50A8\u529F\u80FD\u4E0D\u53EF\u7528",this.warningDescription="\u6D4F\u89C8\u5668\u5B58\u50A8\u529F\u80FD\u88AB\u7981\u7528\uFF0C\u53EF\u80FD\u5F71\u54CD\u7528\u6237\u4F53\u9A8C\u3002\u8BF7\u68C0\u67E5\u6D4F\u89C8\u5668\u8BBE\u7F6E\u6216\u5C1D\u8BD5\u5237\u65B0\u9875\u9762\u3002")},dismissWarning(){this.showWarning=!1;try{localStorage.setItem("storage_warning_dismissed",Date.now().toString())}catch(e){console.warn("\u65E0\u6CD5\u8BB0\u5F55\u8B66\u544A\u72B6\u6001:",e.message)}}}};var At=function(){var r=this,o=r._self._c;return r.showWarning?o("div",{staticClass:"storage-warning"},[o("el-alert",{attrs:{title:r.warningTitle,description:r.warningDescription,type:"warning",closable:!0,"show-icon":""},on:{close:r.dismissWarning}},[o("template",{slot:"title"},[o("i",{staticClass:"el-icon-warning"}),r._v(" "+r._s(r.warningTitle)+" ")])],2)],1):r._e()},bt=[],Rt=ke(Tt,At,bt,!1,null,"80ce8118",null,null);const Pt=Rt.exports,Lt={components:{StorageCompatibility:Pt},name:"App",async mounted(){ir.config({name:"assembly",storeName:"assemblyHistory",description:"\u7528\u4E8E\u5B58\u50A8\u6C47\u7F16\u641C\u7D22\u7684\u5386\u53F2\u8BB0\u5F55"}),this.setupIframeMessageListener(),this.setupCustomEventListeners(),await this.handleServiceAToken(),await this.$store.dispatch("user/initAuth"),this.setupTokenValidityCheck()},methods:{async handleServiceAToken(){try{const e=new URLSearchParams(window.location.search),r=e.get("token"),o=e.get("redirect")||"/",u=e.get("session_id");if(r){if(console.log("\u68C0\u6D4B\u5230URL\u4E2D\u7684Token\uFF0C\u5F00\u59CB\u5904\u7406\u670D\u52A1A\u8BA4\u8BC1..."),u){const d=localStorage.getItem("lastSessionId");d&&d!==u&&(console.log("\u68C0\u6D4B\u5230\u65B0\u4F1A\u8BDD\uFF0C\u6E05\u7406\u65E7\u4F1A\u8BDD\u6570\u636E"),await this.clearLocalSession()),localStorage.setItem("lastSessionId",u)}const h=await fetch("/api-s/api/simple-auth/verify-token",{method:"GET",headers:{Authorization:`Bearer ${r}`}});if(h.ok){const d=await h.json();console.log("Token\u9A8C\u8BC1\u6210\u529F:",d),localStorage.setItem("authToken",r),localStorage.setItem("loginSource","service_a"),localStorage.setItem("currentUser",JSON.stringify(d.user_info)),this.$store.commit("user/SET_TOKEN",r),this.$store.commit("user/SET_USER_INFO",d.user_info),this.$store.commit("user/SET_LOGIN_STATUS",!0),console.log("\u6B22\u8FCE\u4F7F\u7528\u7CFB\u7EDF\uFF01");const w=window.location.origin+window.location.pathname+o;if(window.history.replaceState({},"",w),o!=="/"&&o!==window.location.pathname&&this.$router.push(o),console.log("\u670D\u52A1A\u7528\u6237\u8BA4\u8BC1\u5B8C\u6210\uFF0C\u7528\u6237:",d.user_info.username),u){let E="http://***********:8081";typeof window<"u"&&window.BUILD_CONFIG&&(E=window.BUILD_CONFIG.SERVICE_A_URL),window.parent.postMessage({type:"SERVICE_B_READY",sessionId:u,timestamp:Date.now()},E)}}else{console.error("Token\u9A8C\u8BC1\u5931\u8D25:",h.status,h.statusText);const d=window.location.origin+window.location.pathname;window.history.replaceState({},"",d)}}}catch(e){console.error("\u5904\u7406\u670D\u52A1A Token\u65F6\u51FA\u9519:",e);const r=window.location.origin+window.location.pathname;window.history.replaceState({},"",r)}},setupIframeMessageListener(){window.addEventListener("message",e=>{var r,o,u;if(!(e.data&&(e.data.type==="iframe-pos"||e.data.type==="get-iframe-pos"))){if(e.data&&e.data.source==="pdf-viewer"){console.log("\u{1F4E8} App: PDF\u67E5\u770B\u5668\u6D88\u606F\u5DF2\u7531iframe-security.js\u5904\u7406\uFF0C\u8DF3\u8FC7");return}if(console.log("\u{1F4E8} App: \u6536\u5230\u6D88\u606F\u8BE6\u60C5:"),console.log("  - \u6570\u636E:",e.data),console.log("  - \u6765\u6E90:",e.origin),console.log("  - \u7C7B\u578B\u68C0\u67E5:",{hasData:!!e.data,hasAction:(r=e.data)==null?void 0:r.action,hasType:(o=e.data)==null?void 0:o.type,hasSource:(u=e.data)==null?void 0:u.source}),e.data&&e.data.type==="IFRAME_GO_BACK"&&e.data.source==="preview-page"){console.log("\u2705 App: \u5339\u914D\u9884\u89C8\u9875\u9762\u8FD4\u56DE\u6D88\u606F\u683C\u5F0F!"),console.log("\u{1F4E8} App: \u6536\u5230iframe\u9884\u89C8\u9875\u9762\u8FD4\u56DE\u6D88\u606F:",e.data),this.handleIframeGoBack(e.data);return}switch(console.log("\u{1F513} App: \u5141\u8BB8\u6765\u81EA\u4EFB\u610F\u6E90\u7684\u6D88\u606F:",e.origin),e.data.type){case"CLEAR_SESSION":this.handleClearSession(e.data);break}}}),window.addEventListener("storage",e=>{e.key==="authToken"&&e.newValue===null&&(console.log("\u68C0\u6D4B\u5230Token\u88AB\u6E05\u9664\uFF0C\u6E05\u7406\u5F53\u524D\u4F1A\u8BDD"),this.clearLocalSession())})},getAllowedOrigins(){const e=[];if(typeof window<"u"&&window.BUILD_CONFIG)e.push(window.BUILD_CONFIG.SERVICE_A_URL),e.push(window.location.origin);else{const r=window.location.hostname,o=window.location.protocol;e.push(window.location.origin,`${o}//${r}:8081`,`${o}//${r}:8080`,`${o}//${r}:2222`,`${o}//${r}:18888`,`${o}//${r}:3000`,`${o}//${r}`,"http://***********:8081","http://localhost:8081")}return[...new Set(e)]},setupCustomEventListeners(){window.addEventListener("pdf-viewer-go-back",e=>{console.log("\u{1F4E8} App: \u6536\u5230PDF\u67E5\u770B\u5668\u8FD4\u56DE\u4E8B\u4EF6:",e.detail),this.handlePdfViewerGoBack(e.detail)}),window.addEventListener("preview-page-go-back",e=>{console.log("\u{1F4E8} App: \u6536\u5230\u9884\u89C8\u9875\u9762\u8FD4\u56DE\u4E8B\u4EF6:",e.detail),this.handlePreviewPageGoBack(e.detail)}),window.addEventListener("iframe-route-change",e=>{console.log("\u{1F4E8} App: \u6536\u5230\u8DEF\u7531\u53D8\u66F4\u4E8B\u4EF6:",e.detail),this.handleIframeRouteChange(e.detail)})},handlePdfViewerGoBack(e){if(console.log("\u{1F4E8} App: \u5904\u7406PDF\u67E5\u770B\u5668\u8FD4\u56DE\u8BF7\u6C42:",e),e&&e.source==="app-message-handler"){console.log("\u26A0\uFE0F App: \u68C0\u6D4B\u5230\u5FAA\u73AF\u8C03\u7528\uFF0C\u8DF3\u8FC7\u5904\u7406");return}this.$route&&this.$route.path==="/preview"?(console.log("\u{1F4E8} App: \u5F53\u524D\u5728\u9884\u89C8\u9875\u9762\uFF0C\u76F4\u63A5\u6267\u884C\u8FD4\u56DE"),this.executeGoBack()):(console.log("\u{1F504} App: \u4E0D\u5728\u9884\u89C8\u9875\u9762\uFF0C\u8DF3\u8F6C\u5230\u641C\u7D22\u9875\u9762"),this.$router.push("/search").catch(r=>{console.warn("\u8DEF\u7531\u8DF3\u8F6C\u8B66\u544A:",r)}))},handleIframeGoBack(e){console.log("\u{1F4E8} App: \u5904\u7406iframe\u9884\u89C8\u9875\u9762\u8FD4\u56DE\u8BF7\u6C42:",e),localStorage.removeItem("url"),localStorage.removeItem("docId"),localStorage.removeItem("permissionChecked"),localStorage.removeItem("permissionResult"),console.log("\u{1F9F9} App: \u5DF2\u6E05\u7406\u9884\u89C8\u76F8\u5173\u6570\u636E\uFF0C\u4FDD\u7559\u641C\u7D22\u53C2\u6570");const r=e.targetRoute||"/search";console.log("\u{1F504} App: \u8DF3\u8F6C\u5230:",r);try{this.$router.push(r)}catch(o){console.error("\u274C App: \u8DF3\u8F6C\u5931\u8D25\uFF0C\u964D\u7EA7\u5230\u641C\u7D22\u9875\u9762",o),this.$router.push("/search")}},handlePreviewPageGoBack(e){console.log("\u{1F4E8} App: \u5904\u7406\u9884\u89C8\u9875\u9762\u8FD4\u56DE\u8BF7\u6C42\uFF08\u81EA\u5B9A\u4E49\u4E8B\u4EF6\uFF09:",e);const r=e.targetRoute||"/search";console.log("\u{1F504} App: \u8DF3\u8F6C\u5230\u76EE\u6807\u8DEF\u7531:",r);try{this.$router.push(r)}catch(o){console.error("\u274C App: \u8DF3\u8F6C\u5931\u8D25\uFF0C\u964D\u7EA7\u5230\u641C\u7D22\u9875\u9762",o),this.$router.push("/search")}console.log("\u{1F4E8} App: \u9884\u89C8\u9875\u9762\u8FD4\u56DE\u5904\u7406\u5B8C\u6210")},handleIframeRouteChange(e){console.log("\u{1F4E8} App: \u5904\u7406iframe\u8DEF\u7531\u53D8\u66F4\u8BF7\u6C42\uFF08\u81EA\u5B9A\u4E49\u4E8B\u4EF6\uFF09:",e);const r=e.route||"/search";console.log("\u{1F504} App: \u8DF3\u8F6C\u5230\u76EE\u6807\u8DEF\u7531:",r);try{this.$router.push(r)}catch(o){console.error("\u274C App: \u8DF3\u8F6C\u5931\u8D25\uFF0C\u964D\u7EA7\u5230\u641C\u7D22\u9875\u9762",o),this.$router.push("/search")}console.log("\u{1F4E8} App: iframe\u8DEF\u7531\u53D8\u66F4\u5904\u7406\u5B8C\u6210")},executeGoBack(){console.log("\u{1F519} App: \u6267\u884C\u8FD4\u56DE\u64CD\u4F5C"),localStorage.removeItem("url"),localStorage.removeItem("docId"),localStorage.removeItem("permissionChecked"),localStorage.removeItem("permissionResult"),console.log("\u{1F9F9} App: \u5DF2\u6E05\u7406\u9884\u89C8\u76F8\u5173\u6570\u636E\uFF0C\u4FDD\u7559\u641C\u7D22\u53C2\u6570");const e=localStorage.getItem("queryParams");if(e)try{const r=JSON.parse(e);if(r.keyword&&r.type){console.log("\u{1F4DA} App: \u68C0\u6D4B\u5230\u641C\u7D22\u53C2\u6570\uFF0C\u76F4\u63A5\u8DF3\u8F6C\u5230\u641C\u7D22\u9875\u9762\u4EE5\u6062\u590D\u641C\u7D22\u7ED3\u679C"),this.$router.push("/search");return}}catch(r){console.warn("\u89E3\u6790\u641C\u7D22\u53C2\u6570\u5931\u8D25:",r)}try{window.history.length>1?(console.log("\u{1F4DA} App: \u4F7F\u7528\u6D4F\u89C8\u5668\u5386\u53F2\u8FD4\u56DE"),this.$router.go(-1)):(console.log("\u{1F4DA} App: \u65E0\u5386\u53F2\u8BB0\u5F55\uFF0C\u8DF3\u8F6C\u5230\u641C\u7D22\u9875\u9762"),this.$router.push("/search"))}catch(r){console.error("\u274C App: \u8FD4\u56DE\u5931\u8D25\uFF0C\u964D\u7EA7\u5230\u641C\u7D22\u9875\u9762",r),this.$router.push("/search")}},async handleClearSession(e){console.log("\u6536\u5230\u4F1A\u8BDD\u6E05\u7406\u8BF7\u6C42:",e.reason);try{await this.clearLocalSession();let r="http://***********:8081";typeof window<"u"&&window.BUILD_CONFIG&&(r=window.BUILD_CONFIG.SERVICE_A_URL),window.parent.postMessage({type:"SESSION_CLEARED",timestamp:Date.now()},r)}catch(r){console.error("\u6E05\u7406\u4F1A\u8BDD\u5931\u8D25:",r)}},async clearLocalSession(){console.log("\u6E05\u7406\u672C\u5730\u4F1A\u8BDD\u6570\u636E");try{const{safeLocalStorage:e,safeSessionStorage:r}=await F(()=>Promise.resolve().then(()=>It),void 0);["authToken","loginSource","currentUser","lastSessionId","queryParams","email","userName","access_token","refresh_token","user_info"].forEach(u=>{e.removeItem(u)}),r.clear(),this.$store.commit("user/CLEAR_AUTH_DATA"),this.$store.commit("SET_EMAIL",""),this.$route.path!=="/"&&this.$router.push("/"),console.log("\u672C\u5730\u4F1A\u8BDD\u5DF2\u6E05\u7406\uFF0C\u5305\u62EC\u641C\u7D22\u53C2\u6570\u548C\u7528\u6237\u6570\u636E")}catch(e){console.error("\u6E05\u7406\u4F1A\u8BDD\u65F6\u51FA\u9519:",e);try{["authToken","loginSource","currentUser","lastSessionId","queryParams","email","userName","access_token","refresh_token","user_info"].forEach(o=>{try{localStorage.removeItem(o)}catch(u){console.warn(`\u6E05\u7406localStorage[${o}]\u5931\u8D25:`,u.message)}});try{sessionStorage.clear()}catch(o){console.warn("\u6E05\u7406sessionStorage\u5931\u8D25:",o.message)}this.$store.commit("user/CLEAR_AUTH_DATA"),this.$store.commit("SET_EMAIL",""),this.$route.path!=="/"&&this.$router.push("/"),console.log("\u4F7F\u7528\u964D\u7EA7\u65B9\u6848\u6E05\u7406\u4F1A\u8BDD\u5B8C\u6210")}catch(r){console.error("\u964D\u7EA7\u6E05\u7406\u4E5F\u5931\u8D25\u4E86:",r)}}},setupTokenValidityCheck(){setInterval(async()=>{await this.checkTokenValidity()},5*60*1e3),window.addEventListener("focus",async()=>{await this.checkTokenValidity()})},async checkTokenValidity(){const e=localStorage.getItem("authToken"),r=localStorage.getItem("loginSource");if(!(!e||r!=="service_a"))try{(await fetch("/api-s/api/simple-auth/verify-token",{method:"GET",headers:{Authorization:`Bearer ${e}`}})).ok||(console.log("Token\u5DF2\u5931\u6548\uFF0C\u6E05\u7406\u4F1A\u8BDD"),await this.clearLocalSession())}catch(o){console.error("Token\u9A8C\u8BC1\u5931\u8D25:",o),await this.clearLocalSession()}}}};var Dt=function(){var r=this,o=r._self._c;return o("div",{attrs:{id:"app"}},[o("router-view"),o("StorageCompatibility")],1)},Ct=[],Ot=ke(Lt,Dt,Ct,!1,null,null,null,null);const tr=Ot.exports,kt={VITE_BACKEND_URL:"http://*************:18888",VITE_VUE_APP_BASE_API:"/dev-api",VITE_VUE_APP_OTHER_API:"/api-s",VITE_VUE_APP_OTHER_PATH_API:"http://*************:18888/",VITE_FILE_BASE_URL:"/download?url=",VITE_ENV_NAME:"\u751F\u4EA7\u73AF\u5883",VITE_ENV_MODE:"production",VITE_DEBUG_MODE:"false",VITE_IFRAME_ALLOWED_ORIGINS:"https://*************,https://*************,http://localhost:8080,http://***********:8080",VITE_CSP_FRAME_ANCESTORS:"'self' http://localhost:* https://localhost:* http://***********:* https://***********:* http://*************:* https://*************:* http://*************:* https://*************:*",VITE_CSP_DEFAULT_SRC:"'self'",VITE_CSP_SCRIPT_SRC:"'self' 'unsafe-inline'",VITE_CSP_STYLE_SRC:"'self' 'unsafe-inline'",VITE_ENABLE_GZIP:"true",VITE_SERVICE_A_URL:"http://*************:8080",VITE_SERVICE_B_URL:"http://*************:8080",VITE_EXTRA_ORIGINS:"http://*************:8080,http://*************:8080",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0},V=ft.create({baseURL:kt.VITE_VUE_APP_BASE_API,timeout:12e5});V.interceptors.request.use(async e=>{var o;if(!((o=e.url)==null?void 0:o.includes("/auth/token"))){const u=U.getters["user/hasToken"]?U.state.user.token:localStorage.getItem("authToken");u&&(e.headers.Authorization=`Bearer ${u}`)}return e.method==="get"&&(e.params={...e.params,_t:Date.now()}),e},e=>Promise.reject(e));V.interceptors.response.use(e=>e.data,async e=>{var o;const{response:r}=e;if(r){const{status:u,data:h}=r;switch(u){case 401:G.exports.Message.error("\u767B\u5F55\u5DF2\u8FC7\u671F\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"),await U.dispatch("user/logout");try{const d=(o=window.vueApp)==null?void 0:o.$router;d?d.push("/login"):window.location.href="/login"}catch(d){console.error("\u8DEF\u7531\u8DF3\u8F6C\u5931\u8D25\uFF0C\u4F7F\u7528\u5907\u7528\u65B9\u6848:",d),window.location.href="/login"}break;case 403:break;case 404:G.exports.Message.error("\u8BF7\u6C42\u7684\u8D44\u6E90\u4E0D\u5B58\u5728");break;case 500:G.exports.Message.error("\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF");break;default:G.exports.Message.error((h==null?void 0:h.message)||(h==null?void 0:h.detail)||"\u8BF7\u6C42\u5931\u8D25")}}else e.code==="ECONNABORTED"?G.exports.Message.error("\u8BF7\u6C42\u8D85\u65F6\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5"):G.exports.Message.error("\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC");return Promise.reject(e)});function Nt(e){return V({url:"/auth/token",method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded"},data:new URLSearchParams({username:e.username,password:e.password})})}function Ut(e){return V({url:"/auth/register",method:"post",data:{user_name:e.username,password:e.password,email:e.email}})}function $t(){return V({url:"/auth/refresh",method:"post"})}function Bt(){return V({url:"/api/simple-auth/logout",method:"post"})}function On(e,r){return Math.random().toString(36).substr(2,9),V({url:"/api/permissions/check-document-access",method:"post",data:{doc_id:e,permission_type:r}}).then(h=>h).catch(h=>{throw console.error("\u6743\u9650\u68C0\u67E5API\u9519\u8BEF:",h.message),h})}function kn(e){return V({url:"/api/permissions/request",method:"post",data:e})}function xt(){return V({url:"/api/simple-auth/user-info",method:"get"})}const pe="authToken",_e="refresh_token",ve="currentUser",Mt={token:localStorage.getItem(pe)||"",refreshToken:localStorage.getItem(_e)||"",userInfo:JSON.parse(localStorage.getItem(ve)||"{}"),isLoggedIn:!1,loginLoading:!1},Ft={SET_TOKEN(e,r){e.token=r,r?localStorage.setItem(pe,r):localStorage.removeItem(pe)},SET_REFRESH_TOKEN(e,r){e.refreshToken=r,r?localStorage.setItem(_e,r):localStorage.removeItem(_e)},SET_USER_INFO(e,r){e.userInfo=r||{},r&&Object.keys(r).length>0?(localStorage.setItem(ve,JSON.stringify(r)),console.log("\u{1F504} \u8BBE\u7F6E\u65B0\u7528\u6237\u4FE1\u606F\uFF0C\u6E05\u9664\u641C\u7D22\u53C2\u6570\u7F13\u5B58"),localStorage.removeItem("queryParams")):localStorage.removeItem(ve)},SET_LOGIN_STATUS(e,r){e.isLoggedIn=r},SET_LOGIN_LOADING(e,r){e.loginLoading=r},CLEAR_AUTH_DATA(e){e.token="",e.refreshToken="",e.userInfo={},e.isLoggedIn=!1,localStorage.removeItem(pe),localStorage.removeItem(_e),localStorage.removeItem(ve),localStorage.removeItem("queryParams")}},Vt={async login({commit:e,dispatch:r},o){e("SET_LOGIN_LOADING",!0);try{const u=await Nt(o),{access_token:h,refresh_token:d,user_info:w}=u.data||u;return e("SET_TOKEN",h),e("SET_REFRESH_TOKEN",d),e("SET_USER_INFO",w),e("SET_LOGIN_STATUS",!0),w!=null&&w.email&&(e("SET_EMAIL",w.email,{root:!0}),localStorage.setItem("email",w.email)),w!=null&&w.username&&localStorage.setItem("userName",w.username),G.exports.Message.success("\u767B\u5F55\u6210\u529F"),Promise.resolve(u)}catch(u){return G.exports.Message.error(u.message||"\u767B\u5F55\u5931\u8D25"),Promise.reject(u)}finally{e("SET_LOGIN_LOADING",!1)}},async register({commit:e},r){try{const o=await Ut(r);return G.exports.Message.success("\u6CE8\u518C\u6210\u529F"),Promise.resolve(o)}catch(o){return G.exports.Message.error(o.message||"\u6CE8\u518C\u5931\u8D25"),Promise.reject(o)}},async logout({commit:e,state:r}){if(r.token)try{await Bt(),console.log("Logout API\u8C03\u7528\u6210\u529F")}catch(u){console.warn("Logout API\u8C03\u7528\u5931\u8D25\uFF0C\u4F46\u7EE7\u7EED\u6E05\u7406\u672C\u5730\u6570\u636E:",u.message)}else console.log("\u65E0token\u72B6\u6001\u9000\u51FA\u767B\u5F55\uFF0C\u8DF3\u8FC7API\u8C03\u7528");e("CLEAR_AUTH_DATA"),["email","userName","queryParams","access_token","refresh_token","user_info","loginSource","lastSessionId"].forEach(u=>{localStorage.removeItem(u)}),e("SET_EMAIL","",{root:!0}),window.parent===window?G.exports.Message.success("\u5DF2\u9000\u51FA\u767B\u5F55"):console.log("iframe\u73AF\u5883\u4E2D\u9000\u51FA\u767B\u5F55\u5B8C\u6210")},async getUserInfo({commit:e,state:r}){if(!r.token)return Promise.reject(new Error("No token"));try{const o=await xt(),u=o.data||o.user_info||o;return e("SET_USER_INFO",u),e("SET_LOGIN_STATUS",!0),u!=null&&u.email&&(e("SET_EMAIL",u.email,{root:!0}),localStorage.setItem("email",u.email)),u!=null&&u.user_name&&localStorage.setItem("userName",u.user_name),console.log("\u7528\u6237\u4FE1\u606F\u83B7\u53D6\u6210\u529F:",u.user_name||u.username),Promise.resolve(u)}catch(o){return console.error("\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u5931\u8D25:",o),e("CLEAR_AUTH_DATA"),Promise.reject(o)}},async refreshToken({commit:e,state:r}){if(!r.refreshToken)return Promise.reject(new Error("No refresh token"));try{const o=await $t(),{access_token:u,refresh_token:h}=o.data||o;return e("SET_TOKEN",u),h&&e("SET_REFRESH_TOKEN",h),Promise.resolve(u)}catch(o){return e("CLEAR_AUTH_DATA"),Promise.reject(o)}},async initAuth({commit:e,dispatch:r,state:o}){if(o.token)try{console.log("\u9A8C\u8BC1\u7528\u6237\u662F\u5426\u5728\u6570\u636E\u5E93\u4E2D\u5B58\u5728");try{await r("getUserInfo"),console.log("\u7528\u6237\u6570\u636E\u5E93\u9A8C\u8BC1\u6210\u529F")}catch(u){throw console.error("\u7528\u6237\u6570\u636E\u5E93\u9A8C\u8BC1\u5931\u8D25:",u),e("CLEAR_AUTH_DATA"),new Error("\u7528\u6237\u4E0D\u5B58\u5728\u4E8E\u6570\u636E\u5E93\u4E2D\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458")}}catch(u){console.error("Init auth failed:",u),e("CLEAR_AUTH_DATA")}},checkTokenValidity({state:e}){if(!e.token)return!1;try{const r=JSON.parse(atob(e.token.split(".")[1])),o=Date.now()/1e3;return r.exp>o}catch{return!1}}},qt={isLoggedIn:e=>e.isLoggedIn&&!!e.token,userInfo:e=>e.userInfo,userName:e=>{var r,o;return((r=e.userInfo)==null?void 0:r.username)||((o=e.userInfo)==null?void 0:o.nick_name)||""},userEmail:e=>{var r;return((r=e.userInfo)==null?void 0:r.email)||""},userDept:e=>{var r;return((r=e.userInfo)==null?void 0:r.dept_name)||""},hasToken:e=>!!e.token,avatar:e=>{var r;return((r=e.userInfo)==null?void 0:r.avatar)||""}},Gt={namespaced:!0,state:Mt,mutations:Ft,actions:Vt,getters:qt};function Yt(e){return V({url:"/api/assembly-history/conversations",method:"post",data:e})}function Ht(e={}){return V({url:"/api/assembly-history/conversations",method:"get",params:e})}function Kt(e){return V({url:`/api/assembly-history/conversations/${e}`,method:"delete"})}function Wt(){return V({url:"/api/assembly-history/sessions/generate",method:"post"})}te.use(or);const zt={email:localStorage.getItem("email"),history:[],currentSessionId:null},jt={SET_EMAIL(e,r){e.email=r},SET_HISTORY(e,r){Array.isArray(r)?e.history=r:e.history.unshift(r)},SET_CURRENT_SESSION_ID(e,r){e.currentSessionId=r},REMOVE_HISTORY_ITEM(e,r){e.history=e.history.filter(o=>o.time!==r&&o.id!==r)}},Jt={setEamil({commit:e},r){e("SET_EMAIL",r)},async setHistory({commit:e,dispatch:r},o){if(e("SET_HISTORY",o),!Array.isArray(o))try{await r("saveHistoryToDatabase",o)}catch(u){console.error("\u4FDD\u5B58\u5386\u53F2\u8BB0\u5F55\u5230\u6570\u636E\u5E93\u5931\u8D25:",u)}},async initHistory({commit:e,dispatch:r}){try{console.log("\u{1F680} \u4ECE\u6570\u636E\u5E93\u52A0\u8F7D\u5386\u53F2\u8BB0\u5F55..."),await r("loadHistoryFromDatabase"),console.log("\u2705 \u5386\u53F2\u8BB0\u5F55\u52A0\u8F7D\u6210\u529F")}catch(o){console.error("\u274C \u4ECE\u6570\u636E\u5E93\u52A0\u8F7D\u5386\u53F2\u8BB0\u5F55\u5931\u8D25:",o);try{const u=await ir.getItem("history");if(u&&u.length>0){e("SET_HISTORY",u),console.log("\u{1F4E6} \u4F7F\u7528\u672C\u5730\u5907\u4EFD\u5386\u53F2\u8BB0\u5F55:",u.length,"\u6761"),console.log("\u{1F504} \u5C1D\u8BD5\u8FC1\u79FB\u672C\u5730\u5386\u53F2\u8BB0\u5F55\u5230\u6570\u636E\u5E93...");for(const h of u)try{await r("saveHistoryToDatabase",h)}catch(d){console.warn("\u8FC1\u79FB\u5355\u6761\u8BB0\u5F55\u5931\u8D25:",d)}}else e("SET_HISTORY",[])}catch(u){console.error("\u274C \u672C\u5730\u5B58\u50A8\u4E5F\u5931\u8D25\u4E86:",u),e("SET_HISTORY",[])}}},async loadHistoryFromDatabase({commit:e}){try{const r=await Ht({page:1,page_size:100});if(r.code===200){const o=r.data.conversations.map(u=>{let h=u.response_data;if(typeof h=="string")try{h=JSON.parse(h)}catch(E){console.warn("\u89E3\u6790response_data\u5931\u8D25:",E),h={response:h}}let d=u.request_data;if(typeof d=="string")try{d=JSON.parse(d)}catch(E){console.warn("\u89E3\u6790request_data\u5931\u8D25:",E),d={}}let w=u.echart_data;if(typeof w=="string")try{w=JSON.parse(w)}catch(E){console.warn("\u89E3\u6790echart_data\u5931\u8D25:",E),w=null}return{id:u.id,time:new Date(u.created_at).getTime(),req:d,res:(h==null?void 0:h.response)||h,echart:w,image:u.image_data,type:u.assembly_type==="custom"?"1":"2",session_id:u.session_id}});e("SET_HISTORY",o)}}catch(r){throw console.error("\u4ECE\u6570\u636E\u5E93\u52A0\u8F7D\u5386\u53F2\u8BB0\u5F55\u5931\u8D25:",r),r}},async saveHistoryToDatabase({state:e},r){try{let o=e.currentSessionId;if(!o){const d=await Wt();d.code===200&&(o=d.data.session_id)}const u={session_id:o,assembly_type:r.type==="1"?"custom":"template",request_data:r.req,response_data:{response:r.res},echart_data:r.echart,image_data:r.image,status:"completed"},h=await Yt(u);if(h.code===200)return console.log("\u5386\u53F2\u8BB0\u5F55\u5DF2\u4FDD\u5B58\u5230\u6570\u636E\u5E93:",h.data.id),h.data.id}catch(o){throw console.error("\u4FDD\u5B58\u5386\u53F2\u8BB0\u5F55\u5230\u6570\u636E\u5E93\u5931\u8D25:",o),o}},async deleteHistory({commit:e,state:r},o){try{const u=r.history.find(h=>h.time===o||h.id===o);u&&u.id&&await Kt(u.id),e("REMOVE_HISTORY_ITEM",o)}catch(u){console.error("\u5220\u9664\u5386\u53F2\u8BB0\u5F55\u5931\u8D25:",u),e("REMOVE_HISTORY_ITEM",o)}}},Qt=new or.Store({state:zt,mutations:jt,actions:Jt,modules:{user:Gt}}),U=Qt;function ye(e){if(!e||!e.message)return!1;const r=e.message.toLowerCase();return r.includes("failed to fetch dynamically imported module")||r.includes("loading chunk")||r.includes("loading css chunk")||r.includes("failed to import")}function we(e,r={}){const{targetPath:o,forceReload:u=!1,retryDelay:h=1e3}=r;if(console.warn("\u{1F504} \u68C0\u6D4B\u5230\u6A21\u5757\u52A0\u8F7D\u5931\u8D25:",e.message),console.warn("\u{1F4DD} \u8FD9\u901A\u5E38\u662F\u7531\u4E8E\u4EE5\u4E0B\u539F\u56E0\u4E4B\u4E00:"),console.warn("   1. \u6D4F\u89C8\u5668\u7F13\u5B58\u4E86\u65E7\u7248\u672C\u7684\u6A21\u5757\u5F15\u7528"),console.warn("   2. \u7F51\u7EDC\u8FDE\u63A5\u4E0D\u7A33\u5B9A"),console.warn("   3. \u670D\u52A1\u5668\u4E0A\u7684\u6587\u4EF6\u5DF2\u66F4\u65B0\u4F46\u6D4F\u89C8\u5668\u672A\u5237\u65B0"),console.warn("\u{1F527} \u5904\u7406\u9009\u9879:",{targetPath:o,forceReload:u,retryDelay:h}),u){console.warn("\u{1F504} \u5F3A\u5236\u5237\u65B0\u9875\u9762\u4EE5\u89E3\u51B3\u95EE\u9898"),setTimeout(()=>{window.location.reload()},h);return}if(o){console.warn("\u{1F504} \u8DF3\u8F6C\u5230\u76EE\u6807\u8DEF\u5F84\u4EE5\u89E3\u51B3\u95EE\u9898:",o),setTimeout(()=>{try{window.location=o}catch(d){console.warn("\u26A0\uFE0F \u8DEF\u5F84\u8DF3\u8F6C\u5931\u8D25\uFF0C\u6539\u4E3A\u5237\u65B0\u9875\u9762:",d.message),window.location.reload()}},h);return}console.warn("\u{1F504} \u5237\u65B0\u5F53\u524D\u9875\u9762\u4EE5\u89E3\u51B3\u95EE\u9898"),setTimeout(()=>{window.location.reload()},h)}function Nn(e,r=3,o=1e3){return async function(...h){let d;for(let w=1;w<=r;w++)try{return console.log(`\u{1F4E6} \u5C1D\u8BD5\u52A8\u6001\u5BFC\u5165 (\u7B2C${w}\u6B21)`),await e(...h)}catch(E){if(d=E,ye(E))if(console.warn(`\u26A0\uFE0F \u7B2C${w}\u6B21\u5BFC\u5165\u5931\u8D25:`,E.message),w<r){console.log(`\u{1F504} ${o}ms\u540E\u91CD\u8BD5...`),await new Promise(T=>setTimeout(T,o));continue}else throw console.error("\u274C \u6240\u6709\u91CD\u8BD5\u90FD\u5931\u8D25\u4E86\uFF0C\u6267\u884C\u9519\u8BEF\u5904\u7406"),we(E,{forceReload:!0}),E;else throw E}throw d}}function Xt(){window.addEventListener("error",function(e){if(e.error&&ye(e.error))return console.warn("\u{1F310} \u5168\u5C40\u9519\u8BEF\u5904\u7406\u5668\u6355\u83B7\u5230\u6A21\u5757\u52A0\u8F7D\u9519\u8BEF"),we(e.error,{forceReload:!0,retryDelay:2e3}),e.preventDefault(),!1}),window.addEventListener("unhandledrejection",function(e){if(e.reason&&ye(e.reason))return console.warn("\u{1F310} Promise\u62D2\u7EDD\u5904\u7406\u5668\u6355\u83B7\u5230\u6A21\u5757\u52A0\u8F7D\u9519\u8BEF"),we(e.reason,{forceReload:!0,retryDelay:2e3}),e.preventDefault(),!1}),console.log("\u2705 \u5168\u5C40\u6A21\u5757\u9519\u8BEF\u5904\u7406\u5668\u5DF2\u8BBE\u7F6E")}function Un(){try{const e=[];for(let o=0;o<localStorage.length;o++){const u=localStorage.key(o);u&&(u.includes("cache")||u.includes("version"))&&e.push(u)}e.forEach(o=>localStorage.removeItem(o));const r=[];for(let o=0;o<sessionStorage.length;o++){const u=sessionStorage.key(o);u&&(u.includes("cache")||u.includes("version"))&&r.push(u)}r.forEach(o=>sessionStorage.removeItem(o)),console.log("\u{1F9F9} \u5E94\u7528\u7F13\u5B58\u5DF2\u6E05\u7406")}catch(e){console.warn("\u26A0\uFE0F \u6E05\u7406\u5E94\u7528\u7F13\u5B58\u65F6\u51FA\u9519:",e)}}te.use(sr);const re=new sr({mode:"history",scrollBehavior(e,r,o){return{x:0,y:0}},routes:[{path:"/login",name:"login",component:()=>F(()=>import("./Login.d9b12a01.js"),["Login.d9b12a01.js","vendor.056885aa.js","element.65d3a407.js","utils.7bf54f36.js","Login.f85af641.css"]),meta:{title:"\u7528\u6237\u767B\u5F55",requiresAuth:!1,hideForAuth:!0}},{path:"/register",name:"register",component:()=>F(()=>import("./Register.48ae6ae2.js"),["Register.48ae6ae2.js","vendor.056885aa.js","element.65d3a407.js","utils.7bf54f36.js","Register.dd944082.css"]),meta:{title:"\u7528\u6237\u6CE8\u518C",requiresAuth:!1,hideForAuth:!0}},{path:"/",name:"home",component:()=>F(()=>import("./index.99df8b87.js"),["index.99df8b87.js","vendor.056885aa.js","element.65d3a407.js","utils.7bf54f36.js","index.d0af993d.css"]),meta:{title:"\u9996\u9875",requiresAuth:!0}},{path:"/search",name:"search",component:()=>F(()=>import("./search.bff5190c.js"),["search.bff5190c.js","permission.aa52f4aa.js","vendor.056885aa.js","element.65d3a407.js","utils.7bf54f36.js","search.a1e21804.js","request_ai.f9c15611.js","index.d17f4061.js","index.d186ba97.js","search.06caed61.css"]),meta:{title:"\u641C\u7D22\u7ED3\u679C",requiresAuth:!0}},{path:"/info",name:"info",component:()=>F(()=>import("./projectInfo.dc477e21.js"),["projectInfo.dc477e21.js","search.a1e21804.js","request_ai.f9c15611.js","vendor.056885aa.js","element.65d3a407.js","projectInfoDesc.bbefed2b.js","utils.7bf54f36.js","projectInfoDesc.eec6d0da.css","projectInfo.31942720.css"]),meta:{title:"\u9879\u76EE\u4FE1\u606F",requiresAuth:!0}},{path:"/desc",name:"desc",component:()=>F(()=>import("./projectInfoDesc.bbefed2b.js"),["projectInfoDesc.bbefed2b.js","vendor.056885aa.js","element.65d3a407.js","utils.7bf54f36.js","projectInfoDesc.eec6d0da.css"]),meta:{title:"\u9879\u76EE\u8BE6\u60C5",requiresAuth:!0}},{path:"/preview",name:"preview",component:()=>F(()=>import("./preview.c8c14170.js"),["preview.c8c14170.js","element.65d3a407.js","vendor.056885aa.js","permission.aa52f4aa.js","utils.7bf54f36.js","preview.29da580a.css"]),meta:{title:"\u6587\u6863\u9884\u89C8",requiresAuth:!0}},{path:"/assembly",name:"assembly",component:()=>F(()=>import("./assembly.7d345253.js"),["assembly.7d345253.js","request_ai.f9c15611.js","vendor.056885aa.js","element.65d3a407.js","index.d17f4061.js","utils.7bf54f36.js","index.d186ba97.js","assembly.2c622236.css"]),meta:{title:"\u7EC4\u88C5\u67E5\u8BE2",requiresAuth:!0}},{path:"/permission-request",name:"permissionRequest",component:()=>F(()=>import("./PermissionRequest.ec83210b.js"),["PermissionRequest.ec83210b.js","vendor.056885aa.js","element.65d3a407.js","utils.7bf54f36.js","PermissionRequest.08dbeaf7.css"]),meta:{title:"\u6743\u9650\u7533\u8BF7",requiresAuth:!0}},{path:"/permission-approval",name:"permissionApproval",component:()=>F(()=>import("./PermissionApproval.9b7e218b.js"),["PermissionApproval.9b7e218b.js","vendor.056885aa.js","element.65d3a407.js","utils.7bf54f36.js","PermissionApproval.88902294.css"]),meta:{title:"\u6743\u9650\u5BA1\u6838",requiresAuth:!0,requiresAdmin:!0}},{path:"/permissions",name:"permissions",component:()=>F(()=>import("./permissions.3e3ba37d.js"),["permissions.3e3ba37d.js","permission.aa52f4aa.js","vendor.056885aa.js","element.65d3a407.js","utils.7bf54f36.js","index.d17f4061.js","permissions.afa62a5a.css"]),meta:{title:"\u6743\u9650\u7BA1\u7406",requiresAuth:!0}},{path:"/tasks",name:"tasks",component:()=>F(()=>import("./tasks.82d89782.js"),["tasks.82d89782.js","vendor.056885aa.js","element.65d3a407.js","utils.7bf54f36.js","tasks.5fb71657.css"]),meta:{title:"\u4EFB\u52A1\u7BA1\u7406",requiresAuth:!1}},{path:"/test-module-error",name:"testModuleError",component:()=>F(()=>import("./test-module-error.f6317992.js"),["test-module-error.f6317992.js","vendor.056885aa.js","element.65d3a407.js","utils.7bf54f36.js","test-module-error.5a14fb7a.css"]),meta:{title:"\u6A21\u5757\u9519\u8BEF\u6D4B\u8BD5",requiresAuth:!1}},{path:"*",name:"notFound",redirect:"/"}]});re.onError((e,r)=>{if(console.error("\u{1F6A8} \u8DEF\u7531\u9519\u8BEF:",e),ye(e)){console.warn("\u{1F504} \u8DEF\u7531\u5668\u68C0\u6D4B\u5230\u6A21\u5757\u52A0\u8F7D\u5931\u8D25\uFF0C\u4F7F\u7528\u7EDF\u4E00\u5904\u7406\u5668");const o=r&&r.fullPath?r.fullPath:window.location.pathname;console.warn("\u{1F3AF} \u76EE\u6807\u8DEF\u5F84:",o),we(e,{targetPath:o,retryDelay:500});return}console.error("\u274C \u672A\u5904\u7406\u7684\u8DEF\u7531\u9519\u8BEF:",e)});re.beforeEach((e,r,o)=>{var A,O,_;(A=e.meta)!=null&&A.title&&(document.title=`${e.meta.title} - \u6587\u6863\u641C\u7D22\u7CFB\u7EDF`);const u=((O=e.meta)==null?void 0:O.requiresAuth)!==!1,h=((_=e.meta)==null?void 0:_.hideForAuth)===!0;if(u===!1){o();return}const d=U.getters["user/hasToken"],w=U.getters["user/userInfo"];let E=!1;if(d&&w&&(w.user_id||w.id))E=!0;else if(d)try{const S=localStorage.getItem("currentUser");if(S){const C=JSON.parse(S);C&&C.user_id&&(E=!0,U.commit("user/SET_USER_INFO",C),U.commit("user/SET_LOGIN_STATUS",!0))}}catch{E=!1}if(E&&h){o("/");return}if(!E&&u){o({path:"/login",query:{redirect:e.fullPath}});return}const T=U.state.email||U.getters["user/userEmail"];T?window.email=T:window.email="\u4FDD\u5BC6\u6587\u6863 \u8BF7\u52FF\u5916\u4F20",o()});function Zt(e){return me(e)>me()?me(e.slice(0,4)).format("YYYY-MM-DD HH:mm:ss"):me(e).format("YYYY-MM-DD HH:mm:ss")}function en(e,r){return ur(new Date(e),r)}function ur(e,r){e||(e=new Date),typeof e=="string"&&(console.log("\u65F6\u95F4:"+e),e=new Date(e.replace(/-/g,"/"))),r||(r="yyyy-MM-dd hh:mm:ss");var o={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};console.log(r),/(y+)/.test(r)&&(r=r.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length)));for(var u in o)new RegExp("("+u+")").test(r)&&(r=r.replace(RegExp.$1,RegExp.$1.length==1?o[u]:("00"+o[u]).substr((""+o[u]).length)));return r}function rn(e){return typeof e=="string"?new Date(e.replace(/-/g,"/")):e}const nr=Object.freeze(Object.defineProperty({__proto__:null,time:Zt,timeFmt:en,dateFmt:ur,strToData:rn},Symbol.toStringTag,{value:"Module"})),tn=e=>{Object.keys(nr).forEach(r=>{e.filter(r,nr[r])})},nn={install:tn};function on(){return new URLSearchParams(window.location.search).get("token")}function sn(){const r=new URLSearchParams(window.location.search).get("redirect");if(r)return r;const o=window.location.pathname;return o&&o!=="/"&&o!=="/login"?o:null}function lr(e){if(!e)return null;try{const r=e.split(".");if(r.length!==3)return null;let o=r[1];for(o=o.replace(/-/g,"+").replace(/_/g,"/");o.length%4;)o+="=";const u=atob(o);return JSON.parse(u)}catch(r){return console.error("\u89E3\u6790JWT\u8F7D\u8377\u5931\u8D25:",r,"Token:",e),null}}function fr(e){if(!e)return!1;const r=lr(e);if(!r)return!1;const u=["user_id","user_name","iss","iat","exp"].filter(d=>!r[d]);if(u.length>0)return console.error("Token\u7F3A\u5C11\u5FC5\u8981\u5B57\u6BB5:",u),!1;const h=Math.floor(Date.now()/1e3);return r.exp<h?(console.error("Token\u5DF2\u8FC7\u671F"),!1):r.iss!=="hngpt"?(console.error("\u65E0\u6548\u7684Token\u7B7E\u53D1\u65B9:",r.iss),!1):!0}async function an(e,r=null){try{if(!fr(e))throw new Error("\u65E0\u6548\u7684\u8BA4\u8BC1Token");const o=lr(e),u={id:parseInt(o.user_id),user_id:parseInt(o.user_id),user_name:o.user_name,nick_name:o.nick_name,email:o.email||`${o.user_name}@company.com`,phone:o.phone||"",dept_id:o.dept_id?parseInt(o.dept_id):null,dept_name:o.dept_name,roles:o.roles||[],issued_at:o.iat,expires_at:o.exp};return localStorage.setItem("authToken",e),localStorage.setItem("currentUser",JSON.stringify(u)),localStorage.setItem("email",u.email),localStorage.setItem("userName",u.user_name),r&&(r.commit("user/SET_TOKEN",e),r.commit("user/SET_USER_INFO",u),r.commit("user/SET_LOGIN_STATUS",!0),r.commit("SET_EMAIL",u.email)),!0}catch(o){return console.error("\u767B\u5F55\u5931\u8D25:",o),G.exports.Message.error("\u8BA4\u8BC1\u5931\u8D25: "+o.message),!1}}function cn(){try{const e=localStorage.getItem("currentUser");return e?JSON.parse(e):null}catch(e){return console.error("\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u5931\u8D25:",e),null}}function un(){return localStorage.getItem("authToken")}function ln(){localStorage.removeItem("authToken"),localStorage.removeItem("currentUser"),localStorage.removeItem("email"),localStorage.removeItem("userName")}async function fn(e=null,r=null){try{const o=on(),u=sn();if(o)if(await an(o,e)){const d=new URL(window.location);return d.searchParams.delete("token"),d.searchParams.delete("redirect"),window.history.replaceState({},"",d.toString()),r&&(await new Promise(w=>setTimeout(w,100)),u&&u!=="/"?await r.push(u):await r.push("/")),!0}else return console.error("\u274C \u767B\u5F55\u5904\u7406\u5931\u8D25"),!1;else{const h=un();if(h)if(fr(h)){if(e){const d=cn();d&&(e.commit("user/SET_TOKEN",h),e.commit("user/SET_USER_INFO",d),e.commit("user/SET_LOGIN_STATUS",!0),d.email&&e.commit("SET_EMAIL",d.email))}return!0}else ln();return!0}}catch(o){return console.error("\u{1F4A5} \u521D\u59CB\u5316\u8BA4\u8BC1\u5931\u8D25:",o),G.exports.Message.error("\u8BA4\u8BC1\u521D\u59CB\u5316\u5931\u8D25"),!1}}const dn={SERVICE_A_URL:"http://***********:8081",SERVICE_B_URL:"http://***********:8080",SERVICE_B_API:"http://***********:18888"};function hn(){return typeof window<"u"&&window.BUILD_CONFIG||dn}function mn(){return hn().SERVICE_A_URL}class gn{constructor(){this.currentUser=null,this.currentToken=null,this.sessionId=null,this.parentOrigin=null,this.isInIframe=window.self!==window.top,this.init()}init(){if(!this.isInIframe){console.log("\u{1F50D} \u5F53\u524D\u4E0D\u5728iframe\u4E2D\u8FD0\u884C");return}this.setupMessageListener(),this.detectParentOrigin(),this.parseUrlParams(),this.notifyIframeReady()}detectParentOrigin(){try{this.parentOrigin=document.referrer?new URL(document.referrer).origin:null}catch(r){console.warn("\u65E0\u6CD5\u68C0\u6D4B\u7236\u7A97\u53E3\u6765\u6E90:",r.message)}this.parentOrigin||(this.parentOrigin=mn()),console.log("\u{1F50D} \u68C0\u6D4B\u5230\u7236\u7A97\u53E3\u6765\u6E90:",this.parentOrigin)}parseUrlParams(){const r=new URLSearchParams(window.location.search);this.currentToken=r.get("token"),this.sessionId=r.get("session_id")||`session_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,this.currentToken&&(console.log("\u{1F511} \u4ECEURL\u83B7\u53D6\u5230token:",this.currentToken.substring(0,20)+"..."),console.log("\u{1F194} \u4F1A\u8BDDID:",this.sessionId),this.parseTokenInfo())}parseTokenInfo(){try{if(!this.currentToken)return;const r=this.currentToken.split(".");if(r.length===3){const o=JSON.parse(atob(r[1]));this.currentUser={user_id:o.user_id,user_name:o.user_name,nick_name:o.nick_name,dept_id:o.dept_id,roles:o.roles||[]},console.log("\u{1F464} \u89E3\u6790\u5230\u7528\u6237\u4FE1\u606F:",this.currentUser.user_name)}}catch(r){console.warn("Token\u89E3\u6790\u5931\u8D25:",r.message)}}setupMessageListener(){window.addEventListener("message",r=>{if(console.log("\u{1F4E8} \u6536\u5230\u6765\u81EA\u7236\u7A97\u53E3\u7684\u6D88\u606F:",r.data),r.data&&r.data.action==="goBack"&&r.data.source==="pdf-viewer"){console.log("\u{1F4E8} \u6536\u5230PDF\u67E5\u770B\u5668\u8FD4\u56DE\u6D88\u606F:",r.data),this.handlePdfViewerGoBack(r.data);return}if(r.data&&r.data.type==="IFRAME_GO_BACK"&&r.data.source==="preview-page"){console.log("\u{1F4E8} \u6536\u5230\u9884\u89C8\u9875\u9762\u8FD4\u56DE\u6D88\u606F:",r.data),this.handlePreviewPageGoBack(r.data);return}if(r.data&&r.data.type==="IFRAME_GO_BACK"&&r.data.source==="pdf-viewer"){console.log("\u{1F4E8} \u6536\u5230PDF\u67E5\u770B\u5668\u8FD4\u56DE\u6D88\u606F\uFF08\u65B0\u683C\u5F0F\uFF09:",r.data),this.handlePdfViewerGoBack(r.data);return}if(!this.isValidOrigin(r.origin)){console.warn("\u{1F6AB} \u6536\u5230\u6765\u81EA\u672A\u6388\u6743\u6E90\u7684\u6D88\u606F:",r.origin);return}if(!(r.data&&(r.data.type==="iframe-pos"||r.data.type==="get-iframe-pos")))switch(r.data.type){case"ping":this.handlePing(r);break;case"user-switch":this.handleUserSwitch(r.data);break;case"logout":this.handleLogout(r.data);break;case"route-change":this.handleRouteChange(r.data);break;case"token-refresh":this.handleTokenRefresh(r.data);break;default:console.log("\u{1F504} \u672A\u77E5\u6D88\u606F\u7C7B\u578B:",r.data.type)}})}isValidOrigin(r){return!0}handlePing(r){var o;this.sendMessageToParent({type:"pong",timestamp:Date.now(),sessionId:this.sessionId,user:(o=this.currentUser)==null?void 0:o.user_name},r.origin)}handleUserSwitch(r){var o,u;console.log("\u{1F504} \u5904\u7406\u7528\u6237\u5207\u6362:",(o=r.newUser)==null?void 0:o.user_name),this.clearCurrentSession(),this.currentUser=r.newUser,this.currentToken=r.newToken,this.sessionId=r.sessionId,this.notifyUserSwitch(r.newUser),this.sendMessageToParent({type:"user-switch-complete",sessionId:this.sessionId,user:(u=this.currentUser)==null?void 0:u.user_name})}handleLogout(r){console.log("\u{1F6AA} \u5904\u7406\u7528\u6237\u9000\u51FA"),this.clearCurrentSession(),this.notifyLogout(),this.sendMessageToParent({type:"logout-complete",sessionId:this.sessionId})}handleRouteChange(r){console.log("\u{1F504} \u5904\u7406\u8DEF\u7531\u53D8\u66F4:",r.route),this.notifyRouteChange(r.route)}handleTokenRefresh(r){console.log("\u{1F504} \u5904\u7406token\u5237\u65B0"),this.currentToken=r.newToken,this.notifyTokenRefresh(r.newToken)}handlePdfViewerGoBack(r){console.log("\u{1F519} \u5904\u7406PDF\u67E5\u770B\u5668\u8FD4\u56DE\u8BF7\u6C42:",r),this.notifyPdfViewerGoBack()}handlePreviewPageGoBack(r){console.log("\u{1F519} \u5904\u7406\u9884\u89C8\u9875\u9762\u8FD4\u56DE\u8BF7\u6C42:",r),this.notifyPreviewPageGoBack(r.targetRoute||"/search")}sendMessageToParent(r,o=null){if(!this.isInIframe)return;const u=o||this.parentOrigin;if(!u){console.warn("\u{1F6AB} \u65E0\u6CD5\u53D1\u9001\u6D88\u606F\uFF1A\u672A\u77E5\u7684\u7236\u7A97\u53E3\u6765\u6E90");return}try{window.parent.postMessage(r,u),console.log("\u{1F4E4} \u53D1\u9001\u6D88\u606F\u5230\u7236\u7A97\u53E3:",r.type)}catch(h){console.error("\u{1F4E4} \u53D1\u9001\u6D88\u606F\u5931\u8D25:",h.message)}}notifyIframeReady(){var r;this.sendMessageToParent({type:"iframe-ready",timestamp:Date.now(),url:window.location.href,sessionId:this.sessionId,user:(r=this.currentUser)==null?void 0:r.user_name})}clearCurrentSession(){Object.keys(localStorage).forEach(o=>{(o.startsWith("hngpt_")||o.startsWith("auth_"))&&localStorage.removeItem(o)}),sessionStorage.clear(),console.log("\u{1F9F9} \u4F1A\u8BDD\u6570\u636E\u5DF2\u6E05\u7406")}notifyUserSwitch(r){window.dispatchEvent(new CustomEvent("iframe-user-switch",{detail:{user:r,token:this.currentToken}}))}notifyLogout(){window.dispatchEvent(new CustomEvent("iframe-logout"))}notifyRouteChange(r){window.dispatchEvent(new CustomEvent("iframe-route-change",{detail:{route:r}}))}notifyTokenRefresh(r){window.dispatchEvent(new CustomEvent("iframe-token-refresh",{detail:{token:r}}))}notifyPdfViewerGoBack(){window.dispatchEvent(new CustomEvent("pdf-viewer-go-back",{detail:{timestamp:Date.now()}}))}notifyPreviewPageGoBack(r){window.dispatchEvent(new CustomEvent("preview-page-go-back",{detail:{targetRoute:r,timestamp:Date.now()}}))}getCurrentUser(){return this.currentUser}getCurrentToken(){return this.currentToken}getSessionId(){return this.sessionId}isRunningInIframe(){return this.isInIframe}}function pn(){const e=new gn;return console.log("\u{1F527} iframe\u5B89\u5168\u7BA1\u7406\u5668\u5DF2\u521D\u59CB\u5316"),e}function _n(){const e=U.getters["user/userInfo"];return((e==null?void 0:e.roles)||[]).includes("admin")}function vn(){const e=U.getters["user/userInfo"];return((e==null?void 0:e.roles)||[]).includes("deptAdmin")}function yn(){return _n()}function wn(){return yn()||vn()}const Sn={name:"UserMenu",data(){return{pendingRequestsCount:0}},computed:{...dt("user",["userName","userEmail","userDept","userInfo","avatar"]),userAvatar(){return this.avatar||""},canReviewPermissions(){return wn()}},mounted(){this.canReviewPermissions&&this.loadPendingRequestsCount()},methods:{...ht("user",["logout"]),async handleCommand(e){switch(e){case"profile":this.goToProfile();break;case"permission-request":this.goToPermissionRequest();break;case"permission-approval":this.goToPermissionApproval();break;case"settings":this.goToSettings();break;case"logout":await this.handleLogout();break}},goToProfile(){this.$message.info("\u4E2A\u4EBA\u8D44\u6599\u529F\u80FD\u5F85\u5B9E\u73B0")},goToPermissionRequest(){this.$router.push("/permission-request")},goToPermissionApproval(){this.$router.push("/permission-approval")},goToSettings(){this.$message.info("\u8BBE\u7F6E\u529F\u80FD\u5F85\u5B9E\u73B0")},async handleLogout(){try{await this.$confirm("\u786E\u5B9A\u8981\u9000\u51FA\u767B\u5F55\u5417\uFF1F","\u63D0\u793A",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning"}),await this.logout(),this.$message.success("\u5DF2\u9000\u51FA\u767B\u5F55"),this.$router.push("/login")}catch(e){e!=="cancel"&&console.error("\u9000\u51FA\u767B\u5F55\u5931\u8D25:",e)}},async loadPendingRequestsCount(){var e;try{const r=await this.$http.get("/auth/permission-requests/review",{params:{status:"pending",size:1}});this.pendingRequestsCount=((e=r.data)==null?void 0:e.total)||0}catch(r){console.error("\u83B7\u53D6\u5F85\u5BA1\u6838\u7533\u8BF7\u6570\u91CF\u5931\u8D25:",r)}}}};var En=function(){var r=this,o=r._self._c;return o("div",{staticClass:"user-menu"},[o("el-dropdown",{attrs:{trigger:"click"},on:{command:r.handleCommand}},[o("span",{staticClass:"user-info"},[o("el-avatar",{attrs:{size:32,src:r.userAvatar}},[o("i",{staticClass:"el-icon-user-solid"})]),o("span",{staticClass:"username"},[r._v(r._s(r.userName))]),o("i",{staticClass:"el-icon-arrow-down"})],1),o("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[o("el-dropdown-item",{attrs:{disabled:""}},[o("div",{staticClass:"user-detail"},[o("div",{staticClass:"name"},[r._v(r._s(r.userName))]),o("div",{staticClass:"email"},[r._v(r._s(r.userEmail))]),o("div",{staticClass:"dept"},[r._v(r._s(r.userDept))])])]),o("el-dropdown-item",{attrs:{divided:"",command:"profile"}},[o("i",{staticClass:"el-icon-user"}),r._v(" \u4E2A\u4EBA\u8D44\u6599 ")]),o("el-dropdown-item",{attrs:{command:"permission-request"}},[o("i",{staticClass:"el-icon-document-add"}),r._v(" \u6743\u9650\u7533\u8BF7 ")]),r.canReviewPermissions?o("el-dropdown-item",{attrs:{command:"permission-approval"}},[o("i",{staticClass:"el-icon-s-check"}),r._v(" \u6743\u9650\u5BA1\u6838 "),r.pendingRequestsCount>0?o("el-badge",{staticClass:"badge",attrs:{value:r.pendingRequestsCount}}):r._e()],1):r._e(),o("el-dropdown-item",{attrs:{divided:"",command:"settings"}},[o("i",{staticClass:"el-icon-setting"}),r._v(" \u8BBE\u7F6E ")]),o("el-dropdown-item",{attrs:{command:"logout"}},[o("i",{staticClass:"el-icon-switch-button"}),r._v(" \u9000\u51FA\u767B\u5F55 ")])],1)],1)],1)},In=[],Tn=ke(Sn,En,In,!1,null,"aa577473",null,null);const An=Tn.exports;te.use(mt);te.use(nn);te.component("UserMenu",An);te.prototype.$http=V;window.store=U;function bn(){window.addEventListener("iframe-user-switch",async e=>{const{user:r,token:o}=e.detail;console.log("\u{1F504} iframe\u5B89\u5168\u7BA1\u7406\u5668\uFF1A\u7528\u6237\u5207\u6362",r.user_name);try{await U.dispatch("user/setUserInfo",r),await U.dispatch("user/setToken",o),await U.dispatch("user/initAuth"),console.log("\u2705 \u7528\u6237\u5207\u6362\u5B8C\u6210")}catch(u){console.error("\u274C \u7528\u6237\u5207\u6362\u5931\u8D25:",u)}}),window.addEventListener("iframe-logout",async()=>{console.log("\u{1F6AA} iframe\u5B89\u5168\u7BA1\u7406\u5668\uFF1A\u7528\u6237\u9000\u51FA");try{await U.dispatch("user/logout"),re.push("/login"),console.log("\u2705 \u7528\u6237\u9000\u51FA\u5B8C\u6210")}catch(e){console.error("\u274C \u7528\u6237\u9000\u51FA\u5931\u8D25:",e)}}),window.addEventListener("iframe-route-change",e=>{const{route:r}=e.detail;console.log("\u{1F504} iframe\u5B89\u5168\u7BA1\u7406\u5668\uFF1A\u8DEF\u7531\u53D8\u66F4",r),re.currentRoute.path!==r&&re.push(r)}),window.addEventListener("iframe-token-refresh",async e=>{const{token:r}=e.detail;console.log("\u{1F504} iframe\u5B89\u5168\u7BA1\u7406\u5668\uFF1Atoken\u5237\u65B0");try{await U.dispatch("user/setToken",r),console.log("\u2705 token\u5237\u65B0\u5B8C\u6210")}catch(o){console.error("\u274C token\u5237\u65B0\u5931\u8D25:",o)}}),console.log("\u{1F527} iframe\u5B89\u5168\u7BA1\u7406\u5668\u4E8B\u4EF6\u76D1\u542C\u5DF2\u8BBE\u7F6E")}async function Rn(){try{Xt(),await fn(U,re),await U.dispatch("user/initAuth"),bn();const e=pn();window.iframeSecurityManager=e,new te({router:re,store:U,render:o=>o(tr),mounted(){window.vueApp=this,window.$vm=this}}).$mount("#app")}catch(e){console.error("\u5E94\u7528\u521D\u59CB\u5316\u5931\u8D25:",e),new te({router:re,store:U,render:o=>o(tr),mounted(){window.vueApp=this}}).$mount("#app")}}window.addEventListener("error",function(e){if(e.error&&e.error.message&&(e.error.message.includes("sessionStorage")||e.error.message.includes("localStorage")))return console.warn("\u68C0\u6D4B\u5230\u5B58\u50A8\u8BBF\u95EE\u9519\u8BEF:",e.error.message),window.self!==window.top&&console.warn("\u5F53\u524D\u5728iframe\u73AF\u5883\u4E2D\uFF0C\u5B58\u50A8\u8BBF\u95EE\u53EF\u80FD\u53D7\u9650"),e.preventDefault(),!1});Rn().then(()=>{console.log("\u{1F680} \u5E94\u7528\u542F\u52A8\u5B8C\u6210\uFF0Ciframe\u5B89\u5168\u7BA1\u7406\u5668\u5DF2\u63A5\u7BA1\u901A\u4FE1")});export{F as _,U as a,Nn as b,Un as c,On as g,ye as i,ke as n,kn as r,V as s};
