import{d as a}from"./utils.7bf54f36.js";import{e as r}from"./element.65d3a407.js";function u(s,e){const t=[];for(let o of s)e.includes(o)||t.push(o);for(let o of e)!s.includes(o)&&!t.includes(o)&&t.push(o);return t}function m(s){return a(s).format("YYYY-MM-DD HH:mm:ss")}async function f(s,e){r.exports.Message.info("\u4E0B\u8F7D\u4E2D\u2026\u2026");try{const t=localStorage.getItem("authToken")||window.store&&window.store.state.user.token;if(e||(e=s.substring(s.lastIndexOf("/")+1),e=e.split("?")[0],e.includes(".")||(e+=".pdf")),console.log("\u{1F517} \u4E0B\u8F7DURL:",s),console.log("\u{1F4C1} \u6587\u4EF6\u540D:",e),console.log("\u{1F511} Token\u5B58\u5728:",!!t),t){const o=await fetch(s,{method:"GET",headers:{Authorization:`Bearer ${t}`}});if(!o.ok)throw new Error(`\u4E0B\u8F7D\u5931\u8D25: ${o.status} ${o.statusText}`);const d=await o.blob(),c=window.URL.createObjectURL(d),n=document.createElement("a");n.href=c,n.download=e,n.style.display="none",document.body.appendChild(n),n.click(),setTimeout(()=>{document.body.removeChild(n),window.URL.revokeObjectURL(c)},100),console.log("\u2705 \u4E0B\u8F7D\u5B8C\u6210\uFF01"),r.exports.Message.success("\u4E0B\u8F7D\u5DF2\u5F00\u59CB")}else{console.warn("\u26A0\uFE0F \u6CA1\u6709\u627E\u5230\u8BA4\u8BC1token\uFF0C\u4F7F\u7528\u4F20\u7EDF\u4E0B\u8F7D\u65B9\u5F0F");const o=document.createElement("a");o.href=s,o.download=e,o.style.display="none",o.target="_blank",document.body.appendChild(o),o.click(),setTimeout(()=>{document.body.removeChild(o)},100),r.exports.Message.warning("\u4E0B\u8F7D\u5DF2\u5F00\u59CB\uFF0C\u5982\u679C\u5931\u8D25\u8BF7\u68C0\u67E5\u767B\u5F55\u72B6\u6001")}}catch(t){console.error("\u{1F4A5} \u4E0B\u8F7D\u5931\u8D25:",t),r.exports.Message.error(`\u4E0B\u8F7D\u5931\u8D25: ${t.message}`)}}export{m as a,f as d,u as f};
