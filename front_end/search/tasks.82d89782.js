import{s as f,n as m}from"./index.87b88fdf.js";import"./vendor.056885aa.js";import"./element.65d3a407.js";import"./utils.7bf54f36.js";function _(){return f({url:"/tasks",method:"get"})}function h(){const s={npm_config_user_agent:"npm/10.9.2 node/v22.16.0 linux x64 workspaces/false",HOSTNAME:"c926ea256276",npm_node_execpath:"/root/.nvm/versions/node/v22.16.0/bin/node",SHLVL:"0",PYTHON_PIP_VERSION:"23.0.1",npm_config_noproxy:"",HOME:"/root",OLDPWD:"/workspace/hngpt",NVM_BIN:"/root/.nvm/versions/node/v22.16.0/bin",npm_package_json:"/workspace/hngpt/front_end/package.json",PYTHONUNBUFFERED:"1",NVM_INC:"/root/.nvm/versions/node/v22.16.0/include/node",GPG_KEY:"A035C8C19219BA821ECEA86B64E628F8D684696D",npm_config_userconfig:"/root/.npmrc",npm_config_local_prefix:"/workspace/hngpt/front_end",COLOR:"0",NVM_DIR:"/root/.nvm",PYTHONDONTWRITEBYTECODE:"1",_:"/root/.nvm/versions/node/v22.16.0/bin/npm",npm_config_prefix:"/root/.nvm/versions/node/v22.16.0",npm_config_npm_version:"10.9.2",PYTHON_GET_PIP_URL:"https://github.com/pypa/get-pip/raw/9af82b715db434abb94a0a6f3569f43e72157346/public/get-pip.py",npm_config_cache:"/root/.npm",npm_config_node_gyp:"/root/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js",PATH:"/workspace/hngpt/front_end/node_modules/.bin:/workspace/hngpt/node_modules/.bin:/workspace/node_modules/.bin:/node_modules/.bin:/root/.nvm/versions/node/v22.16.0/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/node-gyp-bin:/root/.nvm/versions/node/v22.16.0/bin:/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin",NODE:"/root/.nvm/versions/node/v22.16.0/bin/node",npm_package_name:"es_search",LANG:"C.UTF-8",npm_lifecycle_script:"BUILD_ENV=production node --max-old-space-size=4096 ./node_modules/.bin/vite build",DEBIAN_FRONTEND:"noninteractive",HOST:"0.0.0.0",npm_package_version:"0.0.0",npm_lifecycle_event:"build",PYTHON_VERSION:"3.10.12",PYTHON_SETUPTOOLS_VERSION:"65.5.1",npm_config_globalconfig:"/root/.nvm/versions/node/v22.16.0/etc/npmrc",npm_config_init_module:"/root/.npm-init.js",PWD:"/workspace/hngpt/front_end",LC_ALL:"C.UTF-8",npm_execpath:"/root/.nvm/versions/node/v22.16.0/lib/node_modules/npm/bin/npm-cli.js",NVM_CD_FLAGS:"",PYTHON_GET_PIP_SHA256:"45a2bb8bf2bb5eff16fdd00faef6f29731831c7c59bd9fc2bf1f3bed511ff1fe",npm_config_global_prefix:"/root/.nvm/versions/node/v22.16.0",npm_command:"run-script",TZ:"Asia/Shanghai",BUILD_ENV:"production",INIT_CWD:"/workspace/hngpt/front_end",EDITOR:"vi",NODE_ENV:"production"}.VUE_APP_BASE_API||"/dev-api";return new EventSource(`${s}/tasks/stream`)}const v={name:"Tasks",data(){return{tasks:[],loading:!1,autoRefresh:!0,connectionStatus:"disconnected",lastUpdateTime:"",eventSource:null,refreshTimer:null}},computed:{filteredTasks(){const s=new Date,e=new Date(s.getTime()-24*60*60*1e3);return this.tasks.filter(t=>t.status==="running"||t.status==="pending"?!0:t.status==="completed"||t.status==="failed"?new Date(t.updated_at||t.created_at)>=e:!1)}},mounted(){this.initializeConnection()},beforeDestroy(){this.cleanup()},methods:{goBack(){this.$router.go(-1)},getFileCount(s){const e=s.processed_files?s.processed_files.length:0,t=s.failed_files?s.failed_files.length:0,n=e+t,r=s.total_files||Object.keys(s.file_progress||{}).length||1;return`${n} / ${r}`},getTableRowClass({row:s}){return{running:"table-row-running",completed:"table-row-completed",failed:"table-row-failed",cancelled:"table-row-cancelled"}[s.status]||"table-row-default"},handleExpandChange(s,e){console.log("\u5C55\u5F00\u72B6\u6001\u53D8\u5316:",s.task_id,e.length>0)},getFileProgressData(s){return s?Object.entries(s).map(([e,t])=>({fullPath:e,fileName:this.getFileName(e),doc_id:t.doc_id||"",status:t.status||"unknown",progress:t.progress||0,duration:t.duration,stage:t.stage||"",stage_detail:t.stage_detail||"",error:t.error||"",message:t.message||"",updated_at:t.updated_at||""})):[]},getOrderedAgentProgress(s){return s?Object.entries(s).map(([t,n])=>({agentId:t,...n})).sort((t,n)=>(t.level||0)-(n.level||0)):[]},getAgentSegmentClass(s){return`agent-segment-${s}`},getAgentSegmentStyle(s){const e=Object.keys(s).length;return{width:`${e>0?100/e:100}%`}},getAgentSegmentFillStyle(s){const e=s.progress||0,t=s.status||"pending";let n="#e9ecef";switch(t){case"running":n="#ffc107";break;case"completed":n="#28a745";break;case"failed":n="#dc3545";break;case"skipped":n="#17a2b8";break}return{width:`${e}%`,backgroundColor:n}},getAgentProgressText(s){if(!s)return"0%";const e=Object.values(s),t=e.filter(o=>o.status==="completed").length,n=e.length,r=n>0?Math.round(t/n*100):0,a=e.find(o=>o.status==="running");if(a)return`${a.agent_name} (${r}%)`;const i=e.filter(o=>o.status==="completed").sort((o,l)=>(l.level||0)-(o.level||0))[0];return i&&r===100?"\u5DF2\u5B8C\u6210 (100%)":i?`${i.agent_name}\u5B8C\u6210 (${r}%)`:`${r}%`},getProjectStages(s){const e=[{name:"\u6587\u4EF6\u5904\u7406",key:"file_processing"},{name:"\u5411\u91CF\u5316\u5B58\u50A8",key:"vectorization"},{name:"\u4FE1\u606F\u68C0\u7D22",key:"retrieval"},{name:"\u4FE1\u606F\u63D0\u53D6",key:"extraction"},{name:"\u6570\u636E\u5B58\u50A8",key:"storage"}],t=s.agent_progress||{},n=s.file_progress||{};Math.max(Object.keys(n).length,1);const r=Object.values(t),a=r.find(c=>c.agent_name==="InitAgent"),i=r.find(c=>c.agent_name==="DownloadAgent"),o=r.find(c=>c.agent_name==="ParseAgent"),l=r.find(c=>c.agent_name==="ExtractionAgent"),g=r.find(c=>c.agent_name==="StorageAgent");return e.map((c,p)=>{let u="pending",d=0;switch(c.key){case"file_processing":o&&o.status==="completed"?(u="completed",d=100):o&&o.status==="running"?(u="running",d=o.progress||50):i&&i.status==="completed"?(u="running",d=75):i&&i.status==="running"?(u="running",d=i.progress||25):a&&a.status==="completed"?(u="running",d=25):a&&a.status==="running"&&(u="running",d=a.progress||10);break;case"vectorization":l&&l.status==="running"?(u="running",d=50):o&&o.status==="completed"&&(u="running",d=25);break;case"retrieval":l&&(l.status==="completed"?(u="completed",d=100):l.status==="running"&&(u="running",d=l.progress||50));break;case"extraction":l&&(l.status==="completed"?(u="completed",d=100):l.status==="running"&&(u="running",d=l.progress||50));break;case"storage":g&&(g.status==="completed"?(u="completed",d=100):g.status==="running"&&(u="running",d=g.progress||50));break}return{...c,status:u,progress:d}})},getProjectStageClass(s){return`project-stage-${s}`},getProjectStageStyle(s){return{width:`${20}%`}},getProjectStageFillStyle(s){const e=s.progress||0,t=s.status||"pending";let n="#e9ecef";switch(t){case"running":n="#ffc107";break;case"completed":n="#28a745";break;case"failed":n="#dc3545";break}return{width:`${e}%`,backgroundColor:n}},getProjectProgressText(s){const e=this.getProjectStages(s),t=e.filter(o=>o.status==="completed").length,n=Math.round(t/e.length*100),r=s.file_progress||{},a=Object.keys(r).length,i=Object.values(r).filter(o=>o.status==="completed").length;return`${n}% ${i}/${a}`},getFileStages(s){const e=[{name:"\u4E0B\u8F7D",key:"download"},{name:"\u89E3\u6790",key:"parse"},{name:"\u5411\u91CF\u5316",key:"vectorize"},{name:"ES\u5B58\u50A8",key:"es_storage"}],t=s.stage||"download";return e.map((n,r)=>{let a="pending";if(s.status==="failed")a=t===n.key?"failed":"pending";else if(s.status==="completed")a="completed";else{const i=["download","parse","vectorize","es_storage"],o=i.indexOf(t),l=i.indexOf(n.key);l<o?a="completed":l===o?a="running":a="pending"}return{...n,status:a}})},getFileStageClass(s){return`file-stage-${s}`},getFileStageFillStyle(s){const e=s.status||"pending";let t="#e9ecef",n="0%";switch(e){case"running":t="#ffc107",n="50%";break;case"completed":t="#28a745",n="100%";break;case"failed":t="#dc3545",n="100%";break}return{width:n,backgroundColor:t}},getFileStageText(s){const e=s.stage||"download",t=s.status||"processing",r={download:"\u4E0B\u8F7D",parse:"\u89E3\u6790",vectorize:"\u5411\u91CF\u5316",es_storage:"ES\u5B58\u50A8"}[e]||e;return t==="completed"?"\u5904\u7406\u5B8C\u6210":t==="failed"?`${r}\u5931\u8D25`:`${r}\u4E2D...`},getFileStatusText(s){return{processing:"\u5904\u7406\u4E2D",completed:"\u5DF2\u5B8C\u6210",failed:"\u5931\u8D25"}[s]||s},getUnifiedProjectProgress(s){const e=s.agent_progress||{},t=s.file_progress||{};if(s.status==="completed")return 100;if(Object.keys(e).length>0){const r=Object.values(e),a=r.filter(g=>g.status==="completed").length,i=r.filter(g=>g.status==="running").length,o=r.length;if(o===0)return 0;let l=a*100;if(i>0){const g=r.filter(c=>c.status==="running").reduce((c,p)=>c+(p.progress||0),0);l+=g}return Math.round(l/o)}if(Object.keys(t).length>0){const r=Object.keys(t).length,a=Object.values(t).filter(i=>i.status==="completed").length;return Math.round(a/r*100)}const n=Number(s.progress)||0;return n===0&&s.status==="completed"?100:n},getTraditionalProgress(s){if(s.status==="completed")return 100;if(s.status==="failed")return 0;const e=s.file_progress||{};if(Object.keys(e).length>0){const n=Object.keys(e).length,r=Object.values(e).filter(a=>a.status==="completed").length;return Math.round(r/n*100)}const t=Number(s.progress)||0;return t===0&&s.status==="completed"?100:t},getUnifiedProgressInfo(s){const e=s.file_progress||{},t=Object.keys(e).length,n=Object.values(e).filter(r=>r.status==="completed").length;return t===0?"0/0":`${n}/${t}`},getFileProcessingPercentage(s){const e=s.file_progress||{},t=Object.keys(e).length;if(t===0)return 0;let n=0;return Object.values(e).forEach(r=>{const a=r.stage||"download",i=r.status||"processing";let o=0;i==="completed"?o=100:i==="processing"&&(o={download:25,parse:50,vectorize:75,es_storage:100}[a]||0),n+=o}),Math.round(n/t)},getFileProgressClass(s){return`file-progress-${s.status||"running"}`},getFileProgressInfo(s){const e=s.file_progress||{},t=Object.keys(e).length;if(t===0)return"0/0 \u6587\u4EF6";const n=Object.values(e).filter(a=>a.status==="completed").length,r=Object.values(e).filter(a=>a.status==="processing").length;return n===t?`${t}/${t} \u6587\u4EF6\u5B8C\u6210`:r>0?`${n}/${t} \u6587\u4EF6 (${r}\u5904\u7406\u4E2D)`:`${n}/${t} \u6587\u4EF6`},formatDuration(s){if(s==null)return"-";const e=parseFloat(s);if(isNaN(e))return"-";if(e<1)return`${Math.round(e*1e3)}ms`;if(e<60)return`${e.toFixed(1)}s`;if(e<3600){const t=Math.floor(e/60),n=Math.round(e%60);return`${t}m${n}s`}else{const t=Math.floor(e/3600),n=Math.floor(e%3600/60);return`${t}h${n}m`}},initializeConnection(){this.autoRefresh?this.connectSSE():this.refreshTasks()},connectSSE(){this.cleanup();try{this.eventSource=h(),this.eventSource.onopen=()=>{this.connectionStatus="connected",console.log("SSE\u8FDE\u63A5\u5DF2\u5EFA\u7ACB")},this.eventSource.onmessage=s=>{try{const e=JSON.parse(s.data);if(e.error){console.error("SSE\u9519\u8BEF:",e.error),this.connectionStatus="disconnected";return}this.tasks=e.tasks||[],this.lastUpdateTime=new Date().toLocaleTimeString(),this.connectionStatus="connected"}catch(e){console.error("\u89E3\u6790SSE\u6570\u636E\u5931\u8D25:",e)}},this.eventSource.onerror=s=>{console.error("SSE\u8FDE\u63A5\u9519\u8BEF:",s),this.connectionStatus="disconnected",setTimeout(()=>{this.autoRefresh&&this.connectSSE()},5e3)}}catch(s){console.error("\u521B\u5EFASSE\u8FDE\u63A5\u5931\u8D25:",s),this.connectionStatus="disconnected"}},async refreshTasks(){this.loading=!0;try{const s=await _();s.code===200&&(this.tasks=s.data||[],this.lastUpdateTime=new Date().toLocaleTimeString())}catch(s){console.error("\u83B7\u53D6\u4EFB\u52A1\u5217\u8868\u5931\u8D25:",s),this.$message.error("\u83B7\u53D6\u4EFB\u52A1\u5217\u8868\u5931\u8D25")}finally{this.loading=!1}},cleanup(){this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null)},getTaskCardClass(s){return{"task-running":s==="running","task-completed":s==="completed","task-failed":s==="failed","task-pending":s==="pending"}},getStatusType(s){return{pending:"info",running:"warning",completed:"success",failed:"danger"}[s]||"info"},getStatusText(s){return{pending:"\u7B49\u5F85\u4E2D",running:"\u8FD0\u884C\u4E2D",completed:"\u5DF2\u5B8C\u6210",failed:"\u5931\u8D25"}[s]||s},getProgressClass(s){switch(s){case"failed":return"progress-failed";case"completed":return"progress-completed";case"running":case"processing":return"progress-running";default:return"progress-default"}},getFileStatusType(s){switch(s){case"failed":case"error":return"danger";case"completed":case"success":return"success";case"processing":case"running":return"primary";case"pending":case"waiting":return"info";default:return"info"}},getFileStatusText(s){return{processing:"\u5904\u7406\u4E2D",running:"\u8FD0\u884C\u4E2D",pending:"\u7B49\u5F85\u4E2D",waiting:"\u7B49\u5F85\u4E2D",completed:"\u5DF2\u5B8C\u6210",success:"\u6210\u529F",failed:"\u5931\u8D25",error:"\u9519\u8BEF"}[s]||s},getFileProgressClass(s){switch(s){case"failed":case"error":return"progress-failed";case"completed":case"success":return"progress-completed";case"processing":case"running":return"progress-running";case"pending":default:return"progress-default"}},getFileName(s){return s.split("/").pop()||s},formatTime(s){return s?new Date(s).toLocaleString():""},formatParamValue(s){return typeof s=="object"?JSON.stringify(s):String(s)},getProjectName(s){return s.task_params&&s.task_params.project_name?s.task_params.project_name:`\u4EFB\u52A1 ${s.task_id.substring(0,8)}`},getTaskTypeText(s){if(s.task_params&&s.task_params.action){const e=s.task_params.action;return e==="\u9879\u76EE\u6863\u6848"?"\u9879\u76EE\u6863\u6848":e==="\u6587\u4E66\u6863\u6848"||e.includes("\u6587\u4E66")?"\u6587\u4E66\u6863\u6848":"\u9879\u76EE\u6863\u6848"}return"\u9879\u76EE\u6863\u6848"},getTaskTypeTagColor(s){const e=this.getTaskTypeText(s);return e==="\u9879\u76EE\u6863\u6848"?"primary":e==="\u6587\u4E66\u6863\u6848"?"success":"info"},getActionText(s){return s.task_params&&s.task_params.action?s.task_params.action:""},getFileItemClass(s){return{"file-processing":s==="processing","file-completed":s==="completed","file-failed":s==="failed"}},getFileIcon(s){return{processing:"el-icon-loading",completed:"el-icon-circle-check",failed:"el-icon-circle-close"}[s]||"el-icon-document"},getStageText(s){return{init:"\u521D\u59CB\u5316",download:"\u6587\u4EF6\u4E0B\u8F7D",validate:"\u6587\u4EF6\u9A8C\u8BC1",parse:"\u6587\u6863\u89E3\u6790",extract_basic:"\u57FA\u7840\u4FE1\u606F\u63D0\u53D6",extract_structure:"\u7ED3\u6784\u5316\u4FE1\u606F\u63D0\u53D6",extract_entities:"\u5B9E\u4F53\u4FE1\u606F\u63D0\u53D6",extract_relations:"\u5173\u7CFB\u4FE1\u606F\u63D0\u53D6",merge_info:"\u4FE1\u606F\u5408\u5E76",validate_result:"\u7ED3\u679C\u9A8C\u8BC1",store:"\u5B58\u50A8\u7ED3\u679C",complete:"\u5904\u7406\u5B8C\u6210"}[s]||s}}};var b=function(){var e=this,t=e._self._c;return t("div",{staticClass:"tasks-container"},[t("div",{staticClass:"header"},[e._m(0),t("div",{staticClass:"header-right"},[t("el-button",{staticClass:"back-button",attrs:{type:"text",icon:"el-icon-arrow-left"},on:{click:e.goBack}},[e._v(" \u8FD4\u56DE\u4E0A\u4E00\u9875 ")])],1)]),t("div",{staticClass:"connection-status"},[t("el-tag",{attrs:{type:e.connectionStatus==="connected"?"success":"danger",size:"small"}},[e._v(" "+e._s(e.connectionStatus==="connected"?"\u5B9E\u65F6\u8FDE\u63A5\u6B63\u5E38":"\u8FDE\u63A5\u65AD\u5F00")+" ")]),t("span",{staticClass:"last-update"},[e._v("\u6700\u540E\u66F4\u65B0: "+e._s(e.lastUpdateTime))])],1),t("div",{staticClass:"tasks-table"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.filteredTasks,"row-class-name":e.getTableRowClass,"row-key":"task_id"},on:{"expand-change":e.handleExpandChange}},[t("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(n){return[t("div",{staticClass:"task-details-expanded"},[n.row.file_progress&&Object.keys(n.row.file_progress).length>0?t("div",{staticClass:"file-progress-section"},[t("h4",[t("i",{staticClass:"el-icon-document"}),e._v(" \u6587\u4EF6\u5904\u7406\u8FDB\u5EA6 ("+e._s(Object.keys(n.row.file_progress).length)+" \u4E2A\u6587\u4EF6)")]),n.row.task_type==="hngpt_workflow"?t("div",{staticClass:"hngpt-file-progress"},e._l(n.row.file_progress,function(r,a){return t("div",{key:a,staticClass:"hngpt-file-item"},[t("div",{staticClass:"file-header"},[t("span",{staticClass:"file-name"},[e._v(e._s(e.getFileName(a)))]),t("span",{staticClass:"file-status",class:r.status},[e._v(e._s(e.getFileStatusText(r.status)))])]),t("div",{staticClass:"file-workflow-progress"},[t("div",{staticClass:"file-stage-segments"},e._l(e.getFileStages(r),function(i,o){return t("div",{key:o,staticClass:"file-stage-segment",class:e.getFileStageClass(i.status),attrs:{title:`${i.name}: ${i.status}`}},[t("div",{staticClass:"file-stage-fill",style:e.getFileStageFillStyle(i)})])}),0),t("div",{staticClass:"file-stage-text"},[e._v(e._s(e.getFileStageText(r)))])]),r.error?t("div",{staticClass:"file-error"},[t("i",{staticClass:"el-icon-warning"}),e._v(" "+e._s(r.error)+" ")]):e._e()])}),0):t("div",[t("el-table",{staticStyle:{"margin-top":"10px"},attrs:{data:e.getFileProgressData(n.row.file_progress),size:"small"}},[t("el-table-column",{attrs:{prop:"fileName",label:"\u6587\u4EF6\u540D","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",{staticClass:"file-name-cell"},[t("i",{class:e.getFileIcon(r.row.status)}),t("span",{attrs:{title:r.row.fullPath}},[e._v(e._s(r.row.fileName))])])]}}],null,!0)}),t("el-table-column",{attrs:{prop:"doc_id",label:"\u6587\u6863ID",width:"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.doc_id?t("div",{staticClass:"doc-id-cell"},[t("el-tooltip",{attrs:{content:r.row.doc_id,placement:"top"}},[t("span",{staticClass:"doc-id-text"},[e._v(e._s(r.row.doc_id.substring(0,8))+"...")])])],1):t("span",{staticClass:"doc-id-empty"},[e._v("-")])]}}],null,!0)}),t("el-table-column",{attrs:{prop:"status",label:"\u72B6\u6001",width:"90"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-tag",{attrs:{type:e.getFileStatusType(r.row.status),size:"mini",effect:"plain"}},[e._v(" "+e._s(e.getFileStatusText(r.row.status))+" ")])]}}],null,!0)}),t("el-table-column",{attrs:{prop:"progress",label:"\u8FDB\u5EA6",width:"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-progress",{class:e.getFileProgressClass(r.row.status),attrs:{percentage:Number(r.row.progress)||0,"stroke-width":6,"show-text":!0,"text-inside":!1}})]}}],null,!0)}),t("el-table-column",{attrs:{prop:"duration",label:"\u8017\u65F6",width:"80"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.duration!==void 0?t("div",{staticClass:"duration-cell"},[t("span",{staticClass:"duration-text"},[e._v(e._s(e.formatDuration(r.row.duration)))])]):t("span",{staticClass:"duration-empty"},[e._v("-")])]}}],null,!0)}),t("el-table-column",{attrs:{prop:"stage",label:"\u5904\u7406\u9636\u6BB5",width:"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.stage_detail?t("div",{staticClass:"stage-info"},[t("span",{staticClass:"stage-detail"},[e._v(e._s(r.row.stage_detail))])]):r.row.stage?t("div",{staticClass:"stage-info"},[t("span",{staticClass:"stage-name"},[e._v(e._s(e.getStageText(r.row.stage)))])]):t("span",{staticClass:"stage-empty"},[e._v("-")])]}}],null,!0)}),t("el-table-column",{attrs:{prop:"error",label:"\u9519\u8BEF\u4FE1\u606F","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.error?t("div",{staticClass:"error-cell"},[t("el-tooltip",{attrs:{content:r.row.error,placement:"top"}},[t("span",{staticClass:"error-text"},[e._v(e._s(r.row.error.substring(0,30))+e._s(r.row.error.length>30?"...":""))])])],1):t("span",[e._v("-")])]}}],null,!0)})],1)],1)]):t("div",{staticClass:"no-file-progress"},[t("h4",[t("i",{staticClass:"el-icon-document"}),e._v(" \u6587\u4EF6\u5904\u7406\u8FDB\u5EA6")]),t("el-alert",{attrs:{title:"\u6682\u65E0\u6587\u4EF6\u5904\u7406\u8FDB\u5EA6\u4FE1\u606F",type:"info",closable:!1,"show-icon":""}})],1),n.row.error?t("div",{staticClass:"error-info"},[t("h4",[t("i",{staticClass:"el-icon-warning"}),e._v(" \u9519\u8BEF\u4FE1\u606F")]),t("el-alert",{attrs:{title:n.row.error,type:"error",closable:!1,"show-icon":""}})],1):e._e()])]}}])}),t("el-table-column",{attrs:{prop:"project_name",label:"\u9879\u76EE\u540D\u79F0","min-width":"300"},scopedSlots:e._u([{key:"default",fn:function(n){return[t("div",{staticClass:"project-name-cell"},[t("strong",[e._v(e._s(e.getProjectName(n.row)))])])]}}])}),t("el-table-column",{attrs:{prop:"status",label:"\u72B6\u6001",width:"120"},scopedSlots:e._u([{key:"default",fn:function(n){return[t("el-tag",{attrs:{type:e.getStatusType(n.row.status),size:"small",effect:"dark"}},[e._v(" "+e._s(e.getStatusText(n.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"task_type",label:"\u7C7B\u578B",width:"120"},scopedSlots:e._u([{key:"default",fn:function(n){return[t("el-tag",{attrs:{type:e.getTaskTypeTagColor(n.row),size:"small"}},[e._v(" "+e._s(e.getTaskTypeText(n.row))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"progress",label:"\u8FDB\u5EA6",width:"175"},scopedSlots:e._u([{key:"default",fn:function(n){return[t("div",{staticClass:"progress-cell"},[n.row.task_type==="hngpt_workflow"||n.row.agent_progress&&Object.keys(n.row.agent_progress).length>0?t("div",{staticClass:"hngpt-progress-container"},[t("div",{staticClass:"unified-project-progress"},[t("el-progress",{class:e.getProgressClass(n.row.status),attrs:{percentage:e.getUnifiedProjectProgress(n.row),"stroke-width":6,"show-text":!0,"text-inside":!1}}),t("div",{staticClass:"unified-progress-info"},[e._v(" "+e._s(e.getUnifiedProgressInfo(n.row))+" ")])],1)]):t("div",{staticClass:"traditional-progress"},[t("el-progress",{class:e.getProgressClass(n.row.status),attrs:{percentage:e.getTraditionalProgress(n.row),"stroke-width":6,"show-text":!0,"text-inside":!1}}),t("div",{staticClass:"file-count-info"},[e._v(" "+e._s(e.getFileCount(n.row))+" ")])],1)])]}}])}),t("el-table-column",{attrs:{prop:"created_at",label:"\u521B\u5EFA\u65F6\u95F4",width:"160"},scopedSlots:e._u([{key:"default",fn:function(n){return[e._v(" "+e._s(e.formatTime(n.row.created_at))+" ")]}}])}),t("el-table-column",{attrs:{prop:"completed_at",label:"\u5B8C\u6210\u65F6\u95F4",width:"160"},scopedSlots:e._u([{key:"default",fn:function(n){return[(n.row.status==="completed"||n.row.status==="failed")&&n.row.updated_at?t("span",[e._v(" "+e._s(e.formatTime(n.row.updated_at))+" ")]):t("span",{staticClass:"completion-empty"},[e._v("-")])]}}])})],1),e.filteredTasks.length===0?t("div",{staticClass:"empty-tasks"},[t("el-empty",{attrs:{description:"\u6682\u65E0\u8FDB\u884C\u4E2D\u621624\u5C0F\u65F6\u5185\u5B8C\u6210\u7684\u4EFB\u52A1"}})],1):e._e()],1)])},k=[function(){var s=this,e=s._self._c;return e("div",{staticClass:"header-left"},[e("h2",[s._v("\u4EFB\u52A1\u7BA1\u7406")])])}],w=m(v,b,k,!1,null,"104150af",null,null);const x=w.exports;export{x as default};
