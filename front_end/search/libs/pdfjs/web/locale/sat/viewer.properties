# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ᱢᱟᱲᱟᱝ ᱥᱟᱦᱴᱟ
previous_label=ᱢᱟᱲᱟᱝᱟᱜ
next.title=ᱤᱱᱟᱹ ᱛᱟᱭᱚᱢ ᱥᱟᱦᱴᱟ
next_label=ᱤᱱᱟᱹ ᱛᱟᱭᱚᱢ

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=ᱥᱟᱦᱴᱟ
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=ᱨᱮᱭᱟᱜ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} ᱠᱷᱚᱱ {{pagesCount}})

zoom_out.title=ᱦᱤᱲᱤᱧ ᱛᱮᱭᱟᱨ
zoom_out_label=ᱦᱤᱲᱤᱧ ᱛᱮᱭᱟᱨ
zoom_in.title=ᱢᱟᱨᱟᱝ ᱛᱮᱭᱟᱨ
zoom_in_label=ᱢᱟᱨᱟᱝ ᱛᱮᱭᱟᱨ
zoom.title=ᱡᱩᱢ
presentation_mode.title=ᱩᱫᱩᱜ ᱥᱚᱫᱚᱨ ᱚᱵᱚᱥᱛᱟ ᱨᱮ ᱚᱛᱟᱭ ᱢᱮ
presentation_mode_label=ᱩᱫᱩᱜ ᱥᱚᱫᱚᱨ ᱚᱵᱚᱥᱛᱟ ᱨᱮ
open_file.title=ᱨᱮᱫ ᱡᱷᱤᱡᱽ ᱢᱮ
open_file_label=ᱡᱷᱤᱡᱽ ᱢᱮ
print.title=ᱪᱷᱟᱯᱟ
print_label=ᱪᱷᱟᱯᱟ
save.title=ᱥᱟᱺᱪᱟᱣ ᱢᱮ
save_label=ᱥᱟᱺᱪᱟᱣ ᱢᱮ
bookmark1.title=ᱱᱤᱛᱚᱜᱟᱜ ᱥᱟᱦᱴᱟ (ᱱᱤᱛᱚᱜᱟᱜ ᱥᱟᱦᱴᱟ ᱠᱷᱚᱱ URL ᱫᱮᱠᱷᱟᱣ ᱢᱮ)
bookmark1_label=ᱱᱤᱛᱚᱜᱟᱜ ᱥᱟᱦᱴᱟ
# LOCALIZATION NOTE (open_in_app.title): This string is used in Firefox for Android.
open_in_app.title=ᱮᱯ ᱨᱮ ᱡᱷᱤᱡᱽ ᱢᱮ
# LOCALIZATION NOTE (open_in_app_label): This string is used in Firefox for Android. Length of the translation matters since we are in a mobile context, with limited screen estate.
open_in_app_label=ᱮᱯ ᱨᱮ ᱡᱷᱤᱡᱽ ᱢᱮ

# Secondary toolbar and context menu
tools.title=ᱦᱟᱹᱛᱤᱭᱟᱹᱨ ᱠᱚ
tools_label=ᱦᱟᱹᱛᱤᱭᱟᱹᱨ ᱠᱚ
first_page.title=ᱯᱩᱭᱞᱩ ᱥᱟᱦᱴᱟ ᱥᱮᱫ ᱪᱟᱞᱟᱜ ᱢᱮ
first_page_label=ᱯᱩᱭᱞᱩ ᱥᱟᱦᱴᱟ ᱥᱮᱫ ᱪᱟᱞᱟᱜ ᱢᱮ
last_page.title=ᱢᱩᱪᱟᱹᱫ ᱥᱟᱦᱴᱟ ᱥᱮᱫ ᱪᱟᱞᱟᱜ ᱢᱮ
last_page_label=ᱢᱩᱪᱟᱹᱫ ᱥᱟᱦᱴᱟ ᱥᱮᱫ ᱪᱟᱞᱟᱜ ᱢᱮ
page_rotate_cw.title=ᱜᱷᱚᱰᱤ ᱦᱤᱥᱟᱹᱵ ᱛᱮ ᱟᱹᱪᱩᱨ
page_rotate_cw_label=ᱜᱷᱚᱰᱤ ᱦᱤᱥᱟᱹᱵ ᱛᱮ ᱟᱹᱪᱩᱨ
page_rotate_ccw.title=ᱜᱷᱚᱰᱤ ᱦᱤᱥᱟᱹᱵ ᱛᱮ ᱩᱞᱴᱟᱹ ᱟᱹᱪᱩᱨ
page_rotate_ccw_label=ᱜᱷᱚᱰᱤ ᱦᱤᱥᱟᱹᱵ ᱛᱮ ᱩᱞᱴᱟᱹ ᱟᱹᱪᱩᱨ

cursor_text_select_tool.title=ᱚᱞ ᱵᱟᱪᱷᱟᱣ ᱦᱟᱹᱛᱤᱭᱟᱨ ᱮᱢ ᱪᱷᱚᱭ ᱢᱮ
cursor_text_select_tool_label=ᱚᱞ ᱵᱟᱪᱷᱟᱣ ᱦᱟᱹᱛᱤᱭᱟᱨ
cursor_hand_tool.title=ᱛᱤ ᱦᱟᱹᱛᱤᱭᱟᱨ ᱮᱢ ᱪᱷᱚᱭ ᱢᱮ
cursor_hand_tool_label=ᱛᱤ ᱦᱟᱹᱛᱤᱭᱟᱨ

scroll_page.title=ᱥᱟᱦᱴᱟ ᱜᱩᱲᱟᱹᱣ ᱵᱮᱵᱷᱟᱨ ᱢᱮ
scroll_page_label=ᱥᱟᱦᱴᱟ ᱜᱩᱲᱟᱹᱣ
scroll_vertical.title=ᱥᱤᱫᱽ ᱜᱩᱲᱟᱹᱣ ᱵᱮᱵᱷᱟᱨ ᱢᱮ
scroll_vertical_label=ᱥᱤᱫᱽ ᱜᱩᱲᱟᱹᱣ
scroll_horizontal.title=ᱜᱤᱛᱤᱡ ᱛᱮ ᱜᱩᱲᱟᱹᱣ ᱵᱮᱵᱷᱟᱨ ᱢᱮ
scroll_horizontal_label=ᱜᱤᱛᱤᱡ ᱛᱮ ᱜᱩᱲᱟᱹᱣ
scroll_wrapped.title=ᱞᱤᱯᱴᱟᱹᱣ ᱜᱩᱰᱨᱟᱹᱣ ᱵᱮᱵᱷᱟᱨ ᱢᱮ
scroll_wrapped_label=ᱞᱤᱯᱴᱟᱣ ᱜᱩᱰᱨᱟᱹᱣ

spread_none.title=ᱟᱞᱚᱢ ᱡᱚᱲᱟᱣ ᱟ ᱥᱟᱦᱴᱟ ᱫᱚ ᱯᱟᱥᱱᱟᱣᱜᱼᱟ
spread_none_label=ᱯᱟᱥᱱᱟᱣ ᱵᱟᱹᱱᱩᱜᱼᱟ
spread_odd.title=ᱥᱟᱦᱴᱟ ᱯᱟᱥᱱᱟᱣ ᱡᱚᱲᱟᱣ ᱢᱮ ᱡᱟᱦᱟᱸ ᱫᱚ ᱚᱰᱼᱮᱞ ᱥᱟᱦᱴᱟᱠᱚ ᱥᱟᱞᱟᱜ ᱮᱛᱦᱚᱵᱚᱜ ᱠᱟᱱᱟ
spread_odd_label=ᱚᱰ ᱯᱟᱥᱱᱟᱣ
spread_even.title=ᱥᱟᱦᱴᱟ ᱯᱟᱥᱱᱟᱣ ᱡᱚᱲᱟᱣ ᱢᱮ ᱡᱟᱦᱟᱸ ᱫᱚ ᱤᱣᱮᱱᱼᱮᱞ ᱥᱟᱦᱴᱟᱠᱚ ᱥᱟᱞᱟᱜ ᱮᱛᱦᱚᱵᱚᱜ ᱠᱟᱱᱟ
spread_even_label=ᱯᱟᱥᱱᱟᱣ ᱤᱣᱮᱱ

# Document properties dialog box
document_properties.title=ᱫᱚᱞᱤᱞ ᱜᱩᱱᱠᱚ …
document_properties_label=ᱫᱚᱞᱤᱞ ᱜᱩᱱᱠᱚ …
document_properties_file_name=ᱨᱮᱫᱽ ᱧᱩᱛᱩᱢ :
document_properties_file_size=ᱨᱮᱫᱽ ᱢᱟᱯ :
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} ᱵᱟᱭᱤᱴ ᱠᱚ)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} ᱵᱟᱭᱤᱴ ᱠᱚ)
document_properties_title=ᱧᱩᱛᱩᱢ :
document_properties_author=ᱚᱱᱚᱞᱤᱭᱟᱹ :
document_properties_subject=ᱵᱤᱥᱚᱭ :
document_properties_keywords=ᱠᱟᱹᱴᱷᱤ ᱥᱟᱵᱟᱫᱽ :
document_properties_creation_date=ᱛᱮᱭᱟᱨ ᱢᱟᱸᱦᱤᱛ :
document_properties_modification_date=ᱵᱚᱫᱚᱞ ᱦᱚᱪᱚ ᱢᱟᱹᱦᱤᱛ :
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=ᱵᱮᱱᱟᱣᱤᱡ :
document_properties_producer=PDF ᱛᱮᱭᱟᱨ ᱚᱰᱚᱠᱤᱡ :
document_properties_version=PDF ᱵᱷᱟᱹᱨᱥᱚᱱ :
document_properties_page_count=ᱥᱟᱦᱴᱟ ᱞᱮᱠᱷᱟ :
document_properties_page_size=ᱥᱟᱦᱴᱟ ᱢᱟᱯ :
document_properties_page_size_unit_inches=ᱤᱧᱪ
document_properties_page_size_unit_millimeters=ᱢᱤᱢᱤ
document_properties_page_size_orientation_portrait=ᱯᱚᱴᱨᱮᱴ
document_properties_page_size_orientation_landscape=ᱞᱮᱱᱰᱥᱠᱮᱯ
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ᱪᱤᱴᱷᱤ
document_properties_page_size_name_legal=ᱠᱟᱹᱱᱩᱱᱤ
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=ᱞᱚᱜᱚᱱ ᱣᱮᱵᱽ ᱧᱮᱞ :
document_properties_linearized_yes=ᱦᱚᱭ
document_properties_linearized_no=ᱵᱟᱝ
document_properties_close=ᱵᱚᱸᱫᱚᱭ ᱢᱮ

print_progress_message=ᱪᱷᱟᱯᱟ ᱞᱟᱹᱜᱤᱫ ᱫᱚᱞᱤᱞ ᱛᱮᱭᱟᱨᱚᱜ ᱠᱟᱱᱟ …
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ᱵᱟᱹᱰᱨᱟᱹ

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=ᱫᱷᱟᱨᱮᱵᱟᱨ ᱥᱮᱫ ᱩᱪᱟᱹᱲᱚᱜ ᱢᱮ
toggle_sidebar_notification2.title=ᱫᱷᱟᱨᱮᱵᱟᱨ ᱥᱮᱫ ᱩᱪᱟᱹᱲᱚᱜ ᱢᱮ  (ᱫᱚᱞᱤᱞ ᱨᱮ ᱟᱣᱴᱞᱟᱭᱤᱢ ᱢᱮᱱᱟᱜᱼᱟ/ᱞᱟᱪᱷᱟᱠᱚ/ᱯᱚᱨᱚᱛᱠᱚ)
toggle_sidebar_label=ᱫᱷᱟᱨᱮᱵᱟᱨ ᱥᱮᱫ ᱩᱪᱟᱹᱲᱚᱜ ᱢᱮ
document_outline.title=ᱫᱚᱞᱚᱞ ᱟᱣᱴᱞᱟᱭᱤᱱ ᱫᱮᱠᱷᱟᱣ ᱢᱮ (ᱡᱷᱚᱛᱚ ᱡᱤᱱᱤᱥᱠᱚ ᱵᱟᱨ ᱡᱮᱠᱷᱟ ᱚᱛᱟ ᱠᱮᱛᱮ ᱡᱷᱟᱹᱞ/ᱦᱩᱰᱤᱧ ᱪᱷᱚᱭ ᱢᱮ)
document_outline_label=ᱫᱚᱞᱤᱞ ᱛᱮᱭᱟᱨ ᱛᱮᱫ
attachments.title=ᱞᱟᱴᱷᱟ ᱥᱮᱞᱮᱫ ᱠᱚ ᱩᱫᱩᱜᱽ ᱢᱮ
attachments_label=ᱞᱟᱴᱷᱟ ᱥᱮᱞᱮᱫ ᱠᱚ
layers.title=ᱯᱚᱨᱚᱛ ᱫᱮᱠᱷᱟᱣ ᱢᱮ (ᱢᱩᱞ ᱡᱟᱭᱜᱟ ᱛᱮ ᱡᱷᱚᱛᱚ ᱯᱚᱨᱚᱛᱠᱚ ᱨᱤᱥᱮᱴ ᱞᱟᱹᱜᱤᱫ ᱵᱟᱨ ᱡᱮᱠᱷᱟ ᱚᱛᱚᱭ ᱢᱮ)
layers_label=ᱯᱚᱨᱚᱛᱠᱚ
thumbs.title=ᱪᱤᱛᱟᱹᱨ ᱟᱦᱞᱟ ᱠᱚ ᱩᱫᱩᱜᱽ ᱢᱮ
thumbs_label=ᱪᱤᱛᱟᱹᱨ ᱟᱦᱞᱟ ᱠᱚ
current_outline_item.title=ᱱᱤᱛᱚᱜᱟᱜ ᱟᱣᱴᱞᱟᱭᱤᱱ ᱡᱟᱱᱤᱥ ᱯᱟᱱᱛᱮ ᱢᱮ
current_outline_item_label=ᱱᱤᱛᱚᱜᱟᱜ ᱟᱣᱴᱞᱟᱭᱤᱱ ᱡᱟᱱᱤᱥ
findbar.title=ᱫᱚᱞᱤᱞ ᱨᱮ ᱯᱟᱱᱛᱮ
findbar_label=ᱥᱮᱸᱫᱽᱨᱟᱭ ᱢᱮ

additional_layers=ᱵᱟᱹᱲᱛᱤ ᱯᱚᱨᱚᱛᱠᱚ
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark={{page}} ᱥᱟᱦᱴᱟ
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title={{page}} ᱥᱟᱦᱴᱟ
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}} ᱥᱟᱦᱴᱟ ᱨᱮᱭᱟᱜ ᱪᱤᱛᱟᱹᱨ ᱟᱦᱞᱟ

# Find panel button title and messages
find_input.title=ᱥᱮᱸᱫᱽᱨᱟᱭ ᱢᱮ
find_input.placeholder=ᱫᱚᱞᱤᱞ ᱨᱮ ᱯᱟᱱᱛᱮ ᱢᱮ …
find_previous.title=ᱟᱭᱟᱛ ᱦᱤᱸᱥ ᱨᱮᱭᱟᱜ ᱯᱟᱹᱦᱤᱞ ᱥᱮᱫᱟᱜ ᱚᱰᱚᱠ ᱧᱟᱢ ᱢᱮ
find_previous_label=ᱢᱟᱲᱟᱝᱟᱜ
find_next.title=ᱟᱭᱟᱛ ᱦᱤᱸᱥ ᱨᱮᱭᱟᱜ ᱤᱱᱟᱹ ᱛᱟᱭᱚᱢ ᱚᱰᱚᱠ ᱧᱟᱢ ᱢᱮ
find_next_label=ᱤᱱᱟᱹ ᱛᱟᱭᱚᱢ
find_highlight=ᱡᱷᱚᱛᱚ ᱩᱫᱩᱜ ᱨᱟᱠᱟᱵ
find_match_case_label=ᱡᱚᱲ ᱠᱟᱛᱷᱟ
find_match_diacritics_label=ᱵᱤᱥᱮᱥᱚᱠ ᱠᱚ ᱢᱮᱲᱟᱣ ᱢᱮ
find_entire_word_label=ᱡᱷᱚᱛᱚ ᱟᱹᱲᱟᱹᱠᱚ
find_reached_top=ᱫᱚᱞᱤᱞ ᱨᱮᱭᱟᱜ ᱪᱤᱴ ᱨᱮ ᱥᱮᱴᱮᱨ, ᱞᱟᱛᱟᱨ ᱠᱷᱚᱱ ᱞᱮᱛᱟᱲ
find_reached_bottom=ᱫᱚᱞᱤᱞ ᱨᱮᱭᱟᱜ ᱢᱩᱪᱟᱹᱫ ᱨᱮ ᱥᱮᱴᱮᱨ, ᱪᱚᱴ ᱠᱷᱚᱱ ᱞᱮᱛᱟᱲ
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} ᱨᱮᱭᱟᱜ {{total}} ᱡᱚᱲ
find_match_count[two]={{current}} ᱨᱮᱭᱟᱜ {{total}} ᱡᱚᱲᱠᱚ
find_match_count[few]={{current}} ᱨᱮᱭᱟᱜ {{total}} ᱡᱚᱲᱠᱚ
find_match_count[many]={{current}} ᱨᱮᱭᱟᱜ {{total}} ᱡᱚᱲᱠᱚ
find_match_count[other]={{current}} ᱨᱮᱭᱟᱜ {{total}} ᱡᱚᱲᱠᱚ
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]={{limit}} ᱡᱚᱲ ᱠᱚ ᱠᱷᱚᱱ ᱰᱷᱮᱨ
find_match_count_limit[one]={{limit}} ᱡᱚᱲ ᱠᱷᱚᱱ ᱰᱷᱮᱨ
find_match_count_limit[two]={{limit}} ᱡᱚᱲ ᱠᱚ ᱠᱷᱚᱱ ᱰᱷᱮᱨ
find_match_count_limit[few]={{limit}} ᱡᱚᱲ ᱠᱚ ᱠᱷᱚᱱ ᱰᱷᱮᱨ
find_match_count_limit[many]={{limit}} ᱡᱚᱲ ᱠᱚ ᱠᱷᱚᱱ ᱰᱷᱮᱨ
find_match_count_limit[other]={{limit}} ᱡᱚᱲ ᱠᱚ ᱠᱷᱚᱱ ᱰᱷᱮᱨ
find_not_found=ᱛᱚᱯᱚᱞ ᱫᱚᱱᱚᱲ ᱵᱟᱝ ᱧᱟᱢ ᱞᱮᱱᱟ

# Predefined zoom values
page_scale_width=ᱥᱟᱦᱴᱟ ᱚᱥᱟᱨ
page_scale_fit=ᱥᱟᱦᱴᱟ ᱠᱷᱟᱯ
page_scale_auto=ᱟᱡᱼᱟᱡ ᱛᱮ ᱦᱩᱰᱤᱧ ᱞᱟᱹᱴᱩ ᱛᱮᱭᱟᱨ
page_scale_actual=ᱴᱷᱤᱠ ᱢᱟᱨᱟᱝ ᱛᱮᱫ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error=PDF ᱞᱟᱫᱮ ᱡᱚᱦᱚᱜ ᱢᱤᱫ ᱵᱷᱩᱞ ᱦᱩᱭ ᱮᱱᱟ ᱾
invalid_file_error=ᱵᱟᱝ ᱵᱟᱛᱟᱣ ᱟᱨᱵᱟᱝᱠᱷᱟᱱ ᱰᱤᱜᱟᱹᱣ PDF ᱨᱮᱫᱽ ᱾
missing_file_error=ᱟᱫᱟᱜ PDF ᱨᱮᱫᱽ ᱾
unexpected_response_error=ᱵᱟᱝᱵᱩᱡᱷ ᱥᱚᱨᱵᱷᱚᱨ ᱛᱮᱞᱟ ᱾
rendering_error=ᱥᱟᱦᱴᱟ ᱮᱢ ᱡᱚᱦᱚᱠ ᱢᱤᱫ ᱵᱷᱩᱞ ᱦᱩᱭ ᱮᱱᱟ ᱾

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} ᱢᱚᱱᱛᱚ ᱮᱢ]
password_label=ᱱᱚᱶᱟ PDF ᱨᱮᱫᱽ ᱡᱷᱤᱡᱽ ᱞᱟᱹᱜᱤᱫ ᱫᱟᱱᱟᱝ ᱥᱟᱵᱟᱫᱽ ᱟᱫᱮᱨ ᱢᱮ ᱾
password_invalid=ᱵᱷᱩᱞ ᱫᱟᱱᱟᱝ ᱥᱟᱵᱟᱫᱽ ᱾ ᱫᱟᱭᱟᱠᱟᱛᱮ ᱫᱩᱦᱲᱟᱹ ᱪᱮᱥᱴᱟᱭ ᱢᱮ ᱾
password_ok=ᱴᱷᱤᱠ
password_cancel=ᱵᱟᱹᱰᱨᱟᱹ

printing_not_supported=ᱦᱚᱥᱤᱭᱟᱨ : ᱪᱷᱟᱯᱟ ᱱᱚᱣᱟ ᱯᱟᱱᱛᱮᱭᱟᱜ ᱫᱟᱨᱟᱭ ᱛᱮ ᱯᱩᱨᱟᱹᱣ ᱵᱟᱭ ᱜᱚᱲᱚᱣᱟᱠᱟᱱᱟ ᱾
printing_not_ready=ᱦᱩᱥᱤᱭᱟᱹᱨ : ᱪᱷᱟᱯᱟ ᱞᱟᱹᱜᱤᱫ PDF ᱯᱩᱨᱟᱹ ᱵᱟᱭ ᱞᱟᱫᱮ ᱟᱠᱟᱱᱟ ᱾
web_fonts_disabled=ᱣᱮᱵᱽ ᱪᱤᱠᱤ ᱵᱟᱝ ᱦᱩᱭ ᱦᱚᱪᱚ ᱠᱟᱱᱟ : ᱵᱷᱤᱛᱤᱨ ᱛᱷᱟᱯᱚᱱ PDF ᱪᱤᱠᱤ ᱵᱮᱵᱷᱟᱨ ᱵᱟᱝ ᱦᱩᱭ ᱠᱮᱭᱟ ᱾

# Editor
editor_free_text2.title=ᱚᱞ
editor_free_text2_label=ᱚᱞ
editor_ink2.title=ᱛᱮᱭᱟᱨ
editor_ink2_label=ᱛᱮᱭᱟᱨ

editor_stamp.title=ᱢᱤᱫᱴᱟᱹᱝ ᱪᱤᱛᱟᱹᱨ ᱥᱮᱞᱮᱫ ᱢᱮ
editor_stamp_label=ᱢᱤᱫᱴᱟᱹᱝ ᱪᱤᱛᱟᱹᱨ ᱥᱮᱞᱮᱫ ᱢᱮ

free_text2_default_content=ᱚᱞ ᱮᱛᱦᱚᱵ ᱢᱮ …

# Editor Parameters
editor_free_text_color=ᱨᱚᱝ
editor_free_text_size=ᱢᱟᱯ
editor_ink_color=ᱨᱚᱝ
editor_ink_thickness=ᱢᱚᱴᱟ
editor_ink_opacity=ᱟᱨᱯᱟᱨ

# Editor aria
editor_free_text2_aria_label=ᱚᱞ ᱥᱟᱯᱲᱟᱣᱤᱭᱟᱹ
editor_ink2_aria_label=ᱛᱮᱭᱟᱨ ᱥᱟᱯᱲᱟᱣᱤᱭᱟᱹ
editor_ink_canvas_aria_label=ᱵᱮᱵᱷᱟᱨᱤᱭᱟᱹ ᱛᱮᱭᱟᱨ ᱠᱟᱫ ᱪᱤᱛᱟᱹᱨ
