var WatermarkPlus=function(t){"use strict";var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},e(t,i)};var i=function(){return i=Object.assign||function(t){for(var e,i=1,a=arguments.length;i<a;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},i.apply(this,arguments)};function a(t,e,i,a){return new(i||(i=Promise))((function(n,o){function r(t){try{l(a.next(t))}catch(t){o(t)}}function s(t){try{l(a.throw(t))}catch(t){o(t)}}function l(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(r,s)}l((a=a.apply(t,e||[])).next())}))}function n(t,e){var i,a,n,o,r={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(l){return function(s){if(i)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(r=0)),r;)try{if(i=1,a&&(n=2&s[0]?a.return:s[0]?a.throw||((n=a.return)&&n.call(a),0):a.next)&&!(n=n.call(a,s[1])).done)return n;switch(a=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,a=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(!(n=r.trys,(n=n.length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){r=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){r.label=s[1];break}if(6===s[0]&&r.label<n[1]){r.label=n[1],n=s;break}if(n&&r.label<n[2]){r.label=n[2],r.ops.push(s);break}n[2]&&r.ops.pop(),r.trys.pop();continue}s=e.call(t,r)}catch(t){s=[6,t],a=0}finally{i=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}var o=function(t){return t.toDataURL("image/png",1)},r=function(t){return"function"==typeof t},s=function(t){return void 0===t},l=function(t,e,i){void 0===e&&(e={}),void 0===i&&(i="http://www.w3.org/2000/svg");var a=document.createElementNS(i,t);for(var n in e)a.setAttribute(n,e[n]);return a},d=function(t,e){return s(t)?e:t},c=function(t,e,i){void 0===e&&(e=void 0),void 0===i&&(i=void 0);var a=new Image;return a.setAttribute("crossOrigin","Anonymous"),!s(e)&&(a.width=e),!s(i)&&(a.height=i),a.src=t,new Promise((function(t){a.onload=function(){t(a)}}))},h={width:300,height:300,rotate:45,layout:"default",auxiliaryLine:!1,translatePlacement:"middle",contentType:"text",content:"hello watermark-js-plus",textType:"fill",imageWidth:0,imageHeight:0,lineHeight:30,zIndex:2147483647,backgroundPosition:"0 0",backgroundRepeat:"repeat",fontSize:"20px",fontFamily:"sans-serif",fontStyle:"",fontVariant:"",fontColor:"#000",fontWeight:"normal",filter:"none",globalAlpha:.5,mode:"default",mutationObserve:!0,movable:!1,parent:"body",onSuccess:function(){},onBeforeDestroy:function(){},onDestroyed:function(){},onObserveError:function(){}},v=function(){function t(e,i){this.props=e,this.options=i,this.canvas=t.createCanvas(this.options.width,this.options.height),this.recommendOptions=function(t,e,i){var a=t.getContext("2d");if(null===a)throw new Error("get context error");a.font="".concat(e.fontStyle," ").concat(e.fontVariant," ").concat(e.fontWeight," ").concat(e.fontSize," ").concat(e.fontFamily),a.filter=e.filter,(null==e?void 0:e.rotate)&&(e.rotate=(360-e.rotate%360)*(Math.PI/180)),s(i.textRowMaxWidth)&&(e.textRowMaxWidth=e.width);var n={image:{rect:{width:e.imageWidth,height:e.imageHeight},position:{x:0,y:0}},textLine:{data:[],yOffsetValue:0},advancedStyleParams:{linear:{x0:0,x1:0},radial:{x0:0,y0:0,r0:0,x1:0,y1:0,r1:0},conic:{x:0,y:0,startAngle:0},pattern:{}}};switch(e.contentType){case"text":n.textLine.data=[e.content];break;case"multi-line-text":n.textLine.data=function(t,e,i){for(var a=[],n="",o=0,r=e.length;o<r;o++)n+=e.charAt(o),t.measureText(n).width>i&&(a.push(n.substring(0,n.length-1)),n="",o--);return a.push(n),a}(a,e.content,e.textRowMaxWidth)}var o=e.width/2,r=e.height/2,l="middle",d="center";switch(s(null==i?void 0:i.translateX)||s(null==i?void 0:i.translateY)?(n.advancedStyleParams.linear.x0=-e.width/2,n.advancedStyleParams.linear.x1=e.width/2,n.advancedStyleParams.radial.r0=0,n.advancedStyleParams.radial.r1=e.width/2):(o=null==i?void 0:i.translateX,r=null==i?void 0:i.translateY,l="top",d="left"),i.translatePlacement){case"top":o=e.width/2,r=0,l="top",n.advancedStyleParams.linear.x0=-e.width/2,n.advancedStyleParams.linear.x1=e.width/2,n.advancedStyleParams.radial.y0=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.y1=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.y=n.textLine.data.length*e.lineHeight/2;break;case"top-start":o=0,r=0,l="top",d="start",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=e.width,n.advancedStyleParams.radial.x0=e.width/2,n.advancedStyleParams.radial.y0=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.x1=e.width/2,n.advancedStyleParams.radial.y1=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.x=e.width/2,n.advancedStyleParams.conic.y=n.textLine.data.length*e.lineHeight/2;break;case"top-end":o=e.width,r=0,l="top",d="end",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=-e.width,n.advancedStyleParams.radial.x0=-e.width/2,n.advancedStyleParams.radial.y0=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.x1=-e.width/2,n.advancedStyleParams.radial.y1=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.x=-e.width/2,n.advancedStyleParams.conic.y=n.textLine.data.length*e.lineHeight/2;break;case"bottom":o=e.width/2,r=e.height,l="bottom",n.advancedStyleParams.linear.x0=-e.width/2,n.advancedStyleParams.linear.x1=e.width/2,n.advancedStyleParams.radial.y0=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.y1=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.x=0,n.advancedStyleParams.conic.y=-n.textLine.data.length*e.lineHeight/2;break;case"bottom-start":o=0,r=e.height,l="bottom",d="start",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=e.width,n.advancedStyleParams.radial.x0=e.width/2,n.advancedStyleParams.radial.y0=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.x1=e.width/2,n.advancedStyleParams.radial.y1=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.x=e.width/2,n.advancedStyleParams.conic.y=-n.textLine.data.length*e.lineHeight/2;break;case"bottom-end":o=e.width,r=e.height,l="bottom",d="end",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=-e.width,n.advancedStyleParams.radial.x0=-e.width/2,n.advancedStyleParams.radial.y0=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.x1=-e.width/2,n.advancedStyleParams.radial.y1=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.x=-e.width/2,n.advancedStyleParams.conic.y=-n.textLine.data.length*e.lineHeight/2;break;case"left":o=0,r=e.height/2,d="start",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=e.width,n.advancedStyleParams.radial.x0=e.width/2,n.advancedStyleParams.radial.x1=e.width/2,n.advancedStyleParams.conic.x=e.width/2,n.advancedStyleParams.conic.y=0;break;case"right":o=e.width,r=e.height/2,d="end",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=-e.width,n.advancedStyleParams.radial.x0=-e.width/2,n.advancedStyleParams.radial.x1=-e.width/2,n.advancedStyleParams.conic.x=-e.width/2,n.advancedStyleParams.conic.y=0}if(e.translateX=o,e.translateY=r,s(null==i?void 0:i.textBaseline)&&(e.textBaseline=l),s(null==i?void 0:i.textAlign)&&(e.textAlign=d),["text","multi-line-text"].includes(e.contentType))switch(e.textBaseline){case"middle":n.textLine.yOffsetValue=(n.textLine.data.length-1)*e.lineHeight/2;break;case"bottom":case"alphabetic":case"ideographic":n.textLine.yOffsetValue=(n.textLine.data.length-1)*e.lineHeight+(e.lineHeight-parseInt(e.fontSize))/2;break;case"top":case"hanging":n.textLine.yOffsetValue=-e.lineHeight/2+parseInt(e.fontSize)/2}return n}(this.canvas,this.options,this.props)}return t.createCanvas=function(t,e){var i,a=window.devicePixelRatio||1,n=document.createElement("canvas");return n.width=t*a,n.height=e*a,n.style.width="".concat(t,"px"),n.style.height="".concat(e,"px"),null===(i=n.getContext("2d"))||void 0===i||i.setTransform(a,0,0,a,0,0),n},t.clearCanvas=function(t){var e=t.getContext("2d");if(null===e)throw new Error("get context error");e.restore(),e.clearRect(0,0,t.width,t.height)},t.prototype.getCanvas=function(){return this.canvas},t.prototype.clear=function(){t.clearCanvas(this.canvas)},t.prototype.draw=function(){var t=this,e=this.canvas.getContext("2d");if(null===e)throw new Error("get context error");return this.options.auxiliaryLine&&(e.beginPath(),e.rect(0,0,this.options.width,this.options.height),e.lineWidth=1,e.strokeStyle="#000",e.stroke(),e.closePath(),e.beginPath(),e.rect(this.options.translateX,this.options.translateY,1,1),e.lineWidth=1,e.strokeStyle="#f00",e.stroke(),e.closePath()),this.setStyle(e),e.save(),e.translate(this.options.translateX,this.options.translateY),e.rotate(this.options.rotate),new Promise((function(i){switch(t.options.contentType){case"text":t.drawText(e,i);break;case"image":t.drawImage(e,i);break;case"multi-line-text":t.drawMultiLineText(e,i);break;case"rich-text":t.drawRichText(e,i)}}))},t.prototype.setStyle=function(t){var e,i="fillStyle";"stroke"===this.options.textType&&(i="strokeStyle");var a=this.options.fontColor;if(null===(e=this.options)||void 0===e?void 0:e.advancedStyle)switch(this.options.advancedStyle.type){case"linear":a=this.createLinearGradient(t);break;case"radial":a=this.createRadialGradient(t);break;case"conic":a=this.createConicGradient(t);break;case"pattern":a=this.createPattern(t)}t[i]&&a&&(t[i]=a),this.options.textAlign&&(t.textAlign=this.options.textAlign),this.options.textBaseline&&(t.textBaseline=this.options.textBaseline),t.globalAlpha=this.options.globalAlpha,this.options.shadowStyle&&(t.shadowBlur=d(this.options.shadowStyle.shadowBlur,0),t.shadowColor=d(this.options.shadowStyle.shadowColor,"#00000000"),t.shadowOffsetX=d(this.options.shadowStyle.shadowOffsetX,0),t.shadowOffsetY=d(this.options.shadowStyle.shadowOffsetY,0)),r(this.options.extraDrawFunc)&&this.options.extraDrawFunc(t)},t.prototype.createLinearGradient=function(t){var e,i,a,n,o,r,s,l,c,h,v,u,p,m,y,g=t.createLinearGradient(d(null===(a=null===(i=null===(e=this.options.advancedStyle)||void 0===e?void 0:e.params)||void 0===i?void 0:i.linear)||void 0===a?void 0:a.x0,this.recommendOptions.advancedStyleParams.linear.x0),d(null===(r=null===(o=null===(n=this.options.advancedStyle)||void 0===n?void 0:n.params)||void 0===o?void 0:o.linear)||void 0===r?void 0:r.y0,0),d(null===(c=null===(l=null===(s=this.options.advancedStyle)||void 0===s?void 0:s.params)||void 0===l?void 0:l.linear)||void 0===c?void 0:c.x1,this.recommendOptions.advancedStyleParams.linear.x1),d(null===(u=null===(v=null===(h=this.options.advancedStyle)||void 0===h?void 0:h.params)||void 0===v?void 0:v.linear)||void 0===u?void 0:u.y1,0));return null===(y=null===(m=null===(p=this.options)||void 0===p?void 0:p.advancedStyle)||void 0===m?void 0:m.colorStops)||void 0===y||y.forEach((function(t){g.addColorStop(t.offset,t.color)})),g},t.prototype.createConicGradient=function(t){var e,i,a,n,o,r,s,l,c,h,v,u,p,m,y,g=t.createConicGradient(d(null===(n=null===(a=null===(i=null===(e=this.options)||void 0===e?void 0:e.advancedStyle)||void 0===i?void 0:i.params)||void 0===a?void 0:a.conic)||void 0===n?void 0:n.startAngle,0),d(null===(l=null===(s=null===(r=null===(o=this.options)||void 0===o?void 0:o.advancedStyle)||void 0===r?void 0:r.params)||void 0===s?void 0:s.conic)||void 0===l?void 0:l.x,this.recommendOptions.advancedStyleParams.conic.x),d(null===(u=null===(v=null===(h=null===(c=this.options)||void 0===c?void 0:c.advancedStyle)||void 0===h?void 0:h.params)||void 0===v?void 0:v.conic)||void 0===u?void 0:u.y,this.recommendOptions.advancedStyleParams.conic.y));return null===(y=null===(m=null===(p=this.options)||void 0===p?void 0:p.advancedStyle)||void 0===m?void 0:m.colorStops)||void 0===y||y.forEach((function(t){g.addColorStop(t.offset,t.color)})),g},t.prototype.createRadialGradient=function(t){var e,i,a,n,o,r,s,l,c,h,v,u,p,m,y,g,f,w,x,b,S,k,P,O,C,L,E,T=t.createRadialGradient(d(null===(n=null===(a=null===(i=null===(e=this.options)||void 0===e?void 0:e.advancedStyle)||void 0===i?void 0:i.params)||void 0===a?void 0:a.radial)||void 0===n?void 0:n.x0,this.recommendOptions.advancedStyleParams.radial.x0),d(null===(l=null===(s=null===(r=null===(o=this.options)||void 0===o?void 0:o.advancedStyle)||void 0===r?void 0:r.params)||void 0===s?void 0:s.radial)||void 0===l?void 0:l.y0,this.recommendOptions.advancedStyleParams.radial.y0),d(null===(u=null===(v=null===(h=null===(c=this.options)||void 0===c?void 0:c.advancedStyle)||void 0===h?void 0:h.params)||void 0===v?void 0:v.radial)||void 0===u?void 0:u.r0,this.recommendOptions.advancedStyleParams.radial.r0),d(null===(g=null===(y=null===(m=null===(p=this.options)||void 0===p?void 0:p.advancedStyle)||void 0===m?void 0:m.params)||void 0===y?void 0:y.radial)||void 0===g?void 0:g.x1,this.recommendOptions.advancedStyleParams.radial.x1),d(null===(b=null===(x=null===(w=null===(f=this.options)||void 0===f?void 0:f.advancedStyle)||void 0===w?void 0:w.params)||void 0===x?void 0:x.radial)||void 0===b?void 0:b.y1,this.recommendOptions.advancedStyleParams.radial.y1),d(null===(O=null===(P=null===(k=null===(S=this.options)||void 0===S?void 0:S.advancedStyle)||void 0===k?void 0:k.params)||void 0===P?void 0:P.radial)||void 0===O?void 0:O.r1,this.recommendOptions.advancedStyleParams.radial.r1));return null===(E=null===(L=null===(C=this.options)||void 0===C?void 0:C.advancedStyle)||void 0===L?void 0:L.colorStops)||void 0===E||E.forEach((function(t){T.addColorStop(t.offset,t.color)})),T},t.prototype.createPattern=function(t){var e,i,a,n,o,r,s,l;return t.createPattern(null===(n=null===(a=null===(i=null===(e=this.options)||void 0===e?void 0:e.advancedStyle)||void 0===i?void 0:i.params)||void 0===a?void 0:a.pattern)||void 0===n?void 0:n.image,(null===(l=null===(s=null===(r=null===(o=this.options)||void 0===o?void 0:o.advancedStyle)||void 0===r?void 0:r.params)||void 0===s?void 0:s.pattern)||void 0===l?void 0:l.repetition)||"")},t.prototype.setText=function(t,e){var i="fillText";"stroke"===this.options.textType&&(i="strokeText"),t[i]&&t[i](e.text,e.x,e.y,e.maxWidth)},t.prototype.drawText=function(t,e){this.setText(t,{text:this.options.content,x:0,y:0-this.recommendOptions.textLine.yOffsetValue,maxWidth:this.options.textRowMaxWidth||this.options.width}),e(t.canvas)},t.prototype.drawImage=function(t,e){var i=this;c(this.options.image).then((function(a){var n=i.getImageRect(a),o=n.width,r=n.height,s=i.getDrawImagePosition(o,r);t.drawImage(a,s.x,s.y,o,r),e(t.canvas)}))},t.prototype.drawMultiLineText=function(t,e){var i=this,a=this.recommendOptions.textLine.data,n=this.recommendOptions.textLine.yOffsetValue;a.forEach((function(e,a){i.setText(t,{text:e,x:0,y:i.options.lineHeight*a-n})})),e(t.canvas)},t.prototype.drawRichText=function(t,e){var i,a,n=this,o=function(t,e){var i=l("svg",{xmlns:"http://www.w3.org/2000/svg"}),a=document.createElement("div");a.setAttribute("xmlns","http://www.w3.org/1999/xhtml"),a.style.cssText="\n  text-align: center;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  font: ".concat(t.font,";\n  color: ").concat(e.fontColor,";\n"),a.innerHTML='<div class="rich-text-content">'.concat(e.content,"</div>"),document.body.appendChild(a);var n=a.querySelector(".rich-text-content"),o=n.offsetHeight,r=n.offsetWidth;document.body.removeChild(a);var s=e.richTextWidth||r||e.width,d=e.richTextHeight||o||e.height;i.setAttribute("width",s.toString()),i.setAttribute("height",d.toString());var c=l("foreignObject",{width:s.toString(),height:d.toString()});return c.appendChild(a),i.appendChild(c),{element:i,width:s,height:d}}(t,this.options);c((i=o.element,a=i.outerHTML.replace(/\n/g,"").replace(/\t/g,"").replace(/#/g,"%23"),"data:image/svg+xml;charset=utf-8,".concat(a)),o.width,o.height).then((function(i){var a=n.getDrawImagePosition(i.width,i.height);t.drawImage(i,a.x,a.y,i.width,i.height),e(t.canvas)}))},t.prototype.getImageRect=function(t){var e={width:this.options.imageWidth||0,height:this.options.imageHeight||0};switch(!0){case 0!==e.width&&0===e.height:e.height=e.width*t.height/t.width;break;case 0===e.width&&0!==e.height:e.width=e.height*t.width/t.height;break;case 0===e.width&&0===e.height:e.width=t.width,e.height=t.height}return e},t.prototype.getDrawImagePosition=function(t,e){var i,a,n={x:-t/2,y:-e/2};switch(this.options.translatePlacement){case"top":n.x=-t/2,n.y=0;break;case"top-start":n.x=0,n.y=0;break;case"top-end":n.x=-t,n.y=0;break;case"bottom":n.x=-t/2,n.y=-e;break;case"bottom-start":n.x=0,n.y=-e;break;case"bottom-end":n.x=-t,n.y=-e;break;case"left":n.x=0,n.y=-e/2;break;case"right":n.x=-t,n.y=-e/2}return!s(null===(i=this.props)||void 0===i?void 0:i.translateX)&&(n.x=0),!s(null===(a=this.props)||void 0===a?void 0:a.translateY)&&(n.y=0),n},t}(),u=function(){function t(t,e){var i,a,n,o,r,s,l;this.options=t,this.partialWidth=this.options.width,this.partialHeight=this.options.height,this.rows=(null===(i=this.options.gridLayoutOptions)||void 0===i?void 0:i.rows)||1,this.cols=(null===(a=this.options.gridLayoutOptions)||void 0===a?void 0:a.cols)||1,this.matrix=(null===(n=this.options.gridLayoutOptions)||void 0===n?void 0:n.matrix)||(r=this.rows,s=this.cols,l=1,Array.from({length:r},(function(){return new Array(s).fill(l)}))),this.gap=(null===(o=this.options.gridLayoutOptions)||void 0===o?void 0:o.gap)||[0,0],this.partialCanvas=e}return t.prototype.draw=function(){var t,e,i,a,n,o,r,s,l=v.createCanvas((null===(t=this.options.gridLayoutOptions)||void 0===t?void 0:t.width)||this.partialWidth*this.cols+this.gap[0]*this.cols,(null===(e=this.options.gridLayoutOptions)||void 0===e?void 0:e.height)||this.partialHeight*this.rows+this.gap[1]*this.rows),d=l.getContext("2d");(null===(i=this.options.gridLayoutOptions)||void 0===i?void 0:i.backgroundImage)&&(null==d||d.drawImage(null===(a=this.options.gridLayoutOptions)||void 0===a?void 0:a.backgroundImage,0,0,null===(n=this.options.gridLayoutOptions)||void 0===n?void 0:n.width,null===(o=this.options.gridLayoutOptions)||void 0===o?void 0:o.height));for(var c=0;c<this.rows;c++)for(var h=0;h<this.cols;h++)(null===(s=null===(r=this.matrix)||void 0===r?void 0:r[c])||void 0===s?void 0:s[h])&&(null==d||d.drawImage(this.partialCanvas,this.partialWidth*h+this.gap[0]*h,this.partialHeight*c+this.gap[1]*c,this.partialWidth,this.partialHeight));return l},t}(),p=function(t,e){return"grid"===t.layout?new u(t,e).draw():e},m=function(){function t(t){void 0===t&&(t={}),this.parentElement=document.body,this.props=t,this.options=Object.assign({},h,t),this.changeParentElement(this.options.parent),this.watermarkCanvas=new v(this.props,this.options),"undefined"!=typeof window&&(Object.defineProperty(window,"MutationObserver",{writable:!1,configurable:!1}),Object.defineProperty(window,"requestAnimationFrame",{writable:!1,configurable:!1}))}return t.prototype.changeOptions=function(t,e,i){return void 0===t&&(t={}),void 0===e&&(e="overwrite"),void 0===i&&(i=!0),a(this,void 0,void 0,(function(){return n(this,(function(a){switch(a.label){case 0:return this.initConfigData(t,e),i?(this.remove(),[4,this.create()]):[3,2];case 1:a.sent(),a.label=2;case 2:return[2]}}))}))},t.prototype.create=function(){var t,e,i,r,l,d,c;return a(this,void 0,void 0,(function(){var a,h,v,u,m;return n(this,(function(n){switch(n.label){case 0:return this.validateUnique()&&this.validateContent()?(a=s(this.watermarkDom),[4,null===(t=this.watermarkCanvas)||void 0===t?void 0:t.draw()]):[2];case 1:if(n.sent(),this.layoutCanvas=p(this.options,null===(e=this.watermarkCanvas)||void 0===e?void 0:e.getCanvas()),h=o(this.layoutCanvas),null===(i=this.watermarkCanvas)||void 0===i||i.clear(),this.watermarkDom=document.createElement("div"),v=document.createElement("div"),this.watermarkDom.__WATERMARK__="watermark",this.watermarkDom.__WATERMARK__INSTANCE__=this,u=this.checkParentElementType(),this.watermarkDom.style.cssText="\n      z-index: ".concat(this.options.zIndex,"!important;display:block!important;visibility:visible!important;transform:none!important;scale:none!important;\n      ").concat("custom"===u?"top:0!important;bottom:0!important;left:0!important;right:0!important;height:100%!important;pointer-events:none!important;position:absolute!important;":"position:relative!important;","\n    "),m=function(t){var e,i,a;if("grid"===t.layout){var n=(null===(e=t.gridLayoutOptions)||void 0===e?void 0:e.cols)||1,o=(null===(i=t.gridLayoutOptions)||void 0===i?void 0:i.rows)||1,r=(null===(a=t.gridLayoutOptions)||void 0===a?void 0:a.gap)||[0,0];return[t.width*n+r[0]*n,t.height*o+r[1]*o]}return[t.width,t.height]}(this.options),v.style.cssText="\n      display:block!important;visibility:visible!important;pointer-events:none;top:0;bottom:0;left:0;right:0;transform:none!important;scale:none!important;\n      position:".concat("root"===u?"fixed":"absolute","!important;-webkit-print-color-adjust:exact!important;width:100%!important;height:100%!important;\n      z-index:").concat(this.options.zIndex,"!important;background-image:url(").concat(h,")!important;background-repeat:").concat(this.options.backgroundRepeat,"!important;\n      background-size:").concat(m[0],"px ").concat(m[1],"px!important;background-position:").concat(this.options.backgroundPosition,";\n      ").concat(this.options.movable?"animation: 200s ease 0s infinite normal none running watermark !important;":"","\n    "),this.watermarkDom.append(v),this.parentElement.appendChild(this.watermarkDom),this.options.mutationObserve)try{this.bindMutationObserve()}catch(t){null===(l=(r=this.options).onObserveError)||void 0===l||l.call(r)}return a&&(null===(c=(d=this.options).onSuccess)||void 0===c||c.call(d)),[2]}}))}))},t.prototype.destroy=function(){this.remove(),this.watermarkDom=void 0},t.prototype.check=function(){return a(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return this.parentElement.contains(this.watermarkDom)?[3,2]:(this.remove(),[4,this.create()]);case 1:t.sent(),t.label=2;case 2:return[2]}}))}))},t.prototype.remove=function(){var t,e,i,a,n,o,r;null===(e=(t=this.options).onBeforeDestroy)||void 0===e||e.call(t),null===(i=this.observer)||void 0===i||i.disconnect(),null===(a=this.parentObserve)||void 0===a||a.disconnect(),this.unbindCheckWatermarkElementEvent(),null===(n=this.watermarkDom)||void 0===n||n.remove(),null===(r=(o=this.options).onDestroyed)||void 0===r||r.call(o)},t.prototype.initConfigData=function(t,e){var i=this;void 0===e&&(e="overwrite"),"append"===e?Object.keys(t).forEach((function(e){i.props&&(i.props[e]=t[e])})):this.props=t,this.options=Object.assign({},h,this.props),this.changeParentElement(this.options.parent),this.watermarkCanvas=new v(this.props,this.options)},t.prototype.changeParentElement=function(t){if("string"==typeof t){var e=document.querySelector(t);e&&(this.parentElement=e)}else this.parentElement=t},t.prototype.validateUnique=function(){var t=!0;return this.parentElement.childNodes.forEach((function(e){t&&Object.hasOwnProperty.call(e,"__WATERMARK__")&&(t=!1)})),t},t.prototype.validateContent=function(){switch(this.options.contentType){case"image":return Object.hasOwnProperty.call(this.options,"image");case"multi-line-text":case"rich-text":case"text":return this.options.content.length>0}return!1},t.prototype.checkParentElementType=function(){return["html","body"].includes(this.parentElement.tagName.toLocaleLowerCase())?"root":"custom"},t.prototype.checkWatermarkElement=function(){return a(this,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return this.parentElement.contains(this.watermarkDom)?[3,2]:(this.remove(),[4,this.create()]);case 1:t.sent(),t.label=2;case 2:return this.bindCheckWatermarkElementEvent(),[2]}}))}))},t.prototype.bindMutationObserve=function(){var t=this;this.watermarkDom&&(this.bindCheckWatermarkElementEvent(),this.observer=new MutationObserver((function(e){return a(t,void 0,void 0,(function(){return n(this,(function(t){switch(t.label){case 0:return e.length>0?(this.remove(),[4,this.create()]):[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}}))}))})),this.observer.observe(this.watermarkDom,{attributes:!0,childList:!0,subtree:!0,characterData:!0}),this.parentObserve=new MutationObserver((function(e){return a(t,void 0,void 0,(function(){var t,i,a,o;return n(this,(function(n){switch(n.label){case 0:t=0,i=e,n.label=1;case 1:return t<i.length?(null==(a=i[t])?void 0:a.target)===this.watermarkDom||(null===(o=null==a?void 0:a.removedNodes)||void 0===o?void 0:o[0])===this.watermarkDom||"childList"===a.type&&a.target===this.parentElement&&a.target.lastChild!==this.watermarkDom?(this.remove(),[4,this.create()]):[3,3]:[3,4];case 2:n.sent(),n.label=3;case 3:return t++,[3,1];case 4:return[2]}}))}))})),this.parentObserve.observe(this.parentElement,{attributes:!0,childList:!0,subtree:!0,characterData:!0}))},t.prototype.bindCheckWatermarkElementEvent=function(){this.unbindCheckWatermarkElementEvent(),this.checkWatermarkElementRequestID=requestAnimationFrame(this.checkWatermarkElement.bind(this))},t.prototype.unbindCheckWatermarkElementEvent=function(){s(this.checkWatermarkElementRequestID)||cancelAnimationFrame(this.checkWatermarkElementRequestID)},t}(),y=function(t){function i(e){return void 0===e&&(e={}),e.globalAlpha=.005,e.mode="blind",t.call(this,e)||this}return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function a(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(a.prototype=i.prototype,new a)}(i,t),i.decode=function(t){var e=Object.assign({url:"",fillColor:"#000",compositeOperation:"color-burn",mode:"canvas"},t);if(e.url&&"canvas"===e.mode){var i=new Image;i.src=e.url,i.onload=function(){var t,a=i.width,n=i.height,s=v.createCanvas(a,n),l=s.getContext("2d");if(null===l)throw new Error("get context error");l.drawImage(i,0,0,a,n),l.globalCompositeOperation=e.compositeOperation,l.fillStyle=e.fillColor,l.fillRect(0,0,a,n);var d=o(s);e.onSuccess&&r(e.onSuccess)&&(null===(t=e.onSuccess)||void 0===t||t.call(e,d))}}},i}(m),g=function(){function t(t){var e;void 0===t&&(t={}),this.drew=!1,this.props=t,this.options=Object.assign({},h,t),this.watermarkCanvas=new v(this.props,this.options),this.originalSrc=null===(e=this.props.dom)||void 0===e?void 0:e.src,this.backgroundImage=this.getBackgroundImage()}return t.prototype.create=function(){var t,e,r,s,l;return a(this,void 0,void 0,(function(){return n(this,(function(a){switch(a.label){case 0:return this.drew?[2]:[4,null===(t=this.watermarkCanvas)||void 0===t?void 0:t.draw()];case 1:return a.sent(),this.options.layout="grid",this.options.gridLayoutOptions=i(i({},this.options.gridLayoutOptions),{width:null===(e=this.backgroundImage)||void 0===e?void 0:e.width,height:null===(r=this.backgroundImage)||void 0===r?void 0:r.height,backgroundImage:this.backgroundImage}),this.layoutCanvas=p(this.options,null===(s=this.watermarkCanvas)||void 0===s?void 0:s.getCanvas()),this.options.dom.src=o(this.layoutCanvas),null===(l=this.watermarkCanvas)||void 0===l||l.clear(),this.drew=!0,[2]}}))}))},t.prototype.destroy=function(){this.options.dom.src=this.originalSrc,this.drew=!1},t.prototype.getBackgroundImage=function(){if(this.options.dom)return this.options.dom},t}();return function(t,e){void 0===e&&(e={});var i=e.insertAt;if(t&&"undefined"!=typeof document){var a=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css","top"===i&&a.firstChild?a.insertBefore(n,a.firstChild):a.appendChild(n),n.styleSheet?n.styleSheet.cssText=t:n.appendChild(document.createTextNode(t))}}("@keyframes watermark{0%{background-position:0 0}25%{background-position:100% 100%}50%{background-position:0 0}75%{background-position:100% -100%}to{background-position:0 0}}"),t.BlindWatermark=y,t.ImageWatermark=g,t.Watermark=m,t}({});
