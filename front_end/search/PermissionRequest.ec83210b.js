import{n as i}from"./index.87b88fdf.js";import"./vendor.056885aa.js";import"./element.65d3a407.js";import"./utils.7bf54f36.js";const n={name:"PermissionRequest",data(){return{requestForm:{document_id:"",permission_type:"read",reason:""},requestRules:{document_id:[{required:!0,message:"\u8BF7\u8F93\u5165\u6587\u6863ID",trigger:"blur"},{pattern:/^\d+$/,message:"\u6587\u6863ID\u5FC5\u987B\u662F\u6570\u5B57",trigger:"blur"}],permission_type:[{required:!0,message:"\u8BF7\u9009\u62E9\u6743\u9650\u7C7B\u578B",trigger:"change"}],reason:[{required:!0,message:"\u8BF7\u8F93\u5165\u7533\u8BF7\u7406\u7531",trigger:"blur"},{min:10,max:500,message:"\u7533\u8BF7\u7406\u7531\u957F\u5EA6\u5728 10 \u5230 500 \u4E2A\u5B57\u7B26",trigger:"blur"}]},submitting:!1,myRequests:[],loading:!1,pagination:{page:1,size:20,total:0}}},mounted(){this.loadMyRequests();const s=this.$route.query.document_id;s&&(this.requestForm.document_id=s)},methods:{async submitRequest(){var s,e;try{await this.$refs.requestForm.validate(),this.submitting=!0,await this.$http.post("/api/permissions/request",this.requestForm),this.$message.success("\u6743\u9650\u7533\u8BF7\u63D0\u4EA4\u6210\u529F\uFF0C\u8BF7\u7B49\u5F85\u5BA1\u6838"),this.resetForm(),this.loadMyRequests()}catch(t){console.error("\u63D0\u4EA4\u7533\u8BF7\u5931\u8D25:",t),this.$message.error(((e=(s=t.response)==null?void 0:s.data)==null?void 0:e.detail)||"\u63D0\u4EA4\u7533\u8BF7\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}finally{this.submitting=!1}},resetForm(){this.$refs.requestForm.resetFields(),this.requestForm={document_id:"",permission_type:"read",reason:""}},async loadMyRequests(){try{this.loading=!0;const s={page:this.pagination.page,size:this.pagination.size},e=await this.$http.get("/api/permissions/my-requests",{params:s});this.myRequests=e.data.records||[],this.pagination.total=e.data.total||0}catch(s){console.error("\u83B7\u53D6\u7533\u8BF7\u8BB0\u5F55\u5931\u8D25:",s),this.$message.error("\u83B7\u53D6\u7533\u8BF7\u8BB0\u5F55\u5931\u8D25")}finally{this.loading=!1}},async withdrawRequest(s){try{await this.$confirm("\u786E\u5B9A\u8981\u64A4\u56DE\u8FD9\u4E2A\u7533\u8BF7\u5417\uFF1F","\u63D0\u793A",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning"}),await this.$http.post(`/auth/permission-requests/${s.id}/withdraw`),this.$message.success("\u7533\u8BF7\u5DF2\u64A4\u56DE"),this.loadMyRequests()}catch(e){e!=="cancel"&&(console.error("\u64A4\u56DE\u7533\u8BF7\u5931\u8D25:",e),this.$message.error("\u64A4\u56DE\u7533\u8BF7\u5931\u8D25"))}},async deleteRequest(s){try{await this.$confirm("\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u4E2A\u7533\u8BF7\u8BB0\u5F55\u5417\uFF1F","\u63D0\u793A",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning"}),await this.$http.delete(`/auth/permission-requests/${s.id}`),this.$message.success("\u7533\u8BF7\u8BB0\u5F55\u5DF2\u5220\u9664"),this.loadMyRequests()}catch(e){e!=="cancel"&&(console.error("\u5220\u9664\u7533\u8BF7\u5931\u8D25:",e),this.$message.error("\u5220\u9664\u7533\u8BF7\u5931\u8D25"))}},handleSizeChange(s){this.pagination.size=s,this.pagination.page=1,this.loadMyRequests()},handleCurrentChange(s){this.pagination.page=s,this.loadMyRequests()},getPermissionTypeText(s){return{read:"\u67E5\u770B",download:"\u4E0B\u8F7D"}[s]||s},getStatusText(s){return{pending:"\u5F85\u5BA1\u6838",approved:"\u5DF2\u901A\u8FC7",rejected:"\u5DF2\u62D2\u7EDD",withdrawn:"\u5DF2\u64A4\u56DE"}[s]||s},getStatusType(s){return{pending:"warning",approved:"success",rejected:"danger",withdrawn:"info"}[s]||"info"},formatDate(s){return s?new Date(s).toLocaleString("zh-CN"):"-"}}};var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"permission-request"},[e._m(0),t("el-card",{staticClass:"request-card",attrs:{shadow:"never"}},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("\u7533\u8BF7\u6587\u6863\u8BBF\u95EE\u6743\u9650")])]),t("el-form",{ref:"requestForm",attrs:{model:e.requestForm,rules:e.requestRules,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"\u6587\u6863ID",prop:"document_id"}},[t("el-input",{attrs:{placeholder:"\u8BF7\u8F93\u5165\u6587\u6863ID",clearable:""},model:{value:e.requestForm.document_id,callback:function(r){e.$set(e.requestForm,"document_id",r)},expression:"requestForm.document_id"}})],1),t("el-form-item",{attrs:{label:"\u6743\u9650\u7C7B\u578B",prop:"permission_type"}},[t("el-radio-group",{model:{value:e.requestForm.permission_type,callback:function(r){e.$set(e.requestForm,"permission_type",r)},expression:"requestForm.permission_type"}},[t("el-radio",{attrs:{label:"read"}},[e._v("\u67E5\u770B\u6743\u9650")]),t("el-radio",{attrs:{label:"download"}},[e._v("\u4E0B\u8F7D\u6743\u9650")])],1)],1),t("el-form-item",{attrs:{label:"\u7533\u8BF7\u7406\u7531",prop:"reason"}},[t("el-input",{attrs:{type:"textarea",rows:4,placeholder:"\u8BF7\u8BE6\u7EC6\u8BF4\u660E\u7533\u8BF7\u8BE5\u6743\u9650\u7684\u7406\u7531",maxlength:"500","show-word-limit":""},model:{value:e.requestForm.reason,callback:function(r){e.$set(e.requestForm,"reason",r)},expression:"requestForm.reason"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary",loading:e.submitting},on:{click:e.submitRequest}},[e._v(" \u63D0\u4EA4\u7533\u8BF7 ")]),t("el-button",{on:{click:e.resetForm}},[e._v("\u91CD\u7F6E")])],1)],1)],1),t("el-card",{staticClass:"history-card",attrs:{shadow:"never"}},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("\u6211\u7684\u7533\u8BF7\u8BB0\u5F55")]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.loadMyRequests}},[e._v(" \u5237\u65B0 ")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.myRequests,stripe:""}},[t("el-table-column",{attrs:{prop:"id",label:"\u7533\u8BF7ID",width:"80"}}),t("el-table-column",{attrs:{label:"\u6587\u6863\u4FE1\u606F","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("div",[t("div",{staticClass:"document-title"},[e._v(e._s(r.row.document_title||`\u6587\u6863ID: ${r.row.document_id}`))]),t("div",{staticClass:"text-muted"},[e._v("ID: "+e._s(r.row.document_id))])])]}}])}),t("el-table-column",{attrs:{label:"\u6743\u9650\u7C7B\u578B",width:"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-tag",{attrs:{type:r.row.permission_type==="read"?"info":"warning",size:"small"}},[e._v(" "+e._s(e.getPermissionTypeText(r.row.permission_type))+" ")])]}}])}),t("el-table-column",{attrs:{label:"\u7533\u8BF7\u7406\u7531","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-tooltip",{attrs:{content:r.row.reason,placement:"top"}},[t("div",{staticClass:"reason-text"},[e._v(e._s(r.row.reason))])])]}}])}),t("el-table-column",{attrs:{label:"\u72B6\u6001",width:"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-tag",{attrs:{type:e.getStatusType(r.row.status),size:"small"}},[e._v(" "+e._s(e.getStatusText(r.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{label:"\u7533\u8BF7\u65F6\u95F4",width:"150"},scopedSlots:e._u([{key:"default",fn:function(r){return[e._v(" "+e._s(e.formatDate(r.row.created_at))+" ")]}}])}),t("el-table-column",{attrs:{label:"\u5BA1\u6838\u4EBA",width:"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[e._v(" "+e._s(r.row.reviewer_name||"-")+" ")]}}])}),t("el-table-column",{attrs:{label:"\u5BA1\u6838\u610F\u89C1","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-tooltip",{attrs:{content:r.row.review_comment,placement:"top"}},[t("div",{staticClass:"comment-text"},[e._v(e._s(r.row.review_comment||"-"))])])]}}])}),t("el-table-column",{attrs:{label:"\u64CD\u4F5C",width:"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(r){return[r.row.status==="pending"?t("div",[t("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.withdrawRequest(r.row)}}},[e._v(" \u64A4\u56DE ")]),t("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.deleteRequest(r.row)}}},[e._v(" \u5220\u9664 ")])],1):t("div",[t("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",size:"mini"},on:{click:function(a){return e.deleteRequest(r.row)}}},[e._v(" \u5220\u9664 ")])],1)]}}])})],1),t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.pagination.page,"page-sizes":[10,20,50],"page-size":e.pagination.size,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)},l=[function(){var s=this,e=s._self._c;return e("div",{staticClass:"page-header"},[e("h2",[s._v("\u6743\u9650\u7533\u8BF7")]),e("p",[s._v("\u7533\u8BF7\u6587\u6863\u8BBF\u95EE\u6743\u9650")])])}],u=i(n,o,l,!1,null,"0145bfe1",null,null);const h=u.exports;export{h as default};
