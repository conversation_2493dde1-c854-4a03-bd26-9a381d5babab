import{s as l}from"./index.87b88fdf.js";import{a as n}from"./request_ai.f9c15611.js";let o=null,e=null,t=null;function s(r){return o&&o.abort(),o=new AbortController,l({url:"/api/search/docs",method:"post",data:r,signal:o.signal})}function u(r){return l({url:"/api/search/projects",method:"post",data:r})}function c(r){return e&&e.abort(),e=new AbortController,n({url:"similar_query",method:"post",data:r,signal:e.signal})}function p(r){return t&&t.abort(),t=new AbortController,n({url:"explain_query",method:"post",data:r,signal:t.signal})}export{c as a,u as b,p as e,s};
