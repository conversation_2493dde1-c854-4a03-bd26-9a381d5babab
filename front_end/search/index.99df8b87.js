import{n as o}from"./index.87b88fdf.js";import"./vendor.056885aa.js";import"./element.65d3a407.js";import"./utils.7bf54f36.js";const i={name:"searchInput",props:{value:{type:Object,default:()=>({type:"1",keyword:""})},isRow:{type:Boolean,default:!1}},data(){return{queryForm:{}}},mounted(){this.queryForm=this.value},watch:{value:{handler(r){this.queryForm=r},deep:!0}},methods:{handleSelectorChange(r){this.$emit("input",this.queryForm),r==3&&this.$router.push({path:"/info"})},handleInput(){this.$emit("input",this.queryForm)},toSearch(){!this.queryForm.keyword.trim()||this.$emit("search",this.queryForm)}}};var a=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"search-input",class:{row:e.isRow,column:!e.isRow}},[t("h1",{class:{title:e.isRow},on:{click:function(s){e.isRow&&e.$router.push("/")}}},[e._v(" \u6863\u6848\u68C0\u7D22 ")]),t("el-input",{staticClass:"input",attrs:{placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u5B57",size:"large"},on:{change:e.handleInput},nativeOn:{keydown:function(s){return!s.type.indexOf("key")&&e._k(s.keyCode,"enter",13,s.key,"Enter")?null:e.toSearch.apply(null,arguments)}},model:{value:e.queryForm.keyword,callback:function(s){e.$set(e.queryForm,"keyword",s)},expression:"queryForm.keyword"}},[t("el-select",{staticStyle:{width:"112px"},attrs:{slot:"prepend",placeholder:"\u8BF7\u9009\u62E9"},on:{change:e.handleSelectorChange},slot:"prepend",model:{value:e.queryForm.type,callback:function(s){e.$set(e.queryForm,"type",s)},expression:"queryForm.type"}},[t("el-option",{attrs:{label:"\u7EFC\u5408\u641C\u7D22",value:"1"}}),t("el-option",{attrs:{label:"\u7CBE\u786E\u641C\u7D22",value:"3"}})],1),t("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.toSearch},slot:"append"})],1)],1)])},l=[],u=o(i,a,l,!1,null,"dccbb826",null,null);const c=u.exports;const h={name:"index",components:{searchInput:c},data(){return{queryForm:{keyword:"",type:"1"}}},mounted(){let{userName:r,email:e}=this.$route.query;r&&e&&(localStorage.setItem("userName",r),localStorage.setItem("email",e),this.$store.dispatch("setEamil",e)),this.$nextTick(()=>{this.syncButtonHeights(),setTimeout(()=>this.syncButtonHeights(),100),setTimeout(()=>this.syncButtonHeights(),300),setTimeout(()=>this.syncButtonHeights(),500)}),window.addEventListener("resize",this.syncButtonHeights)},beforeDestroy(){window.removeEventListener("resize",this.syncButtonHeights)},methods:{goToTasks(){this.$router.push("/tasks")},syncButtonHeights(){this.$nextTick(()=>{})},toSearch(){const r=this.$store.getters["user/isLoggedIn"],e=this.$store.getters["user/hasToken"];if(!r||!e){this.$router.push("/login");return}if(!this.queryForm.keyword.trim())return;const t=this.$store.getters["user/userInfo"],s=(t==null?void 0:t.user_id)||(t==null?void 0:t.id)||"anonymous",n={...this.queryForm,_userId:s};localStorage.setItem("queryParams",JSON.stringify(n)),this.$router.push({path:"/search"})}}};var p=function(){var e=this,t=e._self._c;return t("div",{staticClass:"index"},[t("div",{staticClass:"search-container"},[t("div",{staticClass:"search-with-tasks"},[e._m(0),t("div",{staticClass:"search-input-group"},[t("el-input",{staticClass:"search-input",attrs:{placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u5B57",size:"large"},nativeOn:{keydown:function(s){return!s.type.indexOf("key")&&e._k(s.keyCode,"enter",13,s.key,"Enter")?null:e.toSearch.apply(null,arguments)}},model:{value:e.queryForm.keyword,callback:function(s){e.$set(e.queryForm,"keyword",s)},expression:"queryForm.keyword"}},[t("el-select",{staticStyle:{width:"112px"},attrs:{slot:"prepend",placeholder:"\u8BF7\u9009\u62E9"},slot:"prepend",model:{value:e.queryForm.type,callback:function(s){e.$set(e.queryForm,"type",s)},expression:"queryForm.type"}},[t("el-option",{attrs:{label:"\u7EFC\u5408\u641C\u7D22",value:"1"}}),t("el-option",{attrs:{label:"\u7CBE\u786E\u641C\u7D22",value:"3"}})],1),t("template",{slot:"append"},[t("div",{staticClass:"button-group"},[t("el-button",{staticClass:"search-btn",attrs:{icon:"el-icon-search"},on:{click:e.toSearch}}),t("el-button",{staticClass:"tasks-btn",attrs:{icon:"el-icon-s-order",title:"\u4EFB\u52A1\u67E5\u770B"},on:{click:e.goToTasks}})],1)])],2)],1)])])])},d=[function(){var r=this,e=r._self._c;return e("div",{staticClass:"search-title"},[e("h1",[r._v("\u6863\u6848\u68C0\u7D22")])])}],y=o(h,p,d,!1,null,"2b7779ef",null,null);const k=y.exports;export{k as default};
