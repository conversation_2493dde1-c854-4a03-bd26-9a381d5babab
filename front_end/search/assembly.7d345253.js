import{a as c}from"./request_ai.f9c15611.js";import{f as p,a as g}from"./index.d17f4061.js";import{e as k}from"./vendor.056885aa.js";import{_ as y}from"./utils.7bf54f36.js";import{M as b}from"./index.d186ba97.js";import{n as f}from"./index.87b88fdf.js";import"./element.65d3a407.js";function v(s){return c.post("/assembly_task",s)}function T(s){return c.get(`/assembly_task/${s}`)}function _(){return c.get("/assembly_tasks")}function P(s){return c.delete(`/task/${s}`)}function w(){return c.get("/assembly_task/latest_params")}function q(){return c.post("/assembly_keywords")}function C(){return c.post("/prompt_names")}function I(s){return c.post("/get_prompt",{},{params:{prompt_name:s}})}const R={name:"assembly",data(){return{queryParam:{project_type:"",year:"",keywords:[],question:"",prompt:"\u81EA\u5B9A\u4E49\u6C47\u7F16",promptName:""},typeOptions:[{value:"\u6587\u4E66\u6863\u6848",label:"\u6587\u4E66\u6863\u6848"},{value:"\u79D1\u6280\u9879\u76EE",label:"\u79D1\u6280\u9879\u76EE"},{value:"\u4FE1\u606F\u5316\u9879\u76EE",label:"\u4FE1\u606F\u5316\u9879\u76EE"}],keywordOptions:[],activeTab:"1",checkedKeyword:[],resDialogVisible:!1,loading:!0,historyItem:{},historyItemDialogVisible:!1,promptNames:[],res:"",echart:"",image:"",md:"",assemblyTaskRunning:!1,currentTaskId:null,taskPollingTimer:null,taskPollingInterval:2e3,taskProgress:0,taskProgressText:"\u6B63\u5728\u521D\u59CB\u5316...",taskCompleted:!1,pollingErrorCount:0}},async created(){this.setTitle(),this.getKeyword(),this.md=b({html:!0,linkify:!0,typographer:!0})},async mounted(){var s,e;console.log("\u{1F680} Assembly\u9875\u9762mounted\u5F00\u59CB"),window.toggleThinking=this.toggleThinking;try{await this.$store.dispatch("initHistory"),console.log("\u2705 initHistory\u5B8C\u6210\uFF0C\u5F53\u524D\u5386\u53F2\u8BB0\u5F55:",((s=this.history)==null?void 0:s.length)||0),await this.$nextTick(),console.log("\u{1F4CA} \u5F53\u524DnewHistory:",((e=this.newHistory)==null?void 0:e.length)||0)}catch(t){console.error("\u274C initHistory\u5931\u8D25:",t)}await this.checkRunningTasks(),await this.restoreLatestTaskParams()},beforeDestroy(){this.stopTaskPolling(),window.toggleThinking&&delete window.toggleThinking},computed:{...k(["history"]),newHistory(){var e;return((e=this.history)==null?void 0:e.filter(t=>t.type==this.activeTab))||[]},progressStatus(){return this.taskProgress===100?"success":void 0}},methods:{setTitle(){document.title="\u6C47\u7F16\u641C\u7D22"},toggleThinking(s){const t=s.parentElement.querySelector(".ai-thinking-content"),a=s.querySelector(".ai-thinking-icon");t.style.display==="none"?(t.style.display="block",a.classList.add("expanded")):(t.style.display="none",a.classList.remove("expanded"))},reset(){this.queryParam=this.$options.data().queryParam},async getKeyword(){const s=await q();this.keywordOptions=s.data.options},async getPromptNames(){var e;const s=await C();this.promptNames=(e=s.data)==null?void 0:e.map(t=>({label:t,value:t}))},handleTabClick(s){if(!s||!s.name){console.warn("handleTabClick received invalid parameter:",s);return}this.reset(),s.name=="1"?this.queryParam.prompt="\u81EA\u5B9A\u4E49\u6C47\u7F16":s.name=="2"&&(this.queryParam.prompt="",this.getPromptNames())},handleKeywordChange(s){if(this.checkedKeyword.length<s.length){const[e]=p(s,this.checkedKeyword);this.queryParam.question+=e+`
`}else{const[e]=p(s,this.checkedKeyword);this.queryParam.question=this.queryParam.question.replace(new RegExp(e+`.*?
`,"ig"),"")}this.checkedKeyword=s},async assembly(){var t,a,o,i;if(this.assemblyTaskRunning&&this.currentTaskId){this.$message.warning("\u5F53\u524D\u6C47\u7F16\u4EFB\u52A1\u6B63\u5728\u8FDB\u884C\u4E2D\uFF0C\u8BF7\u7B49\u5F85\u5B8C\u6210\u6216\u53D6\u6D88\u540E\u518D\u63D0\u4EA4\u65B0\u4EFB\u52A1");return}const s=y.cloneDeep(this.queryParam),e=this.activeTab;try{this.res="",this.resDialogVisible=!0,this.loading=!0,this.assemblyTaskRunning=!0,this.taskProgress=0,this.taskProgressText="\u6B63\u5728\u521B\u5EFA\u6C47\u7F16\u4EFB\u52A1...",this.taskCompleted=!1,this.pollingErrorCount=0,this.saveParamsToCache(s);const l=await v(s);if(l.code===202)this.currentTaskId=l.data.task_id,this.taskProgress=10,this.taskProgressText="\u6C47\u7F16\u4EFB\u52A1\u5DF2\u521B\u5EFA\uFF0C\u6B63\u5728\u540E\u53F0\u5904\u7406...",this.$message.success("\u6C47\u7F16\u4EFB\u52A1\u5DF2\u521B\u5EFA\uFF0C\u6B63\u5728\u540E\u53F0\u5904\u7406..."),this.startTaskPolling(s,e);else throw new Error(l.msg||"\u521B\u5EFA\u6C47\u7F16\u4EFB\u52A1\u5931\u8D25")}catch(l){this.loading=!1,this.assemblyTaskRunning=!1,this.currentTaskId=null;let n="\u521B\u5EFA\u6C47\u7F16\u4EFB\u52A1\u5931\u8D25";l.code==="NETWORK_ERROR"||((t=l.message)==null?void 0:t.includes("Network Error"))?n="\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5\u540E\u91CD\u8BD5":((a=l.response)==null?void 0:a.status)===401?n="\u8EAB\u4EFD\u9A8C\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55\u540E\u91CD\u8BD5":((o=l.response)==null?void 0:o.status)===403?n="\u6743\u9650\u4E0D\u8DB3\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458":((i=l.response)==null?void 0:i.status)>=500?n="\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5":l.message?n=`\u521B\u5EFA\u4EFB\u52A1\u5931\u8D25\uFF1A${l.message}`:n="\u521B\u5EFA\u4EFB\u52A1\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5",this.$message.error(n)}},startTaskPolling(s,e){this.taskPollingTimer&&clearInterval(this.taskPollingTimer),this.taskPollingTimer=setInterval(async()=>{try{await this.checkTaskStatus(s,e)}catch(t){console.error("\u8F6E\u8BE2\u4EFB\u52A1\u72B6\u6001\u5931\u8D25:",t)}},this.taskPollingInterval),this.checkTaskStatus(s,e)},async checkTaskStatus(s,e){if(!!this.currentTaskId)try{const t=await T(this.currentTaskId);if(t.code===200){const a=t.data.status,o=t.data.progress||0;this.taskProgress=o,a==="pending"?this.taskProgressText="\u4EFB\u52A1\u6392\u961F\u4E2D...":a==="running"?this.taskProgressText="\u6B63\u5728\u5904\u7406\u6C47\u7F16\u8BF7\u6C42...":a==="completed"?(this.taskProgressText="\u6C47\u7F16\u5B8C\u6210\uFF01",this.taskProgress=100):a==="failed"&&(this.taskProgressText="\u6C47\u7F16\u5931\u8D25")}if(t.code===200&&t.data.status==="completed"){this.stopTaskPolling(),this.loading=!1,this.assemblyTaskRunning=!1;const a=t.data.result;this.res=a.response,this.echart=a.echart,this.image=a.image,this.taskCompleted||(this.taskCompleted=!0,this.$store.dispatch("setHistory",y.cloneDeep({time:Date.now(),req:s,res:a.response,echart:a.echart,image:a.image,type:e}))),this.resDialogVisible?this.$message.success("\u6C47\u7F16\u5B8C\u6210\uFF01"):(this.resDialogVisible=!0,this.$message.success("\u6C47\u7F16\u5B8C\u6210\uFF01"))}else if(t.code===500||t.data.status==="failed"){this.stopTaskPolling(),this.loading=!1,this.assemblyTaskRunning=!1;const a=this.getSmartErrorMessage(t.data);this.$message.error(a)}}catch(t){console.error("\u68C0\u67E5\u4EFB\u52A1\u72B6\u6001\u5931\u8D25:",t),this.handlePollingError(t)}},stopTaskPolling(){this.taskPollingTimer&&(clearInterval(this.taskPollingTimer),this.taskPollingTimer=null),this.currentTaskId=null,this.taskProgress=0,this.taskProgressText="\u6B63\u5728\u521D\u59CB\u5316..."},async checkRunningTasks(){try{const s=await _();if(s.code===200&&s.data.length>0){const e=s.data[0];this.currentTaskId=e.task_id,this.assemblyTaskRunning=!0,this.loading=!0;const t=e.task_params||{},a={question:t.question||"",prompt:t.prompt||"\u540D\u8BCD\u89E3\u91CA",promptName:t.promptName||"\u81EA\u5B9A\u4E49\u6C47\u7F16",project_type:t.project_type||"\u79D1\u6280\u9879\u76EE",year:t.year||"2023",keywords:t.keywords||[],history:t.history||null},o=t.promptName==="\u81EA\u5B9A\u4E49\u6C47\u7F16"?"1":"2";console.log("\u{1F504} \u53D1\u73B0\u6B63\u5728\u8FDB\u884C\u7684\u6C47\u7F16\u4EFB\u52A1:",e.task_id),this.$message.info("\u53D1\u73B0\u6B63\u5728\u8FDB\u884C\u7684\u6C47\u7F16\u4EFB\u52A1\uFF0C\u6B63\u5728\u6062\u590D\u72B6\u6001..."),this.resDialogVisible=!0,this.startTaskPolling(a,o)}}catch(s){console.error("\u68C0\u67E5\u6B63\u5728\u8FDB\u884C\u7684\u4EFB\u52A1\u5931\u8D25:",s)}},async cancelCurrentTask(){if(!!this.currentTaskId)try{const s=await P(this.currentTaskId);s.code===200?(this.stopTaskPolling(),this.loading=!1,this.assemblyTaskRunning=!1,this.$message.success("\u6C47\u7F16\u4EFB\u52A1\u5DF2\u53D6\u6D88")):this.$message.error("\u53D6\u6D88\u4EFB\u52A1\u5931\u8D25\uFF1A"+(s.msg||"\u672A\u77E5\u9519\u8BEF"))}catch(s){console.error("\u53D6\u6D88\u4EFB\u52A1\u5931\u8D25:",s),this.$message.error("\u53D6\u6D88\u4EFB\u52A1\u5931\u8D25\uFF1A"+(s.message||"\u7F51\u7EDC\u9519\u8BEF"))}},formatTime:g,handleDeleteHistory(s){this.$store.dispatch("deleteHistory",s)},handleHistoryItemClick(s){this.historyItemDialogVisible=!0,this.historyItem=s},processHistoryResponse(s){if(!s||typeof s!="string")return s;try{if(s.includes("<think>")){const e=/<think>([\s\S]*?)<\/think>/,t=s.match(e);if(t){const a=t[1].trim(),o=s.replace(e,"").trim();return`
<div class="ai-thinking-container">
    <div class="ai-thinking-toggle" onclick="toggleThinking(this)">
        <svg class="ai-thinking-icon" width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.786 4.167L2.765 1.259c-.416-.4-.985-.482-1.273-.183-.287.298-.183.864.233 1.264l3.021 2.908c.416.4.986.482 1.273.184.287-.299.183-.865-.233-1.265z" fill="currentColor"></path>
            <path d="M8.197 1.206L5.288 4.208c-.4.413-.484.982-.187 1.27.298.289.864.187 1.265-.227L9.274 2.25c.401-.414.485-.983.187-1.271-.297-.288-.863-.187-1.264.227z" fill="currentColor"></path>
        </svg>
        <span class="ai-thinking-label">\u601D\u8003\u8FC7\u7A0B</span>
    </div>
    <div class="ai-thinking-content" style="display: none;">
        <div class="ai-thinking-text">${a}</div>
    </div>
</div>

${o}`}}return s}catch(e){return console.error("\u5904\u7406\u5386\u53F2\u8BB0\u5F55\u54CD\u5E94\u5185\u5BB9\u65F6\u51FA\u9519:",e),s}},getSmartErrorMessage(s){if(!s)return"\u6C47\u7F16\u4EFB\u52A1\u5F02\u5E38\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5";const e=s.status,t=s.error||"",a=s.error_type||"unknown";let o="";switch(e){case"failed":o="\u6C47\u7F16\u4EFB\u52A1\u6267\u884C\u5931\u8D25";break;case"timeout":o="\u6C47\u7F16\u4EFB\u52A1\u6267\u884C\u8D85\u65F6";break;default:o="\u6C47\u7F16\u4EFB\u52A1\u5F02\u5E38"}let i="";switch(a){case"network":i="\u7F51\u7EDC\u8FDE\u63A5\u4E0D\u7A33\u5B9A\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5\u540E\u91CD\u8BD5";break;case"timeout":i="\u670D\u52A1\u54CD\u5E94\u8D85\u65F6\uFF0C\u53EF\u80FD\u662F\u67E5\u8BE2\u5185\u5BB9\u8FC7\u4E8E\u590D\u6742\uFF0C\u8BF7\u7B80\u5316\u67E5\u8BE2\u6761\u4EF6\u6216\u7A0D\u540E\u91CD\u8BD5";break;case"llm_service":i="AI\u670D\u52A1\u6682\u65F6\u4E0D\u53EF\u7528\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5\u6216\u8054\u7CFB\u7BA1\u7406\u5458";break;case"search_service":i="\u6587\u6863\u641C\u7D22\u670D\u52A1\u5F02\u5E38\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5";break;case"database":i="\u6570\u636E\u5E93\u670D\u52A1\u5F02\u5E38\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5\u6216\u8054\u7CFB\u7BA1\u7406\u5458";break;case"processing":i="\u6570\u636E\u5904\u7406\u5F02\u5E38\uFF0C\u8BF7\u68C0\u67E5\u8F93\u5165\u5185\u5BB9\u6216\u7A0D\u540E\u91CD\u8BD5";break;default:t.includes("\u8FDE\u63A5")||t.includes("connection")?i="\u670D\u52A1\u8FDE\u63A5\u5F02\u5E38\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5\u6216\u7A0D\u540E\u91CD\u8BD5":t.includes("\u8D85\u65F6")||t.includes("timeout")?i="\u8BF7\u6C42\u8D85\u65F6\uFF0C\u8BF7\u7B80\u5316\u67E5\u8BE2\u6761\u4EF6\u6216\u7A0D\u540E\u91CD\u8BD5":t.includes("\u6743\u9650")||t.includes("permission")?i="\u6743\u9650\u4E0D\u8DB3\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u68C0\u67E5\u6743\u9650\u8BBE\u7F6E":t.includes("not found")||t.includes("\u627E\u4E0D\u5230")?i="\u8BF7\u6C42\u7684\u8D44\u6E90\u4E0D\u5B58\u5728\uFF0C\u8BF7\u68C0\u67E5\u67E5\u8BE2\u6761\u4EF6":i="\u7CFB\u7EDF\u5F02\u5E38\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5\u6216\u8054\u7CFB\u7BA1\u7406\u5458"}return`${o}\uFF1A${i}`},handlePollingError(s){var e,t,a,o;if(this.pollingErrorCount||(this.pollingErrorCount=0),this.pollingErrorCount++,this.pollingErrorCount>=3){let i="\u4EFB\u52A1\u72B6\u6001\u68C0\u67E5\u5931\u8D25";s.code==="NETWORK_ERROR"||((e=s.message)==null?void 0:e.includes("Network Error"))?i="\u7F51\u7EDC\u8FDE\u63A5\u4E0D\u7A33\u5B9A\uFF0C\u6B63\u5728\u91CD\u8BD5\u8FDE\u63A5...":s.code==="TIMEOUT"||((t=s.message)==null?void 0:t.includes("timeout"))?i="\u670D\u52A1\u54CD\u5E94\u8D85\u65F6\uFF0C\u6B63\u5728\u91CD\u8BD5\u8FDE\u63A5...":((a=s.response)==null?void 0:a.status)===404?(i="\u4EFB\u52A1\u4E0D\u5B58\u5728\u6216\u5DF2\u8FC7\u671F",this.stopTaskPolling(),this.loading=!1,this.assemblyTaskRunning=!1):((o=s.response)==null?void 0:o.status)>=500?i="\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF\uFF0C\u6B63\u5728\u91CD\u8BD5\u8FDE\u63A5...":i="\u8FDE\u63A5\u5F02\u5E38\uFF0C\u6B63\u5728\u91CD\u8BD5\u8FDE\u63A5...",this.assemblyTaskRunning&&this.$message.warning(i),this.pollingErrorCount=0}},async restoreLatestTaskParams(){try{console.log("\u{1F504} \u5C1D\u8BD5\u6062\u590D\u6700\u65B0\u4EFB\u52A1\u53C2\u6570...");const s=this.getParamsFromCache();if(s){console.log("\u{1F4E6} \u4ECE\u6D4F\u89C8\u5668\u7F13\u5B58\u6062\u590D\u53C2\u6570:",s),this.restoreQueryParams(s);return}const e=await w();e.code===200&&e.data?(console.log("\u{1F310} \u4ECE\u670D\u52A1\u5668\u6062\u590D\u6700\u65B0\u53C2\u6570:",e.data),this.restoreQueryParams(e.data),this.saveParamsToCache(e.data)):console.log("\u2139\uFE0F \u672A\u627E\u5230\u53EF\u6062\u590D\u7684\u53C2\u6570")}catch(s){console.warn("\u26A0\uFE0F \u6062\u590D\u53C2\u6570\u5931\u8D25:",s.message)}},getParamsFromCache(){try{const s=localStorage.getItem("assembly_latest_params");if(s){const e=JSON.parse(s);if(Date.now()-e.timestamp<60*60*1e3)return e.params;localStorage.removeItem("assembly_latest_params")}}catch(s){console.warn("\u8BFB\u53D6\u7F13\u5B58\u53C2\u6570\u5931\u8D25:",s)}return null},saveParamsToCache(s){try{const e={params:s,timestamp:Date.now()};localStorage.setItem("assembly_latest_params",JSON.stringify(e)),console.log("\u{1F4BE} \u53C2\u6570\u5DF2\u4FDD\u5B58\u5230\u7F13\u5B58")}catch(e){console.warn("\u4FDD\u5B58\u53C2\u6570\u5230\u7F13\u5B58\u5931\u8D25:",e)}},restoreQueryParams(s){!s||(this.queryParam.question=s.question||"",this.queryParam.project_type=s.project_type||"",this.queryParam.year=s.year||"",this.queryParam.keywords=s.keywords||[],this.queryParam.prompt=s.prompt||"",this.queryParam.promptName=s.promptName||"",s.promptName==="\u81EA\u5B9A\u4E49\u6C47\u7F16"||s.prompt==="\u81EA\u5B9A\u4E49\u6C47\u7F16"?this.activeTab="1":s.promptName&&s.promptName!=="\u81EA\u5B9A\u4E49\u6C47\u7F16"&&(this.activeTab="2"),console.log("\u2705 \u53C2\u6570\u6062\u590D\u5B8C\u6210:",{activeTab:this.activeTab,question:this.queryParam.question,promptName:this.queryParam.promptName}))},selectActiveTabWithData(){if(!this.history||this.history.length===0){console.log("\u{1F4CB} \u6CA1\u6709\u5386\u53F2\u8BB0\u5F55\uFF0C\u4FDD\u6301\u9ED8\u8BA4tab");return}const s=this.history.filter(a=>a.type==="1").length,e=this.history.filter(a=>a.type==="2").length;console.log("\u{1F4CA} \u5386\u53F2\u8BB0\u5F55\u7EDF\u8BA1:",{total:this.history.length,custom:s,template:e,currentTab:this.activeTab}),(this.activeTab==="1"?s:e)===0?this.activeTab==="1"&&e>0?(console.log("\u{1F504} \u5207\u6362\u5230\u6A21\u677F\u6C47\u7F16tab\uFF08\u6709\u6570\u636E\uFF09"),this.activeTab="2"):this.activeTab==="2"&&s>0&&(console.log("\u{1F504} \u5207\u6362\u5230\u81EA\u5B9A\u4E49\u6C47\u7F16tab\uFF08\u6709\u6570\u636E\uFF09"),this.activeTab="1"):console.log("\u2705 \u5F53\u524Dtab\u6709\u6570\u636E\uFF0C\u65E0\u9700\u5207\u6362")},async handlePromptNameChange(s){const e=await I(s);this.queryParam.prompt=e.data},handleDialogClose(){this.assemblyTaskRunning&&this.currentTaskId&&this.$message.info("\u6C47\u7F16\u4EFB\u52A1\u4ECD\u5728\u540E\u53F0\u8FDB\u884C\u4E2D\uFF0C\u5B8C\u6210\u540E\u5C06\u81EA\u52A8\u663E\u793A\u7ED3\u679C\u3002\u60A8\u53EF\u4EE5\u5207\u6362\u9875\u9762\uFF0C\u4EFB\u52A1\u4E0D\u4F1A\u4E2D\u65AD\u3002")},resizeIframe(){const s=document.querySelector("iframe"),e=s.contentDocument||s.contentWindow.document;s.style.height=e.body.scrollHeight+"px",s.style.width=e.body.scrollWidth+"px"}}};var $=function(){var a,o,i,l,n,m,h,d;var e=this,t=e._self._c;return t("div",{staticClass:"assembly"},[t("el-tabs",{on:{"tab-click":e.handleTabClick},model:{value:e.activeTab,callback:function(r){e.activeTab=r},expression:"activeTab"}},[t("el-tab-pane",{attrs:{label:"\u81EA\u5B9A\u4E49\u6C47\u7F16",name:"1"}}),t("el-tab-pane",{attrs:{label:"\u6A21\u677F\u6C47\u7F16",name:"2"}})],1),e.activeTab=="1"?t("el-form",{staticClass:"query-form",attrs:{size:"small"}},[t("el-form-item",{attrs:{label:"\u7C7B\u578B"}},[t("el-select",{attrs:{placeholder:"\u8BF7\u9009\u62E9",clearable:""},model:{value:e.queryParam.project_type,callback:function(r){e.$set(e.queryParam,"project_type",r)},expression:"queryParam.project_type"}},e._l(e.typeOptions,function(r){return t("el-option",{key:r.value,attrs:{label:r.label,value:r.value}})}),1)],1),t("el-form-item",{attrs:{label:"\u5173\u952E\u8BCD"}},[t("el-cascader",{attrs:{options:e.keywordOptions,clearable:"","collapse-tags":"",filterable:"",props:{multiple:!0,emitPath:!1},"show-all-levels":!1,"popper-class":"assembly-keyword"},on:{change:e.handleKeywordChange},model:{value:e.queryParam.keywords,callback:function(r){e.$set(e.queryParam,"keywords",r)},expression:"queryParam.keywords"}})],1),t("el-form-item",{attrs:{label:"\u5E74\u4EFD"}},[t("el-date-picker",{attrs:{type:"year","value-format":"yyyy",placeholder:"\u8BF7\u9009\u62E9",clearable:""},model:{value:e.queryParam.year,callback:function(r){e.$set(e.queryParam,"year",r)},expression:"queryParam.year"}})],1)],1):t("el-form",{staticClass:"query-form template-query-form",staticStyle:{"border-right":"1px solid #dcdfe6"},attrs:{size:"small"}},[t("el-form-item",{attrs:{label:"\u6A21\u677F"}},[t("el-select",{attrs:{placeholder:"\u8BF7\u9009\u62E9",clearable:""},on:{change:e.handlePromptNameChange},model:{value:e.queryParam.promptName,callback:function(r){e.$set(e.queryParam,"promptName",r)},expression:"queryParam.promptName"}},e._l(e.promptNames,function(r){return t("el-option",{key:r.value,attrs:{label:r.label,value:r.value}})}),1)],1)],1),t("div",{staticClass:"main"},[t("div",{staticClass:"card content"},[t("el-alert",{attrs:{title:"\u60A8\u597D\uFF0C\u60A8\u63D0\u4F9B\u7684\u4FE1\u606F\u8D8A\u8BE6\u7EC6\u3001\u8D8A\u4E30\u5BCC\uFF0C\u6C47\u7F16\u6548\u679C\u8D8A\u597D",type:"info",closable:!1,"show-icon":""}}),t("el-input",{staticClass:"input",attrs:{placeholder:"\u8BF7\u8F93\u5165\u81EA\u5B9A\u4E49\u6761\u4EF6",type:"textarea",resize:"none"},model:{value:e.queryParam.question,callback:function(r){e.$set(e.queryParam,"question",r)},expression:"queryParam.question"}}),t("el-button-group",{staticClass:"btns"},[t("el-button",{attrs:{block:""},on:{click:e.reset}},[e._v(" \u91CD\u7F6E ")]),e.assemblyTaskRunning?t("el-button",{attrs:{type:"danger",block:""},on:{click:e.cancelCurrentTask}},[e._v(" \u53D6\u6D88\u6C47\u7F16 ")]):t("el-button",{attrs:{type:"primary",disabled:!e.queryParam.question,block:""},on:{click:e.assembly}},[e._v(" \u5F00\u59CB\u6C47\u7F16 ")])],1)],1),t("div",{staticClass:"card history"},[t("span",{staticClass:"header"},[e._v("\u5386\u53F2\u8BB0\u5F55")]),e.newHistory.length?t("div",{staticClass:"body"},e._l(e.newHistory,function(r){return t("div",{key:r.id||r.time,staticClass:"item",on:{click:function(u){return e.handleHistoryItemClick(r)}}},[t("div",{staticClass:"top"},[t("el-button",{staticClass:"btn",attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(u){return u.stopPropagation(),e.handleDeleteHistory(r.id||r.time)}}})],1),t("div",{staticClass:"value"},[e._v(e._s(r.req.question))]),t("el-divider",{staticClass:"divider"})],1)}),0):t("el-empty")],1)]),t("el-dialog",{staticClass:"res_dialog",attrs:{title:e.activeTab=="1"?"\u81EA\u5B9A\u4E49\u6C47\u7F16":"\u6A21\u677F\u6C47\u7F16",width:"70%",visible:e.resDialogVisible,"close-on-click-modal":!e.loading,"close-on-press-escape":!e.loading,"show-close":!e.loading},on:{"update:visible":function(r){e.resDialogVisible=r},close:e.handleDialogClose}},[t("el-descriptions",{attrs:{border:"",column:2,labelStyle:{width:"100px"}}},[e.activeTab=="1"?t("el-descriptions-item",{attrs:{label:"\u7C7B\u578B"}},[e._v(" "+e._s(e.queryParam.project_type||"--")+" ")]):e._e(),e.activeTab=="1"?t("el-descriptions-item",{attrs:{label:"\u5E74\u4EFD"}},[e._v(" "+e._s(e.queryParam.year||"--")+" ")]):e._e(),e.activeTab=="1"?t("el-descriptions-item",{attrs:{label:"\u5173\u952E\u8BCD",span:"2"}},[e._v(" "+e._s(((a=e.queryParam.keywords)==null?void 0:a.join("\u3001"))||"--")+" ")]):e._e(),t("el-descriptions-item",{attrs:{label:"\u81EA\u5B9A\u4E49\u6761\u4EF6",span:"2"}},[t("div",{domProps:{innerHTML:e._s((o=e.queryParam)==null?void 0:o.question.replace(/\n/gi,"<br/>"))}})]),t("el-descriptions-item",{attrs:{label:"\u6C47\u7F16\u7ED3\u679C",span:"2"}},[e.assemblyTaskRunning&&e.currentTaskId?t("div",{staticClass:"task-progress"},[t("el-progress",{attrs:{percentage:e.taskProgress,status:e.progressStatus,"stroke-width":8}}),t("p",{staticClass:"progress-text"},[e._v(" "+e._s(e.taskProgressText)+" ")])],1):e._e(),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[t("div",{staticClass:"markdown ai-response-content",domProps:{innerHTML:e._s(e.md.render(e.processHistoryResponse(e.res||"")))}}),e.echart?t("iframe",{attrs:{frameborder:"0",srcdoc:"<style>*{margin:0;padding:0}</style>"+e.echart},on:{load:e.resizeIframe}}):e._e(),e.image?t("img",{attrs:{src:"data:image/png;base64,"+e.image}}):e._e()])])],1)],1),t("el-dialog",{attrs:{title:"\u8BE6\u60C5",width:"70%",visible:e.historyItemDialogVisible},on:{"update:visible":function(r){e.historyItemDialogVisible=r}}},[t("el-descriptions",{attrs:{border:"",column:2,labelStyle:{width:"100px"}}},[t("el-descriptions-item",{attrs:{label:"\u6C47\u7F16\u65B9\u5F0F"}},[e._v(" "+e._s(e.historyItem.type=="1"?"\u81EA\u5B9A\u4E49\u6C47\u7F16":"\u6A21\u677F\u6C47\u7F16")+" ")]),t("el-descriptions-item",{attrs:{label:"\u6C47\u7F16\u65F6\u95F4"}},[e._v(" "+e._s(e.formatTime(e.historyItem.time))+" ")]),e.historyItem.type=="1"?t("el-descriptions-item",{attrs:{label:"\u7C7B\u578B"}},[e._v(" "+e._s(((i=e.historyItem.req)==null?void 0:i.project_type)||"--")+" ")]):e._e(),e.historyItem.type=="1"?t("el-descriptions-item",{attrs:{label:"\u5E74\u4EFD"}},[e._v(" "+e._s(((l=e.historyItem.req)==null?void 0:l.year)||"--")+" ")]):e._e(),e.historyItem.type=="1"?t("el-descriptions-item",{attrs:{label:"\u5173\u952E\u8BCD",span:"2"}},[e._v(" "+e._s(((m=(n=e.historyItem.req)==null?void 0:n.keywords)==null?void 0:m.join("\u3001"))||"--")+" ")]):e._e(),t("el-descriptions-item",{attrs:{label:"\u81EA\u5B9A\u4E49\u6761\u4EF6",span:"2"}},[t("div",{staticClass:"pre"},[e._v(e._s((h=e.historyItem.req)==null?void 0:h.question))])]),t("el-descriptions-item",{attrs:{label:"\u6C47\u7F16\u7ED3\u679C",span:"2"}},[t("div",{staticClass:"markdown ai-response-content",domProps:{innerHTML:e._s(e.md.render(e.processHistoryResponse(((d=e.historyItem)==null?void 0:d.res)||"")))}}),e.echart?t("iframe",{attrs:{frameborder:"0",srcdoc:"<style>*{margin:0;padding:0}</style>"+e.historyItem.echart},on:{load:e.resizeIframe}}):e._e(),e.image?t("img",{attrs:{src:"data:image/png;base64,"+e.historyItem.image}}):e._e()])],1)],1)],1)},N=[],x=f(R,$,N,!1,null,"b956edda",null,null);const O=x.exports;export{O as default};
