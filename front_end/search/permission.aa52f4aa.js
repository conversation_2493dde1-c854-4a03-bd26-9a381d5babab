import{s as t}from"./index.87b88fdf.js";import"./vendor.056885aa.js";import"./element.65d3a407.js";import"./utils.7bf54f36.js";function u(e,r="read"){return t({url:`/api/permissions/check/${e}`,method:"get",params:{permission_type:r}})}function m(e){return t({url:"/api/permissions/check-document-access",method:"post",data:e})}function p(e){return t({url:"/api/documents/by-url",method:"get",params:{url:e}})}function a(e){return t({url:`/api/documents/by-doc-id/${e}`,method:"get"})}function c(e){return t({url:"/api/permissions/request",method:"post",data:e})}function d(e={}){return t({url:"/api/permissions/requests",method:"get",params:e})}function l(){return t({url:"/api/permissions/pending-reviews",method:"get"})}function h(e,r){return t({url:`/api/permissions/approve/${e}`,method:"post",data:r})}function f(e,r){return t({url:`/api/permissions/approve/${e}`,method:"post",data:{...r,approved:!1}})}function g(e){return t({url:`/api/permissions/requests/${e}/withdraw`,method:"post"})}function q(e){return t({url:`/api/permissions/requests/${e}`,method:"delete"})}function v(){return t({url:"/api/permissions/my-approved",method:"get"})}function P(e={}){return t({url:"/api/permissions/all-records",method:"get",params:e})}function R(){return t({url:"/api/permissions/department-stats",method:"get"})}function y(){return t({url:"/api/simple-auth/user-info",method:"get"})}function k(e){return t({url:"/api/permissions/revoke",method:"delete",data:e})}export{h as approvePermissionRequest,m as checkDocumentPermission,u as checkDocumentPermissionById,q as deletePermissionRequest,P as getAllPermissionRecords,y as getCurrentUser,R as getDepartmentStats,a as getDocumentByDocId,p as getDocumentByUrl,v as getMyApprovedRequests,l as getPendingReviews,d as getUserPermissionRequests,f as rejectPermissionRequest,k as revokePermission,c as submitPermissionRequest,g as withdrawPermissionRequest};
