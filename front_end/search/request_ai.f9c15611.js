import{a as l}from"./vendor.056885aa.js";import{e as s}from"./element.65d3a407.js";import{a as E}from"./index.87b88fdf.js";const p={VITE_BACKEND_URL:"http://*************:18888",VITE_VUE_APP_BASE_API:"/dev-api",VITE_VUE_APP_OTHER_API:"/api-s",VITE_VUE_APP_OTHER_PATH_API:"http://*************:18888/",VITE_FILE_BASE_URL:"/download?url=",VITE_ENV_NAME:"\u751F\u4EA7\u73AF\u5883",VITE_ENV_MODE:"production",VITE_DEBUG_MODE:"false",VITE_IFRAME_ALLOWED_ORIGINS:"https://*************,https://*************,http://localhost:8080,http://***********:8080",VITE_CSP_FRAME_ANCESTORS:"'self' http://localhost:* https://localhost:* http://***********:* https://***********:* http://*************:* https://*************:* http://*************:* https://*************:*",VITE_CSP_DEFAULT_SRC:"'self'",VITE_CSP_SCRIPT_SRC:"'self' 'unsafe-inline'",VITE_CSP_STYLE_SRC:"'self' 'unsafe-inline'",VITE_ENABLE_GZIP:"true",VITE_SERVICE_A_URL:"http://*************:8080",VITE_SERVICE_B_URL:"http://*************:8080",VITE_EXTRA_ORIGINS:"http://*************:8080,http://*************:8080",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0},n=l.create({baseURL:p.VITE_VUE_APP_OTHER_API,timeout:12e5});n.interceptors.request.use(async e=>{var o;if(!((o=e.url)==null?void 0:o.includes("/auth/token"))){const r=E.getters["user/hasToken"]?E.state.user.token:localStorage.getItem("authToken");r&&(e.headers.Authorization=`Bearer ${r}`)}return e.method==="get"&&(e.params={...e.params,_t:Date.now()}),e},e=>(console.log("AI Request error:",e),Promise.reject(e)));n.interceptors.response.use(e=>e.data,async e=>{var o,r;const{response:a}=e;if(a){const{status:i,data:t}=a;switch(i){case 401:console.log("AI\u670D\u52A1\u6536\u5230401\u9519\u8BEF\uFF0C\u6E05\u9664\u8BA4\u8BC1\u72B6\u6001\u5E76\u8DF3\u8F6C\u5230\u767B\u5F55\u9875"),s.exports.Message.error("\u767B\u5F55\u5DF2\u8FC7\u671F\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"),await E.dispatch("user/logout");try{const _=((o=window.vueApp)==null?void 0:o.$router)||((r=window.$vm)==null?void 0:r.$router);_?(console.log("\u4F7F\u7528Vue Router\u8DF3\u8F6C\u5230\u767B\u5F55\u9875"),_.push("/login")):(console.log("Vue Router\u4E0D\u53EF\u7528\uFF0C\u4F7F\u7528window.location\u8DF3\u8F6C\u5230\u767B\u5F55\u9875"),window.location.href="/login")}catch(_){console.error("\u8DEF\u7531\u8DF3\u8F6C\u5931\u8D25\uFF0C\u4F7F\u7528\u5907\u7528\u65B9\u6848:",_),window.location.href="/login"}break;case 403:break;case 404:s.exports.Message.error("\u8BF7\u6C42\u7684\u8D44\u6E90\u4E0D\u5B58\u5728");break;case 500:s.exports.Message.error("AI\u670D\u52A1\u5185\u90E8\u9519\u8BEF");break;default:s.exports.Message.error((t==null?void 0:t.message)||(t==null?void 0:t.detail)||"AI\u670D\u52A1\u8BF7\u6C42\u5931\u8D25")}}else e.code==="ECONNABORTED"?s.exports.Message.error("AI\u670D\u52A1\u8BF7\u6C42\u8D85\u65F6\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5"):s.exports.Message.error("AI\u670D\u52A1\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC");return Promise.reject(e)});export{n as a};
