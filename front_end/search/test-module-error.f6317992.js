import{n as s,i as a,c as l,b as n}from"./index.87b88fdf.js";import"./vendor.056885aa.js";import"./element.65d3a407.js";import"./utils.7bf54f36.js";const c={name:"TestModuleError",data(){return{errorLog:""}},methods:{addLog(o){const r=new Date().toLocaleTimeString();this.errorLog+=`[${r}] ${o}
`},clearLog(){this.errorLog=""},testDynamicImportError(){this.addLog("\u5F00\u59CB\u6D4B\u8BD5\u52A8\u6001\u5BFC\u5165\u5931\u8D25...");const o=new Error("Failed to fetch dynamically imported module: http://example.com/assets/test.js");this.addLog(`\u9519\u8BEF\u7C7B\u578B\u68C0\u67E5: ${a(o)?"\u2705 \u8BC6\u522B\u4E3A\u6A21\u5757\u9519\u8BEF":"\u274C \u672A\u8BC6\u522B"}`);try{this.addLog("\u8C03\u7528\u9519\u8BEF\u5904\u7406\u5668..."),console.warn("\u6D4B\u8BD5\u6A21\u5F0F\uFF1A\u6A21\u62DF\u5904\u7406\u52A8\u6001\u5BFC\u5165\u9519\u8BEF"),this.addLog("\u2705 \u9519\u8BEF\u5904\u7406\u5668\u8C03\u7528\u6210\u529F\uFF08\u6D4B\u8BD5\u6A21\u5F0F\u4E0B\u4E0D\u4F1A\u5B9E\u9645\u5237\u65B0\uFF09")}catch(r){this.addLog(`\u274C \u9519\u8BEF\u5904\u7406\u5931\u8D25: ${r.message}`)}},testChunkLoadError(){this.addLog("\u5F00\u59CB\u6D4B\u8BD5\u4EE3\u7801\u5757\u52A0\u8F7D\u5931\u8D25...");const o=new Error("Loading chunk 123 failed.");this.addLog(`\u9519\u8BEF\u7C7B\u578B\u68C0\u67E5: ${a(o)?"\u2705 \u8BC6\u522B\u4E3A\u6A21\u5757\u9519\u8BEF":"\u274C \u672A\u8BC6\u522B"}`);try{this.addLog("\u8C03\u7528\u9519\u8BEF\u5904\u7406\u5668..."),console.warn("\u6D4B\u8BD5\u6A21\u5F0F\uFF1A\u6A21\u62DF\u5904\u7406\u4EE3\u7801\u5757\u52A0\u8F7D\u9519\u8BEF"),this.addLog("\u2705 \u9519\u8BEF\u5904\u7406\u5668\u8C03\u7528\u6210\u529F\uFF08\u6D4B\u8BD5\u6A21\u5F0F\u4E0B\u4E0D\u4F1A\u5B9E\u9645\u5237\u65B0\uFF09")}catch(r){this.addLog(`\u274C \u9519\u8BEF\u5904\u7406\u5931\u8D25: ${r.message}`)}},async testRetryableImport(){this.addLog("\u5F00\u59CB\u6D4B\u8BD5\u91CD\u8BD5\u673A\u5236...");let o=0;const t=n(()=>{if(o++,this.addLog(`\u7B2C${o}\u6B21\u5C1D\u8BD5\u5BFC\u5165...`),o<3)throw new Error("Failed to fetch dynamically imported module: mock error");return Promise.resolve({default:"Mock Component"})},3,500);try{const e=await t();this.addLog(`\u2705 \u91CD\u8BD5\u6210\u529F\uFF01\u7ED3\u679C: ${JSON.stringify(e)}`)}catch(e){this.addLog(`\u274C \u91CD\u8BD5\u5931\u8D25: ${e.message}`)}},clearCache(){this.addLog("\u5F00\u59CB\u6E05\u7406\u5E94\u7528\u7F13\u5B58...");try{l(),this.addLog("\u2705 \u5E94\u7528\u7F13\u5B58\u6E05\u7406\u5B8C\u6210")}catch(o){this.addLog(`\u274C \u7F13\u5B58\u6E05\u7406\u5931\u8D25: ${o.message}`)}}}};var d=function(){var r=this,t=r._self._c;return t("div",{staticClass:"test-module-error"},[t("el-card",[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[r._v("\u6A21\u5757\u9519\u8BEF\u5904\u7406\u6D4B\u8BD5")])]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("h3",[r._v("\u6D4B\u8BD5\u573A\u666F")]),t("el-button",{attrs:{type:"danger"},on:{click:r.testDynamicImportError}},[r._v(" \u6A21\u62DF\u52A8\u6001\u5BFC\u5165\u5931\u8D25 ")]),t("br"),t("br"),t("el-button",{attrs:{type:"warning"},on:{click:r.testChunkLoadError}},[r._v(" \u6A21\u62DF\u4EE3\u7801\u5757\u52A0\u8F7D\u5931\u8D25 ")]),t("br"),t("br"),t("el-button",{attrs:{type:"primary"},on:{click:r.testRetryableImport}},[r._v(" \u6D4B\u8BD5\u91CD\u8BD5\u673A\u5236 ")]),t("br"),t("br"),t("el-button",{attrs:{type:"info"},on:{click:r.clearCache}},[r._v(" \u6E05\u7406\u5E94\u7528\u7F13\u5B58 ")])],1),t("el-col",{attrs:{span:12}},[t("h3",[r._v("\u9519\u8BEF\u65E5\u5FD7")]),t("el-input",{attrs:{type:"textarea",rows:10,readonly:"",placeholder:"\u9519\u8BEF\u4FE1\u606F\u5C06\u663E\u793A\u5728\u8FD9\u91CC..."},model:{value:r.errorLog,callback:function(e){r.errorLog=e},expression:"errorLog"}}),t("br"),t("br"),t("el-button",{attrs:{size:"small"},on:{click:r.clearLog}},[r._v("\u6E05\u7A7A\u65E5\u5FD7")])],1)],1),t("el-divider"),t("el-alert",{attrs:{title:"\u4F7F\u7528\u8BF4\u660E",type:"info",closable:!1,"show-icon":""}},[t("p",[r._v("\u8FD9\u4E2A\u9875\u9762\u7528\u4E8E\u6D4B\u8BD5\u751F\u4EA7\u73AF\u5883\u4E2D\u7684\u6A21\u5757\u52A0\u8F7D\u9519\u8BEF\u5904\u7406\u673A\u5236\uFF1A")]),t("ul",[t("li",[t("strong",[r._v("\u6A21\u62DF\u52A8\u6001\u5BFC\u5165\u5931\u8D25\uFF1A")]),r._v('\u6A21\u62DF "Failed to fetch dynamically imported module" \u9519\u8BEF')]),t("li",[t("strong",[r._v("\u6A21\u62DF\u4EE3\u7801\u5757\u52A0\u8F7D\u5931\u8D25\uFF1A")]),r._v('\u6A21\u62DF "Loading chunk" \u9519\u8BEF')]),t("li",[t("strong",[r._v("\u6D4B\u8BD5\u91CD\u8BD5\u673A\u5236\uFF1A")]),r._v("\u6D4B\u8BD5\u5E26\u91CD\u8BD5\u529F\u80FD\u7684\u52A8\u6001\u5BFC\u5165")]),t("li",[t("strong",[r._v("\u6E05\u7406\u5E94\u7528\u7F13\u5B58\uFF1A")]),r._v("\u6E05\u7406\u53EF\u80FD\u5BFC\u81F4\u7F13\u5B58\u95EE\u9898\u7684\u672C\u5730\u5B58\u50A8")])]),t("p",[t("strong",[r._v("\u6CE8\u610F\uFF1A")]),r._v("\u5728\u751F\u4EA7\u73AF\u5883\u4E2D\uFF0C\u8FD9\u4E9B\u9519\u8BEF\u901A\u5E38\u4F1A\u89E6\u53D1\u9875\u9762\u5237\u65B0\u4EE5\u89E3\u51B3\u7F13\u5B58\u95EE\u9898\u3002")])])],1)],1)},i=[],m=s(c,d,i,!1,null,"41c664f5",null,null);const u=m.exports;export{u as default};
