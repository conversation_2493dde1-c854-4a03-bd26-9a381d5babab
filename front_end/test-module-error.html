<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块错误处理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #66b1ff;
        }
        .danger {
            background: #F56C6C;
        }
        .danger:hover {
            background: #f78989;
        }
        .log-area {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .alert {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 模块错误处理测试</h1>
        
        <div class="alert">
            <strong>说明：</strong>这个页面用于测试生产环境中的模块加载错误处理机制。
            点击下面的按钮来模拟不同类型的错误。
        </div>
        
        <h3>测试按钮</h3>
        <button class="test-button danger" onclick="testDynamicImportError()">
            模拟动态导入失败
        </button>
        <button class="test-button danger" onclick="testChunkLoadError()">
            模拟代码块加载失败
        </button>
        <button class="test-button" onclick="testRouterPush()">
            测试路由跳转
        </button>
        <button class="test-button" onclick="clearLog()">
            清空日志
        </button>
        
        <h3>错误日志</h3>
        <div id="logArea" class="log-area">
            等待测试...
        </div>
    </div>

    <script>
        let logArea = document.getElementById('logArea');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            
            // 同时输出到控制台
            if (type === 'error') {
                console.error(message);
            } else if (type === 'warn') {
                console.warn(message);
            } else {
                console.log(message);
            }
        }
        
        function clearLog() {
            logArea.textContent = '日志已清空...\n';
        }
        
        function testDynamicImportError() {
            addLog('🧪 开始测试动态导入失败...', 'info');
            
            // 创建一个模拟的错误事件
            const mockError = new Error('Failed to fetch dynamically imported module: http://example.com/assets/test.js');
            
            addLog('📝 模拟错误: ' + mockError.message, 'warn');
            
            // 触发全局错误事件
            window.dispatchEvent(new ErrorEvent('error', {
                error: mockError,
                message: mockError.message,
                filename: 'test.js',
                lineno: 1,
                colno: 1
            }));
            
            addLog('✅ 错误事件已触发，检查控制台查看处理结果', 'info');
        }
        
        function testChunkLoadError() {
            addLog('🧪 开始测试代码块加载失败...', 'info');
            
            const mockError = new Error('Loading chunk 123 failed.');
            
            addLog('📝 模拟错误: ' + mockError.message, 'warn');
            
            // 触发Promise拒绝事件
            window.dispatchEvent(new PromiseRejectionEvent('unhandledrejection', {
                promise: Promise.reject(mockError),
                reason: mockError
            }));
            
            addLog('✅ Promise拒绝事件已触发，检查控制台查看处理结果', 'info');
        }
        
        function testRouterPush() {
            addLog('🧪 测试路由跳转到不存在的模块...', 'info');
            
            // 尝试跳转到一个不存在的路由
            try {
                // 模拟Vue Router的行为
                const fakeRouter = {
                    push: function(path) {
                        addLog('🔄 尝试跳转到: ' + path, 'info');
                        
                        // 模拟动态导入失败
                        setTimeout(() => {
                            const error = new Error('Failed to fetch dynamically imported module: http://example.com/assets/nonexistent.js');
                            addLog('❌ 路由跳转时发生模块加载错误: ' + error.message, 'error');
                            
                            // 触发错误处理
                            window.dispatchEvent(new ErrorEvent('error', {
                                error: error,
                                message: error.message
                            }));
                        }, 500);
                    }
                };
                
                fakeRouter.push('/nonexistent-route');
            } catch (error) {
                addLog('❌ 路由测试失败: ' + error.message, 'error');
            }
        }
        
        // 监听全局错误事件
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message && 
                (event.error.message.includes('Failed to fetch dynamically imported module') ||
                 event.error.message.includes('Loading chunk'))) {
                addLog('🔍 全局错误处理器捕获到模块错误: ' + event.error.message, 'warn');
                addLog('🔧 在实际应用中，这里会触发页面刷新或路由跳转', 'info');
            }
        });
        
        // 监听Promise拒绝事件
        window.addEventListener('unhandledrejection', function(event) {
            if (event.reason && event.reason.message &&
                (event.reason.message.includes('Failed to fetch dynamically imported module') ||
                 event.reason.message.includes('Loading chunk'))) {
                addLog('🔍 Promise拒绝处理器捕获到模块错误: ' + event.reason.message, 'warn');
                addLog('🔧 在实际应用中，这里会触发页面刷新', 'info');
            }
        });
        
        // 页面加载完成
        addLog('✅ 测试页面已加载，可以开始测试', 'info');
        addLog('💡 提示：打开浏览器开发者工具查看详细的错误处理日志', 'info');
    </script>
</body>
</html>
