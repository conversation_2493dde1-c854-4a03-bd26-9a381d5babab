# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=मागील पृष्ठ
previous_label=मागील
next.title=पुढील पृष्ठ
next_label=पुढील

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=पृष्ठ
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}}पैकी
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pagesCount}} पैकी {{pageNumber}})

zoom_out.title=छोटे करा
zoom_out_label=छोटे करा
zoom_in.title=मोठे करा
zoom_in_label=मोठे करा
zoom.title=लहान किंवा मोठे करा
presentation_mode.title=प्रस्तुतिकरण मोडचा वापर करा
presentation_mode_label=प्रस्तुतिकरण मोड
open_file.title=फाइल उघडा
open_file_label=उघडा
print.title=छपाई करा
print_label=छपाई करा

# Secondary toolbar and context menu
tools.title=साधने
tools_label=साधने
first_page.title=पहिल्या पृष्ठावर जा
first_page_label=पहिल्या पृष्ठावर जा
last_page.title=शेवटच्या पृष्ठावर जा
last_page_label=शेवटच्या पृष्ठावर जा
page_rotate_cw.title=घड्याळाच्या काट्याच्या दिशेने फिरवा
page_rotate_cw_label=घड्याळाच्या काट्याच्या दिशेने फिरवा
page_rotate_ccw.title=घड्याळाच्या काट्याच्या उलट दिशेने फिरवा
page_rotate_ccw_label=घड्याळाच्या काट्याच्या उलट दिशेने फिरवा

cursor_text_select_tool.title=मजकूर निवड साधन कार्यान्वयीत करा
cursor_text_select_tool_label=मजकूर निवड साधन
cursor_hand_tool.title=हात साधन कार्यान्वित करा
cursor_hand_tool_label=हस्त साधन

scroll_vertical.title=अनुलंब स्क्रोलिंग वापरा
scroll_vertical_label=अनुलंब स्क्रोलिंग
scroll_horizontal.title=क्षैतिज स्क्रोलिंग वापरा
scroll_horizontal_label=क्षैतिज स्क्रोलिंग


# Document properties dialog box
document_properties.title=दस्तऐवज गुणधर्म…
document_properties_label=दस्तऐवज गुणधर्म…
document_properties_file_name=फाइलचे नाव:
document_properties_file_size=फाइल आकार:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} बाइट्स)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} बाइट्स)
document_properties_title=शिर्षक:
document_properties_author=लेखक:
document_properties_subject=विषय:
document_properties_keywords=मुख्यशब्द:
document_properties_creation_date=निर्माण दिनांक:
document_properties_modification_date=दुरूस्ती दिनांक:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=निर्माता:
document_properties_producer=PDF निर्माता:
document_properties_version=PDF आवृत्ती:
document_properties_page_count=पृष्ठ संख्या:
document_properties_page_size=पृष्ठ आकार:
document_properties_page_size_unit_inches=इंच
document_properties_page_size_unit_millimeters=मीमी
document_properties_page_size_orientation_portrait=उभी मांडणी
document_properties_page_size_orientation_landscape=आडवे
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=जलद वेब दृष्य:
document_properties_linearized_yes=हो
document_properties_linearized_no=नाही
document_properties_close=बंद करा

print_progress_message=छपाई करीता पृष्ठ तयार करीत आहे…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=रद्द करा

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=बाजूचीपट्टी टॉगल करा
toggle_sidebar_label=बाजूचीपट्टी टॉगल करा
document_outline.title=दस्तऐवज बाह्यरेखा दर्शवा (विस्तृत करण्यासाठी दोनवेळा क्लिक करा /सर्व घटक दाखवा)
document_outline_label=दस्तऐवज रूपरेषा
attachments.title=जोडपत्र दाखवा
attachments_label=जोडपत्र
thumbs.title=थंबनेल्स् दाखवा
thumbs_label=थंबनेल्स्
findbar.title=दस्तऐवजात शोधा
findbar_label=शोधा

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=पृष्ठ {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=पृष्ठाचे थंबनेल {{page}}

# Find panel button title and messages
find_input.title=शोधा
find_input.placeholder=दस्तऐवजात शोधा…
find_previous.title=वाकप्रयोगची मागील घटना शोधा
find_previous_label=मागील
find_next.title=वाकप्रयोगची पुढील घटना शोधा
find_next_label=पुढील
find_highlight=सर्व ठळक करा
find_match_case_label=आकार जुळवा
find_entire_word_label=संपूर्ण शब्द
find_reached_top=दस्तऐवजाच्या शीर्षकास पोहचले, तळपासून पुढे
find_reached_bottom=दस्तऐवजाच्या तळाला पोहचले, शीर्षकापासून पुढे
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{total}} पैकी {{current}} सुसंगत
find_match_count[two]={{total}} पैकी {{current}} सुसंगत
find_match_count[few]={{total}} पैकी {{current}} सुसंगत
find_match_count[many]={{total}} पैकी {{current}} सुसंगत
find_match_count[other]={{total}} पैकी {{current}} सुसंगत
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]={{limit}} पेक्षा अधिक जुळण्या
find_match_count_limit[one]={{limit}} पेक्षा अधिक जुळण्या
find_match_count_limit[two]={{limit}} पेक्षा अधिक जुळण्या
find_match_count_limit[few]={{limit}} पेक्षा अधिक जुळण्या
find_match_count_limit[many]={{limit}} पेक्षा अधिक जुळण्या
find_match_count_limit[other]={{limit}} पेक्षा अधिक जुळण्या
find_not_found=वाकप्रयोग आढळले नाही

# Predefined zoom values
page_scale_width=पृष्ठाची रूंदी
page_scale_fit=पृष्ठ बसवा
page_scale_auto=स्वयं लाहन किंवा मोठे करणे
page_scale_actual=प्रत्यक्ष आकार
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

loading_error=PDF लोड करतेवेळी त्रुटी आढळली.
invalid_file_error=अवैध किंवा दोषीत PDF फाइल.
missing_file_error=न आढळणारी PDF फाइल.
unexpected_response_error=अनपेक्षित सर्व्हर प्रतिसाद.

rendering_error=पृष्ठ दाखवतेवेळी त्रुटी आढळली.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} टिपण्णी]
password_label=ही PDF फाइल उघडण्याकरिता पासवर्ड द्या.
password_invalid=अवैध पासवर्ड. कृपया पुन्हा प्रयत्न करा.
password_ok=ठीक आहे
password_cancel=रद्द करा

printing_not_supported=सावधानता: या ब्राउझरतर्फे छपाइ पूर्णपणे समर्थीत नाही.
printing_not_ready=सावधानता: छपाईकरिता PDF पूर्णतया लोड झाले नाही.
web_fonts_disabled=वेब टंक असमर्थीत आहेत: एम्बेडेड PDF टंक वापर अशक्य.

