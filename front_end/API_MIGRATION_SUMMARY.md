# API接口迁移总结

## 🎯 迁移目标

将前端所有API调用统一迁移到`/api/simple-auth`接口，删除对`/auth`路由的依赖。

## ✅ 已完成的迁移

### 1. 用户信息相关接口

| 原接口 | 新接口 | 状态 | 文件 |
|--------|--------|------|------|
| `/auth/users/me` | `/api/simple-auth/user-info` | ✅ 已修复 | `auth.js`, `permission.js` |
| `/auth/users/me/permissions` | `/api/simple-auth/user-info` | ✅ 已修复 | `auth.js` |
| `/auth/logout` | `/api/simple-auth/logout` | ✅ 已修复 | `auth.js` |

### 2. HTML文件中的接口

| 文件 | 原接口 | 新接口 | 状态 |
|------|--------|--------|------|
| `login.html` | `/auth/users/me` | `/api/simple-auth/user-info` | ✅ 已修复 |
| `dashboard.html` | `/auth/users/me` | `/api/simple-auth/user-info` | ✅ 已修复 |

### 3. Vue组件中的接口

| 组件 | 原接口 | 新接口 | 状态 |
|------|--------|--------|------|
| `PermissionRequest.vue` | `/auth/permission-requests` | `/api/permissions/request` | ✅ 已修复 |
| `PermissionRequest.vue` | `/auth/permission-requests/my` | `/api/permissions/my-requests` | ✅ 已修复 |
| `PermissionApproval.vue` | `/auth/permission-requests/review` | `/api/permissions/pending-requests` | ✅ 已修复 |

## ⚠️ 需要进一步处理的接口

### 1. 认证相关接口（可能需要保留）

这些接口可能在后端确实存在，需要确认是否迁移：

| 接口 | 用途 | 建议 |
|------|------|------|
| `/auth/token` | 用户登录 | 🔍 需要确认后端是否支持 |
| `/auth/register` | 用户注册 | 🔍 需要确认后端是否支持 |
| `/auth/refresh` | 刷新token | 🔍 可能迁移到 `/api/simple-auth/refresh-token` |

### 2. 权限管理接口

这些接口可能需要迁移到统一的权限管理API：

| 原接口 | 建议新接口 | 状态 |
|--------|------------|------|
| `/auth/permission-requests` | `/api/permissions/request` | ⏳ 待处理 |
| `/auth/permission-requests/my` | `/api/permissions/my-requests` | ⏳ 待处理 |
| `/auth/permission-requests/review` | `/api/permissions/pending-requests` | ⏳ 待处理 |
| `/auth/permission-requests/{id}/review` | `/api/permissions/approve/{id}` | ⏳ 待处理 |
| `/auth/permission-requests/{id}/withdraw` | `/api/permissions/requests/{id}/withdraw` | ⏳ 待处理 |
| `/auth/permission-requests/{id}` | `/api/permissions/requests/{id}` | ⏳ 待处理 |

### 3. 其他功能接口

| 原接口 | 用途 | 建议 |
|--------|------|------|
| `/auth/user/password` | 修改密码 | 🔍 需要确认是否迁移 |
| `/auth/user/info` | 更新用户信息 | 🔍 可能迁移到 `/api/simple-auth/update-user` |
| `/auth/check/username` | 检查用户名 | 🔍 需要确认是否迁移 |
| `/auth/check/email` | 检查邮箱 | 🔍 需要确认是否迁移 |
| `/auth/departments` | 获取部门列表 | 🔍 可能迁移到 `/api/departments` |
| `/auth/documents/{id}/permissions/{type}` | 检查文档权限 | 🔍 已有 `/api/permissions/check-document-access` |

## 🚨 当前问题

### 1. 权限检查接口401错误

从错误日志可以看到：
```
POST http://***********:8080/dev-api/api/permissions/check-document-access 401 (Unauthorized)
```

**可能原因：**
- JWT token验证失败
- 用户不在数据库中
- 权限接口认证逻辑有问题

### 2. 用户信息接口404错误

```
GET http://***********:8080/auth/users/me 404 (Not Found)
```

**解决方案：**
- ✅ 已修复：将所有 `/auth/users/me` 改为 `/api/simple-auth/user-info`

## 🔧 修复建议

### 1. 立即修复

优先修复这些高频使用的接口：

```javascript
// 在 auth.js 中添加或修改
export function getUserInfo() {
  return request({
    url: '/api/simple-auth/user-info',  // ✅ 已修复
    method: 'get'
  })
}

export function logout() {
  return request({
    url: '/api/simple-auth/logout',     // ✅ 已修复
    method: 'post'
  })
}
```

### 2. 权限接口统一

建议创建统一的权限管理API：

```javascript
// 新建 permission-simple.js
export function checkDocumentPermission(docId, permissionType) {
  return request({
    url: '/api/permissions/check-document-access',
    method: 'post',
    data: { doc_id: docId, permission_type: permissionType }
  })
}

export function requestPermission(data) {
  return request({
    url: '/api/permissions/request',
    method: 'post',
    data
  })
}
```

### 3. 后端接口确认

需要确认后端是否提供以下接口：

- ✅ `/api/simple-auth/user-info` - 获取用户信息
- ✅ `/api/simple-auth/logout` - 用户登出
- ⚠️ `/api/simple-auth/refresh-token` - 刷新token
- ⚠️ `/api/permissions/check-document-access` - 检查文档权限

## 📋 下一步行动

### 1. 测试当前修复

1. 重新构建前端：`npm run build`
2. 测试用户信息获取
3. 测试权限检查功能
4. 测试登出功能

### 2. 继续迁移

根据测试结果，继续迁移剩余的接口：

1. 权限管理相关接口
2. 用户管理相关接口
3. 部门管理相关接口

### 3. 清理工作

1. 删除不再使用的 `/auth` 接口调用
2. 更新API文档
3. 添加错误处理和日志

## 🎯 预期结果

完成迁移后，前端应该：

1. ✅ 所有用户信息通过 `/api/simple-auth/user-info` 获取
2. ✅ 所有认证操作通过 `/api/simple-auth/*` 处理
3. ✅ 所有权限检查通过 `/api/permissions/*` 处理
4. ❌ 不再有 `/auth/*` 接口调用
5. ✅ 401/404错误得到解决

## 💡 总结

当前已经修复了最关键的用户信息和认证相关接口，这应该能解决大部分的401和404错误。剩余的接口需要根据后端实际支持情况进行逐步迁移。
