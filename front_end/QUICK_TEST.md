# 认证系统快速测试

## 🚀 立即测试步骤

### 1. 清除浏览器数据
打开浏览器开发者工具（F12），在控制台执行：
```javascript
// 清除所有认证相关数据
localStorage.clear();
sessionStorage.clear();
console.log('✅ 已清除所有本地数据');
```

### 2. 刷新页面
按 `Ctrl+F5` 或 `Cmd+Shift+R` 强制刷新页面

### 3. 观察控制台输出
应该看到类似以下的输出：
```
开始初始化应用...
认证状态初始化完成
历史记录初始化完成
应用初始化完成
路由守卫检查: {
  to: "/",
  from: "/",
  isLoggedIn: false,
  hasToken: false,
  requiresAuth: true,
  hideForAuth: false,
  isAppInitialized: true
}
未登录用户访问受保护页面，重定向到登录页
```

### 4. 检查页面跳转
- 如果看到登录页面 ✅ **认证系统工作正常**
- 如果仍在首页 ❌ **需要进一步调试**

## 🔧 故障排除

### 问题1: 控制台没有输出
**可能原因**: 
- JavaScript错误阻止了应用启动
- 文件路径错误

**解决方案**:
```javascript
// 检查是否有JavaScript错误
console.error('检查是否有错误信息');

// 检查Vue是否正确加载
console.log('Vue:', typeof Vue);
console.log('Router:', typeof VueRouter);
```

### 问题2: 看到"等待应用初始化完成..."但没有后续输出
**可能原因**: 
- Store初始化失败
- 异步操作卡住

**解决方案**:
```javascript
// 手动检查store状态
console.log('Store:', window.$vm?.$store);
console.log('User state:', window.$vm?.$store.state.user);
```

### 问题3: 路由守卫检查显示但没有重定向
**可能原因**: 
- 路由配置错误
- next()函数调用问题

**解决方案**:
```javascript
// 手动测试路由跳转
window.$vm?.$router.push('/login');
```

## 🧪 手动测试脚本

在浏览器控制台执行以下脚本进行完整测试：

```javascript
// 完整的认证系统测试脚本
async function testAuthSystem() {
  console.log('🧪 开始认证系统测试...');
  
  // 1. 清除数据
  localStorage.clear();
  console.log('✅ 1. 已清除localStorage');
  
  // 2. 检查初始状态
  const vm = window.$vm || document.querySelector('#app').__vue__;
  if (!vm) {
    console.error('❌ Vue实例未找到');
    return;
  }
  
  console.log('✅ 2. Vue实例正常');
  
  // 3. 检查store状态
  const userState = vm.$store.state.user;
  console.log('📊 3. 用户状态:', {
    token: !!userState.token,
    isLoggedIn: userState.isLoggedIn,
    hasToken: vm.$store.getters['user/hasToken'],
    isLoggedInGetter: vm.$store.getters['user/isLoggedIn']
  });
  
  // 4. 测试路由跳转
  console.log('🔄 4. 测试路由跳转到首页...');
  vm.$router.push('/');
  
  // 等待路由跳转完成
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 5. 检查当前路由
  const currentRoute = vm.$route.path;
  console.log('📍 5. 当前路由:', currentRoute);
  
  if (currentRoute === '/login') {
    console.log('✅ 认证系统工作正常！未登录用户被重定向到登录页');
  } else {
    console.log('❌ 认证系统可能有问题，当前路由:', currentRoute);
  }
  
  // 6. 模拟登录测试
  console.log('🔐 6. 模拟登录状态...');
  const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IlRlc3QgVXNlciIsImV4cCI6' + (Math.floor(Date.now() / 1000) + 3600) + '}.test';
  
  localStorage.setItem('access_token', mockToken);
  localStorage.setItem('user_info', JSON.stringify({
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    nick_name: '测试用户'
  }));
  
  // 手动更新store状态
  vm.$store.commit('user/SET_TOKEN', mockToken);
  vm.$store.commit('user/SET_USER_INFO', {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    nick_name: '测试用户'
  });
  vm.$store.commit('user/SET_LOGIN_STATUS', true);
  
  console.log('✅ 已设置模拟登录状态');
  
  // 7. 测试已登录状态的路由跳转
  console.log('🔄 7. 测试已登录用户访问登录页...');
  vm.$router.push('/login');
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const finalRoute = vm.$route.path;
  console.log('📍 最终路由:', finalRoute);
  
  if (finalRoute === '/') {
    console.log('✅ 已登录用户被正确重定向到首页');
  } else {
    console.log('❌ 已登录用户重定向可能有问题');
  }
  
  console.log('🎉 认证系统测试完成！');
}

// 运行测试
testAuthSystem();
```

## 📋 预期结果

正常工作的认证系统应该表现为：

1. **未登录状态**:
   - 访问 `/` → 自动跳转到 `/login`
   - 访问 `/search` → 自动跳转到 `/login?redirect=/search`
   - 可以正常访问 `/login` 和 `/register`

2. **已登录状态**:
   - 访问 `/login` → 自动跳转到 `/`
   - 访问 `/register` → 自动跳转到 `/`
   - 可以正常访问所有受保护页面

3. **控制台输出**:
   - 应用初始化日志
   - 路由守卫检查日志
   - 重定向操作日志

## 🆘 如果测试失败

1. **检查文件是否正确保存**
2. **重启开发服务器** (`npm run dev`)
3. **清除浏览器缓存**
4. **检查是否有JavaScript错误**
5. **参考 DEBUG_GUIDE.md 进行详细调试**

如果问题仍然存在，请提供控制台的完整输出信息。
