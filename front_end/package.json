{"name": "es_search", "version": "0.0.0", "scripts": {"dev": "vite", "build": "BUILD_ENV=production node --max-old-space-size=4096 ./node_modules/.bin/vite build", "build:prod": "BUILD_ENV=production node --max-old-space-size=4096 ./node_modules/.bin/vite build", "build:test": "BUILD_ENV=test node --max-old-space-size=4096 ./node_modules/.bin/vite build", "preview": "vite preview --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "test:module-error": "echo '访问 http://localhost:4173/test-module-error 测试模块错误处理'"}, "dependencies": {"@vue-office/docx": "^1.3.0", "@vue-office/excel": "^1.4.0", "@vue-office/pdf": "^1.5.3", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^1.4.0", "dayjs": "^1.11.9", "element-ui": "^2.15.13", "localforage": "^1.10.0", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "nprogress": "^0.2.0", "pinia": "^2.0.16", "vue": "^2.7.7", "vue-router": "^3.5.4", "vuex": "^3.6.2"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.0", "@vitejs/plugin-legacy": "^2.0.0", "@vitejs/plugin-vue2": "^1.1.2", "@vue/eslint-config-prettier": "^7.0.0", "eslint": "^8.5.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^3.0.0", "sass": "^1.64.0", "terser": "^5.14.2", "vite": "^3.0.2"}}