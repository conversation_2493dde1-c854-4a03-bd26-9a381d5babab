# 用户认证系统文档

## 概述

本文档描述了为front_end项目实现的完整用户认证系统，包括登录、注册、token管理和权限控制。

## 功能特性

### 🔐 核心认证功能
- **用户登录**: 支持用户名/密码登录，记住我功能
- **用户注册**: 完整的注册流程，包含表单验证
- **密码重置**: 通过邮箱验证码重置密码
- **自动登出**: token过期自动登出

### 🔄 Token管理
- **JWT Token**: 使用JWT进行身份认证
- **自动刷新**: token即将过期时自动刷新
- **持久化存储**: 登录状态在页面刷新后保持
- **安全处理**: token过期自动清理和重定向

### 🛡️ 安全特性
- **路由守卫**: 未登录用户自动重定向到登录页
- **请求拦截**: 所有API请求自动携带token
- **错误处理**: 统一的认证错误处理
- **状态管理**: 集中的用户状态管理

## 文件结构

```
front_end/src/
├── api/
│   └── auth.js                 # 认证相关API接口
├── components/
│   └── UserInfo.vue           # 用户信息组件
├── store/
│   └── modules/
│       └── user.js            # 用户状态管理模块
├── utils/
│   ├── auth.js                # 认证工具函数
│   ├── request.js             # 主API请求拦截器
│   └── request_other.js       # 辅助API请求拦截器
├── views/
│   ├── Login.vue              # 登录页面
│   └── Register.vue           # 注册页面
└── router/
    └── index.js               # 路由配置和守卫
```

## API接口

### 认证接口 (auth.js)

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| login | POST | /auth/login | 用户登录 |
| register | POST | /auth/register | 用户注册 |
| refreshToken | POST | /auth/refresh | 刷新token |
| getUserInfo | GET | /auth/user/info | 获取用户信息 |
| logout | POST | /auth/logout | 用户登出 |
| changePassword | PUT | /auth/user/password | 修改密码 |
| updateUserInfo | PUT | /auth/user/info | 更新用户信息 |

### 请求/响应格式

#### 登录请求
```json
{
  "username": "string",
  "password": "string",
  "remember": boolean
}
```

#### 登录响应
```json
{
  "access_token": "string",
  "refresh_token": "string",
  "user_info": {
    "id": "number",
    "username": "string",
    "email": "string",
    "nick_name": "string",
    "phone": "string",
    "dept_id": "number",
    "dept_name": "string"
  }
}
```

## 使用指南

### 1. 页面访问控制

所有页面默认需要登录，除了登录和注册页面：

```javascript
// 在路由meta中配置
{
  path: '/some-page',
  meta: {
    requiresAuth: true,  // 需要登录 (默认)
    hideForAuth: false   // 已登录用户是否隐藏
  }
}
```

### 2. 在组件中使用用户信息

```vue
<template>
  <div>
    <p>欢迎，{{ userName }}！</p>
    <p>邮箱：{{ userEmail }}</p>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  computed: {
    ...mapGetters('user', ['userName', 'userEmail', 'isLoggedIn'])
  }
}
</script>
```

### 3. 手动登出

```javascript
// 在组件中
async logout() {
  await this.$store.dispatch('user/logout')
  this.$router.push('/login')
}
```

### 4. 检查登录状态

```javascript
// 在组件中
if (this.$store.getters['user/isLoggedIn']) {
  // 用户已登录
}
```

## 配置说明

### 环境变量

在`.env.development`和`.env.production`中配置：

```bash
# 认证服务API地址
VITE_AUTH_API = 'http://localhost:8000/'
```

### Token配置

在`store/modules/user.js`中可以配置：

- Token存储键名
- 自动刷新时机
- 登录状态管理

## 安全考虑

### 1. Token安全
- Access Token短期有效（建议1小时）
- Refresh Token长期有效（建议7天）
- Token存储在localStorage中
- 页面关闭时可选择清除token

### 2. 请求安全
- 所有API请求自动携带Authorization头
- Token过期自动处理
- 网络错误统一处理

### 3. 路由安全
- 未登录用户无法访问受保护页面
- 已登录用户无法访问登录/注册页面
- 登录后自动跳转到原访问页面

## 自定义扩展

### 1. 添加新的认证方式

在`api/auth.js`中添加新的API接口：

```javascript
export function socialLogin(provider, token) {
  return request({
    url: `/auth/social/${provider}`,
    method: 'post',
    data: { token }
  })
}
```

### 2. 扩展用户信息

在`store/modules/user.js`中扩展用户状态：

```javascript
const state = {
  // 现有状态...
  permissions: [],
  roles: []
}
```

### 3. 自定义权限检查

创建权限检查工具函数：

```javascript
// utils/permission.js
export function hasPermission(permission) {
  const permissions = store.state.user.permissions
  return permissions.includes(permission)
}
```

## 故障排除

### 常见问题

1. **登录后页面空白**
   - 检查token是否正确存储
   - 检查用户信息是否正确获取

2. **Token自动刷新失败**
   - 检查refresh token是否有效
   - 检查网络连接
   - 检查API端点配置

3. **路由守卫不生效**
   - 检查路由meta配置
   - 检查store状态初始化

### 调试技巧

1. 在浏览器控制台查看token状态：
```javascript
console.log('Token:', localStorage.getItem('access_token'))
console.log('User Info:', JSON.parse(localStorage.getItem('user_info')))
```

2. 监听store状态变化：
```javascript
store.watch(
  state => state.user.isLoggedIn,
  (newVal) => console.log('Login status changed:', newVal)
)
```

## 更新日志

- **v1.0.0**: 初始版本，包含基础认证功能
- 支持登录、注册、token管理
- 实现路由守卫和请求拦截
- 添加用户信息组件和状态管理
