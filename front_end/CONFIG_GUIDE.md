# 前端配置管理指南

## 📋 概述

本项目使用传统的Vite环境变量配置方式，简单易用，便于部署管理。

## 🏗️ 配置文件结构

```
front_end/
├── .env.development      # 开发环境配置
├── .env.production       # 生产环境配置
├── vite.config.js        # Vite配置文件
└── package.json          # NPM脚本
```

## ⚙️ 环境配置

### 开发环境 (.env.development)

```bash
# 后端服务地址 (用于Vite代理)
VITE_BACKEND_URL = 'http://***********:18888'

# 基础API路径 (开发环境使用代理)
VITE_VUE_APP_BASE_API = '/dev-api'

# AI/LLM API路径 (开发环境使用代理)
VITE_VUE_APP_OTHER_API = '/api-s'

# Elasticsearch地址
VITE_VUE_APP_API = 'http://***********:9200'

# 文件下载服务 (开发环境使用代理)
VITE_FILE_BASE_URL = '/download?url='
```

### 生产环境 (.env.production)

```bash
# 后端服务地址
VITE_BACKEND_URL = 'https://your-domain.com'

# 基础API路径 (生产环境使用相对路径)
VITE_VUE_APP_BASE_API = '/api'

# AI/LLM API路径
VITE_VUE_APP_OTHER_API = '/api-s'

# Elasticsearch地址
VITE_VUE_APP_API = 'https://your-domain.com:9200'

# 文件下载服务 (生产环境使用完整URL)
VITE_FILE_BASE_URL = 'https://your-domain.com/download?url='
```

## 🚀 使用方法

### 开发环境

```bash
# 启动开发服务器
npm run dev

# 访问地址: http://localhost:22
```

**特点**:
- ✅ 使用Vite代理，无需配置CORS
- ✅ 支持热重载
- ✅ 自动处理API路径转换

### 生产环境

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

**特点**:
- ✅ 代码压缩优化
- ✅ 静态资源处理
- ✅ 生产环境配置

## 🔧 配置说明

### 关键配置参数

| 参数 | 说明 | 开发环境 | 生产环境 |
|------|------|----------|----------|
| `VITE_BACKEND_URL` | 后端服务地址 | `http://***********:18888` | `https://your-domain.com` |
| `VITE_VUE_APP_BASE_API` | 基础API路径 | `/dev-api` (代理) | `/api` (直连) |
| `VITE_VUE_APP_OTHER_API` | AI服务API | `/api-s` (代理) | `/api-s` (直连) |
| `VITE_FILE_BASE_URL` | 文件下载 | `/download?url=` (代理) | `https://domain/download?url=` |

### Vite代理配置

开发环境自动配置以下代理：

```javascript
proxy: {
  '/auth': 'http://***********:18888',      // 认证API
  '/api-s': 'http://***********:18888',     // AI服务API  
  '/dev-api': 'http://***********:18888',   // 主要API
  '/download': 'http://***********:18888',  // 文件下载
}
```

## 📦 部署流程

### 1. 开发环境部署

```bash
# 1. 检查开发环境配置
cat .env.development

# 2. 启动开发服务器
npm run dev

# 3. 访问测试
# http://localhost:22
```

### 2. 生产环境部署

```bash
# 1. 修改生产环境配置
vim .env.production

# 2. 构建生产版本
npm run build

# 3. 部署dist目录到服务器
# 将 dist/ 目录上传到Web服务器
```

## 🔄 配置修改流程

### 修改开发环境配置

1. **编辑配置文件**
   ```bash
   vim .env.development
   ```

2. **修改关键参数**
   ```bash
   # 修改后端地址
   VITE_BACKEND_URL = 'http://新的IP:18888'
   
   # 修改Elasticsearch地址  
   VITE_VUE_APP_API = 'http://新的IP:9200'
   ```

3. **重启开发服务器**
   ```bash
   # Ctrl+C 停止服务器
   npm run dev  # 重新启动
   ```

### 修改生产环境配置

1. **编辑配置文件**
   ```bash
   vim .env.production
   ```

2. **修改域名和协议**
   ```bash
   # 修改为实际域名
   VITE_BACKEND_URL = 'https://实际域名.com'
   VITE_FILE_BASE_URL = 'https://实际域名.com/download?url='
   ```

3. **重新构建**
   ```bash
   npm run build
   ```

## 🛠️ 常见配置场景

### 场景1: 更换开发服务器IP

```bash
# .env.development
VITE_BACKEND_URL = 'http://新IP:18888'
VITE_VUE_APP_API = 'http://新IP:9200'
```

### 场景2: 生产环境使用HTTPS

```bash
# .env.production  
VITE_BACKEND_URL = 'https://domain.com'
VITE_VUE_APP_API = 'https://domain.com:9200'
VITE_FILE_BASE_URL = 'https://domain.com/download?url='
```

### 场景3: 测试环境配置

创建 `.env.test` 文件：
```bash
# .env.test
VITE_BACKEND_URL = 'http://test-server:18888'
VITE_VUE_APP_BASE_API = '/api'
VITE_FILE_BASE_URL = 'http://test-server:18888/download?url='
```

使用测试配置构建：
```bash
vite build --mode test
```

## 📝 配置检查清单

### 部署前检查

- [ ] 后端服务地址是否正确
- [ ] API路径配置是否匹配后端路由
- [ ] 文件下载地址是否可访问
- [ ] iframe允许来源是否正确配置
- [ ] HTTPS/HTTP协议是否一致

### 常见问题

1. **API请求失败**
   - 检查 `VITE_BACKEND_URL` 是否正确
   - 检查后端服务是否启动
   - 查看浏览器控制台错误

2. **文件下载失败**
   - 检查 `VITE_FILE_BASE_URL` 配置
   - 确认文件服务是否正常

3. **iframe嵌入失败**
   - 检查 `VITE_IFRAME_ALLOWED_ORIGINS` 配置
   - 确认CSP策略设置

## 🎯 最佳实践

1. **配置管理**
   - ✅ 开发和生产环境分别配置
   - ✅ 使用环境变量而不是硬编码
   - ✅ 定期检查配置的有效性

2. **部署流程**
   - ✅ 部署前检查配置文件
   - ✅ 测试所有功能是否正常
   - ✅ 保留配置文件的备份

3. **安全考虑**
   - ✅ 生产环境使用HTTPS
   - ✅ 限制iframe来源
   - ✅ 不在前端暴露敏感信息

这种配置方式简单直观，便于理解和维护，符合传统的前端项目配置习惯。
