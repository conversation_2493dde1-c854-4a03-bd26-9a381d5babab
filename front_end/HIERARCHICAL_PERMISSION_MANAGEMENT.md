# 🔐 分级权限管理系统设计文档

## 📋 系统概述

本系统实现了三级权限管理架构，为不同角色的用户提供相应的权限管理功能：

- **系统管理员**：全局权限管理
- **部门管理员**：部门权限管理  
- **普通用户**：个人申请管理

## 🎯 权限层级设计

### 1. 系统管理员 (user_id = 1)
- ✅ 查看和审批所有用户的权限申请
- ✅ 查看部门统计数据
- ✅ 跨部门权限管理
- ✅ 系统级权限配置

### 2. 部门管理员 (admin角色 + 同部门)
- ✅ 查看和审批本部门用户的权限申请
- ✅ 管理本部门的权限设置
- ✅ 查看本部门权限统计

### 3. 普通用户
- ✅ 查看和管理自己的权限申请
- ✅ 提交新的权限申请
- ✅ 撤销待审批的申请

## 🖥️ 界面设计

### 系统管理员界面
```
┌─────────────────────────────────────────┐
│ 🔴 系统管理员 张三 (技术部)              │
├─────────────────────────────────────────┤
│ [全部待审批] [部门统计] [我的申请]        │
├─────────────────────────────────────────┤
│ 全部待审批列表 (跨部门)                  │
│ - 申请人 + 部门标签                     │
│ - 文档名称                             │
│ - 权限类型                             │
│ - 申请理由                             │
│ - 操作按钮                             │
└─────────────────────────────────────────┘
```

### 部门管理员界面
```
┌─────────────────────────────────────────┐
│ 🟡 部门管理员 李四 (市场部)              │
├─────────────────────────────────────────┤
│ [部门待审批] [我的申请]                  │
├─────────────────────────────────────────┤
│ 本部门待审批列表                        │
│ - 仅显示本部门用户申请                   │
│ - 审批权限限制在本部门                   │
└─────────────────────────────────────────┘
```

### 普通用户界面
```
┌─────────────────────────────────────────┐
│ 🔵 普通用户 王五 (技术部)                │
├─────────────────────────────────────────┤
│ [我的申请]                              │
├─────────────────────────────────────────┤
│ 个人申请列表                            │
│ - 申请状态筛选                          │
│ - 撤销/删除操作                         │
└─────────────────────────────────────────┘
```

## 🔧 技术实现

### 前端角色检测
```javascript
// 系统管理员检测
isSystemAdmin() {
  return this.currentUser && this.currentUser.user_id === 1
}

// 部门管理员检测
isDepartmentAdmin() {
  if (!this.currentUser || !this.currentUser.dept_id) {
    return false
  }
  const userRoles = this.currentUser.roles || []
  return userRoles.includes('admin')
}
```

### 动态标签页配置
```javascript
availableTabs() {
  if (this.isSystemAdmin) {
    return ['all-pending', 'department-stats', 'my-requests']
  } else if (this.isDepartmentAdmin) {
    return ['dept-pending', 'my-requests']
  } else {
    return ['my-requests']
  }
}
```

### 数据加载策略
```javascript
// 根据用户角色加载不同数据
if (this.isSystemAdmin) {
  this.loadAllPendingReviews()      // 全部待审批
  this.loadDepartmentStats()        // 部门统计
  this.loadMyRequests()             // 我的申请
} else if (this.isDepartmentAdmin) {
  this.loadPendingReviews()         // 部门待审批
  this.loadMyRequests()             // 我的申请
} else {
  this.loadMyRequests()             // 仅我的申请
}
```

## 🛡️ 安全机制

### 后端权限验证
- ✅ JWT token验证
- ✅ 用户角色检查
- ✅ 部门权限隔离
- ✅ 操作权限验证

### 前端权限控制
- ✅ 界面元素动态显示/隐藏
- ✅ 路由权限保护
- ✅ API调用权限检查
- ✅ 数据范围限制

## 📊 权限申请流程

```
用户提交申请 → 系统自动分配审批人 → 审批处理 → 权限生效

审批人分配规则：
1. 文档所有者有部门 → 部门管理员审批
2. 文档所有者无部门 → 系统管理员审批
3. 系统管理员可审批所有申请
```

## 🎨 用户体验优化

### 视觉设计
- ✅ 角色标签颜色区分
- ✅ 权限提示工具提示
- ✅ 统计数据可视化
- ✅ 加载状态优化

### 交互优化
- ✅ 智能默认标签页
- ✅ 批量操作支持
- ✅ 实时数据刷新
- ✅ 错误处理提示

## 🧪 测试用例

### 系统管理员测试
- [ ] 登录后默认显示"全部待审批"标签页
- [ ] 可以看到所有部门的权限申请
- [ ] 可以审批任何用户的申请
- [ ] 部门统计数据正确显示

### 部门管理员测试
- [ ] 登录后默认显示"部门待审批"标签页
- [ ] 只能看到本部门用户的申请
- [ ] 无法审批其他部门的申请
- [ ] 权限隔离正确

### 普通用户测试
- [ ] 只能看到"我的申请"标签页
- [ ] 无法看到其他用户的申请
- [ ] 可以正常提交和管理自己的申请

## 🚀 部署说明

1. **前端更新**：
   ```bash
   cd front_end
   npm run build
   ```

2. **权限配置**：
   - 确保用户角色正确配置
   - 验证部门分配正确
   - 检查JWT token包含必要字段

3. **测试验证**：
   - 使用不同角色用户测试
   - 验证权限隔离效果
   - 确认审批流程正常

## 📝 注意事项

- ⚠️ 系统管理员权限极高，需要严格控制
- ⚠️ 部门管理员权限仅限本部门，需要验证部门ID匹配
- ⚠️ 前端权限控制仅为用户体验，真正的安全依赖后端验证
- ⚠️ 定期审查权限分配，确保符合组织架构变化
