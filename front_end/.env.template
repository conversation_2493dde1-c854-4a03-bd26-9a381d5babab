# =================================
# 环境配置模板
# 复制此文件为 .env.development 或 .env.production 并修改相应配置
# =================================

# 后端服务地址
# 开发环境: http://***********:18888
# 生产环境: https://your-domain.com
VITE_BACKEND_URL = 'http://***********:18888'

# 基础API路径
# 开发环境: /dev-api (使用代理)
# 生产环境: /api (直接访问)
VITE_VUE_APP_BASE_API = '/dev-api'

# AI/LLM API路径
# 开发环境: /api-s (使用代理)
# 生产环境: /api-s (直接访问)
VITE_VUE_APP_OTHER_API = '/api-s'
VITE_VUE_APP_OTHER_PATH_API = 'http://***********:18888/'

# 文件下载服务
# 开发环境: /download?url= (使用代理)
# 生产环境: https://your-domain.com/download?url= (完整URL)
VITE_FILE_BASE_URL = '/download?url='

# =================================
# 可选配置
# =================================

# 环境标识
VITE_ENV_NAME = '开发环境'
VITE_ENV_MODE = 'development'

# 调试模式
VITE_DEBUG_MODE = 'true'

# iframe允许的来源 (多个用逗号分隔)
VITE_IFRAME_ALLOWED_ORIGINS = 'http://localhost:8080,http://***********:8080'
