# =================================
# 生产环境配置
# =================================
# 后端服务地址 (生产环境)
VITE_BACKEND_URL = 'http://*************:18888'

# 基础API路径 (生产环境使用nginx代理)
VITE_VUE_APP_BASE_API = '/dev-api'

# AI/LLM API路径 (生产环境使用nginx代理)
VITE_VUE_APP_OTHER_API = '/api-s'
VITE_VUE_APP_OTHER_PATH_API = 'http://*************:18888/'

# 文件下载服务 (生产环境使用nginx代理，现在使用安全的JWT认证)
VITE_FILE_BASE_URL = '/download?url='
# =================================
# 生产环境特殊配置
# =================================

# 环境标识
VITE_ENV_NAME = '生产环境'
VITE_ENV_MODE = 'production'

# 调试模式 (生产环境关闭)
VITE_DEBUG_MODE = 'false'

# iframe允许的来源 (生产环境限制)
VITE_IFRAME_ALLOWED_ORIGINS = 'https://*************,https://*************,http://localhost:8080,http://***********:8080'

# CSP策略配置 (用于前端代码中的动态设置)
VITE_CSP_FRAME_ANCESTORS = "'self' http://localhost:* https://localhost:* http://***********:* https://***********:* http://*************:* https://*************:* http://*************:* https://*************:*"
VITE_CSP_DEFAULT_SRC = "'self'"
VITE_CSP_SCRIPT_SRC = "'self' 'unsafe-inline'"
VITE_CSP_STYLE_SRC = "'self' 'unsafe-inline'"
# =================================
# 性能优化配置
# =================================

# 启用压缩
VITE_ENABLE_GZIP = 'true'

# =================================
# Service A/B 通信配置
# =================================

# Service A 地址 (生产环境)
VITE_SERVICE_A_URL=http://*************:8080

# Service B 地址 (生产环境)
VITE_SERVICE_B_URL=http://*************:8080

# 额外允许的来源（逗号分隔）
VITE_EXTRA_ORIGINS=http://*************:8080,http://*************:8080
