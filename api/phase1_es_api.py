#!/usr/bin/env python3
"""
第一阶段API：ES索引和基础检索接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

from utils.compatible_knowledge_indexer import CompatibleKnowledgeIndexer
from utils.compatible_retriever import CompatibleRetriever
from utils.log import log

# 创建路由器
router = APIRouter(prefix="/api/es", tags=["ES索引和检索-兼容版"])

# 全局实例 - 使用兼容版本
indexer = CompatibleKnowledgeIndexer()
retriever = CompatibleRetriever()


# 请求模型
class IndexDocumentsRequest(BaseModel):
    project_name: str = Field(..., description="项目名称")
    action: str = Field(..., description="汇编类型")
    urls: List[str] = Field(..., description="文档URL列表")
    force_refresh: bool = Field(False, description="是否强制刷新索引")


class KeywordSearchRequest(BaseModel):
    keywords: Union[str, List[str]] = Field(..., description="关键词")
    project_name: Optional[str] = Field(None, description="项目名称过滤")
    action: Optional[str] = Field(None, description="汇编类型过滤")
    size: int = Field(10, description="返回结果数量")
    min_score: float = Field(0.1, description="最小分数阈值")


class SemanticSearchRequest(BaseModel):
    query_text: str = Field(..., description="语义查询文本")
    project_name: Optional[str] = Field(None, description="项目名称过滤")
    action: Optional[str] = Field(None, description="汇编类型过滤")
    size: int = Field(10, description="返回结果数量")
    min_score: float = Field(0.7, description="最小分数阈值")


class HybridSearchRequest(BaseModel):
    keywords: Union[str, List[str]] = Field(..., description="关键词")
    semantic_query: str = Field(..., description="语义查询")
    project_name: Optional[str] = Field(None, description="项目名称过滤")
    action: Optional[str] = Field(None, description="汇编类型过滤")
    keyword_weight: float = Field(0.4, description="关键词权重")
    semantic_weight: float = Field(0.6, description="语义权重")
    size: int = Field(10, description="返回结果数量")


class MultiFieldSearchRequest(BaseModel):
    field_queries: Dict[str, Dict[str, Any]] = Field(..., description="字段查询配置")
    project_name: Optional[str] = Field(None, description="项目名称过滤")
    action: Optional[str] = Field(None, description="汇编类型过滤")
    size: int = Field(20, description="总返回结果数量")


# 响应模型
class IndexResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


class SearchResponse(BaseModel):
    success: bool
    message: str
    results: List[Dict[str, Any]]
    total_count: int
    search_time: str


# API接口
@router.post("/index/documents", response_model=IndexResponse)
async def index_documents(
    request: IndexDocumentsRequest,
    background_tasks: BackgroundTasks
):
    """索引项目文档"""
    try:
        log.info(f"开始索引文档: {request.project_name}, 文档数: {len(request.urls)}")
        
        # 执行文档索引
        result = await indexer.index_project_documents(
            project_name=request.project_name,
            action=request.action,
            urls=request.urls,
            force_refresh=request.force_refresh
        )
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return IndexResponse(
            success=True,
            message="文档索引成功",
            data=result
        )
        
    except Exception as e:
        log.error(f"索引文档失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/index/info/{project_name}/{action}")
async def get_index_info(project_name: str, action: str):
    """获取索引信息"""
    try:
        info = await indexer.get_index_info(project_name, action)
        
        return {
            "success": True,
            "message": "获取索引信息成功",
            "data": info
        }
        
    except Exception as e:
        log.error(f"获取索引信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/index/{project_name}/{action}")
async def delete_index(project_name: str, action: str):
    """删除项目索引"""
    try:
        success = await indexer.delete_project_index(project_name, action)
        
        if success:
            return {
                "success": True,
                "message": "索引删除成功"
            }
        else:
            raise HTTPException(status_code=500, detail="索引删除失败")
            
    except Exception as e:
        log.error(f"删除索引失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search/keyword", response_model=SearchResponse)
async def keyword_search(request: KeywordSearchRequest):
    """关键词检索 - 兼容版"""
    try:
        start_time = datetime.now()

        results = await retriever.keyword_search(
            keywords=request.keywords,
            project_name=request.project_name,
            action=request.action,
            size=request.size,
            min_score=request.min_score
        )

        search_time = (datetime.now() - start_time).total_seconds()

        return SearchResponse(
            success=True,
            message="关键词检索成功",
            results=results,
            total_count=len(results),
            search_time=f"{search_time:.3f}s"
        )

    except Exception as e:
        log.error(f"关键词检索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search/semantic", response_model=SearchResponse)
async def semantic_search(request: SemanticSearchRequest):
    """语义检索 - 兼容版"""
    try:
        start_time = datetime.now()

        results = await retriever.semantic_search(
            query_text=request.query_text,
            project_name=request.project_name,
            action=request.action,
            size=request.size,
            min_score=request.min_score
        )

        search_time = (datetime.now() - start_time).total_seconds()

        return SearchResponse(
            success=True,
            message="语义检索成功",
            results=results,
            total_count=len(results),
            search_time=f"{search_time:.3f}s"
        )

    except Exception as e:
        log.error(f"语义检索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search/hybrid", response_model=SearchResponse)
async def hybrid_search(request: HybridSearchRequest):
    """混合检索"""
    try:
        start_time = datetime.now()
        
        results = await retriever.hybrid_search(
            keywords=request.keywords,
            semantic_query=request.semantic_query,
            project_name=request.project_name,
            action=request.action,
            keyword_weight=request.keyword_weight,
            semantic_weight=request.semantic_weight,
            size=request.size
        )
        
        search_time = (datetime.now() - start_time).total_seconds()
        
        return SearchResponse(
            success=True,
            message="混合检索成功",
            results=results,
            total_count=len(results),
            search_time=f"{search_time:.3f}s"
        )
        
    except Exception as e:
        log.error(f"混合检索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search/multi-field")
async def multi_field_search(request: MultiFieldSearchRequest):
    """多字段检索"""
    try:
        start_time = datetime.now()
        
        results = await retriever.multi_field_search(
            field_queries=request.field_queries,
            project_name=request.project_name,
            action=request.action,
            size=request.size
        )
        
        search_time = (datetime.now() - start_time).total_seconds()
        
        return {
            "success": True,
            "message": "多字段检索成功",
            "results": results,
            "field_count": len(results),
            "search_time": f"{search_time:.3f}s"
        }
        
    except Exception as e:
        log.error(f"多字段检索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """健康检查 - 兼容版"""
    try:
        # 检查各组件状态
        indexer_stats = await indexer.get_indexer_stats()
        retriever_stats = await retriever.get_retriever_stats()

        return {
            "success": True,
            "message": "系统健康",
            "components": {
                "indexer": indexer_stats,
                "retriever": retriever_stats
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        log.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/test/search")
async def test_search_capabilities(
    project_name: str = "测试项目",
    action: str = "项目档案"
):
    """测试检索能力 - 兼容版"""
    try:
        test_results = await retriever.test_search_capabilities(
            project_name=project_name,
            action=action
        )

        return {
            "success": True,
            "message": "检索能力测试完成",
            "test_results": test_results,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        log.error(f"测试检索能力失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 初始化函数
async def initialize_phase1_components():
    """初始化第一阶段组件 - 兼容版"""
    try:
        await indexer.initialize()
        await retriever.initialize()
        log.info("第一阶段兼容组件初始化完成")
    except Exception as e:
        log.error(f"第一阶段兼容组件初始化失败: {e}")
        raise
