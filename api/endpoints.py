"""
用户和部门API接口
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from database import get_db
from utils.helpers import (
    UserService,
    DepartmentService,
    QueryService,
    get_user_department_path,
    check_user_department_permission
)
from auth.models import User, Department

router = APIRouter(prefix="/api/v2", tags=["用户部门API"])

# Pydantic模型
class UserResponse(BaseModel):
    user_id: int
    user_name: str
    nick_name: str
    email: str
    phonenumber: str
    dept_id: Optional[int]
    department_name: Optional[str]
    status: str

class DepartmentResponse(BaseModel):
    dept_id: int
    dept_name: str
    parent_id: int
    leader: Optional[str]
    phone: Optional[str]
    email: Optional[str]
    status: str

class UserCreateRequest(BaseModel):
    username: str
    nick_name: Optional[str]
    email: str
    password: str
    dept_id: Optional[int]
    phone: Optional[str]

# 用户相关接口
@router.get("/users/{user_id}", response_model=Dict[str, Any])
async def get_user_detail(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取用户详细信息（包含部门信息）"""
    user_info = await QueryService.get_user_with_department(db, user_id)
    if not user_info:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 获取部门完整路径
    dept_path = await get_user_department_path(db, user_id)
    user_info["department_path"] = dept_path
    
    return {
        "success": True,
        "data": user_info,
        "message": "获取用户信息成功"
    }

@router.get("/users", response_model=Dict[str, Any])
async def search_users(
    keyword: str = Query(..., description="搜索关键词"),
    dept_id: Optional[int] = Query(None, description="部门ID"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db)
):
    """搜索用户"""
    users = await QueryService.search_users(db, keyword, dept_id)
    
    # 分页
    total = len(users)
    start = (page - 1) * size
    end = start + size
    paginated_users = users[start:end]
    
    return {
        "success": True,
        "data": {
            "users": paginated_users,
            "pagination": {
                "page": page,
                "size": size,
                "total": total,
                "pages": (total + size - 1) // size
            }
        },
        "message": f"找到 {total} 个用户"
    }

@router.get("/users/department/{dept_id}", response_model=Dict[str, Any])
async def get_department_users(
    dept_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取部门用户列表"""
    users = await UserService.get_users_by_department(db, dept_id)
    
    user_list = []
    for user in users:
        user_list.append({
            "user_id": user.user_id,
            "user_name": user.user_name,
            "nick_name": user.nick_name,
            "email": user.email,
            "phonenumber": user.phonenumber,
            "status": user.status
        })
    
    return {
        "success": True,
        "data": user_list,
        "message": f"部门 {dept_id} 共有 {len(user_list)} 个用户"
    }

@router.post("/users", response_model=Dict[str, Any])
async def create_user(
    user_data: UserCreateRequest,
    db: AsyncSession = Depends(get_db)
):
    """创建本地用户"""
    # 检查用户名是否已存在
    existing_user = await UserService.get_user_by_username(db, user_data.username)
    if existing_user:
        raise HTTPException(status_code=400, detail="用户名已存在")
    
    # 简单密码加密（实际应用中应使用更安全的方法）
    import hashlib
    hashed_password = hashlib.sha256(user_data.password.encode()).hexdigest()
    
    user = await UserService.create_local_user(db, {
        "username": user_data.username,
        "nick_name": user_data.nick_name,
        "email": user_data.email,
        "hashed_password": hashed_password,
        "dept_id": user_data.dept_id,
        "phone": user_data.phone
    })
    
    return {
        "success": True,
        "data": {
            "user_id": user.user_id,
            "user_name": user.user_name,
            "email": user.email
        },
        "message": "用户创建成功"
    }

# 部门相关接口
@router.get("/departments/{dept_id}", response_model=Dict[str, Any])
async def get_department_detail(
    dept_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取部门详细信息"""
    dept = await DepartmentService.get_department_by_id(db, dept_id)
    if not dept:
        raise HTTPException(status_code=404, detail="部门不存在")
    
    # 获取部门层级
    hierarchy = await DepartmentService.get_department_hierarchy(db, dept_id)
    hierarchy_path = " > ".join([d.dept_name for d in hierarchy])
    
    # 获取子部门
    children = await DepartmentService.get_department_children(db, dept_id)
    
    # 获取部门用户数量
    users = await UserService.get_users_by_department(db, dept_id)
    
    # 获取部门管理员
    admin = await DepartmentService.get_department_admin(db, dept_id)
    
    return {
        "success": True,
        "data": {
            "department": {
                "dept_id": dept.dept_id,
                "dept_name": dept.dept_name,
                "parent_id": dept.parent_id,
                "leader": dept.leader,
                "phone": dept.phone,
                "email": dept.email,
                "is_synced": dept.is_synced
            },
            "hierarchy_path": hierarchy_path,
            "children_count": len(children),
            "users_count": len(users),
            "admin": {
                "user_id": admin.user_id,
                "user_name": admin.user_name,
                "nick_name": admin.nick_name
            } if admin else None
        },
        "message": "获取部门信息成功"
    }

@router.get("/departments", response_model=Dict[str, Any])
async def get_department_tree(
    db: AsyncSession = Depends(get_db)
):
    """获取部门树结构"""
    tree = await QueryService.get_department_tree(db)
    
    return {
        "success": True,
        "data": tree,
        "message": "获取部门树成功"
    }

@router.get("/departments/{dept_id}/children", response_model=Dict[str, Any])
async def get_department_children(
    dept_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取子部门列表"""
    children = await DepartmentService.get_department_children(db, dept_id)
    
    children_list = []
    for child in children:
        # 获取子部门用户数量
        users = await UserService.get_users_by_department(db, child.dept_id)
        
        children_list.append({
            "dept_id": child.dept_id,
            "dept_name": child.dept_name,
            "leader": child.leader,
            "users_count": len(users),
            "is_synced": child.is_synced
        })
    
    return {
        "success": True,
        "data": children_list,
        "message": f"部门 {dept_id} 共有 {len(children_list)} 个子部门"
    }

# 同步相关接口
@router.get("/sync/status", response_model=Dict[str, Any])
async def get_sync_status(
    db: AsyncSession = Depends(get_db)
):
    """获取同步状态"""
    # 获取所有有效用户和部门
    from sqlalchemy import text

    # 统计用户数量
    result = await db.execute(text("SELECT COUNT(*) FROM users WHERE del_flag = '0'"))
    total_users = result.scalar()

    # 统计部门数量
    result = await db.execute(text("SELECT COUNT(*) FROM departments WHERE del_flag = '0'"))
    total_departments = result.scalar()

    return {
        "success": True,
        "data": {
            "total_users": total_users,
            "total_departments": total_departments,
            "last_sync_time": None  # 由于移除了同步时间字段，这里返回None
        },
        "message": "获取同步状态成功"
    }

@router.post("/sync/trigger", response_model=Dict[str, Any])
async def trigger_sync():
    """触发手动同步"""
    from sync.sync_service import sync_service

    try:
        await sync_service.init_sync_service()
        result = await sync_service.full_sync()
        await sync_service.stop_sync_service()
        
        return {
            "success": True,
            "data": result,
            "message": "同步完成"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"同步失败: {str(e)}")

# 权限检查接口
@router.get("/permissions/check", response_model=Dict[str, Any])
async def check_permission(
    user_id: int = Query(..., description="用户ID"),
    target_dept_id: int = Query(..., description="目标部门ID"),
    db: AsyncSession = Depends(get_db)
):
    """检查用户对部门的权限"""
    has_permission = await check_user_department_permission(db, user_id, target_dept_id)
    
    return {
        "success": True,
        "data": {
            "has_permission": has_permission,
            "user_id": user_id,
            "target_dept_id": target_dept_id
        },
        "message": "权限检查完成"
    }
