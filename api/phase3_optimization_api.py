#!/usr/bin/env python3
"""
第三阶段API：质量评估和性能优化接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime

from utils.phase3_optimization_processor import Phase3OptimizationProcessor
from utils.log import log

# 创建路由器
router = APIRouter(prefix="/api/optimization", tags=["第三阶段-质量评估和性能优化"])

# 全局实例
processor = Phase3OptimizationProcessor()


# 请求模型
class OptimizedAssemblyRequest(BaseModel):
    project_name: str = Field(..., description="项目名称")
    action: str = Field(..., description="汇编类型")
    urls: List[str] = Field(..., description="文档URL列表")
    target_fields: Optional[List[str]] = Field(None, description="目标字段列表")
    force_refresh: bool = Field(False, description="是否强制刷新索引")
    optimization_level: str = Field("standard", description="优化级别: basic, standard, advanced")


class BatchOptimizationRequest(BaseModel):
    projects: List[Dict[str, Any]] = Field(..., description="项目列表")
    optimization_level: str = Field("standard", description="优化级别")
    max_concurrent: Optional[int] = Field(3, description="最大并发数")


# 响应模型
class OptimizedAssemblyResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    quality_assessment: Optional[Dict[str, Any]] = None
    optimization_metadata: Optional[Dict[str, Any]] = None
    processing_time: Optional[float] = None


class BatchOptimizationResponse(BaseModel):
    success: bool
    message: str
    batch_processing_summary: Dict[str, Any]
    successful_results: List[Dict[str, Any]]
    failed_results: List[Dict[str, Any]]


class PerformanceReportResponse(BaseModel):
    success: bool
    message: str
    performance_report: Dict[str, Any]


# API接口
@router.post("/assembly/process", response_model=OptimizedAssemblyResponse)
async def process_optimized_assembly(
    request: OptimizedAssemblyRequest,
    background_tasks: BackgroundTasks
):
    """优化汇编处理"""
    try:
        log.info(f"开始优化汇编处理: {request.project_name}, 优化级别: {request.optimization_level}")
        
        start_time = datetime.now()
        
        # 执行优化汇编处理
        result = await processor.process_with_optimization(
            project_name=request.project_name,
            action=request.action,
            urls=request.urls,
            target_fields=request.target_fields,
            force_refresh=request.force_refresh,
            optimization_level=request.optimization_level
        )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return OptimizedAssemblyResponse(
            success=True,
            message="优化汇编处理成功",
            data=result.get("extracted_data", {}),
            quality_assessment=result.get("quality_assessment"),
            optimization_metadata=result.get("optimization_metadata"),
            processing_time=processing_time
        )
        
    except Exception as e:
        log.error(f"优化汇编处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch/process", response_model=BatchOptimizationResponse)
async def batch_process_with_optimization(
    request: BatchOptimizationRequest,
    background_tasks: BackgroundTasks
):
    """批量优化处理"""
    try:
        log.info(f"开始批量优化处理: {len(request.projects)}个项目, 优化级别: {request.optimization_level}")
        
        # 执行批量优化处理
        result = await processor.batch_process_with_optimization(
            projects=request.projects,
            optimization_level=request.optimization_level,
            max_concurrent=request.max_concurrent
        )
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return BatchOptimizationResponse(
            success=True,
            message=f"批量优化处理完成: 成功{result['batch_processing_summary']['successful_projects']}, 失败{result['batch_processing_summary']['failed_projects']}",
            batch_processing_summary=result["batch_processing_summary"],
            successful_results=result["successful_results"],
            failed_results=result["failed_results"]
        )
        
    except Exception as e:
        log.error(f"批量优化处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance/report", response_model=PerformanceReportResponse)
async def get_performance_report(
    time_range_hours: int = Query(1, description="时间范围（小时）", ge=1, le=168)
):
    """获取性能报告"""
    try:
        log.info(f"获取性能报告: 时间范围 {time_range_hours} 小时")
        
        # 获取性能报告
        report = await processor.get_performance_report(time_range_hours)
        
        if "error" in report:
            raise HTTPException(status_code=500, detail=report["error"])
        
        return PerformanceReportResponse(
            success=True,
            message="性能报告获取成功",
            performance_report=report
        )
        
    except Exception as e:
        log.error(f"获取性能报告失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/capabilities")
async def get_optimization_capabilities():
    """获取优化能力信息"""
    try:
        capabilities = await processor.get_optimization_capabilities()
        
        return {
            "success": True,
            "message": "获取优化能力信息成功",
            "capabilities": capabilities
        }
        
    except Exception as e:
        log.error(f"获取优化能力信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        health_status = await processor.health_check()
        
        # 根据健康状态设置HTTP状态码
        if health_status.get("overall_status") == "error":
            raise HTTPException(status_code=503, detail=health_status)
        elif health_status.get("overall_status") == "degraded":
            # 降级状态返回200但包含警告信息
            return {
                "success": True,
                "message": "系统运行但存在问题",
                "health_status": health_status,
                "warning": "部分组件状态异常"
            }
        else:
            return {
                "success": True,
                "message": "系统健康",
                "health_status": health_status
            }
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cache/clear")
async def clear_cache():
    """清理缓存"""
    try:
        result = await processor.clear_cache()
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return {
            "success": True,
            "message": result["message"],
            "cache_info": {
                "cache_size_before": result["cache_size_before"],
                "cache_size_after": result["cache_size_after"]
            }
        }
        
    except Exception as e:
        log.error(f"清理缓存失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/optimization/levels")
async def get_optimization_levels():
    """获取优化级别信息"""
    try:
        optimization_levels = {
            "basic": {
                "name": "基础优化",
                "description": "基础数据清理和格式化",
                "features": [
                    "数据清理",
                    "格式标准化",
                    "空值处理"
                ],
                "processing_time": "快速",
                "quality_improvement": "低"
            },
            "standard": {
                "name": "标准优化",
                "description": "包含质量评估和改进建议的标准优化",
                "features": [
                    "基础优化功能",
                    "高级质量评估",
                    "改进建议生成",
                    "历史对比分析"
                ],
                "processing_time": "中等",
                "quality_improvement": "中"
            },
            "advanced": {
                "name": "高级优化",
                "description": "包含语义分析和智能补全的高级优化",
                "features": [
                    "标准优化功能",
                    "语义优化",
                    "智能字段补全",
                    "结果一致性增强",
                    "自动优化策略"
                ],
                "processing_time": "较慢",
                "quality_improvement": "高"
            }
        }
        
        return {
            "success": True,
            "message": "获取优化级别信息成功",
            "optimization_levels": optimization_levels,
            "default_level": "standard",
            "recommendation": "建议使用standard级别获得最佳性价比"
        }
        
    except Exception as e:
        log.error(f"获取优化级别信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/quality/dimensions")
async def get_quality_dimensions():
    """获取质量评估维度信息"""
    try:
        quality_dimensions = {
            "accuracy": {
                "name": "准确性",
                "description": "抽取结果的准确程度",
                "weight": 0.25,
                "factors": ["置信度", "验证通过率", "语义一致性"]
            },
            "completeness": {
                "name": "完整性",
                "description": "字段抽取的完整程度",
                "weight": 0.20,
                "factors": ["字段覆盖率", "必需字段完整性", "信息丰富度"]
            },
            "consistency": {
                "name": "一致性",
                "description": "字段间的逻辑一致性",
                "weight": 0.20,
                "factors": ["交叉验证", "逻辑关系", "格式统一性"]
            },
            "reliability": {
                "name": "可靠性",
                "description": "结果的可信赖程度",
                "weight": 0.15,
                "factors": ["多方法一致性", "历史表现", "错误率"]
            },
            "timeliness": {
                "name": "时效性",
                "description": "处理速度和响应时间",
                "weight": 0.10,
                "factors": ["处理时间", "响应速度", "并发能力"]
            },
            "relevance": {
                "name": "相关性",
                "description": "结果与需求的匹配度",
                "weight": 0.10,
                "factors": ["字段相关性", "内容匹配度", "业务适用性"]
            }
        }
        
        return {
            "success": True,
            "message": "获取质量评估维度信息成功",
            "quality_dimensions": quality_dimensions,
            "total_weight": 1.0,
            "scoring_range": "0.0 - 1.0"
        }
        
    except Exception as e:
        log.error(f"获取质量评估维度信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test/optimization")
async def test_optimization_pipeline(
    project_name: str = "测试项目",
    action: str = "项目档案",
    optimization_level: str = "standard"
):
    """测试优化流水线"""
    try:
        log.info(f"开始测试优化流水线: {project_name}, {action}, {optimization_level}")
        
        # 创建测试数据
        test_urls = ["documents/test_doc1.pdf", "documents/test_doc2.pdf"]
        
        # 测试优化处理
        result = await processor.process_with_optimization(
            project_name=project_name,
            action=action,
            urls=test_urls,
            optimization_level=optimization_level
        )
        
        test_results = {
            "optimization_test": {
                "status": "success" if "error" not in result else "failed",
                "optimization_level": optimization_level,
                "processing_time": result.get("optimization_metadata", {}).get("processing_time_seconds", 0),
                "quality_score": result.get("quality_assessment", {}).get("overall_score", 0)
            }
        }
        
        # 如果有质量评估结果，添加详细信息
        if "quality_assessment" in result:
            quality_assessment = result["quality_assessment"]
            test_results["quality_test"] = {
                "overall_score": quality_assessment.get("overall_score", 0),
                "quality_grade": quality_assessment.get("quality_grade", "unknown"),
                "quality_level": quality_assessment.get("quality_level", "未知")
            }
        
        # 如果有优化元数据，添加优化信息
        if "optimization_metadata" in result:
            optimization_metadata = result["optimization_metadata"]
            test_results["optimization_info"] = {
                "phase": optimization_metadata.get("phase", ""),
                "processing_type": optimization_metadata.get("processing_type", ""),
                "advanced_quality_enabled": optimization_metadata.get("advanced_quality_enabled", False),
                "performance_monitoring_enabled": optimization_metadata.get("performance_monitoring_enabled", False)
            }
        
        return {
            "success": True,
            "message": "优化流水线测试完成",
            "test_results": test_results,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        log.error(f"测试优化流水线失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 初始化函数
async def initialize_phase3_components():
    """初始化第三阶段组件"""
    try:
        await processor.initialize()
        log.info("第三阶段优化组件初始化完成")
    except Exception as e:
        log.error(f"第三阶段优化组件初始化失败: {e}")
        raise
