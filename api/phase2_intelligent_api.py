#!/usr/bin/env python3
"""
第二阶段API：智能抽取和结果融合接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime

from utils.phase2_intelligent_processor import Phase2IntelligentProcessor
from utils.log import log

# 创建路由器
router = APIRouter(prefix="/api/intelligent", tags=["第二阶段-智能抽取"])

# 全局实例
processor = Phase2IntelligentProcessor()


# 请求模型
class IntelligentAssemblyRequest(BaseModel):
    project_name: str = Field(..., description="项目名称")
    action: str = Field(..., description="汇编类型")
    urls: List[str] = Field(..., description="文档URL列表")
    target_fields: Optional[List[str]] = Field(None, description="目标字段列表")
    force_refresh: bool = Field(False, description="是否强制刷新索引")


class FieldExtractionRequest(BaseModel):
    project_name: str = Field(..., description="项目名称")
    action: str = Field(..., description="汇编类型")
    target_fields: Optional[List[str]] = Field(None, description="目标字段列表")


class BatchProcessingRequest(BaseModel):
    projects: List[Dict[str, Any]] = Field(..., description="项目列表")
    max_concurrent: Optional[int] = Field(3, description="最大并发数")


class ResultValidationRequest(BaseModel):
    extraction_result: Dict[str, Any] = Field(..., description="抽取结果")


# 响应模型
class IntelligentAssemblyResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    quality_score: Optional[float] = None
    processing_time: Optional[float] = None


class FieldExtractionResponse(BaseModel):
    success: bool
    message: str
    extracted_fields: Dict[str, Any]
    field_analysis: Dict[str, Any]
    extraction_stats: Dict[str, Any]


class BatchProcessingResponse(BaseModel):
    success: bool
    message: str
    results: List[Dict[str, Any]]
    batch_stats: Dict[str, Any]


# API接口
@router.post("/assembly/process", response_model=IntelligentAssemblyResponse)
async def process_intelligent_assembly(
    request: IntelligentAssemblyRequest,
    background_tasks: BackgroundTasks
):
    """智能汇编处理"""
    try:
        log.info(f"开始智能汇编处理: {request.project_name}, 文档数: {len(request.urls)}")
        
        start_time = datetime.now()
        
        # 执行智能汇编处理
        result = await processor.process_intelligent_assembly(
            project_name=request.project_name,
            action=request.action,
            urls=request.urls,
            target_fields=request.target_fields,
            force_refresh=request.force_refresh
        )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return IntelligentAssemblyResponse(
            success=True,
            message="智能汇编处理成功",
            data=result,
            quality_score=result.get("quality_score"),
            processing_time=processing_time
        )
        
    except Exception as e:
        log.error(f"智能汇编处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/extraction/fields", response_model=FieldExtractionResponse)
async def extract_fields_only(request: FieldExtractionRequest):
    """仅执行字段抽取（假设文档已索引）"""
    try:
        log.info(f"开始字段抽取: {request.project_name}, 类型: {request.action}")
        
        # 直接调用抽取器
        result = await processor.extractor.extract_project_fields(
            project_name=request.project_name,
            action=request.action,
            target_fields=request.target_fields
        )
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return FieldExtractionResponse(
            success=True,
            message="字段抽取成功",
            extracted_fields=result.get("extracted_fields", {}),
            field_analysis=result.get("field_analysis", {}),
            extraction_stats={
                "total_fields": result.get("total_fields", 0),
                "successful_fields": result.get("successful_fields", 0),
                "extraction_method": result.get("extraction_method", ""),
                "extraction_time": result.get("extraction_time", "")
            }
        )
        
    except Exception as e:
        log.error(f"字段抽取失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validation/result")
async def validate_extraction_result(request: ResultValidationRequest):
    """验证抽取结果"""
    try:
        log.info("开始结果验证")
        
        # 执行结果验证
        validation_result = await processor.validator.validate_and_fuse_results(
            request.extraction_result
        )
        
        if "error" in validation_result:
            raise HTTPException(status_code=500, detail=validation_result["error"])
        
        return {
            "success": True,
            "message": "结果验证成功",
            "validation_result": validation_result
        }
        
    except Exception as e:
        log.error(f"结果验证失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch/process", response_model=BatchProcessingResponse)
async def batch_process_projects(
    request: BatchProcessingRequest,
    background_tasks: BackgroundTasks
):
    """批量处理多个项目"""
    try:
        log.info(f"开始批量处理: {len(request.projects)}个项目")
        
        start_time = datetime.now()
        
        # 执行批量处理
        results = await processor.batch_process_projects(
            projects=request.projects,
            max_concurrent=request.max_concurrent
        )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # 统计结果
        successful_count = len([r for r in results if "error" not in r])
        failed_count = len(results) - successful_count
        
        batch_stats = {
            "total_projects": len(request.projects),
            "successful_projects": successful_count,
            "failed_projects": failed_count,
            "processing_time_seconds": round(processing_time, 2),
            "average_time_per_project": round(processing_time / len(request.projects), 2) if request.projects else 0
        }
        
        return BatchProcessingResponse(
            success=True,
            message=f"批量处理完成: 成功{successful_count}, 失败{failed_count}",
            results=results,
            batch_stats=batch_stats
        )
        
    except Exception as e:
        log.error(f"批量处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/capabilities")
async def get_processing_capabilities():
    """获取处理能力信息"""
    try:
        capabilities = await processor.get_processing_capabilities()
        
        return {
            "success": True,
            "message": "获取处理能力信息成功",
            "capabilities": capabilities
        }
        
    except Exception as e:
        log.error(f"获取处理能力信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        health_status = await processor.health_check()
        
        # 根据健康状态设置HTTP状态码
        if health_status.get("overall_status") == "error":
            raise HTTPException(status_code=503, detail=health_status)
        elif health_status.get("overall_status") == "degraded":
            # 降级状态返回200但包含警告信息
            return {
                "success": True,
                "message": "系统运行但存在问题",
                "health_status": health_status,
                "warning": "部分组件状态异常"
            }
        else:
            return {
                "success": True,
                "message": "系统健康",
                "health_status": health_status
            }
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config/fields/{action}")
async def get_field_configuration(action: str):
    """获取字段配置"""
    try:
        field_config = processor.extractor.field_extraction_config.get(action)
        
        if not field_config:
            raise HTTPException(status_code=404, detail=f"不支持的汇编类型: {action}")
        
        return {
            "success": True,
            "message": "获取字段配置成功",
            "action": action,
            "field_config": field_config,
            "field_count": len(field_config)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"获取字段配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config/actions")
async def get_supported_actions():
    """获取支持的汇编类型"""
    try:
        supported_actions = list(processor.extractor.field_extraction_config.keys())
        
        action_details = {}
        for action in supported_actions:
            config = processor.extractor.field_extraction_config[action]
            action_details[action] = {
                "field_count": len(config),
                "required_fields": [
                    field_name for field_name, field_config in config.items()
                    if field_config.get("required", False)
                ],
                "optional_fields": [
                    field_name for field_name, field_config in config.items()
                    if not field_config.get("required", False)
                ]
            }
        
        return {
            "success": True,
            "message": "获取支持的汇编类型成功",
            "supported_actions": supported_actions,
            "action_details": action_details,
            "total_actions": len(supported_actions)
        }
        
    except Exception as e:
        log.error(f"获取支持的汇编类型失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test/extraction")
async def test_extraction_pipeline(
    project_name: str = "测试项目",
    action: str = "项目档案"
):
    """测试抽取流水线"""
    try:
        log.info(f"开始测试抽取流水线: {project_name}, {action}")
        
        # 测试字段抽取
        extraction_result = await processor.extractor.extract_project_fields(
            project_name=project_name,
            action=action,
            target_fields=["project_name", "total_investment"]  # 测试部分字段
        )
        
        test_results = {
            "extraction_test": {
                "status": "success" if "error" not in extraction_result else "failed",
                "extracted_fields": extraction_result.get("successful_fields", 0),
                "total_fields": extraction_result.get("total_fields", 0)
            }
        }
        
        # 如果抽取成功，测试验证
        if "error" not in extraction_result:
            validation_result = await processor.validator.validate_and_fuse_results(
                extraction_result
            )
            
            test_results["validation_test"] = {
                "status": "success" if "error" not in validation_result else "failed",
                "quality_score": validation_result.get("quality_assessment", {}).get("overall_score", 0)
            }
        
        return {
            "success": True,
            "message": "抽取流水线测试完成",
            "test_results": test_results,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        log.error(f"测试抽取流水线失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 初始化函数
async def initialize_phase2_components():
    """初始化第二阶段组件"""
    try:
        await processor.initialize()
        log.info("第二阶段智能组件初始化完成")
    except Exception as e:
        log.error(f"第二阶段智能组件初始化失败: {e}")
        raise
