import os
import tarfile
import logging
from logging.handlers import RotatingFileHandler
import sys


def setup_logging(log_dir):
    """设置日志配置"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, "update.log")
    
    formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
    
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=1,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)



def create_update_package(paths_to_pack):
    """创建更新包
    Args:
        paths_to_pack: 要打包的文件或目录列表
    """
    output_file = "update.tar.gz"
    missing_paths = []
    files_to_pack = set()
    
    # 检查是否在正确的目录
    if os.getcwd() != "/workspace/hngpt":
        logging.error("请在 /workspace/hngpt 目录下运行此脚本")
        return False
    
    # 检查是否有root权限（对于系统目录需要）
    if os.geteuid() != 0:
        logging.error("需要root权限来访问系统目录，请使用sudo运行")
        return False
    
    # 处理文件和目录
    for path in paths_to_pack:
        if not os.path.exists(path):
            missing_paths.append(path)
            continue
            
        if os.path.isdir(path):
            # 如果是系统路径（以/开头）
            if path.startswith('/'):
                for root, _, filenames in os.walk(path):
                    for filename in filenames:
                        full_path = os.path.join(root, filename)
                        # 去掉开头的/
                        rel_path = full_path[1:]
                        files_to_pack.add(rel_path)
            else:
                # 对于项目内的目录，只添加指定目录下的文件
                base_dir = path  # 例如 "models/hngpt-mini"
                if os.path.exists(base_dir):
                    for root, _, filenames in os.walk(base_dir):
                        for filename in filenames:
                            full_path = os.path.join(root, filename)
                            # 添加 workspace/hngpt 前缀
                            rel_path = os.path.join("workspace/hngpt", full_path)
                            files_to_pack.add(rel_path)
            logging.info(f"添加目录: {path}")
        else:
            if path.startswith('/'):
                files_to_pack.add(path[1:])  # 系统文件，去掉开头的/
            else:
                # 项目文件，添加 workspace/hngpt 前缀
                rel_path = os.path.join("workspace/hngpt", path)
                files_to_pack.add(rel_path)
            logging.info(f"添加文件: {path}")
    
    if missing_paths:
        logging.error(f"以下路径不存在: {', '.join(missing_paths)}")
        return False
    
    try:
        # 如果已存在则删除旧的更新包
        if os.path.exists(output_file):
            os.remove(output_file)
            logging.info(f"已删除旧的 {output_file}")
        
        # 创建新的tar.gz文件
        with tarfile.open(output_file, "w:gz") as tar:
            for file_path in sorted(files_to_pack):  # 排序以保持稳定的打包顺序
                # 源文件路径需要加回/（如果是系统路径）
                src_path = f"/{file_path}" if file_path.startswith(('usr/', 'lib/')) else file_path.replace('workspace/hngpt/', '')
                logging.info(f"正在添加: {file_path}")
                tar.add(src_path, file_path)
        
        # 验证文件大小
        size = os.path.getsize(output_file)
        logging.info(f"更新包创建成功: {output_file} (大小: {size/1024/1024:.2f} MB)")
        
        return True
        
    except Exception as e:
        logging.error(f"创建更新包时出错: {str(e)}")
        return False

def main():
    setup_logging("log")
    logging.info("开始创建生产环境更新包...")

    # 需要打包的路径列表 - 包含所有新增功能模块
    paths_to_pack = [
        # 核心应用文件
        "app.py",
        "paper_qa.py",
        "knowledge_control.py",
        "database.py",
        "start_service.py",

        # 核心模块
        "agent",
        "utils",
        "loaders",
        "templates",

        # 认证和权限系统
        "auth",
        "middleware",
        "routers",
        "services",
        "sync",

        # 搜索功能
        "search",

        # 静态文件
        "static",

        # 前端构建文件
        "front_end/search",

        # 配置文件
        "config_production.yaml",
        "requirements.txt",
        "requirements-install.txt",

        # 数据库脚本
        "hngpt_schema.sql",
        "sync_table.py",
        "init_production_db.py",
        "export_schema.py",

        # Python包管理脚本
        "export_python_packages.py",
        "download_offline_packages.py",
        "download_core_packages_fast.py",
        "install_python_packages_offline.py",
        "requirements_current.txt",
        "requirements_core.txt",
        "pip_list.txt",
        "python_env_info.json",

        # Python离线包目录
        "python_packages_offline",

        # MySQL容器相关文件
        "mysql.tar",
        "setup_mysql_container.sh",
        "deploy_mysql_only.sh",

        # 数据目录结构（空目录）
        "data/db",
        "data/logs",
        "data/uploads",

        # 日志目录
        "log"
    ]
 
    if create_update_package(paths_to_pack):
        logging.info("生产环境更新包创建完成")
        logging.info("\n=== 生产环境部署说明 ===")
        logging.info("1. 将 update.tar.gz 复制到生产服务器")
        logging.info("   scp update.tar.gz root@*************:/workspace/hngpt/")
        logging.info("\n2. 在生产服务器上执行以下命令:")
        logging.info("   # 停止现有容器")
        logging.info("   docker stop hngpt-app")
        logging.info("   docker rm hngpt-app")
        logging.info("\n   # 解压更新文件")
        logging.info("   cd /workspace/hngpt")
        logging.info("   tar -xzvf update.tar.gz")
        logging.info("\n   # 更新配置文件")
        logging.info("   cp config_production.yaml config.yaml")
        logging.info("\n   # 部署前端到nginx")
        logging.info("   rm -rf /var/www/html/search")
        logging.info("   cp -r workspace/hngpt/front_end/search /var/www/html/")
        logging.info("\n   # 启动后端容器")
        logging.info("   docker run -d --name hngpt-app --network host \\")
        logging.info("     -v /workspace/hngpt:/workspace/hngpt \\")
        logging.info("     -v /workspace/mysql/data:/workspace/mysql/data \\")
        logging.info("     -v /workspace/redis/data:/workspace/redis/data \\")
        logging.info("     hngpt-bi:latest")
        logging.info("\n   # 检查服务状态")
        logging.info("   docker logs hngpt-app")
        logging.info("   curl http://*************:18888/docs")
    else:
        logging.error("更新包创建失败")
        sys.exit(1)

if __name__ == "__main__":
    main() 