#!/usr/bin/env python3
import requests
import json
import time

def test_assembly_fixes():
    print("🧪 快速测试汇编接口修复效果")
    
    # 1. 获取认证令牌
    auth_data = {
        "user_id": 1,
        "user_name": "admin", 
        "nick_name": "管理员",
        "dept_id": 100,
        "dept_name": "测试部门",
        "roles": ["admin"]
    }
    
    try:
        # 获取token
        print("🔑 获取认证令牌...")
        response = requests.post(
            "http://localhost:18888/api/simple-auth/generate-token",
            json=auth_data,
            timeout=10
        )
        
        if response.status_code != 200:
            print(f"❌ 认证失败: {response.status_code} - {response.text}")
            return False
            
        token = response.json().get("access_token")
        print("✅ 认证成功")
        
        # 2. 测试汇编接口
        headers = {"Authorization": f"Bearer {token}"}
        request_data = {
            "project_name": "修复测试项目",
            "action": "项目档案", 
            "urls": ["http://***********:9000/hngpt/test/sample1.json"]
        }
        
        print("\n🚀 开始汇编测试...")
        response = requests.post(
            "http://localhost:18888/assembly_doc?wait=true",
            headers=headers,
            json=request_data,
            timeout=120
        )
        
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            data = result.get("data", {})
            
            # 检查提取数据
            extracted_info = data.get("extracted_info", {})
            if extracted_info:
                print(f"✅ 找到提取数据: {len(extracted_info)} 个字段")
                print(f"   字段: {list(extracted_info.keys())}")
            else:
                print("❌ 未找到提取数据")
            
            # 检查处理结果
            processed = data.get("processed", [])
            print(f"📈 处理成功: {len(processed)} 个文档")
            
            return True
        else:
            print(f"❌ 汇编失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = test_assembly_fixes()
    if success:
        print("\n🎉 测试完成！")
    else:
        print("\n💥 测试失败！")
