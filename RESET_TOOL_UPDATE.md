# 重置工具更新说明

## 📋 更新内容

### ✅ 新增清空表

根据用户要求，已将 `assembly_conversations` 表添加到重置工具的清空列表中。

### 🔧 修改的文件

#### 1. **主要重置工具**
- **`reset.py`** - 完整版重置工具
- **`reset_simple.py`** - 简化版重置工具

#### 2. **辅助工具**
- **`test_reset_tool.py`** - 功能测试脚本
- **`demo_reset_tool.py`** - 演示脚本
- **`verify_reset_tables.py`** - 验证脚本（新增）

#### 3. **文档**
- **`docs/reset_tool_guide.md`** - 使用指南

### 📊 现在清空的数据

#### **Elasticsearch 索引**：
- `docs` - 文档内容索引
- `projectinfo` - 项目信息索引

#### **MySQL 数据库表**：
- `documents` - 文档基础信息表
- `conference_extract` - 会议提取数据表
- `project_extract` - 项目提取数据表
- `document_permissions` - 文档权限表
- `permission_requests` - 权限请求表
- **`assembly_conversations`** - 汇编对话记录表 ⭐ **新增**

### 🚀 使用方法

#### **推荐使用完整版**：
```bash
python reset.py
```

#### **或使用简化版**：
```bash
python reset_simple.py
```

#### **验证重置效果**：
```bash
python verify_reset_tables.py
```

### 📝 确认提示更新

现在运行重置工具时会显示：

```
⚠️  警告：此操作将清空以下数据：
   📁 Elasticsearch 索引:
      - docs
      - projectinfo
   🗄️  MySQL 数据库表:
      - documents
      - conference_extract
      - project_extract
      - document_permissions
      - permission_requests
      - assembly_conversations  ⭐ 新增

❗ 此操作不可逆，所有数据将被永久删除！

确认执行重置操作吗？(yes/no):
```

### 🔍 验证工具

新增了 `verify_reset_tables.py` 验证脚本，可以：

1. **检查所有表的状态** - 显示每个表的记录数量
2. **验证 ES 索引状态** - 显示每个索引的文档数量
3. **确认重置效果** - 验证是否成功清空

### 📊 预期输出示例

重置完成后，验证脚本应该显示：

```
📊 表状态检查:
   🗄️ documents                  : ✅ 空
   🗄️ conference_extract         : ✅ 空
   🗄️ project_extract            : ✅ 空
   🗄️ document_permissions       : ✅ 空
   🗄️ permission_requests        : ✅ 空
   🗄️ assembly_conversations     : ✅ 空  ⭐ 新增

📊 索引状态检查:
   📁 docs            : ✅ 空
   📁 projectinfo     : ✅ 空
```

### ⚠️ 重要说明

1. **数据不可恢复** - 重置操作会永久删除所有数据
2. **包含对话记录** - 现在会清空所有汇编对话历史
3. **建议备份** - 重置前请确保重要数据已备份
4. **测试环境优先** - 建议先在测试环境验证

### 🎯 适用场景

- **开发测试** - 清空测试数据，重新开始
- **数据迁移** - 清空旧数据，准备导入新数据
- **系统重置** - 恢复到初始状态
- **清理对话** - 清除汇编对话历史记录
- **故障恢复** - 清除损坏的数据

### 🎉 总结

**`assembly_conversations` 表已成功添加到重置工具中！**

现在重置工具会清空：
- ✅ **6个 MySQL 表** (原来5个 + 新增1个)
- ✅ **2个 ES 索引**
- ✅ **完整的数据清理** - 包括汇编对话记录

所有相关文件和文档都已更新，工具可以安全使用！
