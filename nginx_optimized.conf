# 优化后的nginx配置文件
# 针对前端部署进行了优化

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    proxy_request_buffering off;
    proxy_buffering off;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 4096;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    include /etc/nginx/conf.d/*.conf;

    # 前端服务配置 (端口8080)
    server {
        listen   8080;
        server_name _;  # 匹配任何域名，适应nps转发

        # 日志配置
        access_log /var/log/nginx/frontend_access.log;
        error_log /var/log/nginx/frontend_error.log;

        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;

        # CORS设置 - 支持跨域访问
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-User-ID" always;
        add_header Access-Control-Allow-Credentials "true" always;

        # iframe嵌入支持 - 允许被Service A等服务嵌入
        add_header Content-Security-Policy "frame-ancestors 'self' http://localhost:* https://localhost:* http://***********:* https://***********:* http://*************:* https://*************:* http://*************:* https://*************:*; default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'" always;

        # 前端静态文件配置
        location / {
            root   /usr/share/nginx/html/search;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;

            # 静态资源缓存策略
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                # 添加CORS头到静态资源
                add_header Access-Control-Allow-Origin "*" always;
            }

            # HTML文件不缓存 - 确保更新后立即生效
            location ~* \.html$ {
                expires -1;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                # 添加CORS头到HTML文件
                add_header Access-Control-Allow-Origin "*" always;
            }

            # JSON文件不缓存 - 配置文件等
            location ~* \.json$ {
                expires -1;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Access-Control-Allow-Origin "*" always;
            }
        }

        # 健康检查端点
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # 后端API代理配置
        location /dev-api {
            proxy_pass http://***********:18888/;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_set_header X-Forwarded-Host $host;
            rewrite ^/dev-api/(.*)$ /$1 break;

            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 认证API代理
        location /auth {
            proxy_pass http://***********:18888/;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_set_header X-Forwarded-Host $host;
            rewrite ^/auth/(.*)$ /$1 break;

            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 长时间API代理 (汇编等)
        location /api-s {
            proxy_pass http://***********:18888/;
            proxy_read_timeout 600s;  # 10分钟超时
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            rewrite ^/api-s/(.*)$ /$1 break;
        }

        # 文件下载代理
        location /download {
            proxy_pass http://***********:18888;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 传递认证头到后端进行权限验证
            proxy_set_header Authorization $http_authorization;

            # 添加 CORS 头
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
        }

        # 静态文件服务
        location /file/ {
            add_header Access-Control-Allow-Origin *;
            alias /usr/app/search/file/;
            if ($request_filename ~* ^.*?\.(html|doc|pdf|zip|docx|txt)$) {
                add_header Content-Disposition attachment;
                add_header Content-Type application/octet-stream;
            }
            sendfile on;   # 开启高效文件传输模式
            autoindex on;  # 开启目录文件列表
            autoindex_exact_size on;  # 显示出文件的确切大小，单位是bytes
            autoindex_localtime on;  # 显示的文件时间为文件的服务器时间
            charset utf-8,gbk;  # 避免中文乱码
        }
    }

    # 其他服务配置保持不变...
    upstream server_main {
        server *************:8080;
    }
    upstream server_check {
        server *************:8889;
    }
    upstream server_ocr {
        server *************:8890;
    }
    upstream server_file {
        server *************:8891;
    }
    upstream server_thirdpartypush {
        server *************:8888;
    }
    upstream server_group {
        server *************:8099;
    }

    # 文档归档服务 (端口81)
    server {
        listen       81 default_server;
        listen       [::]:81 default_server;
        server_name  _;
        root         /usr/share/nginx/html/docarchive;
        client_max_body_size 4G;
        charset utf-8;

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

        location / {
            root   /usr/share/nginx/html/docarchive;
            try_files $uri $uri/ /index.html;
            index  index.html index.htm;
        }
        
        # API代理配置
        location /prod-api/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://server_main/;
        }
        
        location /prod-api/8888/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://server_thirdpartypush/;
        }
        
        location /prod-api/8889/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://server_check/;
        }
        
        location /prod-api/8890/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://server_ocr/;
        }
        
        location /prod-api/8891/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://server_file/;
        }
        
        location /prod-api/8099/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://server_group/;
        }

        location /docArchiveServer/  {
            proxy_pass http://127.0.0.1:8000/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        location /zstpServer0/  {
            proxy_pass http://127.0.0.1:40100/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /zstpServer1/  {
            proxy_pass http://127.0.0.1:40101/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }     

        error_page 404 /404.html;
        location = /40x.html {
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
        }
    }
    
    # OCR检查服务 (端口8089)
    upstream pythonScript {
        server **************:8686 weight=1;
        server **************:8686 weight=1;
        server **************:8686 weight=1;
    }
    
    server {
        listen   8089;
        server_name ocrCheck;
        location / {
            default_type application/json;
            proxy_pass http://pythonScript/api-script/script/unionDetect/$1?$args;
            proxy_read_timeout 1800s;
            proxy_send_timeout 1800s;
        }
    }
}
