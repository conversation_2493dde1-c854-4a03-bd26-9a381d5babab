FROM python:3.10.12-slim-bullseye
# 预设时区和语言环境
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV HOST=0.0.0.0

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    supervisor \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    locales \
    && rm -rf /var/lib/apt/lists/*

# 设置 locale
RUN locale-gen C.UTF-8 || true && \
    /usr/sbin/update-locale LANG=C.UTF-8

WORKDIR /workspace/hngpt

# 复制 requirements.txt 并安装依赖
COPY requirements.txt .
RUN pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple --no-cache-dir -r requirements.txt

# 复制配置文件
COPY supervisord.conf /etc/supervisor/
COPY app.conf /etc/supervisor/conf.d/

# 复制单个文件
COPY config.yaml prompts.py paper_qa.py knowledge_control.py app.py ./

# 复制目录
COPY utils utils
COPY templates templates
COPY static static
COPY loaders loaders
COPY data data
COPY agent agent

# 创建必要的目录
RUN mkdir -p /var/log/supervisor && \
    mkdir -p data/logs && \
    chown -R www-data:www-data /workspace/hngpt

# 暴露应用程序端口
EXPOSE 18888

# 使用supervisor启动应用
CMD ["/usr/bin/supervisord"]