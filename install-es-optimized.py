#!/usr/bin/env python3
"""
优化的Elasticsearch安装脚本
针对16GB内存限制进行优化
"""

import time
import requests
import subprocess
import os
import sys
import logging
import yaml
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('es_setup_optimized.log')
    ]
)
logger = logging.getLogger(__name__)

def load_config():
    """加载配置文件"""
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"配置文件加载失败: {e}")
        sys.exit(1)

def run_command(command, check=True):
    """执行shell命令"""
    try:
        logger.info(f"执行命令: {command}")
        result = subprocess.run(command, shell=True, check=check, 
                              capture_output=True, text=True)
        if result.stdout:
            logger.info(f"输出: {result.stdout}")
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {e}")
        if check:
            sys.exit(1)
        return e

def check_system_resources():
    """检查系统资源"""
    logger.info("检查系统资源...")
    
    # 检查内存
    try:
        with open('/proc/meminfo', 'r') as f:
            meminfo = f.read()
        
        for line in meminfo.split('\n'):
            if 'MemTotal:' in line:
                total_mem_kb = int(line.split()[1])
                total_mem_gb = total_mem_kb / 1024 / 1024
                logger.info(f"系统总内存: {total_mem_gb:.1f}GB")
                
                if total_mem_gb < 8:
                    logger.warning("内存不足8GB，建议增加内存")
                    return False
                elif total_mem_gb < 16:
                    logger.warning("内存较少，将使用保守配置")
                    return "conservative"
                else:
                    logger.info("内存充足")
                    return "normal"
    except Exception as e:
        logger.error(f"内存检查失败: {e}")
        return "conservative"

def get_optimized_es_config(memory_mode="conservative"):
    """根据内存情况获取优化的ES配置"""
    if memory_mode == "conservative":
        # 保守配置：适用于16GB以下内存
        return {
            "heap_size": "2g",  # 降低到2GB
            "additional_opts": "-XX:+UseG1GC -XX:MaxGCPauseMillis=200"
        }
    else:
        # 正常配置：适用于16GB以上内存
        return {
            "heap_size": "4g",
            "additional_opts": "-XX:+UseG1GC -XX:MaxGCPauseMillis=200"
        }

def cleanup_old_containers():
    """清理旧容器"""
    logger.info("清理旧容器...")
    
    containers = ["elasticsearch", "hngpt-app"]
    
    for container in containers:
        # 停止容器
        result = run_command(f"docker stop {container}", check=False)
        if result.returncode == 0:
            logger.info(f"容器 {container} 已停止")
        
        # 删除容器
        result = run_command(f"docker rm {container}", check=False)
        if result.returncode == 0:
            logger.info(f"容器 {container} 已删除")

def setup_elasticsearch_optimized(es_config, memory_mode="conservative"):
    """设置优化的Elasticsearch容器"""
    logger.info("设置优化的Elasticsearch容器...")
    
    # 获取优化配置
    opt_config = get_optimized_es_config(memory_mode)
    
    # 创建必要目录
    directories = [
        '/workspace/elasticsearch/data',
        '/workspace/elasticsearch/logs',
        '/workspace/elasticsearch/config'
    ]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"创建目录: {directory}")
    
    # 创建优化的elasticsearch.yml配置
    es_yml_content = f"""
# Elasticsearch优化配置
cluster.name: "hngpt-cluster"
node.name: "hngpt-node-1"
path.data: /usr/share/elasticsearch/data
path.logs: /var/log/elasticsearch

# 网络配置
network.host: 0.0.0.0
http.port: 9200

# 安全配置
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: false
xpack.security.http.ssl.enabled: false

# 内存优化
bootstrap.memory_lock: false
indices.memory.index_buffer_size: 20%
indices.memory.min_index_buffer_size: 96mb

# 性能优化
thread_pool.write.queue_size: 1000
thread_pool.search.queue_size: 1000

# 集群配置（单节点）
discovery.type: single-node
"""
    
    config_path = '/workspace/elasticsearch/config/elasticsearch.yml'
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(es_yml_content)
    logger.info(f"Elasticsearch配置文件已创建: {config_path}")
    
    # 启动优化的Elasticsearch容器
    es_start_command = f"""
    docker run -d \
      --name elasticsearch \
      --network host \
      --ulimit memlock=-1:-1 \
      --ulimit nofile=65535:65535 \
      -v es_data:/usr/share/elasticsearch/data \
      -v /workspace/elasticsearch/logs:/var/log/elasticsearch \
      -v /workspace/elasticsearch/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml \
      -e ES_JAVA_OPTS="-Xms{opt_config['heap_size']} -Xmx{opt_config['heap_size']} {opt_config['additional_opts']}" \
      -e ELASTIC_PASSWORD={es_config.get('password', 'elastic')} \
      --restart unless-stopped \
      es-ik
    """
    
    result = run_command(es_start_command)
    if result.returncode == 0:
        logger.info("Elasticsearch容器启动成功")
        return True
    else:
        logger.error("Elasticsearch容器启动失败")
        return False

def restore_data_if_exists():
    """如果存在备份数据则恢复"""
    logger.info("检查是否存在备份数据...")
    
    # 查找最新的备份目录
    backup_dirs = [d for d in os.listdir('/workspace') if d.startswith('es_backup_')]
    if not backup_dirs:
        logger.info("未找到备份数据")
        return True
    
    latest_backup = sorted(backup_dirs)[-1]
    backup_path = f"/workspace/{latest_backup}"
    
    logger.info(f"找到备份数据: {backup_path}")
    
    # 恢复数据
    restore_cmd = f"""
    docker run --rm \
      -v es_data:/target \
      -v {backup_path}:/backup \
      alpine \
      sh -c "cd /target && tar xzf /backup/es_data.tar.gz"
    """
    
    result = run_command(restore_cmd, check=False)
    if result.returncode == 0:
        logger.info("数据恢复成功")
        return True
    else:
        logger.warning("数据恢复失败，将使用空数据库")
        return True

def main():
    """主函数"""
    try:
        logger.info("开始优化的Elasticsearch安装...")
        
        # 检查系统资源
        memory_mode = check_system_resources()
        if memory_mode is False:
            logger.error("系统资源不足，无法继续")
            sys.exit(1)
        
        # 加载配置
        config = load_config()
        es_config = config['elasticsearch']
        
        # 清理旧容器
        cleanup_old_containers()
        
        # 恢复数据（如果存在）
        restore_data_if_exists()
        
        # 设置优化的Elasticsearch
        if not setup_elasticsearch_optimized(es_config, memory_mode):
            logger.error("Elasticsearch设置失败")
            sys.exit(1)
        
        # 等待启动
        logger.info("等待Elasticsearch启动...")
        time.sleep(30)
        
        # 验证连接
        from elasticsearch import Elasticsearch
        es = Elasticsearch(
            [f"http://localhost:9200"],
            basic_auth=(es_config.get('username', 'elastic'), es_config.get('password', 'elastic')),
            verify_certs=False
        )
        
        for i in range(10):
            try:
                if es.ping():
                    logger.info("Elasticsearch连接成功")
                    break
            except Exception as e:
                logger.info(f"等待连接... ({i+1}/10)")
                time.sleep(10)
        else:
            logger.error("Elasticsearch连接失败")
            sys.exit(1)
        
        logger.info("✅ 优化的Elasticsearch安装完成")
        logger.info(f"内存模式: {memory_mode}")
        logger.info(f"访问地址: http://localhost:9200")
        logger.info(f"用户名: {es_config.get('username', 'elastic')}")
        logger.info(f"密码: {es_config.get('password', 'elastic')}")
        
    except Exception as e:
        logger.error(f"安装过程中出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
