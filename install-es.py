import time
import requests
from elasticsearch import Elasticsearch
from elasticsearch.exceptions import ConnectionError
import yaml
import logging
import sys
import subprocess
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('es_setup.log')
    ]
)
logger = logging.getLogger(__name__)

def load_config():
    """加载配置文件"""
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"Error loading config: {e}")
        sys.exit(1)

def run_command(command):
    """执行shell命令"""
    try:
        logger.info(f"Running command: {command}")
        result = subprocess.run(command, shell=True, check=True, 
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                              text=True)
        logger.info(f"Command output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e.stderr}")
        return False

def check_container_exists(container_name):
    """检查容器是否存在"""
    cmd = f"docker ps -a --filter name=^/{container_name}$ --format '{{{{.Status}}}}'"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    return bool(result.stdout.strip())

def check_container_running(container_name):
    """检查容器是否正在运行"""
    cmd = f"docker ps --filter name=^/{container_name}$ --format '{{{{.Status}}}}'"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    return bool(result.stdout.strip())

def stop_container(container_name):
    """安全停止容器"""
    if check_container_running(container_name):
        logger.info(f"Stopping container {container_name}...")
        if not run_command(f"docker stop {container_name}"):
            logger.error(f"Failed to stop container {container_name}")
            return False
        logger.info(f"Container {container_name} stopped successfully")
    return True

def remove_container(container_name):
    """安全移除容器"""
    if check_container_exists(container_name):
        if not stop_container(container_name):
            return False
        logger.info(f"Removing container {container_name}...")
        if not run_command(f"docker rm {container_name}"):
            logger.error(f"Failed to remove container {container_name}")
            return False
        logger.info(f"Container {container_name} removed successfully")
    return True

def setup_containers(es_config):
    """启动和配置所有容器"""
    # ES容器名称和镜像
    es_container = "elasticsearch"
    es_image = "es-ik"  # 使用已存在的镜像
    
    # 应用容器名称和镜像
    app_container = "hngpt-app"
    app_image = "hngpt-bi"
    
    # 创建必要的目录
    directories = [
        '/workspace/elasticsearch/data',
        '/workspace/elasticsearch/logs',
        '/var/log/supervisor'
    ]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # 1. 处理 Elasticsearch 容器
    if check_container_running(es_container):
        logger.info(f"Container {es_container} is already running")
    else:
        if check_container_exists(es_container):
            # if not remove_container(es_container):
            logger.info(f"Container {es_container} is already exists")
            sys.exit(1)
        
        # 直接使用已有的 ES 镜像启动容器
        es_start_command = f"""
        docker run -d \
          --name {es_container} \
          --network host \
          --ulimit memlock=-1:-1 \
          --ulimit nofile=65535:65535 \
          -v es_data:/usr/share/elasticsearch/data \
          -v /workspace/elasticsearch/logs:/var/log/elasticsearch \
          -e ES_JAVA_OPTS="-Xms4g -Xmx4g" \
          -e ELASTIC_PASSWORD={es_config.get('password', 'elastic')} \
          -e xpack.security.enabled=true \
          {es_image}
        """
        
        if not run_command(es_start_command):
            logger.error("Failed to start Elasticsearch container")
            sys.exit(1)
        
        logger.info("Elasticsearch container started successfully")
    
    # 2. 处理应用容器
    if check_container_running(app_container):
        logger.info(f"Container {app_container} is already running")
    else:
        if check_container_exists(app_container):
            # if not remove_container(app_container):
            logger.info(f"Container {app_container} is already exists")
            sys.exit(1)
        
        # 构建应用镜像
        # if not run_command("docker build -t hngpt-bi -f Dockerfile ."):
        #     logger.error("Failed to build application image")
        #     sys.exit(1)
        
        # 启动应用容器
        app_start_command = f"""
        docker run -d \
          --name {app_container} \
          --network host \
          -v $(pwd):/workspace/hngpt \
          -v /var/log/supervisor:/var/log/supervisor \
          -e PORT=18888 \
          {app_image}
        """
        
        if not run_command(app_start_command):
            logger.error("Failed to start application container")
            sys.exit(1)
        
        logger.info("Application container started successfully")

def wait_for_elasticsearch(es_config):
    """等待 Elasticsearch 启动"""
    host = es_config.get('host', 'localhost')
    port = es_config.get('port', 9200)
    username = es_config.get('username', 'elastic')
    password = es_config.get('password', 'elastic')
    
    url = f"http://{host}:{port}"
    es = Elasticsearch(
        [url],
        basic_auth=(username, password),
        verify_certs=False
    )
    
    for _ in range(30):  # 最多等待5分钟
        try:
            if es.ping():
                logger.info("Successfully connected to Elasticsearch")
                return es
        except ConnectionError:
            logger.info("Waiting for Elasticsearch to start...")
            time.sleep(10)
    
    logger.error("Could not connect to Elasticsearch after 5 minutes")
    sys.exit(1)

def setup_index(es, index_name):
    """设置索引和映射"""
    # 索引映射
    mapping = {
        "settings": {
            "analysis": {
                "analyzer": {
                    "text_analyzer": {
                        "type": "custom",
                        "tokenizer": "ik_max_word",
                        "filter": ["lowercase", "asciifolding"]
                    }
                }
            },
            "index": {
                "max_ngram_diff": 50,
                "mapping": {
                    "total_fields": {
                        "limit": 2000
                    }
                }
            }
        },
        "mappings": {
            "properties": {
                "content": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "search_analyzer": "ik_smart",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 256
                        }
                    }
                },
                "project_name": {
                    "type": "keyword",
                    "fields": {
                        "text": {
                            "type": "text",
                            "analyzer": "ik_smart"
                        }
                    }
                },
                "source": {
                    "type": "keyword"
                },
                "page_num": {
                    "type": "integer"
                },
                "doc_id": {
                    "type": "keyword"
                },
                "action": {
                    "type": "keyword"
                },
                "metadata": {
                    "type": "object",
                    "dynamic": True
                },
                "embedding": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True,
                    "similarity": "cosine"
                },
                "year": {
                    "type": "integer"
                }
            }
        }
    }

    try:
        # 检查索引是否存在
        if not es.indices.exists(index=index_name):
            # 创建索引
            es.indices.create(index=index_name, body=mapping)
            logger.info(f"Successfully created index: {index_name}")
        else:
            logger.info(f"Index {index_name} already exists")
            
        # 验证IK分词器
        test_text = "中华人民共和国"
        analysis = es.indices.analyze(
            body={
                "analyzer": "ik_smart",
                "text": test_text
            }
        )
        logger.info(f"IK analyzer test result: {analysis}")
        
    except Exception as e:
        logger.error(f"Error setting up index: {e}")
        sys.exit(1)

def main():
    """主函数"""
    try:
        # 加载配置
        config = load_config()
        es_config = config['elasticsearch']
        
        # 启动所有容器
        logger.info("Setting up containers...")
        setup_containers(es_config)
        
        # 等待 ES 启动
        logger.info("Waiting for Elasticsearch to start...")
        es = wait_for_elasticsearch(es_config)
        
        # 设置索引
        index_name = es_config.get('index_name', 'docs')
        setup_index(es, index_name)
        
        logger.info("Setup completed successfully")
        
        # 打印连接信息
        logger.info(f"""
        Services are ready to use:
        
        Elasticsearch:
        URL: http://{es_config.get('host', 'localhost')}:{es_config.get('port', 9200)}
        Username: {es_config.get('username', 'elastic')}
        Password: {es_config.get('password', 'elastic')}
        Index: {index_name}
        
        Application:
        URL: http://localhost:5000
        API Docs: http://localhost:5000/docs
        """)
        
    except Exception as e:
        logger.error(f"Error in setup: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 