from fastapi import FastAPI, Query, Body, applications, HTTPException, Request, Depends, UploadFile, Form, File, Path
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any, Optional
from auth.router import router as auth_router
from auth.models import User, ProjectExtract, ConferenceExtract
from database import get_db, Base, engine
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import hashlib
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.staticfiles import StaticFiles
from middleware.auth_middleware import log_user_action
from middleware.jwt_auth import get_current_user_from_jwt, get_current_user_from_jwt_or_query
from services.document_service import DocumentService
import uuid
import asyncio
import json
from datetime import datetime
from contextlib import asynccontextmanager
from pydantic import BaseModel, Field
from paper_qa import PaperQA
from utils.task_manager import TaskStatus, task_manager
from utils.config import config
from utils.log import log
from utils.sqlite_client import SQLiteClient
# SimpleFastAssemblyProcessor - 已移除（简化系统）
import httpx
import os
import re
from starlette.responses import RedirectResponse, StreamingResponse
from prompts import Prompts
from utils.llm import LLM
from utils.agent import Agent
from agent.agent import O1Agent
from agent.qagent import QueryAgent
from utils.utils import load_json
from minio import Minio
from urllib.parse import quote, unquote_plus
from fastapi.responses import StreamingResponse
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi import Security, Depends
import functools
from sync import sync_router
from utils.data_cleaner import clean_response
# # 导入第二阶段智能API模块
# try:
#     from api.phase2_intelligent_api import router as phase2_intelligent_router, initialize_phase2_components
#     phase2_intelligent_available = True
# except ImportError as e:
#     log.warning(f"第二阶段智能API模块不可用: {e}")
#     phase2_intelligent_available = False

# 全局 SQLite 客户端实例
db_client = None

def get_db_client():
    global db_client
    if db_client is None or not db_client.is_connected():
        db_path_relative = config.get("sqlite.db_path", "data/db/knowledge.db")
        current_dir = os.path.dirname(os.path.abspath(__file__))
        db_path_absolute = os.path.join(current_dir, db_path_relative)
        db_client = SQLiteClient(db_path=db_path_absolute)
        if not db_client.connect():
            log.error("无法连接到数据库以进行权限检查")
            raise HTTPException(status_code=500, detail="数据库连接错误")
    return db_client

async def check_file_permission(user_id: int, file_path: str, permission_type: str) -> bool:
    """检查用户对文件的权限"""
    try:
        # 临时解决方案：对于系统管理员用户（user_id=1）直接允许访问
        if user_id == 1:
            log.info(f"系统管理员用户 {user_id} 拥有所有文件权限")
            return True

        client = get_db_client()
        sql = """
            SELECT 1 FROM file_permissions
            WHERE user_id = ? AND file_path = ? AND permission_type = ?
        """
        result = client.query(sql, (user_id, file_path, permission_type))
        return len(result) > 0
    except Exception as e:
        log.error(f"检查权限时出错: user_id={user_id}, file_path={file_path}, error={e}")
        # 对于系统管理员用户，即使出错也允许访问
        if user_id == 1:
            log.warning(f"权限检查出错，但允许系统管理员用户 {user_id} 访问")
            return True
        return False # 默认拒绝访问以保证安全


# 创建 lifespan 事件处理器
@asynccontextmanager
async def lifespan(_app: FastAPI):
    # 启动事件
    log.info("应用程序启动完成 - 使用MySQL数据库")

    # 初始化HNGPT Agent系统集成
    # global hngpt_task_integration
    # hngpt_task_integration = initialize_hngpt_integration(task_manager)
    # log.info("HNGPT Agent系统集成初始化完成")

    # 初始化第二阶段智能组件
    # if phase2_intelligent_available:
    #     try:
    #         await initialize_phase2_components()
    #         log.info("第二阶段智能组件初始化完成")
    #     except Exception as e:
    #         log.error(f"第二阶段智能组件初始化失败: {e}")

    # 第三阶段优化组件 - 已移除

    yield

    # 关闭事件（如果需要的话）
    log.info("应用程序关闭")

# 创建FastAPI应用
app = FastAPI(
   title="Knowledge Base API",
   description="Knowledge Base and Document Management API",
   version="1.0.0",
   swagger_ui_parameters={"persistAuthorization": True},
   lifespan=lifespan
)

# 旧的 on_event 装饰器已被 lifespan 函数替代

# Include auth router with prefix
app.include_router(auth_router, prefix="/auth", tags=["auth"])

# Include permission router
try:
    from routers.permission_router import router as permission_router
    from routers.document_router import router as document_router
    app.include_router(permission_router, prefix="/api", tags=["permissions"])
    app.include_router(document_router, prefix="/api", tags=["documents"])
    log.info("权限管理和文档管理路由已加载")
except ImportError as e:
    log.warning(f"无法加载权限管理或文档管理路由: {e}")
except Exception as e:
    log.error(f"加载权限管理或文档管理路由时发生错误: {e}")

# Include department admin router
try:
    from routers.department_admin_router import router as department_admin_router
    app.include_router(department_admin_router, prefix="/api", tags=["department-admin"])
    log.info("部门管理员路由已加载")
except ImportError as e:
    log.warning(f"无法加载部门管理员路由: {e}")
except Exception as e:
    log.error(f"加载部门管理员路由时发生错误: {e}")

# Include simple service A auth router for testing
try:
    from auth.simple_service_a_router import router as simple_service_a_router
    app.include_router(simple_service_a_router, prefix="/api", tags=["simple-auth"])
    log.info("简化服务A认证路由已加载")
except ImportError as e:
    log.warning(f"无法加载简化服务A认证路由: {e}")
except Exception as e:
    log.error(f"加载简化服务A认证路由时发生错误: {e}")

# 测试接口路由已删除 - 简化系统配置
# try:
#     from api.test_endpoints import router as test_endpoints_router
#     app.include_router(test_endpoints_router, tags=["测试接口"])
#     log.info("测试接口路由已加载")
# except ImportError as e:
#     log.warning(f"无法加载测试接口路由: {e}")
# except Exception as e:
#     log.error(f"加载测试接口路由时发生错误: {e}")

# Include secure search router
try:
    from search.router import router as search_router
    app.include_router(search_router, prefix="/api", tags=["search"])
    log.info("安全搜索路由已加载")
except ImportError as e:
    log.warning(f"无法加载安全搜索路由: {e}")
except Exception as e:
    log.error(f"加载安全搜索路由时发生错误: {e}")

# Include assembly history router
try:
    from routers.assembly_history_router import router as assembly_history_router
    app.include_router(assembly_history_router, prefix="/api", tags=["assembly-history"])
    log.info("汇编历史路由已加载")
except ImportError as e:
    log.warning(f"无法加载汇编历史路由: {e}")

# Include fast assembly router - temporarily disabled due to import issues
# try:
#     from routers.fast_assembly_router import router as fast_assembly_router
#     app.include_router(fast_assembly_router, tags=["fast-assembly"])
#     log.info("快速汇编路由已加载")
# except ImportError as e:
#     log.info(f"快速汇编路由不可用: {e}")
# except Exception as e:
#     log.info(f"快速汇编路由加载失败: {e}")
#     # 详细错误信息用于调试
#     import traceback
#     log.error(f"快速汇编路由详细错误: {traceback.format_exc()}")
#log.info("快速汇编路由已暂时禁用，等待修复导入问题")

# Include data sync router
try:
    app.include_router(sync_router, tags=["数据同步"])
    log.info("数据同步路由已加载")
except ImportError as e:
    log.warning(f"无法加载数据同步路由: {e}")
except Exception as e:
    log.error(f"加载数据同步路由时发生错误: {e}")

# Phase 1 ES API - 已移除（简化系统）

# Include Phase 2 Intelligent API router
# if phase2_intelligent_available:
#     try:
#         app.include_router(phase2_intelligent_router, tags=["第二阶段-智能抽取"])
#         log.info("第二阶段智能API路由已加载")
#     except Exception as e:
#         log.error(f"加载第二阶段智能API路由时发生错误: {e}")

# Phase 3 Optimization API - 已移除（不需要性能监控）

# Agent工作流路由 - 已移除（简化系统）

# 文档记录创建函数
async def create_document_records(
    db: AsyncSession,
    processed_files: List[Dict[str, Any]],
    project_name: str,
    owner_id: int,
    department_id: int = None
):
    """在汇编成功后创建文档记录"""
    try:
        for file_info in processed_files:
            try:
                # 从处理结果中提取信息
                file_url = file_info.get("url", "")
                doc_id = file_info.get("doc_id", "")

                if not doc_id or not file_url:
                    log.warning(f"跳过无效的文件信息: {file_info}")
                    continue

                # 检查文档是否已存在
                existing_doc = await DocumentService.get_document_by_doc_id(db, doc_id)
                if existing_doc:
                    # 文档已存在，更新项目名称和其他信息
                    try:
                        existing_doc.project_name = project_name
                        existing_doc.owner_id = owner_id
                        existing_doc.department_id = department_id
                        await db.commit()
                        await db.refresh(existing_doc)
                        log.info(f"文档已存在，已更新信息: doc_id={doc_id}, project_name={project_name}")
                    except Exception as e:
                        log.error(f"更新已存在文档失败: doc_id={doc_id}, 错误: {e}")
                        await db.rollback()
                    continue

                # 生成文档标题
                file_name = file_url.split('/')[-1]
                title = os.path.splitext(file_name)[0] if file_name else project_name

                # 创建文档记录
                await DocumentService.create_document(
                    db=db,
                    doc_id=doc_id,
                    title=title,
                    file_path=file_url,
                    owner_id=owner_id,
                    department_id=department_id,
                    project_name=project_name
                )

                log.info(f"文档记录创建成功: doc_id={doc_id}, title={title}")

            except Exception as e:
                log.error(f"创建文档记录失败: {file_info}, 错误: {e}")
                continue

    except Exception as e:
        log.error(f"批量创建文档记录失败: {e}")

# ensure_extract_tables_exist 函数已移至 knowledge_control.py 统一管理

def generate_key_from_name(name: str) -> str:
    """根据名称生成唯一标识"""
    return hashlib.md5(name.encode('utf-8')).hexdigest()

def clean_date_value(date_value):
    """清理日期值，将无效值转换为默认日期"""
    print(f"🔧 clean_date_value called with: {repr(date_value)}")

    if not date_value or date_value in ['未提及', '未知', '无', 'None', 'null', '']:
        result = '1900-01-01'
        print(f"🔧 clean_date_value returning default: {result}")
        return result  # 默认日期

    # 如果已经是有效的日期格式，直接返回
    if isinstance(date_value, str) and len(date_value) == 10 and '-' in date_value:
        try:
            # 验证日期格式
            from datetime import datetime
            datetime.strptime(date_value, '%Y-%m-%d')
            print(f"🔧 clean_date_value returning valid date: {date_value}")
            return date_value
        except ValueError:
            result = '1900-01-01'
            print(f"🔧 clean_date_value returning default for invalid date: {result}")
            return result

    result = '1900-01-01'
    print(f"🔧 clean_date_value returning default fallback: {result}")
    return result

def clean_numeric_value(numeric_value):
    """清理数值字段，将无效值转换为默认值"""
    if not numeric_value or numeric_value in ['未提及', '未知', '无', 'None', 'null', '']:
        return 0

    # 尝试转换为数字
    if isinstance(numeric_value, (int, float)):
        return numeric_value

    if isinstance(numeric_value, str):
        # 移除常见的非数字字符
        cleaned = numeric_value.replace('万元', '').replace('元', '').replace(',', '').replace('，', '').strip()
        try:
            return float(cleaned)
        except ValueError:
            return 0

    return 0

async def upsert_extract_data(db: AsyncSession, action: str, project_name: str, extracted_data: dict):
    """根据action类型插入或更新提取数据"""
    try:
        if action == "项目档案":
            # 生成项目key
            project_key = generate_key_from_name(project_name)

            # 构建REPLACE INTO SQL - 包含project_key字段
            replace_sql = """
            REPLACE INTO `project_extract` (
                `project_name`, `project_key`, `project_no`, `start_date`, `end_date`,
                `total_investment`, `responsible_unit`, `leader`, `research_points`,
                `innovation`, `main_deliverables`, `patent`
            ) VALUES (
                :project_name, :project_key, :project_no, :start_date, :end_date,
                :total_investment, :responsible_unit, :leader, :research_points,
                :innovation, :main_deliverables, :patent
            )
            """

            params = {
                'project_name': project_name,
                'project_key': project_key,
                'project_no': extracted_data.get('project_no') or '未提及',
                'start_date': clean_date_value(extracted_data.get('start_date')),
                'end_date': clean_date_value(extracted_data.get('end_date')),
                'total_investment': clean_numeric_value(extracted_data.get('total_investment')),
                'responsible_unit': extracted_data.get('responsible_unit') or '未提及',
                'leader': extracted_data.get('leader') or '未提及',
                'research_points': extracted_data.get('research_points') or '未提及',
                'innovation': extracted_data.get('innovation') or '未提及',
                'main_deliverables': extracted_data.get('main_deliverables') or '未提及',
                'patent': extracted_data.get('patent') or '未提及'
            }

        elif action == "文书档案":
            # 生成文书key
            conference_key = generate_key_from_name(project_name)

            # 构建REPLACE INTO SQL
            replace_sql = """
            REPLACE INTO `conference_extract` (
                `conference_name`, `conference_key`, `conference_no`, `date`, `type`,
                `organizer`, `participants`, `summary`
            ) VALUES (
                :conference_name, :conference_key, :conference_no, :date, :type,
                :organizer, :participants, :summary
            )
            """

            params = {
                'conference_name': project_name,
                'conference_key': conference_key,
                'conference_no': extracted_data.get('conference_no') or '未提及',
                'date': clean_date_value(extracted_data.get('date')),
                'type': extracted_data.get('type') or '未提及',
                'organizer': extracted_data.get('organizer') or '未提及',
                'participants': extracted_data.get('participants') or '未提及',
                'summary': extracted_data.get('summary') or '未提及'
            }
        else:
            log.warning(f"未知的action类型: {action}")
            return

        await db.execute(text(replace_sql), params)
        await db.commit()
        log.info(f"成功更新{action}数据: {project_name}")

    except Exception as e:
        log.error(f"更新提取数据失败: {e}")
        await db.rollback()
        raise

# 添加安全方案
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def swagger_path(app):
    if app.openapi_url and app.docs_url:
        assets_path = os.path.join(os.path.dirname(__file__),"static")
        if os.path.exists(assets_path + "/swagger-ui.css") and os.path.exists(assets_path + "/swagger-ui-bundle.js"):
            app.mount("/assets", StaticFiles(directory=assets_path), name="static")
            def swagger_patch(*args, **kwargs):
                return get_swagger_ui_html(
                    *args,
                    **kwargs,
                    swagger_favicon_url="/assets/favicon.png",
                    swagger_css_url="/assets/swagger-ui.css",
                    swagger_js_url="/assets/swagger-ui-bundle.js",
                )
            applications.get_swagger_ui_html = swagger_patch
            
swagger_path(app)

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")

class AssemblyDocumentRequest(BaseModel):
    action: str  # 文档类型，如"文书档案"等
    project_name: str  # 项目名称
    urls: List[str]  # 文档URL列表
    callback_url: Optional[str] = None  # 可选的回调URL

# 请求和响应模型
class TxtRequestData(BaseModel):
    enable_seal: bool
    enable_ocr: bool
    enable_handwrite: bool
    urls: List[str]
    project_name: Optional[str] = None
    callback_url: Optional[str] = None  # 可选调URL

class BaseResponse(BaseModel):
    code: int
    msg: str
    data: Optional[Any] = None

class ListDocsResponse(BaseResponse):
    data: List[str]

class PromptResponse(BaseResponse):
    data: str

class ChatMessage(BaseModel):
    question: str
    response: str
    history: Optional[List[Dict[str, str]]]
    source_documents: List[str]
 
class OptionItem(BaseModel):
    value: str
    label: str
    children: Optional[List[Dict[str, str]]]
    
       
class DictKeysResponse(BaseResponse):
    data: Dict[str, List[OptionItem]]


async def send_callback(url: str, data: Dict[str, Any]):
    """发送回调请求，支持重试机制"""
    max_retries = 3  # 最大重试次数
    base_delay = 1.0  # 基础延迟时间（秒）
    
    # 获取任务数据，确保有默认值
    task_data = data.get("data", {}) or {}
    
    # 构建统一的回调数据格式
    callback_data = {
        "code": data.get("code", 500),
        "msg": data.get("msg", "Unknown status"),
        "data": {
            "task_id": task_data.get("task_id"),
            "status": task_data.get("status", "unknown"),
            "progress": task_data.get("progress", 0),
            "processed": task_data.get("processed", []),
            "failed": task_data.get("failed", []),
            "error": task_data.get("error", "")
        }
    }
    
    for attempt in range(max_retries):
        try:
            print(f"\n===== 发送回调请求 (尝试 {attempt + 1}/{max_retries}) =====")
            print(f"目标URL: {url}")
            print(f"回调数据: {json.dumps(callback_data, ensure_ascii=False, indent=2)}")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    json=callback_data,
                    timeout=300.0
                )
                
                # 检查响应状态码
                if response.status_code == 200:
                    print(f"回调请求成功发送到 {url}")
                    return
                else:
                    print(f"回调请求返回非200状态码: {response.status_code}")
                    print(f"响应内容: {response.text}")
                    
                    # 如果不是最后一次尝试，则继续重试
                    if attempt < max_retries - 1:
                        delay = base_delay * (2 ** attempt)  # 指数退避
                        print(f"等待 {delay} 秒后重试...")
                        await asyncio.sleep(delay)
                        continue
                    else:
                        print("已达到最大重试次数，回调发送失败")
                        return
                        
        except httpx.TimeoutException:
            print(f"回调请求超时 (尝试 {attempt + 1}/{max_retries})")
            if attempt < max_retries - 1:
                delay = base_delay * (2 ** attempt)
                print(f"等待 {delay} 秒后重试...")
                await asyncio.sleep(delay)
                continue
            else:
                print("已达到最大重试次数，回调发送失败")
                return
                
        except httpx.ConnectError as e:
            print(f"连接错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                delay = base_delay * (2 ** attempt)
                print(f"等待 {delay} 秒后重试...")
                await asyncio.sleep(delay)
                continue
            else:
                print("已达到最大重试次数，回调发送失败")
                return
                
        except Exception as e:
            print(f"发送回调时发生错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            
            if attempt < max_retries - 1:
                delay = base_delay * (2 ** attempt)
                print(f"等待 {delay} 秒后重试...")
                await asyncio.sleep(delay)
                continue
            else:
                print("已达到最大重试次数，回调发送失败")
                return
    
    print("所有重试都失败，回调发送失败")

# 文本处理接口
@app.post("/txt_info", response_model=BaseResponse)
async def txt_info(
    request: TxtRequestData,
    wait: bool = Query(False, description="是否等待任务完成"),
    _timeout: float = Query(300.0, description="等待超时时间(秒)")  # 保留用于API文档
):
    """处理文件并提取文本信息"""
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())
        log.info(f"Starting task {task_id}")
        
        # 获取回调URL
        callback_url = request.callback_url
        if not callback_url:
            cb_endpoint = config.get('cb.endpoint')
            cb_route = config.get('cb.route')
            if cb_endpoint and cb_route:
                callback_url = f"http://{cb_endpoint}{cb_route}"
        
        # 创建异步任务
        async def process_task():
            try:
                # 更新任务状态为运行中
                await task_manager.update_result(
                    task_id,
                    status=TaskStatus.RUNNING
                )
                
                callback_data = None  # 初始化回调数据
                
                try:
                    qa = PaperQA(
                        api_url=config.get('llm.api_url'),
                        token=config.get('llm.token'),
                        cache_dir="cache",
                        minio_config=config.get('minio')
                    )
                    missing_urls = []
                    for url in request.urls:
                        if not qa.minio.check_file_exists(url):
                            missing_urls.append(url)
                    
                    if missing_urls:
                        txt_result = await qa.process_txt_info(
                            urls=missing_urls,
                            enable_ocr=False,
                            enable_seal=False,
                            enable_handwrite=False,
                            task_id=task_id,
                            task_manager=task_manager,
                            project_name=request.project_name
                        )
                        if len(txt_result.get("failed", 0)) > 0:
                            error_msg = "JSON文件生成失败"
                            await task_manager.update_result(task_id, status=TaskStatus.FAILED, error=error_msg)
                            callback_data = {
                                "code": 500,
                                "msg": error_msg,
                                "data": txt_result
                            }
                            return  # 终止任务
                    # 处理文件
                    result = await qa.process_txt_info(
                        urls=request.urls,
                        enable_ocr=request.enable_ocr,
                        enable_seal=request.enable_seal,
                        enable_handwrite=request.enable_handwrite,
                        task_id=task_id,
                        task_manager=task_manager,
                        project_name=request.project_name if request.project_name is not None else "default_project"
                    )
                    
                    # 检查处理结果
                    if not result["processed"] and result["failed"]:
                        await task_manager.update_result(
                            task_id,
                            status=TaskStatus.FAILED,
                            error="所有文件处理失败"
                        )
                        callback_data = {
                            "code": 500,
                            "msg": "所有文件处理失败",
                            "data": result
                        }
                    elif result["failed"]:
                        await task_manager.update_result(
                            task_id,
                            status=TaskStatus.COMPLETED,
                            error="部分文件处理失败"
                        )
                        callback_data = {
                            "code": 206,
                            "msg": "部分文件处理失败",
                            "data": result
                        }
                    else:
                        await task_manager.update_result(
                            task_id,
                            status=TaskStatus.COMPLETED
                        )
                        callback_data = {
                            "code": 200,
                            "msg": "所有文件处理成功",
                            "data": result
                        }
                    
                except Exception as e:
                    log.error(f"Error in process_task: {str(e)}", exc_info=True)
                    error_msg = str(e)
                    await task_manager.update_result(
                        task_id,
                        status=TaskStatus.FAILED,
                        error=error_msg
                    )
                    callback_data = {
                        "code": 500,
                        "msg": f"处理失败: {error_msg}",
                        "data": {
                            "task_id": task_id,
                            "error": error_msg,
                            "status": "failed"
                        }
                    }
                    
            except Exception as e:
                log.error(f"Unexpected error in process_task: {str(e)}", exc_info=True)
                await task_manager.update_result(
                    task_id,
                    status=TaskStatus.FAILED,
                    error=f"Unexpected error: {str(e)}"
                )
                callback_data = {
                    "code": 500,
                    "msg": f"意外错误: {str(e)}",
                    "data": {
                        "task_id": task_id,
                        "error": str(e),
                        "status": "failed"
                    }
                }
            
            finally:
                # 在任务结束时总是尝试发送回调
                if callback_url and callback_data:
                    try:
                        await send_callback(callback_url, callback_data)
                    except Exception as e:
                        log.error(f"Error sending callback: {str(e)}", exc_info=True)
        
        # 创建任务
        await task_manager.create_task(
            task_id=task_id,
            coro=process_task(),
            total_files=len(request.urls),
            task_type="assembly",
            task_params={
                "project_name": request.project_name,
                "urls": request.urls,
                "callback_url": callback_url
            }
        )
        
        if wait:
            try:
                # 等待任务完成
                while True:
                    task_result = await task_manager.get_result(task_id)
                    if task_result["status"] in ["completed", "failed"]:
                        return BaseResponse(
                            code=200 if task_result["status"] == "completed" else 500,
                            msg="success" if task_result["status"] == "completed" else task_result.get("error", "Task failed"),
                            data=task_result
                        )
                    await asyncio.sleep(0.1)
                    
            except Exception as e:
                log.error(f"Error waiting for task: {str(e)}", exc_info=True)
                return BaseResponse(
                    code=500,
                    msg=f"Error waiting for task: {str(e)}",
                    data={"task_id": task_id}
                )
        else:
            # 立即返回任务ID
            return BaseResponse(
                code=202,
                msg="Task started",
                data={"task_id": task_id}
            )
            
    except Exception as e:
        log.error(f"Error in txt_info endpoint: {str(e)}", exc_info=True)
        return BaseResponse(
            code=500,
            msg=f"Server error: {str(e)}",
            data=None
        )

# 任务状态查询接口 (调试模式，暂时不需要认证)
@app.get("/task/{task_id}", response_model=BaseResponse)
async def get_task_status(task_id: str):
    """获取任务状态"""
    result = await task_manager.get_result(task_id)
    if not result:
        return BaseResponse(
            code=404,
            msg="Task not found",
            task_id=task_id
        )

    return BaseResponse(
        code=200,
        msg="success",
        task_id=task_id,
        data=result
    )

# 获取所有任务列表接口 (调试模式，暂时不需要认证)
@app.get("/tasks", response_model=BaseResponse)
async def get_all_tasks():
    """获取所有任务列表"""
    try:
        all_tasks = []
        for _task_id, result in task_manager.results.items():
            task_data = result.to_dict()
            all_tasks.append(task_data)

        # 按创建时间倒序排列
        all_tasks.sort(key=lambda x: x['created_at'], reverse=True)

        return BaseResponse(
            code=200,
            msg="success",
            data=all_tasks
        )
    except Exception as e:
        log.error(f"Error getting all tasks: {str(e)}")
        return BaseResponse(
            code=500,
            msg=f"Server error: {str(e)}",
            data=[]
        )

# 取消任务接口 (调试模式，暂时不需要认证)
@app.delete("/task/{task_id}", response_model=BaseResponse)
async def cancel_task(task_id: str):
    """取消任务"""
    task = task_manager.tasks.get(task_id)
    if not task:
        return BaseResponse(
            code=404,
            msg="Task not found",
            task_id=task_id
        )

    if not task.done():
        task.cancel()
        await task_manager.update_result(
            task_id,
            status=TaskStatus.FAILED,
            error="Task cancelled by user"
        )

    return BaseResponse(
        code=200,
        msg="Task cancelled",
        task_id=task_id
    )

# SSE任务进度推送接口 (调试模式，暂时不需要认证)
@app.get("/tasks/stream")
async def stream_tasks_progress():
    """SSE实时推送任务进度"""

    async def event_generator():
        try:
            while True:
                # 获取所有任务状态
                all_tasks = []
                for _task_id, result in task_manager.results.items():
                    task_data = result.to_dict()
                    all_tasks.append(task_data)

                # 按创建时间倒序排列
                all_tasks.sort(key=lambda x: x['created_at'], reverse=True)

                # 发送SSE数据
                data = {
                    "timestamp": asyncio.get_event_loop().time(),
                    "tasks": all_tasks
                }

                yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

                # 等待2秒后再次发送
                await asyncio.sleep(2)

        except asyncio.CancelledError:
            log.info("SSE connection cancelled")
        except Exception as e:
            log.error(f"SSE error: {str(e)}")
            yield f"data: {json.dumps({'error': str(e)}, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )



@app.post("/assembly_doc",
    response_model=BaseResponse,
    summary="文档汇编（快速版）",
    description="基于ES向量化的快速文档汇编和结构化信息提取，支持异步处理",
    responses={
        200: {
            "description": "汇编任务创建成功或完成",
            "content": {
                "application/json": {
                    "examples": {
                        "async_task": {
                            "summary": "异步任务创建成功",
                            "value": {
                                "code": 200,
                                "msg": "汇编任务已创建",
                                "data": {
                                    "task_id": "550e8400-e29b-41d4-a716-446655440000",
                                    "status": "running",
                                    "message": "文档汇编任务正在处理中",
                                    "created_at": "2025-07-01T08:30:00Z",
                                    "estimated_completion": "2025-07-01T08:35:00Z"
                                }
                            }
                        },
                        "sync_completed": {
                            "summary": "同步任务完成",
                            "value": {
                                "code": 200,
                                "msg": "汇编完成",
                                "data": {
                                    "task_id": "550e8400-e29b-41d4-a716-446655440000",
                                    "status": "completed",
                                    "result": {
                                        "project_name": "智慧城市建设项目",
                                        "document_type": "技术方案",
                                        "summary": "本项目旨在构建智慧城市基础设施...",
                                        "key_points": [
                                            "物联网传感器部署",
                                            "大数据分析平台",
                                            "智能交通系统"
                                        ],
                                        "documents_processed": 3,
                                        "processing_time": "2.5分钟"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 400,
                        "msg": "文档URL列表不能为空",
                        "data": None
                    }
                }
            }
        },
        403: {
            "description": "权限不足",
            "content": {
                "application/json": {
                    "example": {
                        "code": 403,
                        "msg": "无权限访问文档: /path/to/document.pdf",
                        "data": None
                    }
                }
            }
        }
    }
)
async def assembly_doc(
    request: AssemblyDocumentRequest,
    wait: bool = Query(False, description="是否等待任务完成，true为同步处理，false为异步处理"),
    _timeout: float = Query(300.0, description="等待超时时间(秒)，仅在wait=true时有效", ge=10, le=600),  # 保留用于API文档
    fast_mode: bool = Query(False, description="是否使用快速模式（基于批量处理优化）"),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """
    快速文档汇编并提取结构化信息 - 基于ES向量化的高性能处理

    **功能说明：**
    - 基于ES向量化预处理的快速文档汇编
    - 批量结构化信息提取，大幅提升处理速度
    - 智能多维度检索和信息合并
    - 支持异步和同步两种处理模式
    - 自动验证用户对所有文档的访问权限

    **性能优化：**
    1. 预处理阶段：文档批量向量化存入ES
    2. 智能检索：多维度检索策略获取相关信息
    3. 批量提取：高效的批量AI调用和信息合并
    4. 结构化存储：MySQL存储供后续问答使用

    **处理流程：**
    1. 验证用户对所有文档的访问权限
    2. 检查向量化状态，必要时进行预处理
    3. 批量提取结构化信息
    4. 存储到MySQL供问答系统使用
    5. 返回结构化的汇编结果

    **权限要求：**
    - 用户必须已登录（提供有效的JWT Token）
    - 用户必须对所有请求的文档具有读取权限

    **请求示例：**
    ```bash
    # 异步处理
    curl -X POST "http://localhost:18888/assembly_doc" \
         -H "Authorization: Bearer your_jwt_token" \
         -H "Content-Type: application/json" \
         -d '{
           "action": "技术方案",
           "project_name": "智慧城市项目",
           "urls": [
             "/docs/project/requirements.pdf",
             "/docs/project/design.docx",
             "/docs/project/implementation.pdf"
           ]
         }'

    # 同步处理（等待完成）
    curl -X POST "http://localhost:18888/assembly_doc?wait=true&timeout=300" \
         -H "Authorization: Bearer your_jwt_token" \
         -H "Content-Type: application/json" \
         -d '{
           "action": "技术方案",
           "project_name": "智慧城市项目",
           "urls": ["/docs/project/requirements.pdf"]
         }'
    ```

    **支持的文档类型：**
    - PDF文档
    - Word文档 (.doc, .docx)
    - 文本文件 (.txt)
    - 其他常见办公文档格式
    """
    try:
        log.info(f"用户 {current_user.user_name} 请求汇编文档: {request.urls}")

        # 生成任务ID
        task_id = str(uuid.uuid4())
        log.info(f"Starting assembly task {task_id} for user {current_user.user_name}")

        # 获取回调URL
        callback_url = request.callback_url
        if not callback_url:
            cb_endpoint = config.get('cb.endpoint')
            cb_route = config.get('cb.route')
            if cb_endpoint and cb_route:
                callback_url = f"http://{cb_endpoint}{cb_route}"
        
        # 创建异步任务
        async def process_task():
            try:
                await task_manager.update_result(
                    task_id,
                    status=TaskStatus.RUNNING
                )

                # 提取表创建已由 knowledge_control.init() 统一管理
                # 不再在此处重复创建

                callback_data = None

                try:
                    # 快速模式已禁用（简化系统）
                    if fast_mode:
                        log.warning("快速模式已禁用，使用标准处理模式")

                    # 使用标准处理逻辑
                    qa = PaperQA(
                        api_url=config.get('llm.api_url'),
                        token=config.get('llm.token'),
                        cache_dir="cache",
                        minio_config=config.get('minio')
                    )

                    missing_urls = []
                    for url in request.urls:
                        json_url = os.path.splitext(url)[0] + ".json"
                        if not qa.minio.check_file_exists(json_url):
                            missing_urls.append(url)

                    json_gen_errors = []
                    if missing_urls:
                        try:
                            txt_result = await qa.process_txt_info(
                                urls=missing_urls,
                                enable_ocr=False,
                                enable_seal=False,
                                enable_handwrite=False,
                                task_id=task_id,
                                task_manager=task_manager,
                                project_name=request.project_name
                            )
                            if len(txt_result.get("failed",[])) > 0:
                                json_gen_errors.extend(txt_result["failed"])
                        except Exception as e:
                            log.error(f"JSON生成阶段全局错误: {str(e)}")
                            json_gen_errors.append({"error": str(e), "urls": missing_urls})

                    # 处理文档
                    result = await qa.process_assembly(
                        urls=request.urls,
                        project_name=request.project_name,
                        action=request.action,
                        task_id=task_id,
                        task_manager=task_manager
                    )

                    # 合并JSON生成错误和处理错误
                    all_failed = json_gen_errors + result.get("failed", [])
                    result["failed"] = all_failed
                    
                    # 检查处理结果
                    if result["failed"]:
                        if not result["processed"]:
                            # 所有文件都失败
                            await task_manager.update_result(
                                task_id,
                                status=TaskStatus.FAILED,
                                error="所有文件处理失败"
                            )
                            callback_data = {
                                "code": 500,
                                "msg": "所有文件处理失败",
                                "data": result
                            }
                        else:
                            # 部分文件失败
                            await task_manager.update_result(
                                task_id,
                                status=TaskStatus.COMPLETED,
                                error="部分文件处理失败"
                            )
                            callback_data = {
                                "code": 206,
                                "msg": "部分文件处理失败",
                                "data": result
                            }
                    else:
                        # 任务成功完成，确保进度为100%
                        await task_manager.update_result(
                            task_id,
                            status=TaskStatus.COMPLETED
                        )
                        # 手动设置进度为100%（确保显示正确）
                        task_result = task_manager.results.get(task_id)
                        if task_result:
                            task_result.progress = 100
                        callback_data = {
                            "code": 200,
                            "msg": "所有文件处理成功",
                            "data": result
                        }

                        # 创建文档记录
                        await create_document_records(
                            db=db,
                            processed_files=result.get("processed", []),
                            project_name=request.project_name,
                            owner_id=current_user.user_id,
                            department_id=current_user.dept_id
                        )

                        # 更新提取数据到对应的表
                        try:
                            # 从处理结果中获取提取的数据
                            extracted_data = result.get("extracted_info", {})
                            if extracted_data:
                                await upsert_extract_data(
                                    db=db,
                                    action=request.action,
                                    project_name=request.project_name,
                                    extracted_data=extracted_data
                                )
                                log.info(f"成功更新{request.action}提取数据: {request.project_name}")
                            else:
                                log.warning(f"未找到提取数据，跳过数据库更新: {request.project_name}")
                        except Exception as e:
                            log.error(f"更新提取数据失败: {e}")
                            # 不影响主流程，继续执行
                    
                except Exception as e:
                    log.error(f"Error in process_task: {str(e)}", exc_info=True)
                    error_msg = str(e)
                    await task_manager.update_result(
                        task_id,
                        status=TaskStatus.FAILED,
                        error=error_msg
                    )
                    callback_data = {
                        "code": 500,
                        "msg": f"处理失败: {error_msg}",
                        "data": {
                            "task_id": task_id,
                            "error": error_msg,
                            "status": "failed"
                        }
                    }
                    
            except Exception as e:
                log.error(f"Unexpected error in process_task: {str(e)}", exc_info=True)
                await task_manager.update_result(
                    task_id,
                    status=TaskStatus.FAILED,
                    error=f"Unexpected error: {str(e)}"
                )
                callback_data = {
                    "code": 500,
                    "msg": f"意外错误: {str(e)}",
                    "data": {
                        "task_id": task_id,
                        "error": str(e),
                        "status": "failed"
                    }
                }
            
            finally:
                # 关闭连接以避免 "Unclosed client session" 警告
                try:
                    if 'qa' in locals() and qa and hasattr(qa, 'close'):
                        await qa.close()
                except Exception as e:
                    log.error(f"Error closing QA connections: {str(e)}")

                # 在任务结束时总是尝试发送回调
                if callback_url and callback_data:
                    try:
                        await send_callback(callback_url, callback_data)
                    except Exception as e:
                        log.error(f"Error sending callback: {str(e)}", exc_info=True)
        
        # 创建任务
        await task_manager.create_task(
            task_id=task_id,
            coro=process_task(),
            total_files=len(request.urls),
            task_type="assembly",
            task_params={
                "project_name": request.project_name,
                "action": request.action,
                "urls": request.urls,
                "callback_url": callback_url
            }
        )
        
        # 处理等待逻辑
        if wait:
            try:
                while True:
                    task_result = await task_manager.get_result(task_id)
                    if task_result["status"] in ["completed", "failed"]:
                        return BaseResponse(
                            code=200 if task_result["status"] == "completed" else 500,
                            msg="success" if task_result["status"] == "completed" else task_result.get("error", "Task failed"),
                            data=task_result
                        )
                    await asyncio.sleep(0.1)
            except Exception as e:
                log.error(f"Error waiting for task: {str(e)}", exc_info=True)
                return BaseResponse(
                    code=500,
                    msg=f"Error waiting for task: {str(e)}",
                    data={"task_id": task_id}
                )
        else:
            return BaseResponse(
                code=202,
                msg="Task started",
                data={"task_id": task_id}
            )
            
    except HTTPException:
        # 重新抛出HTTP异常（如权限错误）
        raise
    except Exception as e:
        log.error(f"Error in assembly_doc endpoint for user {current_user.user_name if 'current_user' in locals() else 'unknown'}: {str(e)}", exc_info=True)
        return BaseResponse(
            code=500,
            msg=f"Server error: {str(e)}",
            data=None
        )


@app.get("/")
async def document():
    return RedirectResponse(url="/docs")
       
           
app.get("/", response_model=BaseResponse)
async def document():
    return RedirectResponse(url="/docs")


@app.post("/chat", response_model=ChatMessage)
async def llm_chat(
    prompt: str = Body(...,description="prompt", example="名词解释"), 
    history: Optional[List[Dict[str, str]]] = Body(None, description="History text")
):
    try:
        # 初始化LLM
        llm = LLM(
            api_url=config.get('llm.api_url'),
            token=config.get('llm.token'),
            model=config.get('llm.model'),
            temperature=config.get('llm.temperature', 0.1),
            max_tokens=config.get('llm.max_tokens', 32000)
        )
        
        # 使用LLM进行对话
        response = await llm.chat([
            {
                "role": "system",
                "content": "你是一有帮的助手。"
            },
            {"role": "user", "content": prompt}
        ])

        # 清理响应中的思考内容
        cleaned_response = clean_response(response)

        return ChatMessage(
            question=prompt,
            response=cleaned_response,
            history=history or [],
            source_documents=[],
        )
    except Exception as e:
        log.error(f"Error in chat: {str(e)}")
        raise

# 异步汇编任务接口
@app.post('/assembly_task', response_model=BaseResponse)
async def create_assembly_task(
        request: Request,
        question: str = Body(..., description="Question", example="mate60"),
        prompt: str = Body(default="名词解释", description="prompt", example="名词解释"),
        promptName: str = Body(default="自定义汇编", description="promptName", example="自定义汇编"),
        project_type: Optional[str] = Body(default="科技项目", description="项目类型", example="科技项目"),
        year: Optional[str] = Body(default="2023", description="时间范围年", example="2023"),
        keywords: Optional[List[str]] = Body(default=[], description="关键词列表", example="['题目报告','实验记录']"),
        history: Optional[List[Dict[str, str]]] = Body(default=None, description="History text")
):
    """创建异步汇编任务"""
    try:
        # 获取当前用户ID
        user_id = None
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            try:
                from middleware.jwt_auth import JWTAuthService
                payload = JWTAuthService.decode_token(token)
                if payload:
                    user_id = payload.get("user_id")
                    if isinstance(user_id, str):
                        user_id = int(user_id)
            except Exception as e:
                log.warning(f"解析JWT token失败: {e}")

        # 如果没有用户ID，使用默认值（用于测试）
        if not user_id:
            user_id = 10
            log.warning(f"未找到用户ID，使用默认值: {user_id}")

        # 生成任务ID
        task_id = str(uuid.uuid4())
        log.info(f"Creating assembly task {task_id} for user {user_id}")

        # 保存任务参数
        task_params = {
            "question": question,
            "prompt": prompt,
            "promptName": promptName,
            "project_type": project_type,
            "year": year,
            "keywords": keywords,
            "history": history,
            "user_id": user_id  # 添加用户ID到任务参数
        }

        # 创建异步任务
        async def process_assembly_task():
            try:
                # 更新任务状态为运行中
                await task_manager.update_result(
                    task_id,
                    status=TaskStatus.RUNNING
                )

                # 调用原有的汇编逻辑
                result = await explain_query(
                    question, prompt, promptName, project_type, year, keywords, history
                )

                # 更新任务状态为完成，保存汇编结果
                await task_manager.update_result(
                    task_id,
                    status=TaskStatus.COMPLETED,
                    task_params={
                        "assembly_result": {
                            "question": result.question,
                            "response": result.response,
                            "history": result.history,
                            "source_documents": result.source_documents,
                            "echart": getattr(result, 'echart', None),
                            "image": getattr(result, 'image', None)
                        }
                    }
                )

                # 直接保存到数据库（作为备份机制）
                try:
                    await save_assembly_to_database(task_id, task_params, result)
                    log.info(f"汇编结果已保存到数据库: {task_id}")
                except Exception as db_error:
                    log.error(f"保存汇编结果到数据库失败: {db_error}")

            except Exception as e:
                log.error(f"Assembly task {task_id} failed: {str(e)}")

                # 根据异常类型提供更详细的错误信息
                error_message = str(e)
                error_type = "unknown"

                if "connection" in error_message.lower() or "network" in error_message.lower():
                    error_type = "network"
                    error_message = f"网络连接失败: {error_message}"
                elif "timeout" in error_message.lower():
                    error_type = "timeout"
                    error_message = f"请求超时: {error_message}"
                elif "llm" in error_message.lower() or "api" in error_message.lower():
                    error_type = "llm_service"
                    error_message = f"AI服务异常: {error_message}"
                elif "elasticsearch" in error_message.lower():
                    error_type = "search_service"
                    error_message = f"搜索服务异常: {error_message}"
                elif "database" in error_message.lower() or "sql" in error_message.lower():
                    error_type = "database"
                    error_message = f"数据库异常: {error_message}"
                else:
                    error_type = "processing"
                    error_message = f"处理异常: {error_message}"

                await task_manager.update_result(
                    task_id,
                    status=TaskStatus.FAILED,
                    error=error_message,
                    task_params={
                        **task_params,
                        "error_type": error_type,
                        "original_error": str(e)
                    }
                )

        # 创建任务
        await task_manager.create_task(
            task_id,
            process_assembly_task(),
            total_files=1,
            task_type="assembly",
            task_params=task_params
        )

        return BaseResponse(
            code=202,
            msg="Assembly task created",
            data={"task_id": task_id}
        )

    except Exception as e:
        log.error(f"Error creating assembly task: {str(e)}")
        return BaseResponse(
            code=500,
            msg=f"Failed to create assembly task: {str(e)}",
            data=None
        )

# 获取汇编任务结果接口
@app.get("/assembly_task/{task_id}", response_model=BaseResponse)
async def get_assembly_task_result(task_id: str):
    """获取汇编任务结果"""
    try:
        result = await task_manager.get_result(task_id)
        if not result:
            return BaseResponse(
                code=404,
                msg="Assembly task not found",
                data={"task_id": task_id}
            )

        # 如果任务完成，返回汇编结果
        if result["status"] == "completed" and "assembly_result" in result.get("task_params", {}):
            assembly_result = result["task_params"]["assembly_result"]
            return BaseResponse(
                code=200,
                msg="Assembly completed",
                data={
                    "task_id": task_id,
                    "status": result["status"],
                    "progress": result["progress"],
                    "result": assembly_result
                }
            )
        else:
            # 返回任务状态
            return BaseResponse(
                code=200 if result["status"] != "failed" else 500,
                msg="success" if result["status"] != "failed" else result.get("error", "Task failed"),
                data={
                    "task_id": task_id,
                    "status": result["status"],
                    "progress": result["progress"],
                    "error": result.get("error")
                }
            )

    except Exception as e:
        log.error(f"Error getting assembly task result: {str(e)}")
        return BaseResponse(
            code=500,
            msg=f"Server error: {str(e)}",
            data={"task_id": task_id}
        )

# 获取用户当前汇编任务列表接口
@app.get("/assembly_tasks", response_model=BaseResponse)
async def get_user_assembly_tasks(request: Request):
    """获取用户当前的汇编任务列表（仅返回进行中的任务）"""
    try:
        # 获取当前用户ID
        user_id = None
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            try:
                from middleware.jwt_auth import JWTAuthService
                payload = JWTAuthService.decode_token(token)
                if payload:
                    user_id = payload.get("user_id")
                    if isinstance(user_id, str):
                        user_id = int(user_id)
            except Exception as e:
                log.warning(f"解析JWT token失败: {e}")
                return BaseResponse(
                    code=401,
                    msg="无效的认证token",
                    data=[]
                )

        if not user_id:
            return BaseResponse(
                code=401,
                msg="未提供有效的用户认证",
                data=[]
            )

        running_tasks = []
        for task_id, result in task_manager.results.items():
            # 只返回汇编类型且正在运行的任务，并且属于当前用户
            if (result.task_type == "assembly" and
                result.status in [TaskStatus.PENDING, TaskStatus.RUNNING]):

                # 检查任务是否属于当前用户
                task_params = result.task_params or {}
                task_user_id = task_params.get("user_id")

                if task_user_id == user_id:
                    task_data = result.to_dict()
                    running_tasks.append({
                        "task_id": task_id,
                        "status": task_data["status"],
                        "progress": task_data["progress"],
                        "created_at": task_data["created_at"],
                        "task_params": task_data.get("task_params", {})
                    })

        return BaseResponse(
            code=200,
            msg="success",
            data=running_tasks
        )
    except Exception as e:
        log.error(f"Error getting user assembly tasks: {str(e)}")
        return BaseResponse(
            code=500,
            msg=f"Server error: {str(e)}",
            data=[]
        )

# 获取最新任务参数接口
@app.get("/assembly_task/latest_params", response_model=BaseResponse)
async def get_latest_task_params(request: Request):
    """获取用户最新的汇编任务参数"""
    try:
        # 获取当前用户ID
        user_id = None
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            try:
                from middleware.jwt_auth import JWTAuthService
                payload = JWTAuthService.decode_token(token)
                if payload:
                    user_id = payload.get("user_id")
                    if isinstance(user_id, str):
                        user_id = int(user_id)
            except Exception as e:
                log.warning(f"解析JWT token失败: {e}")
                return BaseResponse(
                    code=401,
                    msg="无效的认证token",
                    data=None
                )

        if not user_id:
            return BaseResponse(
                code=401,
                msg="未提供有效的用户认证",
                data=None
            )

        # 查找用户最新的任务参数
        latest_params = None
        latest_time = 0

        for _task_id, result in task_manager.results.items():
            if (result.task_type == "assembly" and
                result.task_params and
                result.task_params.get("user_id") == user_id and
                result.created_at > latest_time):
                latest_time = result.created_at
                latest_params = result.task_params

        if latest_params:
            # 清理内部参数，只返回前端需要的参数
            clean_params = {
                "question": latest_params.get("question", ""),
                "prompt": latest_params.get("prompt", ""),
                "promptName": latest_params.get("promptName", ""),
                "project_type": latest_params.get("project_type", ""),
                "year": latest_params.get("year", ""),
                "keywords": latest_params.get("keywords", [])
            }

            return BaseResponse(
                code=200,
                msg="success",
                data=clean_params
            )
        else:
            return BaseResponse(
                code=404,
                msg="未找到最新的任务参数",
                data=None
            )

    except Exception as e:
        log.error(f"获取最新任务参数失败: {str(e)}")
        return BaseResponse(
            code=500,
            msg=f"获取参数失败: {str(e)}",
            data=None
        )

# 测试数据库查询接口
@app.get("/test_db", response_model=BaseResponse)
async def test_database_query():
    """测试数据库查询"""
    try:
        from database import get_db
        from sqlalchemy import text

        async for db in get_db():
            # 查询汇编对话记录
            sql = text("""
                SELECT id, user_id, session_id, assembly_type, status,
                       created_at, request_data, response_data
                FROM assembly_conversations
                ORDER BY created_at DESC
                LIMIT 5
            """)

            result = await db.execute(sql)
            rows = result.fetchall()

            records = []
            for row in rows:
                records.append({
                    "id": row[0],
                    "user_id": row[1],
                    "session_id": row[2],
                    "assembly_type": row[3],
                    "status": row[4],
                    "created_at": str(row[5]),
                    "request_data": row[6][:100] if row[6] else None,
                    "response_data": row[7][:100] if row[7] else None
                })

            return BaseResponse(
                code=200,
                msg="success",
                data={
                    "count": len(records),
                    "records": records
                }
            )

    except Exception as e:
        log.error(f"测试数据库查询失败: {str(e)}")
        return BaseResponse(
            code=500,
            msg=f"Database error: {str(e)}",
            data=None
        )

@app.post('/explain_query', response_model=ChatMessage)
async def explain_query(
        question: str = Body(..., description="Question", example="mate60"),
        prompt: str = Body(default="名词解释", description="prompt", example="名词解释"),
        promptName: str = Body(default="自定义汇编", description="promptName", example="自定义汇编"),
        project_type: Optional[str] = Body(default="科技项目", description="项目类型", example="科技项目"),
        year: Optional[str] = Body(default="2023", description="时间范围年", example="2023"),
        keywords: Optional[List[str]] = Body(default=[], description="关键词列表", example="['题目报告','实验记录']"),
        history: Optional[List[Dict[str, str]]] = Body(default=None, description="History text")
):
    try:
        llm = LLM(
                    api_url=config.get('llm.api_url'),
                    token=config.get('llm.token'),
                    model=config.get('llm.model'),
                    temperature=config.get('llm.temperature', 0.6),
                    max_tokens=config.get('llm.max_tokens', 16000)
                )
        if prompt == "名词解释":
            prompt_template = """我给出名词，你来给出名词解释，不允许在答案中添加编造成分，答案请使用中文。如果无法给出解释，请说"无相关信息"。我给出的名词是：{query} """
            query = prompt_template.format_map({'query': question})     
            try:
                # 使用LLM进行对话
                response = await llm.chat([
                    {
                        "role": "system",
                        "content": "你是文档AI助手"
                    },
                    {"role": "user", "content": query}
                ])
            
                return ChatMessage(
                    question=question,
                    response=response,
                    history=history or [],
                    source_documents=[],
                )
            except Exception as e:
                log.error(f"Error in chat: {str(e)}")
                raise

        # 获取问题嵌入向量
        question_embedding = await llm.get_embedding(question)
        
        # 获取项目名称和完整分析结果
        project_info, analysis = await get_project_name(llm, question, return_full_analysis=True)
        
        # 如果用户提供了特定的project_type，使用用户提供的
        project_type = project_type if project_type else project_info.get("type", "项目档案")
        
        # 如果用户提供了特定的year，使用用户提供的
        if year and year.isdigit():
            analysis["year"] = int(year)
            
        # 如果用户提供了关键词，合并它们
        if keywords:
            # 确保analysis有keywords字段
            if "keywords" not in analysis or not isinstance(analysis["keywords"], list):
                analysis["keywords"] = []
            # 合并自定义关键词，确保不重复
            analysis["keywords"] = list(set(analysis["keywords"] + keywords))
            
        # 记录查询信息，便于调试
        log.info(f"项目名称: {project_info.get('name', '未识别')}")
        log.info(f"文档类型: {project_type}")
        log.info(f"查询意图: {analysis.get('query_intent', 'general_information')}")
        log.info(f"关键词列表: {analysis.get('keywords', [])}")
       
        if prompt == "自定义汇编" or promptName == "通用":
            # 创建QueryAgent实例来处理查询
            query_agent = QueryAgent(
                api_url=config.get('llm.api_url'),
                token=config.get('llm.token'),
                model=config.get('llm.model'),
                temperature=config.get('llm.temperature', 0.6),
                max_tokens=config.get('llm.max_tokens', 16000),
                debug=True  # 启用调试输出
            )
            
            # 构建消息历史
            messages = []
            if history:
                messages.extend(history)
            messages.append({"role": "user", "content": question})

            # 使用QueryAgent处理查询
            chat_response = await query_agent.chat_with_context(
                messages=messages,
                project_name=project_info.get("name", ""),
                project_type=project_type,
                year=analysis.get("year"),
                keywords=analysis.get("keywords", []),  # 确保使用合并后的关键词
                question_embedding=question_embedding,
                full_analysis=analysis  # 传递完整的分析结果
            )

            return ChatMessage(
                question=question,
                response=chat_response.get("content", ""),
                history=history or [],
                source_documents=chat_response.get("source_documents", []),
            )
        else:
            if promptName == "项目大事记" or promptName == "文案大事记":
                prompt_template = Prompts.get_prompt(promptName)
                sum_agent = Agent()
                response = await sum_agent.handle_note_query(question,promptName, prompt_template)
                return ChatMessage(
                    question=question,
                    response=response,
                    history=[],
                    source_documents=[],
                )
            else:
                # 使用O1Agent进行多步推理
                o1_agent = O1Agent(
                    api_url=config.get('llm.api_url'),
                    token=config.get('llm.token'),
                    model=config.get('llm.model'),
                    temperature=config.get('llm.temperature', 0.6),
                    max_tokens=config.get('llm.max_tokens', 16000)
                )
                prompt_template = Prompts.get_prompt(promptName)
                # 传递合并后的关键词列表
                response = await o1_agent.handle_user_query(
                    question,
                    question_embedding,
                    project_info.get("name", ""),
                    project_type,
                    year,
                    analysis.get("keywords", []),  # 使用合并后的关键词列表
                    prompt_template
                )
                await o1_agent.close()

                return ChatMessage(
                    question=question,
                    response=response,
                    history=[],
                    source_documents=[],
                )


    except Exception as e:
        log.error(f"Error in explain_query: {str(e)}")
        raise

async def save_assembly_to_database(task_id: str, task_params: dict, result: ChatMessage):
    """将汇编结果保存到数据库"""
    try:
        from database import get_db
        from sqlalchemy import text

        # 获取数据库连接
        async for db in get_db():
            # 构造保存数据
            # 判断汇编类型：根据prompt字段判断
            prompt_name = task_params.get("promptName", "")
            prompt = task_params.get("prompt", "")

            # 更准确的类型判断逻辑
            if prompt_name == "自定义汇编" or prompt == "自定义汇编":
                assembly_type = "custom"
            else:
                assembly_type = "template"

            log.info(f"汇编类型判断: promptName='{prompt_name}', prompt='{prompt}', assembly_type='{assembly_type}'")

            # 清理响应数据中的思考内容
            cleaned_response = clean_response(result.response)
            cleaned_echart = clean_response(getattr(result, 'echart', None))
            cleaned_image = clean_response(getattr(result, 'image', None))

            assembly_data = {
                "session_id": f"task_{task_id}",
                "assembly_type": assembly_type,
                "request_data": {
                    "question": task_params.get("question"),
                    "prompt": task_params.get("prompt"),
                    "promptName": task_params.get("promptName"),
                    "project_type": task_params.get("project_type"),
                    "year": task_params.get("year"),
                    "keywords": task_params.get("keywords", [])
                },
                "response_data": {
                    "response": cleaned_response
                },
                "echart_data": cleaned_echart,
                "image_data": cleaned_image,
                "status": "completed",
                "user_id": task_params.get("user_id", 10)  # 从任务参数获取用户ID
            }

            # 使用Python系统时间而不是MySQL的NOW()
            current_time = datetime.now()

            # 插入数据库
            sql = text("""
                INSERT INTO assembly_conversations
                (user_id, session_id, assembly_type, request_data, response_data,
                 echart_data, image_data, status, created_at, updated_at)
                VALUES
                (:user_id, :session_id, :assembly_type, :request_data, :response_data,
                 :echart_data, :image_data, :status, :created_at, :updated_at)
            """)

            await db.execute(sql, {
                "user_id": assembly_data["user_id"],
                "session_id": assembly_data["session_id"],
                "assembly_type": assembly_data["assembly_type"],
                "request_data": json.dumps(assembly_data["request_data"], ensure_ascii=False),
                "response_data": json.dumps(assembly_data["response_data"], ensure_ascii=False),
                "echart_data": json.dumps(assembly_data["echart_data"], ensure_ascii=False) if assembly_data["echart_data"] else None,
                "image_data": assembly_data["image_data"],
                "status": assembly_data["status"],
                "created_at": current_time,
                "updated_at": current_time
            })

            await db.commit()
            break

    except Exception as e:
        log.error(f"保存汇编结果到数据库失败: {str(e)}")
        raise




@app.post("/similar_query", response_model=ChatMessage)
async def similar_query(
        question: str = Body(..., description="Question", example="怎么学习一门语言？"),
        history: Optional[List[Dict[str, str]]] = Body(None, description="History text"),
):
    try:
        # 初始化LLM
        llm = LLM(
            api_url=config.get('llm.api_url'),
            token=config.get('llm.token'),
            model=config.get('llm.model'),
            temperature=config.get('llm.temperature', 0.6),
            max_tokens=config.get('llm.max_tokens', 16384)
        )
        
        # 构造提示词
        prompt_template = """给定内容'{question}'，请你构造8个和这个内容相关的问题列表，使用#分隔每个问题。"""
        prompt = prompt_template.format_map({'question': question})
        
        # 获取响应
        response = await llm.chat([
            {
                "role": "system",
                "content": "你是一个问题生成助手。请生成相关的问题列表。"
            },
            {"role": "user", "content": prompt}
        ])
        
        # 提取问题 - 修改这部分
        pattern = r'\d+\.\s*([^?？]+)[?？]'  # 使用字符类而不是反斜杠
        similar = []
        for sentence in response.split("\n"):
            match = re.search(pattern, sentence)
            if match:
                similar.append(match.group(1))
        
        return ChatMessage(
            question=question,
            response=response,
            history=history or [],
            source_documents=similar,
        )
        
    except Exception as e:
        log.error(f"Error in similar_query: {str(e)}")
        raise

@app.post('/assembly_keywords', response_model=DictKeysResponse)
async def assembly_keywords():
    keys = Prompts.keys
    return DictKeysResponse(
        code=200,
        msg="success",
        data=keys
    )

@app.post("/add_prompt", response_model=BaseResponse)
async def add_prompt(prompt_name: str = Body(...,description="模板名",example="专业回答"),
                    template: str = Body(...,description="板内容",example=""),):
    
    Prompts.add_prompt(prompt_name, template)
    return BaseResponse(code=200, msg=f"add prompt {prompt_name} success")

@app.post("/del_prompt", response_model=BaseResponse)
async def del_prompt(
    prompt_name: str = Body(..., description="知识库名", example="专业回答")
):
    Prompts.del_prompt(prompt_name)
    return BaseResponse(
        code=200,
        msg=f"del prompt {prompt_name} success"
    )

# 首先定一个请求模
class GetPromptRequest(BaseModel):
    prompt_name: str = Field(..., description="模板名", example="通用")
    document: str = Field(..., description="文档内容")
    query: str = Field(..., description="查询内容")


@app.get("/get_prompt", response_model=PromptResponse)
@app.post("/get_prompt", response_model=PromptResponse)
async def get_prompt_by_query(prompt_name: str = Query(..., description="模板名称")):
    """通过查询参数获取指定名称的提示模板"""
    try:
        prompt = Prompts.get_prompt(prompt_name)
        return PromptResponse(
            code=200,
            msg="success",
            data=prompt
        )
    except Exception as e:
        log.error(f"Error in get_prompt: {str(e)}")
        return PromptResponse(
            code=500,
            msg=f"Error: {str(e)}",
            data=None
        )

@app.post("/make_prompt", response_model=PromptResponse)
async def make_prompt(request: GetPromptRequest):
    """根据提供的文档和查询生成提示"""
    try:
        prompt = Prompts.make_prompt(
            request.prompt_name,
            request.document,
            request.query
        )
        return PromptResponse(
            code=200,
            msg="success",
            data=prompt
        )
    except Exception as e:
        log.error(f"Error in make_prompt: {str(e)}")
        return PromptResponse(
            code=500,
            msg=f"Error: {str(e)}",
            data=None
        )

@app.get("/prompt_names", response_model=ListDocsResponse)
@app.post("/prompt_names", response_model=ListDocsResponse)
async def get_prompt_names():
    prompt_names = Prompts.get_prompt_names()
    return ListDocsResponse(
        code=200,
        msg="success",
        data=prompt_names
    )

# 清理项目数据接口
class ClearProjectRequest(BaseModel):
    project_name: str = Field(..., description="要删除的项目名称", example="测试项目")

@app.post("/clear", response_model=BaseResponse)
async def clear_project_data(
    request: ClearProjectRequest
):
    """
    从Elasticsearch的docs索引中删除指定项目名称的所有记录

    Args:
        request: 包含project_name的请求体

    Returns:
        BaseResponse: 删除操作的结果
    """
    try:
        project_name = request.project_name.strip()
        if not project_name:
            return BaseResponse(
                code=400,
                msg="项目名称不能为空",
                data=None
            )

        log.info(f"开始清理项目数据: {project_name}")

        # 获取Elasticsearch配置
        es_config = config.get('elasticsearch', {})
        if not es_config:
            log.error("Elasticsearch配置未找到")
            return BaseResponse(
                code=500,
                msg="Elasticsearch配置错误",
                data=None
            )

        # 初始化Elasticsearch客户端
        from utils.es_client import ESClient

        # 处理 host 配置，提取主机名和端口
        host_config = es_config.get('host', 'localhost')
        if '://' in host_config:
            # 如果包含协议，提取主机名
            host_config = host_config.split('://')[-1]
        if ':' in host_config:
            # 如果包含端口，分离主机名和端口
            host, port = host_config.split(':')
            port = int(port)
        else:
            host = host_config
            port = es_config.get('port', 9200)

        es_client = ESClient(
            host=host,
            port=port,
            username=es_config.get('username', 'elastic'),
            password=es_config.get('password', 'elastic'),
            index_name=es_config.get('index_name', 'docs')
        )

        # 构建删除查询 - 匹配project_name字段
        delete_query = {
            "query": {
                "bool": {
                    "should": [
                        # 精确匹配
                        {"term": {"project_name.keyword": project_name}},
                        # 文本匹配（如果没有keyword字段）
                        {"term": {"project_name": project_name}},
                        # 模糊匹配作为备选
                        {"match": {"project_name": project_name}}
                    ],
                    "minimum_should_match": 1
                }
            }
        }

        # 先查询要删除的文档数量
        search_body = {
            "query": delete_query["query"],
            "size": 0  # 只获取总数，不返回具体文档
        }
        search_result = await es_client.es.search(
            index=es_client.index_name,
            body=search_body
        )

        total_docs = search_result.get("hits", {}).get("total", {}).get("value", 0)
        log.info(f"找到 {total_docs} 个匹配项目 '{project_name}' 的文档")

        if total_docs == 0:
            return BaseResponse(
                code=200,
                msg=f"未找到项目 '{project_name}' 的相关文档",
                data={"deleted_count": 0, "project_name": project_name}
            )

        # 执行删除操作
        delete_result = await es_client.es.delete_by_query(
            index=es_client.index_name,
            body=delete_query,
            refresh=True  # 立即刷新索引
        )

        deleted_count = delete_result.get("deleted", 0)

        log.info(f"成功删除 {deleted_count} 个文档，项目: {project_name}")

        return BaseResponse(
            code=200,
            msg=f"成功删除项目 '{project_name}' 的 {deleted_count} 个文档",
            data={
                "deleted_count": deleted_count,
                "project_name": project_name,
                "total_found": total_docs
            }
        )

    except Exception as e:
        error_msg = f"删除项目数据失败: {str(e)}"
        log.error(error_msg, exc_info=True)
        return BaseResponse(
            code=500,
            msg=error_msg,
            data=None
        )



@app.get("/download")
async def download_file(
    request: Request,
    url: str,
    current_user: User = Depends(get_current_user_from_jwt_or_query)
):
    """安全的文件下载接口 - 使用JWT认证，前端已完成权限验证"""
    try:
        # 解析文件路径
        url_parts = unquote_plus(url).lstrip("/").split("/")
        if len(url_parts) < 2:
            raise HTTPException(status_code=400, detail="Invalid file path")

        bucket_name = url_parts[0]
        object_name = "/".join(url_parts[1:])
        if object_name.lower().endswith('.json'):
            object_name = object_name[:-5] + '.pdf'

        file_path_for_permission = f"{bucket_name}/{object_name}"

        # JWT认证已通过，前端已完成权限检查，直接处理文件下载
        log.info(f"用户 {current_user.user_name} (ID: {current_user.user_id}) 下载文件: {file_path_for_permission}")

        # 3. 获取并返回文件
        try:
            minio_client = get_minio_client()
            stat = minio_client.stat_object(bucket_name, object_name)
            response = minio_client.get_object(bucket_name, object_name)
            
            filename = os.path.basename(object_name)
            encoded_filename = quote(filename)

            # 记录成功下载日志
            await log_user_action(request, "document_download", "document", None, "success", current_user)

            return StreamingResponse(
                file_iterator(response),
                media_type='application/pdf',
                headers={
                    'Content-Disposition': f'attachment; filename="{encoded_filename}"',
                    'Content-Type': 'application/pdf',
                    'Content-Length': str(stat.size)
                }
            )

        except Exception as e:
            if 'response' in locals():
                response.close()
            log.error(f"File error: {str(e)}")
            await log_user_action(request, "document_download", "document", None, "failed", current_user)
            raise HTTPException(status_code=404, detail="File not found")

    except HTTPException:
        raise
    except Exception as e:
        log.error(f"Download error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@functools.lru_cache() # 缓存客户端实例
def get_minio_client():
    """获取MinIO客户端实例"""
    # 从配置中读取MinIO连接信息
    endpoint = config.get("minio.endpoint", "localhost:9000")
    access_key = config.get("minio.access_key", "minioadmin")
    secret_key = config.get("minio.secret_key", "minioadmin")
    secure = config.get("minio.secure", False)
    
    try:
        return Minio(
            endpoint=endpoint,
            access_key=access_key,
            secret_key=secret_key,
            secure=secure
        )
    except Exception as e:
        log.error(f"MinIO客户端初始化失败: {str(e)}")
        raise

def file_iterator(file_obj, chunk_size=8192):
    """文件流迭代器"""
    try:
        while True:
            chunk = file_obj.read(chunk_size)
            if not chunk:
                break
            yield chunk
    except Exception as e:
        log.error(f"文件流读取错误: {str(e)}")
        raise
    finally:
        file_obj.close()

# 模拟 token 验证函数，实际应替换为真实逻辑
# 假设验证成功返回 user_id，失败抛出异常或返回 None
async def verify_token(credentials: HTTPAuthorizationCredentials = Security(HTTPBearer())) -> int:
    # 这里应该是真实的 token 验证逻辑
    # 例如，解码 JWT，查询数据库等
    # 为了演示，我们暂时假设 token 'testtoken' 对应 user_id 1
    if credentials.scheme == "Bearer" and credentials.credentials == "testtoken":
        return 1 # 返回用户 ID
    else:
        raise HTTPException(
            status_code=403,
            detail="Invalid authentication credentials",
        )

# 依赖项，用于获取当前用户ID
async def get_current_user_id(user_id: int = Depends(verify_token)) -> int:
    return user_id

# 文件下载端点 - 使用新的认证系统
@app.get("/download/{url:path}")
async def download_file_new(
    url: str,
    request: Request,
    current_user: User = Depends(get_current_user_from_jwt)
):
    """安全的文件下载端点，支持权限检查和审计日志"""
    try:
        # 解析文件路径
        url_parts = unquote_plus(url).lstrip("/").split("/")
        if len(url_parts) < 2:
            raise HTTPException(status_code=400, detail="Invalid file path")

        bucket_name = url_parts[0]
        object_name = "/".join(url_parts[1:])
        file_path_for_permission = f"{bucket_name}/{object_name}"

        # 权限检查 - 使用新的权限系统
        log.info(f"用户 {current_user.user_name} (ID: {current_user.user_id}) 尝试下载文件: {file_path_for_permission}")

        # 这里可以根据文件路径查找对应的文档记录，然后检查权限
        # 暂时使用旧的权限检查方法作为后备
        has_permission = await check_file_permission(current_user.user_id, file_path_for_permission, "read")
        if not has_permission:
            # 记录权限拒绝日志
            await log_user_action(request, "document_download_denied", "document", None, "failed", current_user)
            log.warning(f"权限拒绝: 用户 {current_user.user_name} 没有读取文件 {file_path_for_permission} 的权限")
            raise HTTPException(status_code=403, detail="Forbidden: You do not have permission to access this file.")

        log.info(f"权限通过: 用户 {current_user.user_name} 允许读取文件 {file_path_for_permission}")

        # 处理 .json 后缀指向 .pdf 的情况
        if object_name.lower().endswith('.json'):
            object_name = object_name[:-5] + '.pdf'

        # 获取并返回文件
        try:
            minio_client = get_minio_client()
            stat = minio_client.stat_object(bucket_name, object_name)
            response = minio_client.get_object(bucket_name, object_name)

            filename = os.path.basename(object_name)
            encoded_filename = quote(filename)

            # 记录成功下载日志
            await log_user_action(request, "document_download", "document", None, "success", current_user)

            return StreamingResponse(
                file_iterator(response),
                media_type='application/pdf',
                headers={
                    'Content-Disposition': f'attachment; filename="{encoded_filename}"',
                    'Content-Type': 'application/pdf',
                    'Content-Length': str(stat.size)
                }
            )

        except Exception as e:
            if 'response' in locals():
                response.close()
            log.error(f"文件下载错误: {str(e)}")
            await log_user_action(request, "document_download", "document", None, "failed", current_user)
            raise HTTPException(status_code=404, detail="File not found")

    except HTTPException:
        raise
    except Exception as e:
        log.error(f"下载文件时发生意外错误: {str(e)}")
        # 在异常处理中，current_user 可能不在作用域中，所以使用 None
        await log_user_action(request, "document_download", "document", None, "error", None)
        raise HTTPException(status_code=500, detail="Internal server error")

# 添加文件上传、下载和查看的API路由 - 使用新的认证系统
@app.post("/api/documents/upload",
    response_model=BaseResponse,
    summary="上传文档",
    description="上传文档到系统，支持多种文件格式",
    responses={
        200: {
            "description": "文档上传成功",
            "content": {
                "application/json": {
                    "example": {
                        "code": 200,
                        "msg": "文档上传成功",
                        "data": {
                            "document_id": 123,
                            "title": "项目需求文档",
                            "file_name": "requirements.pdf",
                            "file_size": 2048576,
                            "file_type": "pdf",
                            "upload_time": "2025-07-01T08:30:00Z",
                            "doc_id": "doc_20250701_083000_abc123"
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 400,
                        "msg": "文件类型不支持",
                        "data": None
                    }
                }
            }
        },
        401: {
            "description": "未授权访问",
            "content": {
                "application/json": {
                    "example": {
                        "code": 401,
                        "msg": "Token无效或已过期",
                        "data": None
                    }
                }
            }
        },
        403: {
            "description": "权限不足",
            "content": {
                "application/json": {
                    "example": {
                        "code": 403,
                        "msg": "无权限上传到指定部门",
                        "data": None
                    }
                }
            }
        }
    }
)
async def upload_document(
    request: Request,
    file: UploadFile = File(..., description="要上传的文件，支持PDF、DOC、DOCX、TXT等格式"),
    title: str = Form(..., description="文档标题，例如：项目需求文档"),
    department_id: int = Form(..., description="所属部门ID，例如：1"),
    description: str = Form(None, description="文档描述（可选），例如：这是一个详细的项目需求文档"),
    current_user: User = Depends(get_current_user_from_jwt)
):
    """
    上传文档API - 使用新的认证和权限系统

    **功能说明：**
    - 支持多种文件格式：PDF、DOC、DOCX、TXT、XLS、XLSX等
    - 自动生成唯一的文档ID (doc_id)
    - 记录上传时间和用户信息
    - 支持部门级权限控制

    **权限要求：**
    - 用户必须已登录（提供有效的JWT Token）
    - 用户必须属于指定部门或具有管理员权限

    **请求示例：**
    ```bash
    curl -X POST "http://localhost:18888/api/documents/upload" \
         -H "Authorization: Bearer your_jwt_token" \
         -F "file=@document.pdf" \
         -F "title=项目需求文档" \
         -F "department_id=1" \
         -F "description=详细的项目需求说明"
    ```
    """
    try:
        # 检查用户是否属于指定部门
        user_in_department = any(dept.id == department_id for dept in current_user.departments)
        is_admin = any(role.name == "admin" for role in current_user.roles)

        if not (user_in_department or is_admin):
            await log_user_action(request, "document_upload_denied", "document", None, "failed", current_user)
            return {"code": 403, "msg": "您没有权限向该部门上传文档", "data": None}

        # 生成文件存储路径
        file_name = file.filename
        file_extension = os.path.splitext(file_name)[1].lower()
        file_unique_id = str(uuid.uuid4())
        relative_path = f"data/uploads/{current_user.user_id}/{file_unique_id}{file_extension}"
        absolute_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), relative_path)

        # 确保目录存在
        os.makedirs(os.path.dirname(absolute_path), exist_ok=True)

        # 保存文件
        file_content = await file.read()
        with open(absolute_path, "wb") as f:
            f.write(file_content)

        # 文件大小
        file_size = len(file_content)

        # 使用新的数据库模型创建文档记录
        from database import SessionLocal
        from auth.service import create_document
        from auth.schemas import DocumentCreate

        async with SessionLocal() as db:
            document_data = DocumentCreate(
                title=title,
                description=description,
                department_id=department_id
            )

            file_info = {
                "file_path": relative_path,
                "file_name": file_name,
                "file_type": file_extension.replace(".", ""),
                "file_size": file_size
            }

            document = await create_document(db, document_data, file_info, current_user.user_id)

        # 记录操作日志
        await log_user_action(request, "document_upload", "document", document.id, "success", current_user)

        return {
            "code": 200,
            "msg": "文档上传成功",
            "data": {
                "document_id": document.id,
                "file_path": relative_path,
                "file_name": file_name,
                "file_size": file_size,
                "title": title,
                "department_id": department_id
            }
        }
    except Exception as e:
        log.error(f"文档上传失败: {str(e)}")
        await log_user_action(request, "document_upload", "document", None, "error", current_user)
        return {"code": 500, "msg": f"文档上传失败: {str(e)}", "data": None}

@app.get("/api/file/list",
    response_model=BaseResponse,
    summary="获取用户文件列表",
    description="获取用户有权限查看的文件列表，支持分页和类型过滤",
    responses={
        200: {
            "description": "获取文件列表成功",
            "content": {
                "application/json": {
                    "example": {
                        "code": 200,
                        "msg": "获取文件列表成功",
                        "data": {
                            "records": [
                                {
                                    "file_id": 123,
                                    "file_name": "项目需求文档.pdf",
                                    "file_path": "/uploads/documents/2025/07/requirements.pdf",
                                    "file_type": "pdf",
                                    "file_size": 2048576,
                                    "upload_time": "2025-07-01T08:30:00Z",
                                    "uploader_id": 1,
                                    "uploader_name": "张三",
                                    "department_id": 1,
                                    "department_name": "技术部"
                                },
                                {
                                    "file_id": 124,
                                    "file_name": "设计方案.docx",
                                    "file_path": "/uploads/documents/2025/07/design.docx",
                                    "file_type": "docx",
                                    "file_size": 1024000,
                                    "upload_time": "2025-07-01T09:15:00Z",
                                    "uploader_id": 2,
                                    "uploader_name": "李四",
                                    "department_id": 1,
                                    "department_name": "技术部"
                                }
                            ],
                            "total": 25,
                            "page": 1,
                            "size": 10
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 400,
                        "msg": "用户ID不能为空",
                        "data": None
                    }
                }
            }
        }
    }
)
async def list_user_files(
    user_id: int = Query(..., description="用户ID，例如：1"),
    file_type: str = Query(None, description="文件类型过滤（可选），例如：pdf, docx, txt"),
    page: int = Query(1, description="页码，从1开始，例如：1", ge=1),
    size: int = Query(10, description="每页记录数，例如：10", ge=1, le=100)
):
    """
    获取用户有权限查看的文件列表

    **功能说明：**
    - 返回用户有查看权限的所有文件
    - 支持按文件类型过滤
    - 支持分页查询
    - 包含文件基本信息和权限信息

    **权限说明：**
    - 返回用户作为所有者的文件
    - 返回用户所在部门的文件（如果是部门管理员）
    - 返回通过申请获得权限的文件

    **请求示例：**
    ```bash
    # 获取第一页的PDF文件
    curl -X GET "http://localhost:18888/api/file/list?user_id=1&file_type=pdf&page=1&size=10" \
         -H "Authorization: Bearer your_jwt_token"

    # 获取所有类型的文件
    curl -X GET "http://localhost:18888/api/file/list?user_id=1&page=1&size=20" \
         -H "Authorization: Bearer your_jwt_token"
    ```

    **支持的文件类型：**
    - pdf: PDF文档
    - docx, doc: Word文档
    - xlsx, xls: Excel表格
    - txt: 文本文件
    - pptx, ppt: PowerPoint演示文稿
    """
    try:
        db_client = get_db_client()
        
        # 构建SQL查询
        sql = """
        SELECT f.* FROM files f
        INNER JOIN file_permissions p ON f.file_path = p.file_path
        WHERE p.user_id = ? AND p.permission_type = 'read' AND f.status = 0
        """
        params = [user_id]
        
        # 添加文件类型过滤
        if file_type:
            sql += " AND f.file_type = ?"
            params.append(file_type)
        
        # 添加分页
        sql += " LIMIT ? OFFSET ?"
        params.append(size)
        params.append((page - 1) * size)
        
        # 执行查询
        results = db_client.query(sql, tuple(params))
        
        # 获取总记录数
        count_sql = """
        SELECT COUNT(*) as total FROM files f
        INNER JOIN file_permissions p ON f.file_path = p.file_path
        WHERE p.user_id = ? AND p.permission_type = 'read' AND f.status = 0
        """
        count_params = [user_id]
        
        if file_type:
            count_sql += " AND f.file_type = ?"
            count_params.append(file_type)
        
        count_result = db_client.query(count_sql, tuple(count_params))
        total = count_result[0]["total"] if count_result else 0
        
        return {
            "code": 200,
            "msg": "获取文件列表成功",
            "data": {
                "records": results,
                "total": total,
                "page": page,
                "size": size
            }
        }
    except Exception as e:
        log.error(f"获取文件列表失败: {str(e)}")
        return {"code": 500, "msg": f"获取文件列表失败: {str(e)}", "data": None}

@app.get("/api/file/download/{file_id}",
    summary="下载文件",
    description="根据文件ID下载文件，需要权限验证",
    responses={
        200: {
            "description": "文件下载成功",
            "content": {
                "application/octet-stream": {
                    "example": "文件二进制内容"
                }
            },
            "headers": {
                "Content-Disposition": {
                    "description": "文件下载头信息",
                    "example": "attachment; filename=document.pdf"
                },
                "Content-Type": {
                    "description": "文件MIME类型",
                    "example": "application/pdf"
                }
            }
        },
        403: {
            "description": "权限不足",
            "content": {
                "application/json": {
                    "example": {
                        "code": 403,
                        "msg": "没有下载权限",
                        "data": None
                    }
                }
            }
        },
        404: {
            "description": "文件不存在",
            "content": {
                "application/json": {
                    "example": {
                        "code": 404,
                        "msg": "文件不存在",
                        "data": None
                    }
                }
            }
        }
    }
)
async def download_file(
    file_id: int = Path(..., description="文件ID，例如：123"),
    user_id: int = Query(..., description="用户ID，用于权限验证，例如：1")
):
    """
    下载文件API

    **功能说明：**
    - 根据文件ID下载文件
    - 自动验证用户下载权限
    - 支持多种文件格式
    - 返回文件流供浏览器下载

    **权限要求：**
    - 用户必须对该文件具有下载权限
    - 权限来源：文档所有者、部门管理员、或通过申请获得的授权

    **请求示例：**
    ```bash
    curl -X GET "http://localhost:18888/api/file/download/123?user_id=1" \
         -H "Authorization: Bearer your_jwt_token" \
         -o downloaded_file.pdf
    ```

    **响应说明：**
    - 成功时返回文件二进制流
    - 失败时返回JSON错误信息
    """
    try:
        db_client = get_db_client()
        
        # 获取文件信息
        file_result = db_client.query("SELECT * FROM files WHERE file_id = ? AND status = 0", (file_id,))
        
        if not file_result:
            return {"code": 404, "msg": "文件不存在", "data": None}
        
        file_info = file_result[0]
        file_path = file_info["file_path"]
        
        # 验证下载权限
        has_permission = await check_file_permission(user_id, file_path, "download")
        
        if not has_permission:
            return {"code": 403, "msg": "没有下载权限", "data": None}
        
        # 构建文件的绝对路径
        absolute_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), file_path)
        
        if not os.path.exists(absolute_path):
            return {"code": 404, "msg": "文件不存在于存储系统中", "data": None}
        
        # 记录下载操作日志
        log_record = {
            "user_id": user_id,
            "operation_type": "download",
            "file_path": file_path,
            "ip_address": None,  # 这里可以从请求中获取
            "status": 0,
            "remark": ""
        }
        db_client.insert("operation_logs", log_record)
        
        # 返回文件进行下载
        return StreamingResponse(
            open(absolute_path, "rb"),
            media_type="application/octet-stream",
            headers={"Content-Disposition": f"attachment; filename={quote(file_info['file_name'])}"}
        )
    except Exception as e:
        log.error(f"文件下载失败: {str(e)}")
        return {"code": 500, "msg": f"文件下载失败: {str(e)}", "data": None}

@app.get("/api/file/derivatives/{file_id}")
async def get_file_derivatives(
    file_id: int,
    user_id: int = Query(..., description="用户ID")
):
    """
    获取文件的衍生文件列表
    - file_id: 原始文件ID
    - user_id: 用户ID，用于权限验证
    """
    try:
        db_client = get_db_client()
        
        # 获取原始文件信息
        file_result = db_client.query("SELECT * FROM files WHERE file_id = ?", (file_id,))
        
        if not file_result:
            return {"code": 404, "msg": "原始文件不存在", "data": None}
        
        original_file_path = file_result[0]["file_path"]
        
        # 验证读取权限
        has_permission = await check_file_permission(user_id, original_file_path, "read")
        
        if not has_permission:
            return {"code": 403, "msg": "没有权限访问该文件", "data": None}
        
        # 获取衍生文件列表
        derivatives_sql = """
        SELECT fd.*, f.file_name, f.file_type, f.file_size
        FROM file_derivatives fd
        INNER JOIN files f ON fd.derived_file_path = f.file_path
        WHERE fd.original_file_path = ?
        AND f.status = 0
        """
        
        derivatives = db_client.query(derivatives_sql, (original_file_path,))
        
        # 过滤掉用户没有权限的衍生文件
        authorized_derivatives = []
        for derivative in derivatives:
            has_read_perm = await check_file_permission(user_id, derivative["derived_file_path"], "read")
            if has_read_perm:
                authorized_derivatives.append(derivative)
        
        return {
            "code": 200,
            "msg": "获取衍生文件列表成功",
            "data": authorized_derivatives
        }
    except Exception as e:
        log.error(f"获取衍生文件列表失败: {str(e)}")
        return {"code": 500, "msg": f"获取衍生文件列表失败: {str(e)}", "data": None}

# ==================== 数据同步接口 ====================
# 注意：数据同步接口已迁移到 sync/ 模块中







async def get_project_name(llm: LLM, text: str, return_full_analysis: bool = False) -> tuple:
    """
    使用LLM分析问题，提取项目名称、文档类型及相关信息。
    改进的实现包含多层回退机制，提高可靠性。
    
    Args:
        llm: LLM实例
        text: 需要分析的文本
        return_full_analysis: 此参数保留但不再影响返回值，总是返回完整结果
        
    Returns:
        tuple: 包含(project_info, analysis)的元组，其中:
            - project_info: 包含项目基本信息的字典，有name和type字段
            - analysis: 完整的分析结果字典，包含项目名称、类型、意图等详细信息
    """
    log.info(f"开始分析文本: '{text[:50]}...'")
    
    # 初始化返回结果
    analysis = {
        "project_name": "",
        "doc_type": "项目档案",
        "year": None,
        "is_amount_query": False,
        "is_personnel_query": False,
        "query_intent": "general_information",
        "keywords": []
    }
    
    # 预处理文本 - 清理多余空格和标点
    text = text.strip()
    
    # 检查是否为空文本
    if not text:
        log.warning("收到空文本，返回默认值")
        return {"name": "", "type": "项目档案"}, analysis
    
    # 方法1: LLM提取 - 主要提取方法
    try:
        # 增强版分析提示
        analysis_prompt = f"""
        作为专业的自然语言处理专家，请仔细分析以下问题并精确提取核心信息，特别是项目名称和文档类型。
        
        问题: "{text}"
        
        【提取规则】
        1. 项目名称提取:
           - 如果文本中包含"关于"、"通知"、"通报"、"报告"等词且位于句首，说明这是一个文书标题，直接返回完整的文书标题，类型为"文书档案"
           - 优先查找包含"系统"、"平台"、"工程"、"研究"等词的最长短语
           - 如果短语后面跟着"的"，则将"的"之前的部分作为项目名称
           - 忽略问句中的疑问词（如"是什么"、"如何"等）
           - 请不要包含"这个项目"、"投资额"等非项目名称的部分
        
        2. 文档类型判断:
           - 如果项目名称以"关于"、"通知"、"通报"开头，通常为"文书档案"
           - 其他情况下，项目名称通常属于"项目档案"类型
           - 如果问题中明确提到文档类型（如"可行性研究报告"、"项目立项文件"等），则使用该类型
        
        3. 特别注意查询意图识别:
           - 需要精确识别用户的意图类别，可能的类别包括:
             a) 项目编号 - 如询问编号、项目代码等
             b) 开始时间 - 如询问项目开始日期、启动时间等
             c) 结束时间 - 如询问项目结束日期、完成时间等
             d) 总投资 - 如询问资金、预算、投入、费用等
             e) 承担单位 - 如询问负责机构、承担组织等
             f) 负责人 - 如询问项目领导、主要人员等
             g) 研究内容 - 如询问技术方案、研究方向、研究计划等
             h) 创新点 - 如询问技术创新、突破点等
             i) 交付成果 - 如询问最终产出、项目成果等
             j) 专利 - 如询问知识产权、专利申请情况等
             k) 会议信息 - 如询问会议时间、组织者、参与者等
           - 请准确将查询意图归类到上述类别之一，如果无法确定则使用"general_information"
        
        请提取以下全部信息：
        1. 项目名称: 精确识别问题中提到的项目名称(如"电网故障综合分析平台的研究与应用")
        2. 文档类型: 识别问题是否涉及特定类型的文档，如"文书档案"或"项目档案"
        3. 时间信息: 提取相关年份或时间段
        4. 金额相关: 问题是否询问金额、投资、预算等信息
        5. 人员相关: 问题是否询问项目负责人、团队成员等人员信息
        6. 查询意图: 确定问题的主要意图，请使用具体的意图类别，如"total_investment", "leader", "research_points"等
        7. 关键词: 提取问题中的关键词
        
        以JSON格式返回，确保项目名称和文档类型的提取非常精确：
        {{
          "project_name": "提取的项目名称，如果无法识别则返回空字符串",
          "doc_type": "提取的文档类型，如果没有则空字符串",
          "year": "提取的年份，如果没有则null",
          "is_amount_query": true/false,
          "is_personnel_query": true/false,
          "query_intent": "明确的意图类别，如total_investment、leader、research_points等",
          "keywords": ["关键词1", "关键词2"]
        }}
        """
        
        # 调用LLM获取分析结果，设置超时机制，确保高可用
        try:
            llm_result = await asyncio.wait_for(
                llm.chat([
                    {
                        "role": "system", 
                        "content": "你是一个精确的JSON输出助手。你的任务是严格按照规则提取信息并只返回JSON格式数据。对于项目名称，要准确提取完整的项目名称，不要遗漏或截断。"
                    },
                    {
                        "role": "user", 
                        "content": analysis_prompt
                    }
                ]), 
                timeout=120.0
            )
        except asyncio.TimeoutError:
            log.warning("LLM分析超时，将使用备用方法")
            raise ValueError("LLM分析超时")
            
        # 清理和规范化输出
        llm_result = llm_result.strip()
        llm_result = llm_result.replace("<|im_start|>", "").replace("<|im_end|>", "").strip()
        
        # 尝试解析JSON结果
        try:
            llm_analysis = load_json(llm_result)
            
            # 规范化keywords字段，确保它是字符串列表
            if "keywords" in llm_analysis:
                if not isinstance(llm_analysis["keywords"], list):
                    llm_analysis["keywords"] = [str(llm_analysis["keywords"])]
                else:
                    # 递归展平并转换
                    flat_keywords = []
                    def flatten(items):
                        for item in items:
                            if isinstance(item, list):
                                flatten(item)
                            elif item is not None:
                                flat_keywords.append(str(item))
                    
                    flatten(llm_analysis["keywords"])
                    llm_analysis["keywords"] = flat_keywords
                    
            # 清理项目名称
            if llm_analysis.get("project_name"):
                # 移除非项目名称的部分
                for phrase in ["这个项目", "项目的投资额", "的投资额", "的预算", "的经费"]:
                    if phrase in llm_analysis["project_name"]:
                        llm_analysis["project_name"] = llm_analysis["project_name"].replace(phrase, "").strip()
                
                # 处理末尾的"项目"
                if llm_analysis["project_name"].endswith("项目"):
                    llm_analysis["project_name"] = llm_analysis["project_name"][:-2].strip()
                    
                # 验证项目名称是否有效（至少2个字符）
                if len(llm_analysis["project_name"]) >= 2:
                    # LLM提取成功，更新分析结果
                    analysis.update(llm_analysis)
                    log.info(f"LLM成功提取项目名称: '{analysis['project_name']}'")
                else:
                    # 项目名称太短，尝试备用方法
                    raise ValueError("LLM提取的项目名称太短")
            else:
                # 项目名称为空，尝试备用方法
                raise ValueError("LLM未能提取项目名称")
                
        except Exception as e:
            log.warning(f"LLM分析结果解析失败: {str(e)}")
            # 继续到备用方法
            raise ValueError(f"LLM结果解析失败: {str(e)}")
            
    except Exception as llm_error:
        log.error(f"LLM提取失败: {str(llm_error)}")
        
        # 方法2: 使用正则表达式提取特定模式
        try:
            log.info("尝试使用正则表达式提取")
            
            # 识别文书档案标题
            if text.startswith(("关于", "通知", "通报", "报告")):
                # 可能是文书标题，获取到第一个标点符号或问号之前的部分
                match = re.search(r'^[^？?!！。.，,;；]+', text)
                if match:
                    project_name = match.group(0).strip()
                    if len(project_name) >= 4:  # 确保标题长度合理
                        analysis["project_name"] = project_name
                        analysis["doc_type"] = "文书档案"
                        log.info(f"正则表达式提取文书标题: '{project_name}'")
            
            # 项目名称后跟"的"的模式
            if not analysis["project_name"]:
                match = re.search(r'(.+?)的', text)
                if match:
                    potential_name = match.group(1).strip()
                    # 检查是否包含项目指示词
                    indicators = ["系统", "平台", "工程", "项目", "研究", "应用", "开发"]
                    if any(indicator in potential_name for indicator in indicators) and len(potential_name) >= 4:
                        analysis["project_name"] = potential_name
                        analysis["doc_type"] = "项目档案"
                        log.info(f"正则表达式提取到项目名称: '{potential_name}'")
            
            # 如果仍未提取到项目名称，尝试其他正则模式
            # ...可以根据需要添加更多的正则模式
            
        except Exception as regex_error:
            log.error(f"正则表达式提取失败: {str(regex_error)}")
        
        # 方法3: 使用jieba提取名词短语（如果前两种方法都失败）
        if not analysis["project_name"]:
            try:
                log.info("尝试使用jieba分词提取项目名称")
                
                # 确保jieba库已正确加载
                try:
                    if 'jieba' not in globals():
                        import jieba
                    if 'pseg' not in globals():
                        import jieba.posseg as pseg
                        
                    # 添加常见项目名称模式到自定义词典
                    jieba.add_word("研究与应用", freq=3000, tag='n')
                    jieba.add_word("综合系统", freq=3000, tag='n')
                    jieba.add_word("综合平台", freq=3000, tag='n')
                    jieba.add_word("管理系统", freq=3000, tag='n')
                    jieba.add_word("信息平台", freq=3000, tag='n')
                    jieba.add_word("大数据平台", freq=3000, tag='n')
                    jieba.add_word("数据中心", freq=3000, tag='n')
                    jieba.add_word("工程建设", freq=3000, tag='n')
                    
                    # 项目命名常见后缀
                    jieba.add_word("的研究", freq=2000, tag='n')
                    jieba.add_word("的应用", freq=2000, tag='n')
                    jieba.add_word("的建设", freq=2000, tag='n')
                    jieba.add_word("的开发", freq=2000, tag='n')
                    jieba.add_word("的实施", freq=2000, tag='n')
                    
                    # 文案常见开头
                    jieba.add_word("关于加强", freq=3000, tag='n')
                    jieba.add_word("关于实施", freq=3000, tag='n')
                    jieba.add_word("关于开展", freq=3000, tag='n')
                    jieba.add_word("关于印发", freq=3000, tag='n')
                    jieba.add_word("关于推进", freq=3000, tag='n')
                except Exception as jieba_init_error:
                    log.error(f"初始化jieba词典时出错: {str(jieba_init_error)}")
                
                # 步骤1: 首先尝试使用最长切分模式提取完整的项目名称
                full_mode_candidates = []
                # 使用全模式切分，获取所有可能的短语
                words_full = jieba.cut(text, cut_all=True)
                words_full_list = list(words_full)
                
                # 寻找包含关键词的最长短语
                project_indicators = ["系统", "平台", "工程", "项目", "研究", "应用", "开发", "通知", "报告"]
                for i in range(len(words_full_list)):
                    for j in range(i+1, len(words_full_list)+1):
                        phrase = ''.join(words_full_list[i:j])
                        if len(phrase) >= 4 and any(indicator in phrase for indicator in project_indicators):
                            full_mode_candidates.append(phrase)
                
                # 按长度排序，优先选择最长的短语
                if full_mode_candidates:
                    longest_candidate = max(full_mode_candidates, key=len)
                    if len(longest_candidate) >= 4:
                        log.info(f"jieba最长切分模式提取到项目名称: '{longest_candidate}'")
                        analysis["project_name"] = longest_candidate
                        analysis["doc_type"] = "项目档案" if not any(longest_candidate.startswith(word) for word in ["关于", "通知", "通报", "报告"]) else "文书档案"
                        return project_info, analysis
                
                # 步骤2: 如果最长切分模式未找到合适结果，使用精确模式结合词性标注
                # 使用jieba词性标注
                words_with_flags = list(pseg.cut(text))
                
                # 查找连续的名词短语
                noun_phrases = []
                current_phrase = []
                
                for word, flag in words_with_flags:
                    # 名词(n)、机构名(nt)、专有名词(nz)、地名(ns)
                    if flag.startswith('n') or flag == 'eng':
                        current_phrase.append(word)
                    elif current_phrase:
                        if len(''.join(current_phrase)) >= 2:  # 短语至少2个字符
                            noun_phrases.append(''.join(current_phrase))
                        current_phrase = []
                
                # 处理最后一个短语
                if current_phrase and len(''.join(current_phrase)) >= 2:
                    noun_phrases.append(''.join(current_phrase))
                
                # 优先选择包含特定关键词的短语
                project_indicators = ["系统", "平台", "工程", "项目", "研究", "应用", "开发"]
                best_phrase = None
                max_length = 0
                
                for phrase in noun_phrases:
                    if any(indicator in phrase for indicator in project_indicators) and len(phrase) > max_length:
                        best_phrase = phrase
                        max_length = len(phrase)
                
                # 如果找到合适的项目名称短语，使用它
                if best_phrase:
                    analysis["project_name"] = best_phrase
                    analysis["doc_type"] = "项目档案" if not any(best_phrase.startswith(word) for word in ["关于", "通知", "通报", "报告"]) else "文书档案"
                    log.info(f"jieba词性标注模式提取到项目名称: '{best_phrase}'")
                else:
                    # 如果没有找到包含特定指示词的短语，使用最长的名词短语
                    if noun_phrases:
                        longest_phrase = max(noun_phrases, key=len)
                        if len(longest_phrase) >= 2:
                            analysis["project_name"] = longest_phrase
                            log.info(f"jieba提取到最长名词短语: '{longest_phrase}'")
                
                # 步骤3: 对于文书类型的文本，特殊处理
                if not analysis["project_name"] and any(text.startswith(word) for word in ["关于", "通知", "通报", "报告"]):
                    # 尝试提取完整的文书标题（不限于名词短语）
                    # 提取连续的非标点符号段落
                    non_punct_seq = re.findall(r'[^，。；,.;!?？！\s]+', text)
                    if non_punct_seq:
                        # 取第一段作为可能的文书标题
                        title_candidate = non_punct_seq[0]
                        if len(title_candidate) >= 5:  # 确保长度合理
                            analysis["project_name"] = title_candidate
                            analysis["doc_type"] = "文书档案"
                            log.info(f"从文书格式中提取到标题: '{title_candidate}'")
            
            except Exception as jieba_error:
                log.error(f"jieba分词提取失败: {str(jieba_error)}")
    
    # 方法4: 使用启发式规则进行提取（作为最后的回退）
    if not analysis["project_name"]:
        try:
            log.info("使用启发式规则进行最终提取")
            
            # 简单启发式：使用第一个"的"之前的内容作为项目名称
            if "的" in text:
                potential_name = text.split("的")[0].strip()
                if len(potential_name) >= 2:
                    analysis["project_name"] = potential_name
                    log.info(f"启发式规则提取项目名称: '{potential_name}'")
            # 未找到"的"，使用整个查询（如果较短）
            elif len(text) <= 20:
                analysis["project_name"] = text
                log.info(f"使用完整查询作为项目名称: '{text}'")
            # 使用前10个字作为近似项目名称
            else:
                analysis["project_name"] = text[:10].strip()
                log.info(f"使用查询前10个字作为项目名称: '{text[:10]}'")
                
            # 根据项目名称确定文档类型
            if analysis["project_name"] and any(analysis["project_name"].startswith(word) for word in ["关于", "通知", "通报", "报告"]):
                analysis["doc_type"] = "文书档案"
            
        except Exception as heuristic_error:
            log.error(f"启发式规则提取失败: {str(heuristic_error)}")
    
    # 提取关键词（如果尚未提取）
    if not analysis.get("keywords"):
        try:
            # 使用jieba提取更有意义的关键词
            try:
                words_with_flags = pseg.cut(text)
                extracted_keywords = []
                
                # 提取名词、动词等作为关键词
                important_flags = {'n', 'nz', 'v', 'vn', 'a', 'an', 'eng'}  # 名词、动名词、动词、形容词等
                for word, flag in words_with_flags:
                    if (flag[0] in important_flags or flag in important_flags) and len(word) > 1:
                        extracted_keywords.append(word)
                
                if extracted_keywords:
                    analysis["keywords"] = extracted_keywords
                else:
                    analysis["keywords"] = [w for w in text.split() if w and len(w) > 1]
            except Exception as kw_error:
                log.error(f"jieba提取关键词失败: {str(kw_error)}")
                analysis["keywords"] = [w for w in text.split() if w and len(w) > 1]
        except Exception as keyword_error:
            log.error(f"提取关键词失败: {str(keyword_error)}")
            # 确保keywords字段至少是空列表
            analysis["keywords"] = []
    
    # 确保keywords字段是扁平化的字符串列表
    try:
        flat_keywords = []
        for item in analysis["keywords"]:
            if item is not None:
                flat_keywords.append(str(item))
        analysis["keywords"] = flat_keywords
    except Exception:
        analysis["keywords"] = []
    
    # 检测查询意图（如果尚未确定）
    if not analysis.get("query_intent") or analysis.get("query_intent") == "general_information":
        # 基础启发式意图检测
        if "投资" in text or "金额" in text or "费用" in text or "预算" in text:
            analysis["query_intent"] = "total_investment"
            analysis["is_amount_query"] = True
        elif "负责人" in text or "人员" in text or "团队" in text or "谁" in text:
            analysis["query_intent"] = "leader"
            analysis["is_personnel_query"] = True
        elif "开始" in text or "启动" in text:
            analysis["query_intent"] = "start_date"
        elif "结束" in text or "完成" in text:
            analysis["query_intent"] = "end_date"
        elif "单位" in text or "机构" in text or "公司" in text:
            analysis["query_intent"] = "responsible_unit"
        elif "成果" in text or "产出" in text or "输出" in text:
            analysis["query_intent"] = "main_deliverables"
        elif "创新" in text or "突破" in text:
            analysis["query_intent"] = "innovation"
        elif "研究" in text or "方案" in text or "计划" in text:
            analysis["query_intent"] = "research_points"
        elif "专利" in text or "知识产权" in text:
            analysis["query_intent"] = "patent"
    
    # 从配置文件添加额外关键词
    try:
        entity_type = "project_extract" if analysis.get("doc_type") != "文书档案" else "conference_extract"
        query_intent = analysis.get("query_intent", "")
        
        yaml_config = config.get(entity_type, {})
        if query_intent in yaml_config:
            intent_config = yaml_config[query_intent]
            if "keywords" in intent_config and isinstance(intent_config["keywords"], list):
                config_keywords = [str(k) for k in intent_config["keywords"] if k is not None]
                existing_keywords = [str(k) for k in analysis["keywords"] if k is not None]
                
                # 合并关键词，避免重复
                merged_keywords = []
                for k in existing_keywords + config_keywords:
                    if k not in merged_keywords:
                        merged_keywords.append(k)
                
                analysis["keywords"] = merged_keywords
                log.info(f"从配置添加了{len(config_keywords)}个关键词")
    except Exception as config_error:
        log.error(f"从配置获取关键词时出错: {str(config_error)}")
    
    # 构造返回的项目信息
    project_info = {
        "name": analysis.get("project_name", ""),
        "type": analysis.get("doc_type", "项目档案")
    }
    
    # 记录最终结果
    if project_info["name"]:
        log.info(f"最终提取项目名称: '{project_info['name']}', 类型: '{project_info['type']}'")
    else:
        log.warning("未能提取到有效的项目名称")
    
    # 返回结果
    return project_info, analysis



# 添加直接运行支持












# ===== 数据转换和验证核心函数 =====

class DataTransformationError(Exception):
    """数据转换异常"""
    pass

class DataValidationError(Exception):
    """数据验证异常"""
    pass

def safe_convert_to_int(value: Any, field_name: str, allow_none: bool = True) -> Optional[int]:
    """安全转换为整数"""
    if value is None:
        if allow_none:
            return None
        raise DataValidationError(f"字段 {field_name} 不能为空")

    if isinstance(value, int):
        return value

    if isinstance(value, str):
        if value.strip() == "":
            return None if allow_none else 0
        try:
            return int(value.strip())
        except ValueError:
            raise DataValidationError(f"字段 {field_name} 的值 '{value}' 无法转换为整数")

    if isinstance(value, float):
        return int(value)

    raise DataValidationError(f"字段 {field_name} 的值 '{value}' 类型不支持转换为整数")

def safe_convert_to_str(value: Any, field_name: str, max_length: Optional[int] = None, default: str = "") -> str:
    """安全转换为字符串"""
    if value is None:
        return default

    if isinstance(value, str):
        result = value.strip()
    else:
        result = str(value).strip()

    if max_length and len(result) > max_length:
        log.warning(f"字段 {field_name} 的值长度 {len(result)} 超过最大长度 {max_length}，将被截断")
        result = result[:max_length]

    return result

def safe_convert_to_datetime(value: Any, field_name: str, allow_none: bool = True) -> Optional[datetime]:
    """安全转换为日期时间，使用系统本地时间"""
    if value is None:
        return None if allow_none else datetime.now()

    if isinstance(value, datetime):
        return value

    if isinstance(value, str):
        if value.strip() == "":
            return None if allow_none else datetime.now()

        # 尝试多种日期格式
        formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d %H:%M:%S.%f",
            "%Y-%m-%d",
            "%Y/%m/%d %H:%M:%S",
            "%Y/%m/%d"
        ]

        for fmt in formats:
            try:
                return datetime.strptime(value.strip(), fmt)
            except ValueError:
                continue

        raise DataValidationError(f"字段 {field_name} 的值 '{value}' 无法转换为日期时间")

    raise DataValidationError(f"字段 {field_name} 的值 '{value}' 类型不支持转换为日期时间")

def safe_convert_to_bool(value: Any, _field_name: str, default: bool = True) -> bool:
    """安全转换为布尔值"""
    if value is None:
        return default

    if isinstance(value, bool):
        return value

    if isinstance(value, str):
        value = value.strip().lower()
        if value in ("true", "1", "yes", "on", "0"):  # "0" 在若依系统中表示正常/激活
            return True
        elif value in ("false", "0", "no", "off", "1"):  # "1" 在若依系统中表示停用/非激活
            return False
        else:
            return default

    if isinstance(value, int):
        return value == 0  # 若依系统：0=正常，1=停用

    return default

# @app.post("/api/hngpt/workflow/execute")
# async def execute_hngpt_workflow(
#     request: AssemblyDocumentRequest,
#     wait: bool = Query(False, description="是否等待任务完成"),
#     timeout: float = Query(300.0, description="等待超时时间(秒)"),
#     current_user: User = Depends(get_current_user_from_jwt),
#     db: AsyncSession = Depends(get_db)
# ):
#     """执行HNGPT标准化Agent工作流"""
#     try:
#         task_id = f"hngpt_workflow_{uuid.uuid4().hex[:8]}"

#         # 创建工作流
#         if request.action == "项目档案":
#             dag = HNGPTWorkflowFactory.create_document_processing_workflow(f"project_workflow_{task_id}")
#         else:
#             dag = HNGPTWorkflowFactory.create_document_processing_workflow(f"document_workflow_{task_id}")

#         # 创建上下文
#         context = WorkflowContext(
#             task_id=task_id,
#             workflow_id=dag.workflow_id
#         )

#         # 设置输入数据
#         context.set_data("task_id", task_id)
#         context.set_data("project_name", request.project_name)
#         context.set_data("action", request.action)
#         context.set_data("file_urls", request.urls)
#         context.set_data("user_name", current_user.user_name)

#         # 异步执行工作流
#         async def execute_workflow():
#             try:
#                 result = await hngpt_task_integration.create_workflow_task(
#                     task_id=task_id,
#                     dag=dag,
#                     context=context
#                 )
#                 return result
#             except Exception as e:
#                 log.error(f"HNGPT工作流执行失败: {e}", exc_info=True)
#                 raise e

#         if wait:
#             # 同步执行
#             result = await execute_workflow()
#             return {
#                 "task_id": task_id,
#                 "success": result.success,
#                 "status": result.status.value,
#                 "execution_time": result.execution_time,
#                 "message": "工作流执行完成" if result.success else result.error_message
#             }
#         else:
#             # 异步执行
#             await task_manager.create_task(task_id, execute_workflow())
#             return {
#                 "task_id": task_id,
#                 "message": "HNGPT工作流已启动，请通过SSE监控执行进度",
#                 "workflow_id": dag.workflow_id
#             }

#     except Exception as e:
#         log.error(f"启动HNGPT工作流失败: {e}", exc_info=True)
#         raise HTTPException(status_code=500, detail=f"启动工作流失败: {str(e)}")


# @app.post("/api/hngpt/workflow/execute_v2")
# async def execute_hngpt_workflow_v2(
#     request: AssemblyDocumentRequest,
#     wait: bool = Query(False, description="是否等待任务完成"),
#     timeout: float = Query(300.0, description="等待超时时间(秒)"),
#     current_user: User = Depends(get_current_user_from_jwt),
#     db: AsyncSession = Depends(get_db)
# ):
#     """
#     执行HNGPT工作流 V2 - 基于assembly_doc实现的Agent工作流

#     支持项目档案和文书档案两种类型的处理，使用与assembly_doc相同的处理逻辑
#     """
#     try:
#         log.info(f"用户 {current_user.user_name} 请求HNGPT工作流V2: {request.action} - {request.project_name}")

#         # 生成任务ID
#         task_id = f"hngpt_{uuid.uuid4().hex[:8]}"
#         log.info(f"Starting HNGPT V2 task {task_id} for user {current_user.user_name}")

#         # 创建异步任务处理函数
#         async def process_hngpt_task():
#             try:
#                 # 使用新的HNGPT工作流（基于assembly_doc实现）
#                 from agents.hngpt_workflow import HNGPTWorkflowFactory
#                 from paper_qa import PaperQA

#                 # 初始化PaperQA实例
#                 qa = PaperQA(
#                     api_url=config.get('llm.api_url'),
#                     token=config.get('llm.token'),
#                     cache_dir="cache",
#                     minio_config=config.get('minio')
#                 )

#                 # 创建HNGPT工作流
#                 hngpt_workflow = HNGPTWorkflowFactory.create_hngpt_workflow(
#                     task_manager, qa
#                 )

#                 # 执行HNGPT工作流
#                 result = await hngpt_workflow.process_hngpt_request(
#                     task_id=task_id,
#                     action=request.action,
#                     project_name=request.project_name,
#                     urls=request.urls,
#                     user_name=current_user.user_name
#                 )

#                 return result

#             except Exception as e:
#                 log.error(f"HNGPT V2任务处理异常: {str(e)}")
#                 await task_manager.update_result(
#                     task_id,
#                     status=TaskStatus.FAILED,
#                     message=f"HNGPT V2任务处理异常: {str(e)}"
#                 )
#                 return {
#                     "success": False,
#                     "error": str(e),
#                     "task_id": task_id
#                 }

#         if wait:
#             # 同步执行
#             result = await process_hngpt_task()
#             return {
#                 "task_id": task_id,
#                 "success": result.get("success", False),
#                 "status": "completed" if result.get("success") else "failed",
#                 "message": f"HNGPT V2处理完成: {result.get('processed_files', 0)}/{result.get('total_files', 0)} 文件成功" if result.get("success") else f"HNGPT V2处理失败: {result.get('error', 'Unknown error')}",
#                 "data": result
#             }
#         else:
#             # 异步执行
#             await task_manager.create_task(task_id, process_hngpt_task())
#             return {
#                 "task_id": task_id,
#                 "status": "running",
#                 "message": "HNGPT V2工作流已启动",
#                 "workflow_id": task_id
#             }

#     except Exception as e:
#         log.error(f"HNGPT V2工作流启动失败: {e}", exc_info=True)
#         return {
#             "task_id": task_id if 'task_id' in locals() else None,
#             "success": False,
#             "error": str(e),
#             "message": "HNGPT V2工作流启动失败"
#         }


@app.post("/test-date-processing")
async def test_date_processing(db: AsyncSession = Depends(get_db)):
    """测试日期处理功能的端点"""
    try:
        # 测试数据，包含"未提及"的日期值
        test_data = {
            'project_no': 'TEST001',
            'start_date': '未提及',
            'end_date': '未提及',
            'total_investment': '未提及',
            'responsible_unit': '测试单位',
            'leader': '测试负责人',
            'research_points': '未提及',
            'innovation': '未提及',
            'main_deliverables': '未提及',
            'patent': '未提及'
        }

        # 调用 upsert_extract_data 函数
        result = await upsert_extract_data(
            db=db,
            action="项目档案",
            project_name="日期处理测试项目",
            extracted_data=test_data
        )

        return {
            "status": "success",
            "message": "日期处理测试完成",
            "result": result,
            "test_data": test_data
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"日期处理测试失败: {str(e)}",
            "error_type": type(e).__name__
        }

if __name__ == "__main__":
    import uvicorn

    # 从配置文件读取服务器配置，如果没有则使用默认值
    host = config.get("server.host", "0.0.0.0")
    port = config.get("server.port", 18888)
    reload = config.get("server.reload", False)

    if reload:
        uvicorn.run(
            "app:app",  # 应用模块:应用实例
            host=host,
            port=port,
            reload=True,
            reload_excludes=["front_end/dist/*", "front_end/node_modules/*", "*.log"],
            log_level="info"
        )
    else:
        uvicorn.run(
            "app:app",  # 应用模块:应用实例
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )

