#!/bin/bash

# 为前端目录创建Git版本的脚本
# 使用方法: bash create_git_version.sh

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查git是否安装
check_git() {
    if ! command -v git &> /dev/null; then
        log_error "Git未安装，请先安装Git"
        echo "Ubuntu/Debian: sudo apt install git"
        echo "CentOS/RHEL: sudo yum install git"
        exit 1
    fi
    log_success "Git已安装"
}

# 创建.gitignore文件
create_gitignore() {
    log_info "创建.gitignore文件..."
    
    cat > .gitignore << 'EOF'
# 依赖文件
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# 构建输出
dist/
build/
*.egg-info/

# 日志文件
*.log
logs/
log/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件（包含敏感信息）
config_production.yaml
.env
.env.local
.env.production

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
tmp/
temp/
*.tmp

# 缓存文件
cache/
.cache/

# 上传文件
data/uploads/
data/logs/

# 备份文件
*.bak
*.backup

# 压缩文件
*.tar.gz
*.zip
*.rar

# 前端特定
front_end/node_modules/
front_end/dist/
front_end/.nuxt/
front_end/.next/
front_end/coverage/

# Python特定
*.egg
*.egg-info/
.tox/
.coverage
.pytest_cache/

# Docker
.dockerignore

# 其他
*.orig
.merge_file_*
EOF

    log_success ".gitignore文件创建完成"
}

# 方案1：为整个项目创建Git仓库
create_full_project_git() {
    log_info "=== 方案1：为整个项目创建Git仓库 ==="
    
    # 初始化Git仓库
    git init
    log_success "Git仓库初始化完成"
    
    # 创建.gitignore
    create_gitignore
    
    # 添加所有文件
    git add .
    log_info "添加所有文件到暂存区"
    
    # 创建初始提交
    git commit -m "Initial commit: Complete HNGPT project with frontend improvements

Features:
- Frontend with Vue.js and Element UI
- Backend with FastAPI and MySQL
- Document permission system
- JWT authentication
- PDF viewer with return button
- Responsive design
- Agent workflow system
- Assembly task management

Frontend improvements:
- Removed permission check popup messages
- Added PDF viewer return button
- Optimized responsive layout
- Enhanced user experience"
    
    log_success "初始提交创建完成"
    
    # 创建开发分支
    git checkout -b develop
    log_success "创建并切换到develop分支"
    
    # 回到主分支
    git checkout main 2>/dev/null || git checkout master
    
    echo ""
    log_success "🎉 整个项目的Git仓库创建完成！"
    echo ""
    echo "📋 仓库信息："
    echo "   - 主分支: $(git branch --show-current)"
    echo "   - 总提交数: $(git rev-list --count HEAD)"
    echo "   - 仓库大小: $(du -sh .git | cut -f1)"
    echo ""
    echo "🔧 常用Git命令："
    echo "   查看状态: git status"
    echo "   查看历史: git log --oneline"
    echo "   创建分支: git checkout -b feature/new-feature"
    echo "   提交更改: git add . && git commit -m 'Your message'"
    echo ""
}

# 方案2：只为前端目录创建Git仓库
create_frontend_only_git() {
    log_info "=== 方案2：只为前端目录创建Git仓库 ==="
    
    cd front_end
    
    # 初始化Git仓库
    git init
    log_success "前端Git仓库初始化完成"
    
    # 创建前端专用.gitignore
    cat > .gitignore << 'EOF'
# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db

# 日志
*.log
logs/

# 缓存
.cache/
.nuxt/
.next/

# 测试覆盖率
coverage/

# 临时文件
*.tmp
tmp/

# 备份文件
*.bak
EOF
    
    # 添加所有前端文件
    git add .
    log_info "添加所有前端文件到暂存区"
    
    # 创建初始提交
    git commit -m "Initial commit: HNGPT Frontend

Features:
- Vue.js 2 with Element UI
- Responsive design
- JWT authentication
- Document search and preview
- Permission management system
- PDF viewer with return button

Recent improvements:
- Removed permission check popup messages
- Added PDF viewer return button functionality
- Optimized responsive layout for mobile devices
- Enhanced user experience with cleaner interface
- Fixed PDF.js integration issues"
    
    log_success "前端初始提交创建完成"
    
    cd ..
    
    echo ""
    log_success "🎉 前端Git仓库创建完成！"
    echo ""
    echo "📋 前端仓库信息："
    echo "   - 位置: front_end/"
    echo "   - 分支: $(cd front_end && git branch --show-current)"
    echo "   - 提交数: $(cd front_end && git rev-list --count HEAD)"
    echo ""
}

# 方案3：创建前端版本标签
create_frontend_version_tag() {
    log_info "=== 方案3：为前端创建版本标签 ==="
    
    # 检查是否已有Git仓库
    if [ ! -d ".git" ]; then
        log_warning "当前目录没有Git仓库，先创建整个项目的Git仓库"
        create_full_project_git
    fi
    
    # 确保所有更改都已提交
    if ! git diff-index --quiet HEAD --; then
        log_info "检测到未提交的更改，先提交这些更改"
        git add .
        git commit -m "Frontend improvements: Remove permission popups and add PDF return button

- Removed permission check popup messages
- Added return button to PDF viewer
- Optimized responsive design
- Enhanced user experience"
    fi
    
    # 创建版本标签
    VERSION_TAG="frontend-v1.0.0-$(date +%Y%m%d)"
    git tag -a "$VERSION_TAG" -m "Frontend Version 1.0.0

Features:
- Complete Vue.js frontend with Element UI
- Document search and preview system
- JWT authentication integration
- Permission management interface
- Responsive design for all devices
- PDF viewer with return functionality

Improvements in this version:
- Removed intrusive permission check popups
- Added PDF viewer return button
- Enhanced mobile responsiveness
- Cleaner user interface
- Better error handling"
    
    log_success "版本标签 $VERSION_TAG 创建完成"
    
    echo ""
    log_success "🎉 前端版本标签创建完成！"
    echo ""
    echo "📋 版本信息："
    echo "   - 标签名: $VERSION_TAG"
    echo "   - 创建时间: $(date)"
    echo "   - 基于提交: $(git rev-parse --short HEAD)"
    echo ""
    echo "🔧 版本管理命令："
    echo "   查看所有标签: git tag"
    echo "   查看标签详情: git show $VERSION_TAG"
    echo "   切换到此版本: git checkout $VERSION_TAG"
    echo "   删除标签: git tag -d $VERSION_TAG"
    echo ""
}

# 主菜单
show_menu() {
    echo ""
    echo "🚀 HNGPT 前端Git版本创建工具"
    echo "=================================="
    echo ""
    echo "请选择操作方案："
    echo ""
    echo "1) 为整个项目创建Git仓库 (推荐)"
    echo "   - 包含前端、后端、配置等所有文件"
    echo "   - 统一版本管理"
    echo "   - 便于团队协作"
    echo ""
    echo "2) 只为前端目录创建Git仓库"
    echo "   - 只管理前端代码"
    echo "   - 独立的前端仓库"
    echo "   - 适合前端独立开发"
    echo ""
    echo "3) 为前端创建版本标签"
    echo "   - 在现有仓库中创建前端版本标签"
    echo "   - 标记重要的前端版本"
    echo "   - 便于版本回滚"
    echo ""
    echo "4) 退出"
    echo ""
    read -p "请输入选项 (1-4): " choice
    
    case $choice in
        1)
            create_full_project_git
            ;;
        2)
            create_frontend_only_git
            ;;
        3)
            create_frontend_version_tag
            ;;
        4)
            log_info "退出程序"
            exit 0
            ;;
        *)
            log_error "无效选项，请重新选择"
            show_menu
            ;;
    esac
}

# 主程序
main() {
    log_info "检查Git环境..."
    check_git
    
    # 确保在正确的目录
    if [ ! -d "front_end" ]; then
        log_error "当前目录不是HNGPT项目根目录（找不到front_end目录）"
        log_info "请切换到HNGPT项目根目录后再运行此脚本"
        exit 1
    fi
    
    show_menu
}

# 运行主程序
main "$@"
