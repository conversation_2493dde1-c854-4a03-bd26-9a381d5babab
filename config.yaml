# 生产环境配置

# 系统时区配置
system:
  timezone: "Asia/Shanghai"  # 中国标准时间
  time_zone_offset: "+08:00"

elasticsearch:
  host: "***********"
  port: 9200
  username: "elastic"
  password: "elastic"
  timeout: 30
  max_retries: 3
  verify_certs: false

# 文档索引器配置
document_indexer:
  chunk_size: 512
  chunk_overlap: 50
  min_chunk_size: 100
  batch_size: 50
  index_prefix: "docs"

# ES汇编处理配置
es_assembly:
  batch_size: 10
  max_concurrent: 5
  index_prefix: "project_docs"

# 第二阶段智能抽取配置
phase2:
  parallel_processing: true
  max_concurrent: 3
  result_caching: true

# 字段抽取器配置
field_extractor:
  max_chunks_per_field: 10
  confidence_threshold: 0.7

# 结果验证器配置
validator:
  min_confidence: 0.5
  cross_validation: true

# 第三阶段优化配置
phase3:
  advanced_quality: true
  performance_monitoring: true
  auto_optimization: false
  result_caching: true
  cache_ttl_hours: 24

# 高级质量评估器配置
quality_assessor:
  semantic_quality: true
  historical_comparison: true
  thresholds:
    excellent: 0.95
    good: 0.85
    acceptable: 0.75
    poor: 0.60

# 性能监控器配置
performance_monitor:
  real_time: true
  interval: 30
  retention_hours: 24

minio:
  endpoint: "***********:9000"
  access_key: "minio"
  secret_key: "minio@2023"
  secure: false
  bucket_name: "docs"

cb:
  endpoint: "175.178.225.26:19889"
  route: /ocr/ocrResult/remoteResultHandler"

sqlite:
  db_path: "data/db/knowledge.db"

llm:
  api_url: "http://***********:8888"
  token: "startfrom2023"  # 请配置实际的LLM API token
  model: "hngpt-mini"
  embeding: "hngpt-embedding"
  temperature: 0.6
  max_tokens: 16384
  # 调用控制参数
  max_retries: 3              # 最大重试次数
  base_delay: 2               # 基础延迟时间（秒）
  max_delay: 30               # 最大延迟时间（秒）
  min_request_interval: 0.5   # 最小请求间隔（秒）
  timeout: 300                # 请求超时时间（秒）
  # 并发控制
  max_concurrent_requests: 2  # 最大并发请求数

# 图像处理配置
image_processing:
  enable_ocr: false           # 是否启用OCR处理
  enable_seal: false          # 是否启用印章检测
  enable_handwrite: false     # 是否启用手写检测
  skip_on_error: true         # 图像处理出错时是否跳过而不是失败

version: 0.0.1
upload_dir: data/uploads
chunk_size: 256
chunk_overlap: 0

project_extract:
  project_no:
    keywords: [项目编号, 项 目 编号, 项目 编号]
    query: "项目的编号"
    hint: "提取项目编号"
  start_date:
    keywords: [开始时间, 项目起止时间, 项目起止年限, 项目开始时间,项目起止日期,项目执行期限,起止时间]
    query: "项目的开始时间"
    hint: "总结项目的实际开始时间"
  
  end_date:
    keywords: [结束时间, 项目起止时间, 项目起止年限, 项目结束时间,项目起止日期,项目执行期限,起止时间]
    query: "项目的结束时间"
    hint: "总结项目的实际结束时间"
  
  total_investment:
    keywords: [项目总投资,总计, 项目总经费,项目 总经费,经费总额,经费 总额,经 费总额,申请经费总额]
    query: "项目总投资"
    hint: "总结项目的实际总投资金额"
  
  responsible_unit:
    keywords: [承建单位, 申请单位,协助单位]
    query: "项目的承建单位"
    hint: "总结项目的实际承担单位"
  
  leader:
    keywords: [项目负责人,项目 负责人, 项目责任人]
    query: "项目负责人"
    hint: "总结项目的实际负责人，提取的人名"
  
  research_points:
    keywords: [项目研究内容,项目主要研究内容, 主要技术内容, 主要技术难点,项目研究的实施方案, 研究内容,研究计划,项目研究的关键技术与难点]
    query: "项目的研究内容"
    hint: "总结项目的主要研究内容和技术要点"
  
  innovation:
    keywords: [预期目标和创新点,创新点, 创新特色, 创新突破, 创新内容, 创新思路, 创新之处, 创新要点, 创新方向, 创新成果, 创新价值, 项目目标及创新点]
    query: "项目的创新点"
    hint: "总结项目的主要创新点和技术突破"
  
  main_deliverables:
    keywords: [主要交付成果, 最终成果形式, 项目成果,成果,项目最终形成的标准,项目形成的论文,项目形成的专利, 项目分工与计划进度安排, 任务名称,主要内容及交付项]
    query: "项目的主要交付成果"
    hint: "总结项目的主要成果产出"
  
  patent:
    keywords: [项目形成的专利, 发明专利,实用新型专利, 发明创造名称, 发明名称, 发明专利证书, 专利申请受理通知书,论文成果文件,专利成果文件,成果名称, 实用新型, 发明,核心期刊, 期刊,受理,授权, 已发表,录用,以下论文,以下专利]
    query: "项目的专利和论文信息"
    hint: "总结项目的专利申请和论文发表情况"

conference_extract:
  name:
    keywords: [会议名称, 通知, 纪要, 通报, 复函, 请示, 议案, 免职的通知, 任职资格认定的通知, 备忘录, 嘉奖的请示]
    query: "会议名称或文档标题"
    hint: "总结文档的完整标题"
  
  date:
    keywords: [会议时间, 开会时间, 会议日期, 活动时间, 研讨时间, 交流时间, 讨论时间, 座谈时间, 发布时间, 说明时间, 时间, 年月日]
    query: "会议开始时间或文书发布时间"
    hint: "总结文档的实际时间"
  
  type:
    keywords: [决策会议, 印发文件, 人事任免, 表彰获奖, 问责处分, 函件回复, 通知通报]
    query: "会议或文书类型"
    hint: "总结文档的具体类型"
  
  organizer:
    keywords: [会议或者发文中的组织]
    query: "发起组织"
    hint: "总结文档的发起组织"
  
  participants:
    keywords: [参会部门和参会人员, 发文的接收组织和个人]
    query: "参会部门和人员或接收组织和个人"
    hint: "总结文档的参与者或接收者"
  
  summary:
    keywords: [关于举办, 关于应发, 工作简报, 情况通报, 整改目标, 免职, 任职, 建设意见方案的通知, 成果, 方案, 报告, 请示, 签报, 批复, 复函, 决定, 会议纪要, 感谢信, 备忘录, 通知]
    query: "文档的核心内容"
    hint: "总结文档的主要内容要点"

download:
  # 不再使用硬编码token，改为动态JWT认证
  # access_token: "deprecated_hardcoded_token"
  use_jwt_auth: true

# 认证配置 - 统一JWT密钥配置
auth:
  secret_key: "shared_jwt_secret_2024_service_a_b_integration_very_secure"  # JWT签名密钥(与service_auth.jwt统一)
  algorithm: "HS256"                 # JWT算法
  access_token_expire_minutes: 30    # Token有效期(分钟)

# 服务间认证配置
service_auth:
  # 统一JWT配置
  jwt:
    secret_key: "shared_jwt_secret_2024_service_a_b_integration_very_secure"  # 统一JWT密钥
    algorithm: "HS256"                            # JWT算法
    expire_minutes: 30                            # Token有效期(分钟)
    issuer: "hngpt"                              # Token签发方

  # 服务A认证配置
  service_a:
    identifier: "RuoyiSystem"                     # 服务A标识
    secret_key: "service_a_secret_key_2024_very_secure_random_string"  # 服务A密钥
    allowed_ips: ["***********", "127.0.0.1"]    # 允许的IP地址

  # 服务B认证配置
  service_b:
    identifier: "FastAPIService"                  # 服务B标识
    secret_key: "service_b_secret_key_2024_very_secure_random_string"  # 服务B密钥

  # 安全配置
  signature_expire_seconds: 300                   # 签名有效期(5分钟)
  max_clock_skew_seconds: 60                      # 时钟偏差容忍(1分钟)
  nonce_cache_ttl_seconds: 3600                   # 防重放缓存时间(1小时)

  # JWT配置(服务A与服务B共享)
  jwt:
    secret_key: "shared_jwt_secret_2024_service_a_b_integration_very_secure"
    algorithm: "HS256"
    expire_minutes: 30                            # Token有效期
    issuer: "hngpt"                               # 签发方 (统一使用hngpt)
    # audience: "ServiceB"                        # 暂时不使用接收方字段

# 服务A配置
service_a:
  base_url: "http://localhost:8000"  # 服务A的基础URL
  api_prefix: "/api/v1"              # API前缀

mysql:
  host: "***********"            # 数据库主机(监听所有接口)
  port: 3306                # 数据库端口
  database: "hngpt"        # 默认数据库名
  username: "root"      # 应用数据库用户
  password: "startfrom2023"      # 应用数据库密码
  data_dir: "/workspace/mysql/data"  # 数据存储目录

  # 高级配置
  max_connections: 100       # 最大连接数
  character_set: "utf8mb4"   # 字符集
  collation: "utf8mb4_unicode_ci"  # 排序规则


# 服务A数据库配置（用于用户同步）
service_a_mysql:
  host: "***********"            # 服务A数据库主机
  port: 3306                     # 数据库端口
  database: "hn_ruoyi-vue"       # 服务A数据库名
  username: "root"               # 数据库用户
  password: "startfrom2023"      # 数据库密码

  # 同步配置
  sync_enabled: true             # 是否启用同步
  sync_interval_minutes: 5       # 同步间隔(分钟)
  sync_batch_size: 100           # 每批同步数量
  sync_table: "sys_user"         # 源用户表名

  # 连接池配置
  max_connections: 10            # 最大连接数
  character_set: "utf8mb4"       # 字符集
  collation: "utf8mb4_0900_ai_ci" # 排序规则

# Redis配置
redis:
  port: 6379                # Redis端口
  # password: ""            # Redis密码(可选)
  data_dir: "/workspace/redis/data"  # 数据存储目录
