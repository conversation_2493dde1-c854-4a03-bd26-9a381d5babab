#!/usr/bin/env python3
"""
统一发布脚本 - 制作前端和后端更新包
生成最终的 update.tar.gz 包含:
- front_end.tar.gz: 前端构建包，部署到nginx目录
- back_end.tar.gz: 后端更新包，可直接解压到根目录
"""

import os
import sys
import tarfile
import shutil
import subprocess
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime
import json

def setup_logging(log_dir):
    """设置日志配置"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, "build_release.log")
    
    formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
    
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=1,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

def run_command(cmd, cwd=None, check=True):
    """执行命令并返回结果"""
    logging.info(f"执行命令: {cmd}")
    if cwd:
        logging.info(f"工作目录: {cwd}")
    
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            check=check
        )
        if result.stdout:
            logging.info(f"输出: {result.stdout.strip()}")
        return result
    except subprocess.CalledProcessError as e:
        logging.error(f"命令执行失败: {e}")
        if e.stdout:
            logging.error(f"标准输出: {e.stdout}")
        if e.stderr:
            logging.error(f"错误输出: {e.stderr}")
        raise

def build_frontend():
    """构建前端项目"""
    logging.info("🔨 开始构建前端项目...")
    
    frontend_dir = "front_end"
    if not os.path.exists(frontend_dir):
        raise FileNotFoundError(f"前端目录不存在: {frontend_dir}")
    
    # 检查package.json
    package_json = os.path.join(frontend_dir, "package.json")
    if not os.path.exists(package_json):
        raise FileNotFoundError(f"package.json不存在: {package_json}")
    
    # 清理旧的构建文件
    dist_dir = os.path.join(frontend_dir, "dist")
    if os.path.exists(dist_dir):
        logging.info("清理旧的构建文件...")
        shutil.rmtree(dist_dir)
    
    # 安装依赖（如果需要）
    node_modules = os.path.join(frontend_dir, "node_modules")
    if not os.path.exists(node_modules):
        logging.info("安装前端依赖...")
        run_command("npm install", cwd=frontend_dir)
    
    # 构建前端
    logging.info("构建前端项目...")
    run_command("npm run build", cwd=frontend_dir)
    
    # 验证构建结果
    if not os.path.exists(dist_dir):
        raise FileNotFoundError("前端构建失败，dist目录不存在")
    
    # 检查关键文件
    index_html = os.path.join(dist_dir, "index.html")
    if not os.path.exists(index_html):
        raise FileNotFoundError("前端构建失败，index.html不存在")
    
    logging.info("✅ 前端构建完成")
    return dist_dir

def create_frontend_package(dist_dir):
    """创建前端压缩包"""
    logging.info("📦 创建前端压缩包...")
    
    frontend_package = "front_end.tar.gz"
    
    # 删除旧的前端包
    if os.path.exists(frontend_package):
        os.remove(frontend_package)
        logging.info(f"已删除旧的 {frontend_package}")
    
    # 创建前端压缩包
    # 包结构: usr/share/nginx/html/search/
    with tarfile.open(frontend_package, "w:gz") as tar:
        # 遍历dist目录中的所有文件
        for root, dirs, files in os.walk(dist_dir):
            for file in files:
                file_path = os.path.join(root, file)
                # 计算相对路径
                rel_path = os.path.relpath(file_path, dist_dir)
                # 在tar包中的路径
                tar_path = os.path.join("usr/share/nginx/html/search", rel_path)
                logging.info(f"添加前端文件: {tar_path}")
                tar.add(file_path, tar_path)
    
    # 验证包大小
    size = os.path.getsize(frontend_package)
    logging.info(f"✅ 前端包创建成功: {frontend_package} (大小: {size/1024/1024:.2f} MB)")
    
    return frontend_package

def create_backend_package():
    """创建后端压缩包"""
    logging.info("📦 创建后端压缩包...")
    
    backend_package = "back_end.tar.gz"
    
    # 后端需要打包的路径列表
    backend_paths = [
        # 核心应用文件
        "app.py",
        "paper_qa.py", 
        "knowledge_control.py",
        "database.py",
        # 核心模块
        "agent",
        "utils", 
        "loaders",
        "templates",
        
        # 认证和权限系统
        "auth",
        "middleware",
        "routers", 
        "services",
        "sync",
        
        # 搜索功能
        "search",
        
        # 静态文件
        "static",
        
        # 配置文件
        "config_production.yaml",
        "requirements.txt",
        "requirements-install.txt",
        "nginx.conf",
        # 数据库脚本
        "hngpt_schema.sql",
        
        # 数据目录结构（空目录）
        "data/db",
        "data/logs", 
        "data/uploads",
        
        # 日志目录
        "log"
    ]
    
    # 检查是否在正确的目录
    if os.getcwd() != "/workspace/hngpt":
        logging.error("请在 /workspace/hngpt 目录下运行此脚本")
        return None
    
    missing_paths = []
    files_to_pack = set()
    
    # 处理文件和目录
    for path in backend_paths:
        if not os.path.exists(path):
            missing_paths.append(path)
            continue
            
        if os.path.isdir(path):
            # 添加目录下的所有文件
            base_dir = path
            if os.path.exists(base_dir):
                for root, _, filenames in os.walk(base_dir):
                    for filename in filenames:
                        full_path = os.path.join(root, filename)
                        # 添加 workspace/hngpt 前缀
                        rel_path = os.path.join("workspace/hngpt", full_path)
                        files_to_pack.add(rel_path)
            logging.info(f"添加后端目录: {path}")
        else:
            # 项目文件，添加 workspace/hngpt 前缀
            rel_path = os.path.join("workspace/hngpt", path)
            files_to_pack.add(rel_path)
            logging.info(f"添加后端文件: {path}")
    
    if missing_paths:
        logging.warning(f"以下路径不存在，将跳过: {', '.join(missing_paths)}")
    
    # 删除旧的后端包
    if os.path.exists(backend_package):
        os.remove(backend_package)
        logging.info(f"已删除旧的 {backend_package}")
    
    # 创建后端压缩包
    with tarfile.open(backend_package, "w:gz") as tar:
        for file_path in sorted(files_to_pack):
            # 源文件路径
            src_path = file_path.replace('workspace/hngpt/', '')
            if os.path.exists(src_path):
                logging.info(f"添加后端文件: {file_path}")
                tar.add(src_path, file_path)
    
    # 验证包大小
    size = os.path.getsize(backend_package)
    logging.info(f"✅ 后端包创建成功: {backend_package} (大小: {size/1024/1024:.2f} MB)")
    
    return backend_package

def create_final_package(frontend_package, backend_package):
    """创建最终的更新包"""
    logging.info("📦 创建最终更新包...")

    final_package = "update.tar.gz"

    # 删除旧的最终包
    if os.path.exists(final_package):
        os.remove(final_package)
        logging.info(f"已删除旧的 {final_package}")

    # 创建最终压缩包
    with tarfile.open(final_package, "w:gz") as tar:
        # 添加前端包
        if os.path.exists(frontend_package):
            logging.info(f"添加前端包: {frontend_package}")
            tar.add(frontend_package, frontend_package)

        # 添加后端包
        if os.path.exists(backend_package):
            logging.info(f"添加后端包: {backend_package}")
            tar.add(backend_package, backend_package)

        # 添加部署脚本
        deploy_script = create_deploy_script()
        if deploy_script:
            logging.info(f"添加部署脚本: {deploy_script}")
            tar.add(deploy_script, deploy_script)

        # 添加版本信息
        version_file = create_version_info()
        if version_file:
            logging.info(f"添加版本信息: {version_file}")
            tar.add(version_file, version_file)

    # 验证包大小
    size = os.path.getsize(final_package)
    logging.info(f"✅ 最终更新包创建成功: {final_package} (大小: {size/1024/1024:.2f} MB)")

    return final_package

def create_deploy_script():
    """创建部署脚本"""
    logging.info("📝 创建部署脚本...")

    deploy_script = "deploy.sh"

    script_content = '''#!/bin/bash
# 自动部署脚本
# 使用方法: sudo bash deploy.sh

set -e

echo "🚀 开始部署更新包..."

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用sudo运行此脚本"
    exit 1
fi

# 检查文件
if [ ! -f "front_end.tar.gz" ]; then
    echo "❌ 前端包不存在: front_end.tar.gz"
    exit 1
fi

if [ ! -f "back_end.tar.gz" ]; then
    echo "❌ 后端包不存在: back_end.tar.gz"
    exit 1
fi

echo "📦 部署前端..."
# 备份现有前端
if [ -d "/usr/share/nginx/html/search" ]; then
    echo "备份现有前端到 /usr/share/nginx/html/search.backup.$(date +%Y%m%d_%H%M%S)"
    mv /usr/share/nginx/html/search /usr/share/nginx/html/search.backup.$(date +%Y%m%d_%H%M%S)
fi

# 解压前端包
echo "解压前端包..."
tar -xzf front_end.tar.gz -C /

# 设置前端文件权限
echo "设置前端文件权限..."
chown -R nginx:nginx /usr/share/nginx/html/search
chmod -R 755 /usr/share/nginx/html/search

echo "📦 部署后端..."
# 停止现有容器
echo "停止现有容器..."
docker stop hngpt-app 2>/dev/null || true
docker rm hngpt-app 2>/dev/null || true

# 备份现有后端
if [ -d "/workspace/hngpt.backup" ]; then
    rm -rf /workspace/hngpt.backup.old
    mv /workspace/hngpt.backup /workspace/hngpt.backup.old
fi
if [ -d "/workspace/hngpt" ]; then
    echo "备份现有后端到 /workspace/hngpt.backup"
    cp -r /workspace/hngpt /workspace/hngpt.backup
fi

# 解压后端包
echo "解压后端包..."
tar -xzf back_end.tar.gz -C /

# 更新配置文件
echo "更新配置文件..."
cd /workspace/hngpt
if [ -f "config_production.yaml" ]; then
    cp config_production.yaml config.yaml
    echo "已更新配置文件"
fi

# 重启nginx
echo "重启nginx..."
systemctl reload nginx

# 启动后端容器
echo "启动后端容器..."
docker run -d --name hngpt-app --network host \\
    -v /workspace/hngpt:/workspace/hngpt \\
    -v /workspace/mysql/data:/workspace/mysql/data \\
    -v /workspace/redis/data:/workspace/redis/data \\
    hngpt-bi:latest

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查服务状态..."
docker logs --tail 20 hngpt-app

echo "✅ 部署完成!"
echo ""
echo "📋 服务地址:"
echo "  前端: http://$(hostname -I | awk '{print $1}'):8080"
echo "  后端: http://$(hostname -I | awk '{print $1}'):18888"
echo "  API文档: http://$(hostname -I | awk '{print $1}'):18888/docs"
echo ""
echo "🔍 检查服务状态:"
echo "  docker logs hngpt-app"
echo "  systemctl status nginx"
'''

    with open(deploy_script, 'w', encoding='utf-8') as f:
        f.write(script_content)

    # 设置执行权限
    os.chmod(deploy_script, 0o755)

    logging.info(f"✅ 部署脚本创建成功: {deploy_script}")
    return deploy_script

def create_version_info():
    """创建版本信息文件"""
    logging.info("📝 创建版本信息...")

    version_file = "version.json"

    version_info = {
        "build_time": datetime.now().isoformat(),
        "build_host": os.uname().nodename,
        "build_user": os.getenv('USER', 'unknown'),
        "git_commit": get_git_commit(),
        "components": {
            "frontend": {
                "framework": "Vue 2.7.7",
                "build_tool": "Vite 3.0.2"
            },
            "backend": {
                "framework": "FastAPI",
                "python_version": sys.version
            }
        }
    }

    with open(version_file, 'w', encoding='utf-8') as f:
        json.dump(version_info, f, indent=2, ensure_ascii=False)

    logging.info(f"✅ 版本信息创建成功: {version_file}")
    return version_file

def get_git_commit():
    """获取Git提交信息"""
    try:
        result = run_command("git rev-parse HEAD", check=False)
        if result.returncode == 0:
            return result.stdout.strip()
    except:
        pass
    return "unknown"

def cleanup_temp_files(*files):
    """清理临时文件"""
    logging.info("🧹 清理临时文件...")
    for file in files:
        if file and os.path.exists(file):
            os.remove(file)
            logging.info(f"删除临时文件: {file}")

def main():
    """主函数"""
    setup_logging("log")
    logging.info("🚀 开始构建发布包...")
    logging.info(f"构建时间: {datetime.now()}")
    logging.info(f"工作目录: {os.getcwd()}")

    frontend_package = None
    backend_package = None
    final_package = None

    try:
        # 1. 构建前端
        dist_dir = build_frontend()

        # 2. 创建前端包
        frontend_package = create_frontend_package(dist_dir)

        # 3. 创建后端包
        backend_package = create_backend_package()

        if not backend_package:
            raise Exception("后端包创建失败")

        # 4. 创建最终包
        final_package = create_final_package(frontend_package, backend_package)

        # 5. 显示部署说明
        show_deployment_instructions(final_package)

        logging.info("🎉 发布包构建完成!")

    except Exception as e:
        logging.error(f"❌ 构建失败: {str(e)}")
        sys.exit(1)

    finally:
        # 清理临时文件（保留最终包）
        temp_files = [f for f in [frontend_package, backend_package, "deploy.sh", "version.json"] if f != final_package]
        cleanup_temp_files(*temp_files)

def show_deployment_instructions(final_package):
    """显示部署说明"""
    logging.info("\n" + "="*60)
    logging.info("📋 部署说明")
    logging.info("="*60)
    logging.info(f"✅ 更新包已创建: {final_package}")
    logging.info(f"📦 包大小: {os.path.getsize(final_package)/1024/1024:.2f} MB")
    logging.info("")
    logging.info("🚀 部署步骤:")
    logging.info("1. 将更新包复制到生产服务器:")
    logging.info(f"   scp {final_package} root@10.96.123.208:/tmp/")
    logging.info("")
    logging.info("2. 在生产服务器上解压并部署:")
    logging.info("   cd /tmp")
    logging.info(f"   tar -xzf {final_package}")
    logging.info("   sudo bash deploy.sh")
    logging.info("")
    logging.info("📁 包内容:")
    logging.info("   - front_end.tar.gz: 前端构建文件")
    logging.info("   - back_end.tar.gz: 后端应用文件")
    logging.info("   - deploy.sh: 自动部署脚本")
    logging.info("   - version.json: 版本信息")
    logging.info("")
    logging.info("🔧 nginx配置:")
    logging.info("   前端部署路径: /usr/share/nginx/html/search")
    logging.info("   nginx配置文件: /etc/nginx/nginx.conf")
    logging.info("   服务端口: 8080")
    logging.info("="*60)

if __name__ == "__main__":
    main()
