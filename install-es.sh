#!/bin/bash

# 设置日志颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

log_warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

log_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# 检查容器是否存在
check_container() {
    docker ps -a --filter name=^/$1$ --format '{{.Status}}' | grep -q .
    return $?
}

# 检查容器是否运行
check_running() {
    docker ps --filter name=^/$1$ --format '{{.Status}}' | grep -q .
    return $?
}

# 安全停止并删除容器
remove_container() {
    if check_container $1; then
        log_info "Stopping container $1..."
        docker stop $1 > /dev/null 2>&1
        log_info "Removing container $1..."
        docker rm $1 > /dev/null 2>&1
    fi
}

# 创建必要的目录
create_directories() {
    log_info "Creating directories..."
    # Elasticsearch 目录
    mkdir -p /workspace/elasticsearch/data
    mkdir -p /workspace/elasticsearch/logs
    # MinIO 目录
    mkdir -p /workspace/minio/data
    mkdir -p /workspace/minio/config
    # 应用日志目录
    mkdir -p /workspace/logs/supervisor
    
    # 设置权限
    chmod -R 777 /workspace/elasticsearch
    chmod -R 777 /workspace/minio
    chmod -R 777 /workspace/logs
}

# 启动 Elasticsearch
start_elasticsearch() {
    local container_name="elasticsearch"
    local image_name="es-ik"
    
    if check_running $container_name; then
        log_info "Elasticsearch is already running"
        return 0
    fi
    
    remove_container $container_name
    
    log_info "Starting Elasticsearch..."
    docker run -d \
        --restart=always \
        --name $container_name \
        -p 9200:9200 \
        -p 9300:9300 \
        --ulimit memlock=-1:-1 \
        --ulimit nofile=65535:65535 \
        -v /etc/localtime:/etc/localtime \
        -v /workspace/elasticsearch/data:/usr/share/elasticsearch/data \
        -v /workspace/elasticsearch/logs:/var/log/elasticsearch \
        -e ES_JAVA_OPTS="-Xms4g -Xmx4g" \
        -e ELASTIC_PASSWORD=elastic \
        -e xpack.security.enabled=true \
        -e discovery.type=single-node \
        -e network.host=0.0.0.0 \
        $image_name
    
    if [ $? -ne 0 ]; then
        log_error "Failed to start Elasticsearch"
        exit 1
    fi
}

# 启动 MinIO
start_minio() {
    local container_name="minio"
    local image_name="minio"
    
    if check_running $container_name; then
        log_info "MinIO is already running"
        return 0
    fi
    
    remove_container $container_name
    
    log_info "Starting MinIO..."
    docker run -d \
        --restart=always \
        --name $container_name \
        -p 9000:9000 \
        -p 9001:9001 \
        -v /workspace/minio/data:/data \
        -v /workspace/minio/config:/root/.minio \
        -e "MINIO_ACCESS_KEY=y4UIFvfDRhSYnkjquuAf" \
        -e "MINIO_SECRET_KEY=Anl739RDZ7kTBWdd6zZQ318ZkkKCFfFtp1qzZqoV" \
        -e "MINIO_ROOT_USER=minio" \
        -e "MINIO_ROOT_PASSWORD=minio@2023" \
        $image_name server /data \
        --console-address ":9001"
    
    if [ $? -ne 0 ]; then
        log_error "Failed to start MinIO"
        exit 1
    fi
}

# 启动 es-search
start_es_search() {
    local container_name="es-search"
    local image_name="es-search"
    
    if check_running $container_name; then
        log_info "es-search is already running"
        return 0
    fi
    
    remove_container $container_name
    
    log_info "Starting es-search..."
    docker run -d \
        --restart=always \
        --name $container_name \
        -p 5173:5173 \
        -v /etc/localtime:/etc/localtime \
        -v /workspace/logs/supervisor:/var/log/supervisor \
        --link elasticsearch:elasticsearch \
        $image_name
    
    if [ $? -ne 0 ]; then
        log_error "Failed to start es-search"
        exit 1
    fi
}

# 启动应用
start_application() {
    local container_name="hngpt-app"
    local image_name="hngpt-bi"
    
    if check_running $container_name; then
        log_info "Application is already running"
        return 0
    fi
    
    remove_container $container_name
    
    log_info "Starting application..."
    docker run -d \
        --restart=always \
        --name $container_name \
        -p 18888:18888 \
        -p 18889:18889 \
        -v /etc/localtime:/etc/localtime \
        -v /workspace/logs/supervisor:/var/log/supervisor \
        $image_name
    
    if [ $? -ne 0 ]; then
        log_error "Failed to start application"
        exit 1
    fi
}

# 等待服务就绪
wait_for_service() {
    local service_name=$1
    local check_url=$2
    local max_attempts=$3
    local sleep_time=$4
    
    log_info "Waiting for $service_name to be ready..."
    for i in $(seq 1 $max_attempts); do
        if curl -s $check_url > /dev/null; then
            log_info "$service_name is ready"
            return 0
        fi
        log_warn "Waiting for $service_name... ($i/$max_attempts)"
        sleep $sleep_time
    done
    log_error "$service_name did not become ready in time"
    exit 1
}

# 更新容器重启策略
update_restart_policy() {
    local container_name=$1
    if check_container $container_name; then
        log_info "Updating restart policy for $container_name..."
        docker update --restart=always $container_name > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            log_info "Successfully updated restart policy for $container_name"
        else
            log_warn "Failed to update restart policy for $container_name"
        fi
    fi
}

# 主函数
main() {
    log_info "Starting deployment..."
    
    # 创建目录
    create_directories
    
    # 启动服务
    start_elasticsearch
    start_minio
    
    # 等待基础服务就绪
    wait_for_service "Elasticsearch" "http://localhost:9200" 30 10
    wait_for_service "MinIO" "http://localhost:9000" 30 10
    
    # 启动应用
    start_application
    # 等待应用就绪
    wait_for_service "Application" "http://localhost:18888/health" 30 10
    
    # 启动 es-search
    start_es_search
    # 等待 es-search 就绪
    wait_for_service "es-search" "http://localhost:5173/health" 30 10
    
    # 确保所有容器都设置了重启策略
    log_info "Ensuring restart policies..."
    update_restart_policy "elasticsearch"
    update_restart_policy "minio"
    update_restart_policy "es-search"
    update_restart_policy "hngpt-app"
    
    log_info "Deployment completed successfully"
    log_info "
Services are ready:

Elasticsearch:
URL: http://localhost:9200
Username: elastic
Password: elastic

MinIO:
URL: http://localhost:9000 (API)
Console: http://localhost:9001
Username: minio
Password: minio@2023
Access Key: y4UIFvfDRhSYnkjquuAf
Secret Key: Anl739RDZ7kTBWdd6zZQ318ZkkKCFfFtp1qzZqoV

es-search:
URL: http://localhost:5173
Health: http://localhost:5173/health

Application:
URL: http://localhost:18888
API Docs: http://localhost:18888/docs
"
}

# 执行主函数
main