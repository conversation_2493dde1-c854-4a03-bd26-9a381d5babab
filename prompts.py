import json

# class PromptManager:
#     def __init__(self, prompts_file: str):
#         self.prompts_file = prompts_file
#         self.prompts = self.load_prompts()

#     def load_prompts(self):
#         try:
#             with open(self.prompts_file, "r",encoding='utf-8') as file:
#                 prompts = json.load(file)
#         except FileNotFoundError:
#             prompts = {}
#         return prompts

#     def save_prompts(self):
#         with open(self.prompts_file, "w",encoding='utf-8') as file:
#             json.dump(self.prompts, file)

#     def add_prompt(self, prompt_name: str, template: str):
#         self.prompts[prompt_name] = {
#             "template": template
#         }
#         self.save_prompts()

#     def make_prompt(self, prompt_name: str, document: str, query: str):
#         if prompt_name not in self.prompts:
#             return ""
#         prompt_template = self.prompts[prompt_name]["template"]
#         prompt = prompt_template.replace("{document}", document).replace("{query}", query)
#         return prompt

#     def get_prompts(self):
#         return self.prompts


#     def get_prompt_names(self):
#         return list(self.prompts.keys())
    
#     def get_prompt(self, prompt_name: str):
#         if prompt_name not in self.prompts:
#             return ""
#         return self.prompts[prompt_name]
    
#     def del_prompt(self, prompt_name: str):
#         if prompt_name not in self.prompts:
#             return
#         del self.prompts[prompt_name]
#         self.save_prompts()


class PromptManager:
    def __init__(self, prompts_file: str):
        self.prompts_file = prompts_file
        self.prompts = self.load_prompts()
        self.keys = self.load_keys()

    def load_prompts(self):
        try:
            with open(self.prompts_file, "r", encoding='utf-8') as file:
                prompts = json.load(file)
        except FileNotFoundError:
            prompts = []
        return prompts

    def save_prompts(self):
        with open(self.prompts_file, "w", encoding='utf-8') as file:
            json.dump(self.prompts, file, ensure_ascii=False)

    def add_prompt(self, act: str, prompt: str):
        for i, p in enumerate(self.prompts):
            if p["act"] == act:
                self.prompts[i]["prompt"] = prompt
                self.save_prompts()
                return
        self.prompts.append({
            "act": act,
            "prompt": prompt
        })
        self.save_prompts()


    def make_prompt(self, act: str, context: str = "", question: str = ""):
        for prompt in self.prompts:
            if prompt["act"] == act:
                prompt_template = prompt["prompt"]
                prompt = prompt_template.replace("{context}", context).replace("{question}", question)
                return prompt
        return ""

    def get_prompts(self):
        return self.prompts

    def get_prompt_names(self):
        return [prompt["act"] for prompt in self.prompts]
    
    def get_prompt(self, prompt_name: str):
        self.load_prompts()
        for prompt in self.prompts:
            if prompt["act"] == prompt_name:
                return prompt["prompt"]
        return ""
    
    def del_prompt(self, prompt_name: str):
        for i, prompt in enumerate(self.prompts):
            if prompt["act"] == prompt_name:
                del self.prompts[i]
                self.save_prompts()
                return


    def load_keys(self):
        try:
            with open("templates/keywords.json", "r", encoding='utf-8') as file:
                keys = json.load(file)
        except FileNotFoundError:
                keys = {"options":[]}
        return keys

Prompts = PromptManager('templates/prompts.json')