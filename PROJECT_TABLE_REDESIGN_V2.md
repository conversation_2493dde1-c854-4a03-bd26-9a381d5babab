# 项目表结构重新设计 V2 - 基于项目维度

## 🎯 **设计理念转变**

### ❌ **之前的错误理解**
- 以为每个文档都要生成一条记录
- 使用 `doc_id` 作为主要标识
- 导致一个项目有多条记录，无法统计

### ✅ **正确的业务理解**
- **一个项目包含多个文档** → **解析所有文档** → **合并信息** → **生成一条项目记录**
- 项目是统计的基本单位，不是文档
- 需要支持项目信息的更新和合并

## 🔧 **新表结构设计**

### **1. 项目提取表 (project_extract)**

```sql
CREATE TABLE `project_extract` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `project_name` varchar(500) NOT NULL,           -- 项目名称（唯一）
  `project_key` varchar(255) NOT NULL,            -- 项目唯一标识（MD5）
  `project_no` varchar(100) DEFAULT NULL,         -- 项目编号
  `start_date` date DEFAULT NULL,                 -- 开始日期
  `end_date` date DEFAULT NULL,                   -- 结束日期
  `total_investment` bigint(20) DEFAULT NULL,     -- 总投资（万元）
  `responsible_unit` text,                        -- 承担单位
  `leader` varchar(200) DEFAULT NULL,             -- 项目负责人
  `research_points` text,                         -- 主要研究内容
  `innovation` text,                              -- 创新点
  `main_deliverables` text,                       -- 主要交付成果
  `patent` text,                                  -- 专利信息
  `source_documents` json DEFAULT NULL,           -- 来源文档列表
  `document_count` int(11) DEFAULT 0,             -- 包含的文档数量
  `extraction_confidence` decimal(3,2),           -- 整体提取置信度
  `last_updated_from` varchar(255),               -- 最后更新来源
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_key` (`project_key`),
  UNIQUE KEY `uk_project_name` (`project_name`),
  KEY `idx_investment` (`total_investment`),       -- 支持投资额查询
  KEY `idx_date_range` (`start_date`, `end_date`)  -- 支持时间范围查询
);
```

### **2. 核心设计特点**

#### **唯一性保证**
- **`project_name`** - 项目名称唯一约束
- **`project_key`** - 基于项目名称的MD5哈希，确保唯一性

#### **统计查询优化**
- **`total_investment`** - 投资金额索引，支持 `> 40万` 等查询
- **`start_date, end_date`** - 时间范围索引，支持 `2020-2025年` 查询
- **`responsible_unit`** - 承担单位索引，支持按单位统计

#### **数据完整性**
- **`source_documents`** - JSON数组，记录所有来源文档ID
- **`document_count`** - 文档数量，便于评估数据完整性
- **`extraction_confidence`** - 提取置信度，便于质量控制

## 📊 **支持的统计查询**

### **1. 基本统计查询**
```sql
-- 查询2020-2025年投资超过40万的项目
SELECT 
    project_name,
    total_investment,
    start_date,
    responsible_unit,
    document_count
FROM project_extract
WHERE YEAR(start_date) BETWEEN 2020 AND 2025
  AND total_investment > 40
ORDER BY total_investment DESC;
```

### **2. 聚合统计查询**
```sql
-- 按年度统计项目投资情况
SELECT 
    YEAR(start_date) as year,
    COUNT(*) as project_count,
    SUM(total_investment) as total_investment,
    AVG(total_investment) as avg_investment
FROM project_extract
WHERE YEAR(start_date) BETWEEN 2020 AND 2025
GROUP BY YEAR(start_date)
ORDER BY year;
```

### **3. 按单位统计**
```sql
-- 按承担单位统计项目情况
SELECT 
    responsible_unit,
    COUNT(*) as project_count,
    SUM(total_investment) as total_investment
FROM project_extract
WHERE YEAR(start_date) BETWEEN 2020 AND 2025
GROUP BY responsible_unit
HAVING COUNT(*) >= 2
ORDER BY total_investment DESC;
```

## 🔄 **数据更新逻辑**

### **项目信息合并流程**
```python
def finalize_project(project_name, action, merged_info):
    # 1. 生成项目唯一标识
    project_key = hashlib.md5(project_name.strip().encode('utf-8')).hexdigest()
    
    # 2. 准备项目数据
    sql_data = {
        "project_key": project_key,
        "project_name": project_name,
        "total_investment": parse_investment_amount(merged_info.get("total_investment")),
        "document_count": len(source_documents),
        # ... 其他字段
    }
    
    # 3. 使用project_key进行upsert
    upsert(table="project_extract", data=sql_data, key_fields=["project_key"])
```

### **支持的更新场景**
1. **新项目** - 插入新记录
2. **项目更新** - 基于 `project_key` 更新现有记录
3. **信息合并** - 多个文档的信息合并到一条记录

## 🎯 **解决的问题**

### ✅ **统计查询支持**
- **投资额统计** - `WHERE total_investment > 40`
- **时间范围查询** - `WHERE YEAR(start_date) BETWEEN 2020 AND 2025`
- **单位统计** - `GROUP BY responsible_unit`

### ✅ **数据一致性**
- **一个项目一条记录** - 避免重复统计
- **唯一性约束** - 防止数据冲突
- **信息完整性** - 记录来源文档和置信度

### ✅ **性能优化**
- **专门的索引** - 针对统计查询优化
- **合理的数据类型** - `bigint` 支持大金额，`varchar` 限制长度
- **JSON字段** - 灵活存储来源文档信息

## 🚀 **实施步骤**

### **1. 数据库迁移**
```bash
# 运行迁移脚本
python migrate_extract_tables_v2.py
```

### **2. 代码更新**
- ✅ 修改 `knowledge_control.py` 中的 `finalize_project` 方法
- ✅ 使用 `project_key` 替代 `doc_id`
- ✅ 优化字段映射和数据类型

### **3. 测试验证**
```sql
-- 验证数据完整性
SELECT COUNT(*) FROM project_extract;

-- 验证统计查询
SELECT * FROM project_statistics_view_v2 
WHERE start_year BETWEEN 2020 AND 2025 
  AND total_investment > 40;
```

## 💡 **总结**

### **核心改进**
1. **从文档维度转向项目维度** - 符合业务逻辑
2. **移除不必要的doc_id依赖** - 简化设计
3. **优化统计查询性能** - 专门的索引设计
4. **支持数据更新和合并** - 灵活的upsert机制

### **预期效果**
- ✅ **统计查询高效** - "2020-2025年投资超过40万的项目"等查询秒级响应
- ✅ **数据一致性** - 一个项目一条记录，避免重复统计
- ✅ **扩展性强** - 支持未来的统计需求扩展
- ✅ **维护简单** - 清晰的数据模型，易于理解和维护

现在的设计完全符合你的业务需求：**一个项目包含多个文档，解析完毕后生成一条项目记录，支持高效的统计查询**！🎯
