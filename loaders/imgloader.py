from typing import List
from loaders.detect import rapid_ocr, rapid_ocrm,rapid_hwseal, find_time, img23
from pathlib import Path
from typing import List, Union
from io import BytesIO
import os
import textwrap
import cv2
import numpy as np
from sumary import get_normal
class ExtractImg: 
    def __init__(self, ocr = True, seal = True) -> None:
        self.ocr = ocr
        self.seal = seal  
         
    def img2text(self,filepath):
        pages = []
        print("begin converting image")
        page_data = ""
        seals = []
        hws = []
        img_array = cv2.imread(filename=filepath)
        img_array = img23(img_array)
        # image = Image.fromarray(img_array)
        # image.save('saved_image.png')
        if self.ocr:
            result, _ = rapid_ocr(img_array)
            if result:
                ocr_result = [box[1] for box in result]
                page_data += " ".join(ocr_result)
            # print(f"{page_num} \n {page_data}")
        if self.seal:
            seal, hw, _ = rapid_hwseal(img_array)
            seals.extend(seal)
            hws.extend(hw)
        page_data = get_normal(page_data)
        print(page_data)
        dates = find_time(page_data) 
        content = {
            "id": os.path.basename(filepath),
            "pageNo": 1,
            "pageContent": textwrap.wrap(page_data,width=60),
            "sealContent": seals,
            "handWriteContent": hws,
            "dateContent": dates
        }
        pages.append(content)
        return pages 

    def __call__(self,filepath) -> List:
        contents = self.img2text(filepath)
        return contents
    
    

