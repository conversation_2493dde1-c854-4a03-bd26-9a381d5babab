import re
import chardet
from datetime import datetime,timedelta


# 匹配正则表达式
matchs = {
    1:(r'\d{4}%s\d{1,2}%s\d{1,2}%s \d{1,2}%s\d{1,2}%s\d{1,2}%s','%%Y%s%%m%s%%d%s %%H%s%%M%s%%S%s'),
    2:(r'\d{4}%s\d{1,2}%s\d{1,2}%s \d{1,2}%s\d{1,2}%s','%%Y%s%%m%s%%d%s %%H%s%%M%s'),
    3:(r'\d{4}%s\d{1,2}%s\d{1,2}%s','%%Y%s%%m%s%%d%s'),
    4:(r'\d{2}%s\d{1,2}%s\d{1,2}%s','%%y%s%%m%s%%d%s'),
   
    # 没有年份
    5:(r'\d{1,2}%s\d{1,2}%s \d{1,2}%s\d{1,2}%s\d{1,2}%s','%%m%s%%d%s %%H%s%%M%s%%S%s'),
    6:(r'\d{1,2}%s\d{1,2}%s \d{1,2}%s\d{1,2}%s','%%m%s%%d%s %%H%s%%M%s'),
    7:(r'\d{1,2}%s\d{1,2}%s','%%m%s%%d%s'),
    

    # 没有年月日
    8:(r'\d{1,2}%s\d{1,2}%s\d{1,2}%s','%%H%s%%M%s%%S%s'),
    9:(r'\d{1,2}%s\d{1,2}%s','%%H%s%%M%s'),
}

# 正则中的%s分割
splits = [
    {1:[('年','月','日','点','分','秒'),('-','-','',':',':',''),('\/','\/','',':',':',''),('\.','\.','',':',':','')]},
    {2:[('年','月','日','点','分'),('-','-','',':',''),('\/','\/','',':',''),('\.','\.','',':','')]},
    {3:[('年','月','日'),('-','-',''),('\/','\/',''),('\.','\.','')]},
    {4:[('年','月','日'),('-','-',''),('\/','\/',''),('\.','\.','')]},

    {5:[('月','日','点','分','秒'),('-','',':',':',''),('\/','',':',':',''),('\.','',':',':','')]},
    {6:[('月','日','点','分'),('-','',':',''),('\/','',':',''),('\.','',':','')]},
    {7:[('月','日'),('-',''),('\/',''),('\.','')]},

    {8:[('点','分','秒'),(':',':','')]},
    {9:[('点','分'),(':','')]},
]

def func(parten,tp):
    re.search(parten,parten)
    

class TimeFinder(object):

    def __init__(self):
        self.match_item = []
        self.base_date = datetime.now()
        self.init_args()
        self.init_match_item()

    def init_args(self):
        if self.base_date and not isinstance(self.base_date,datetime):
            try:
                self.base_date = datetime.strptime(self.base_date,'%Y-%m-%d %H:%M:%S')
            except Exception as e:
                raise 'type of base_date must be str of%Y-%m-%d %H:%M:%S or datetime'

    def init_match_item(self):
        # 构建穷举正则匹配公式 及提取的字符串转datetime格式映射
        for item in splits:
            for num,value in item.items():
                match = matchs[num]
                for sp in value:
                    tmp = []
                    for m in match:
                        tmp.append(m%sp)
                    self.match_item.append(tuple(tmp))


    def find_time(self,text):
         # 格式化text为str类型
        if isinstance(text,bytes):
            encoding =chardet.detect(text)['encoding']
            text = text.decode(encoding)

        res = []
        parten = '|'.join([x[0] for x in self.match_item])

        match_list = re.findall(parten,text)
        if not match_list:
            return []
        for match in match_list:
            for item in self.match_item:
                try:
                    date = datetime.strptime(match,item[1].replace('\\',''))
                    if date.year==1900:
                        date = date.replace(year=self.base_date.year)
                        if date.month==1:
                            date = date.replace(month=self.base_date.month)
                            if date.day==1:
                                date = date.replace(day=self.base_date.day)
                    res.append(datetime.strftime(date,'%Y-%m-%d %H:%M:%S'))
                    break
                except Exception as e:
                    continue
        if not res:
            return []
        return res
    
    def find_date(self, text):
        # 格式化text为str类型
        if isinstance(text, bytes):
            encoding = chardet.detect(text)['encoding']
            text = text.decode(encoding)

        res = []
        parten = '|'.join([x[0] for x in self.match_item])

        match_list = re.findall(parten, text)
        if not match_list:
            return []

        for match in match_list:
            for item in self.match_item:
                try:
                    # 使用匹配项中的格式字符串解析日期
                    date = datetime.strptime(match, item[1].replace('\\', ''))
                    
                    # 如果年份为1900，则使用base_date的年份
                    if date.year == 1900:
                        date = date.replace(year=self.base_date.year)
                        
                        # 如果月份为1，则使用base_date的月份
                        if date.month == 1:
                            date = date.replace(month=self.base_date.month)
                            
                            # 如果天为1，则使用base_date的天
                            if date.day == 1:
                                date = date.replace(day=self.base_date.day)
                    
                    # 将日期格式化为YYYY-MM
                    res.append(date.strftime('%Y-%m'))
                    break
                except Exception as e:
                    continue

        if not res:
            return []
        return res

timefinder =TimeFinder()


