import os
import sys
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(current_dir)
import argparse
import re
import xml.etree.ElementTree as ET
import zipfile
from io import BytesIO
from pathlib import Path
from typing import List, Union
import numpy as np
import docx
import lxml.etree as etree
import pandas as pd
from docx.document import Document
from docx.oxml.table import CT_Tbl
from docx.table import Table, _Cell
import chardet

def clean_xml_string(text):
    # 移除无效的字符
    cleaned_text = ''.join(c for c in text if is_xml_compatible(c))
    return cleaned_text

def is_xml_compatible(char):
    # 检查字符是否是 XML 兼容的
    return char == '\n' or '\u0020' <= char <= '\uD7FF' or '\uE000' <= char <= '\uFFFD'

class ExtractWord:
    def __init__(
        self,
    ):
        self.img_suffix = [".jpg", ".jpeg", ".png", ".bmp"]
        self.nsmap = {
            "w": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"
        }
        self.extract_table = ExtractWordTable()
        self.parser = etree.XMLParser()
        
    def doc2docx(self, doc_file):
        doc = docx.Document()
        with open(doc_file, 'rb') as file:
            content = file.read()
            detected_encoding = chardet.detect(content)['encoding']
            if not detected_encoding:
                detected_encoding = 'gbk'
            decoded_content = content.decode(detected_encoding, errors='ignore')
        clean_content = clean_xml_string(decoded_content)
        for line in clean_content.split('\n'):
            doc.add_paragraph(line)
        new_docx_file = doc_file + "x"
        doc.save(new_docx_file)
        os.remove(doc_file)
        return new_docx_file


        
    def __call__(self, docx_content: Union[str, bytes], save_img_dir=None) -> List:
        if isinstance(docx_content, str) and not Path(docx_content).exists():
            raise FileNotFoundError(f"{docx_content} does not exist.")
        elif isinstance(docx_content, bytes):
            docx_content = BytesIO(docx_content)

        self.table_content = self.extract_table(docx_content)

        text = ""

        # unzip the docx_content in memory
        zipf = zipfile.ZipFile(docx_content)
        filelist = zipf.namelist()

        header_files, footer_files, img_files = [], [], []
        header_xmls = "word/header[0-9]*.xml"
        footer_xmls = "word/footer[0-9]*.xml"

        for fname in filelist:
            if re.match(header_xmls, fname):
                header_files.append(fname)
            elif re.match(footer_xmls, fname):
                footer_files.append(fname)
            elif Path(fname).suffix.lower() in self.img_suffix:
                img_files.append(fname)
            else:
                continue

        # get header text
        # there can be 3 header files in the zip
        header_text = [self.xml2text(zipf.read(path)) for path in header_files]
        if header_text:
            header_text = "".join(header_text).strip()
            text += header_text

        # get main text
        doc_xml = "word/document.xml"
        main_txt = self.xml2text(zipf.read(doc_xml))
        if main_txt:
            text += main_txt.strip()

        # get footer text
        # there can be 3 footer files in the zip
        footer_text = [self.xml2text(zipf.read(path)) for path in footer_files]
        if footer_text:
            footer_text = "".join(footer_text).strip()
            text += footer_text
        seals = []
        hws = []
        for img_path in img_files:
            img_array = np.frombuffer(zipf.read(img_path), dtype=np.uint8).reshape(pix.height, pix.width, -1)
            result, _ = rapid_ocr(img_array)
            if result:
                ocr_result = [box[1] for box in result]
                page_data += ocr_result
            seal, hw, _ = rapid_hwseal(img_array)
            seals.extend(seal)
            hws.extend(hw)
        # zipf.close()
        # dates = find_time(page_data) 
        # content = {
        #     "id": os.path.basename(filepath),
        #     "pageNo": page_num,
        #     "pageContent": textwrap.wrap(page_data),
        #     "sealContent": seals,
        #     "handWriteContent": hws,
        #     "dateContent": dates
        # }
        # pages.append(content)
        text += self.table_content
        return text 

    def qn(self, tag):
        """
        Stands for 'qualified name', a utility function to turn a namespace
        prefixed tag name into a Clark-notation qualified tag name for lxml. For
        example, ``qn('p:cSld')`` returns ``'{http://schemas.../main}cSld'``.
        Source: https://github.com/python-openxml/python-docx/
        """
        prefix, tagroot = tag.split(":")
        uri = self.nsmap[prefix]
        return f"{{{uri}}}{tagroot}"

    def xml2text(self, xml):
        """
        A string representing the textual content of this run, with content
        child elements like ``<w:tab/>`` translated to their Python
        equivalent.
        Adapted from: https://github.com/python-openxml/python-docx/
        """
        text = ""
        table_xml = self.extract_table_by_xml(xml_path=xml)

        root = ET.fromstring(xml)
        for child in root.iter():
            if child.tag == self.qn("w:t"):
                t_text = child.text
                if t_text in table_xml:
                    continue

                text += t_text if t_text is not None else ""
            elif child.tag == self.qn("w:tab"):
                text += "\t"
            elif child.tag in (self.qn("w:br"), self.qn("w:cr")):
                text += "\n"
            elif child.tag == self.qn("w:p"):
                text += "\n\n"
        return text

    def extract_table_by_xml(
        self,
        xml_path: str,
    ) -> str:
        tree = etree.fromstring(xml_path, self.parser)
        table_txts = tree.xpath("//w:tbl//w:t/text()", namespaces=self.nsmap)
        return table_txts


class ExtractWordTable:
    def __init__(
        self,
    ):
        pass

    def __call__(self, docx_content):
        curr_content = ""
        doc = docx.Document(docx_content)
        for block in self.iter_block_items(doc):
            if isinstance(block, docx.table.Table):
                df = self.get_table_dataframe(block)
                try:
                    curr_content += f"\n{df.to_markdown().replace('--','').replace('  ','')}"
                except:
                    curr_content += f"\n{df}"
        return curr_content


    def to_str(self,df):
        data_dict = df.to_dict(orient="records")
        # 构建自定义字符串
        result = ""
        for row in data_dict:
            row_str = ", ".join(f"'{key}': {value}" for key, value in row.items())
            result += f"{row_str},\n"

        return result
    
    def iter_block_items(self, parent):
        if isinstance(parent, Document):
            # 判断传入的是否为word文档对象，是则获取文档内容的全部子对象
            parent_elm = parent.element.body
        elif isinstance(parent, _Cell):
            # 判断传入的是否为单元格，是则获取单元格内全部子对象
            parent_elm = parent._tc
        else:
            raise ValueError("something's not right")

        for child in parent_elm.iterchildren():
            if isinstance(child, CT_Tbl):
                yield Table(child, parent)

    def get_table_dataframe(self, table: docx.table.Table) -> pd.DataFrame:
        """获取表格数据，转换为dataframe数据结构"""
        text = []
        if len(table.rows) == 1:
            for i in table.rows[0].cells:
                text.append(i.text)
            return text[-1]

        keys, table_data = None, []
        for i, row in enumerate(table.rows):
            # 获取表格一行的数据
            text = (cell.text for cell in row.cells)

            # 判断是否是表头
            if i == 0:
                keys = tuple(text)
                continue

            table_data.append(dict(zip(keys, text)))
        df = pd.DataFrame(table_data)
        return df


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("word_path", type=str)
    parser.add_argument("-img_dir", "--save_img_dir", type=str, default=None)
    args = parser.parse_args()

    word_extract = ExtractWord()
    res = word_extract(args.word_path, save_img_dir=args.save_img_dir)
    print(res)


if __name__ == "__main__":
    main()
