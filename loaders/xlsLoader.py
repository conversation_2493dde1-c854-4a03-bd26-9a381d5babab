import os
import sys
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(current_dir)
from io import BytesIO
import argparse
import tempfile
import uuid
import warnings
import zipfile
from pathlib import Path
from typing import List, Union
import xlrd
import openpyxl
import pandas as pd
from openpyxl.workbook.workbook import Workbook
from loaders.detect import mkdir,split_text2


class ExtractExcel:
    def __init__(self,):
        self.img_suffix = [".jpg", ".jpeg", ".png", ".bmp"]

    def xls2xlsx(self, xls_file):
        workbook = xlrd.open_workbook(xls_file)
        new_workbook = Workbook()
        for sheet_name in workbook.sheet_names():
            worksheet = new_workbook.create_sheet(title=sheet_name)
            sheet = workbook.sheet_by_name(sheet_name)
            for row in range(sheet.nrows):
                for col in range(sheet.ncols):
                    cell_value = sheet.cell_value(row, col)
                    worksheet.cell(row=row + 1, column=col + 1, value=cell_value)
        new_workbook.remove(new_workbook["Sheet"])
        new_xlsx_file = xls_file + "x"
        new_workbook.save(new_xlsx_file)
        os.remove(xls_file)
        return new_xlsx_file


    def __call__(
        self,
        excel_content: Union[str, Path, bytes],
        out_format: str = "json",
        save_img_dir: str = None,
    ) -> List:
        if isinstance(excel_content, (str, Path)):
            if not Path(excel_content).exists():
                raise FileNotFoundError(f"{excel_content} does not exist.")
            excel_content = str(excel_content)
        elif isinstance(excel_content, bytes):
            excel_content = BytesIO(excel_content)
        if out_format != "record":
            wb = self.unmerge_cell(excel_content)
        else:
            wb = self.unmerge_cell2(excel_content)
        contents = self.extract_table(wb, out_format)

        if save_img_dir:
            try:
                self.extract_imgs(excel_content, save_img_dir)
            except FileExistsError:
                warnings.warn(f"The {excel_content} does not contain any images.")
        return contents

    def unmerge_cell(self, file_name: str) -> Workbook:
        wb = openpyxl.load_workbook(file_name)
        for sheet_name in wb.sheetnames:
            sheet = wb[sheet_name]
            self.unmerge_and_fill_cells(sheet)
        return wb
    
    def unmerge_cell2(self,file_name):
        wb = self.unmerge_cell(file_name)
        with tempfile.TemporaryDirectory() as tmp_dir:
            tmp_save_path = Path(tmp_dir) / f"{uuid.uuid1()}.xlsx"
            wb.save(str(tmp_save_path))
            wb.close()
            wb = openpyxl.load_workbook(tmp_save_path)
            for sheet_name in wb.sheetnames:
                sheet = wb[sheet_name]
                self.rm_rows(sheet)
            os.remove(tmp_save_path)
        return wb

    def rm_rows(self, worksheet):
        rows_to_delete = []
        for row in worksheet.iter_rows():
            cell_values = [cell.value for cell in row]
            if all(value == cell_values[0] for value in cell_values):
                rows_to_delete.append(row[0].row)

        for row in reversed(rows_to_delete):
            worksheet.delete_rows(row, 1)
                   
    def unmerge_and_fill_cells(self, worksheet: Workbook) -> None:
        """
        # 拆分所有的合并单元格，并赋予合并之前的值。
        # 由于openpyxl并没有提供拆分并填充的方法，所以使用该方法进行完成
        """
        all_merged_cell_ranges = list(worksheet.merged_cells.ranges)

        for merged_cell_range in all_merged_cell_ranges:
            merged_cell = merged_cell_range.start_cell
            worksheet.unmerge_cells(range_string=merged_cell_range.coord)
            for row_index, col_index in merged_cell_range.cells:
                cell = worksheet.cell(row=row_index, column=col_index)
                cell.value = merged_cell.value

    def extract_table(self, wb: Workbook, out_format: str) -> List:
        sheet_names = wb.sheetnames
        with tempfile.TemporaryDirectory() as tmp_dir:
            tmp_save_path = Path(tmp_dir) / f"{uuid.uuid1()}.xlsx"
            wb.save(str(tmp_save_path))
            wb.close()

            contents = ""
            for name in sheet_names:
                data = pd.read_excel(tmp_save_path, index_col=None, sheet_name=name)
                #cvt_data = self.convert_table(data, out_format)
                cvt_data = self.to_str(data)
                contents+= cvt_data 
            os.remove(tmp_save_path)
        return contents


    def to_str(self,df):
        data_dict = df.to_dict(orient="records")
        # 构建自定义字符串
        result = ""
        for row in data_dict:
            row_str = ", ".join(f"'{key}': {value}" for key, value in row.items())
            result += f"{row_str},\n"

        return result


    @staticmethod
    def convert_table(df_table: pd.core.frame.DataFrame, out_format: str) -> str:
        if "to_" not in out_format:
            out_format = f"to_{out_format}"

        try:
            return getattr(df_table, out_format)()
        except AttributeError as exc:
            raise AttributeError(f"{out_format} is not supported.") from exc

    def extract_imgs(
        self, excel_content: Union[str, Path, bytes], save_img_dir: Union[str, Path]
    ) -> None:
        with zipfile.ZipFile(excel_content) as zf:
            file_list = zf.namelist()

            img_list = [
                path for path in file_list if Path(path).suffix in self.img_suffix
            ]

            if not img_list:
                raise FileExistsError("The xl/media is not existed.")

            mkdir(save_img_dir)
            for img_path in img_list:
                save_path = Path(save_img_dir) / Path(img_path).name
                with open(save_path, "wb") as f:
                    f.write(zf.read(img_path))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--excel_path", type=str, default= "./downloads/test.xlxs")
    parser.add_argument(
        "-f",
        "--output_format",
        type=str,
        default="record",
        choices=["markdown", "html", "latex", "string"],
    )
    parser.add_argument("-o", "--save_img_dir", type=str, default=None)
    try:
        args = parser.parse_args()
    except SystemExit as e:
        print("Error occurred during argument parsing:", e)
    excel_extract = ExtractExcel()
    res = excel_extract(
        args.excel_path, out_format=args.output_format, save_img_dir=args.save_img_dir
    )
    print(res)


if __name__ == "__main__":
    main()
