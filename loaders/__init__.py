# 注释掉依赖langchain的导入
# from .normal import RapidOCRPDFLoader
# from .normal import RapidOCRLoader
from .fileloader import FileLoader
from .detect import rapid_ocr, rapid_hwseal
from .extime import timefinder

# 为了兼容性，创建简单的替代类
class RapidOCRPDFLoader:
    def __init__(self, *args, **kwargs):
        pass

    def load(self):
        return []

class RapidOCRLoader:
    def __init__(self, *args, **kwargs):
        pass

    def load(self):
        return []