import sys
import os
from pathlib import Path
parent_dir = str(Path(__file__).parent.parent)
sys.path.append(parent_dir)
dir = str(Path(__file__).parent)
sys.path.append(dir)
from typing import List, Union
import shutil
import subprocess
import re
import numpy as np
import cv2
import time
from datetime import datetime
from typing import List, Union


def img23(img_array):
    channels = img_array.shape[2]
    if channels == 4:
        img_array = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
    elif channels == 1:
        img_array = cv2.cvtColor(img_array, cv2.COLOR_GRAY2BGR)

    return img_array




def remove_chinese_gibberish(text):
    pattern = r'[\u4e00-\u9fff]+'
    cleaned_text = re.sub(pattern, '', text)
    return cleaned_text


def split_text2(text, min_length=512, buffer_min_length=0):
    # text = text.replace("\n","")
    if not text:
        return []
    if len(text) < min_length:
        return [text]

    # Pre-process the text to remove \n after ":" or "："
    text = re.sub(r'[:：]\s*\n\s*', lambda m: m.group()[0], text)

    # Define the pattern for splitting while capturing the punctuation
    pattern = r'([\?\!；；\。\？\！、，；,])'

    # First, split the text by ':' and '：', and process each part separately
    colon_parts = re.split(r'(?<=[:：])', text)
    
    segments = []
    current_segment = ''
    
    for part in colon_parts:
        # Split the part into sentences while keeping the punctuation
        parts = re.split(pattern, part)
        
        # Recombine the parts into sentences with punctuation attached
        sentences = []
        for i in range(0, len(parts) - 1, 2):
            sentence = parts[i] + (parts[i + 1] if i + 1 < len(parts) else '')
            sentences.append(sentence)

        if len(parts) % 2 != 0:  # handle the last part without punctuation
            sentences.append(parts[-1])

        # Process sentences
        buffer_sentences = ''
        cleared_buffer = ''

        for i, sentence in enumerate(sentences):
            # Add the sentence to the buffer
            buffer_sentences += sentence

            # If the buffer reaches the minimum length, append it to the current segment
            if len(buffer_sentences) >= buffer_min_length:
                current_segment += buffer_sentences
                cleared_buffer = buffer_sentences
                buffer_sentences = ''

            # If the current segment reaches the minimum length or we are at the last sentence of the last part
            if len(current_segment) >= min_length or (part == colon_parts[-1] and i == len(sentences) - 1):
                if (part == colon_parts[-1] and i == len(sentences) - 1) and buffer_sentences:
                    current_segment += buffer_sentences
                if current_segment:
                    # Remove extra spaces but keep other newlines
                    segments.append(re.sub(r' +', ' ', current_segment))
                if i < len(sentences) - 1 or part != colon_parts[-1]:
                    current_segment = cleared_buffer
                else:
                    current_segment = ''

    if current_segment:
        # Remove extra spaces but keep other newlines
        segments.append(re.sub(r' +', ' ', current_segment))

    return segments



def update(file_path):
    ext = os.path.splitext(file_path)[1]
    outdir = os.path.join(parent_dir,"data/tmp_files")
    if not os.path.exists(outdir):
        os.makedirs(outdir)
    base_name = os.path.basename(file_path)
    file_name = os.path.splitext(base_name)[0]  # 去除扩展名的文件名
    if ext == ".doc":
        converted_file = os.path.join(outdir, file_name + ".pdf")
        cmd = [
            "libreoffice",
            "--headless",
            f'--infilter="MS Word 97"',
            '--convert-to',
            f'pdf:writer_pdf_Export',
            '--outdir',
            outdir,
            file_path
        ]
    if ext == ".docx":
        converted_file = os.path.join(outdir, file_name + ".pdf")
        cmd = [
            "libreoffice",
            "--headless",
            f'--infilter="MS Word 2007 XML"',
            '--convert-to',
            f'pdf:writer_pdf_Export',
            '--outdir',
            outdir,
            file_path
        ]
    elif ext == ".xls":
        converted_file = os.path.join(outdir, file_name + ".pdf")
        cmd = [
            "libreoffice",
            "--headless",
            f'--infilter="MS Excel 97"',
            '--convert-to',
            f'pdf:calc_pdf_Export',
            '--outdir',
            outdir,
            file_path
        ]
    elif ext == ".xlsx":
        converted_file = os.path.join(outdir, file_name + ".pdf")
        cmd = [
            "libreoffice",
            "--headless",
            f'--infilter="Calc MS Excel 2007 XML"',
            '--convert-to',
            f'pdf:calc_pdf_Export',
            '--outdir',
            outdir,
            file_path
        ]    
    
    elif ext == ".ppt":
        converted_file = os.path.join(outdir, file_name + ".pdf")
        cmd = [
            "libreoffice",
            "--headless",
            f'--infilter="MS PowerPoint 97"',
            '--convert-to',
            f'pdf:impress_pdf_Export',
            '--outdir',
            outdir,
            file_path
        ]
    elif ext == ".pptx":
        converted_file = os.path.join(outdir, file_name + ".pdf")
        cmd = [
            "libreoffice",
            "--headless",
            f'--infilter="Impress MS PowerPoint 2007 XML"',
            '--convert-to',
            f'pdf:impress_pdf_Export',
            '--outdir',
            outdir,
            file_path
        ]
    elif ext == ".txt":
        converted_file = os.path.join(outdir, file_name + ".pdf")
        cmd = [
            "libreoffice",
            "--headless",
            f'--infilter="Text (encoded):UTF8,LF,Liberation Mono,en-US"',
            '--convert-to',
            f'pdf:writer_pdf_Export',
            '--outdir',
            outdir,
            file_path
        ]
    elif ext == ".pdf":
        converted_file = os.path.join(outdir, file_name + ".pdf")
        cmd = [
            "libreoffice",
            "--headless",
            f'--infilter="draw_pdf_addstream_import"',
            '--convert-to',
            f'pdf:writer_pdf_Export',
            '--outdir',
            outdir,
            file_path
        ]
    else:
        return file_path

    res = subprocess.run(cmd, shell=False, check=True)
    if res.returncode == 0:
        shutil.move(converted_file, file_path)
    return file_path




def get_timestamp():
    return datetime.strftime(datetime.now(), "%Y-%m-%d")


def mkdir(dir_path):
    Path(dir_path).mkdir(parents=True, exist_ok=True)


def read_txt(txt_path: str) -> List:
    if not isinstance(txt_path, str):
        txt_path = str(txt_path)

    with open(txt_path, "r", encoding="utf-8") as f:
        data = list(map(lambda x: x.rstrip("\n"), f))
    return data


def write_txt(save_path: Union[str, Path], content: list, mode: str = "w"):
    if not isinstance(save_path, str):
        save_path = str(save_path)

    if not isinstance(content, list):
        content = [content]

    with open(save_path, mode, encoding="utf-8") as f:
        for value in content:
            f.write(f"{value}\n")


def is_contain(
    sentence: str,
    key_words: Union[str, List],
) -> bool:
    """sentences中是否包含key_words中任意一个"""
    return any(i in sentence for i in key_words)


if __name__ == "__main__":
    # img_path = 'data/images/air_ticket.jpg'

    # result, elapse = rapid_ocr(img_path)
    # for rs in result:
    #     print(rs[1])
        
    # contents = transcribe_video_audio("data/docs/dbee6f4689dbd235e2e2e3c415a3cfd2.mp4")
    # docs = split_text2(contents)
    # for doc in docs:
    #     print(doc)
    #     print("="*20)    
    # print(update("data/docs/xx.docx"))
    # print(update("data/docs/tt.xls"))
    # print(update("data/docs/pp.doc"))
    # print(update("data/docs/yyy.ppt"))
    # print(update("data/docs/xx.pdf"))
    # print(update("data/docs/yy.pdf"))

    img_path = '/workspace/tensorrt_llm/trtocr/test_imgs/full.png'
    color_img=np.array(cv2.imread(img_path))
    result,_= rapid_ocr(color_img)
    datas = ""
    if result:
        ocr_result = [line[1] for line in result]
        datas += "".join(ocr_result)
    print(datas)
    
    seals, hws, _ = rapid_hwseal(color_img)
    print(seals, hws)