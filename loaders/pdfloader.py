from typing import List
from loaders.detect import rapid_ocr, rapid_ocrm,rapid_hwseal, find_time, img23
import numpy as np
from pathlib import Path
from tqdm import tqdm
import fitz
import copy
import re
import textwrap
import os
from PIL import Image
from sumary import get_normal
from utils.log import log
import rec

def to_newline(meta_txt):
    for index, block_txt in enumerate(meta_txt):
        if len(block_txt) < 100:
            meta_txt[index] = '\n'
    return meta_txt


def rm_empty(meta_txt):
    for index in reversed(range(1, len(meta_txt))):
        if meta_txt[index] == '\n' and meta_txt[index-1] == '\n':
            meta_txt.pop(index)
    return meta_txt

def merge_small(meta_txt):
    def starts_with_lowercase_word(s):
        pattern = r"^[a-z]+"
        match = re.match(pattern, s)
        if match:
            return True
        else:
            return False
    # 对于某些PDF会有第一个段落就以小写字母开头,为了避免索引错误将其更改为大写
    if starts_with_lowercase_word(meta_txt[0]):
        meta_txt[0] = meta_txt[0].capitalize()
    for _ in range(100):
        for index, block_txt in enumerate(meta_txt):
            if starts_with_lowercase_word(block_txt):
                if meta_txt[index-1] != '\n':
                    meta_txt[index-1] += ' '
                else:
                    meta_txt[index-1] = ''
                meta_txt[index-1] += meta_txt[index]
                meta_txt[index] = '\n'
    return meta_txt


def primary_ffsize(l):
    """
    提取文本块主字体
    """
    fsize_statiscs = {}
    for wtf in l['spans']:
        if wtf['size'] not in fsize_statiscs: fsize_statiscs[wtf['size']] = 0
        fsize_statiscs[wtf['size']] += len(wtf['text'])
    return max(fsize_statiscs, key=fsize_statiscs.get)

def ffsize_same(a,b):
    """
    提取字体大小是否近似相等
    """
    return abs((a-b)/max(a,b)) < 0.02



def get_text(pdf_path):
    dpi=300
    # 获取PDF文件的目录和文件名（不包括扩展名）
    pdf_dir = os.path.dirname(pdf_path)
    pdf_filename = os.path.splitext(os.path.basename(pdf_path))[0]
    
    # 创建输出文件夹
    output_folder = os.path.join(pdf_dir, pdf_filename)
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    ocr_text = ""
    with fitz.open(pdf_path) as doc:
        for page_num, page in enumerate(doc):  
            # 设置缩放因子，这里使用dpi来确定
            zoom = dpi / 72  # 72是PDF的默认DPI
            mat = fitz.Matrix(zoom, zoom)

            # 获取页面的像素图（pix）
            pix = page.get_pixmap(matrix=mat)
            img_array = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, -1)
            img_array = img23(img_array)
            result = rec.rapid_ocr(img_array)
            page_data = ""
            if result:
                ocr_result = [box[1] for box in result]
                page_data += "\n".join(ocr_result)
            ocr_text += page_data +"\n"
    return ocr_text

class ExtractPDF:
    def __init__(self, ocr = True, seal = True) -> None:
        self.ocr = ocr
        self.seal = seal
        
    def pdf2textocr(self, filepath):
        try:
            dpi=300
            zoom = dpi / 72  # 72是PDF的默认DPI
            with fitz.open(filepath) as doc:
                pages = []
                for page_num, page in enumerate(doc):  
                    page_data = ""
                    try:
                        mat = fitz.Matrix(zoom, zoom)
                        pix = page.get_pixmap(matrix=mat)
                        img_array = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, -1)
                        img_array = img23(img_array)
                        result = rec.rapid_ocr(img_array)
                        if result:
                            ocr_result = [box[1] for box in result]
                            page_data += "\n".join(ocr_result)
                        img_list = page.get_images()
                    except Exception as e:
                        print(f"Error loading page {page_num}: {e}")
                        continue
                    
                    seals = []
                    hws = []
                    for img in img_list:
                        try:
                            pix = fitz.Pixmap(doc, img[0])
                            img_array = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, -1)
                            img_array = img23(img_array)                            
                            if self.seal:
                                seal, hw, _ = rapid_hwseal(img_array)
                                seals.extend(seal)
                                hws.extend(hw)
                        except Exception as e:
                            print(f"Error processing image: {e}")
                            continue
                    log.warning(page_data)    
                    page_data = get_normal(page_data)
                    log.info(page_data)
                    dates = find_time(page_data) 
                    
                    content = {
                        "id": os.path.basename(filepath),
                        "pageNo": page_num+1,
                        "pageContent": page_data,
                        "sealContent": seals,
                        "handWriteContent": hws,
                        "dateContent": dates
                    }
                    pages.append(content)
                
                return pages
        
        except Exception as e:
            log.error(f"An error occurred while processing {filepath}: {e}")
            return []

    def __call__(self, filepath) -> List:
        contents =  self.pdf2textocr(filepath)
        # contents = self.pdf2text2(filepath)
        return contents


    def pdf2text(self, filepath):
        try:
            doc = fitz.open(filepath)
            pages = []
            for page_num in tqdm(range(len(doc)), desc=f"Converting {filepath} pages:"):
                page_data = ""
                try:
                    page = doc.load_page(page_num)
                    text = page.get_text("text")
                    page_data += text
                    img_list = page.get_images()
                except Exception as e:
                    print(f"Error loading page {page_num}: {e}")
                    continue
                
                seals = []
                hws = []
                for img in img_list:
                    try:
                        pix = fitz.Pixmap(doc, img[0])
                        img_array = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, -1)
                        img_array = img23(img_array)
                        
                        if self.ocr:
                            result, _ = rapid_ocr(img_array)
                            if result:
                                ocr_result = [box[1] for box in result]
                                page_data += " ".join(ocr_result)
                        
                        if self.seal:
                            seal, hw, _ = rapid_hwseal(img_array)
                            seals.extend(seal)
                            hws.extend(hw)
                    except Exception as e:
                        print(f"Error processing image: {e}")
                        continue
                log.warning(page_data)    
                page_data = get_normal(page_data)
                log.info(page_data)
                dates = find_time(page_data) 
                
                content = {
                    "id": os.path.basename(filepath),
                    "pageNo": page_num+1,
                    "pageContent": textwrap.wrap(page_data, width=60),
                    "sealContent": seals,
                    "handWriteContent": hws,
                    "dateContent": dates
                }
                pages.append(content)
            
            return pages
        
        except Exception as e:
            log.error(f"An error occurred while processing {filepath}: {e}")
            return []   
    
    def pdf2text2(self, fp):
        fc = 0  # Index 0 文本
        fs = 1  # Index 1 字体
        fb = 2  # Index 2 框框
        REMOVE_FOOT_NOTE = True # 是否丢弃掉 不是正文的内容 （比正文字体小，如参考文献、脚注、图注等）
        REMOVE_FOOT_FFSIZE_PERCENT = 0.95 # 小于正文的？时，判定为不是正文（有些文章的正文部分字体大小不是100%统一的，有肉眼不可见的小变化）

        datas = ""
        with fitz.open(fp) as doc:
            ############################## <第 1 步，搜集初始信息> ##################################
            for index, page in enumerate(doc):
                meta_txt = []
                meta_font = []

                meta_line = []
                meta_span = []
                # file_content += page.get_text()
                text_areas = page.get_text("dict")  # 获取页面上的文本信息
                for t in text_areas['blocks']:
                    if 'lines' in t:
                        pf = 998
                        for l in t['lines']:
                            txt_line = "".join([wtf['text'] for wtf in l['spans']])
                            if len(txt_line) == 0: continue
                            pf = primary_ffsize(l)
                            meta_line.append([txt_line, pf, l['bbox'], l])
                            for wtf in l['spans']: # for l in t['lines']:
                                meta_span.append([wtf['text'], wtf['size'], len(wtf['text'])])
                        # meta_line.append(["NEW_BLOCK", pf])
                # 块元提取                           for each word segment with in line                       for each line         cross-line words                          for each block
                meta_txt.extend([" ".join(["".join([wtf['text'] for wtf in l['spans']]) for l in t['lines']]).replace(
                    '- ', '') for t in text_areas['blocks'] if 'lines' in t])
                meta_font.extend([np.mean([np.mean([wtf['size'] for wtf in l['spans']])
                                for l in t['lines']]) for t in text_areas['blocks'] if 'lines' in t])

                ############################## <第 2 步，获取正文主字体> ##################################
                try:
                    fsize_statiscs = {}
                    for span in meta_span:
                        if span[1] not in fsize_statiscs: fsize_statiscs[span[1]] = 0
                        fsize_statiscs[span[1]] += span[2]
                    if not fsize_statiscs:
                        main_fsize = max([line[fs] for line in meta_line])
                    else:
                        main_fsize = max(fsize_statiscs, key=fsize_statiscs.get)
                    
                    if REMOVE_FOOT_NOTE:
                        give_up_fize_threshold = main_fsize * REMOVE_FOOT_FFSIZE_PERCENT
                except Exception as e:
                    exception_type = type(e).__name__ 
                    exception_reason = str(e)
                    print(f"捕获到异常：{exception_type}，原因：{exception_reason}")
                    raise RuntimeError(f'抱歉, 我们暂时无法解析此PDF文档: {fp}。')
                ############################## <第 3 步，切分和重新整合> ##################################
                mega_sec = []
                sec = []
                for index, line in enumerate(meta_line):
                    if index == 0:
                        if meta_line[index][fs] > give_up_fize_threshold:
                            sec.append(line[fc])
                        continue
                    if REMOVE_FOOT_NOTE:
                        if meta_line[index][fs] <= give_up_fize_threshold:
                            continue
                    if ffsize_same(meta_line[index][fs], meta_line[index-1][fs]):
                        # 尝试识别段落
                        if meta_line[index][fc].endswith('.') and\
                            (meta_line[index-1][fc] != 'NEW_BLOCK') and \
                            (meta_line[index][fb][2] - meta_line[index][fb][0]) < (meta_line[index-1][fb][2] - meta_line[index-1][fb][0]) * 0.7:
                            sec[-1] += line[fc]
                            sec[-1] += "\n\n"
                        else:
                            sec[-1] += "\n"
                            sec[-1] += line[fc]
                    else:
                        if (index+1 < len(meta_line)) and \
                            meta_line[index][fs] > main_fsize:
                            # 单行 + 字体大
                            mega_sec.append(copy.deepcopy(sec))
                            sec = []
                            sec.append("# " + line[fc])
                        else:
                            # 尝试识别section
                            if meta_line[index-1][fs] > meta_line[index][fs]:
                                sec.append("\n" + line[fc])
                            else:
                                sec.append(line[fc])
                mega_sec.append(copy.deepcopy(sec))

                finals = []
                for ms in mega_sec:
                    final = " ".join(ms)
                    final = final.replace('- ', ' ')
                    finals.append(final)
                meta_txt = finals

                ############################## <第 4 步，乱七八糟的后处理> ##################################
                meta_txt = to_newline(meta_txt)
                meta_txt = rm_empty(meta_txt)
                meta_txt = merge_small(meta_txt)
                meta_txt = rm_empty(meta_txt)

                meta_txt = '\n'.join(meta_txt)
                # 清除重复的换行
                for _ in range(5):
                    meta_txt = meta_txt.replace('\n\n', '\n')
                # 换行 -> 双换行
                meta_txt = meta_txt.replace("\n \n", "")
                datas += meta_txt
                img_list = page.get_images()
                for img in img_list:
                    pix = fitz.Pixmap(doc, img[0])
                    img_array = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, -1)
                    result, _ = rapid_ocr(img_array)
                    if result:
                        ocr_result = [line[1] for line in result]
                        if len(ocr_result) > 20:
                            datas += "\n".join(ocr_result)
        # datas = datas.replace('\n', '')
        return datas