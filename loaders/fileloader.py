import os
import sys
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(current_dir)
import argparse
import filetype
from typing import Union
from pathlib import Path
from .xlsLoader import ExtractExcel
from .pptloader import ExtractPPT
from .docloader import ExtractWord
from .imgloader import ExtractImg
from .pdfloader import ExtractPDF
# from .normal import ExtractMD, ExtractTXT

# 简单的替代类
class ExtractMD:
    def __call__(self, filepath):
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except:
            return ""

class ExtractTXT:
    def __call__(self, filepath):
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except:
            return ""
class FileLoader:
    def __init__(self, ocr=True, seal=True) -> None:
        self.excel = ExtractExcel()
        self.ppt = ExtractPPT()
        self.word = ExtractWord()
        self.img = ExtractImg(ocr, seal)
        self.pdf = ExtractPDF(ocr, seal)
        self.md = ExtractMD()
        self.txt = ExtractTXT()
        
        self.doc_suffix = ["doc", "docx"]
        self.excel_suffix = ["xls", "xlsx"]
        self.ppt_suffix = ["ppt", "pptx"]
        self.img_suffix = ["jpg", "png", "jpeg","bmp"]
        self.pdf_suffix = ["pdf"]
        self.md_suffix = ["md"]
        self.txt_suffix = ["txt"]
        
        
    def __call__(self, file_content: Union[Path, str], save_img_dir: str = None):
        file_content = str(file_content)

        if not file_content:
            raise Exception(f"{file_content} must be Path or str.")

        file_type = self.which_type(file_content)
        ext = os.path.splitext(file_content)[1].replace(".","")
        all_suffix = self.doc_suffix + self.excel_suffix + self.ppt_suffix + self.img_suffix+ self.pdf_suffix  + self.md_suffix + self.txt_suffix
        if file_type not in all_suffix:
            raise Exception(f"{file_type} must in {all_suffix}")

        pages = []
        if ext in self.img_suffix:
            pages =   self.img(file_content)
        elif file_type in self.pdf_suffix:
            pages =   self.pdf(file_content)
        return pages

    def which_type(self, file_content: Union[str, Path]):
        if isinstance(file_content, str):
            guessed_type = filetype.guess(file_content)
            if guessed_type is not None:
                return guessed_type.extension
            else:
                file_extension = Path(file_content).suffix
                return file_extension.lstrip(".")   
        if isinstance(file_content, bytes):
            with open(file_content, "rb") as f:
                data = f.read()
            return filetype.guess(data).extension
        return None


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("file_path", type=str)
    parser.add_argument(
        "-img_dir",
        "--save_img_dir",
        type=str,
    )
    args = parser.parse_args()

    extracter = FileLoader()
    if Path(args.file_path).is_dir():
        file_list = list(Path(args.file_path).iterdir())
    else:
        file_list = [args.file_path]

    for file_one in file_list:
        res = extracter(str(file_one))
        print(res)


if __name__ == "__main__":
    main()
