#!/usr/bin/env python3
"""
批量结构化信息提取器
基于ES检索结果进行高效的批量AI调用和信息合并
"""

import asyncio
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from collections import defaultdict

from utils.llm import LLM
from utils.smart_retriever import SmartRetriever, SearchFilter
from utils.log import log
from utils.config import config


@dataclass
class ExtractionTemplate:
    """提取模板"""
    name: str
    fields: Dict[str, str]  # 字段名 -> 字段描述
    prompt_template: str
    merge_strategy: str = "llm"  # "llm", "rule", "hybrid"


class BatchExtractor:
    """批量结构化信息提取器"""
    
    def __init__(self):
        self.llm = LLM()
        self.retriever = SmartRetriever()
        
        # 批量处理配置
        self.batch_size = 5  # 每批处理的文档数量
        self.max_concurrent = 3  # 最大并发数
        self.chunk_limit = 10  # 每个文档最多使用的chunk数量
        
        # 预定义提取模板
        self.templates = self._load_extraction_templates()
        
        # 缓存机制
        self.extraction_cache = {}
    
    def _load_extraction_templates(self) -> Dict[str, ExtractionTemplate]:
        """加载提取模板"""
        templates = {}
        
        # 项目档案模板
        templates["项目档案"] = ExtractionTemplate(
            name="项目档案",
            fields={
                "name": "项目名称",
                "date": "项目时间",
                "responsible_unit": "项目承担单位",
                "summary": "项目内容摘要"
            },
            prompt_template="""
基于以下文档内容，提取项目档案的结构化信息：

{content}

请提取以下信息（如果文档中没有相关信息，请填写"未提及"）：
1. 项目名称：项目的正式名称
2. 项目时间：项目的开始时间或相关时间
3. 项目承担单位：负责项目的单位或组织
4. 项目内容摘要：项目的主要内容和目标

请以JSON格式返回结果：
{{"name": "项目名称", "date": "项目时间", "responsible_unit": "承担单位", "summary": "内容摘要"}}
""",
            merge_strategy="llm"
        )
        
        # 文书档案模板
        templates["文书档案"] = ExtractionTemplate(
            name="文书档案",
            fields={
                "name": "文书名称",
                "date": "文书时间",
                "type": "文书类型",
                "organizer": "发起组织",
                "participants": "参与者",
                "summary": "文书摘要"
            },
            prompt_template="""
基于以下文档内容，提取文书档案的结构化信息：

{content}

请提取以下信息（如果文档中没有相关信息，请填写"未提及"）：
1. 文书名称：文书的正式名称或标题
2. 文书时间：文书的发起时间或相关时间
3. 文书类型：文书的类型或性质
4. 发起组织：发起该文书的组织或部门
5. 参与者：参与该文书的组织和个人
6. 文书摘要：文书的主要内容和要点

请以JSON格式返回结果：
{{"name": "文书名称", "date": "文书时间", "type": "文书类型", "organizer": "发起组织", "participants": "参与者", "summary": "文书摘要"}}
""",
            merge_strategy="llm"
        )
        
        return templates
    
    async def extract_project_info(
        self,
        project_name: str,
        action: str,
        urls: Optional[List[str]] = None,
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """
        提取项目结构化信息
        
        Args:
            project_name: 项目名称
            action: 文档类型（"项目档案" 或 "文书档案"）
            urls: 可选的文档URL列表，如果不提供则从ES中检索所有相关文档
            force_refresh: 是否强制刷新缓存
            
        Returns:
            提取的结构化信息
        """
        cache_key = f"{project_name}_{action}"
        
        # 检查缓存
        if not force_refresh and cache_key in self.extraction_cache:
            log.info(f"使用缓存的提取结果: {cache_key}")
            return self.extraction_cache[cache_key]
        
        try:
            log.info(f"开始批量提取项目信息: {project_name}, 类型: {action}")
            
            # 1. 获取相关文档内容
            if urls:
                # 基于指定URL检索
                relevant_chunks = await self._retrieve_chunks_by_urls(urls, project_name)
            else:
                # 从ES中检索所有相关内容
                relevant_chunks = await self._retrieve_project_chunks(project_name, action)
            
            if not relevant_chunks:
                log.warning(f"未找到相关文档内容: {project_name}")
                return {"error": "未找到相关文档内容"}
            
            # 2. 按文档分组
            doc_groups = self._group_chunks_by_document(relevant_chunks)
            
            # 3. 批量提取每个文档的信息
            doc_extractions = await self._batch_extract_documents(doc_groups, action)
            
            # 4. 合并所有文档的提取结果
            merged_result = await self._merge_extractions(doc_extractions, action, project_name)
            
            # 5. 缓存结果
            self.extraction_cache[cache_key] = merged_result
            
            log.info(f"批量提取完成: {project_name}, 提取文档数: {len(doc_extractions)}")
            return merged_result
            
        except Exception as e:
            log.error(f"批量提取失败: {e}")
            return {"error": str(e)}
    
    async def _retrieve_chunks_by_urls(
        self,
        urls: List[str],
        project_name: str
    ) -> List[Dict[str, Any]]:
        """基于URL列表检索相关chunks"""
        all_chunks = []
        
        for url in urls:
            # 构建搜索过滤器
            search_filter = SearchFilter(
                project_name=project_name,
                sources=[url]
            )
            
            # 检索该URL的所有chunks
            results = await self.retriever.smart_search(
                query="",  # 空查询，获取所有chunks
                search_filter=search_filter,
                strategy="keyword",
                top_k=self.chunk_limit
            )
            
            for result in results:
                all_chunks.append({
                    "content": result.content,
                    "doc_id": result.doc_id,
                    "source": result.source,
                    "doc_types": result.doc_types,
                    "chunk_index": result.chunk_index,
                    "score": result.score
                })
        
        return all_chunks
    
    async def _retrieve_project_chunks(
        self,
        project_name: str,
        action: str
    ) -> List[Dict[str, Any]]:
        """检索项目相关的所有chunks"""
        
        # 构建搜索过滤器
        search_filter = SearchFilter(project_name=project_name)
        
        # 使用项目名称作为查询
        results = await self.retriever.smart_search(
            query=project_name,
            search_filter=search_filter,
            strategy="hybrid",
            top_k=100  # 获取更多结果
        )
        
        chunks = []
        for result in results:
            chunks.append({
                "content": result.content,
                "doc_id": result.doc_id,
                "source": result.source,
                "doc_types": result.doc_types,
                "chunk_index": result.chunk_index,
                "score": result.score
            })
        
        return chunks
    
    def _group_chunks_by_document(
        self,
        chunks: List[Dict[str, Any]]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """按文档分组chunks"""
        doc_groups = defaultdict(list)
        
        for chunk in chunks:
            doc_id = chunk.get("doc_id", "unknown")
            doc_groups[doc_id].append(chunk)
        
        # 按chunk_index排序
        for doc_id in doc_groups:
            doc_groups[doc_id].sort(key=lambda x: x.get("chunk_index", 0))
        
        return dict(doc_groups)
    
    async def _batch_extract_documents(
        self,
        doc_groups: Dict[str, List[Dict[str, Any]]],
        action: str
    ) -> List[Dict[str, Any]]:
        """批量提取文档信息"""
        
        if action not in self.templates:
            raise ValueError(f"不支持的文档类型: {action}")
        
        template = self.templates[action]
        
        # 准备批量提取任务
        extract_tasks = []
        for doc_id, chunks in doc_groups.items():
            task = self._extract_single_document(doc_id, chunks, template)
            extract_tasks.append(task)
        
        # 分批并发执行
        results = []
        for i in range(0, len(extract_tasks), self.batch_size):
            batch_tasks = extract_tasks[i:i + self.batch_size]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, Exception):
                    log.error(f"文档提取失败: {result}")
                elif result:
                    results.append(result)
        
        return results
    
    async def _extract_single_document(
        self,
        doc_id: str,
        chunks: List[Dict[str, Any]],
        template: ExtractionTemplate
    ) -> Optional[Dict[str, Any]]:
        """提取单个文档的信息"""
        try:
            # 合并chunks内容
            combined_content = "\n\n".join([
                chunk["content"] for chunk in chunks[:self.chunk_limit]
            ])
            
            if not combined_content.strip():
                return None
            
            # 构建提取提示词
            prompt = template.prompt_template.format(content=combined_content)
            
            # 调用LLM提取
            response = await self.llm.chat([
                {
                    "role": "system",
                    "content": "你是一个信息提取助手。请严格按照要求提取信息并以JSON格式返回。"
                },
                {"role": "user", "content": prompt}
            ])
            
            # 解析JSON结果
            extracted_info = self._parse_extraction_result(response, template.fields)
            
            if extracted_info:
                extracted_info["doc_id"] = doc_id
                extracted_info["source"] = chunks[0].get("source", "") if chunks else ""
                extracted_info["extraction_time"] = datetime.now().isoformat()
                
            return extracted_info
            
        except Exception as e:
            log.error(f"单文档提取失败 {doc_id}: {e}")
            return None
    
    def _parse_extraction_result(
        self,
        response: str,
        expected_fields: Dict[str, str]
    ) -> Optional[Dict[str, Any]]:
        """解析提取结果"""
        try:
            # 尝试直接解析JSON
            if response.strip().startswith('{') and response.strip().endswith('}'):
                result = json.loads(response.strip())
                
                # 验证字段
                validated_result = {}
                for field in expected_fields:
                    value = result.get(field, "未提及")
                    validated_result[field] = value if value and value.strip() else "未提及"
                
                return validated_result
            
            # 如果不是标准JSON，尝试提取JSON部分
            json_match = re.search(r'\{[^}]+\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)
                
                validated_result = {}
                for field in expected_fields:
                    value = result.get(field, "未提及")
                    validated_result[field] = value if value and value.strip() else "未提及"
                
                return validated_result
            
            return None
            
        except Exception as e:
            log.error(f"解析提取结果失败: {e}")
            return None
    
    async def _merge_extractions(
        self,
        extractions: List[Dict[str, Any]],
        action: str,
        project_name: str
    ) -> Dict[str, Any]:
        """合并多个文档的提取结果"""
        if not extractions:
            return {"error": "没有有效的提取结果"}
        
        if len(extractions) == 1:
            result = extractions[0].copy()
            result["project_name"] = project_name
            return result
        
        template = self.templates[action]
        
        if template.merge_strategy == "llm":
            return await self._llm_merge_extractions(extractions, template, project_name)
        elif template.merge_strategy == "rule":
            return self._rule_merge_extractions(extractions, template, project_name)
        else:
            return await self._hybrid_merge_extractions(extractions, template, project_name)
    
    async def _llm_merge_extractions(
        self,
        extractions: List[Dict[str, Any]],
        template: ExtractionTemplate,
        project_name: str
    ) -> Dict[str, Any]:
        """使用LLM合并提取结果"""
        try:
            # 构建合并提示词
            merge_prompt = self._build_merge_prompt(extractions, template, project_name)
            
            response = await self.llm.chat([
                {
                    "role": "system",
                    "content": "你是一个信息合并助手。请合并多个文档的提取结果，选择最准确和完整的信息。"
                },
                {"role": "user", "content": merge_prompt}
            ])
            
            # 解析合并结果
            merged_result = self._parse_extraction_result(response, template.fields)
            
            if merged_result:
                merged_result["project_name"] = project_name
                merged_result["merged_from"] = len(extractions)
                merged_result["merge_time"] = datetime.now().isoformat()
                
            return merged_result or {"error": "合并失败"}
            
        except Exception as e:
            log.error(f"LLM合并失败: {e}")
            return self._rule_merge_extractions(extractions, template, project_name)
    
    def _build_merge_prompt(
        self,
        extractions: List[Dict[str, Any]],
        template: ExtractionTemplate,
        project_name: str
    ) -> str:
        """构建合并提示词"""
        prompt_parts = [
            f"项目名称: {project_name}",
            f"文档类型: {template.name}",
            "",
            "以下是从多个文档中提取的信息，请合并成一个最准确和完整的结果：",
            ""
        ]
        
        for i, extraction in enumerate(extractions, 1):
            prompt_parts.append(f"文档{i}提取结果：")
            for field, value in extraction.items():
                if field not in ["doc_id", "source", "extraction_time"]:
                    prompt_parts.append(f"  {field}: {value}")
            prompt_parts.append("")
        
        prompt_parts.extend([
            "合并规则：",
            "1. 选择最完整和准确的信息",
            "2. 如果多个文档有不同信息，选择最详细的",
            "3. 如果信息冲突，选择最可信的",
            "4. 保持信息的一致性",
            "",
            f"请以JSON格式返回合并后的结果，包含以下字段：{list(template.fields.keys())}"
        ])
        
        return "\n".join(prompt_parts)
    
    def _rule_merge_extractions(
        self,
        extractions: List[Dict[str, Any]],
        template: ExtractionTemplate,
        project_name: str
    ) -> Dict[str, Any]:
        """使用规则合并提取结果"""
        merged_result = {"project_name": project_name}
        
        for field in template.fields:
            values = []
            for extraction in extractions:
                value = extraction.get(field, "未提及")
                if value and value != "未提及" and value.strip():
                    values.append(value.strip())
            
            if values:
                # 选择最长的值（通常更完整）
                merged_result[field] = max(values, key=len)
            else:
                merged_result[field] = "未提及"
        
        merged_result["merged_from"] = len(extractions)
        merged_result["merge_time"] = datetime.now().isoformat()
        
        return merged_result
    
    async def _hybrid_merge_extractions(
        self,
        extractions: List[Dict[str, Any]],
        template: ExtractionTemplate,
        project_name: str
    ) -> Dict[str, Any]:
        """混合策略合并提取结果"""
        # 先使用规则合并
        rule_result = self._rule_merge_extractions(extractions, template, project_name)
        
        # 如果规则合并结果不够好，使用LLM合并
        incomplete_fields = [
            field for field, value in rule_result.items()
            if value == "未提及" and field in template.fields
        ]
        
        if len(incomplete_fields) > len(template.fields) * 0.3:  # 如果超过30%字段未提及
            return await self._llm_merge_extractions(extractions, template, project_name)
        else:
            return rule_result
    
    def clear_cache(self, project_name: Optional[str] = None):
        """清除缓存"""
        if project_name:
            # 清除特定项目的缓存
            keys_to_remove = [key for key in self.extraction_cache.keys() if key.startswith(project_name)]
            for key in keys_to_remove:
                del self.extraction_cache[key]
        else:
            # 清除所有缓存
            self.extraction_cache.clear()
        
        log.info(f"缓存已清除: {project_name or 'all'}")
