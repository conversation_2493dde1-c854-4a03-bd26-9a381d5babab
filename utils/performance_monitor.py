#!/usr/bin/env python3
"""
性能监控器
实时监控系统性能并提供优化建议
"""

import time
import psutil
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque

from utils.log import log
from utils.config import config


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        # 监控配置
        self.enable_real_time_monitoring = config.get("performance_monitor.real_time", True)
        self.monitoring_interval = config.get("performance_monitor.interval", 30)  # 秒
        self.history_retention_hours = config.get("performance_monitor.retention_hours", 24)
        
        # 性能阈值
        self.performance_thresholds = {
            "cpu_usage": 80.0,           # CPU使用率阈值
            "memory_usage": 85.0,        # 内存使用率阈值
            "processing_time": 120.0,    # 处理时间阈值（秒）
            "response_time": 5.0,        # 响应时间阈值（秒）
            "error_rate": 5.0,           # 错误率阈值（%）
            "concurrent_requests": 10    # 并发请求数阈值
        }
        
        # 性能数据存储
        self.performance_history = defaultdict(lambda: deque(maxlen=1000))
        self.current_metrics = {}
        self.active_requests = {}
        
        # 监控状态
        self._monitoring_task = None
        self._initialized = False
    
    async def initialize(self):
        """初始化性能监控器"""
        if not self._initialized:
            # 启动实时监控
            if self.enable_real_time_monitoring:
                await self._start_real_time_monitoring()
            
            self._initialized = True
            log.info("性能监控器初始化完成")
    
    async def _start_real_time_monitoring(self):
        """启动实时监控"""
        try:
            if self._monitoring_task is None:
                self._monitoring_task = asyncio.create_task(self._monitoring_loop())
                log.info("实时性能监控已启动")
        except Exception as e:
            log.error(f"启动实时监控失败: {e}")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while True:
            try:
                # 收集系统性能指标
                await self._collect_system_metrics()
                
                # 检查性能阈值
                await self._check_performance_thresholds()
                
                # 清理过期数据
                await self._cleanup_expired_data()
                
                # 等待下次监控
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                log.error(f"性能监控循环异常: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _collect_system_metrics(self):
        """收集系统性能指标"""
        try:
            timestamp = datetime.now()
            
            # CPU使用率
            cpu_usage = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_usage = disk.percent
            
            # 进程信息
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024 / 1024  # MB
            process_cpu = process.cpu_percent()
            
            # 更新当前指标
            self.current_metrics = {
                "timestamp": timestamp.isoformat(),
                "system": {
                    "cpu_usage": round(cpu_usage, 2),
                    "memory_usage": round(memory_usage, 2),
                    "disk_usage": round(disk_usage, 2),
                    "available_memory_mb": round(memory.available / 1024 / 1024, 2)
                },
                "process": {
                    "memory_usage_mb": round(process_memory, 2),
                    "cpu_usage": round(process_cpu, 2)
                },
                "application": {
                    "active_requests": len(self.active_requests),
                    "total_requests_processed": len(self.performance_history["request_times"])
                }
            }
            
            # 保存到历史记录
            self.performance_history["system_metrics"].append({
                "timestamp": timestamp,
                "metrics": self.current_metrics
            })
            
        except Exception as e:
            log.error(f"收集系统指标失败: {e}")
    
    async def _check_performance_thresholds(self):
        """检查性能阈值"""
        try:
            alerts = []
            
            # 检查CPU使用率
            cpu_usage = self.current_metrics.get("system", {}).get("cpu_usage", 0)
            if cpu_usage > self.performance_thresholds["cpu_usage"]:
                alerts.append({
                    "type": "cpu_high",
                    "severity": "warning",
                    "message": f"CPU使用率过高: {cpu_usage}%",
                    "threshold": self.performance_thresholds["cpu_usage"],
                    "current_value": cpu_usage,
                    "timestamp": datetime.now()
                })
            
            # 检查内存使用率
            memory_usage = self.current_metrics.get("system", {}).get("memory_usage", 0)
            if memory_usage > self.performance_thresholds["memory_usage"]:
                alerts.append({
                    "type": "memory_high",
                    "severity": "warning",
                    "message": f"内存使用率过高: {memory_usage}%",
                    "threshold": self.performance_thresholds["memory_usage"],
                    "current_value": memory_usage,
                    "timestamp": datetime.now()
                })
            
            # 检查并发请求数
            active_requests = len(self.active_requests)
            if active_requests > self.performance_thresholds["concurrent_requests"]:
                alerts.append({
                    "type": "concurrent_requests_high",
                    "severity": "warning",
                    "message": f"并发请求数过高: {active_requests}",
                    "threshold": self.performance_thresholds["concurrent_requests"],
                    "current_value": active_requests,
                    "timestamp": datetime.now()
                })
            
            # 记录告警
            if alerts:
                self.performance_history["alerts"].extend(alerts)
                for alert in alerts:
                    log.warning(f"性能告警: {alert['message']}")
            
        except Exception as e:
            log.error(f"检查性能阈值失败: {e}")
    
    async def _cleanup_expired_data(self):
        """清理过期数据"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=self.history_retention_hours)
            
            # 清理系统指标历史
            system_metrics = self.performance_history["system_metrics"]
            while system_metrics and system_metrics[0]["timestamp"] < cutoff_time:
                system_metrics.popleft()
            
            # 清理请求时间历史
            request_times = self.performance_history["request_times"]
            while request_times and request_times[0]["timestamp"] < cutoff_time:
                request_times.popleft()
            
        except Exception as e:
            log.error(f"清理过期数据失败: {e}")
    
    def start_request_tracking(self, request_id: str, request_info: Dict[str, Any]) -> str:
        """开始请求跟踪"""
        try:
            start_time = time.time()
            self.active_requests[request_id] = {
                "start_time": start_time,
                "request_info": request_info,
                "timestamp": datetime.now()
            }
            return request_id
        except Exception as e:
            log.error(f"开始请求跟踪失败: {e}")
            return request_id
    
    def end_request_tracking(
        self,
        request_id: str,
        success: bool = True,
        error_info: Optional[str] = None
    ):
        """结束请求跟踪"""
        try:
            if request_id not in self.active_requests:
                return
            
            request_data = self.active_requests.pop(request_id)
            end_time = time.time()
            processing_time = end_time - request_data["start_time"]
            
            # 记录请求性能
            request_record = {
                "request_id": request_id,
                "timestamp": datetime.now(),
                "processing_time": round(processing_time, 3),
                "success": success,
                "error_info": error_info,
                "request_info": request_data["request_info"]
            }
            
            self.performance_history["request_times"].append(request_record)
            
            # 检查处理时间阈值
            if processing_time > self.performance_thresholds["processing_time"]:
                log.warning(f"请求处理时间过长: {processing_time:.2f}s, 请求ID: {request_id}")
            
        except Exception as e:
            log.error(f"结束请求跟踪失败: {e}")
    
    async def get_performance_report(
        self,
        time_range_hours: int = 1
    ) -> Dict[str, Any]:
        """获取性能报告"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
            
            # 获取时间范围内的数据
            recent_requests = [
                req for req in self.performance_history["request_times"]
                if req["timestamp"] > cutoff_time
            ]
            
            recent_metrics = [
                metric for metric in self.performance_history["system_metrics"]
                if metric["timestamp"] > cutoff_time
            ]
            
            # 计算统计信息
            request_stats = self._calculate_request_statistics(recent_requests)
            system_stats = self._calculate_system_statistics(recent_metrics)
            
            # 生成性能报告
            performance_report = {
                "report_timestamp": datetime.now().isoformat(),
                "time_range_hours": time_range_hours,
                "current_metrics": self.current_metrics,
                "request_statistics": request_stats,
                "system_statistics": system_stats,
                "performance_alerts": self._get_recent_alerts(time_range_hours),
                "optimization_suggestions": await self._generate_optimization_suggestions(
                    request_stats, system_stats
                )
            }
            
            return performance_report
            
        except Exception as e:
            log.error(f"获取性能报告失败: {e}")
            return {"error": str(e)}

    def _calculate_request_statistics(self, requests: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算请求统计信息"""
        if not requests:
            return {"total_requests": 0}

        processing_times = [req["processing_time"] for req in requests]
        successful_requests = [req for req in requests if req["success"]]
        failed_requests = [req for req in requests if not req["success"]]

        return {
            "total_requests": len(requests),
            "successful_requests": len(successful_requests),
            "failed_requests": len(failed_requests),
            "success_rate": round(len(successful_requests) / len(requests) * 100, 2),
            "error_rate": round(len(failed_requests) / len(requests) * 100, 2),
            "processing_time": {
                "average": round(sum(processing_times) / len(processing_times), 3),
                "min": round(min(processing_times), 3),
                "max": round(max(processing_times), 3),
                "p95": round(self._calculate_percentile(processing_times, 95), 3),
                "p99": round(self._calculate_percentile(processing_times, 99), 3)
            },
            "requests_per_hour": round(len(requests), 2)
        }

    def _calculate_system_statistics(self, metrics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算系统统计信息"""
        if not metrics:
            return {"data_points": 0}

        cpu_usages = [m["metrics"]["system"]["cpu_usage"] for m in metrics]
        memory_usages = [m["metrics"]["system"]["memory_usage"] for m in metrics]

        return {
            "data_points": len(metrics),
            "cpu_usage": {
                "average": round(sum(cpu_usages) / len(cpu_usages), 2),
                "min": round(min(cpu_usages), 2),
                "max": round(max(cpu_usages), 2)
            },
            "memory_usage": {
                "average": round(sum(memory_usages) / len(memory_usages), 2),
                "min": round(min(memory_usages), 2),
                "max": round(max(memory_usages), 2)
            }
        }

    def _calculate_percentile(self, values: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not values:
            return 0.0

        sorted_values = sorted(values)
        index = int(len(sorted_values) * percentile / 100)
        return sorted_values[min(index, len(sorted_values) - 1)]

    def _get_recent_alerts(self, hours: int) -> List[Dict[str, Any]]:
        """获取最近的告警"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        recent_alerts = []
        for alert in self.performance_history["alerts"]:
            if alert.get("timestamp", datetime.now()) > cutoff_time:
                recent_alerts.append(alert)

        return recent_alerts

    async def _generate_optimization_suggestions(
        self,
        request_stats: Dict[str, Any],
        system_stats: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成优化建议"""
        suggestions = []

        try:
            # 基于请求统计的建议
            if request_stats.get("total_requests", 0) > 0:
                avg_processing_time = request_stats.get("processing_time", {}).get("average", 0)
                error_rate = request_stats.get("error_rate", 0)

                if avg_processing_time > 60:
                    suggestions.append({
                        "type": "processing_time",
                        "priority": "high",
                        "suggestion": "平均处理时间过长，建议优化处理流程",
                        "current_value": avg_processing_time,
                        "actions": [
                            "启用并行处理",
                            "优化数据库查询",
                            "增加缓存机制",
                            "减少LLM调用次数"
                        ]
                    })

                if error_rate > 5:
                    suggestions.append({
                        "type": "error_rate",
                        "priority": "high",
                        "suggestion": "错误率过高，需要改进错误处理",
                        "current_value": error_rate,
                        "actions": [
                            "增强错误处理机制",
                            "添加重试逻辑",
                            "改进输入验证",
                            "优化异常捕获"
                        ]
                    })

            # 基于系统统计的建议
            if system_stats.get("data_points", 0) > 0:
                avg_cpu = system_stats.get("cpu_usage", {}).get("average", 0)
                avg_memory = system_stats.get("memory_usage", {}).get("average", 0)

                if avg_cpu > 70:
                    suggestions.append({
                        "type": "cpu_usage",
                        "priority": "medium",
                        "suggestion": "CPU使用率较高，建议优化计算密集型操作",
                        "current_value": avg_cpu,
                        "actions": [
                            "优化算法复杂度",
                            "使用异步处理",
                            "减少同步操作",
                            "考虑分布式处理"
                        ]
                    })

                if avg_memory > 80:
                    suggestions.append({
                        "type": "memory_usage",
                        "priority": "medium",
                        "suggestion": "内存使用率较高，建议优化内存管理",
                        "current_value": avg_memory,
                        "actions": [
                            "优化数据结构",
                            "及时释放不用的对象",
                            "使用内存池",
                            "减少内存泄漏"
                        ]
                    })

            return suggestions

        except Exception as e:
            log.error(f"生成优化建议失败: {e}")
            return [{"type": "error", "suggestion": f"生成建议时出错: {e}"}]

    async def get_monitor_stats(self) -> Dict[str, Any]:
        """获取监控器统计信息"""
        try:
            return {
                "monitor_info": {
                    "type": "performance_monitor",
                    "version": "1.0",
                    "initialized": self._initialized,
                    "real_time_monitoring": self.enable_real_time_monitoring,
                    "monitoring_interval": self.monitoring_interval
                },
                "performance_thresholds": self.performance_thresholds,
                "data_statistics": {
                    "system_metrics_count": len(self.performance_history["system_metrics"]),
                    "request_records_count": len(self.performance_history["request_times"]),
                    "active_requests": len(self.active_requests),
                    "total_alerts": len(self.performance_history["alerts"])
                },
                "current_status": self.current_metrics
            }

        except Exception as e:
            log.error(f"获取监控器统计失败: {e}")
            return {"error": str(e)}

    async def stop_monitoring(self):
        """停止监控"""
        try:
            if self._monitoring_task:
                self._monitoring_task.cancel()
                self._monitoring_task = None
                log.info("性能监控已停止")
        except Exception as e:
            log.error(f"停止监控失败: {e}")
