#!/usr/bin/env python3
"""
快速assembly_doc接口实现
基于ES检索和批量处理架构实现快速的结构化抽取
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from utils.log import log

# 内联FastVectorizer实现，避免导入问题
class FastVectorizer:
    """快速向量化处理器（内联实现）"""

    def __init__(self):
        self.chunk_size = 1000
        self.overlap = 100
        self._initialized = False

    async def initialize(self):
        """初始化向量化器"""
        if not self._initialized:
            log.info("快速向量化器初始化完成（内联模式）")
            self._initialized = True

    async def process_documents(self, documents: List[Dict[str, Any]], project_name: str) -> Dict[str, Any]:
        """处理文档列表"""
        try:
            await self.initialize()
            log.info(f"开始处理 {len(documents)} 个文档（内联模式）")

            all_chunks = []
            document_metadata = []

            # 处理每个文档
            for doc_idx, document in enumerate(documents):
                doc_content = document.get("content", "")
                doc_url = document.get("url", f"doc_{doc_idx}")

                if not doc_content.strip():
                    continue

                # 简单分割文档
                chunks = self._split_document(doc_content)

                # 为每个块添加元数据
                for chunk_idx, chunk in enumerate(chunks):
                    chunk_metadata = {
                        "doc_index": doc_idx,
                        "chunk_index": chunk_idx,
                        "doc_url": doc_url,
                        "chunk_text": chunk,
                        "chunk_length": len(chunk)
                    }

                    all_chunks.append(chunk)
                    document_metadata.append(chunk_metadata)

            # 构建结果
            result = {
                "project_name": project_name,
                "total_documents": len(documents),
                "total_chunks": len(all_chunks),
                "chunks": all_chunks,
                "metadata": document_metadata,
                "vectors": None,
                "vectorization_enabled": False,
                "processing_timestamp": datetime.now().isoformat()
            }

            log.info(f"文档处理完成: {len(all_chunks)} 个文本块")
            return result

        except Exception as e:
            log.error(f"文档处理失败: {e}")
            return {"error": str(e)}

    def _split_document(self, content: str) -> List[str]:
        """简单分割文档"""
        if not content or len(content) <= self.chunk_size:
            return [content] if content else []

        chunks = []
        start = 0

        while start < len(content):
            end = start + self.chunk_size

            # 如果不是最后一块，尝试在句子边界处分割
            if end < len(content):
                sentence_end = -1
                for i in range(end, max(start + self.chunk_size // 2, start), -1):
                    if content[i] in '。！？；.!?;':
                        sentence_end = i + 1
                        break

                if sentence_end > start:
                    end = sentence_end

            chunk = content[start:end].strip()
            if chunk:
                chunks.append(chunk)

            start = max(start + 1, end - self.overlap)

            if start >= len(content):
                break

        return chunks
from utils.batch_extractor import BatchExtractor
from utils.structured_storage import StructuredStorage
from utils.task_manager import TaskManager, TaskStatus


@dataclass
class FastAssemblyResult:
    """快速汇编结果"""
    task_id: str
    project_name: str
    action: str
    status: str
    extracted_info: Optional[Dict[str, Any]] = None
    processing_time: float = 0.0
    total_documents: int = 0
    processed_documents: int = 0
    failed_documents: int = 0
    error_message: Optional[str] = None
    created_at: Optional[str] = None
    completed_at: Optional[str] = None


class FastAssemblyProcessor:
    """快速汇编处理器"""
    
    def __init__(self):
        self.vectorizer = FastVectorizer()
        self.extractor = BatchExtractor()
        self.storage = StructuredStorage()
        
        # 初始化标志
        self._initialized = False
    
    async def init(self):
        """初始化处理器"""
        if self._initialized:
            return
        
        try:
            await self.storage.init()
            self._initialized = True
            log.info("快速汇编处理器初始化完成")
        except Exception as e:
            log.error(f"快速汇编处理器初始化失败: {e}")
            raise
    
    async def process_assembly_fast(
        self,
        project_name: str,
        action: str,
        urls: List[str],
        task_id: Optional[str] = None,
        task_manager: Optional[TaskManager] = None,
        force_refresh: bool = False
    ) -> FastAssemblyResult:
        """
        快速处理文档汇编
        
        Args:
            project_name: 项目名称
            action: 文档类型（"项目档案" 或 "文书档案"）
            urls: 文档URL列表
            task_id: 任务ID
            task_manager: 任务管理器
            force_refresh: 是否强制刷新
            
        Returns:
            快速汇编结果
        """
        if not self._initialized:
            await self.init()
        
        task_id = task_id or str(uuid.uuid4())
        start_time = datetime.now()
        
        result = FastAssemblyResult(
            task_id=task_id,
            project_name=project_name,
            action=action,
            status="processing",
            total_documents=len(urls),
            created_at=start_time.isoformat()
        )
        
        try:
            log.info(f"开始快速汇编处理: {project_name}, 类型: {action}, 文档数: {len(urls)}")
            
            # 更新任务状态
            if task_manager:
                await task_manager.update_result(
                    task_id,
                    status=TaskStatus.RUNNING,
                    total_files=len(urls)
                )
            
            # 1. 检查是否需要向量化预处理
            vectorization_needed = await self._check_vectorization_needed(
                project_name, urls, force_refresh
            )
            
            if vectorization_needed:
                # 2. 执行向量化预处理
                await self._update_progress(task_manager, task_id, 10, "正在进行向量化预处理...")
                
                vectorization_stats = await self.vectorizer.vectorize_project(
                    project_name, urls, force_refresh
                )
                
                if vectorization_stats.get("errors"):
                    log.warning(f"向量化过程中出现错误: {vectorization_stats['errors']}")
            
            # 3. 批量提取结构化信息
            await self._update_progress(task_manager, task_id, 40, "正在提取结构化信息...")
            
            extracted_info = await self.extractor.extract_project_info(
                project_name, action, urls, force_refresh
            )
            
            if extracted_info.get("error"):
                raise Exception(f"结构化信息提取失败: {extracted_info['error']}")
            
            # 4. 存储到MySQL
            await self._update_progress(task_manager, task_id, 70, "正在存储结构化信息...")
            
            storage_success = await self._store_extracted_info(
                project_name, action, extracted_info, urls
            )
            
            if not storage_success:
                log.warning("结构化信息存储失败，但提取成功")
            
            # 5. 完成处理
            await self._update_progress(task_manager, task_id, 100, "处理完成")
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            result.status = "completed"
            result.extracted_info = extracted_info
            result.processing_time = processing_time
            result.processed_documents = len(urls)
            result.completed_at = end_time.isoformat()
            
            # 更新任务状态为完成
            if task_manager:
                await task_manager.update_result(
                    task_id,
                    status=TaskStatus.COMPLETED,
                    task_params={
                        "assembly_result": extracted_info,
                        "processing_time": processing_time,
                        "total_documents": len(urls)
                    }
                )
            
            log.info(f"快速汇编处理完成: {project_name}, 耗时: {processing_time:.2f}s")
            return result
            
        except Exception as e:
            log.error(f"快速汇编处理失败: {e}")
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            result.status = "failed"
            result.error_message = str(e)
            result.processing_time = processing_time
            result.completed_at = end_time.isoformat()
            
            # 更新任务状态为失败
            if task_manager:
                await task_manager.update_result(
                    task_id,
                    status=TaskStatus.FAILED,
                    error=str(e)
                )
            
            return result
    
    async def _check_vectorization_needed(
        self,
        project_name: str,
        urls: List[str],
        force_refresh: bool
    ) -> bool:
        """检查是否需要向量化预处理"""
        if force_refresh:
            return True
        
        try:
            # 获取项目向量化统计
            stats = await self.vectorizer.get_vectorization_stats(project_name)
            
            if stats.get("error"):
                return True
            
            # 检查是否有足够的向量化数据
            total_chunks = stats.get("total_chunks", 0)
            total_documents = stats.get("total_documents", 0)
            
            # 如果向量化的文档数少于请求的文档数，需要重新向量化
            if total_documents < len(urls) * 0.8:  # 允许20%的容错
                return True
            
            # 如果chunks数量太少，可能需要重新向量化
            if total_chunks < len(urls) * 5:  # 平均每个文档至少5个chunks
                return True
            
            return False
            
        except Exception as e:
            log.error(f"检查向量化状态失败: {e}")
            return True
    
    async def _update_progress(
        self,
        task_manager: Optional[TaskManager],
        task_id: str,
        progress: int,
        message: str
    ):
        """更新处理进度"""
        if task_manager:
            try:
                await task_manager.update_result(
                    task_id,
                    progress=progress,
                    message=message
                )
            except Exception as e:
                log.error(f"更新进度失败: {e}")
    
    async def _store_extracted_info(
        self,
        project_name: str,
        action: str,
        extracted_info: Dict[str, Any],
        urls: List[str]
    ) -> bool:
        """存储提取的结构化信息"""
        try:
            # 计算置信度分数（基于提取结果的完整性）
            confidence_score = self._calculate_confidence_score(extracted_info, action)
            
            # 准备元数据
            metadata = {
                "extraction_method": "fast_batch",
                "total_urls": len(urls),
                "extraction_time": datetime.now().isoformat(),
                "merged_from": extracted_info.get("merged_from", 1)
            }
            
            if action == "项目档案":
                record_id = await self.storage.store_project_archive(
                    project_name=project_name,
                    extracted_data=extracted_info,
                    source_docs=urls,
                    extraction_method="fast_batch",
                    confidence_score=confidence_score,
                    metadata=metadata
                )
            else:  # 文书档案
                record_id = await self.storage.store_conference_archive(
                    project_name=project_name,
                    extracted_data=extracted_info,
                    source_docs=urls,
                    extraction_method="fast_batch",
                    confidence_score=confidence_score,
                    metadata=metadata
                )
            
            return record_id is not None
            
        except Exception as e:
            log.error(f"存储结构化信息失败: {e}")
            return False
    
    def _calculate_confidence_score(
        self,
        extracted_info: Dict[str, Any],
        action: str
    ) -> float:
        """计算置信度分数"""
        try:
            if action == "项目档案":
                required_fields = ["name", "date", "responsible_unit", "summary"]
            else:  # 文书档案
                required_fields = ["name", "date", "type", "organizer", "participants", "summary"]
            
            filled_fields = 0
            total_fields = len(required_fields)
            
            for field in required_fields:
                value = extracted_info.get(field, "")
                if value and value.strip() and value != "未提及":
                    filled_fields += 1
            
            # 基础分数：字段完整性
            base_score = filled_fields / total_fields
            
            # 调整分数：基于内容质量
            quality_bonus = 0.0
            
            # 如果有合并信息，增加置信度
            if extracted_info.get("merged_from", 1) > 1:
                quality_bonus += 0.1
            
            # 如果内容较长，增加置信度
            summary = extracted_info.get("summary", "")
            if len(summary) > 100:
                quality_bonus += 0.1
            
            final_score = min(1.0, base_score + quality_bonus)
            return round(final_score, 2)
            
        except Exception as e:
            log.error(f"计算置信度分数失败: {e}")
            return 0.5
    
    async def get_cached_result(
        self,
        project_name: str,
        action: str
    ) -> Optional[Dict[str, Any]]:
        """获取缓存的结果"""
        try:
            if action == "项目档案":
                return await self.storage.get_project_archive(project_name)
            else:
                return await self.storage.get_conference_archive(project_name)
        except Exception as e:
            log.error(f"获取缓存结果失败: {e}")
            return None
    
    async def clear_project_cache(self, project_name: str) -> bool:
        """清除项目缓存"""
        try:
            # 清除向量化缓存
            self.vectorizer.clear_project_vectors(project_name)
            
            # 清除提取器缓存
            self.extractor.clear_cache(project_name)
            
            # 清除存储的结构化信息
            await self.storage.delete_project_archives(project_name)
            
            log.info(f"项目缓存已清除: {project_name}")
            return True
            
        except Exception as e:
            log.error(f"清除项目缓存失败: {e}")
            return False
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        try:
            # 获取存储统计
            storage_stats = await self.storage.get_extraction_stats()
            
            return {
                "storage_stats": storage_stats,
                "processor_info": {
                    "initialized": self._initialized,
                    "vectorizer_config": {
                        "chunk_size": self.vectorizer.chunk_size,
                        "chunk_overlap": self.vectorizer.chunk_overlap,
                        "max_concurrent": self.vectorizer.max_concurrent
                    },
                    "extractor_config": {
                        "batch_size": self.extractor.batch_size,
                        "max_concurrent": self.extractor.max_concurrent,
                        "chunk_limit": self.extractor.chunk_limit
                    }
                }
            }
            
        except Exception as e:
            log.error(f"获取处理统计失败: {e}")
            return {"error": str(e)}
