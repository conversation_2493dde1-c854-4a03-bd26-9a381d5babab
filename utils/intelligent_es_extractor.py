#!/usr/bin/env python3
"""
智能ES信息抽取器
基于ES多维度检索的智能化信息抽取
"""

import json
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict

from utils.llm import LLM
from utils.log import log
from utils.config import config


class IntelligentESExtractor:
    """智能ES信息抽取器"""
    
    def __init__(self):
        self.llm = LLM()
        # TODO: 初始化ES客户端
        # self.es_client = ElasticsearchClient()
        
        # 字段提取配置
        self.field_extraction_config = {
            "项目档案": {
                "basic_info": {
                    "project_name": {
                        "keywords": ["项目名称", "项目", "工程名称", "建设项目"],
                        "semantic_query": "项目的正式名称是什么",
                        "patterns": [r"项目名称[：:]\s*(.+)", r"(.+)项目", r"(.+)工程"]
                    },
                    "project_number": {
                        "keywords": ["项目编号", "编号", "项目代码"],
                        "semantic_query": "项目的编号或代码",
                        "patterns": [r"项目编号[：:]\s*([A-Z0-9\-]+)", r"编号[：:]\s*([A-Z0-9\-]+)"]
                    },
                    "responsible_unit": {
                        "keywords": ["承担单位", "负责单位", "实施单位", "建设单位"],
                        "semantic_query": "负责实施这个项目的单位或组织",
                        "patterns": [r"承担单位[：:]\s*(.+)", r"负责单位[：:]\s*(.+)"]
                    },
                    "project_leader": {
                        "keywords": ["项目负责人", "负责人", "项目经理"],
                        "semantic_query": "项目的负责人或项目经理是谁",
                        "patterns": [r"项目负责人[：:]\s*([^\s]+)", r"负责人[：:]\s*([^\s]+)"]
                    }
                },
                "financial_info": {
                    "total_amount": {
                        "keywords": ["项目总投资", "总金额", "总预算", "项目投资"],
                        "semantic_query": "项目的总投资金额或总预算是多少",
                        "patterns": [r"总投资[：:]?\s*([￥¥]?[\d,]+\.?\d*[万亿]?元?)", r"总金额[：:]?\s*([￥¥]?[\d,]+\.?\d*[万亿]?元?)"]
                    },
                    "requested_amount": {
                        "keywords": ["申请金额", "申请资金", "财政资金"],
                        "semantic_query": "申请的资金金额",
                        "patterns": [r"申请金额[：:]?\s*([￥¥]?[\d,]+\.?\d*[万亿]?元?)"]
                    },
                    "approved_amount": {
                        "keywords": ["批准金额", "批复金额", "核定金额"],
                        "semantic_query": "实际批准的资金金额",
                        "patterns": [r"批准金额[：:]?\s*([￥¥]?[\d,]+\.?\d*[万亿]?元?)"]
                    }
                },
                "time_info": {
                    "initiation_date": {
                        "keywords": ["立项时间", "立项日期", "项目立项"],
                        "semantic_query": "项目立项的时间",
                        "patterns": [r"立项时间[：:]?\s*(\d{4}年\d{1,2}月\d{1,2}日|\d{4}年\d{1,2}月|\d{4}年)"]
                    },
                    "start_date": {
                        "keywords": ["开始时间", "开工时间", "启动时间"],
                        "semantic_query": "项目开始实施的时间",
                        "patterns": [r"开始时间[：:]?\s*(\d{4}年\d{1,2}月\d{1,2}日|\d{4}年\d{1,2}月|\d{4}年)"]
                    },
                    "completion_date": {
                        "keywords": ["完成时间", "竣工时间", "结束时间"],
                        "semantic_query": "项目预计完成的时间",
                        "patterns": [r"完成时间[：:]?\s*(\d{4}年\d{1,2}月\d{1,2}日|\d{4}年\d{1,2}月|\d{4}年)"]
                    }
                }
            }
        }
    
    async def extract_project_info_from_es(
        self,
        project_name: str,
        action: str,
        es_index: str = "document_chunks"
    ) -> Dict[str, Any]:
        """从ES中智能抽取项目信息"""
        try:
            log.info(f"开始从ES抽取项目信息: {project_name}, 类型: {action}")
            
            # 获取字段配置
            field_config = self.field_extraction_config.get(action, {})
            
            # 分组抽取信息
            extracted_info = {}
            field_analysis = {}
            
            for group_name, group_fields in field_config.items():
                log.info(f"处理字段组: {group_name}")
                
                group_results = await self._extract_field_group(
                    group_fields, project_name, es_index
                )
                
                extracted_info.update(group_results["data"])
                field_analysis.update(group_results["analysis"])
            
            # 生成最终结果
            result = {
                "project_name": project_name,
                "action": action,
                "extracted_info": extracted_info,
                "field_analysis": field_analysis,
                "extraction_method": "intelligent_es_retrieval",
                "extraction_time": datetime.now().isoformat()
            }
            
            log.info(f"ES信息抽取完成: 提取{len(extracted_info)}个字段")
            return result
            
        except Exception as e:
            log.error(f"ES信息抽取失败: {e}")
            return {"error": str(e)}
    
    async def _extract_field_group(
        self,
        group_fields: Dict[str, Any],
        project_name: str,
        es_index: str
    ) -> Dict[str, Any]:
        """抽取字段组信息"""
        group_data = {}
        group_analysis = {}
        
        for field_name, field_config in group_fields.items():
            try:
                # 多维度检索相关文档片段
                relevant_chunks = await self._multi_dimensional_search(
                    field_config, project_name, es_index
                )
                
                if not relevant_chunks:
                    group_data[field_name] = None
                    group_analysis[field_name] = {
                        "method": "no_relevant_chunks",
                        "confidence": 0.0
                    }
                    continue
                
                # 从检索结果中抽取信息
                extracted_value, analysis = await self._extract_from_chunks(
                    field_name, field_config, relevant_chunks
                )
                
                group_data[field_name] = extracted_value
                group_analysis[field_name] = analysis
                
            except Exception as e:
                log.error(f"抽取字段失败 {field_name}: {e}")
                group_data[field_name] = None
                group_analysis[field_name] = {"error": str(e)}
        
        return {
            "data": group_data,
            "analysis": group_analysis
        }
    
    async def _multi_dimensional_search(
        self,
        field_config: Dict[str, Any],
        project_name: str,
        es_index: str,
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """多维度检索相关文档片段"""
        try:
            # 1. 关键词检索
            keyword_results = await self._keyword_search(
                field_config["keywords"], project_name, es_index, top_k//2
            )
            
            # 2. 语义向量检索
            semantic_results = await self._semantic_search(
                field_config["semantic_query"], project_name, es_index, top_k//2
            )
            
            # 3. 合并和去重
            all_results = keyword_results + semantic_results
            unique_results = self._deduplicate_chunks(all_results)
            
            # 4. 重新排序（基于相关性分数）
            sorted_results = sorted(
                unique_results, 
                key=lambda x: x.get("_score", 0), 
                reverse=True
            )
            
            return sorted_results[:top_k]
            
        except Exception as e:
            log.error(f"多维度检索失败: {e}")
            return []
    
    async def _keyword_search(
        self,
        keywords: List[str],
        project_name: str,
        es_index: str,
        top_k: int
    ) -> List[Dict[str, Any]]:
        """关键词检索"""
        # TODO: 实现ES关键词检索
        # 模拟检索结果
        mock_results = [
            {
                "_score": 0.95,
                "_source": {
                    "chunk_text": f"项目名称：{project_name}建设项目，项目总投资5000万元",
                    "source": "项目立项书.pdf",
                    "page": 1,
                    "chunk_id": "doc1_chunk_1"
                }
            },
            {
                "_score": 0.88,
                "_source": {
                    "chunk_text": f"本项目总预算为人民币伍仟万元整（￥50,000,000元）",
                    "source": "预算文件.pdf", 
                    "page": 2,
                    "chunk_id": "doc2_chunk_3"
                }
            }
        ]
        
        log.debug(f"关键词检索: {keywords}, 返回{len(mock_results)}个结果")
        return mock_results
    
    async def _semantic_search(
        self,
        semantic_query: str,
        project_name: str,
        es_index: str,
        top_k: int
    ) -> List[Dict[str, Any]]:
        """语义向量检索"""
        try:
            # 1. 生成查询向量
            query_vector = await self.llm.get_embedding(semantic_query)
            
            # TODO: 实现ES向量检索
            # 模拟检索结果
            mock_results = [
                {
                    "_score": 0.92,
                    "_source": {
                        "chunk_text": f"智慧城市综合管理平台建设项目由市信息化办公室负责实施",
                        "source": "可研报告.pdf",
                        "page": 3,
                        "chunk_id": "doc3_chunk_2"
                    }
                }
            ]
            
            log.debug(f"语义检索: {semantic_query}, 返回{len(mock_results)}个结果")
            return mock_results
            
        except Exception as e:
            log.error(f"语义检索失败: {e}")
            return []
    
    def _deduplicate_chunks(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重文档片段"""
        seen_chunk_ids = set()
        unique_chunks = []
        
        for chunk in chunks:
            chunk_id = chunk.get("_source", {}).get("chunk_id")
            if chunk_id and chunk_id not in seen_chunk_ids:
                seen_chunk_ids.add(chunk_id)
                unique_chunks.append(chunk)
        
        return unique_chunks
    
    async def _extract_from_chunks(
        self,
        field_name: str,
        field_config: Dict[str, Any],
        chunks: List[Dict[str, Any]]
    ) -> Tuple[Optional[str], Dict[str, Any]]:
        """从文档片段中抽取字段信息"""
        try:
            # 1. 正则表达式抽取
            regex_results = self._extract_with_regex(field_config.get("patterns", []), chunks)
            
            # 2. LLM智能抽取
            llm_results = await self._extract_with_llm(field_name, field_config, chunks)
            
            # 3. 结果融合
            final_value, analysis = self._merge_extraction_results(
                field_name, regex_results, llm_results, chunks
            )
            
            return final_value, analysis
            
        except Exception as e:
            log.error(f"从片段抽取信息失败 {field_name}: {e}")
            return None, {"error": str(e)}
    
    def _extract_with_regex(
        self,
        patterns: List[str],
        chunks: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """使用正则表达式抽取"""
        import re
        results = []
        
        for chunk in chunks:
            chunk_text = chunk.get("_source", {}).get("chunk_text", "")
            
            for pattern in patterns:
                matches = re.findall(pattern, chunk_text)
                for match in matches:
                    results.append({
                        "value": match,
                        "source": chunk.get("_source", {}).get("source", ""),
                        "method": "regex",
                        "confidence": 0.9,  # 正则匹配置信度较高
                        "chunk_id": chunk.get("_source", {}).get("chunk_id", "")
                    })
        
        return results
    
    async def _extract_with_llm(
        self,
        field_name: str,
        field_config: Dict[str, Any],
        chunks: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """使用LLM智能抽取"""
        try:
            # 构建上下文
            context_texts = []
            for chunk in chunks:
                chunk_text = chunk.get("_source", {}).get("chunk_text", "")
                source = chunk.get("_source", {}).get("source", "")
                context_texts.append(f"[来源: {source}] {chunk_text}")
            
            context = "\n\n".join(context_texts)
            
            # 构建提取提示词
            extraction_prompt = f"""
请从以下文档片段中提取"{field_name}"的信息：

文档片段：
{context}

提取要求：
- 字段含义：{field_config.get('semantic_query', '')}
- 如果找到多个可能的值，请选择最准确的一个
- 如果没有找到相关信息，请返回"未找到"
- 请保持原文的表述方式

请直接返回提取到的值：
"""
            
            response = await self.llm.chat([
                {"role": "system", "content": "你是信息提取专家，请准确提取指定字段的信息。"},
                {"role": "user", "content": extraction_prompt}
            ])
            
            if response and response.strip() != "未找到":
                return [{
                    "value": response.strip(),
                    "method": "llm_extraction",
                    "confidence": 0.8,  # LLM提取置信度
                    "source_count": len(chunks)
                }]
            
            return []
            
        except Exception as e:
            log.error(f"LLM抽取失败: {e}")
            return []
    
    def _merge_extraction_results(
        self,
        field_name: str,
        regex_results: List[Dict[str, Any]],
        llm_results: List[Dict[str, Any]],
        chunks: List[Dict[str, Any]]
    ) -> Tuple[Optional[str], Dict[str, Any]]:
        """融合抽取结果"""
        all_results = regex_results + llm_results
        
        if not all_results:
            return None, {
                "method": "no_extraction",
                "confidence": 0.0,
                "searched_chunks": len(chunks)
            }
        
        # 选择置信度最高的结果
        best_result = max(all_results, key=lambda x: x.get("confidence", 0))
        
        return best_result["value"], {
            "method": best_result["method"],
            "confidence": best_result["confidence"],
            "source": best_result.get("source", ""),
            "alternative_count": len(all_results) - 1,
            "searched_chunks": len(chunks)
        }
    
    async def get_extraction_stats(self) -> Dict[str, Any]:
        """获取抽取统计信息"""
        return {
            "extractor_info": {
                "type": "intelligent_es_extractor",
                "version": "1.0",
                "supported_actions": list(self.field_extraction_config.keys())
            },
            "extraction_capabilities": {
                "multi_dimensional_search": True,
                "semantic_retrieval": True,
                "regex_extraction": True,
                "llm_extraction": True,
                "result_fusion": True
            }
        }
