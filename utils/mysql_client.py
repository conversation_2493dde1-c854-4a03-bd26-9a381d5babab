"""
MySQL 数据库客户端 - 使用 SQLAlchemy
"""

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
import logging
from typing import List, Dict, Any
import urllib.parse

log = logging.getLogger(__name__)

class MySQLClient:
    """MySQL 数据库客户端 - 使用 SQLAlchemy"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.engine = None
        self.session_factory = None

    async def connect(self):
        """连接到 MySQL 数据库"""
        try:
            # 构建连接字符串
            password = urllib.parse.quote_plus(self.config.get('password', ''))
            username = self.config.get('username', 'root')
            host = self.config.get('host', 'localhost')
            port = self.config.get('port', 3306)
            database = self.config.get('database', 'hngpt')

            connection_string = f"mysql+asyncmy://{username}:{password}@{host}:{port}/{database}?charset=utf8mb4"

            self.engine = create_async_engine(
                connection_string,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                echo=False
            )

            self.session_factory = sessionmaker(
                bind=self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )

            log.info(f"Connected to MySQL database: {database}")

            # 创建必要的表
            await self._create_tables()

        except Exception as e:
            log.error(f"Failed to connect to MySQL: {e}")
            raise

    async def is_connected(self) -> bool:
        """检查是否已连接到数据库"""
        try:
            if self.engine is None:
                return False
            # 尝试执行简单查询来检查连接状态
            async with self.session_factory() as session:
                await session.execute(text("SELECT 1"))
                return True
        except Exception:
            return False
    
    async def _create_tables(self):
        """创建必要的表 - 与实际数据库结构保持一致"""
        try:
            # 创建 project_extract 表 - 与实际数据库结构一致
            project_extract_sql = """
            CREATE TABLE IF NOT EXISTS `project_extract` (
              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
              `doc_id` varchar(255) COMMENT '文档ID',
              `project_name` varchar(500) NOT NULL COMMENT '项目名称',
              `project_key` varchar(255) NOT NULL COMMENT '项目唯一标识（基于项目名称生成）',
              `project_no` varchar(100) DEFAULT NULL COMMENT '项目编号',
              `start_date` date DEFAULT NULL COMMENT '开始日期',
              `end_date` date DEFAULT NULL COMMENT '结束日期',
              `total_investment` bigint(20) DEFAULT NULL COMMENT '总投资金额（单位：万元）',
              `responsible_unit` text COMMENT '承担单位',
              `leader` varchar(200) DEFAULT NULL COMMENT '项目负责人',
              `research_points` text COMMENT '主要研究内容',
              `innovation` text COMMENT '创新点',
              `main_deliverables` text COMMENT '主要交付成果',
              `patent` text COMMENT '专利信息',
              `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (`id`),
              UNIQUE KEY `uk_project_key` (`project_key`),
              UNIQUE KEY `uk_project_name` (`project_name`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目提取表-基于项目维度'
            """

            # 创建 conference_extract 表 - 与实际数据库结构一致
            conference_extract_sql = """
            CREATE TABLE IF NOT EXISTS `conference_extract` (
              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
              `doc_id` varchar(255) COMMENT '文档ID',
              `conference_name` varchar(500) NOT NULL COMMENT '文书名称',
              `conference_key` varchar(255) NOT NULL COMMENT '文书唯一标识（基于文书名称生成）',
              `conference_no` varchar(100) DEFAULT NULL COMMENT '文书编号',
              `conference_date` date DEFAULT NULL COMMENT '文书日期',
              `location` varchar(500) DEFAULT NULL COMMENT '会议地点',
              `organizer` text COMMENT '发起组织',
              `participants` text COMMENT '参与者/接收者',
              `main_content` text COMMENT '主要内容',
              `decisions` text COMMENT '决议内容',
              `follow_up` text COMMENT '后续行动',
              `attachments` text COMMENT '附件信息',
              `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (`id`),
              UNIQUE KEY `uk_conference_key` (`conference_key`),
              UNIQUE KEY `uk_conference_name` (`conference_name`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文书提取表-基于文书维度'
            """

            async with self.session_factory() as session:
                await session.execute(text(project_extract_sql))
                await session.execute(text(conference_extract_sql))
                await session.commit()

            log.info("Created project_extract and conference_extract tables")
            
        except Exception as e:
            log.error(f"Failed to create MySQL tables: {e}")
            raise


    
    async def execute(self, sql: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """执行 SQL 查询"""
        try:
            # 确保连接已建立
            if not self.session_factory:
                await self.connect()

            async with self.session_factory() as session:
                result = await session.execute(text(sql), params or {})

                if sql.strip().upper().startswith('SELECT'):
                    rows = result.fetchall()
                    if not rows:
                        return []
                    # 获取列名列表
                    keys = result.keys()
                    # 将每行元组转换为字典
                    return [dict(zip(keys, row)) for row in rows]
                else:
                    await session.commit()
                    return []

        except Exception as e:
            log.error(f"SQL execution failed: {e}")
            log.error(f"SQL: {sql}")
            log.error(f"Params: {params}")
            raise
    
    async def upsert(self, table: str, data: Dict[str, Any], key_fields: List[str]) -> bool:
        """插入或更新数据 - 使用 REPLACE INTO 简化逻辑"""
        try:
            # 使用 REPLACE INTO 语句
            # REPLACE INTO 会自动处理插入/更新逻辑：
            # - 如果记录不存在，执行 INSERT
            # - 如果记录存在（基于唯一键冲突），执行 DELETE + INSERT

            columns = list(data.keys())
            placeholders = ', '.join([f':{col}' for col in columns])

            # 构建 REPLACE INTO 语句
            sql = f"REPLACE INTO `{table}` ({', '.join([f'`{col}`' for col in columns])}) VALUES ({placeholders})"

            await self.execute(sql, data)
            log.info(f"Successfully updated/created {table} record with key: {[data.get(field) for field in key_fields]}")
            return True

        except Exception as e:
            log.error(f"Upsert failed: {e}")
            return False
    
    async def query(self, sql: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """查询数据"""
        return await self.execute(sql, params)

    async def create_table(self, table_name: str, schema) -> bool:
        """创建表"""
        try:
            # 处理不同类型的schema
            if isinstance(schema, dict):
                # 如果schema是字典，转换为SQL列定义
                columns = []
                for column_name, column_type in schema.items():
                    # 将SQLite类型转换为MySQL类型
                    mysql_type = self._convert_sqlite_to_mysql_type(column_type)
                    columns.append(f"`{column_name}` {mysql_type}")

                columns_sql = ", ".join(columns)
                sql = f"CREATE TABLE IF NOT EXISTS `{table_name}` ({columns_sql})"

            elif isinstance(schema, str):
                # 如果schema是字符串
                if not schema.strip().upper().startswith('CREATE TABLE'):
                    # 如果不是完整的CREATE TABLE语句，构建一个
                    sql = f"CREATE TABLE IF NOT EXISTS `{table_name}` ({schema})"
                else:
                    sql = schema
            else:
                raise ValueError(f"Unsupported schema type: {type(schema)}")

            await self.execute(sql)
            return True

        except Exception as e:
            log.error(f"Failed to create table {table_name}: {e}")
            return False

    def _convert_sqlite_to_mysql_type(self, sqlite_type: str) -> str:
        """将SQLite类型转换为MySQL类型"""
        sqlite_type = sqlite_type.upper()

        # 类型映射
        type_mapping = {
            'INTEGER PRIMARY KEY': 'BIGINT PRIMARY KEY',
            'INTEGER': 'BIGINT',
            'TEXT NOT NULL': 'TEXT NOT NULL',
            'TEXT': 'TEXT',
            'DATE': 'DATE',
            'DATETIME': 'DATETIME',
            'TIMESTAMP': 'TIMESTAMP',
            'REAL': 'DOUBLE',
            'BLOB': 'BLOB'
        }

        return type_mapping.get(sqlite_type, sqlite_type)
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            log.info("MySQL connection closed")
    
    async def close(self):
        """关闭数据库连接"""
        try:
            if self.engine:
                await self.engine.dispose()
                log.info("MySQL connection closed")
        except Exception as e:
            log.error(f"Error closing MySQL connection: {e}")

    def __del__(self):
        """析构函数"""
        # 注意：在析构函数中不能调用异步方法
        pass
