#!/usr/bin/env python3
"""
兼容现有架构的知识索引器
基于现有的KnowledgeControl和ESKnowledge架构
"""

import os
import json
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional

from utils.log import log
from utils.config import config
from knowledge_control import KnowledgeControl
from utils.minio_client import MinioClient


class CompatibleKnowledgeIndexer:
    """兼容现有架构的知识索引器"""
    
    def __init__(self):
        # 从配置中获取ES和数据库配置
        es_config = {
            "host": config.get("elasticsearch.host", "localhost"),
            "port": config.get("elasticsearch.port", 9200),
            "username": config.get("elasticsearch.username", ""),
            "password": config.get("elasticsearch.password", ""),
            "index_name": config.get("elasticsearch.index_name", "knowledge_base")
        }

        # 数据库配置（使用MySQL配置）
        db_config = {
            "host": config.get("mysql.host", "***********"),
            "port": config.get("mysql.port", 3306),
            "user": config.get("mysql.user", "root"),
            "password": config.get("mysql.password", "startfrom2023"),
            "database": config.get("mysql.database", "hngpt")
        }

        self.knowledge_control = KnowledgeControl(es_config, db_config)
        self.minio = MinioClient()
        
        # 配置参数
        self.chunk_size = config.get("document_indexer.chunk_size", 512)
        self.chunk_overlap = config.get("document_indexer.chunk_overlap", 50)
        
        self._initialized = False
    
    async def initialize(self):
        """初始化索引器"""
        if not self._initialized:
            await self.knowledge_control.init()
            self._initialized = True
            log.info("兼容知识索引器初始化完成")
    
    async def index_project_documents(
        self,
        project_name: str,
        action: str,
        urls: List[str],
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """索引项目文档 - 兼容现有架构"""
        try:
            await self.initialize()
            
            log.info(f"开始索引项目文档: {project_name}, 类型: {action}, 文档数: {len(urls)}")
            
            # 如果强制刷新，清理现有数据
            if force_refresh:
                await self._cleanup_existing_data(project_name, action)
            
            # 处理每个文档
            total_chunks = 0
            success_docs = 0
            failed_docs = 0
            processed_doc_ids = []
            
            for url in urls:
                try:
                    doc_id, chunks_count = await self._process_single_document(
                        url, project_name, action
                    )
                    
                    if doc_id and chunks_count > 0:
                        total_chunks += chunks_count
                        success_docs += 1
                        processed_doc_ids.append(doc_id)
                        log.info(f"文档处理成功: {url}, doc_id: {doc_id}, 切片数: {chunks_count}")
                    else:
                        failed_docs += 1
                        log.error(f"文档处理失败: {url}")
                        
                except Exception as e:
                    failed_docs += 1
                    log.error(f"处理文档异常 {url}: {e}")
            
            # 返回结果
            result = {
                "index_name": "docs",  # 使用固定的索引名
                "project_name": project_name,
                "action": action,
                "total_documents": len(urls),
                "success_documents": success_docs,
                "failed_documents": failed_docs,
                "total_chunks": total_chunks,
                "processed_doc_ids": processed_doc_ids,
                "indexed_at": datetime.now().isoformat()
            }
            
            log.info(f"项目文档索引完成: {project_name}, 成功{success_docs}/{len(urls)}, 总切片{total_chunks}")
            return result
            
        except Exception as e:
            log.error(f"索引项目文档失败: {e}")
            return {"error": str(e)}
    
    async def _process_single_document(
        self,
        url: str,
        project_name: str,
        action: str
    ) -> tuple[Optional[str], int]:
        """处理单个文档"""
        try:
            # 1. 下载JSON文件
            json_url = os.path.splitext(url)[0] + ".json"
            json_path = self.minio.download_file(json_url)
            
            # 2. 读取文档内容
            with open(json_path, 'r', encoding='utf-8') as f:
                doc_data = json.load(f)
            
            # 3. 生成doc_id（兼容现有逻辑）
            source_name = doc_data.get("source", os.path.basename(url))
            doc_id = hashlib.md5(f"{project_name}_{source_name}".encode()).hexdigest()
            
            # 4. 使用现有的KnowledgeControl处理文档
            success = await self.knowledge_control.process_document(
                doc_data, project_name, action
            )
            
            if not success:
                log.error(f"KnowledgeControl处理失败: {url}")
                return None, 0
            
            # 5. 统计切片数量（从ES中查询）
            chunks_count = await self._count_document_chunks(doc_id)
            
            return doc_id, chunks_count
            
        except Exception as e:
            log.error(f"处理单个文档失败 {url}: {e}")
            return None, 0
    
    async def _count_document_chunks(self, doc_id: str) -> int:
        """统计文档的切片数量"""
        try:
            # 使用现有的ES客户端查询
            es_knowledge = self.knowledge_control.es_knowledge
            
            query = {
                "bool": {
                    "must": [
                        {"term": {"doc_id": doc_id}}
                    ]
                }
            }
            
            result = await es_knowledge.search(
                query=query,
                size=0  # 只获取总数
            )
            
            total = result.get("hits", {}).get("total", {})
            if isinstance(total, dict):
                return total.get("value", 0)
            else:
                return total or 0
                
        except Exception as e:
            log.error(f"统计文档切片数量失败: {e}")
            return 0
    
    async def _cleanup_existing_data(self, project_name: str, action: str):
        """清理现有数据"""
        try:
            # 从ES中删除相关文档
            es_knowledge = self.knowledge_control.es_knowledge
            
            query = {
                "bool": {
                    "must": [
                        {"term": {"project_name": project_name}},
                        {"term": {"action": action}}
                    ]
                }
            }
            
            # 删除ES中的文档
            await es_knowledge.es.delete_by_query(
                index="docs",
                body={"query": query}
            )
            
            log.info(f"已清理现有数据: {project_name} - {action}")
            
        except Exception as e:
            log.error(f"清理现有数据失败: {e}")
    
    async def get_index_info(self, project_name: str, action: str) -> Dict[str, Any]:
        """获取索引信息"""
        try:
            await self.initialize()
            
            # 查询ES中的文档数量
            es_knowledge = self.knowledge_control.es_knowledge
            
            query = {
                "bool": {
                    "must": [
                        {"term": {"project_name": project_name}},
                        {"term": {"action": action}}
                    ]
                }
            }
            
            result = await es_knowledge.search(
                query=query,
                size=0
            )
            
            total = result.get("hits", {}).get("total", {})
            if isinstance(total, dict):
                document_count = total.get("value", 0)
            else:
                document_count = total or 0
            
            return {
                "exists": document_count > 0,
                "index_name": "docs",
                "project_name": project_name,
                "action": action,
                "document_count": document_count,
                "query_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            log.error(f"获取索引信息失败: {e}")
            return {"error": str(e)}
    
    async def delete_project_index(self, project_name: str, action: str) -> bool:
        """删除项目索引数据"""
        try:
            await self.initialize()
            
            # 删除ES中的相关文档
            await self._cleanup_existing_data(project_name, action)
            
            # 删除MySQL中的相关数据
            table_name = self.knowledge_control.TABLE_MAPPING.get(action)
            if table_name:
                # 这里可以添加MySQL删除逻辑
                pass
            
            log.info(f"项目索引删除成功: {project_name} - {action}")
            return True
            
        except Exception as e:
            log.error(f"删除项目索引失败: {e}")
            return False
    
    async def search_documents(
        self,
        project_name: str,
        action: str,
        query: str,
        size: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索文档 - 使用现有的ES架构"""
        try:
            await self.initialize()
            
            es_knowledge = self.knowledge_control.es_knowledge
            
            # 构建搜索查询
            search_query = {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "query": query,
                                "fields": ["content^2", "source"],
                                "type": "best_fields"
                            }
                        },
                        {"term": {"project_name": project_name}},
                        {"term": {"action": action}}
                    ]
                }
            }
            
            # 执行搜索
            result = await es_knowledge.search(
                query=search_query,
                size=size,
                source=["content", "source", "page_num", "doc_id", "project_name", "action"]
            )
            
            # 处理结果
            hits = result.get("hits", {}).get("hits", [])
            documents = []
            
            for hit in hits:
                source = hit.get("_source", {})
                documents.append({
                    "score": hit.get("_score", 0),
                    "content": source.get("content", ""),
                    "source": source.get("source", ""),
                    "page_num": source.get("page_num", 0),
                    "doc_id": source.get("doc_id", ""),
                    "project_name": source.get("project_name", ""),
                    "action": source.get("action", "")
                })
            
            return documents
            
        except Exception as e:
            log.error(f"搜索文档失败: {e}")
            return []
    
    async def get_indexer_stats(self) -> Dict[str, Any]:
        """获取索引器统计信息"""
        try:
            return {
                "indexer_info": {
                    "type": "compatible_knowledge_indexer",
                    "initialized": self._initialized,
                    "chunk_size": self.chunk_size,
                    "chunk_overlap": self.chunk_overlap
                },
                "knowledge_control_status": {
                    "es_initialized": hasattr(self.knowledge_control, 'es_knowledge'),
                    "sql_initialized": hasattr(self.knowledge_control, 'sql_knowledge')
                }
            }
            
        except Exception as e:
            log.error(f"获取索引器统计失败: {e}")
            return {"error": str(e)}
