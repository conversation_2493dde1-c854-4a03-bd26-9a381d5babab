import sqlite3
from typing import Dict, Any, List
import os

class SQLiteClient:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        
    def connect(self):
        """建立数据库连接"""
        try:
            # 确保数据库目录存在
            os.makedirs(os.path.dirname(os.path.abspath(self.db_path)), exist_ok=True)
            
            self.conn = sqlite3.connect(self.db_path)
            self.cursor = self.conn.cursor()
            return True
        except Exception as e:
            print(f"Error connecting to database: {str(e)}")
            return False
            
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.conn is not None and self.cursor is not None
        
    def create_table(self, table_name: str, schema: Dict[str, str]):
        """创建表"""
        try:
            if not self.is_connected():
                self.connect()
                
            # 构建建表SQL
            columns = [f"{name} {type_}" for name, type_ in schema.items()]
            sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})"
            
            self.cursor.execute(sql)
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error creating table: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False
            
    def insert(self, table: str, record: Dict[str, Any]) -> bool:
        """插入记录"""
        try:
            if not self.is_connected():
                self.connect()
                
            columns = ', '.join(record.keys())
            placeholders = ', '.join(['?' for _ in record])
            sql = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
            
            self.cursor.execute(sql, list(record.values()))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error inserting record: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False
            
    def query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行查询"""
        try:
            if not self.is_connected():
                self.connect()

            if params:
                self.cursor.execute(sql, params)
            else:
                self.cursor.execute(sql)
            columns = [description[0] for description in self.cursor.description]
            return [dict(zip(columns, row)) for row in self.cursor.fetchall()]
        except Exception as e:
            print(f"Error executing query: {str(e)}")
            return []
            
    def execute_sql(self, sql: str, params: tuple = None) -> bool:
        """执行任意SQL语句"""
        try:
            if not self.is_connected():
                self.connect()
                
            if params:
                self.cursor.execute(sql, params)
            else:
                self.cursor.execute(sql)
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error executing SQL: {str(e)}")
            if self.conn:
                self.conn.rollback()
            return False

    def close(self):
        """关闭连接"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.conn:
                self.conn.close()
            self.cursor = None
            self.conn = None
        except Exception as e:
            print(f"Error closing database connection: {str(e)}")
            
    def upsert(self, table: str, record: Dict[str, Any], key_fields: List[str]) -> bool:
        """插入或更新记录
        
        Args:
            table: 表名
            record: 记录数据
            key_fields: 用于确定记录唯一性的字段列表
            
        Returns:
            bool: 操作是否成功
        """
        try:
            if not self.is_connected():
                self.connect()
                
            # 构建WHERE子句
            where_clause = " AND ".join([f"{field} = ?" for field in key_fields])
            where_values = [record[field] for field in key_fields]
            
            # 检查记录是否存在
            check_sql = f"SELECT 1 FROM {table} WHERE {where_clause}"
            self.cursor.execute(check_sql, where_values)
            exists = self.cursor.fetchone() is not None
            
            if exists:
                # 更新记录
                set_fields = [f"{k} = ?" for k in record.keys() if k not in key_fields]
                set_values = [v for k, v in record.items() if k not in key_fields]
                update_sql = f"""
                    UPDATE {table} 
                    SET {', '.join(set_fields)}
                    WHERE {where_clause}
                """
                self.cursor.execute(update_sql, set_values + where_values)
            else:
                # 插入新记录
                fields = list(record.keys())
                placeholders = ["?" for _ in fields]
                insert_sql = f"""
                    INSERT INTO {table} 
                    ({', '.join(fields)}) 
                    VALUES ({', '.join(placeholders)})
                """
                self.cursor.execute(insert_sql, list(record.values()))
                
            self.conn.commit()
            return True
            
        except Exception as e:
            print(f"Error upserting record: {str(e)}")
            return False