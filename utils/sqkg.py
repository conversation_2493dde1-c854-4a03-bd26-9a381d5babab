from typing import List, Dict, Any, Optional
import os
from utils.sqlite_client import SQLiteClient
from dataclasses import dataclass
from utils.valid import parse_date, parse_number
import re
import sqlite3
import json
from utils.utils import load_json
@dataclass
class ExtractConfig:
    """信息抽取配置"""
    fields: List[str]  # 需要抽取的字段列表
    table_name: str    # 保存的表名
    schema: Dict[str, str]  # 字段类型定义
    prompt_template: str = ""  # 自定义提示词模板

def preprocess_general(value):
    """通用字段预处理"""
    return ', '.join(value) if isinstance(value, list) else str(value)

def preprocess_field(field_name: str, value: Any) -> Any:
    """根据字段名预处理字段值"""
    try:
        if field_name in ['start_date', 'end_date', 'date']:
            return parse_date(value)
        elif field_name == 'total_investment':
            # 如果是空值或未提及
            if not value or value == "未提及":
                return 0
                
            # 如果已经是数字
            if isinstance(value, (int, float)):
                return int(value * 10000)  # 转换为元
                
            # 处理字符串格式
            value_str = str(value).strip()
            
            # 提取数字部分
            number_match = re.search(r"([\d.]+)", value_str)
            if not number_match:
                return 0
                
            amount = float(number_match.group(1))
            
            # 根据单位转换为元
            if "亿" in value_str:
                amount = amount * 100000000  # 亿 -> 元
            elif "千万" in value_str:
                amount = amount * 10000000   # 千万 -> 元
            elif "万" in value_str:
                amount = amount * 10000      # 万 -> 元
                
            return int(amount)  # 返回整数（单位：元）
        else:
            return preprocess_general(value)
    except Exception as e:
        print(f"Error preprocessing field {field_name}: {str(e)}")
        if field_name in ['start_date', 'end_date', 'date']:
            return "1900-01-01"  # 返回有效的默认日期
        elif field_name == 'total_investment':
            return 0
        else:
            return "未提及"


def safe_get(data: dict, key: str, default: str = "") -> str:
    """安全获取字典值"""
    try:
        value = data.get(key, default)
        return str(value) if value is not None else default
    except Exception:
        return default


class SQLKnowledge:
    """SQL知识仓库：负责结构化信息的存储和查询（支持SQLite和MySQL）"""

    def __init__(self, db_config):
        # 支持两种初始化方式：字符串路径（SQLite）或字典配置（MySQL）
        if isinstance(db_config, str):
            # SQLite 模式
            self.db_type = "sqlite"
            self.db = SQLiteClient(db_config)
            # 确保数据库目录存在
            os.makedirs(os.path.dirname(os.path.abspath(db_config)), exist_ok=True)
            # 初始化连接
            self.db.connect()
        elif isinstance(db_config, dict):
            # MySQL 模式
            self.db_type = "mysql"
            from utils.mysql_client import MySQLClient
            self.db = MySQLClient(db_config)
            # 注意：连接将在需要时异步建立
        else:
            raise ValueError("db_config must be either a string (SQLite path) or dict (MySQL config)")
        
        # 表结构定义 - 与 KnowledgeControl.TABLE_SCHEMAS 保持一致
        self.project_schema = """
        The table `project_extract` has the following columns:
        - id: bigint(20) NOT NULL AUTO_INCREMENT (自增主键)
        - doc_id: varchar(255) (文档ID)
        - project_name: varchar(500) NOT NULL (项目名称)
        - project_key: varchar(255) NOT NULL (项目唯一标识，基于项目名称生成)
        - project_no: varchar(100) DEFAULT NULL (项目编号)
        - start_date: date DEFAULT NULL (开始日期)
        - end_date: date DEFAULT NULL (结束日期)
        - total_investment: bigint(20) DEFAULT NULL (总投资金额，单位：万元)
        - responsible_unit: text (承担单位)
        - leader: varchar(200) DEFAULT NULL (项目负责人)
        - research_points: text (主要研究内容)
        - innovation: text (创新点)
        - main_deliverables: text (主要交付成果)
        - patent: text (专利信息)
        - create_time: datetime DEFAULT CURRENT_TIMESTAMP (创建时间)
        - update_time: datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (更新时间)

        UNIQUE KEYS: uk_project_key (project_key), uk_project_name (project_name)
        """

        self.conference_schema = """
        The table `conference_extract` has the following columns:
        - id: bigint(20) NOT NULL AUTO_INCREMENT (自增主键)
        - doc_id: varchar(255) (文档ID)
        - conference_name: varchar(500) NOT NULL (文书名称)
        - conference_key: varchar(255) NOT NULL (文书唯一标识，基于文书名称生成)
        - conference_no: varchar(100) DEFAULT NULL (文书编号)
        - conference_date: date DEFAULT NULL (文书日期)
        - location: varchar(500) DEFAULT NULL (会议地点)
        - organizer: text (发起组织)
        - participants: text (参与者/接收者)
        - main_content: text (主要内容)
        - decisions: text (决议内容)
        - follow_up: text (后续行动)
        - attachments: text (附件信息)
        - create_time: datetime DEFAULT CURRENT_TIMESTAMP (创建时间)
        - update_time: datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (更新时间)

        UNIQUE KEYS: uk_conference_key (conference_key), uk_conference_name (conference_name)
        """


    def _format_project_data(self, project_data: List[Dict[str, Any]], query_type: str) -> str:
        if not project_data:
            return "未找到相关项目信息"

        # 智能识别统计结果（四重验证机制）
        is_statistical = (
            query_type == "aggregate"  # 显式类型标记
            or any(key in row for row in project_data for key in ('count', 'total', 'sum', 'avg'))  # 统计字段
            or (len(project_data) > 0 and len(project_data[0]) == 1)  # 单字段聚合
            or any(isinstance(v, (int, float)) and k != "id" for row in project_data for k, v in row.items())  # 数值型字段
        )

        # 统计结果处理分支
        if is_statistical:
            results = []
            for row in project_data:
                if not isinstance(row, dict):
                    continue
                if 'year' in row and 'total' in row:
                    results.append(f"{row['year']}年总投资：{row['total']:,}元")
                elif 'month' in row and 'count' in row:
                    results.append(f"{row['month']}月立项：{row['count']}个项目")
                elif 'responsible_unit' in row and 'total' in row:
                    results.append(f"{row['responsible_unit']}累计投资：{row['total']:,}元")
                else:
                    stats = []
                    for key, value in row.items():
                        if isinstance(value, (int, float)):
                            if key == 'count':
                                stats.append(f"{value}个项目")
                            elif 'total' in key:
                                stats.append(f"总额：{value:,}元")
                            elif 'avg' in key:
                                stats.append(f"均值：{value:,.2f}元")
                            else:
                                stats.append(f"{value:,}")
                        else:
                            stats.append(f"{key}：{value}")
                    results.append("▪️ " + " | ".join(stats))
            
            return "项目统计：\n" + "\n".join(results)
        else:
            projects_info = []
            for proj in project_data:
                details = [
                    f"- 项目名称：{safe_get(proj, 'name', '未知')}",
                    f"  开始日期：{safe_get(proj, 'start_date', '未知')}",
                    f"  结束日期：{safe_get(proj, 'end_date', '未知')}",
                    f"  总投资：{int(safe_get(proj, 'total_investment', '0')):,}元",
                    f"  承担单位：{safe_get(proj, 'responsible_unit', '未知')}",
                    f"  负责人：{safe_get(proj, 'leader', '未知')}",
                    f"  主要研究点：{safe_get(proj, 'research_points', '无')}",
                    f"  创新点：{safe_get(proj, 'innovation', '无')}",
                    f"  主要交付成果：{safe_get(proj, 'main_deliverables', '无')}",
                    f"  专利：{safe_get(proj, 'patent', '无')}"
                ]
                projects_info.append("\n".join(details))
            
            return "项目信息：\n" + "\n".join(projects_info)


    def _format_conference_data(self, conference_data: List[Dict[str, Any]], query_type: str) -> str:
        # 空数据处理
        if not conference_data:
            return "未找到相关会议记录"

        # 判断统计结果的三重条件
        is_statistical = (
            query_type == "aggregate"  # 查询类型标记
            or any(key in row for row in conference_data for key in ('count', 'total', 'sum', 'avg'))  # 统计字段
            or (len(conference_data) > 0 and len(conference_data[0]) == 1)  # 单字段聚合
        )

        # 统计结果处理
        if is_statistical:
            results = []
            for row in conference_data:
                if not isinstance(row, dict):
                    continue

                # 年度会议统计
                if 'year' in row and 'count' in row:
                    results.append(f"● {row['year']}年举办会议：{row['count']}次") 
                # 月度统计
                elif 'month' in row and 'count' in row:
                    results.append(f"● {row['month']}月会议数量：{row['count']}次")
                # 组织统计
                elif 'organizer' in row and 'count' in row:
                    results.append(f"● {row['organizer']}主办会议：{row['count']}次")
                # 参会人数统计
                elif 'participants_total' in row:
                    results.append(f"● 累计参会人数：{row['participants_total']:,}人")
                # 通用数值处理
                else:
                    parts = []
                    for key, value in row.items():
                        if isinstance(value, (int, float)):
                            if key == 'count':
                                parts.append(f"{value}次")
                            elif 'participant' in key:
                                parts.append(f"{value}人")
                            else:
                                parts.append(f"{value:,}")
                        else:
                            parts.append(f"{key}：{value}")
                    results.append("● " + "，".join(parts))
            
            return "会议统计结果：\n" + "\n".join(results)

        # 普通记录处理
        else:
            details = []
            for idx, conf in enumerate(conference_data, 1):
                if not isinstance(conf, dict):
                    continue

                # 构建详细信息
                info = [
                    f"会议{idx}：{safe_get(conf, 'name', '未命名会议')}",
                    f"时间：{safe_get(conf, 'date', '日期未知')}",
                    f"类型：{safe_get(conf, 'type', '未分类')}",
                    f"主办方：{safe_get(conf, 'organizer', '未知单位')}",
                    f"参会人数：{safe_get(conf, 'participants_count', '0')}人",
                    f"主要内容：{safe_get(conf, 'summary', '暂无摘要').strip('。')[:50]}..."
                ]
                details.append("\n".join(info))
            
            return "会议记录详情：\n" + "\n\n".join(details)

    

    async def get_context(self, question: str, project_name: str, project_type: str, year: str, keywords: List[str], llm: Any) -> Dict[str, Any]:
        try:
            # ================ 1. 根据项目类型选择目标表和日期字段 ================
            target_table = "conference_extract" if project_type == "文书档案" else "project_extract"
            date_field = "conference_date" if target_table == "conference_extract" else "start_date"  # 新增日期字段映射
            print(f"根据项目类型[{project_type}]选择表: {target_table}, 日期字段: {date_field}")

            # ================ 2. 修改后的年份提取逻辑 ================
            year_range_match = re.search(r'(\d{4})年[至到](\d{4})年', question)
            year_single_match = re.search(r'(\d{4})年', question)
            
            start_year = None
            end_year = None
            year_condition = None
            
            if year_range_match:
                start_year = year_range_match.group(1)
                end_year = year_range_match.group(2)
                year_condition = f"strftime('%Y', {date_field}) BETWEEN '{start_year}' AND '{end_year}'"  # 使用动态字段
            elif year_single_match:
                year = year_single_match.group(1)
                year_condition = f"strftime('%Y', {date_field}) = '{year}'"  # 使用动态字段
            else:
                year_condition = None
            if project_type == "文书档案":
                return await self._handle_conference(question, project_name,year_condition, llm)
            else:
                return await self._handle_project(question, project_name,year_condition, llm)
        except Exception as e:
            print(f"全局错误: {str(e)}")
            return {
                "project_data": [],
                "conference_data": [],
                "formatted_context": "数据获取失败，请检查查询条件"
            }

    def _is_valid_query(self, query: str) -> bool:
        """查询有效性检查"""
        return all([
            "select" in query.lower(),
            not any(kw in query.lower() for kw in ["drop", "delete", "insert"]),
            len(query) > 15
        ])
    

    async def _handle_project(self, question: str, project_name: str,year: str, llm: Any) -> Dict[str, Any]:
        """处理项目档案查询"""
        rules = (
        f"重要说明：\n"
        f"1. total_investment 字段在数据库中已经统一使用\"元\"作为单位，无需进行单位转换\n"
        f"2. start_date, end_date字段都是DATE类型，必须使用strftime函数进行日期过滤\n"
        f"3. 如果问题或参数中包含年份，必须直接使用该年份值（不要使用参数占位符 ?）;如果问题或参数中不包含年份，不要添加任何日期过滤条件\n" 
        f"4. 年份值应该直接写在SQL中，例如：strftime('%Y', start_date) = '2017'\n"
        f"5. 金额条件必须保持原始单位，例如：\n"
        f"   - 100万 -> total_investment > 1000000\n"
        f"   - 1亿 -> total_investment > 100000000\n"
        f"   不要自行转换单位\n\n"
        f"查询规则：\n"
        f"1. 当用户提供项目名称时（非空），必须添加：project_name LIKE '%{{project_name}}%'\n"
        f"2. 名称查询必须返回完整记录，使用SELECT *\n"
        f"3. 统计类查询仅在问题包含'统计'、'总数'等关键词时使用\n\n"
        f"4. 对于统计类查询（如总金额、项目数量、年度统计等），不使用项目名称条件\n"
        f"5. 对于具体项目信息查询，使用项目名称作为条件：project_name LIKE '%项目名称%'\n"
        f"6. 日期查询必须使用strftime函数，不能使用LIKE\n"
        f"7. 年份查询必须使用传入的年份参数，不要使用示例中的年份\n\n")

        prompt = f"""
        根据用户问题生成合适的SQL查询
        用户问题：\"{question}\"
        项目名称：\"{project_name}\"
        年份条件：\"{year if year else ''}\"
        {rules}

        "输出格式：\n"
        "{{\n"
            \"table\": \"project_extract\",\n
            \"query\": \"SQL查询语句\",\n
            \"type\": \"normal\" 或 \"aggregate\"\n
        }}\n\n
        【项目档案查询规则】
        1. 必须查询project_extract表
        2. 必须包含名称条件: project_name LIKE '%{project_name}%'
        3. 典型字段: start_date, total_investment, responsible_unit
        4. 示例查询:
           - 基础查询: SELECT * FROM project_extract WHERE project_name LIKE '%{project_name}%'
           - 投资金额: SELECT * FROM project_extract
             WHERE project_name LIKE '%{project_name}%' AND total_investment > 1000000
           - 时间范围: SELECT * FROM project_extract
             WHERE project_name LIKE '%{project_name}%'
             AND strftime('%Y', start_date) BETWEEN '2020' AND '2022'

        # 表结构
        {self.project_schema}
          
        # 重要约束
        - 禁止使用 start_date 字段
        - 无金额字段
        - 最新排序使用 ORDER BY date DESC
        """
        
        # 获取LLM响应
        response = await llm.chat([{"role": "user", "content": prompt}])
        
        # 解析响应
        query_info = self._parse_response(response, "project_extract")
        
        # 备用查询生成
        if not query_info or not self._is_valid_query(query_info["query"]):
            print("启用项目备用查询")
            query_info = {
                "table": "project_extract",
                "query": f"""SELECT * FROM project_extract
                          WHERE project_name LIKE '%{project_name}%'
                          ORDER BY start_date DESC LIMIT 1""",
                "type": "normal"
            }
        
        return self._execute_query(query_info)

    
    async def _handle_conference(self, question: str, project_name: str,year: str, llm: Any) -> Dict[str, Any]:
        """处理文书档案查询"""

        rules = (
        f"查询规则：\n"
        f"1. 当用户提供项目名称时（非空），必须添加：conference_name LIKE '%{{project_name}}%'\n"
        f"2. 名称查询必须返回完整记录，使用SELECT *\n"
        f"3. 统计类查询仅在问题包含'统计'、'总数'等关键词时使用\n\n"
        f"4. 对于统计类查询（如总金额、项目数量、年度统计等），不使用项目名称条件\n"
        f"5. 对于具体项目信息查询，使用项目名称作为条件：conference_name LIKE '%项目名称%'\n"
        f"6. 日期查询必须使用strftime函数，不能使用LIKE\n"
        f"7. 年份查询必须使用传入的年份参数，不要使用示例中的年份\n\n")

        prompt = f"""
        根据用户问题生成合适的SQL查询
        用户问题：\"{question}\"
        项目名称：\"{project_name}\"
        年份条件：\"{year if year else ''}\"
        {rules}
        "输出格式：\n"
        "{{\n"
            \"table\": \"conference_extract\",\n
            \"query\": \"SQL查询语句\",\n
            \"type\": \"normal\" 或 \"aggregate\"\n
        }}\n\n
        # 文书档案专用规则
        1. 必须查询conference_extract表
        2. 必须包含名称条件: conference_name LIKE '%{project_name}%'
        3. 典型字段: conference_date, organizer, participants, main_content
        4. 示例查询:
           - 基础查询: SELECT * FROM conference_extract WHERE conference_name LIKE '%{project_name}%'
           - 带日期: SELECT * FROM conference_extract
             WHERE conference_name LIKE '%{project_name}%' AND strftime('%Y', conference_date) = '2023'
           - 最新10条: SELECT * FROM conference_extract
             WHERE conference_name LIKE '%{project_name}%' ORDER BY conference_date DESC LIMIT 10

        # 表结构
        {self.conference_schema}
          
        # 重要约束
        - 禁止使用 start_date 字段
        - 无金额字段
        - 最新排序使用 ORDER BY date DESC
        """
        
        # 获取LLM响应
        response = await llm.chat([{"role": "user", "content": prompt}])
        
        # 解析响应
        query_info = self._parse_response(response, "conference_extract")
        
        # 备用查询生成
        if not query_info or not self._is_valid_query(query_info["query"]):
            print("启用文书备用查询")
            query_info = {
                "table": "conference_extract",
                "query": f"SELECT * FROM conference_extract WHERE conference_name LIKE '%{project_name}%' ORDER BY conference_date DESC LIMIT 1",
                "type": "normal"
            }
        
        return self._execute_query(query_info)

    
    def _parse_response(self, response: str, expected_table: str) -> Optional[Dict]:
        """解析LLM响应"""
        try:
            data = load_json(response)
            if not isinstance(data, dict):
                return None
                
            # 验证表名和查询存在性
            if data.get("table") != expected_table:
                print(f"表名不匹配，预期: {expected_table}, 实际: {data.get('table')}")
                return None
                
            if not data.get("query") or len(data["query"]) < 20:
                print("查询语句过短或缺失")
                return None
                
            return data
        except Exception as e:
            print(f"响应解析失败: {str(e)}")
            return None

    def _process_data(self, raw_data: List[Dict]) -> List[Dict]:
        """数据空值处理"""
        return [
            {k: (v if v is not None else "暂无数据") 
             for k, v in row.items()}
            for row in raw_data
        ]
       
    def _execute_query(self, query_info: Dict) -> Dict[str, Any]:
        """执行查询并格式化"""
        try:
            raw_data = self.db.query(query_info["query"])
            processed_data = self._process_data(raw_data)
            
            return {
                "project_data": processed_data if query_info["table"] == "project_extract" else [],
                "conference_data": processed_data if query_info["table"] == "conference_extract" else [],
                "formatted_context": self._format_data(processed_data, query_info["table"],query_info["type"])
            }
        except Exception as e:
            print(f"查询执行失败: {str(e)}")
            return {
                "project_data": [],
                "conference_data": [],
                "formatted_context": "数据获取失败，请检查查询条件"
            }
        
    
    def _format_data(self, data: List[Dict], table: str, type: str) -> str:
        """数据格式化"""
        if table == "conference_extract":
            return self._format_conference_data(data, type)
        return self._format_project_data(data, type)


    async def get_context_o(self, question: str, project_name: str, project_type: str, year: str, keywords: List[str], llm: Any) -> Dict[str, Any]:
        """获取问题相关的上下文"""
        try:
            # ================ 1. 根据项目类型选择目标表和日期字段 ================
            target_table = "conference_extract" if project_type == "文书档案" else "project_extract"
            date_field = "conference_date" if target_table == "conference_extract" else "start_date"  # 新增日期字段映射
            print(f"根据项目类型[{project_type}]选择表: {target_table}, 日期字段: {date_field}")

            # ================ 2. 修改后的年份提取逻辑 ================
            year_range_match = re.search(r'(\d{4})[-至到](\d{4})年', question)
            year_single_match = re.search(r'(\d{4})年', question)
            
            start_year = None
            end_year = None
            year_condition = None
            
            if year_range_match:
                start_year = year_range_match.group(1)
                end_year = year_range_match.group(2)
                year_condition = f"strftime('%Y', {date_field}) BETWEEN '{start_year}' AND '{end_year}'"  # 使用动态字段
            elif year_single_match:
                year = year_single_match.group(1)
                year_condition = f"strftime('%Y', {date_field}) = '{year}'"  # 使用动态字段
            else:
                year_condition = None
            # 获取LLM生成的SQL查询
            prompt = (
                f"根据用户问题生成合适的SQL查询。\n\n"
                f"用户问题：\"{question}\"\n"
                f"项目名称：\"{project_name}\"\n"
                f"项目类型：\"{project_type}\"\n"
                f"目标表：\"{target_table}\"\n"
                f"年份条件：\"{year_condition if year_condition else ''}\"\n\n"
                f"数据库表结构：\n"
                f"{self.project_schema if target_table == 'project_extract' else self.conference_schema}\n\n"
                f"重要说明：\n"
                f"1. total_investment 字段在数据库中已经统一使用\"元\"作为单位，无需进行单位转换\n"
                f"2. start_date, end_date, date 字段都是DATE类型，必须使用strftime函数进行日期过滤\n"
                f"3. 如果问题或参数中包含年份，必须直接使用该年份值（不要使用参数占位符 ?）;如果问题或参数中不包含年份，不要添加任何日期过滤条件\n" 
                f"4. 年份值应该直接写在SQL中，例如：strftime('%Y', start_date) = '2017'\n"
                f"5. 金额条件必须保持原始单位，例如：\n"
                f"   - 100万 -> total_investment > 1000000\n"
                f"   - 1亿 -> total_investment > 100000000\n"
                f"   不要自行转换单位\n\n"
                f"查询规则：\n"
                f"1. 当用户提供项目名称时（非空），必须添加：{target_table.replace('_extract', '_name')} LIKE '%{{project_name}}%'\n"
                f"2. 名称查询必须返回完整记录，使用SELECT *\n"
                f"3. 统计类查询仅在问题包含'统计'、'总数'等关键词时使用\n\n"
                f"4. 对于统计类查询（如总金额、项目数量、年度统计等），不使用项目名称条件\n"
                f"5. 对于具体项目信息查询，使用项目名称作为条件：{target_table.replace('_extract', '_name')} LIKE '%项目名称%'\n"
                f"6. 日期查询必须使用strftime函数，不能使用LIKE\n"
                f"7. 年份查询必须使用传入的年份参数，不要使用示例中的年份\n\n"
                f"输出格式：\n"
                f"{{\n"
                f"    \"table\": \"project_extract\" 或 \"conference_extract\",\n"
                f"    \"query\": \"SQL查询语句\",\n"
                f"    \"type\": \"normal\" 或 \"aggregate\"\n"
                f"}}\n\n"
                f"查询示例：\n"
                f"新增示例：\n"
                f"- 项目精确查询：SELECT * FROM project_extract WHERE project_name = '{project_name}' LIMIT 1\n"
                f"- 文书模糊查询：SELECT * FROM conference_extract WHERE conference_name LIKE '%{project_name}%'\n"
                f"- 带年份的名称查询：SELECT * FROM conference_extract WHERE conference_name LIKE '%年度报告%' AND strftime('%Y', conference_date) = '2022'\n"
                f"1. project_extract表示例：\n"
                f"   - 年度总金额（单年）：SELECT strftime('%Y', start_date) as year, SUM(total_investment) as total FROM project_extract WHERE strftime('%Y', start_date) = '2020' GROUP BY year\n"
                f"   - 年度总金额（年份范围）：SELECT strftime('%Y', start_date) as year, SUM(total_investment) as total FROM project_extract WHERE strftime('%Y', start_date) BETWEEN '2017' AND '2019' GROUP BY year\n"
                f"   - 年度总金额（年份范围汇总）：SELECT SUM(total_investment) as total FROM project_extract WHERE strftime('%Y', start_date) BETWEEN '2017' AND '2019'\n"
                f"   - 月度统计：SELECT strftime('%Y-%m', start_date) as month, SUM(total_investment) as total FROM project_extract WHERE strftime('%Y', start_date) = '{year}' GROUP BY month\n"
                f"   - 项目数量：SELECT strftime('%Y', start_date) as year, COUNT(*) as count FROM project_extract GROUP BY year\n"
                f"   - 金额过滤：SELECT * FROM project_extract WHERE total_investment > 1000000  # 100万\n"
                f"   - 金额范围：SELECT * FROM project_extract WHERE total_investment BETWEEN 1000000 AND 10000000  # 100万到1000万\n"
                f"   - 项目投资：SELECT total_investment FROM project_extract WHERE project_name LIKE '%项目名称%'\n"
                f"   - 项目详情：SELECT * FROM project_extract WHERE project_name LIKE '%项目名称%'\n\n"
                f"2. conference_extract表示例：\n"
                f"   - 年度文书数量：SELECT strftime('%Y', conference_date) as year, COUNT(*) as count FROM conference_extract GROUP BY year\n"
                f"   - 查询某组织文书：SELECT * FROM conference_extract WHERE organizer LIKE '%科技局%' AND strftime('%Y', conference_date) = '2021'\n"
                f"   - 跨年会议查询：SELECT conference_name, conference_date, organizer FROM conference_extract WHERE strftime('%Y', conference_date) BETWEEN '2020' AND '2022'\n"
                f"   - 月度会议统计：SELECT strftime('%Y-%m', conference_date) as month, COUNT(*) FROM conference_extract GROUP BY month\n"
                f"   - 文书详情查询：SELECT conference_name, conference_date, organizer, participants FROM conference_extract WHERE conference_name LIKE '%年度总结%'\n"
                f"   - 最新10条文书：SELECT * FROM conference_extract ORDER BY conference_date DESC LIMIT 10\n\n"
                f"3. 日期过滤正确示例（conference_extract表）：\n"
                f"   - 年份：strftime('%Y', conference_date) = '2020'\n"
                f"   - 月份：strftime('%Y-%m', conference_date) = '2023-08'\n"
                f"   - 季度：strftime('%Y', conference_date) || '-' || ((strftime('%m', conference_date)-1)/3 + 1) AS quarter\n"
                f"   - 日期范围：strftime('%Y-%m-%d', conference_date) BETWEEN '2022-01-01' AND '2022-12-31'\n\n"
                f"4. 常见错误示例：\n"
                f"   - ❌ 错误使用start_date字段：SELECT * FROM conference_extract WHERE strftime('%Y', start_date) = '2020'\n"
                f"   - ❌ 缺少strftime函数：SELECT * FROM conference_extract WHERE conference_date > '2020'\n"
                f"   - ❌ 错误字段引用：SELECT total_investment FROM conference_extract  # 该表无此字段\n\n"
                f"注意：\n"
                f"1. 只返回JSON格式的响应\n"
                f"2. total_investment 字段值已经是\"元\"，不要做任何单位转换\n"
                f"3. 所有日期过滤必须使用strftime函数\n"
                f"4. 分析问题是统计类还是具体项目查询，据此决定是否使用项目名称条件"
                f"5. 必须查询{target_table}表\n"
                f"6. 项目类型为{project_type}时使用对应表结构"
                f"7. 若果问题中没有明显的过滤条件，那么就查询"
            )
            response = await llm.chat([
                    {
                        "role": "system", 
                        "content": "你是一个SQL专家，当前需要查询{target_table}表，帮助生成准确的SQL查询语句。"
                                "注意：项目类型为{project_type}时必须使用{target_table}表,如果用户问题中没有明确年份要求，不要添加任何日期过滤条件\n"
                                "1. 如果是统计分析，不要使用项目名称条件"
                                "2. 如果是查询具体项目信息，必须使用项目名称条件"
                                "3. total_investment 字段已经是\"元\"为单位，不要进行单位转换"
                                "4. 所有日期过滤必须使用strftime函数"
                                "5. 年份范围查询必须使用 BETWEEN AND"
                                "6. 必须使用提供的年份条件进行查询"
                                "7. 金额条件必须使用原始单位，100万应该写作 1000000"
                    },
                    {"role": "user", "content": prompt}
                ])
            
            try:
                query_info = load_json(response)
                print(f"LLM Response: {response}")  # 添加日志
                print(f"Parsed query_info: {query_info}")  # 添加日志
                
                if not isinstance(query_info, dict):
                    print(f"Invalid query_info format: {query_info}")  # 添加日志
                    return {"project_data": [], "conference_data": [], "formatted_context": ""}
                
                table = query_info.get("table")
                query = query_info.get("query")
                query_type = query_info.get("type", "normal")
                
                print(f"Extracted values - table: {table}, query: {query}, type: {query_type}")  # 添加日志
                
                if not table or not query:
                    print(f"Missing table or query in response")  # 添加日志
                    return {"project_data": [], "conference_data": [], "formatted_context": ""}
                    
                # 强制使用正确的年份条件
                if year_condition and year_condition.strip():
                    original_query = query
                    # 正则表达式增强，匹配各种空格情况
                    query = re.sub(
                        r"WHERE\s+strftime\s*\(\s*'%Y'\s*,\s*{date_field}\s*\)\s*=\s*'\d{4}'\s*".format(date_field=date_field),
                        f"WHERE {year_condition} ",
                        query,
                        flags=re.IGNORECASE
                    )
                    query = re.sub(
                        r"WHERE\s+strftime\s*\(\s*'%Y'\s*,\s*{date_field}\s*\)\s*BETWEEN\s*'\d{4}'\s*AND\s*'\d{4}'\s*".format(date_field=date_field),
                        f"WHERE {year_condition} ",
                        query,
                        flags=re.IGNORECASE
                    )
                    
                    # 如果没有 WHERE 子句，添加年份条件
                    if "WHERE" not in query.upper():
                        before_group = query.upper().split("GROUP BY")[0] if "GROUP BY" in query.upper() else query
                        if "WHERE" not in before_group:
                            query = query.replace(
                                "FROM project_extract",
                                f"FROM project_extract WHERE {year_condition}"
                            )
                    
                    print(f"Query transformation:")  # 添加日志
                    print(f"Original: {original_query}")
                    print(f"Modified: {query}")
                
                # 执行查询获取原始数据
                results = self.db.query(query)
                
                # 处理None值
                processed_results = []
                for row in results:
                    processed_row = {}
                    for key, value in row.items():
                        processed_row[key] = value if value is not None else ""
                    processed_results.append(processed_row)
                
                # 根据表名和查询类型格式化结果
                formatted_context = ""
                project_data = []
                conference_data = []
                
                if table == "project_extract":
                    project_data = processed_results
                    formatted_context = self._format_project_data(processed_results, query_type)
                elif table == "conference_extract":
                    conference_data = processed_results
                    formatted_context = self._format_conference_data(processed_results)
                
                return {
                    "project_data": project_data,
                    "conference_data": conference_data,
                    "formatted_context": formatted_context
                }
                
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {str(e)}")  # 添加日志
                print(f"Raw response: {response}")  # 添加日志
                return {"project_data": [], "conference_data": [], "formatted_context": ""}
                
        except Exception as e:
            print(f"Error in get_context: {str(e)}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")  # 添加详细的错误追踪
            return {
                "project_data": [],
                "conference_data": [],
                "formatted_context": ""
            }


    def _build_base_prompt(self, question: str, project_name: str, project_type: str) -> str:
        """构建提示词基础模板"""
        return f"""
        # 基础规则
        1. 必须生成有效的SELECT查询
        2. 必须使用LIKE进行名称模糊匹配
        3. 必须包含JSON响应格式
        
        # 用户信息
        问题：{question}
        项目名称：{project_name or "无"}
        项目类型：{project_type}
        
        # 响应格式
        {{
            "table": "project_extract/conference_extract",
            "query": "SQL语句",
            "type": "normal/aggregate"
        }}
        """

    def _build_project_prompt(self, question: str, project_name: str, schema: str, year_condition: str) -> str:
        """项目表专用提示组件"""
        return f"""
        # 项目档案专用规则
        1. 必须查询 project_extract 表
        2. 必须使用 start_date 字段进行日期过滤
        3. 金额字段：total_investment（单位：元）
        
        # 表结构
        {schema}
        
        # 专用示例
        1. 基础查询：SELECT * FROM project_extract WHERE project_name LIKE '%{project_name}%'
        2. 金额过滤：SELECT * FROM project_extract
        WHERE project_name LIKE '%{project_name}%' AND total_investment > 1000000
        3. 时间范围：SELECT project_name, start_date FROM project_extract
        WHERE project_name LIKE '%{project_name}%'
        AND {year_condition or "strftime('%Y', start_date) = '2023'"}
        
        # 重要约束
        - 禁止使用 date 字段
        - 金额单位必须保持元
        - 统计查询需包含 GROUP BY
        """


    def close(self):
        """关闭数据库连接"""
        if hasattr(self, 'conn'):
            self.conn.close()
        
    async def init_table(self, config: ExtractConfig):
        """初始化表结构"""
        try:
            if not await self.db.is_connected():
                await self.db.connect()

            await self.db.create_table(config.table_name, config.schema)
        except Exception as e:
            print(f"Error initializing table: {str(e)}")
            raise
        
    async def upsert(self, table: str, record: Dict[str, Any], key_fields: List[str]) -> bool:
        """插入或更新记录"""
        try:
            if not await self.db.is_connected():
                await self.db.connect()

            # 预处理每个字段
            processed_record = {}
            for field_name, value in record.items():
                try:
                    if value is None or value == "未提及":
                        # 处理空值和未提及的情况
                        if field_name in ['start_date', 'end_date', 'date']:
                            processed_record[field_name] = "1900-01-01"  # 使用有效的默认日期
                        elif field_name == 'total_investment':
                            processed_record[field_name] = 0
                        else:
                            processed_record[field_name] = "未提及"
                    else:
                        # 使用preprocess_field处理值
                        processed_value = preprocess_field(field_name, value)
                        processed_record[field_name] = processed_value
                except Exception as e:
                    print(f"Error preprocessing field {field_name}: {str(e)}")
                    # 设置默认值
                    if field_name in ['start_date', 'end_date', 'date']:
                        processed_record[field_name] = "1900-01-01"  # 使用有效的默认日期
                    elif field_name == 'total_investment':
                        processed_record[field_name] = 0
                    else:
                        processed_record[field_name] = "未提及"

            # 执行upsert操作
            return await self.db.upsert(table, processed_record, key_fields)

        except Exception as e:
            print(f"Error upserting record: {str(e)}")
            return False
            
    def delete_by_doc_id(self, table: str, doc_id: str) -> bool:
        """根据doc_id删除记录（用于文档级删除）"""
        try:
            if not self.db.is_connected():
                self.db.connect()

            # 根据数据库类型构建删除SQL
            if self.db_type == "sqlite":
                sql = f"DELETE FROM `{table}` WHERE doc_id = ?"
                result = self.db.execute_sql(sql, (doc_id,))
            else:  # MySQL
                sql = f"DELETE FROM `{table}` WHERE doc_id = %s"
                result = self.db.execute(sql, (doc_id,))
                result = True  # MySQL execute 返回结果列表，删除成功则返回True

            print(f"Deleted records from {table} where doc_id = {doc_id}")
            return result

        except Exception as e:
            print(f"Error deleting by doc_id from {table}: {str(e)}")
            return False

    def delete_by_id(self, table: str, record_id: int) -> bool:
        """根据id删除记录（用于项目级删除）"""
        try:
            if not self.db.is_connected():
                self.db.connect()

            # 根据数据库类型构建删除SQL
            if self.db_type == "sqlite":
                sql = f"DELETE FROM `{table}` WHERE id = ?"
                result = self.db.execute_sql(sql, (record_id,))
            else:  # MySQL
                sql = f"DELETE FROM `{table}` WHERE id = %s"
                result = self.db.execute(sql, (record_id,))
                result = True  # MySQL execute 返回结果列表，删除成功则返回True

            print(f"Deleted record from {table} where id = {record_id}")
            return result

        except Exception as e:
            print(f"Error deleting by id from {table}: {str(e)}")
            return False

    def delete_by_project_name(self, table: str, project_name: str) -> bool:
        """根据项目名称删除记录（用于项目级删除）- 适配新表结构"""
        try:
            if not self.db.is_connected():
                self.db.connect()

            # 根据表类型确定字段名
            if table == "project_extract":
                field_name = "project_name"
            elif table == "conference_extract":
                field_name = "document_name"
            else:
                # 兼容旧表结构
                field_name = "name"

            # 根据数据库类型构建删除SQL
            if self.db_type == "sqlite":
                sql = f"DELETE FROM `{table}` WHERE {field_name} = ?"
                result = self.db.execute_sql(sql, (project_name,))
            else:  # MySQL
                sql = f"DELETE FROM `{table}` WHERE {field_name} = %s"
                result = self.db.execute(sql, (project_name,))
                result = True  # MySQL execute 返回结果列表，删除成功则返回True

            print(f"Deleted records from {table} where {field_name} = {project_name}")
            return result

        except Exception as e:
            print(f"Error deleting by project name from {table}: {str(e)}")
            return False

    def delete_by_key(self, table: str, key_value: str) -> bool:
        """根据唯一键删除记录（适配新表结构）"""
        try:
            if not self.db.is_connected():
                self.db.connect()

            # 根据表类型确定键字段名
            if table == "project_extract":
                key_field = "project_key"
            elif table == "conference_extract":
                key_field = "document_key"
            else:
                # 兼容旧表结构，使用doc_id
                key_field = "doc_id"

            # 根据数据库类型构建删除SQL
            if self.db_type == "sqlite":
                sql = f"DELETE FROM `{table}` WHERE {key_field} = ?"
                result = self.db.execute_sql(sql, (key_value,))
            else:  # MySQL
                sql = f"DELETE FROM `{table}` WHERE {key_field} = %s"
                result = self.db.execute(sql, (key_value,))
                result = True  # MySQL execute 返回结果列表，删除成功则返回True

            print(f"Deleted records from {table} where {key_field} = {key_value}")
            return result

        except Exception as e:
            print(f"Error deleting by key from {table}: {str(e)}")
            return False

    def query(self, sql: str) -> List[Dict[str, Any]]:
        """执行SQL查询"""
        try:
            if not self.db.is_connected():
                self.db.connect()

            return self.db.query(sql)
        except Exception as e:
            print(f"Error executing query: {str(e)}")
            return []
            
    def close(self):
        """关闭连接"""
        try:
            if self.db and self.db.is_connected():
                self.db.close()
        except Exception as e:
            print(f"Error closing database connection: {str(e)}") 