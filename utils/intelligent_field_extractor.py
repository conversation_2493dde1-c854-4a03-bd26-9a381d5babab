#!/usr/bin/env python3
"""
智能字段抽取器
基于多维度检索的智能化信息抽取
"""

import re
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict

from utils.llm import LLM
from utils.log import log
from utils.config import config
from utils.compatible_retriever import CompatibleRetriever


class IntelligentFieldExtractor:
    """智能字段抽取器"""
    
    def __init__(self):
        self.llm = LLM()
        self.retriever = CompatibleRetriever()
        
        # 抽取配置
        self.max_chunks_per_field = config.get("field_extractor.max_chunks_per_field", 10)
        self.confidence_threshold = config.get("field_extractor.confidence_threshold", 0.7)
        
        # 字段抽取配置
        self.field_extraction_config = self._load_field_config()
        
        self._initialized = False
    
    async def initialize(self):
        """初始化抽取器"""
        if not self._initialized:
            await self.retriever.initialize()
            self._initialized = True
            log.info("智能字段抽取器初始化完成")
    
    def _load_field_config(self) -> Dict[str, Any]:
        """加载字段抽取配置"""
        return {
            "项目档案": {
                "project_name": {
                    "keywords": ["项目名称", "项目", "工程名称", "建设项目"],
                    "semantic_query": "项目的正式名称是什么",
                    "patterns": [
                        r"项目名称[：:]\s*(.+?)(?:\n|$)",
                        r"(.+?)项目",
                        r"(.+?)工程",
                        r"关于(.+?)的"
                    ],
                    "field_type": "text",
                    "required": True
                },
                "project_no": {
                    "keywords": ["项目编号", "编号", "项目代码", "项目号"],
                    "semantic_query": "项目的编号或代码",
                    "patterns": [
                        r"项目编号[：:]\s*([A-Z0-9\-]+)",
                        r"编号[：:]\s*([A-Z0-9\-]+)",
                        r"项目代码[：:]\s*([A-Z0-9\-]+)"
                    ],
                    "field_type": "code",
                    "required": False
                },
                "responsible_unit": {
                    "keywords": ["承担单位", "负责单位", "实施单位", "建设单位", "申请单位"],
                    "semantic_query": "负责实施这个项目的单位或组织",
                    "patterns": [
                        r"承担单位[：:]\s*(.+?)(?:\n|$)",
                        r"负责单位[：:]\s*(.+?)(?:\n|$)",
                        r"实施单位[：:]\s*(.+?)(?:\n|$)",
                        r"建设单位[：:]\s*(.+?)(?:\n|$)"
                    ],
                    "field_type": "text",
                    "required": True
                },
                "leader": {
                    "keywords": ["项目负责人", "负责人", "项目经理", "项目主管"],
                    "semantic_query": "项目的负责人或项目经理是谁",
                    "patterns": [
                        r"项目负责人[：:]\s*([^\s\n]+)",
                        r"负责人[：:]\s*([^\s\n]+)",
                        r"项目经理[：:]\s*([^\s\n]+)"
                    ],
                    "field_type": "person",
                    "required": True
                },
                "total_investment": {
                    "keywords": ["项目总投资", "总金额", "总预算", "项目投资", "总投资"],
                    "semantic_query": "项目的总投资金额或总预算是多少",
                    "patterns": [
                        r"总投资[：:]?\s*([￥¥]?[\d,]+\.?\d*[万亿]?元?)",
                        r"总金额[：:]?\s*([￥¥]?[\d,]+\.?\d*[万亿]?元?)",
                        r"总预算[：:]?\s*([￥¥]?[\d,]+\.?\d*[万亿]?元?)",
                        r"人民币([壹贰叁肆伍陆柒捌玖拾佰仟万亿]+)元整",
                        r"([￥¥]?[\d,]+\.?\d*[万亿]?元)"
                    ],
                    "field_type": "money",
                    "required": True
                },
                "start_date": {
                    "keywords": ["开始时间", "开工时间", "启动时间", "立项时间"],
                    "semantic_query": "项目开始实施的时间",
                    "patterns": [
                        r"开始时间[：:]?\s*(\d{4}年\d{1,2}月\d{1,2}日|\d{4}年\d{1,2}月|\d{4}年)",
                        r"开工时间[：:]?\s*(\d{4}年\d{1,2}月\d{1,2}日|\d{4}年\d{1,2}月|\d{4}年)",
                        r"立项时间[：:]?\s*(\d{4}年\d{1,2}月\d{1,2}日|\d{4}年\d{1,2}月|\d{4}年)"
                    ],
                    "field_type": "date",
                    "required": False
                },
                "end_date": {
                    "keywords": ["完成时间", "竣工时间", "结束时间", "预计完成"],
                    "semantic_query": "项目预计完成的时间",
                    "patterns": [
                        r"完成时间[：:]?\s*(\d{4}年\d{1,2}月\d{1,2}日|\d{4}年\d{1,2}月|\d{4}年)",
                        r"竣工时间[：:]?\s*(\d{4}年\d{1,2}月\d{1,2}日|\d{4}年\d{1,2}月|\d{4}年)",
                        r"结束时间[：:]?\s*(\d{4}年\d{1,2}月\d{1,2}日|\d{4}年\d{1,2}月|\d{4}年)"
                    ],
                    "field_type": "date",
                    "required": False
                }
            },
            "文书档案": {
                "name": {
                    "keywords": ["会议名称", "通知", "纪要", "通报", "复函"],
                    "semantic_query": "会议名称或文档标题",
                    "patterns": [
                        r"关于(.+?)的通知",
                        r"(.+?)会议纪要",
                        r"(.+?)通报"
                    ],
                    "field_type": "text",
                    "required": True
                },
                "date": {
                    "keywords": ["会议时间", "开会时间", "会议日期", "发布时间"],
                    "semantic_query": "会议开始时间或文书发布时间",
                    "patterns": [
                        r"(\d{4}年\d{1,2}月\d{1,2}日)",
                        r"(\d{4}-\d{1,2}-\d{1,2})"
                    ],
                    "field_type": "date",
                    "required": False
                }
            }
        }
    
    async def extract_project_fields(
        self,
        project_name: str,
        action: str,
        target_fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """抽取项目字段信息"""
        try:
            await self.initialize()
            
            log.info(f"开始智能字段抽取: {project_name}, 类型: {action}")
            
            # 获取字段配置
            action_config = self.field_extraction_config.get(action, {})
            if not action_config:
                return {"error": f"不支持的汇编类型: {action}"}
            
            # 确定要抽取的字段
            fields_to_extract = target_fields or list(action_config.keys())
            
            # 并行抽取各个字段
            extraction_tasks = []
            for field_name in fields_to_extract:
                if field_name in action_config:
                    task = self._extract_single_field(
                        field_name,
                        action_config[field_name],
                        project_name,
                        action
                    )
                    extraction_tasks.append((field_name, task))
            
            # 等待所有抽取任务完成
            extracted_fields = {}
            field_analysis = {}
            
            for field_name, task in extraction_tasks:
                try:
                    field_result = await task
                    extracted_fields[field_name] = field_result["value"]
                    field_analysis[field_name] = field_result["analysis"]
                except Exception as e:
                    log.error(f"抽取字段失败 {field_name}: {e}")
                    extracted_fields[field_name] = None
                    field_analysis[field_name] = {"error": str(e)}
            
            # 生成最终结果
            result = {
                "project_name": project_name,
                "action": action,
                "extracted_fields": extracted_fields,
                "field_analysis": field_analysis,
                "extraction_method": "intelligent_multi_strategy",
                "extraction_time": datetime.now().isoformat(),
                "total_fields": len(fields_to_extract),
                "successful_fields": len([v for v in extracted_fields.values() if v is not None])
            }
            
            log.info(f"字段抽取完成: {project_name}, 成功{result['successful_fields']}/{result['total_fields']}")
            return result
            
        except Exception as e:
            log.error(f"智能字段抽取失败: {e}")
            return {"error": str(e)}
    
    async def _extract_single_field(
        self,
        field_name: str,
        field_config: Dict[str, Any],
        project_name: str,
        action: str
    ) -> Dict[str, Any]:
        """抽取单个字段"""
        try:
            # 第一步：多维度检索相关文档片段
            relevant_chunks = await self._retrieve_relevant_chunks(
                field_config, project_name, action
            )
            
            if not relevant_chunks:
                return {
                    "value": None,
                    "analysis": {
                        "method": "no_relevant_chunks",
                        "confidence": 0.0,
                        "searched_chunks": 0
                    }
                }
            
            # 第二步：多策略抽取
            extraction_results = await self._multi_strategy_extraction(
                field_name, field_config, relevant_chunks
            )
            
            # 第三步：结果融合和验证
            final_result = await self._merge_and_validate_results(
                field_name, field_config, extraction_results, relevant_chunks
            )
            
            return final_result
            
        except Exception as e:
            log.error(f"抽取单个字段失败 {field_name}: {e}")
            return {
                "value": None,
                "analysis": {"error": str(e)}
            }
    
    async def _retrieve_relevant_chunks(
        self,
        field_config: Dict[str, Any],
        project_name: str,
        action: str
    ) -> List[Dict[str, Any]]:
        """检索相关文档片段"""
        try:
            # 使用混合检索获取最相关的文档片段
            keywords = field_config.get("keywords", [])
            semantic_query = field_config.get("semantic_query", "")
            
            chunks = await self.retriever.hybrid_search(
                keywords=keywords,
                semantic_query=semantic_query,
                project_name=project_name,
                action=action,
                size=self.max_chunks_per_field,
                min_score=0.1  # 降低阈值以获取更多候选
            )
            
            log.debug(f"检索到{len(chunks)}个相关片段")
            return chunks
            
        except Exception as e:
            log.error(f"检索相关片段失败: {e}")
            return []
    
    async def _multi_strategy_extraction(
        self,
        field_name: str,
        field_config: Dict[str, Any],
        chunks: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """多策略抽取"""
        try:
            extraction_results = []
            
            # 策略1：正则表达式抽取
            regex_results = self._extract_with_regex(field_config, chunks)
            extraction_results.extend(regex_results)
            
            # 策略2：LLM智能抽取
            llm_results = await self._extract_with_llm(field_name, field_config, chunks)
            extraction_results.extend(llm_results)
            
            # 策略3：基于字段类型的专门抽取
            specialized_results = await self._extract_with_specialized_method(
                field_name, field_config, chunks
            )
            extraction_results.extend(specialized_results)
            
            return extraction_results
            
        except Exception as e:
            log.error(f"多策略抽取失败: {e}")
            return []
    
    def _extract_with_regex(
        self,
        field_config: Dict[str, Any],
        chunks: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """正则表达式抽取"""
        results = []
        patterns = field_config.get("patterns", [])
        
        for chunk in chunks:
            content = chunk.get("content", "")
            
            for pattern in patterns:
                try:
                    matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[0] if match else ""
                        
                        if match and match.strip():
                            results.append({
                                "value": match.strip(),
                                "method": "regex",
                                "confidence": 0.9,  # 正则匹配置信度高
                                "source_chunk": chunk.get("doc_id", ""),
                                "source_score": chunk.get("score", 0),
                                "pattern": pattern
                            })
                except Exception as e:
                    log.debug(f"正则匹配失败: {e}")
                    continue
        
        return results
    
    async def _extract_with_llm(
        self,
        field_name: str,
        field_config: Dict[str, Any],
        chunks: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """LLM智能抽取"""
        try:
            if not chunks:
                return []
            
            # 构建上下文
            context_texts = []
            for i, chunk in enumerate(chunks[:5]):  # 限制上下文长度
                content = chunk.get("content", "")
                source = chunk.get("source", "")
                context_texts.append(f"[文档{i+1}: {source}]\n{content}")
            
            context = "\n\n".join(context_texts)
            
            # 构建抽取提示词
            field_type = field_config.get("field_type", "text")
            semantic_query = field_config.get("semantic_query", "")
            
            extraction_prompt = f"""
请从以下文档片段中准确提取"{field_name}"的信息。

字段说明：{semantic_query}
字段类型：{field_type}

文档片段：
{context}

提取要求：
1. 如果找到多个可能的值，请选择最准确、最完整的一个
2. 如果没有找到相关信息，请返回"未找到"
3. 保持原文的表述方式，不要过度解释
4. 对于金额类型，请保留完整的数字和单位
5. 对于日期类型，请使用标准格式
6. 对于人名，请只返回姓名，不包括职务

请直接返回提取到的值，不要添加额外说明：
"""
            
            response = await self.llm.chat([
                {"role": "system", "content": "你是专业的信息提取专家，请准确提取指定字段的信息。"},
                {"role": "user", "content": extraction_prompt}
            ])
            
            if response and response.strip() and response.strip() != "未找到":
                return [{
                    "value": response.strip(),
                    "method": "llm_extraction",
                    "confidence": 0.8,  # LLM提取置信度
                    "source_chunks": len(chunks),
                    "context_length": len(context)
                }]
            
            return []
            
        except Exception as e:
            log.error(f"LLM抽取失败: {e}")
            return []
    
    async def _extract_with_specialized_method(
        self,
        field_name: str,
        field_config: Dict[str, Any],
        chunks: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """基于字段类型的专门抽取方法"""
        try:
            field_type = field_config.get("field_type", "text")
            
            if field_type == "money":
                return self._extract_money_amounts(chunks)
            elif field_type == "date":
                return self._extract_dates(chunks)
            elif field_type == "person":
                return self._extract_person_names(chunks)
            else:
                return []
                
        except Exception as e:
            log.error(f"专门抽取方法失败: {e}")
            return []
    
    def _extract_money_amounts(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """专门抽取金额"""
        results = []
        money_patterns = [
            r'([￥¥]?[\d,]+\.?\d*[万亿]?元)',
            r'人民币([壹贰叁肆伍陆柒捌玖拾佰仟万亿]+)元整',
            r'总投资[：:]?\s*([￥¥]?[\d,]+\.?\d*[万亿]?元?)',
            r'(\d+\.?\d*[万亿]元)'
        ]
        
        for chunk in chunks:
            content = chunk.get("content", "")
            for pattern in money_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    if match and match.strip():
                        results.append({
                            "value": match.strip(),
                            "method": "specialized_money",
                            "confidence": 0.85,
                            "source_chunk": chunk.get("doc_id", "")
                        })
        
        return results
    
    def _extract_dates(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """专门抽取日期"""
        results = []
        date_patterns = [
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
            r'(\d{4}年\d{1,2}月)',
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})'
        ]
        
        for chunk in chunks:
            content = chunk.get("content", "")
            for pattern in date_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if match and match.strip():
                        results.append({
                            "value": match.strip(),
                            "method": "specialized_date",
                            "confidence": 0.85,
                            "source_chunk": chunk.get("doc_id", "")
                        })
        
        return results
    
    def _extract_person_names(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """专门抽取人名"""
        results = []
        name_patterns = [
            r'负责人[：:]\s*([^\s\n，,；;]{2,4})',
            r'项目经理[：:]\s*([^\s\n，,；;]{2,4})',
            r'主管[：:]\s*([^\s\n，,；;]{2,4})'
        ]
        
        for chunk in chunks:
            content = chunk.get("content", "")
            for pattern in name_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if match and match.strip() and len(match.strip()) >= 2:
                        results.append({
                            "value": match.strip(),
                            "method": "specialized_person",
                            "confidence": 0.85,
                            "source_chunk": chunk.get("doc_id", "")
                        })
        
        return results
    
    async def _merge_and_validate_results(
        self,
        field_name: str,
        field_config: Dict[str, Any],
        extraction_results: List[Dict[str, Any]],
        chunks: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """合并和验证抽取结果"""
        try:
            if not extraction_results:
                return {
                    "value": None,
                    "analysis": {
                        "method": "no_extraction_results",
                        "confidence": 0.0,
                        "searched_chunks": len(chunks)
                    }
                }
            
            # 按置信度排序
            sorted_results = sorted(
                extraction_results,
                key=lambda x: x.get("confidence", 0),
                reverse=True
            )
            
            # 选择最佳结果
            best_result = sorted_results[0]
            
            # 计算最终置信度
            final_confidence = self._calculate_final_confidence(
                best_result, sorted_results, field_config
            )
            
            return {
                "value": best_result["value"],
                "analysis": {
                    "method": best_result["method"],
                    "confidence": final_confidence,
                    "alternative_count": len(sorted_results) - 1,
                    "searched_chunks": len(chunks),
                    "extraction_details": {
                        "primary_method": best_result["method"],
                        "source_score": best_result.get("source_score", 0),
                        "all_methods": list(set(r["method"] for r in sorted_results))
                    }
                }
            }
            
        except Exception as e:
            log.error(f"合并验证结果失败: {e}")
            return {
                "value": None,
                "analysis": {"error": str(e)}
            }
    
    def _calculate_final_confidence(
        self,
        best_result: Dict[str, Any],
        all_results: List[Dict[str, Any]],
        field_config: Dict[str, Any]
    ) -> float:
        """计算最终置信度"""
        base_confidence = best_result.get("confidence", 0.5)
        
        # 如果多个方法得到相同结果，提高置信度
        same_value_count = len([
            r for r in all_results 
            if r["value"] == best_result["value"]
        ])
        
        if same_value_count > 1:
            base_confidence = min(base_confidence + 0.1 * (same_value_count - 1), 1.0)
        
        # 如果是必需字段且找到结果，提高置信度
        if field_config.get("required", False) and best_result["value"]:
            base_confidence = min(base_confidence + 0.05, 1.0)
        
        return round(base_confidence, 2)
    
    async def get_extractor_stats(self) -> Dict[str, Any]:
        """获取抽取器统计信息"""
        try:
            return {
                "extractor_info": {
                    "type": "intelligent_field_extractor",
                    "initialized": self._initialized,
                    "max_chunks_per_field": self.max_chunks_per_field,
                    "confidence_threshold": self.confidence_threshold
                },
                "supported_actions": list(self.field_extraction_config.keys()),
                "extraction_strategies": [
                    "regex_extraction",
                    "llm_extraction", 
                    "specialized_extraction",
                    "result_fusion"
                ]
            }
            
        except Exception as e:
            log.error(f"获取抽取器统计失败: {e}")
            return {"error": str(e)}
