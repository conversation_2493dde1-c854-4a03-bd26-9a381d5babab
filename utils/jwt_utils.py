#!/usr/bin/env python3
"""
统一JWT工具类
所有服务使用相同的JWT加密解密逻辑和密钥
"""

import jwt
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional
from utils.config import config
from utils.log import log

class UnifiedJWTService:
    """统一JWT服务类"""
    
    def __init__(self):
        # 统一从配置文件读取JWT配置
        self.secret_key = config.get("service_auth.jwt.secret_key")
        self.algorithm = config.get("service_auth.jwt.algorithm", "HS256")
        self.expire_minutes = config.get("service_auth.jwt.expire_minutes", 30)
        self.issuer = config.get("service_auth.jwt.issuer", "hngpt")
        
        if not self.secret_key:
            raise ValueError("JWT密钥未配置，请检查config.yaml中的service_auth.jwt.secret_key")
    
    def encode_token(self, payload: Dict[str, Any], expire_minutes: Optional[int] = None) -> str:
        """
        生成JWT token
        
        Args:
            payload: token载荷数据
            expire_minutes: 过期时间（分钟），默认使用配置值
            
        Returns:
            JWT token字符串
        """
        try:
            # 使用传入的过期时间或默认配置
            expire_time = expire_minutes or self.expire_minutes
            now = datetime.now(timezone.utc)
            
            # 添加标准JWT字段
            import uuid
            token_payload = payload.copy()
            token_payload.update({
                'iss': self.issuer,  # 统一签发方
                'iat': int(now.timestamp()),  # 签发时间
                'exp': int((now + timedelta(minutes=expire_time)).timestamp()),  # 过期时间
                'jti': str(uuid.uuid4()),  # Token唯一标识（防重放）
            })
            
            # 生成token
            token = jwt.encode(token_payload, self.secret_key, algorithm=self.algorithm)
            
            log.debug(f"JWT token生成成功，过期时间: {expire_time}分钟")
            return token
            
        except Exception as e:
            log.error(f"JWT token生成失败: {str(e)}")
            raise
    
    def decode_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        解码JWT token
        
        Args:
            token: JWT token字符串
            
        Returns:
            解码后的payload，失败返回None
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            log.debug(f"JWT token解码成功，用户ID: {payload.get('user_id', 'N/A')}")
            return payload
            
        except jwt.ExpiredSignatureError:
            log.warning("JWT token已过期")
            return None
        except jwt.InvalidTokenError as e:
            log.warning(f"JWT token无效: {str(e)}")
            return None
        except Exception as e:
            log.error(f"JWT token解码异常: {str(e)}")
            return None
    
    def verify_token(self, token: str) -> bool:
        """
        验证JWT token是否有效
        
        Args:
            token: JWT token字符串
            
        Returns:
            True表示有效，False表示无效
        """
        return self.decode_token(token) is not None
    
    def get_token_info(self, token: str) -> Optional[Dict[str, Any]]:
        """
        获取token信息（不验证签名，仅用于调试）
        
        Args:
            token: JWT token字符串
            
        Returns:
            token信息字典
        """
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            
            # 格式化时间字段
            if 'exp' in payload:
                payload['exp_readable'] = datetime.fromtimestamp(payload['exp'], tz=timezone.utc).isoformat()
            if 'iat' in payload:
                payload['iat_readable'] = datetime.fromtimestamp(payload['iat'], tz=timezone.utc).isoformat()
            
            return payload
            
        except Exception as e:
            log.error(f"获取token信息失败: {str(e)}")
            return None
    
    def create_user_token(self, user_data: Dict[str, Any], expire_minutes: Optional[int] = None) -> str:
        """
        为用户创建标准格式的JWT token
        
        Args:
            user_data: 用户数据字典，必须包含user_id和username
            expire_minutes: 过期时间（分钟）
            
        Returns:
            JWT token字符串
        """
        if not user_data.get('user_id'):
            raise ValueError("用户数据必须包含user_id字段")
        if not user_data.get('user_name'):
            raise ValueError("用户数据必须包含username字段")
        
        # 构建标准用户token载荷
        payload = {
            # 用户标识（统一格式）
            'user_id': user_data['user_id'],
            'user_name': user_data['user_name'],
            'nick_name': user_data.get('nick_name', user_data['user_name']),
            
            # 部门信息
            'dept_id': user_data.get('dept_id'),
            'dept_name': user_data.get('dept_name'),
            
            # 角色权限 - 标准化处理
            'roles': self._normalize_user_roles(user_data.get('roles', [])),
            
            # 联系信息
            'email': user_data.get('email'),
            'phone': user_data.get('phone'),
            
            # 扩展信息
            'login_time': int(datetime.now(timezone.utc).timestamp()),
            'source': user_data.get('source', 'system'),
            'service_version': user_data.get('service_version', '1.0')
        }
        
        # 移除None值
        payload = {k: v for k, v in payload.items() if v is not None}
        
        return self.encode_token(payload, expire_minutes)

    def _normalize_user_roles(self, roles: list) -> list:
        """
        标准化用户角色处理

        Args:
            roles: 原始角色列表

        Returns:
            list: 标准化后的角色列表
        """
        if not roles:
            roles = []

        # 检查是否有标准角色
        has_admin = 'admin' in roles
        has_dept_admin = 'deptAdmin' in roles
        has_user = 'user' in roles

        # 如果没有任何标准角色，默认添加user角色
        if not (has_admin or has_dept_admin or has_user):
            log.info(f"用户角色为空或非标准角色 {roles}，默认添加user角色")
            roles = roles + ['user'] if roles else ['user']

        return roles


# 全局统一JWT服务实例
try:
    unified_jwt_service = UnifiedJWTService()
except Exception as e:
    log.error(f"初始化统一JWT服务失败: {str(e)}")
    unified_jwt_service = None


# 便捷函数
def encode_jwt_token(payload: Dict[str, Any], expire_minutes: Optional[int] = None) -> str:
    """生成JWT token的便捷函数"""
    if not unified_jwt_service:
        raise RuntimeError("统一JWT服务未初始化")
    return unified_jwt_service.encode_token(payload, expire_minutes)


def decode_jwt_token(token: str) -> Optional[Dict[str, Any]]:
    """解码JWT token的便捷函数"""
    if not unified_jwt_service:
        return None
    return unified_jwt_service.decode_token(token)


def verify_jwt_token(token: str) -> bool:
    """验证JWT token的便捷函数"""
    if not unified_jwt_service:
        return False
    return unified_jwt_service.verify_token(token)


def create_user_jwt_token(user_data: Dict[str, Any], expire_minutes: Optional[int] = None) -> str:
    """为用户创建JWT token的便捷函数"""
    if not unified_jwt_service:
        raise RuntimeError("统一JWT服务未初始化")
    return unified_jwt_service.create_user_token(user_data, expire_minutes)


# 导出
__all__ = [
    "UnifiedJWTService",
    "unified_jwt_service", 
    "encode_jwt_token",
    "decode_jwt_token", 
    "verify_jwt_token",
    "create_user_jwt_token"
]
