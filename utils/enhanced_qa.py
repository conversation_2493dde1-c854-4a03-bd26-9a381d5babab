#!/usr/bin/env python3
"""
增强问答系统
集成ES向量检索和MySQL结构化信息，实现多角度的智能检索和回答
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass

from utils.smart_retriever import <PERSON>R<PERSON>riever, SearchFilter, SearchResult
from utils.structured_storage import StructuredStorage
from utils.llm import LLM
from utils.logger import log


@dataclass
class QAContext:
    """问答上下文"""
    vector_results: List[SearchResult]
    structured_data: Dict[str, Any]
    project_name: Optional[str] = None
    doc_type: Optional[str] = None
    confidence_score: float = 0.0


@dataclass
class QAResponse:
    """问答响应"""
    answer: str
    sources: List[Dict[str, Any]]
    structured_info: Optional[Dict[str, Any]] = None
    confidence: float = 0.0
    processing_time: float = 0.0
    search_strategy: str = "auto"


class EnhancedQA:
    """增强问答系统"""
    
    def __init__(self):
        self.retriever = SmartRetriever()
        self.storage = StructuredStorage()
        self.llm = LLM()
        
        # 初始化标志
        self._initialized = False
        
        # 问答配置
        self.max_context_length = 8000  # 最大上下文长度
        self.min_confidence_threshold = 0.3  # 最小置信度阈值
        self.vector_weight = 0.7  # 向量检索权重
        self.structured_weight = 0.3  # 结构化信息权重
    
    async def init(self):
        """初始化问答系统"""
        if self._initialized:
            return
        
        try:
            await self.storage.init()
            self._initialized = True
            log.info("增强问答系统初始化完成")
        except Exception as e:
            log.error(f"增强问答系统初始化失败: {e}")
            raise
    
    async def answer_question(
        self,
        question: str,
        project_name: Optional[str] = None,
        doc_type: Optional[str] = None,
        search_strategy: str = "auto",
        include_structured: bool = True,
        max_results: int = 10
    ) -> QAResponse:
        """
        回答问题
        
        Args:
            question: 用户问题
            project_name: 项目名称（可选）
            doc_type: 文档类型（可选）
            search_strategy: 搜索策略
            include_structured: 是否包含结构化信息
            max_results: 最大结果数量
            
        Returns:
            问答响应
        """
        if not self._initialized:
            await self.init()
        
        start_time = datetime.now()
        
        try:
            log.info(f"开始回答问题: {question[:50]}...")
            
            # 1. 构建搜索上下文
            qa_context = await self._build_qa_context(
                question, project_name, doc_type, search_strategy, 
                include_structured, max_results
            )
            
            # 2. 生成回答
            answer = await self._generate_answer(question, qa_context)
            
            # 3. 构建响应
            processing_time = (datetime.now() - start_time).total_seconds()
            
            response = QAResponse(
                answer=answer,
                sources=self._format_sources(qa_context.vector_results),
                structured_info=qa_context.structured_data if include_structured else None,
                confidence=qa_context.confidence_score,
                processing_time=processing_time,
                search_strategy=search_strategy
            )
            
            log.info(f"问答完成，耗时: {processing_time:.2f}s, 置信度: {qa_context.confidence_score:.2f}")
            return response
            
        except Exception as e:
            log.error(f"问答失败: {e}")
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return QAResponse(
                answer=f"抱歉，回答问题时出现错误: {str(e)}",
                sources=[],
                structured_info=None,
                confidence=0.0,
                processing_time=processing_time,
                search_strategy=search_strategy
            )
    
    async def _build_qa_context(
        self,
        question: str,
        project_name: Optional[str],
        doc_type: Optional[str],
        search_strategy: str,
        include_structured: bool,
        max_results: int
    ) -> QAContext:
        """构建问答上下文"""
        
        # 1. 向量检索
        search_filter = SearchFilter(
            project_name=project_name,
            doc_types=[doc_type] if doc_type else None
        )
        
        vector_results = await self.retriever.smart_search(
            query=question,
            search_filter=search_filter,
            strategy=search_strategy,
            top_k=max_results,
            include_metadata=True
        )
        
        # 2. 结构化信息检索
        structured_data = {}
        if include_structured and project_name:
            structured_data = await self._get_structured_info(project_name, doc_type)
        
        # 3. 计算置信度
        confidence_score = self._calculate_context_confidence(vector_results, structured_data)
        
        return QAContext(
            vector_results=vector_results,
            structured_data=structured_data,
            project_name=project_name,
            doc_type=doc_type,
            confidence_score=confidence_score
        )
    
    async def _get_structured_info(
        self,
        project_name: str,
        doc_type: Optional[str]
    ) -> Dict[str, Any]:
        """获取结构化信息"""
        structured_info = {}
        
        try:
            if not doc_type or doc_type == "项目档案":
                project_archive = await self.storage.get_project_archive(project_name)
                if project_archive:
                    structured_info["project_archive"] = project_archive
            
            if not doc_type or doc_type == "文书档案":
                conference_archive = await self.storage.get_conference_archive(project_name)
                if conference_archive:
                    structured_info["conference_archive"] = conference_archive
            
        except Exception as e:
            log.error(f"获取结构化信息失败: {e}")
        
        return structured_info
    
    def _calculate_context_confidence(
        self,
        vector_results: List[SearchResult],
        structured_data: Dict[str, Any]
    ) -> float:
        """计算上下文置信度"""
        try:
            # 向量检索置信度
            vector_confidence = 0.0
            if vector_results:
                avg_score = sum(result.score for result in vector_results) / len(vector_results)
                vector_confidence = min(1.0, avg_score / 2.0)  # 假设最高分为2.0
            
            # 结构化信息置信度
            structured_confidence = 0.0
            if structured_data:
                # 基于结构化信息的完整性
                total_fields = 0
                filled_fields = 0
                
                for archive_type, archive_data in structured_data.items():
                    if isinstance(archive_data, dict):
                        for key, value in archive_data.items():
                            if key not in ["id", "created_at", "updated_at", "metadata"]:
                                total_fields += 1
                                if value and str(value).strip() and value != "未提及":
                                    filled_fields += 1
                
                if total_fields > 0:
                    structured_confidence = filled_fields / total_fields
            
            # 综合置信度
            if vector_results and structured_data:
                confidence = (vector_confidence * self.vector_weight + 
                            structured_confidence * self.structured_weight)
            elif vector_results:
                confidence = vector_confidence * 0.8  # 只有向量检索时降低置信度
            elif structured_data:
                confidence = structured_confidence * 0.6  # 只有结构化信息时降低置信度
            else:
                confidence = 0.0
            
            return round(confidence, 2)
            
        except Exception as e:
            log.error(f"计算置信度失败: {e}")
            return 0.0
    
    async def _generate_answer(
        self,
        question: str,
        qa_context: QAContext
    ) -> str:
        """生成回答"""
        try:
            # 构建上下文信息
            context_parts = []
            
            # 1. 向量检索结果
            if qa_context.vector_results:
                context_parts.append("=== 相关文档内容 ===")
                for i, result in enumerate(qa_context.vector_results[:5], 1):
                    context_parts.append(f"文档片段{i}（来源：{result.source}）：")
                    context_parts.append(result.content[:500] + "..." if len(result.content) > 500 else result.content)
                    context_parts.append("")
            
            # 2. 结构化信息
            if qa_context.structured_data:
                context_parts.append("=== 结构化信息 ===")
                
                for archive_type, archive_data in qa_context.structured_data.items():
                    if archive_type == "project_archive":
                        context_parts.append("项目档案信息：")
                        context_parts.append(f"项目名称：{archive_data.get('name', '未知')}")
                        context_parts.append(f"项目时间：{archive_data.get('date', '未知')}")
                        context_parts.append(f"承担单位：{archive_data.get('responsible_unit', '未知')}")
                        context_parts.append(f"项目摘要：{archive_data.get('summary', '未知')}")
                    
                    elif archive_type == "conference_archive":
                        context_parts.append("文书档案信息：")
                        context_parts.append(f"文书名称：{archive_data.get('name', '未知')}")
                        context_parts.append(f"文书时间：{archive_data.get('date', '未知')}")
                        context_parts.append(f"文书类型：{archive_data.get('type', '未知')}")
                        context_parts.append(f"发起组织：{archive_data.get('organizer', '未知')}")
                        context_parts.append(f"参与者：{archive_data.get('participants', '未知')}")
                        context_parts.append(f"文书摘要：{archive_data.get('summary', '未知')}")
                    
                    context_parts.append("")
            
            # 构建完整上下文
            full_context = "\n".join(context_parts)
            
            # 限制上下文长度
            if len(full_context) > self.max_context_length:
                full_context = full_context[:self.max_context_length] + "..."
            
            # 构建提示词
            prompt = f"""基于以下信息回答用户问题。请提供准确、详细的回答，并在适当时引用具体的信息来源。

用户问题：{question}

参考信息：
{full_context}

回答要求：
1. 基于提供的信息进行回答，不要编造不存在的信息
2. 如果信息不足以完全回答问题，请说明缺少哪些信息
3. 适当引用具体的文档或数据来源
4. 回答要条理清晰，重点突出
5. 如果涉及多个方面，请分点说明

请回答："""
            
            # 调用LLM生成回答
            response = await self.llm.chat([
                {
                    "role": "system",
                    "content": "你是一个专业的文档问答助手。请基于提供的信息准确回答用户问题，不要编造信息。"
                },
                {"role": "user", "content": prompt}
            ])
            
            return response.strip()
            
        except Exception as e:
            log.error(f"生成回答失败: {e}")
            return f"抱歉，生成回答时出现错误: {str(e)}"
    
    def _format_sources(self, vector_results: List[SearchResult]) -> List[Dict[str, Any]]:
        """格式化信息源"""
        sources = []
        
        for result in vector_results:
            source = {
                "content": result.content[:200] + "..." if len(result.content) > 200 else result.content,
                "source": result.source,
                "doc_id": result.doc_id,
                "doc_types": result.doc_types,
                "score": result.score,
                "chunk_index": result.chunk_index
            }
            sources.append(source)
        
        return sources
    
    async def get_project_summary(self, project_name: str) -> Dict[str, Any]:
        """获取项目摘要"""
        try:
            # 获取向量检索摘要
            vector_summary = await self.retriever.get_project_summary(project_name)
            
            # 获取结构化信息
            structured_info = await self._get_structured_info(project_name, None)
            
            return {
                "project_name": project_name,
                "vector_summary": vector_summary,
                "structured_info": structured_info,
                "summary_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            log.error(f"获取项目摘要失败: {e}")
            return {"error": str(e)}
    
    async def search_similar_projects(
        self,
        query: str,
        doc_type: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索相似项目"""
        try:
            # 从结构化存储中搜索
            archives = await self.storage.search_archives(
                doc_type=doc_type or "项目档案",
                keywords=[query],
                limit=limit
            )
            
            return archives
            
        except Exception as e:
            log.error(f"搜索相似项目失败: {e}")
            return []
