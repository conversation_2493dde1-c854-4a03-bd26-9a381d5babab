#!/usr/bin/env python3
"""
MySQL结构化信息存储层
设计MySQL表结构存储提取的结构化信息，支持项目档案、文书档案等不同类型的结构化数据
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, Text, DateTime, JSON, Index
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.dialects.mysql import LONGTEXT

from utils.log import log
from utils.config import config


@dataclass
class StructuredRecord:
    """结构化记录"""
    id: Optional[int] = None
    project_name: str = ""
    doc_type: str = ""  # "项目档案" 或 "文书档案"
    extracted_data: Dict[str, Any] = None
    source_docs: List[str] = None
    extraction_method: str = "batch"  # "batch", "single", "manual"
    confidence_score: float = 0.0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None


class StructuredStorage:
    """结构化信息存储管理器"""
    
    def __init__(self):
        # 数据库配置
        self.db_config = config.get("mysql", {})
        self.engine = None
        self.async_engine = None
        self.async_session_factory = None
        
        # 表结构定义
        self.metadata = MetaData()
        self._define_tables()
        
        # 初始化标志
        self._initialized = False
    
    def _define_tables(self):
        """定义数据库表结构"""
        
        # 项目档案表
        self.project_archives = Table(
            'project_archives',
            self.metadata,
            Column('id', Integer, primary_key=True, autoincrement=True),
            Column('project_name', String(255), nullable=False, comment='项目名称'),
            Column('name', String(500), comment='项目正式名称'),
            Column('date', String(100), comment='项目时间'),
            Column('responsible_unit', Text, comment='项目承担单位'),
            Column('summary', LONGTEXT, comment='项目内容摘要'),
            Column('source_docs', JSON, comment='来源文档列表'),
            Column('extraction_method', String(50), default='batch', comment='提取方法'),
            Column('confidence_score', String(10), default='0.0', comment='置信度分数'),
            Column('created_at', DateTime, default=datetime.now, comment='创建时间'),
            Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间'),
            Column('metadata', JSON, comment='元数据'),
            
            # 索引
            Index('idx_project_name', 'project_name'),
            Index('idx_created_at', 'created_at'),
            Index('idx_extraction_method', 'extraction_method'),
            
            comment='项目档案结构化信息表'
        )
        
        # 文书档案表
        self.conference_archives = Table(
            'conference_archives',
            self.metadata,
            Column('id', Integer, primary_key=True, autoincrement=True),
            Column('project_name', String(255), nullable=False, comment='项目名称'),
            Column('name', String(500), comment='文书名称'),
            Column('date', String(100), comment='文书时间'),
            Column('type', String(200), comment='文书类型'),
            Column('organizer', Text, comment='发起组织'),
            Column('participants', Text, comment='参与者'),
            Column('summary', LONGTEXT, comment='文书摘要'),
            Column('source_docs', JSON, comment='来源文档列表'),
            Column('extraction_method', String(50), default='batch', comment='提取方法'),
            Column('confidence_score', String(10), default='0.0', comment='置信度分数'),
            Column('created_at', DateTime, default=datetime.now, comment='创建时间'),
            Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间'),
            Column('metadata', JSON, comment='元数据'),
            
            # 索引
            Index('idx_conf_project_name', 'project_name'),
            Index('idx_conf_created_at', 'created_at'),
            Index('idx_conf_type', 'type'),
            Index('idx_conf_extraction_method', 'extraction_method'),
            
            comment='文书档案结构化信息表'
        )
        
        # 提取任务记录表
        self.extraction_tasks = Table(
            'extraction_tasks',
            self.metadata,
            Column('id', Integer, primary_key=True, autoincrement=True),
            Column('task_id', String(100), unique=True, nullable=False, comment='任务ID'),
            Column('project_name', String(255), nullable=False, comment='项目名称'),
            Column('doc_type', String(50), nullable=False, comment='文档类型'),
            Column('status', String(20), default='pending', comment='任务状态'),
            Column('total_docs', Integer, default=0, comment='总文档数'),
            Column('processed_docs', Integer, default=0, comment='已处理文档数'),
            Column('failed_docs', Integer, default=0, comment='失败文档数'),
            Column('extraction_config', JSON, comment='提取配置'),
            Column('result_summary', JSON, comment='结果摘要'),
            Column('error_info', Text, comment='错误信息'),
            Column('started_at', DateTime, comment='开始时间'),
            Column('completed_at', DateTime, comment='完成时间'),
            Column('created_at', DateTime, default=datetime.now, comment='创建时间'),
            Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间'),
            
            # 索引
            Index('idx_task_id', 'task_id'),
            Index('idx_task_project_name', 'project_name'),
            Index('idx_task_status', 'status'),
            Index('idx_task_created_at', 'created_at'),
            
            comment='结构化信息提取任务记录表'
        )
    
    async def init(self):
        """初始化数据库连接和表结构"""
        if self._initialized:
            return
        
        try:
            # 创建数据库连接
            db_url = f"mysql+aiomysql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
            
            self.async_engine = create_async_engine(
                db_url,
                pool_size=20,
                max_overflow=30,
                pool_pre_ping=True,
                echo=False
            )
            
            self.async_session_factory = sessionmaker(
                self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # 创建表结构
            async with self.async_engine.begin() as conn:
                await conn.run_sync(self.metadata.create_all)
            
            self._initialized = True
            log.info("结构化存储层初始化完成")
            
        except Exception as e:
            log.error(f"结构化存储层初始化失败: {e}")
            raise
    
    async def store_project_archive(
        self,
        project_name: str,
        extracted_data: Dict[str, Any],
        source_docs: List[str] = None,
        extraction_method: str = "batch",
        confidence_score: float = 0.0,
        metadata: Dict[str, Any] = None
    ) -> Optional[int]:
        """存储项目档案信息"""
        try:
            async with self.async_session_factory() as session:
                # 检查是否已存在
                existing_query = text("""
                    SELECT id FROM project_archives 
                    WHERE project_name = :project_name
                """)
                
                result = await session.execute(existing_query, {"project_name": project_name})
                existing_record = result.fetchone()
                
                if existing_record:
                    # 更新现有记录
                    update_query = text("""
                        UPDATE project_archives SET
                            name = :name,
                            date = :date,
                            responsible_unit = :responsible_unit,
                            summary = :summary,
                            source_docs = :source_docs,
                            extraction_method = :extraction_method,
                            confidence_score = :confidence_score,
                            updated_at = :updated_at,
                            metadata = :metadata
                        WHERE project_name = :project_name
                    """)
                    
                    await session.execute(update_query, {
                        "project_name": project_name,
                        "name": extracted_data.get("name", ""),
                        "date": extracted_data.get("date", ""),
                        "responsible_unit": extracted_data.get("responsible_unit", ""),
                        "summary": extracted_data.get("summary", ""),
                        "source_docs": json.dumps(source_docs or [], ensure_ascii=False),
                        "extraction_method": extraction_method,
                        "confidence_score": str(confidence_score),
                        "updated_at": datetime.now(),
                        "metadata": json.dumps(metadata or {}, ensure_ascii=False)
                    })
                    
                    record_id = existing_record[0]
                    
                else:
                    # 插入新记录
                    insert_query = text("""
                        INSERT INTO project_archives (
                            project_name, name, date, responsible_unit, summary,
                            source_docs, extraction_method, confidence_score, metadata
                        ) VALUES (
                            :project_name, :name, :date, :responsible_unit, :summary,
                            :source_docs, :extraction_method, :confidence_score, :metadata
                        )
                    """)
                    
                    result = await session.execute(insert_query, {
                        "project_name": project_name,
                        "name": extracted_data.get("name", ""),
                        "date": extracted_data.get("date", ""),
                        "responsible_unit": extracted_data.get("responsible_unit", ""),
                        "summary": extracted_data.get("summary", ""),
                        "source_docs": json.dumps(source_docs or [], ensure_ascii=False),
                        "extraction_method": extraction_method,
                        "confidence_score": str(confidence_score),
                        "metadata": json.dumps(metadata or {}, ensure_ascii=False)
                    })
                    
                    record_id = result.lastrowid
                
                await session.commit()
                log.info(f"项目档案存储成功: {project_name}, ID: {record_id}")
                return record_id
                
        except Exception as e:
            log.error(f"项目档案存储失败: {e}")
            return None
    
    async def store_conference_archive(
        self,
        project_name: str,
        extracted_data: Dict[str, Any],
        source_docs: List[str] = None,
        extraction_method: str = "batch",
        confidence_score: float = 0.0,
        metadata: Dict[str, Any] = None
    ) -> Optional[int]:
        """存储文书档案信息"""
        try:
            async with self.async_session_factory() as session:
                # 检查是否已存在
                existing_query = text("""
                    SELECT id FROM conference_archives 
                    WHERE project_name = :project_name
                """)
                
                result = await session.execute(existing_query, {"project_name": project_name})
                existing_record = result.fetchone()
                
                if existing_record:
                    # 更新现有记录
                    update_query = text("""
                        UPDATE conference_archives SET
                            name = :name,
                            date = :date,
                            type = :type,
                            organizer = :organizer,
                            participants = :participants,
                            summary = :summary,
                            source_docs = :source_docs,
                            extraction_method = :extraction_method,
                            confidence_score = :confidence_score,
                            updated_at = :updated_at,
                            metadata = :metadata
                        WHERE project_name = :project_name
                    """)
                    
                    await session.execute(update_query, {
                        "project_name": project_name,
                        "name": extracted_data.get("name", ""),
                        "date": extracted_data.get("date", ""),
                        "type": extracted_data.get("type", ""),
                        "organizer": extracted_data.get("organizer", ""),
                        "participants": extracted_data.get("participants", ""),
                        "summary": extracted_data.get("summary", ""),
                        "source_docs": json.dumps(source_docs or [], ensure_ascii=False),
                        "extraction_method": extraction_method,
                        "confidence_score": str(confidence_score),
                        "updated_at": datetime.now(),
                        "metadata": json.dumps(metadata or {}, ensure_ascii=False)
                    })
                    
                    record_id = existing_record[0]
                    
                else:
                    # 插入新记录
                    insert_query = text("""
                        INSERT INTO conference_archives (
                            project_name, name, date, type, organizer, participants, summary,
                            source_docs, extraction_method, confidence_score, metadata
                        ) VALUES (
                            :project_name, :name, :date, :type, :organizer, :participants, :summary,
                            :source_docs, :extraction_method, :confidence_score, :metadata
                        )
                    """)
                    
                    result = await session.execute(insert_query, {
                        "project_name": project_name,
                        "name": extracted_data.get("name", ""),
                        "date": extracted_data.get("date", ""),
                        "type": extracted_data.get("type", ""),
                        "organizer": extracted_data.get("organizer", ""),
                        "participants": extracted_data.get("participants", ""),
                        "summary": extracted_data.get("summary", ""),
                        "source_docs": json.dumps(source_docs or [], ensure_ascii=False),
                        "extraction_method": extraction_method,
                        "confidence_score": str(confidence_score),
                        "metadata": json.dumps(metadata or {}, ensure_ascii=False)
                    })
                    
                    record_id = result.lastrowid
                
                await session.commit()
                log.info(f"文书档案存储成功: {project_name}, ID: {record_id}")
                return record_id
                
        except Exception as e:
            log.error(f"文书档案存储失败: {e}")
            return None
    
    async def get_project_archive(self, project_name: str) -> Optional[Dict[str, Any]]:
        """获取项目档案信息"""
        try:
            async with self.async_session_factory() as session:
                query = text("""
                    SELECT * FROM project_archives 
                    WHERE project_name = :project_name
                    ORDER BY updated_at DESC
                    LIMIT 1
                """)
                
                result = await session.execute(query, {"project_name": project_name})
                row = result.fetchone()
                
                if row:
                    return {
                        "id": row[0],
                        "project_name": row[1],
                        "name": row[2],
                        "date": row[3],
                        "responsible_unit": row[4],
                        "summary": row[5],
                        "source_docs": json.loads(row[6]) if row[6] else [],
                        "extraction_method": row[7],
                        "confidence_score": float(row[8]) if row[8] else 0.0,
                        "created_at": row[9].isoformat() if row[9] else None,
                        "updated_at": row[10].isoformat() if row[10] else None,
                        "metadata": json.loads(row[11]) if row[11] else {}
                    }
                
                return None
                
        except Exception as e:
            log.error(f"获取项目档案失败: {e}")
            return None
    
    async def get_conference_archive(self, project_name: str) -> Optional[Dict[str, Any]]:
        """获取文书档案信息"""
        try:
            async with self.async_session_factory() as session:
                query = text("""
                    SELECT * FROM conference_archives 
                    WHERE project_name = :project_name
                    ORDER BY updated_at DESC
                    LIMIT 1
                """)
                
                result = await session.execute(query, {"project_name": project_name})
                row = result.fetchone()
                
                if row:
                    return {
                        "id": row[0],
                        "project_name": row[1],
                        "name": row[2],
                        "date": row[3],
                        "type": row[4],
                        "organizer": row[5],
                        "participants": row[6],
                        "summary": row[7],
                        "source_docs": json.loads(row[8]) if row[8] else [],
                        "extraction_method": row[9],
                        "confidence_score": float(row[10]) if row[10] else 0.0,
                        "created_at": row[11].isoformat() if row[11] else None,
                        "updated_at": row[12].isoformat() if row[12] else None,
                        "metadata": json.loads(row[13]) if row[13] else {}
                    }
                
                return None
                
        except Exception as e:
            log.error(f"获取文书档案失败: {e}")
            return None

    async def search_archives(
        self,
        doc_type: str,
        project_name: Optional[str] = None,
        keywords: Optional[List[str]] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """搜索档案信息"""
        try:
            table_name = "project_archives" if doc_type == "项目档案" else "conference_archives"

            # 构建查询条件
            where_conditions = []
            params = {}

            if project_name:
                where_conditions.append("project_name LIKE :project_name")
                params["project_name"] = f"%{project_name}%"

            if keywords:
                for i, keyword in enumerate(keywords):
                    where_conditions.append(f"(name LIKE :keyword_{i} OR summary LIKE :keyword_{i})")
                    params[f"keyword_{i}"] = f"%{keyword}%"

            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            query = text(f"""
                SELECT * FROM {table_name}
                WHERE {where_clause}
                ORDER BY updated_at DESC
                LIMIT :limit
            """)

            params["limit"] = limit

            async with self.async_session_factory() as session:
                result = await session.execute(query, params)
                rows = result.fetchall()

                archives = []
                for row in rows:
                    if doc_type == "项目档案":
                        archive = {
                            "id": row[0],
                            "project_name": row[1],
                            "name": row[2],
                            "date": row[3],
                            "responsible_unit": row[4],
                            "summary": row[5],
                            "source_docs": json.loads(row[6]) if row[6] else [],
                            "extraction_method": row[7],
                            "confidence_score": float(row[8]) if row[8] else 0.0,
                            "created_at": row[9].isoformat() if row[9] else None,
                            "updated_at": row[10].isoformat() if row[10] else None,
                            "metadata": json.loads(row[11]) if row[11] else {}
                        }
                    else:
                        archive = {
                            "id": row[0],
                            "project_name": row[1],
                            "name": row[2],
                            "date": row[3],
                            "type": row[4],
                            "organizer": row[5],
                            "participants": row[6],
                            "summary": row[7],
                            "source_docs": json.loads(row[8]) if row[8] else [],
                            "extraction_method": row[9],
                            "confidence_score": float(row[10]) if row[10] else 0.0,
                            "created_at": row[11].isoformat() if row[11] else None,
                            "updated_at": row[12].isoformat() if row[12] else None,
                            "metadata": json.loads(row[13]) if row[13] else {}
                        }

                    archives.append(archive)

                return archives

        except Exception as e:
            log.error(f"搜索档案失败: {e}")
            return []

    async def get_extraction_stats(self) -> Dict[str, Any]:
        """获取提取统计信息"""
        try:
            async with self.async_session_factory() as session:
                # 项目档案统计
                project_query = text("""
                    SELECT
                        COUNT(*) as total,
                        COUNT(CASE WHEN extraction_method = 'batch' THEN 1 END) as batch_count,
                        COUNT(CASE WHEN extraction_method = 'single' THEN 1 END) as single_count,
                        AVG(CAST(confidence_score AS DECIMAL(3,2))) as avg_confidence
                    FROM project_archives
                """)

                project_result = await session.execute(project_query)
                project_stats = project_result.fetchone()

                # 文书档案统计
                conference_query = text("""
                    SELECT
                        COUNT(*) as total,
                        COUNT(CASE WHEN extraction_method = 'batch' THEN 1 END) as batch_count,
                        COUNT(CASE WHEN extraction_method = 'single' THEN 1 END) as single_count,
                        AVG(CAST(confidence_score AS DECIMAL(3,2))) as avg_confidence
                    FROM conference_archives
                """)

                conference_result = await session.execute(conference_query)
                conference_stats = conference_result.fetchone()

                return {
                    "project_archives": {
                        "total": project_stats[0] if project_stats else 0,
                        "batch_extracted": project_stats[1] if project_stats else 0,
                        "single_extracted": project_stats[2] if project_stats else 0,
                        "avg_confidence": float(project_stats[3]) if project_stats and project_stats[3] else 0.0
                    },
                    "conference_archives": {
                        "total": conference_stats[0] if conference_stats else 0,
                        "batch_extracted": conference_stats[1] if conference_stats else 0,
                        "single_extracted": conference_stats[2] if conference_stats else 0,
                        "avg_confidence": float(conference_stats[3]) if conference_stats and conference_stats[3] else 0.0
                    }
                }

        except Exception as e:
            log.error(f"获取提取统计失败: {e}")
            return {"error": str(e)}

    async def delete_project_archives(self, project_name: str) -> bool:
        """删除项目的所有档案记录"""
        try:
            async with self.async_session_factory() as session:
                # 删除项目档案
                project_query = text("DELETE FROM project_archives WHERE project_name = :project_name")
                await session.execute(project_query, {"project_name": project_name})

                # 删除文书档案
                conference_query = text("DELETE FROM conference_archives WHERE project_name = :project_name")
                await session.execute(conference_query, {"project_name": project_name})

                await session.commit()
                log.info(f"删除项目档案记录: {project_name}")
                return True

        except Exception as e:
            log.error(f"删除项目档案记录失败: {e}")
            return False
