import hashlib
import json

def generate_md5(file_path):
    with open(file_path, 'rb') as file:
        md5_hash = hashlib.md5()
        while chunk := file.read(8192):
            md5_hash.update(chunk)
    
    md5_value = md5_hash.hexdigest()
    return md5_value





def get_id(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        if data:
            first_id = data[0]['id']
            return first_id
        else:
            return None
    except Exception as e:
        print(f"An error occurred: {e}")
        return None

if __name__ == '__main__':
    md5 = generate_md5("d:/HJ/kg/kg-courseware/data/docs/kg.txt")
    print(md5)