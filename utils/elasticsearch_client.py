#!/usr/bin/env python3
"""
Elasticsearch客户端
处理ES连接、索引管理和基础操作
"""

import json
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from elasticsearch import AsyncElasticsearch
from elasticsearch.helpers import async_bulk

from utils.log import log
from utils.config import config


class ElasticsearchClient:
    """Elasticsearch客户端"""
    
    def __init__(self):
        # ES连接配置
        self.es_host = config.get("elasticsearch.host", "localhost")
        self.es_port = config.get("elasticsearch.port", 9200)
        self.es_username = config.get("elasticsearch.username", "")
        self.es_password = config.get("elasticsearch.password", "")
        
        # 连接参数 - 使用新版本的参数格式
        es_url = f"http://{self.es_host}:{self.es_port}"
        self.es_config = {
            "hosts": [es_url],
            "request_timeout": 30,
            "max_retries": 3,
            "retry_on_timeout": True,
            "verify_certs": False
        }

        # 如果有认证信息
        if self.es_username and self.es_password:
            self.es_config["basic_auth"] = (self.es_username, self.es_password)
        
        self.client = None
        self._initialized = False
    
    async def initialize(self):
        """初始化ES客户端"""
        try:
            if not self._initialized:
                self.client = AsyncElasticsearch(**self.es_config)
                
                # 测试连接
                info = await self.client.info()
                log.info(f"ES连接成功: {info['version']['number']}")
                
                self._initialized = True
                
        except Exception as e:
            log.error(f"ES初始化失败: {e}")
            raise
    
    async def close(self):
        """关闭ES连接"""
        if self.client:
            await self.client.close()
            self._initialized = False
    
    async def create_index(
        self,
        index_name: str,
        mapping: Optional[Dict[str, Any]] = None,
        settings: Optional[Dict[str, Any]] = None
    ) -> bool:
        """创建索引"""
        try:
            # 检查索引是否已存在
            if await self.index_exists(index_name):
                log.info(f"索引已存在: {index_name}")
                return True
            
            # 默认映射配置
            if mapping is None:
                mapping = self._get_default_mapping()
            
            # 默认设置配置
            if settings is None:
                settings = self._get_default_settings()
            
            # 创建索引
            body = {
                "mappings": mapping,
                "settings": settings
            }
            
            response = await self.client.indices.create(
                index=index_name,
                body=body
            )
            
            log.info(f"索引创建成功: {index_name}")
            return response.get("acknowledged", False)
            
        except Exception as e:
            log.error(f"创建索引失败 {index_name}: {e}")
            return False
    
    def _get_default_mapping(self) -> Dict[str, Any]:
        """获取兼容现有架构的映射配置"""
        return {
            "settings": {
                "analysis": {
                    "analyzer": {
                        "text_analyzer": {
                            "type": "custom",
                            "tokenizer": "ik_max_word",
                            "filter": ["lowercase", "asciifolding"]
                        }
                    }
                },
                "index": {
                    "number_of_replicas": 0,
                    "number_of_shards": 1,
                    "max_ngram_diff": 50,
                    "mapping": {
                        "total_fields": {
                            "limit": 2000
                        }
                    }
                }
            },
            "mappings": {
                "properties": {
                    "content": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart",
                        "fields": {
                            "keyword": {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "project_name": {
                        "type": "keyword",
                        "fields": {
                            "text": {
                                "type": "text",
                                "analyzer": "ik_smart"
                            }
                        }
                    },
                    "source": {
                        "type": "keyword"
                    },
                    "page_num": {
                        "type": "integer"
                    },
                    "doc_id": {
                        "type": "keyword"
                    },
                    "action": {
                        "type": "keyword"
                    },
                    "metadata": {
                        "type": "object",
                        "dynamic": True
                    },
                    "embedding": {
                        "type": "dense_vector",
                        "dims": 1024,  # 使用现有的1024维
                        "index": True,
                        "similarity": "cosine"
                    },
                    "year": {
                        "type": "integer"
                    }
                }
            }
        }
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """获取默认设置配置"""
        return {
            "number_of_shards": 1,
            "number_of_replicas": 0,
            "analysis": {
                "analyzer": {
                    "ik_max_word": {
                        "type": "ik_max_word"
                    },
                    "ik_smart": {
                        "type": "ik_smart"
                    }
                }
            }
        }
    
    async def index_exists(self, index_name: str) -> bool:
        """检查索引是否存在"""
        try:
            return await self.client.indices.exists(index=index_name)
        except Exception as e:
            log.error(f"检查索引存在性失败 {index_name}: {e}")
            return False
    
    async def delete_index(self, index_name: str) -> bool:
        """删除索引"""
        try:
            if await self.index_exists(index_name):
                response = await self.client.indices.delete(index=index_name)
                log.info(f"索引删除成功: {index_name}")
                return response.get("acknowledged", False)
            else:
                log.info(f"索引不存在，无需删除: {index_name}")
                return True
                
        except Exception as e:
            log.error(f"删除索引失败 {index_name}: {e}")
            return False
    
    async def index_document(
        self,
        index_name: str,
        doc_id: Optional[str],
        document: Dict[str, Any]
    ) -> bool:
        """索引单个文档"""
        try:
            response = await self.client.index(
                index=index_name,
                id=doc_id,
                body=document
            )
            
            return response.get("result") in ["created", "updated"]
            
        except Exception as e:
            log.error(f"索引文档失败: {e}")
            return False
    
    async def bulk_index_documents(
        self,
        index_name: str,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, int]:
        """批量索引文档"""
        try:
            # 准备批量操作数据
            actions = []
            for doc in documents:
                action = {
                    "_index": index_name,
                    "_source": doc
                }
                
                # 如果文档有ID，使用它
                if "doc_id" in doc:
                    action["_id"] = doc.pop("doc_id")
                
                actions.append(action)
            
            # 执行批量索引
            success_count, failed_items = await async_bulk(
                self.client,
                actions,
                chunk_size=100,
                request_timeout=60
            )
            
            result = {
                "success_count": success_count,
                "failed_count": len(failed_items),
                "total_count": len(documents)
            }
            
            if failed_items:
                log.warning(f"批量索引部分失败: 成功{success_count}, 失败{len(failed_items)}")
                for item in failed_items[:5]:  # 只记录前5个失败项
                    log.warning(f"失败项: {item}")
            else:
                log.info(f"批量索引成功: {success_count}个文档")
            
            return result
            
        except Exception as e:
            log.error(f"批量索引失败: {e}")
            return {"success_count": 0, "failed_count": len(documents), "total_count": len(documents)}
    
    async def search(
        self,
        index_name: str,
        query: Dict[str, Any],
        size: int = 10,
        from_: int = 0,
        sort: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """搜索文档"""
        try:
            body = {
                "query": query,
                "size": size,
                "from": from_
            }
            
            if sort:
                body["sort"] = sort
            
            response = await self.client.search(
                index=index_name,
                body=body
            )
            
            return response
            
        except Exception as e:
            log.error(f"搜索失败: {e}")
            return {"hits": {"hits": [], "total": {"value": 0}}}
    
    async def keyword_search(
        self,
        index_name: str,
        keywords: Union[str, List[str]],
        fields: List[str] = None,
        size: int = 10
    ) -> List[Dict[str, Any]]:
        """关键词搜索"""
        try:
            if fields is None:
                fields = ["chunk_text"]
            
            # 构建查询
            if isinstance(keywords, list):
                keywords = " ".join(keywords)
            
            query = {
                "multi_match": {
                    "query": keywords,
                    "fields": fields,
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            }
            
            response = await self.search(index_name, query, size=size)
            
            # 提取结果
            hits = response.get("hits", {}).get("hits", [])
            results = []
            
            for hit in hits:
                result = {
                    "_score": hit.get("_score", 0),
                    "_source": hit.get("_source", {}),
                    "_id": hit.get("_id", "")
                }
                results.append(result)
            
            log.debug(f"关键词搜索完成: {keywords}, 返回{len(results)}个结果")
            return results
            
        except Exception as e:
            log.error(f"关键词搜索失败: {e}")
            return []
    
    async def vector_search(
        self,
        index_name: str,
        query_vector: List[float],
        vector_field: str = "embedding",
        size: int = 10,
        min_score: float = 0.0
    ) -> List[Dict[str, Any]]:
        """向量搜索"""
        try:
            query = {
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": f"cosineSimilarity(params.query_vector, '{vector_field}') + 1.0",
                        "params": {"query_vector": query_vector}
                    }
                }
            }
            
            response = await self.search(index_name, query, size=size)
            
            # 提取结果并过滤低分结果
            hits = response.get("hits", {}).get("hits", [])
            results = []
            
            for hit in hits:
                score = hit.get("_score", 0)
                if score >= min_score:
                    result = {
                        "_score": score,
                        "_source": hit.get("_source", {}),
                        "_id": hit.get("_id", "")
                    }
                    results.append(result)
            
            log.debug(f"向量搜索完成: 返回{len(results)}个结果")
            return results
            
        except Exception as e:
            log.error(f"向量搜索失败: {e}")
            return []
    
    async def hybrid_search(
        self,
        index_name: str,
        keywords: str,
        query_vector: List[float],
        keyword_weight: float = 0.5,
        vector_weight: float = 0.5,
        size: int = 10
    ) -> List[Dict[str, Any]]:
        """混合搜索（关键词 + 向量）"""
        try:
            query = {
                "bool": {
                    "should": [
                        {
                            "multi_match": {
                                "query": keywords,
                                "fields": ["chunk_text"],
                                "boost": keyword_weight
                            }
                        },
                        {
                            "script_score": {
                                "query": {"match_all": {}},
                                "script": {
                                    "source": f"cosineSimilarity(params.query_vector, 'embedding') * {vector_weight}",
                                    "params": {"query_vector": query_vector}
                                }
                            }
                        }
                    ]
                }
            }
            
            response = await self.search(index_name, query, size=size)
            
            # 提取结果
            hits = response.get("hits", {}).get("hits", [])
            results = []
            
            for hit in hits:
                result = {
                    "_score": hit.get("_score", 0),
                    "_source": hit.get("_source", {}),
                    "_id": hit.get("_id", "")
                }
                results.append(result)
            
            log.debug(f"混合搜索完成: 关键词={keywords}, 返回{len(results)}个结果")
            return results
            
        except Exception as e:
            log.error(f"混合搜索失败: {e}")
            return []
    
    async def get_index_stats(self, index_name: str) -> Dict[str, Any]:
        """获取索引统计信息"""
        try:
            stats = await self.client.indices.stats(index=index_name)
            
            index_stats = stats.get("indices", {}).get(index_name, {})
            total_stats = index_stats.get("total", {})
            
            return {
                "document_count": total_stats.get("docs", {}).get("count", 0),
                "store_size": total_stats.get("store", {}).get("size_in_bytes", 0),
                "indexing_total": total_stats.get("indexing", {}).get("index_total", 0),
                "search_total": total_stats.get("search", {}).get("query_total", 0)
            }
            
        except Exception as e:
            log.error(f"获取索引统计失败 {index_name}: {e}")
            return {}
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self._initialized:
                return {"status": "not_initialized"}
            
            health = await self.client.cluster.health()
            
            return {
                "status": health.get("status", "unknown"),
                "cluster_name": health.get("cluster_name", ""),
                "number_of_nodes": health.get("number_of_nodes", 0),
                "active_shards": health.get("active_shards", 0)
            }
            
        except Exception as e:
            log.error(f"健康检查失败: {e}")
            return {"status": "error", "error": str(e)}
