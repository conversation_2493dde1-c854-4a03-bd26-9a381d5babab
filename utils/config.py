import yaml
import os
from typing import Dict, Any

class Config:
    _instance = None
    _config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._config is None:
            self.load_config()
    
    def load_config(self, config_path: str = "config.yaml"):
        """加载配置文件"""
        try:
            # 获取当前文件所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            # 获取项目根目录
            root_dir = os.path.dirname(current_dir)
            # 配置文件完整路径
            config_path = os.path.join(root_dir, config_path)
            
            with open(config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
        except Exception as e:
            print(f"Error loading config: {str(e)}")
            # 使用默认配置
            self._config = {
                "elasticsearch": {
                    "host": "127.0.0.1",
                    "port": 9200,
                    "username": "elastic",
                    "password": "elastic",
                    "index_name": "docs",
                    "verify_certs": False
                },
                "minio": {
                    "endpoint": "127.0.0.1:9000",
                    "access_key": "minioadmin",
                    "secret_key": "minioadmin",
                    "secure": False,
                    "bucket_name": "docs"
                },
                "sqlite": {
                    "db_path": "data/db/knowledge.db"
                },
                "llm": {
                    "api_url": "http://localhost:8888",
                    "token": "startfrom2023",
                    "model": "hngpt",
                    "temperature": 0.7,
                    "max_tokens": 32000
                }
            }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            keys = key.split('.')
            value = self._config
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_all(self) -> Dict:
        """获取所有配置"""
        return self._config.copy()

    def get_mysql_config(self) -> Dict:
        """获取服务B的MySQL配置"""
        return self.get("mysql", {})

    def get_service_a_mysql_config(self) -> Dict:
        """获取服务A的MySQL配置"""
        return self.get("service_a_mysql", {})

# 创建全局配置实例
config = Config()

chunk_size = config.get("chunk_size")
chunk_overlap = config.get("chunk_overlap")

mi_cfg  = config.get("minio")
es_cfg = config.get("elasticsearch")
threshold = config.get("threshold")
model_cfg = config.get("model_configs")
cb = config.get("cb")
pg_cfg = config.get("pg")
project_extract = config.get("project_extract")
conference_extract = config.get("conference_extract")

if __name__ == "__main__":
    print(chunk_size)
    print(chunk_overlap)
    print(mi_cfg)
    print(es_cfg)
    print(config)
    print(model_cfg)
    print(es_cfg.get("host"))
    print(es_cfg.get("port"))
    print(es_cfg.get("index"))
    print(es_cfg.get("user"))
    print(es_cfg.get("passwd"))
    print(threshold)