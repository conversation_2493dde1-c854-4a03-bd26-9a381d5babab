from transformers import PreTrainedTokenizer
from typing import List, Tu<PERSON>,Union
import regex as re
import json
from datetime import datetime
from urllib.parse import urlparse, unquote

def locate_json(text: str) -> str:
    """定位JSON字符串"""
    if not text:
        return "{}"
    
    # 1. 尝试从代码块中提取
    code_patterns = [
        r'```(?:json)?[\r\n]*([\s\S]*?)[\r\n]*```',  # 处理任意换行符的代码块
        r'```([\s\S]*?)```',                         # 无语言标记的代码块
        r'`([\s\S]*?)`'                              # 单行代码块
    ]
    
    # 尝试从代码块中提取
    for pattern in code_patterns:
        matches = re.findall(pattern, text, re.MULTILINE | re.DOTALL)
        if matches:
            for match in matches:
                # 找到第一个 { 和最后一个 }
                start = match.find('{')
                end = match.rfind('}')
                if start != -1 and end != -1:
                    json_str = match[start:end+1].strip()
                    try:
                        # 清理JSON字符串
                        json_str = clean_json_string(json_str)
                        # 尝试标准JSON解析
                        json.loads(json_str)
                        return json_str
                    except:
                        continue
    
    # 2. 尝试直接从文本中提取
    start = text.find('{')
    end = text.rfind('}')
    if start != -1 and end != -1:
        json_str = text[start:end+1].strip()
        try:
            # 清理JSON字符串
            json_str = clean_json_string(json_str)
            # 尝试标准JSON解析
            json.loads(json_str)
            return json_str
        except:
            pass
    
    # 3. 如果标准解析都失败了，尝试宽松解析
    try:
        # 首先尝试从代码块中提取内容进行宽松解析
        for pattern in code_patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.DOTALL)
            if matches:
                for match in matches:
                    json_str = parse_loose_json(match)
                    if json_str:
                        return json_str
        
        # 如果代码块中没找到，尝试直接从文本中提取进行宽松解析
        if start != -1 and end != -1:
            json_str = text[start:end+1].strip()
            result = parse_loose_json(json_str)
            if result:
                return result
        
        # 最后尝试对整个文本进行宽松解析
        result = parse_loose_json(text)
        if result:
            return result
            
    except Exception as e:
        print(f"Error in loose parsing: {str(e)}")
    
    return "{}"

def parse_loose_json(text: str) -> str:
    """使用更宽松的方式解析类JSON文本
    
    Args:
        text: 包含JSON格式的文本
        
    Returns:
        str: 格式化的JSON字符串
    """
    try:
        # 1. 提取所有key-value对，同时支持单引号和双引号
        pattern = r'["\']([^"\']+)["\']\s*:\s*(["\'][^"\']*["\']|[^,}\s][^,}]*)'
        pairs = re.findall(pattern, text)
        
        if not pairs:
            return ""
            
        # 2. 构建JSON对象
        result = {}
        for key, value in pairs:
            # 清理value
            value = value.strip()
            # 如果value不是以引号开始，添加双引号
            if not (value.startswith('"') or value.startswith("'")):
                value = f'"{value}"'
            # 统一转换为双引号
            value = value.replace("'", '"')
            # 添加到结果中
            try:
                result[key] = json.loads(value)
            except json.JSONDecodeError:
                # 如果解析失败，尝试清理value中的转义字符
                value = value.replace('\\', '')
                result[key] = value.strip('"')
            
        # 3. 转换回JSON字符串
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        print(f"Error in loose JSON parsing:{text} {str(e)}")
        return ""

def clean_json_string(json_str: str) -> str:
    """清理JSON字符串"""
    try:
        # 移除注释和多余字符
        lines = []
        for line in json_str.split('\n'):
            # 移除行内注释
            comment_idx = line.find('//')
            if comment_idx != -1:
                line = line[:comment_idx]
            # 移除空行
            if line.strip():
                lines.append(line.strip())
        
        json_str = ' '.join(lines)
        
        # 移除多行注释
        json_str = re.sub(r'/\*[\s\S]*?\*/', '', json_str)
        
        # 修复分隔符问题
        # 1. 将中文分号替换为英文逗号
        json_str = re.sub(r'；(?=\s*["}])', ',', json_str)
        
        # 2. 确保键名有引号
        json_str = re.sub(r'([{,])\s*"?(\w+)"?\s*:', r'\1"\2":', json_str)
        
        # 3. 为非引号包裹的值添加引号，但排除已经有引号的值
        json_str = re.sub(r':\s*([^",\{\[\]\}]+)([,\}])', r':"\1"\2', json_str)
        
        # 4. 在缺少逗号的地方添加逗号 (修改后的版本)
        json_str = re.sub(r'(?<!:)\s*"\s*"(?!\s*[,}\]])', '","', json_str)  # 处理相邻的字符串，但排除空值字段
        json_str = re.sub(r'"\s*{', '",{', json_str)  # 处理字符串后跟对象
        json_str = re.sub(r'}\s*"', '},"', json_str)  # 处理对象后跟字符串
        json_str = re.sub(r'}\s*{', '},{', json_str)  # 处理相邻的对象
        
        # 5. 移除尾随逗号
        json_str = re.sub(r',\s*([\]}])', r'\1', json_str)
        
        # 处理特殊字符
        json_str = json_str.replace('\\"', '"')  # 处理转义的引号
        json_str = re.sub(r'(?<!\\)\\(?!["\\])', '\\\\', json_str)  # 处理单个反斜杠
        
        return json_str
        
    except Exception as e:
        print(f"Error cleaning JSON string: {str(e)}")
        return json_str

def is_valid_json(json_string: str) -> bool:
    """检查字符串是否为有效的JSON"""
    try:
        if not json_string.strip().startswith('{'):
            return False
        json.loads(json_string)
        return True
    except json.JSONDecodeError:
        return False

def load_json(llm_response: str) -> dict:
    """从LLM响应中加载JSON"""
    try:
        if not llm_response:
            return {}
        # 导入数据清理版本的clean_response
        from utils.data_cleaner import clean_response
        response = clean_response(llm_response)
        # 1. 定位JSON字符串
        json_str = locate_json(response)
        if not json_str or json_str == "{}":
            print(f"No valid JSON found in response:{response}")
            return {}
        
        # 2. 尝试直接解析
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            print(f"First parse attempt failed: {str(e)}")
            
            # 3. 尝试进一步清理并重新解析
            cleaned = clean_json_string(json_str)
            try:
                return json.loads(cleaned)
            except json.JSONDecodeError as e:
                print(f"Second parse attempt failed: {str(e)}")
                print(f"Cleaned JSON string: {cleaned}")
                return {}
                
    except Exception as e:
        print(f"Error loading JSON: {str(e)}")
        return {}




def make_context(
    tokenizer: PreTrainedTokenizer,
    query: str,
    history: List[Tuple[str, str]] = None,
    system: str = "You are a helpful assistant.",
    max_input_length: int = 2048, # if you want to change this, you need to change the max_input_len in tensorrt_llm_july-release-v1/examples/qwen/build.py
    max_window_size: int = 6144,
    chat_format: str = "chatml",
):
    if history is None:
        history = []

    if chat_format == "chatml":
        im_start, im_end = "<|im_start|>", "<|im_end|>"
        im_start_tokens = [tokenizer.im_start_id]
        im_end_tokens = [tokenizer.im_end_id]
        nl_tokens = tokenizer.encode("\n")

        def _tokenize_str(role, content):
            return (
                f"{role}\n{content}",
                tokenizer.encode(
                    role,
                    allowed_special=set(),
                ) + nl_tokens + tokenizer.encode(
                    content,
                    allowed_special=set(),
                )
            )

        system_text, system_tokens_part = _tokenize_str("system", system)
        system_tokens = im_start_tokens + system_tokens_part + im_end_tokens
        raw_text = ""
        context_tokens = []

        for turn_query, turn_response in reversed(history):
            query_text, query_tokens_part = _tokenize_str("user", turn_query)
            query_tokens = im_start_tokens + query_tokens_part + im_end_tokens

            response_text, response_tokens_part = _tokenize_str(
                "assistant", turn_response
            )
            response_tokens = im_start_tokens + response_tokens_part + im_end_tokens
            next_context_tokens = nl_tokens + query_tokens + nl_tokens + response_tokens
            prev_chat = (
                f"\n{im_start}{query_text}{im_end}\n{im_start}{response_text}{im_end}"
            )

            current_context_size = (
                len(system_tokens) + len(next_context_tokens) + len(context_tokens)
            )
            if current_context_size < max_window_size:
                context_tokens = next_context_tokens + context_tokens
                raw_text = prev_chat + raw_text
            else:
                break

        context_tokens = system_tokens + context_tokens
        raw_text = f"{im_start}{system_text}{im_end}" + raw_text
        context_tokens += (
            nl_tokens
            + im_start_tokens
            + _tokenize_str("user", query)[1]
            + im_end_tokens
            + nl_tokens
            + im_start_tokens
            + tokenizer.encode("assistant")
            + nl_tokens
        )
        raw_text += f"\n{im_start}user\n{query}{im_end}\n{im_start}assistant\n"

    elif chat_format == "raw":
        raw_text = query
        context_tokens = tokenizer.encode(raw_text)
    else:
        raise NotImplementedError(f"Unknown chat format {chat_format!r}")
    # truncate to max_input_length, truncate from the front
    return raw_text, context_tokens[-max_input_length: ]
  

def _decode_chatml(
    tokens: List[int],
    stop_words: List[str],
    eod_token_ids: List[int],
    tokenizer: PreTrainedTokenizer,
    raw_text_len: int,
    context_length: int,
    verbose: bool = False,
    return_end_reason: bool = False,
    errors: str='replace'
):
    end_reason = f"Gen length {len(tokens)}"
    eod_token_idx = context_length
    for eod_token_idx in range(context_length, len(tokens)):
        if tokens[eod_token_idx] in eod_token_ids:
            end_reason = f"Gen {tokenizer.decode([tokens[eod_token_idx]])!r}"
            break

    trim_decode_tokens = tokenizer.decode(tokens[:eod_token_idx], errors=errors)[raw_text_len:]
    if verbose:
        print("\nRaw Generate w/o EOD:", tokenizer.decode(tokens, errors=errors)[raw_text_len:])
        print("\nRaw Generate:", trim_decode_tokens)
        print("\nEnd Reason:", end_reason)
    for stop_word in stop_words:
        trim_decode_tokens = trim_decode_tokens.replace(stop_word, "").strip()
    trim_decode_tokens = trim_decode_tokens.strip()
    if verbose:
        print("\nGenerate:", trim_decode_tokens)

    if return_end_reason:
        return trim_decode_tokens, end_reason
    else:
        return trim_decode_tokens


def get_stop_words_ids(chat_format, tokenizer):
    if chat_format == "raw":
        stop_words_ids = [tokenizer.encode("Human:"), [tokenizer.eod_id]]
    elif chat_format == "chatml":
        stop_words_ids = [[tokenizer.im_end_id], [tokenizer.im_start_id]]
    else:
        raise NotImplementedError(f"Unknown chat format {chat_format!r}")
    return stop_words_ids


def get_date(date_str):
    # 将日期时间字符串转换为datetime对象
    date_obj = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
    # 将datetime对象格式化为日期字符串
    date_str = date_obj.strftime('%Y-%m-%d')
    return date_str


def estimate_tokens(text: str) -> int:
    """估算文本的token数量
    
    一般规则：
    - 英文单词约等于1个token
    - 中文字符约等于2个token
    - 标点符号约等于0.5个token
    """
    # 英文单词数
    english_words = len(re.findall(r'[a-zA-Z]+', text))
    
    # 中文字符数
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    
    # 标点符号数
    punctuations = len(re.findall(r'[^\w\s\u4e00-\u9fff]', text))
    
    # 估算总token数
    total_tokens = english_words + (chinese_chars * 2) + (punctuations * 0.5)
    
    return int(total_tokens)

def split_text(text: str, max_tokens: int = 15000) -> List[str]:
    """将长文本分割成较小的块，根据token数量决定块数
    
    Args:
        text: 待分割的文本
        max_tokens: 每个块的最大token数（留出4000 tokens给响应）
        
    Returns:
        List[str]: 分割后的文本块列表
    """
    if not text:
        return []
        
    # 估算总token数
    total_tokens = estimate_tokens(text)
    
    # 如果文本小于最大token数，直接返回
    if total_tokens <= max_tokens:
        return [text]
    
    # 计算需要分割的块数
    num_chunks = (total_tokens + max_tokens - 1) // max_tokens
    
    # 处理不同的分隔符
    separators = [
        '\n\n',      # 双换行（段落）
        '\n',        # 单换行
        '。',        # 中文句号
        '！',        # 中文感叹号
        '？',        # 中文问号
        '；',        # 中文分号
        '. ',       # 英文句号+空格
        '! ',       # 英文感叹号+空格
        '? ',       # 英文问号+空格
        '; '        # 英文分号+空格
    ]
    
    # 按分隔符分割文本
    paragraphs = []
    temp_text = text
    for sep in separators:
        if len(paragraphs) > num_chunks:
            break
        parts = [p.strip() for p in temp_text.split(sep) if p.strip()]
        if len(parts) > 1:
            paragraphs.extend(parts)
            break
        temp_text = temp_text.replace(sep, '<SPLIT>')
    
    if not paragraphs:
        # 如果没有找到合适的分隔符，按字符分割
        paragraphs = [text[i:i+max_tokens] for i in range(0, len(text), max_tokens)]
        return paragraphs
    
    # 合并段落成块，确保每块的token数接近目标大小
    chunks = []
    current_chunk = []
    current_tokens = 0
    target_tokens = total_tokens // num_chunks
    
    for para in paragraphs:
        para_tokens = estimate_tokens(para)
        if current_tokens + para_tokens > target_tokens and current_chunk:
            chunks.append(' '.join(current_chunk))
            current_chunk = [para]
            current_tokens = para_tokens
        else:
            current_chunk.append(para)
            current_tokens += para_tokens
    
    # 添加最后一个块
    if current_chunk:
        chunks.append(' '.join(current_chunk))
    
    return chunks




def get_filename_from_url(url):
    # 解析URL
    parsed_url = urlparse(url)
    # 获取路径部分
    path = parsed_url.path
    # 对路径进行解码，以处理其中的中文和空格
    decoded_path = unquote(path)
    # 分割路径以获得最后一个元素（即文件名）
    filename = decoded_path.split('/')[-1]
    return filename

def collapse_think_response(text: str) -> str:
    """处理LLM响应文本，将<think>标签转换为可折叠的HTML结构

    Args:
        text: LLM返回的原始文本

    Returns:
        str: 处理后的文本，包含可折叠的思考过程
    """
    try:
        # 移除空白字符
        text = text.strip()

        # 如果包含<think>标签
        if '<think>' in text:
            # 提取思考内容和最终答案
            think_pattern = r'<think>([\s\S]*?)</think>'
            think_match = re.search(think_pattern, text)

            if think_match:
                think_content = think_match.group(1).strip()
                # 移除<think>标签，保留其他内容作为最终答案
                final_answer = re.sub(think_pattern, '', text).strip()

                # 构建可折叠的HTML结构
                collapsible_html = f'''
<div class="ai-thinking-container">
    <div class="ai-thinking-toggle" onclick="toggleThinking(this)">
        <svg class="ai-thinking-icon" width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.786 4.167L2.765 1.259c-.416-.4-.985-.482-1.273-.183-.287.298-.183.864.233 1.264l3.021 2.908c.416.4.986.482 1.273.184.287-.299.183-.865-.233-1.265z" fill="currentColor"></path>
            <path d="M8.197 1.206L5.288 4.208c-.4.413-.484.982-.187 1.27.298.289.864.187 1.265-.227L9.274 2.25c.401-.414.485-.983.187-1.271-.297-.288-.863-.187-1.264.227z" fill="currentColor"></path>
        </svg>
        <span class="ai-thinking-label">思考过程</span>
    </div>
    <div class="ai-thinking-content" style="display: none;">
        <div class="ai-thinking-text">{think_content}</div>
    </div>
</div>

{final_answer}'''

                return collapsible_html

        # 如果不包含<think>标签，直接返回清理后的文本
        return text

    except Exception as e:
        print(f"Error processing LLM response: {str(e)}")
        return text


# 保持向后兼容性的别名
clean_llm_response = collapse_think_response