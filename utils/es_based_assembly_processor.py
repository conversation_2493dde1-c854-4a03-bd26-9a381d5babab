#!/usr/bin/env python3
"""
基于ES的汇编处理器
实现方案B：文档切片 → ES索引 → 智能检索 → 信息融合
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

from utils.llm import LLM
from utils.log import log
from utils.config import config
from knowledge_control import KnowledgeControl
from utils.minio_client import MinioClient
from utils.intelligent_es_extractor import IntelligentESExtractor


class ESBasedAssemblyProcessor:
    """基于ES的汇编处理器"""
    
    def __init__(self):
        self.llm = LLM()
        self.knowledge_control = KnowledgeControl()
        self.minio = MinioClient()
        self.es_extractor = IntelligentESExtractor()
        
        # 处理配置
        self.batch_size = config.get("es_assembly.batch_size", 10)
        self.max_concurrent = config.get("es_assembly.max_concurrent", 5)
        
        # ES配置
        self.es_index_prefix = config.get("es_assembly.index_prefix", "project_docs")
        
        # 初始化标志
        self._initialized = False
    
    async def initialize(self):
        """初始化处理器"""
        if not self._initialized:
            await self.knowledge_control.initialize()
            # TODO: 初始化ES连接
            self._initialized = True
            log.info("ES汇编处理器初始化完成")
    
    async def process_assembly_with_es(
        self,
        project_name: str,
        action: str,
        urls: List[str],
        task_id: str,
        task_manager=None,
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """基于ES的汇编处理主流程"""
        try:
            await self.initialize()
            
            log.info(f"开始ES汇编处理: {project_name}, 类型: {action}, 文档数: {len(urls)}")
            
            # 阶段1：文档预处理和ES索引
            es_index = await self._index_documents_to_es(
                urls, project_name, action, task_id, task_manager, force_refresh
            )
            
            # 阶段2：基于ES的智能信息抽取
            extracted_info = await self.es_extractor.extract_project_info_from_es(
                project_name, action, es_index
            )
            
            # 阶段3：结果后处理和验证
            final_result = await self._post_process_results(
                extracted_info, project_name, action
            )
            
            log.info(f"ES汇编处理完成: {project_name}")
            return final_result
            
        except Exception as e:
            log.error(f"ES汇编处理失败: {e}")
            return {"error": str(e)}
    
    async def _index_documents_to_es(
        self,
        urls: List[str],
        project_name: str,
        action: str,
        task_id: str,
        task_manager=None,
        force_refresh: bool = False
    ) -> str:
        """将文档索引到ES"""
        try:
            # 生成ES索引名
            es_index = f"{self.es_index_prefix}_{project_name.lower().replace(' ', '_')}_{action}"
            
            # 如果强制刷新，删除现有索引
            if force_refresh:
                await self._delete_es_index(es_index)
                log.info(f"已删除现有ES索引: {es_index}")
            
            # 检查索引是否已存在
            if await self._es_index_exists(es_index) and not force_refresh:
                log.info(f"ES索引已存在，跳过索引过程: {es_index}")
                return es_index
            
            # 批量处理文档
            indexed_count = 0
            failed_count = 0
            
            for i in range(0, len(urls), self.batch_size):
                batch_urls = urls[i:i + self.batch_size]
                
                # 并发处理批次
                tasks = [
                    self._index_single_document(url, project_name, action, es_index)
                    for url in batch_urls
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 统计结果
                for result in results:
                    if isinstance(result, Exception):
                        failed_count += 1
                        log.error(f"文档索引失败: {result}")
                    elif result:
                        indexed_count += 1
                    else:
                        failed_count += 1
                
                # 更新任务进度
                if task_manager:
                    await task_manager.update_result(
                        task_id,
                        processed_files=indexed_count + failed_count,
                        success_files=indexed_count,
                        failed_files=failed_count
                    )
            
            log.info(f"文档索引完成: 成功{indexed_count}, 失败{failed_count}")
            return es_index
            
        except Exception as e:
            log.error(f"文档索引过程失败: {e}")
            raise
    
    async def _index_single_document(
        self,
        url: str,
        project_name: str,
        action: str,
        es_index: str
    ) -> bool:
        """索引单个文档"""
        try:
            # 1. 下载JSON文件
            json_url = os.path.splitext(url)[0] + ".json"
            json_path = self.minio.download_file(json_url)
            
            # 2. 读取文档内容
            with open(json_path, 'r', encoding='utf-8') as f:
                doc_data = json.load(f)
            
            # 3. 使用knowledge_control处理文档（生成切片和向量）
            success = await self.knowledge_control.process_document(
                doc_data, project_name, action
            )
            
            if not success:
                log.error(f"knowledge_control处理失败: {url}")
                return False
            
            # 4. 从knowledge_control获取处理后的切片数据
            chunks_data = await self._get_processed_chunks(doc_data, project_name)
            
            # 5. 将切片索引到ES
            indexed_chunks = await self._index_chunks_to_es(
                chunks_data, es_index, url, doc_data.get("source", "")
            )
            
            log.info(f"文档索引成功: {url}, 切片数: {indexed_chunks}")
            return True
            
        except Exception as e:
            log.error(f"索引单个文档失败 {url}: {e}")
            return False
    
    async def _get_processed_chunks(
        self,
        doc_data: Dict[str, Any],
        project_name: str
    ) -> List[Dict[str, Any]]:
        """从knowledge_control获取处理后的切片数据"""
        try:
            # TODO: 从knowledge_control的数据库中查询切片数据
            # 这里需要根据实际的knowledge_control实现来调整
            
            # 模拟切片数据
            chunks = []
            for i, page in enumerate(doc_data.get("pages", [])):
                content = page.get("content", "")
                if content:
                    # 简单分片（实际应该使用knowledge_control的分片逻辑）
                    chunk_size = 512
                    for j in range(0, len(content), chunk_size):
                        chunk_text = content[j:j + chunk_size]
                        
                        # 生成向量（实际应该从knowledge_control获取）
                        embedding = await self.llm.get_embedding(chunk_text)
                        
                        chunks.append({
                            "chunk_text": chunk_text,
                            "embedding": embedding,
                            "page_number": i + 1,
                            "chunk_index": j // chunk_size,
                            "chunk_id": f"page_{i+1}_chunk_{j//chunk_size}"
                        })
            
            return chunks
            
        except Exception as e:
            log.error(f"获取处理后切片失败: {e}")
            return []
    
    async def _index_chunks_to_es(
        self,
        chunks_data: List[Dict[str, Any]],
        es_index: str,
        source_url: str,
        source_name: str
    ) -> int:
        """将切片索引到ES"""
        try:
            # TODO: 实现ES批量索引
            # 这里应该使用ES的bulk API进行批量索引
            
            indexed_count = 0
            
            for chunk in chunks_data:
                # 构建ES文档
                es_doc = {
                    "chunk_text": chunk["chunk_text"],
                    "embedding": chunk["embedding"],
                    "metadata": {
                        "source_url": source_url,
                        "source_name": source_name,
                        "page_number": chunk["page_number"],
                        "chunk_index": chunk["chunk_index"],
                        "chunk_id": chunk["chunk_id"],
                        "indexed_at": datetime.now().isoformat()
                    }
                }
                
                # TODO: 索引到ES
                # await self.es_client.index(index=es_index, body=es_doc)
                indexed_count += 1
            
            log.debug(f"切片索引完成: {indexed_count}个切片")
            return indexed_count
            
        except Exception as e:
            log.error(f"切片索引失败: {e}")
            return 0
    
    async def _es_index_exists(self, es_index: str) -> bool:
        """检查ES索引是否存在"""
        # TODO: 实现ES索引存在性检查
        # return await self.es_client.indices.exists(index=es_index)
        return False  # 模拟索引不存在
    
    async def _delete_es_index(self, es_index: str):
        """删除ES索引"""
        # TODO: 实现ES索引删除
        # await self.es_client.indices.delete(index=es_index, ignore=[400, 404])
        pass
    
    async def _post_process_results(
        self,
        extracted_info: Dict[str, Any],
        project_name: str,
        action: str
    ) -> Dict[str, Any]:
        """后处理抽取结果"""
        try:
            # 1. 结果验证
            validation_result = await self._validate_extracted_info(extracted_info)
            
            # 2. 结果增强（如果需要）
            enhanced_result = await self._enhance_extracted_info(
                extracted_info, project_name, action
            )
            
            # 3. 生成最终结果
            final_result = {
                "project_name": project_name,
                "action": action,
                "extracted_info": enhanced_result.get("extracted_info", {}),
                "field_analysis": enhanced_result.get("field_analysis", {}),
                "validation_result": validation_result,
                "processing_method": "es_based_intelligent_extraction",
                "processing_time": datetime.now().isoformat()
            }
            
            return final_result
            
        except Exception as e:
            log.error(f"结果后处理失败: {e}")
            return extracted_info  # 返回原始结果
    
    async def _validate_extracted_info(
        self,
        extracted_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证抽取信息"""
        try:
            validation_result = {
                "is_valid": True,
                "issues": [],
                "completeness_score": 0.0
            }
            
            # 检查字段完整性
            extracted_fields = extracted_info.get("extracted_info", {})
            expected_fields = ["project_name", "total_amount", "responsible_unit", "initiation_date"]
            
            missing_fields = [field for field in expected_fields if not extracted_fields.get(field)]
            if missing_fields:
                validation_result["issues"].append({
                    "type": "missing_fields",
                    "fields": missing_fields
                })
            
            # 计算完整性分数
            completeness = (len(expected_fields) - len(missing_fields)) / len(expected_fields)
            validation_result["completeness_score"] = round(completeness, 2)
            
            if completeness < 0.7:
                validation_result["is_valid"] = False
                validation_result["issues"].append({
                    "type": "low_completeness",
                    "score": completeness
                })
            
            return validation_result
            
        except Exception as e:
            log.error(f"结果验证失败: {e}")
            return {"is_valid": False, "error": str(e)}
    
    async def _enhance_extracted_info(
        self,
        extracted_info: Dict[str, Any],
        project_name: str,
        action: str
    ) -> Dict[str, Any]:
        """增强抽取信息"""
        try:
            # 如果某些关键字段缺失，尝试使用LLM进行补充
            enhanced_info = extracted_info.copy()
            
            extracted_fields = enhanced_info.get("extracted_info", {})
            
            # 如果项目名称缺失，使用传入的项目名称
            if not extracted_fields.get("project_name"):
                extracted_fields["project_name"] = project_name
                enhanced_info["field_analysis"]["project_name"] = {
                    "method": "fallback_to_input",
                    "confidence": 0.8
                }
            
            return enhanced_info
            
        except Exception as e:
            log.error(f"信息增强失败: {e}")
            return extracted_info
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        try:
            return {
                "processor_info": {
                    "type": "es_based_assembly",
                    "version": "1.0",
                    "initialized": self._initialized,
                    "batch_size": self.batch_size,
                    "max_concurrent": self.max_concurrent
                },
                "es_config": {
                    "index_prefix": self.es_index_prefix
                },
                "capabilities": {
                    "multi_dimensional_search": True,
                    "semantic_retrieval": True,
                    "intelligent_extraction": True,
                    "result_validation": True
                }
            }
            
        except Exception as e:
            log.error(f"获取处理统计失败: {e}")
            return {"error": str(e)}
