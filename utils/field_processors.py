from typing import Any
from datetime import datetime
import re
from utils.field_types import FieldType

def process_date(value: str) -> str:
    """处理日期字段，转换为YYYY-MM-DD格式
    
    Args:
        value: 输入的日期字符串
        
    Returns:
        str: YYYY-MM-DD格式的日期字符串，如果无效则返回"未提及"
    """
    if not value or value == "未提及":
        return "1900-01-01"
    try:
        # 如果输入包含多个日期，只处理第一个
        date_str = str(value).split(',')[0].strip()
        
        # 如果已经是YYYY-MM-DD格式，直接返回
        if re.match(r'\d{4}-\d{2}-\d{2}$', date_str):
            return date_str
        
        # 匹配多种日期格式
        patterns = [
            # 标准格式：2023-12-15 或 2023/12/15 或 2023.12.15
            r"(\d{4})[-./\s]*(\d{1,2})[-./\s]*(\d{1,2})",
            # 中文格式：2023年12月15日
            r"(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})?[日]?",
            # 年月格式：2023-12 或 2023年12月
            r"(\d{4})[-年./\s]*(\d{1,2})[-月]?",
            # 纯数字格式：202312 或 20231215
            r"(\d{4})(\d{2})(\d{2})?",
            # 纯年份格式：2023 或 2023年
            r"(\d{4})[年]?"
        ]
        
        # 尝试每种模式
        for pattern in patterns:
            match = re.search(pattern, date_str)
            if match:
                year = match.group(1)
                month = match.group(2) if len(match.groups()) >= 2 else "01"
                day = match.group(3) if len(match.groups()) >= 3 and match.group(3) else "01"
                
                # 验证年月日的有效性
                year_num = int(year)
                month_num = int(month)
                day_num = int(day)
                
                if 1900 <= year_num <= 2100 and 1 <= month_num <= 12 and 1 <= day_num <= 31:
                    # 使用 zfill 而不是格式化代码
                    return f"{year}-{str(month_num).zfill(2)}-{str(day_num).zfill(2)}"
                
        return None  # 返回 None 而不是 "未提及"，这样在数据库中会存储为 NULL
        
    except Exception as e:
        print(f"Error processing date '{value}': {str(e)}")
        return "1900-01-01"

def process_amount(value: str) -> str:
    """处理金额字段"""
    if not value or value == "未提及":
        return "未提及"
    try:
        # 如果输入是数字类型
        if isinstance(value, (int, float)):
            return f"{value}万"
            
        # 转换为字符串并清理空格
        value_str = str(value).strip()
        
        # 提取数字部分
        number_match = re.search(r"([\d.]+)", value_str)
        if not number_match:
            return "未提及"
            
        # 提取单位（如果有）
        unit = "万"  # 默认单位
        if "亿" in value_str:
            unit = "亿"
        elif "千万" in value_str:
            unit = "千万"
        elif "万" in value_str:
            unit = "万"
            
        # 返回原始数字加单位
        return f"{number_match.group(1)}{unit}"
        
    except Exception as e:
        print(f"Error processing amount: {str(e)}")
        return "未提及"

def process_list(value: Any) -> str:
    """处理列表字段"""
    if not value or value == "未提及":
        return "未提及"
    try:
        if isinstance(value, list):
            return "；".join(str(item) for item in value)
        return str(value)
    except Exception as e:
        print(f"Error processing list: {str(e)}")
        return "未提及"

def process_text(value: str) -> str:
    """处理文本字段"""
    if not value or value == "未提及":
        return "未提及"
    try:
        return str(value).strip()
    except Exception as e:
        print(f"Error processing text: {str(e)}")
        return "未提及"

# 字段处理器映射
FIELD_PROCESSORS = {
    FieldType.DATE: process_date,
    FieldType.AMOUNT: process_amount,
    FieldType.LIST: process_list,
    FieldType.TEXT: process_text
} 