{"settings": {"max_ngram_diff": "50", "routing": {"allocation": {"include": {"_tier_preference": "data_content"}}}, "mapping": {"total_fields": {"limit": "2000"}}, "number_of_shards": "1", "provided_name": "docs", "creation_date": "*************", "analysis": {"analyzer": {"text_analyzer": {"filter": ["lowercase", "asciifolding"], "type": "custom", "tokenizer": "ik_max_word"}}}, "number_of_replicas": "1", "uuid": "jcrEsJ4QRQajwsKbXh_y8A", "version": {"created": "8505000"}}, "mappings": {"properties": {"action": {"type": "keyword"}, "chunk_num": {"type": "long"}, "content": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}, "analyzer": "ik_max_word", "search_analyzer": "ik_smart"}, "doc_id": {"type": "keyword"}, "embedding": {"type": "dense_vector", "dims": 1024, "index": true, "similarity": "cosine", "index_options": {"type": "int8_hnsw", "m": 16, "ef_construction": 100}}, "metadata": {"dynamic": "true", "properties": {"chunk_info": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "doc_type": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "enable_handwrite": {"type": "boolean"}, "enable_ocr": {"type": "boolean"}, "enable_seal": {"type": "boolean"}, "processed_at": {"type": "date"}}}, "page_num": {"type": "integer"}, "project_name": {"type": "keyword", "fields": {"text": {"type": "text", "analyzer": "ik_smart"}}}, "source": {"type": "keyword"}, "year": {"type": "integer"}}}}