# 合并两种提取方式的结果
MERGE_EXTRACTIONS_PROMPT = """请合并以下两种方式提取的信息。

第一种方式结果：
{method_result}

第二种方式结果：
{config_result}

合并规则：
1. 日期字段处理规则：
   - start_date: 选择最早的有效日期
     * 如果一个是"未提及"，选择另一个有效日期
     * 如果都有日期，选择较早的日期
     * 例如：2019-10 和 2020-09，应选择 2019-10
   
   - end_date: 选择最晚的有效日期
     * 如果一个是"未提及"，选择另一个有效日期
     * 如果都有日期，选择较晚的日期
     * 例如：2020-10 和 "未提及"，应选择 2020-10

2. 金额字段 (total_investment):
   - 如果一个是"未提及"，选择另一个具体金额
   - 保持"数字+单位"格式，如"150.8万"
   - 例如："150.8万" 和 "未提及"，应选择 "150.8万"

3. 文本字段合并规则:
   - 如果一个是"未提及"，使用另一个的具体内容
   - 如果都有内容且不同，用分号合并，去重
   - 例如：
     * "研究点A" 和 "未提及" -> "研究点A"
     * "研究点A" 和 "研究点B" -> "研究点A；研究点B"

请直接返回合并后的JSON，不要包含任何解释。"""

# 合并多个文件的提取结果
MERGE_FILES_PROMPT = """请合并以下多个文件的提取信息。

提取的信息列表：
{cached_infos}

合并规则：
1. name: 使用固定的项目名称

2. start_date: 
   - 选择最早的有效日期
   - 如果某个值是"未提及"，忽略它
   - 例如：["2019-10", "2020-09", "未提及"] -> "2019-10"

3. end_date:
   - 选择最晚的有效日期
   - 如果某个值是"未提及"，忽略它
   - 例如：["2020-10", "2019-09", "未提及"] -> "2020-10"

4. total_investment:
   - 如果有具体金额，不要选择"未提及"
   - 保持"数字+单位"格式
   - 选择最合理的值
   - 例如：["150.8万", "未提及", "150.8万"] -> "150.8万"

5. responsible_unit:
   - 选择最完整的单位名称
   - 如果有多个不同的有效值，用分号合并
   - 例如：["火星公司", "火星有限责任公司", "未提及"] -> "火星有限责任公司"

6. leader:
   - 选择最完整的负责人信息
   - 如果有多个不同的负责人，用分号合并
   - 例如：["张三", "张三(项目负责人)", "未提及"] -> "张三(项目负责人)"

7. research_points:
   - 合并所有不同的研究点
   - 用分号分隔
   - 去除重复或相似的内容
   - 例如：["研究点A；研究点B", "研究点B；研究点C", "未提及"] -> "研究点A；研究点B；研究点C"

8. innovation:
   - 合并所有不同的创新点
   - 用分号分隔
   - 去除重复或相似的内容
   - 例如：["创新点1；创新点2", "创新点2；创新点3", "未提及"] -> "创新点1；创新点2；创新点3"

9. main_deliverables:
   - 合并所有不同的交付成果
   - 用分号分隔
   - 去除重复或相似的内容
   - 例如：["成果A；成果B", "成果B；成果C", "未提及"] -> "成果A；成果B；成果C"

10. patent:
    - 合并所有不同的专利信息
    - 用分号分隔
    - 去除重复或相似的内容
    - 例如：["专利1；专利2", "专利2；专利3", "未提及"] -> "专利1；专利2；专利3"

注意事项：
1. 对于日期字段：
   - 必须是YYYY-MM格式
   - 选择最早的start_date和最晚的end_date
   - 忽略"未提及"的值

2. 对于金额字段：
   - 必须是"数字+单位"格式
   - 在多个有效金额中选择最合理的值
   - 忽略"未提及"的值

3. 对于文本字段：
   - 选择最完整的信息
   - 合并不同的有效内容
   - 用分号分隔多个项目
   - 去除重复或相似的内容
   - 忽略"未提及"的值

4. 对于列表类型字段（research_points, innovation, main_deliverables, patent）：
   - 合并所有不同的内容
   - 用分号分隔
   - 去除重复或相似的内容
   - 保持内容的完整性
   - 忽略"未提及"的值

请直接返回JSON结果，不要包含任何解释。"""

# 系统提示词
SYSTEM_PROMPT = "你是一个信息合并助手。请直接返回JSON格式的结果，不要包含任何解释或注释。"

# JSON输出示例
JSON_EXAMPLE = {
    "name": "项目名称",
    "start_date": "2019-10",
    "end_date": "2020-10",
    "total_investment": "150.8万",
    "responsible_unit": "单位A",
    "leader": "张三",
    "research_points": "研究点1；研究点2",
    "innovation": "创新点1；创新点2",
    "main_deliverables": "成果1；成果2",
    "patent": "专利1；专利2"
} 