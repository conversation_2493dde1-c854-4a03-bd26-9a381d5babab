"""
文档ID生成器
提供多种稳定的文档ID生成策略，解决OCR识别结果不稳定导致的ID变化问题
"""

import hashlib
import re
import os
from typing import Optional, Dict, Any
from urllib.parse import unquote
from pathlib import Path


class DocumentIDGenerator:
    """文档ID生成器类"""
    
    @staticmethod
    def normalize_file_path(file_path: str) -> str:
        """
        标准化文件路径

        Args:
            file_path: 原始文件路径

        Returns:
            str: 标准化后的文件路径
        """
        try:
            # URL解码
            path = unquote(file_path)

            # 标准化路径分隔符
            path = path.replace('\\', '/')

            # 标准化空格：移除多余空格，统一空格字符
            path = re.sub(r'\s+', ' ', path.strip())

            # 标准化中文标点符号
            path = path.replace('（', '(').replace('）', ')')
            path = path.replace('【', '[').replace('】', ']')
            path = path.replace('"', '"').replace('"', '"')
            path = path.replace(''', "'").replace(''', "'")

            # 移除文件名中的时间戳后缀 (例如: _1640995200)
            path = re.sub(r'_\d{10,}(?=\.[^/]*$)', '', path)

            # 移除文件名中的版本号后缀 (例如: _v1, _v2)
            path = re.sub(r'_v\d+(?=\.[^/]*$)', '', path)

            # 移除文件名中的副本标识 (例如: _copy, _副本, _备份)
            path = re.sub(r'_(?:copy|副本|备份|拷贝)(?=\.[^/]*$)', '', path, flags=re.IGNORECASE)

            # 标准化连续的路径分隔符
            path = re.sub(r'/+', '/', path)

            # 移除路径开头和结尾的分隔符
            path = path.strip('/')

            return path

        except Exception as e:
            print(f"标准化路径失败: {e}")
            return file_path
    
    @staticmethod
    def extract_document_metadata(file_path: str) -> Dict[str, str]:
        """
        从文件路径中提取文档元数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 包含文档元数据的字典
        """
        try:
            path_obj = Path(file_path)
            
            # 提取基本信息
            metadata = {
                'filename': path_obj.name,
                'stem': path_obj.stem,  # 不含扩展名的文件名
                'suffix': path_obj.suffix,  # 扩展名
                'parent_dir': path_obj.parent.name,
                'full_path': str(path_obj)
            }
            
            # 尝试从路径中提取项目信息
            path_parts = path_obj.parts
            if len(path_parts) > 1:
                # 假设项目名在路径的某个层级中
                for part in path_parts:
                    if '项目' in part or '工程' in part or '系统' in part:
                        metadata['project_hint'] = part
                        break
            
            return metadata
            
        except Exception as e:
            print(f"提取文档元数据失败: {e}")
            return {'filename': os.path.basename(file_path)}
    
    @classmethod
    def generate_stable_id(
        cls,
        file_path: str,
        project_name: Optional[str] = None,
        strategy: str = "path_based"
    ) -> str:
        """
        生成稳定的文档ID
        
        Args:
            file_path: 文件路径
            project_name: 项目名称（可选）
            strategy: 生成策略
                - "path_based": 基于标准化文件路径
                - "metadata_based": 基于文档元数据
                - "project_path": 基于项目名称和文件路径
                - "hierarchical": 基于层次化路径结构
        
        Returns:
            str: 32位MD5哈希值
        """
        try:
            if strategy == "path_based":
                # 策略1：基于标准化文件路径
                normalized_path = cls.normalize_file_path(file_path)
                return hashlib.md5(normalized_path.encode('utf-8')).hexdigest()
                
            elif strategy == "metadata_based":
                # 策略2：基于文档元数据
                metadata = cls.extract_document_metadata(file_path)
                # 使用文件名（不含扩展名）+ 父目录名
                key_parts = [
                    metadata.get('stem', ''),
                    metadata.get('parent_dir', ''),
                    metadata.get('suffix', '')
                ]
                combined_key = '|'.join(filter(None, key_parts))
                return hashlib.md5(combined_key.encode('utf-8')).hexdigest()
                
            elif strategy == "project_path":
                # 策略3：基于项目名称和文件路径
                normalized_path = cls.normalize_file_path(file_path)
                if project_name:
                    # 标准化项目名称
                    normalized_project = re.sub(r'\s+', '', project_name.strip())
                    combined_key = f"{normalized_project}:{normalized_path}"
                else:
                    combined_key = normalized_path
                return hashlib.md5(combined_key.encode('utf-8')).hexdigest()
                
            elif strategy == "hierarchical":
                # 策略4：基于层次化路径结构
                path_obj = Path(file_path)
                # 使用最后两级目录 + 文件名（不含扩展名）
                parts = path_obj.parts
                if len(parts) >= 2:
                    key_parts = parts[-2:]  # 最后两级
                else:
                    key_parts = parts
                
                # 添加文件名（不含扩展名）
                stem = path_obj.stem
                hierarchical_key = '/'.join(key_parts) + '/' + stem
                
                # 标准化
                hierarchical_key = cls.normalize_file_path(hierarchical_key)
                return hashlib.md5(hierarchical_key.encode('utf-8')).hexdigest()
                
            else:
                raise ValueError(f"未知的生成策略: {strategy}")
                
        except Exception as e:
            print(f"生成稳定ID失败: {e}")
            # 最简单的后备方案
            fallback = file_path.strip()
            return hashlib.md5(fallback.encode('utf-8')).hexdigest()
    
    @classmethod
    def generate_content_independent_id(
        cls,
        file_path: str,
        project_name: Optional[str] = None,
        additional_metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        生成与内容无关的稳定文档ID

        这个方法专门用于解决OCR识别结果不稳定的问题，
        完全不依赖文件内容，只基于文件路径和元数据。

        策略：最小化标准化，保留文档的重要区别特征

        Args:
            file_path: 文件路径
            project_name: 项目名称
            additional_metadata: 额外的元数据

        Returns:
            str: 32位MD5哈希值
        """
        try:
            # 构建稳定的标识符组件
            components = []

            # 1. 轻度标准化的文件路径（保留重要信息）
            normalized_path = cls._light_normalize_path(file_path)
            components.append(f"path:{normalized_path}")

            # 2. 轻度标准化的项目名称（如果提供）
            if project_name:
                normalized_project = cls._light_normalize_text(project_name)
                components.append(f"project:{normalized_project}")

            # 3. 额外元数据（如果提供）
            if additional_metadata:
                for key, value in sorted(additional_metadata.items()):
                    if value:  # 只包含非空值
                        normalized_value = cls._light_normalize_text(str(value))
                        components.append(f"{key}:{normalized_value}")

            # 组合所有组件
            combined_key = '|'.join(filter(None, components))  # 过滤空组件

            # 生成MD5哈希
            return hashlib.md5(combined_key.encode('utf-8')).hexdigest()

        except Exception as e:
            print(f"生成内容无关ID失败: {e}")
            # 最简单的后备方案
            return cls.generate_stable_id(file_path, project_name, "path_based")

    @staticmethod
    def _light_normalize_path(file_path: str) -> str:
        """
        轻度标准化文件路径
        只处理明显的格式差异，保留语义信息
        """
        try:
            # URL解码
            path = unquote(file_path)

            # 统一路径分隔符
            path = path.replace('\\', '/')

            # 标准化连续空格为单个空格
            path = re.sub(r'\s+', ' ', path.strip())

            # 标准化中文括号（这个通常是输入法差异）
            path = path.replace('（', '(').replace('）', ')')

            # 移除路径开头和结尾的分隔符
            path = path.strip('/')

            return path

        except Exception as e:
            print(f"轻度标准化路径失败: {e}")
            return file_path

    @staticmethod
    def _light_normalize_text(text: str) -> str:
        """
        轻度标准化文本
        只处理明显的格式差异，保留语义信息
        """
        try:
            # 移除首尾空格
            text = text.strip()

            # 标准化连续空格为单个空格
            text = re.sub(r'\s+', ' ', text)

            # 标准化中文括号（这个通常是输入法差异）
            text = text.replace('（', '(').replace('）', ')')

            return text

        except Exception as e:
            print(f"轻度标准化文本失败: {e}")
            return text

    @classmethod
    def generate_simple_combined_id(
        cls,
        file_path: str,
        project_name: Optional[str] = None
    ) -> str:
        """
        生成简单组合ID - 只做最基本的处理

        只统一路径分隔符，其他完全保持原样
        这样既解决了OCR问题，又保留了文档的所有区别特征

        Args:
            file_path: 原始文件路径
            project_name: 原始项目名称

        Returns:
            str: 32位MD5哈希值
        """
        try:
            # 构建组合键
            components = []

            if project_name:
                # 项目名称只去除首尾空格
                components.append(project_name.strip())

            # 文件路径只统一分隔符（解决Windows/Linux差异）
            normalized_path = file_path.strip().replace('\\', '/')
            components.append(normalized_path)

            # 直接组合
            combined_key = '|'.join(components)

            # 生成MD5哈希
            return hashlib.md5(combined_key.encode('utf-8')).hexdigest()

        except Exception as e:
            print(f"生成简单组合ID失败: {e}")
            # 后备方案：只使用文件路径
            normalized_path = file_path.strip().replace('\\', '/')
            return hashlib.md5(normalized_path.encode('utf-8')).hexdigest()
    
    @staticmethod
    def validate_doc_id(doc_id: str) -> bool:
        """
        验证文档ID格式
        
        Args:
            doc_id: 文档ID
            
        Returns:
            bool: 是否为有效的文档ID
        """
        if not doc_id:
            return False
        
        # 检查是否为32位十六进制字符串（MD5格式）
        if len(doc_id) == 32 and re.match(r'^[a-f0-9]{32}$', doc_id.lower()):
            return True
        
        return False
    
    @classmethod
    def migrate_existing_doc_id(
        cls,
        old_doc_id: str,
        file_path: str,
        project_name: Optional[str] = None
    ) -> str:
        """
        迁移现有的文档ID到新的稳定格式
        
        Args:
            old_doc_id: 旧的文档ID
            file_path: 文件路径
            project_name: 项目名称
            
        Returns:
            str: 新的稳定文档ID
        """
        try:
            # 生成新的稳定ID
            new_doc_id = cls.generate_content_independent_id(file_path, project_name)
            
            print(f"文档ID迁移: {old_doc_id} -> {new_doc_id}")
            print(f"文件路径: {file_path}")
            print(f"项目名称: {project_name}")
            
            return new_doc_id
            
        except Exception as e:
            print(f"迁移文档ID失败: {e}")
            return old_doc_id  # 返回原ID作为后备


# 便捷函数
def generate_document_id(
    file_path: str,
    project_name: Optional[str] = None,
    strategy: str = "simple_combined"
) -> str:
    """
    生成文档ID的便捷函数

    Args:
        file_path: 文件路径
        project_name: 项目名称
        strategy: 生成策略，默认为"simple_combined"（推荐）

    Returns:
        str: 文档ID
    """
    if strategy == "simple_combined":
        return DocumentIDGenerator.generate_simple_combined_id(file_path, project_name)
    elif strategy == "content_independent":
        return DocumentIDGenerator.generate_content_independent_id(file_path, project_name)
    else:
        return DocumentIDGenerator.generate_stable_id(file_path, project_name, strategy)


if __name__ == "__main__":
    # 测试用例
    test_paths = [
        "docs/分省公司专业机构管理应用建设 （公司食堂管理系统）/分省公司专业机构管理应用建设 （公司食堂管理系统）.json",
        "docs/海南电网调度应急处置辅助系统研究与应用科技项目档案/2019005-721-001-04 海南电网调度应急处置辅助系统研究与应用科技项目开题报告.json",
        "docs/项目文档/技术方案_v2_1640995200.pdf"
    ]
    
    project_name = "公司食堂管理系统"
    
    print("=== 文档ID生成测试 ===")
    for path in test_paths:
        print(f"\n文件路径: {path}")
        
        # 测试不同策略
        strategies = ["path_based", "metadata_based", "project_path", "hierarchical"]
        for strategy in strategies:
            doc_id = DocumentIDGenerator.generate_stable_id(path, project_name, strategy)
            print(f"  {strategy}: {doc_id}")
        
        # 测试内容无关策略
        content_independent_id = DocumentIDGenerator.generate_content_independent_id(path, project_name)
        print(f"  content_independent: {content_independent_id}")
        
        # 验证ID格式
        is_valid = DocumentIDGenerator.validate_doc_id(content_independent_id)
        print(f"  ID格式有效: {is_valid}")
