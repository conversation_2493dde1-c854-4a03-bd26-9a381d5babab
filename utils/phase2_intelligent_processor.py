#!/usr/bin/env python3
"""
第二阶段智能处理器
整合智能抽取和结果融合的完整处理流程
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

from utils.log import log
from utils.config import config
from utils.compatible_knowledge_indexer import CompatibleKnowledgeIndexer
from utils.intelligent_field_extractor import IntelligentFieldExtractor
from utils.result_fusion_validator import ResultFusionValidator


class Phase2IntelligentProcessor:
    """第二阶段智能处理器"""
    
    def __init__(self):
        self.indexer = CompatibleKnowledgeIndexer()
        self.extractor = IntelligentFieldExtractor()
        self.validator = ResultFusionValidator()
        
        # 处理配置
        self.enable_parallel_processing = config.get("phase2.parallel_processing", True)
        self.max_concurrent_extractions = config.get("phase2.max_concurrent", 3)
        self.enable_result_caching = config.get("phase2.result_caching", True)
        
        self._initialized = False
    
    async def initialize(self):
        """初始化处理器"""
        if not self._initialized:
            # 并行初始化各个组件
            await asyncio.gather(
                self.indexer.initialize(),
                self.extractor.initialize()
            )
            self._initialized = True
            log.info("第二阶段智能处理器初始化完成")
    
    async def process_intelligent_assembly(
        self,
        project_name: str,
        action: str,
        urls: List[str],
        target_fields: Optional[List[str]] = None,
        force_refresh: bool = False,
        task_id: Optional[str] = None,
        task_manager=None
    ) -> Dict[str, Any]:
        """智能汇编处理主流程"""
        try:
            await self.initialize()
            
            log.info(f"开始智能汇编处理: {project_name}, 类型: {action}, 文档数: {len(urls)}")
            
            processing_start_time = datetime.now()
            
            # 阶段1：文档索引（如果需要）
            indexing_result = await self._ensure_documents_indexed(
                project_name, action, urls, force_refresh, task_manager, task_id
            )
            
            if "error" in indexing_result:
                return {"error": f"文档索引失败: {indexing_result['error']}"}
            
            # 阶段2：智能字段抽取
            extraction_result = await self._perform_intelligent_extraction(
                project_name, action, target_fields, task_manager, task_id
            )
            
            if "error" in extraction_result:
                return {"error": f"智能抽取失败: {extraction_result['error']}"}
            
            # 阶段3：结果融合和验证
            validation_result = await self._perform_result_validation(
                extraction_result, task_manager, task_id
            )
            
            if "error" in validation_result:
                return {"error": f"结果验证失败: {validation_result['error']}"}
            
            # 阶段4：生成最终结果
            final_result = await self._generate_final_result(
                indexing_result, extraction_result, validation_result,
                processing_start_time
            )
            
            # 更新任务状态
            if task_manager and task_id:
                await task_manager.update_result(
                    task_id,
                    status="completed",
                    result=final_result
                )
            
            log.info(f"智能汇编处理完成: {project_name}, 质量分数: {final_result.get('quality_score', 0)}")
            return final_result
            
        except Exception as e:
            log.error(f"智能汇编处理失败: {e}")
            
            # 更新任务状态为失败
            if task_manager and task_id:
                await task_manager.update_result(
                    task_id,
                    status="failed",
                    error=str(e)
                )
            
            return {"error": str(e)}
    
    async def _ensure_documents_indexed(
        self,
        project_name: str,
        action: str,
        urls: List[str],
        force_refresh: bool,
        task_manager=None,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """确保文档已被索引"""
        try:
            # 检查是否需要重新索引
            if not force_refresh:
                index_info = await self.indexer.get_index_info(project_name, action)
                if index_info.get("exists") and index_info.get("document_count", 0) > 0:
                    log.info(f"文档已存在索引，跳过索引步骤: {project_name}")
                    return {
                        "status": "skipped",
                        "reason": "documents_already_indexed",
                        "document_count": index_info.get("document_count", 0)
                    }
            
            # 更新任务状态
            if task_manager and task_id:
                await task_manager.update_result(
                    task_id,
                    status="indexing",
                    progress={"stage": "document_indexing", "progress": 0}
                )
            
            # 执行文档索引
            indexing_result = await self.indexer.index_project_documents(
                project_name=project_name,
                action=action,
                urls=urls,
                force_refresh=force_refresh
            )
            
            # 更新进度
            if task_manager and task_id:
                await task_manager.update_result(
                    task_id,
                    progress={"stage": "document_indexing", "progress": 100}
                )
            
            return indexing_result
            
        except Exception as e:
            log.error(f"文档索引阶段失败: {e}")
            return {"error": str(e)}
    
    async def _perform_intelligent_extraction(
        self,
        project_name: str,
        action: str,
        target_fields: Optional[List[str]],
        task_manager=None,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """执行智能字段抽取"""
        try:
            # 更新任务状态
            if task_manager and task_id:
                await task_manager.update_result(
                    task_id,
                    status="extracting",
                    progress={"stage": "field_extraction", "progress": 0}
                )
            
            # 执行智能抽取
            extraction_result = await self.extractor.extract_project_fields(
                project_name=project_name,
                action=action,
                target_fields=target_fields
            )
            
            # 更新进度
            if task_manager and task_id:
                await task_manager.update_result(
                    task_id,
                    progress={"stage": "field_extraction", "progress": 100}
                )
            
            return extraction_result
            
        except Exception as e:
            log.error(f"智能抽取阶段失败: {e}")
            return {"error": str(e)}
    
    async def _perform_result_validation(
        self,
        extraction_result: Dict[str, Any],
        task_manager=None,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """执行结果验证和融合"""
        try:
            # 更新任务状态
            if task_manager and task_id:
                await task_manager.update_result(
                    task_id,
                    status="validating",
                    progress={"stage": "result_validation", "progress": 0}
                )
            
            # 执行结果验证
            validation_result = await self.validator.validate_and_fuse_results(
                extraction_result
            )
            
            # 更新进度
            if task_manager and task_id:
                await task_manager.update_result(
                    task_id,
                    progress={"stage": "result_validation", "progress": 100}
                )
            
            return validation_result
            
        except Exception as e:
            log.error(f"结果验证阶段失败: {e}")
            return {"error": str(e)}
    
    async def _generate_final_result(
        self,
        indexing_result: Dict[str, Any],
        extraction_result: Dict[str, Any],
        validation_result: Dict[str, Any],
        processing_start_time: datetime
    ) -> Dict[str, Any]:
        """生成最终结果"""
        try:
            processing_time = (datetime.now() - processing_start_time).total_seconds()
            
            # 提取关键信息
            validated_fields = validation_result.get("validated_fields", {})
            quality_assessment = validation_result.get("quality_assessment", {})
            
            # 构建最终结果
            final_result = {
                "project_name": validation_result.get("project_name"),
                "action": validation_result.get("action"),
                
                # 主要结果
                "extracted_data": validated_fields,
                "quality_score": quality_assessment.get("overall_score", 0.0),
                "confidence_level": quality_assessment.get("confidence_level", "unknown"),
                "completeness": quality_assessment.get("completeness", 0.0),
                "reliability": quality_assessment.get("reliability", 0.0),
                
                # 处理信息
                "processing_info": {
                    "method": "phase2_intelligent_assembly",
                    "processing_time_seconds": round(processing_time, 2),
                    "indexing_status": indexing_result.get("status", "completed"),
                    "total_documents": indexing_result.get("total_documents", 0),
                    "successful_documents": indexing_result.get("success_documents", 0),
                    "total_chunks": indexing_result.get("total_chunks", 0),
                    "extracted_fields_count": extraction_result.get("successful_fields", 0),
                    "total_fields_count": extraction_result.get("total_fields", 0)
                },
                
                # 详细分析（可选）
                "detailed_analysis": {
                    "field_analysis": extraction_result.get("field_analysis", {}),
                    "validation_reports": validation_result.get("validation_reports", {}),
                    "cross_validation": validation_result.get("cross_validation", {}),
                },
                
                # 时间戳
                "completed_at": datetime.now().isoformat(),
                "extraction_time": extraction_result.get("extraction_time"),
                "validation_time": validation_result.get("processing_info", {}).get("validation_time")
            }
            
            return final_result
            
        except Exception as e:
            log.error(f"生成最终结果失败: {e}")
            return {"error": str(e)}
    
    async def batch_process_projects(
        self,
        projects: List[Dict[str, Any]],
        max_concurrent: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """批量处理多个项目"""
        try:
            await self.initialize()
            
            max_concurrent = max_concurrent or self.max_concurrent_extractions
            
            log.info(f"开始批量处理: {len(projects)}个项目, 最大并发: {max_concurrent}")
            
            # 创建处理任务
            tasks = []
            for project in projects:
                task = self.process_intelligent_assembly(
                    project_name=project["project_name"],
                    action=project["action"],
                    urls=project["urls"],
                    target_fields=project.get("target_fields"),
                    force_refresh=project.get("force_refresh", False)
                )
                tasks.append(task)
            
            # 限制并发数量
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_with_semaphore(task):
                async with semaphore:
                    return await task
            
            # 执行批量处理
            results = await asyncio.gather(
                *[process_with_semaphore(task) for task in tasks],
                return_exceptions=True
            )
            
            # 处理结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append({
                        "project_name": projects[i]["project_name"],
                        "error": str(result)
                    })
                else:
                    processed_results.append(result)
            
            log.info(f"批量处理完成: {len(processed_results)}个结果")
            return processed_results
            
        except Exception as e:
            log.error(f"批量处理失败: {e}")
            return [{"error": str(e)}]
    
    async def get_processing_capabilities(self) -> Dict[str, Any]:
        """获取处理能力信息"""
        try:
            # 获取各组件状态
            indexer_stats = await self.indexer.get_indexer_stats()
            extractor_stats = await self.extractor.get_extractor_stats()
            
            return {
                "processor_info": {
                    "type": "phase2_intelligent_processor",
                    "version": "1.0",
                    "initialized": self._initialized,
                    "parallel_processing": self.enable_parallel_processing,
                    "max_concurrent": self.max_concurrent_extractions,
                    "result_caching": self.enable_result_caching
                },
                "components": {
                    "indexer": indexer_stats,
                    "extractor": extractor_stats,
                    "validator": {
                        "type": "result_fusion_validator",
                        "min_confidence_threshold": self.validator.min_confidence_threshold,
                        "cross_validation_enabled": self.validator.enable_cross_validation
                    }
                },
                "supported_actions": list(self.extractor.field_extraction_config.keys()),
                "processing_stages": [
                    "document_indexing",
                    "field_extraction", 
                    "result_validation",
                    "final_assembly"
                ]
            }
            
        except Exception as e:
            log.error(f"获取处理能力信息失败: {e}")
            return {"error": str(e)}
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            health_status = {
                "processor_status": "healthy" if self._initialized else "not_initialized",
                "components": {},
                "timestamp": datetime.now().isoformat()
            }
            
            # 检查各组件健康状态
            try:
                indexer_stats = await self.indexer.get_indexer_stats()
                health_status["components"]["indexer"] = {
                    "status": "healthy" if indexer_stats.get("indexer_info", {}).get("initialized") else "unhealthy",
                    "details": indexer_stats
                }
            except Exception as e:
                health_status["components"]["indexer"] = {"status": "error", "error": str(e)}
            
            try:
                extractor_stats = await self.extractor.get_extractor_stats()
                health_status["components"]["extractor"] = {
                    "status": "healthy" if extractor_stats.get("extractor_info", {}).get("initialized") else "unhealthy",
                    "details": extractor_stats
                }
            except Exception as e:
                health_status["components"]["extractor"] = {"status": "error", "error": str(e)}
            
            # 判断整体健康状态
            component_statuses = [comp.get("status") for comp in health_status["components"].values()]
            if all(status == "healthy" for status in component_statuses):
                health_status["overall_status"] = "healthy"
            elif any(status == "error" for status in component_statuses):
                health_status["overall_status"] = "error"
            else:
                health_status["overall_status"] = "degraded"
            
            return health_status
            
        except Exception as e:
            log.error(f"健康检查失败: {e}")
            return {
                "overall_status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
