import sqlite3
from pathlib import Path
db_path = str(Path(__file__).resolve().parent.parent)+"/data/db/cdb.db"  
import json
import re
import hashlib
from datetime import datetime
from utils.valid import parse_date, parse_number
def extract_date(text):
    """从文本中提取日期并转换为YYYY-MM-DD格式
    
    Args:
        text: 输入的文本
        
    Returns:
        str: YYYY-MM-DD格式的日期字符串，如果未找到日期则返回"0000-01-01"
    """
    try:
        print(f"[extract_date] Processing text: {text}")
        
        # 如果已经是YYYY-MM-DD格式，直接返回
        if re.match(r'\d{4}-\d{2}-\d{2}$', str(text)):
            return str(text)
        
        # 定义正则表达式来匹配不同格式的日期
        patterns = [
            # 标准格式：2023-12-15 或 2023/12/15
            r'(\d{4})[\s年./-]*(\d{1,2})[\s月./-]*(\d{1,2})[\s日]?',
            # 年月格式：2023-12 或 2023年12月
            r'(\d{4})[\s年./-]*(\d{1,2})[\s月]?',
            # 纯年份：2023 或 2023年
            r'(\d{4})[\s年]?'
        ]

        # 依次尝试每个模式
        for i, pattern in enumerate(patterns):
            match = re.search(pattern, str(text))
            if match:
                print(f"[extract_date] Matched pattern {i+1}: {pattern}")
                year = match.group(1)
                month = match.group(2) if len(match.groups()) >= 2 else "01"
                day = match.group(3) if len(match.groups()) >= 3 else "01"
                
                # 验证年月日的有效性
                year_num = int(year)
                month_num = int(month)
                day_num = int(day)
                
                if 1900 <= year_num <= 2100 and 1 <= month_num <= 12 and 1 <= day_num <= 31:
                    # 使用 zfill 而不是格式化代码
                    result = f"{year}-{str(month_num).zfill(2)}-{str(day_num).zfill(2)}"
                    print(f"[extract_date] Formatted date: {result}")
                    return result

        print(f"[extract_date] No date pattern matched, returning default date")
        return "1900-01-01"  # 如果没有找到日期，返回有效的默认日期
        
    except Exception as e:
        print(f"[extract_date] Error processing text '{text}': {str(e)}")
        import traceback
        print(f"[extract_date] Traceback:\n{traceback.format_exc()}")
        return "1900-01-01"  # 异常时返回有效的默认日期


def extract_amount(text):
    # 如果输入是数字（整数或浮点数），直接返回其整数值
    if isinstance(text, (int, float)):
        return int(text)
    
    # 如果输入是字符串形式的数字，尝试直接转换
    if isinstance(text, str):
        try:
            return int(float(text))
        except ValueError:
            pass  # 如果转换失败，继续使用正则表达式方法

    # 定义正则表达式来匹配中文和英文语境中的金额
    pattern = r'(\d{1,3}(?:,\d{3})*|\d+)(\.\d+)?\s*(万|million|thousand|billion|千|百)?'
    
    # 查找第一个匹配的金额
    match = re.search(pattern, text)

    if match:
        # 将匹配的数字转化为浮点数并去掉千位分隔符
        number = float(match.group(1).replace(',', '') + (match.group(2) if match.group(2) else ''))
        
        # 处理单位
        unit = match.group(3)
        if unit in ['千', 'thousand']:
            number *= 1000  # 千元 -> 元
        elif unit in ['百']:
            number *= 100  # 百元 -> 元
        elif unit in ['万']:
            number *= 10000  # 万元 -> 元
        elif unit in ['million']:
            number *= 1000000  # million -> 元
        elif unit in ['billion']:
            number *= 1000000000  # billion -> 元
        
        # 返回第一个金额的整数值
        return int(number)
    else:
        return 0  # 如果没有找到金额，返回 0
    

def preprocess_date(value):
    """Preprocess date fields to format YYYY-MM-DD.
    
    Args:
        value: 输入的日期值，可以是 datetime 对象或字符串
        
    Returns:
        str: YYYY-MM-DD 格式的日期字符串
    """
    try:
        print(f"[preprocess_date] Processing date value: {value} (type: {type(value)})")
        
        if isinstance(value, datetime):
            result = value.strftime('%Y-%m-%d')
            print(f"[preprocess_date] Processed datetime object to: {result}")
            return result
            
        elif isinstance(value, str):
            try:
                # 如果已经是 YYYY-MM-DD 格式，直接返回
                if re.match(r'\d{4}-\d{2}-\d{2}$', value):
                    print(f"[preprocess_date] Value already in YYYY-MM-DD format: {value}")
                    return value
                    
                # 否则尝试提取日期
                result = extract_date(value)
                print(f"[preprocess_date] Extracted date from string: {result}")
                return result
                
            except ValueError as e:
                print(f"[preprocess_date] ValueError processing string '{value}': {e}")
                return value
        else:
            result = str(value)
            print(f"[preprocess_date] Converted other type to string: {result}")
            return result
            
    except Exception as e:
        print(f"[preprocess_date] Unexpected error processing '{value}': {str(e)}")
        import traceback
        print(f"[preprocess_date] Traceback:\n{traceback.format_exc()}")
        return value

def preprocess_general(value):
    return ', '.join(value) if isinstance(value, list) else str(value)


def preprocess_field(field_name, value):
    """Preprocess field value based on its name.
    
    Args:
        field_name: 字段名
        value: 字段值
        
    Returns:
        处理后的值
    """
    try:
        print(f"[preprocess_field] Processing {field_name}: {value} (type: {type(value)})")
        
        if field_name in ['start_date', 'end_date', 'date']:
            if isinstance(value, datetime):
                result = value.strftime('%Y-%m-%d')
            else:
                # 如果已经是YYYY-MM-DD格式，直接返回
                if isinstance(value, str) and re.match(r'\d{4}-\d{2}-\d{2}$', value):
                    result = value
                else:
                    result = extract_date(str(value))
            print(f"[preprocess_field] Date field result: {result}")
            return result
            
        elif field_name == 'total_investment':
            if isinstance(value, (int, float)):
                return int(value)
            return int(parse_number(value))
            
        else:
            return preprocess_general(value)
            
    except Exception as e:
        print(f"[preprocess_field] Error processing {field_name}: {value}")
        print(f"[preprocess_field] Error details: {str(e)}")
        import traceback
        print(f"[preprocess_field] Traceback:\n{traceback.format_exc()}")
        return value


def hash_name_to_id(name):
    """Generate a unique integer ID from the name using SHA-256 hash."""
    hash_object = hashlib.sha256(name.encode())
    hex_dig = hash_object.hexdigest()
    # Convert the hexadecimal digest to an integer
    # We can use a smaller part of the hash for the ID if needed
    # Here we take the first 8 characters and convert them to an integer
    id_value = int(hex_dig[:8], 16)
    return id_value

class Database:
    def __init__(self, db_name=db_path):
        self.conn = sqlite3.connect(db_name)
        self.cursor = self.conn.cursor()
        self.create_tables()
        # 定义列名和YAML字段名的映射关系
        self.COLUMN_MAPPING = {
            'project_extract': {
                'name': '项目名称',
                'start_date': '开始日期',
                'end_date': '截止日期',
                'total_investment': '总投资',
                'responsible_unit': '项目承建单位',
                'leader': '负责人',
                'research_points': '主要研究点',
                'innovation': '创新点',
                'main_deliverables': '主要交付成果',
                'patent': "专利"
            },
            'conference_extract': {
                'name': '文书名称',
                'date': '文书时间',
                'type': '文书类型',
                'organizer': '文书发起组织',
                'participants': '文书参与者',
                'summary': '文书摘要'
            }
        }
    
    def close(self):
        """关闭所有连接"""
        if hasattr(self, 'conn'):
            self.conn.close()
            
    def create_tables(self):
        query_project = """
          CREATE TABLE IF NOT EXISTS project_extract (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,             -- 项目名称
                start_date DATE,                -- 开始日期
                end_date DATE,                  -- 结束日期
                total_investment INTEGER,       -- 总投资
                responsible_unit TEXT,          -- 承担单位
                leader TEXT,                    -- 负责人
                research_points TEXT,           -- 主要研究点
                innovation TEXT,                -- 创新点
                main_deliverables TEXT,         -- 主要交付成果
                patent TEXT                     -- 专利
            );
        """
        
        query_conference = """
          CREATE TABLE IF NOT EXISTS conference_extract (
                id INTEGER PRIMARY KEY,          -- 文书的ID
                name TEXT NOT NULL,              -- 文书名称
                date TEXT,                       -- 文书的发起时间
                type TEXT,                       -- 文书的类型
                organizer TEXT,                  -- 文书的发起组织
                participants TEXT,               -- 文书的接收组织和个人  
                summary TEXT                     -- 文书的摘要
            );
        """

        cursor = self.conn.cursor()
        cursor.execute(query_project)
        cursor.execute(query_conference)
        self.conn.commit()


    def insert_project_extract(self, **kwargs):
        query = """
            INSERT INTO project_extract (id, name, start_date, end_date, total_investment, responsible_unit, leader,
                                        research_points, innovation, main_deliverables, patent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(id) DO UPDATE SET
                name = EXCLUDED.name,
                start_date = EXCLUDED.start_date,
                end_date = EXCLUDED.end_date,
                total_investment = EXCLUDED.total_investment,
                responsible_unit = EXCLUDED.responsible_unit,
                leader = EXCLUDED.leader,
                research_points = EXCLUDED.research_points,
                innovation = EXCLUDED.innovation,
                main_deliverables = EXCLUDED.main_deliverables,
                patent = EXCLUDED.patent;
        """
        
        # Generate id from name
        kwargs['id'] = hash_name_to_id(kwargs['name'])
        
        # Preprocess all values
        processed_kwargs = {key: preprocess_field(key, value) for key, value in kwargs.items()}
        
        cursor = self.conn.cursor()
        cursor.execute(query, (
            processed_kwargs['id'],
            processed_kwargs['name'],
            processed_kwargs['start_date'],
            processed_kwargs['end_date'],
            processed_kwargs['total_investment'],
            processed_kwargs['responsible_unit'],
            processed_kwargs['leader'],
            processed_kwargs.get('research_points', None),
            processed_kwargs.get('innovation', None),
            processed_kwargs.get('main_deliverables', None),
            processed_kwargs.get('patent', None),
        ))
        
        self.conn.commit()


    def insert_conference_extract(self, **kwargs):
        query = """
            INSERT INTO conference_extract (id, name, date, type,organizer,participants, summary)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                date = EXCLUDED.date,
                type = EXCLUDED.type,
                organizer = EXCLUDED.organizer,
                participants = EXCLUDED.participants,
                summary = EXCLUDED.summary;
        """
        
        # Generate id from name
        kwargs['id'] = hash_name_to_id(kwargs['name'])
        
        # Preprocess all values
        processed_kwargs = {key: preprocess_field(key, value) for key, value in kwargs.items()}
        
        cursor = self.conn.cursor()
        cursor.execute(query, (
            processed_kwargs['id'],
            processed_kwargs['name'],
            processed_kwargs['date'],
            processed_kwargs['type'],
            processed_kwargs['organizer'],
            processed_kwargs['participants'],
            processed_kwargs.get('summary', None),
        ))
        
        self.conn.commit()

    def get_project_by_name(self, project_name):
        query = "SELECT * FROM project_extract WHERE name=?;"
        cursor = self.conn.cursor()
        result = cursor.execute(query, (project_name,)).fetchone()
        return result

    def get_conference_by_name(self, conference_name):
        query = "SELECT * FROM conference_extract WHERE name=?;"
        cursor = self.conn.cursor()
        result = cursor.execute(query, (conference_name,)).fetchone()
        return result



    def query_table(self, table_name, **kwargs):
        """
        根据指定的表格名和关键字参数查询数据。

        参数:
            table_name (str): 要查询的数据表名称，可以是 'project_extract' 或 'conference_extract'
            kwargs: 用于构建WHERE子句的关键字参数，支持 '=' 和 'LIKE' 查询
        
        返回:
            list: 查询结果,每个元素都是一个字典,键是YAML中的字段名,值是对应的查询结果
        """
        try:
            # 构建基础查询语句
            query = f'SELECT * FROM {table_name}'

            where_clauses = []
            params = []

            # 根据提供的关键字参数构建查询条件
            for key, value in kwargs.items():
                if value:  # 确保value不是None或空值
                    if 'LIKE' in str(value):
                        where_clauses.append(f"{key} LIKE ?")
                        params.append('%' + value.replace('LIKE ', '') + '%')
                    else:
                        where_clauses.append(f"{key} = ?")
                        params.append(value)

            # 如果有WHERE子句，则添加到查询语句中
            if where_clauses:
                query += ' WHERE ' + ' AND '.join(where_clauses)

            self.cursor.execute(query, params)
            rows = self.cursor.fetchall()
            result = []
            for row in rows:
                row_dict = {}
                for i, column in enumerate(self.cursor.description):
                    column_name = column[0]
                    if table_name in self.COLUMN_MAPPING and column_name in self.COLUMN_MAPPING[table_name]:
                        row_dict[self.COLUMN_MAPPING[table_name][column_name]] = row[i]
                    else:
                        row_dict[column_name] = row[i]
                result.append(row_dict)
            return json.dumps(result, ensure_ascii=False)
        except sqlite3.Error as e:
            print("An error occurred:", e)

    def execute_sql(self, query):
        cursor = self.conn.cursor()
        rows = cursor.execute(query, ()).fetchall()
        column_names = [description[0] for description in cursor.description]
        mapped_column_names = [self.COLUMN_MAPPING.get("project_extract", {}).get(name, name) for name in column_names]
        formatted_results = []
        for row in rows:
            formatted_row = ', '.join(f'{mapped_name}: {row[i]}' for i, mapped_name in enumerate(mapped_column_names))
            formatted_results.append(formatted_row)
        return "\n".join(formatted_results)
    

    def execute(self, query):
        cursor = self.conn.cursor()
        rows = cursor.execute(query).fetchall()
        columns = [column[0] for column in cursor.description]
        record_dicts = [dict(zip(columns, row)) for row in rows]
        
        return record_dicts  # Return the list of dictionaries

    def query_to_sql(self, base_query, params):
        # 将参数填充到 SQL 语句中
        if params:
            param_list = [f"'{param}'" if isinstance(param, str) else str(param) for param in params]
            complete_query = base_query % tuple(param_list)
        else:
            complete_query = base_query
        return complete_query

    def _format_date(self, date_str: str) -> str:
        """格式化日期为YYYY-MM-DD格式
        
        Args:
            date_str: 输入的日期字符串，支持多种格式
            
        Returns:
            str: YYYY-MM-DD格式的日期字符串，解析失败返回原字符串
        """
        try:
            # 清理输入字符串
            date_str = str(date_str).strip()
            
            # 如果已经是YYYY-MM-DD格式，直接返回
            if re.match(r'\d{4}-\d{2}-\d{2}$', date_str):
                return date_str
            
            # 匹配多种日期格式
            patterns = [
                # 标准格式：2023-12-15 或 2023/12/15 或 2023.12.15
                r"(\d{4})[-./\s]*(\d{1,2})[-./\s]*(\d{1,2})",
                # 中文格式：2023年12月15日
                r"(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})?[日]?",
                # 年月格式：2023-12 或 2023年12月
                r"(\d{4})[-年./\s]*(\d{1,2})[-月]?",
                # 纯年份格式：2023 或 2023年
                r"(\d{4})[年]?"
            ]
            
            for pattern in patterns:
                match = re.search(pattern, date_str)
                if match:
                    year = match.group(1)
                    month = match.group(2) if len(match.groups()) >= 2 else "01"
                    day = match.group(3) if len(match.groups()) >= 3 and match.group(3) else "01"
                    
                    # 验证年月日的有效性
                    year_num = int(year)
                    month_num = int(month)
                    day_num = int(day)
                    
                    if 1900 <= year_num <= 2100 and 1 <= month_num <= 12 and 1 <= day_num <= 31:
                        # 使用 zfill 而不是格式化代码
                        return f"{year}-{str(month_num).zfill(2)}-{str(day_num).zfill(2)}"
                
            return date_str
            
        except Exception as e:
            print(f"Error formatting date '{date_str}': {str(e)}")
            return date_str



def main():
    
    # 创建数据库操作实例
    cdb = Database()
    cdb.create_tables()
    data = {
        'name': 'Example Project',
        'start_date': '2024-08-26',
        'end_date': '2025-08-26',
        'total_investment': 1000000,  # Integer value
        'responsible_unit': 'Research Unit',
        'leader': 'John Doe',
        'research_points': ['Point 1', 'Point 2'],  # List of strings
        'innovation': 'Innovation Description',
        'main_deliverables': ['Deliverable 1', 'Deliverable 2'],  # List of strings
        'patent': 'Patent Number 12345'
    }
    # 插入示例数据到project_extract表中
    cdb.insert_project_extract(**data)

    # 插入示例数据到conference_extract表中
    cdb.insert_conference_extract(
        name='全球人工智能峰会',
        date='2023-11-15',
        type='国际学术会议',
        summary='本次峰会聚焦于AI前沿技术、行业应用和伦理问题的探讨。'
    )

    # 查询并打印数据来验证插入是否成功
    project_info = cdb.get_project_by_name('智能网络系统研究')
    print("项目信息:", project_info)

    conference_info = cdb.get_conference_by_name('全球人工智能峰会')
    print("会议信息:", conference_info)
        
    # 查询项目数据，例如查找负责人是 '张三' 的所有项目
    projects_result = cdb.query_table('project_extract', leader='张三')
    print("项目信息:", projects_result)
    # 查询会议数据，例如查找类型是 '学术研讨会' 的所有会议
    conferences_result = cdb.query_table('conference_extract', type='国际学术会议')
    print("会议信息:", conferences_result)
    
    
    results = cdb.query_table('project_extract', name='LIKE Example')
    print(results)
    cdb.close()


# 运行函数
if __name__ == "__main__":
    main()
