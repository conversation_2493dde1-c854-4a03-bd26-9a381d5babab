#!/usr/bin/env python3
"""
兼容现有架构的检索器
基于现有的ESKnowledge进行多维度检索
"""

from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from utils.log import log
from utils.config import config
from knowledge_control import KnowledgeControl
from utils.llm import LLM


class CompatibleRetriever:
    """兼容现有架构的检索器"""
    
    def __init__(self):
        # 从配置中获取ES和数据库配置
        es_config = {
            "host": config.get("elasticsearch.host", "localhost"),
            "port": config.get("elasticsearch.port", 9200),
            "username": config.get("elasticsearch.username", ""),
            "password": config.get("elasticsearch.password", ""),
            "index_name": config.get("elasticsearch.index_name", "knowledge_base")
        }

        # 数据库配置（使用MySQL配置）
        db_config = {
            "host": config.get("mysql.host", "***********"),
            "port": config.get("mysql.port", 3306),
            "user": config.get("mysql.user", "root"),
            "password": config.get("mysql.password", "startfrom2023"),
            "database": config.get("mysql.database", "hngpt")
        }

        self.knowledge_control = KnowledgeControl(es_config, db_config)
        self.llm = LLM()
        self._initialized = False
    
    async def initialize(self):
        """初始化检索器"""
        if not self._initialized:
            await self.knowledge_control.init()
            self._initialized = True
            log.info("兼容检索器初始化完成")
    
    async def keyword_search(
        self,
        project_name: Optional[str] = None,
        action: Optional[str] = None,
        keywords: Union[str, List[str]] = "",
        size: int = 10,
        min_score: float = 0.1
    ) -> List[Dict[str, Any]]:
        """关键词检索 - 兼容现有架构"""
        try:
            await self.initialize()
            
            # 处理关键词
            if isinstance(keywords, list):
                keyword_str = " ".join(keywords)
            else:
                keyword_str = keywords
            
            # 构建查询
            must_clauses = []
            
            # 添加关键词查询
            if keyword_str:
                must_clauses.append({
                    "multi_match": {
                        "query": keyword_str,
                        "fields": ["content^2", "source"],
                        "type": "best_fields",
                        "fuzziness": "AUTO"
                    }
                })
            
            # 添加过滤条件
            filter_clauses = []
            if project_name:
                filter_clauses.append({"term": {"project_name": project_name}})
            if action:
                filter_clauses.append({"term": {"action": action}})
            
            # 构建完整查询
            query = {"bool": {"must": must_clauses}}
            if filter_clauses:
                query["bool"]["filter"] = filter_clauses
            
            # 执行搜索
            es_knowledge = self.knowledge_control.es_knowledge
            result = await es_knowledge.search(
                query=query,
                size=size,
                source=["content", "source", "page_num", "doc_id", "project_name", "action", "year"]
            )
            
            # 处理结果
            return self._process_search_results(result, min_score)
            
        except Exception as e:
            log.error(f"关键词检索失败: {e}")
            return []
    
    async def semantic_search(
        self,
        query_text: str,
        project_name: Optional[str] = None,
        action: Optional[str] = None,
        size: int = 10,
        min_score: float = 0.7
    ) -> List[Dict[str, Any]]:
        """语义向量检索 - 兼容现有架构"""
        try:
            await self.initialize()
            
            # 生成查询向量
            query_vector = await self.llm.get_embedding(query_text)
            
            # 构建向量查询
            query = {
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                        "params": {"query_vector": query_vector}
                    }
                }
            }
            
            # 添加过滤条件
            filter_clauses = []
            if project_name:
                filter_clauses.append({"term": {"project_name": project_name}})
            if action:
                filter_clauses.append({"term": {"action": action}})
            
            if filter_clauses:
                query = {
                    "bool": {
                        "must": [query],
                        "filter": filter_clauses
                    }
                }
            
            # 执行搜索
            es_knowledge = self.knowledge_control.es_knowledge
            result = await es_knowledge.search(
                query=query,
                size=size,
                source=["content", "source", "page_num", "doc_id", "project_name", "action", "year"]
            )
            
            # 处理结果
            return self._process_search_results(result, min_score)
            
        except Exception as e:
            log.error(f"语义检索失败: {e}")
            return []
    
    async def hybrid_search(
        self,
        keywords: Union[str, List[str]],
        semantic_query: str,
        project_name: Optional[str] = None,
        action: Optional[str] = None,
        keyword_weight: float = 0.4,
        semantic_weight: float = 0.6,
        size: int = 10,
        min_score: float = 0.3
    ) -> List[Dict[str, Any]]:
        """混合检索（关键词 + 语义）- 兼容现有架构"""
        try:
            await self.initialize()
            
            # 处理关键词
            if isinstance(keywords, list):
                keyword_str = " ".join(keywords)
            else:
                keyword_str = keywords
            
            # 生成查询向量
            query_vector = await self.llm.get_embedding(semantic_query)
            
            # 构建混合查询
            should_clauses = []
            
            # 关键词查询
            if keyword_str:
                should_clauses.append({
                    "multi_match": {
                        "query": keyword_str,
                        "fields": ["content^2", "source"],
                        "type": "best_fields",
                        "boost": keyword_weight
                    }
                })
            
            # 语义向量查询
            should_clauses.append({
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": f"cosineSimilarity(params.query_vector, 'embedding') * {semantic_weight}",
                        "params": {"query_vector": query_vector}
                    }
                }
            })
            
            # 构建完整查询
            query = {
                "bool": {
                    "should": should_clauses,
                    "minimum_should_match": 1
                }
            }
            
            # 添加过滤条件
            filter_clauses = []
            if project_name:
                filter_clauses.append({"term": {"project_name": project_name}})
            if action:
                filter_clauses.append({"term": {"action": action}})
            
            if filter_clauses:
                query["bool"]["filter"] = filter_clauses
            
            # 执行搜索
            es_knowledge = self.knowledge_control.es_knowledge
            result = await es_knowledge.search(
                query=query,
                size=size,
                source=["content", "source", "page_num", "doc_id", "project_name", "action", "year"]
            )
            
            # 处理结果
            return self._process_search_results(result, min_score)
            
        except Exception as e:
            log.error(f"混合检索失败: {e}")
            return []
    
    async def multi_field_search(
        self,
        field_queries: Dict[str, Dict[str, Any]],
        project_name: Optional[str] = None,
        action: Optional[str] = None,
        size: int = 20
    ) -> Dict[str, List[Dict[str, Any]]]:
        """多字段检索 - 兼容现有架构"""
        try:
            await self.initialize()
            
            results = {}
            
            for field_name, field_config in field_queries.items():
                try:
                    # 获取字段检索配置
                    keywords = field_config.get("keywords", [])
                    semantic_query = field_config.get("semantic_query", "")
                    field_size = field_config.get("size", size // len(field_queries))
                    
                    # 执行混合检索
                    field_results = await self.hybrid_search(
                        keywords=keywords,
                        semantic_query=semantic_query,
                        project_name=project_name,
                        action=action,
                        size=field_size
                    )
                    
                    results[field_name] = field_results
                    
                except Exception as e:
                    log.error(f"字段检索失败 {field_name}: {e}")
                    results[field_name] = []
            
            log.debug(f"多字段检索完成: {len(field_queries)}个字段")
            return results
            
        except Exception as e:
            log.error(f"多字段检索失败: {e}")
            return {}
    
    def _process_search_results(
        self,
        es_results: Dict[str, Any],
        min_score: float = 0.0
    ) -> List[Dict[str, Any]]:
        """处理ES搜索结果 - 兼容现有架构"""
        try:
            hits = es_results.get("hits", {}).get("hits", [])
            processed_results = []
            
            for hit in hits:
                score = hit.get("_score", 0)
                
                # 过滤低分结果
                if score < min_score:
                    continue
                
                source = hit.get("_source", {})
                
                result = {
                    "score": round(score, 4),
                    "content": source.get("content", ""),
                    "source": source.get("source", ""),
                    "page_num": source.get("page_num", 0),
                    "doc_id": source.get("doc_id", ""),
                    "project_name": source.get("project_name", ""),
                    "action": source.get("action", ""),
                    "year": source.get("year", 0)
                }
                
                processed_results.append(result)
            
            return processed_results
            
        except Exception as e:
            log.error(f"处理搜索结果失败: {e}")
            return []
    
    async def get_retriever_stats(self) -> Dict[str, Any]:
        """获取检索器统计信息"""
        try:
            return {
                "retriever_info": {
                    "type": "compatible_retriever",
                    "initialized": self._initialized,
                    "supported_search_types": ["keyword", "semantic", "hybrid", "multi_field"]
                },
                "knowledge_control_status": {
                    "es_available": hasattr(self.knowledge_control, 'es_knowledge'),
                    "sql_available": hasattr(self.knowledge_control, 'sql_knowledge')
                }
            }
            
        except Exception as e:
            log.error(f"获取检索器统计失败: {e}")
            return {"error": str(e)}
    
    async def test_search_capabilities(
        self,
        project_name: str = "测试项目",
        action: str = "项目档案"
    ) -> Dict[str, Any]:
        """测试检索能力"""
        try:
            test_results = {}
            
            # 测试关键词检索
            try:
                keyword_results = await self.keyword_search(
                    project_name=project_name,
                    action=action,
                    keywords="项目 投资",
                    size=3
                )
                test_results["keyword_search"] = {
                    "status": "success",
                    "result_count": len(keyword_results)
                }
            except Exception as e:
                test_results["keyword_search"] = {
                    "status": "failed",
                    "error": str(e)
                }
            
            # 测试语义检索
            try:
                semantic_results = await self.semantic_search(
                    query_text="项目的总投资金额",
                    project_name=project_name,
                    action=action,
                    size=3
                )
                test_results["semantic_search"] = {
                    "status": "success",
                    "result_count": len(semantic_results)
                }
            except Exception as e:
                test_results["semantic_search"] = {
                    "status": "failed",
                    "error": str(e)
                }
            
            # 测试混合检索
            try:
                hybrid_results = await self.hybrid_search(
                    keywords="投资 金额",
                    semantic_query="项目的资金投入",
                    project_name=project_name,
                    action=action,
                    size=3
                )
                test_results["hybrid_search"] = {
                    "status": "success",
                    "result_count": len(hybrid_results)
                }
            except Exception as e:
                test_results["hybrid_search"] = {
                    "status": "failed",
                    "error": str(e)
                }
            
            return test_results
            
        except Exception as e:
            log.error(f"测试检索能力失败: {e}")
            return {"error": str(e)}
