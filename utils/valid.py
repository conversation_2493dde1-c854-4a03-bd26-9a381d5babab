import re
from datetime import datetime

import re
from datetime import datetime

def parse_date(value: str) -> str:
    """Parse date string to YYYY-MM-DD format.
    
    Args:
        value: 输入的日期字符串
        
    Returns:
        str: YYYY-MM-DD格式的日期字符串
    """
    try:
        if not value or value == "未提及":
            return "1900-01-01"
            
        # 如果已经是YYYY-MM-DD格式，直接返回
        if re.match(r'\d{4}-\d{2}-\d{2}$', value):
            return value
            
        # 使用extract_date处理其他格式
        from utils.cdb import extract_date
        result = extract_date(value)
        return result
        
    except Exception as e:
        print(f"[parse_date] Error processing '{value}': {str(e)}")
        import traceback
        print(f"[parse_date] Traceback:\n{traceback.format_exc()}")
        return "1900-01-01"  # 异常时返回有效的默认日期

# 示例测试
# print(parse_date("9月15日"))  # 应该返回当前年份的9月15日
# print(parse_date("15日"))     # 应该返回当前年份和月份的15日
# print(parse_date("2023年9月")) # 应该返回2023年9月1日
# print(parse_date("2023年"))   # 应该返回2023年1月1日


def validate_date(field_name: str, old_value: str, new_value: str) -> str:
    try:
        old_date = parse_date(old_value)
        new_date = parse_date(new_value)

        if not new_date:
            return old_value if old_date else ""
        if not old_date:
            return new_date.strftime('%Y-%m-%d')

        if field_name in ['start_date', 'date'] and new_date > old_date:
            print(f"Warning: New {field_name} is later than old {field_name}. Old: {old_value}, New: {new_value}. Keeping the earlier date.")
            return old_date.strftime('%Y-%m-%d')
        elif field_name == 'end_date' and new_date < old_date:
            print(f"Warning: New end date is earlier than old end date. Old: {old_value}, New: {new_value}. Keeping the later date.")
            return old_date.strftime('%Y-%m-%d')
        
        # 总是返回标准化的日期格式
        return new_date.strftime('%Y-%m-%d')
    except Exception as e:
        print(f"Error processing dates: {e}. Keeping the valid value.")
        return new_value if parse_date(new_value) else (old_value if parse_date(old_value) else "")

# 测试函数
def test_validate_date():
    test_cases = [
        ("start_date","2017年4月","2017-04-01", "2017-04-01"),
        ("start_date", "2017-01", "2017-01-01", "2017-01-01"),
        ("start_date", "2022-01-01", "2023-01-01", "2022-01-01"),  # 保留较早的开始日期
        ("end_date", "2023-12-31", "2022-12-31", "2023-12-31"),    # 保留较晚的结束日期
        ("date", "2022年1月1日", "2022-01-01", "2022-01-01"),      # 标准化日期格式
        ("start_date", "2022年", "2022-01-01", "2022-01-01"),      # 年份被视为年初
        ("end_date", "2022年12月", "2022-12-31", "2022-12-31"),    # 月份被视为月末
        ("date", "2022/1/1", "2022.1.1", "2022-01-01"),            # 不同的分隔符
        ("date", "20220101", "2022-01-01", "2022-01-01"),          # 无分隔符
        ("date", "二〇二二年一月一日", "2022-01-01", "2022-01-01"),  # 中文数字日期
        ("date", "", "2022-01-01", "2022-01-01"),                  # 旧值为空
        ("date", "2022-01-01", "", "2022-01-01"),                  # 新值为空
        ("date", "无效日期", "2022-01-01", "2022-01-01"),           # 旧值无效
        ("date", "2022-01-01", "无效日期", "2022-01-01"),           # 新值无效
        ("date", "2022 年 1 月 1 日", "2022-01-01", "2022-01-01"),  # 日期中包含空格
        ("date", "无效日期", "无效日期", ""),                         # 两个值都无效
    ]

    for field, old, new, expected in test_cases:
        result = validate_date(field, old, new)
        print(f"Field: {field}, Old: {old}, New: {new}, Result: {result}, Expected: {expected}")
        assert result == expected, f"Test failed for field {field}, old value {old}, and new value {new}"

    print("All date validation tests passed!")

# 运行测试
# test_validate_date()


def parse_number(value: str) -> str:
    """解析金额字符串，保留原始数字和单位
    
    Args:
        value: 输入的金额字符串或数字
        
    Returns:
        str: 提取的金额字符串，格式为"数字+单位"，如"150.8万"
    """
    if not value:
        return "未提及"
    
    try:
        # 如果输入已经是数字类型
        if isinstance(value, (int, float)):
            # 如果是整数或整数形式的浮点数
            if float(value).is_integer():
                return f"{int(value)}万"
            # 如果是浮点数，保留原始精度
            return f"{value}万"
        
        # 转换为字符串并清理空格
        value_str = str(value).strip()
        
        # 提取数字部分
        number_match = re.search(r"([\d,.]+)", value_str)
        if not number_match:
            return "未提及"
            
        # 处理数字部分
        number_str = number_match.group(1).replace(',', '')
        try:
            number = float(number_str)
            # 如果是整数，转为整数形式
            if number.is_integer():
                number_str = str(int(number))
            else:
                number_str = str(number)
        except ValueError:
            return "未提及"
        
        # 提取单位
        unit = "万"  # 默认单位
        if "亿" in value_str:
            unit = "亿"
        elif "千万" in value_str:
            unit = "千万"
        elif "万" in value_str:
            unit = "万"
            
        return f"{number_str}{unit}"
        
    except Exception as e:
        print(f"Error parsing number '{value}': {str(e)}")
        return "未提及"





def validate_amount(old_value: str, new_value: str) -> str:
    

    try:
        old_amount = parse_number(old_value)
        new_amount = parse_number(new_value)

        # 如果新值为0（可能是空字符串或无法解析），保留旧值
        if new_amount == 0 and old_amount != 0:
            return str(old_amount)

        # 如果旧为0（可能是空字符串或无法解析），使用新值
        if old_amount == 0 and new_amount != 0:
            return str(new_amount)

        # 比较两个值，保留较大的值
        return str(max(old_amount, new_amount))
    except Exception as e:
        print(f"Error processing amounts: {e}. Keeping the valid value.")
        return str(new_amount) if new_amount != 0 else (str(old_amount) if old_amount != 0 else "0")




def extract_amount(text):
    # 如果输入是数字（整数或浮点数），直接返回其整数值
    if isinstance(text, (int, float)):
        return int(text)
    
    # 如果输入是字符串形式的数字，尝试直接转换
    if isinstance(text, str):
        try:
            return int(float(text))
        except ValueError:
            pass  # 如果转换失败，继续使用正则表达式方法

    # 定义正则表达式来匹配中文和英文语境中的金额
    pattern = r'(\d{1,3}(?:,\d{3})*|\d+)(\.\d+)?\s*(万|million|thousand|billion|千|百)?'
    
    # 查找第一个匹配的金额
    match = re.search(pattern, text)

    if match:
        # 将匹配的数字转化为浮点数并去掉千位分隔符
        number = float(match.group(1).replace(',', '') + (match.group(2) if match.group(2) else ''))
        
        # 处理单位
        unit = match.group(3)
        if unit in ['千', 'thousand']:
            number *= 1000  # 千元 -> 元
        elif unit in ['百']:
            number *= 100  # 百元 -> 元
        elif unit in ['万']:
            number *= 10000  # 万元 -> 元
        elif unit in ['million']:
            number *= 1000000  # million -> 元
        elif unit in ['billion']:
            number *= 1000000000  # billion -> 元
        
        # 返回第一个金额的整数值
        return int(number)
    else:
        return 0  # 如果没有找到金额，返回 0
    




# 测试函数
def test_validate_amount():
    test_cases = [
        ("252.6万元", "2526000", "2526000"),
        ("1234万5600元", "12345600", "12345600"),
        ("1千万", "1000万", "10000000"),
        ("壹佰万元整", "1,000,000.00", "1000000"),
        ("1亿元", "10000万元", "100000000"),
        ("100万元", "1000000元", "1000000"),
        ("100万元", "90万元", "1000000"),
        ("1,000,000元", "100万", "1000000"),
        ("720万元", "100万美元", "7200000"),
        ("100万元", "", "1000000"),
        ("100万元", "", "1000000"),
        ("100 万 元", "1000000", "1000000"),
        ("柒拾叁万捌仟元", "73.8万元", "738000"),
        ("1234.56万元", "12345600", "12345600"),
        ("1000000","无效金额", "1000000"),
        ("1000000", "无效金额", "1000000"),
        ("无效金额", "无效金额", "0"),
        ("十万", "100000", "100000"),
        ("一亿一千万", "110000000", "110000000"),
    ]

    for old, new, expected in test_cases:
        told = str(parse_number(old))
        tnew = str(parse_number(new))
        print(f"Old: {told}, New: {tnew}, Result: {tnew}, Expected: {expected}")
        assert told == expected, f"Test failed for {old} and {new}"
        # result = validate_amount(old, new)
        # print(f"Old: {old}, New: {new}, Result: {result}, Expected: {expected}")
        # assert result == expected, f"Test failed for {old} and {new}"

    print("All amount validation tests passed!")
# 运行测试
# test_validate_amount()
# test_validate_date()

# print(parse_number("252.6万元"))

# print(parse_date("2017年4月"))