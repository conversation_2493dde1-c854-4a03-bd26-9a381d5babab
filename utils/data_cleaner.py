#!/usr/bin/env python3
"""
数据清理工具
用于过滤LLM返回数据中的思考过程内容
"""

import re
from typing import Any, Dict, List, Union
from utils.log import log


class DataCleaner:
    """数据清理器，用于清理LLM返回的数据"""
    
    def __init__(self):
        # 思考容器的正则表达式模式
        self.thinking_patterns = [
            # 匹配完整的思考容器（改进版，处理嵌套结构）
            r'<div class="ai-thinking-container"[^>]*>(?:[^<]|<(?!/?div))*(?:<div[^>]*>(?:[^<]|<(?!/?div))*</div>)*[^<]*</div>',
            # 匹配可能的变体
            r'<div class=["\']ai-thinking-container["\'][^>]*>(?:[^<]|<(?!/?div))*(?:<div[^>]*>(?:[^<]|<(?!/?div))*</div>)*[^<]*</div>',
            # 匹配思考标签（备用）
            r'<think>.*?</think>',
            r'<thinking>.*?</thinking>',
            # 更简单但更强力的模式（作为后备）
            r'<div[^>]*class="ai-thinking-container"[^>]*>.*?</div>(?:\s*</div>)*',
        ]

        # 编译正则表达式（DOTALL模式匹配换行符）
        self.compiled_patterns = [
            re.compile(pattern, re.DOTALL | re.IGNORECASE)
            for pattern in self.thinking_patterns
        ]
    
    def clean_text(self, text: str) -> str:
        """
        清理文本中的思考内容

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        if not isinstance(text, str):
            return text

        cleaned_text = text

        # 使用更简单的方法：找到思考容器的开始和结束位置
        cleaned_text = self._remove_thinking_containers(cleaned_text)

        # 应用其他清理模式
        for pattern in self.compiled_patterns[2:]:  # 跳过前两个复杂的模式
            cleaned_text = pattern.sub('', cleaned_text)

        # 清理多余的空白字符
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
        cleaned_text = cleaned_text.strip()

        # 记录清理结果
        if cleaned_text != text:
            log.debug(f"数据清理: 原长度={len(text)}, 清理后长度={len(cleaned_text)}")

        return cleaned_text

    def _remove_thinking_containers(self, text: str) -> str:
        """
        移除思考容器，使用简单的字符串查找方法
        """
        result = text

        # 查找所有思考容器的开始位置
        start_markers = [
            '<div class="ai-thinking-container"',
            "<div class='ai-thinking-container'",
        ]

        for marker in start_markers:
            while marker in result:
                start_pos = result.find(marker)
                if start_pos == -1:
                    break

                # 找到对应的结束标签
                # 简单方法：找到下一个独立的</div>，考虑嵌套
                div_count = 0
                pos = start_pos
                end_pos = -1

                while pos < len(result):
                    if result[pos:pos+5] == '<div ':
                        div_count += 1
                    elif result[pos:pos+6] == '</div>':
                        div_count -= 1
                        if div_count == 0:
                            end_pos = pos + 6
                            break
                    pos += 1

                if end_pos != -1:
                    # 移除整个思考容器
                    result = result[:start_pos] + result[end_pos:]
                else:
                    # 如果找不到结束标签，移除从开始到文本末尾
                    result = result[:start_pos]
                    break

        return result
    
    def clean_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理字典中的思考内容
        
        Args:
            data: 原始字典数据
            
        Returns:
            清理后的字典数据
        """
        if not isinstance(data, dict):
            return data
        
        cleaned_data = {}
        
        for key, value in data.items():
            if isinstance(value, str):
                cleaned_data[key] = self.clean_text(value)
            elif isinstance(value, dict):
                cleaned_data[key] = self.clean_dict(value)
            elif isinstance(value, list):
                cleaned_data[key] = self.clean_list(value)
            else:
                cleaned_data[key] = value
        
        return cleaned_data
    
    def clean_list(self, data: List[Any]) -> List[Any]:
        """
        清理列表中的思考内容
        
        Args:
            data: 原始列表数据
            
        Returns:
            清理后的列表数据
        """
        if not isinstance(data, list):
            return data
        
        cleaned_data = []
        
        for item in data:
            if isinstance(item, str):
                cleaned_data.append(self.clean_text(item))
            elif isinstance(item, dict):
                cleaned_data.append(self.clean_dict(item))
            elif isinstance(item, list):
                cleaned_data.append(self.clean_list(item))
            else:
                cleaned_data.append(item)
        
        return cleaned_data
    
    def clean_any(self, data: Any) -> Any:
        """
        清理任意类型的数据
        
        Args:
            data: 原始数据
            
        Returns:
            清理后的数据
        """
        if isinstance(data, str):
            return self.clean_text(data)
        elif isinstance(data, dict):
            return self.clean_dict(data)
        elif isinstance(data, list):
            return self.clean_list(data)
        else:
            return data
    
    def validate_cleaned_data(self, original: Any, cleaned: Any) -> bool:
        """
        验证清理后的数据是否合理
        
        Args:
            original: 原始数据
            cleaned: 清理后的数据
            
        Returns:
            是否通过验证
        """
        # 基本类型检查
        if type(original) != type(cleaned):
            log.warning(f"数据清理后类型发生变化: {type(original)} -> {type(cleaned)}")
            return False
        
        # 字符串长度检查
        if isinstance(original, str) and isinstance(cleaned, str):
            if len(cleaned) > len(original):
                log.warning("清理后的文本长度大于原文本，可能有问题")
                return False
            
            # 如果清理后变成空字符串，检查原文本是否只包含思考内容
            if not cleaned and original:
                log.info("文本清理后为空，原文本可能只包含思考内容")
        
        return True


# 创建全局数据清理器实例
data_cleaner = DataCleaner()


def clean_response(response: Any) -> Any:
    """
    清理LLM响应中的思考内容

    Args:
        response: LLM原始响应

    Returns:
        清理后的响应
    """
    return data_cleaner.clean_any(response)


# 保持向后兼容性的别名
clean_llm_response = clean_response


def clean_extracted_data(extracted_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    清理提取的数据中的思考内容
    
    Args:
        extracted_data: 原始提取数据
        
    Returns:
        清理后的提取数据
    """
    cleaned = data_cleaner.clean_dict(extracted_data)
    
    # 验证清理结果
    if not data_cleaner.validate_cleaned_data(extracted_data, cleaned):
        log.warning("数据清理验证失败，请检查清理逻辑")
    
    return cleaned


# 测试函数
def test_data_cleaner():
    """测试数据清理功能"""
    test_cases = [
        # 测试用例1：包含思考容器的文本
        {
            "input": '3598262689\t测试项目\t<div class="ai-thinking-container"><div class="ai-thinking-content">思考过程</div></div>未提及',
            "expected": "3598262689\t测试项目\t未提及"
        },
        # 测试用例2：字典数据
        {
            "input": {
                "project_name": "测试项目",
                "result": '<div class="ai-thinking-container">思考内容</div>未提及',
                "status": "completed"
            },
            "expected": {
                "project_name": "测试项目", 
                "result": "未提及",
                "status": "completed"
            }
        }
    ]
    
    for i, case in enumerate(test_cases):
        cleaned = data_cleaner.clean_any(case["input"])
        print(f"测试用例 {i+1}:")
        print(f"  输入: {case['input']}")
        print(f"  输出: {cleaned}")
        print(f"  期望: {case['expected']}")
        print()


if __name__ == "__main__":
    test_data_cleaner()
