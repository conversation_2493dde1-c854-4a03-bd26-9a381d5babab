#!/usr/bin/env python3
"""
中间结果处理器
用于处理和管理汇编过程中的中间结果
"""

import json
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict

from utils.llm import LLM
from utils.log import log


class IntermediateResultsProcessor:
    """中间结果处理器"""
    
    def __init__(self):
        self.llm = LLM()
        self.results_cache = {}
        self.processing_stats = defaultdict(int)
    
    async def process_intermediate_results(
        self,
        results: Dict[str, Any],
        stage: str = "unknown"
    ) -> Dict[str, Any]:
        """处理中间结果"""
        try:
            log.info(f"开始处理中间结果，阶段: {stage}")
            
            # 基本验证
            if not results or not isinstance(results, dict):
                return {"error": "无效的结果数据"}
            
            # 处理不同阶段的结果
            if stage == "extraction":
                return await self._process_extraction_results(results)
            elif stage == "analysis":
                return await self._process_analysis_results(results)
            elif stage == "synthesis":
                return await self._process_synthesis_results(results)
            else:
                return await self._process_generic_results(results)
                
        except Exception as e:
            log.error(f"处理中间结果失败: {e}")
            return {"error": str(e)}
    
    async def _process_extraction_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """处理提取阶段的结果"""
        try:
            processed_results = {
                "stage": "extraction",
                "timestamp": datetime.now().isoformat(),
                "data": results,
                "summary": {
                    "total_items": len(results.get("items", [])),
                    "categories": list(results.get("categories", {}).keys()),
                    "status": "processed"
                }
            }
            
            # 更新统计
            self.processing_stats["extraction_processed"] += 1
            
            return processed_results
            
        except Exception as e:
            log.error(f"处理提取结果失败: {e}")
            return {"error": str(e)}
    
    async def _process_analysis_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """处理分析阶段的结果"""
        try:
            processed_results = {
                "stage": "analysis",
                "timestamp": datetime.now().isoformat(),
                "data": results,
                "summary": {
                    "analysis_type": results.get("type", "unknown"),
                    "confidence": results.get("confidence", 0.0),
                    "key_findings": results.get("findings", []),
                    "status": "processed"
                }
            }
            
            # 更新统计
            self.processing_stats["analysis_processed"] += 1
            
            return processed_results
            
        except Exception as e:
            log.error(f"处理分析结果失败: {e}")
            return {"error": str(e)}
    
    async def _process_synthesis_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """处理综合阶段的结果"""
        try:
            processed_results = {
                "stage": "synthesis",
                "timestamp": datetime.now().isoformat(),
                "data": results,
                "summary": {
                    "synthesis_method": results.get("method", "unknown"),
                    "output_format": results.get("format", "text"),
                    "quality_score": results.get("quality", 0.0),
                    "status": "processed"
                }
            }
            
            # 更新统计
            self.processing_stats["synthesis_processed"] += 1
            
            return processed_results
            
        except Exception as e:
            log.error(f"处理综合结果失败: {e}")
            return {"error": str(e)}
    
    async def _process_generic_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """处理通用结果"""
        try:
            processed_results = {
                "stage": "generic",
                "timestamp": datetime.now().isoformat(),
                "data": results,
                "summary": {
                    "data_type": type(results).__name__,
                    "data_size": len(str(results)),
                    "status": "processed"
                }
            }
            
            # 更新统计
            self.processing_stats["generic_processed"] += 1
            
            return processed_results
            
        except Exception as e:
            log.error(f"处理通用结果失败: {e}")
            return {"error": str(e)}
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            "stats": dict(self.processing_stats),
            "cache_size": len(self.results_cache),
            "timestamp": datetime.now().isoformat()
        }
    
    def clear_cache(self):
        """清理缓存"""
        self.results_cache.clear()
        log.info("中间结果缓存已清理")
    
    async def validate_results(self, results: Dict[str, Any]) -> Tuple[bool, str]:
        """验证结果的有效性"""
        try:
            if not results:
                return False, "结果为空"
            
            if not isinstance(results, dict):
                return False, "结果必须是字典类型"
            
            # 检查必要字段
            required_fields = ["stage", "timestamp", "data"]
            for field in required_fields:
                if field not in results:
                    return False, f"缺少必要字段: {field}"
            
            return True, "验证通过"
            
        except Exception as e:
            return False, f"验证失败: {e}"
    
    async def merge_results(self, results_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合并多个结果"""
        try:
            if not results_list:
                return {"error": "没有结果需要合并"}
            
            merged_result = {
                "stage": "merged",
                "timestamp": datetime.now().isoformat(),
                "source_count": len(results_list),
                "data": {},
                "summary": {
                    "stages": [],
                    "total_items": 0,
                    "status": "merged"
                }
            }
            
            # 合并数据
            for i, result in enumerate(results_list):
                if isinstance(result, dict):
                    stage = result.get("stage", f"unknown_{i}")
                    merged_result["summary"]["stages"].append(stage)
                    merged_result["data"][f"result_{i}"] = result
                    
                    # 统计项目数量
                    if "summary" in result and "total_items" in result["summary"]:
                        merged_result["summary"]["total_items"] += result["summary"]["total_items"]
            
            return merged_result
            
        except Exception as e:
            log.error(f"合并结果失败: {e}")
            return {"error": str(e)}
