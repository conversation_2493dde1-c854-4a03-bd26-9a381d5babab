# import os
# import sys
# root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# sys.path.append(root_dir)
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import json
import asyncio
from utils.es_client import ESClient
from utils.config import config
from utils.data_cleaner import clean_response
from utils.field_types import FieldType, FIELD_TYPES
from utils.field_processors import FIELD_PROCESSORS
from utils.utils import get_filename_from_url, load_json
from elasticsearch import Elasticsearch
import re

class ESKnowledge:
    """ES知识仓库：负责知识的向量化存储和语义检索"""
    
    # 预定义的文档类型
    VALID_TYPES = [
        "决策会议",  # 涉及重要决策的会议
        "印发文件",  # 正式发布或下发的文件
        "人事任免",  # 涉及人员任命或免职的文件
        "表彰获奖",  # 表扬、奖励相关的文件
        "问责处分",  # 处罚、问责相关的文件
        "函件回复",  # 对请示、咨询等的回复
        "请示批复",  # 请示和审批相关的文件
        "通知通报"   # 一般性通知、通报
    ]
    
    # 定义ES索引映射
    ES_MAPPING = {
        "settings": {
            "analysis": {
                "analyzer": {
                    "text_analyzer": {
                        "type": "custom",
                        "tokenizer": "ik_max_word",
                        "filter": ["lowercase", "asciifolding"]
                    }
                }
            },
            "index": {
                "number_of_replicas": 0,
                "number_of_shards": 1,
                "max_ngram_diff": 50,
                "mapping": {
                    "total_fields": {
                        "limit": 2000
                    }
                }
            }
        },
        "mappings": {
            "properties": {
                "content": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "search_analyzer": "ik_smart",
                    "fields": {
                        "keyword": {  
                            "type": "keyword",
                            "ignore_above": 256
                        }
                    }
                },
                "project_name": {
                    "type": "keyword",
                    "fields": {
                        "text": {
                            "type": "text",
                            "analyzer": "ik_smart"
                        }
                    }
                },
                "source": {
                    "type": "keyword"
                },
                "page_num": {
                    "type": "integer"
                },
                "doc_id": {
                    "type": "keyword"
                },
                "action": {
                    "type": "keyword"
                },
                "metadata": {
                    "type": "object",
                    "dynamic": True
                },
                "embedding": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True,
                    "similarity": "cosine"
                },
                "year": {
                    "type": "integer"
                }
            }
        }
    }
    
    # 添加 PROJECTINFO_MAPPING
    PROJECTINFO_MAPPING = {
        "settings": {
            "analysis": {
                "analyzer": {
                    "text_analyzer": {
                        "type": "custom",
                        "tokenizer": "ik_max_word",
                        "filter": ["lowercase", "asciifolding"]
                    }
                }
            }
        },
        "mappings": {
            "dynamic": False,
            "properties": {
                "docType": {
                    "type": "keyword",
                    "doc_values": True
                },
                "projectName": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "fields": {
                        "keyword": {"type": "keyword"} 
                    }
                },
                "projectNo": {"type": "keyword"},
                "projectYear": {"type": "short"},
                "projectBeginEndYear": {"type": "text"},
                "mainCompany": {"type": "text", "analyzer": "ik_max_word"},
                "auxiliaryCompany": {"type": "text", "analyzer": "ik_max_word"},
                "money": {"type": "long"},
                "checkTime": {"type": "date", "format": "yyyy-MM-dd"},
                "skill": {"type": "text", "analyzer": "text_analyzer"},
                "content": {"type": "text", "analyzer": "text_analyzer"},
                "result": {"type": "text", "analyzer": "text_analyzer"},
                "documentType": {"type": "keyword"},
                "documentDate": {"type": "date", "format": "yyyy-MM-dd"},
                "organizer": {"type": "text", "analyzer": "ik_max_word"},
                "participants": {
                    "type": "text",
                    "analyzer": "ik_max_word",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "summary": {"type": "text", "analyzer": "text_analyzer"}
            }
        }
    }
    
    def __init__(self, es_config: Dict[str, Any]):
        """初始化 ES 客户端"""
        # 从配置中获取完整URL
        host = es_config.get('host', 'http://localhost:9200')
        
        # 从URL中提取host和port
        if '://' in host:
            host = host.split('://')[-1]
        if ':' in host:
            host, port = host.split(':')
            port = int(port)
        else:
            port = 9200
        
        print(f"Initializing ES client with host={host}, port={port}")
        
        self.es = ESClient(
            host=host,
            port=port,
            username=es_config.get('username', 'elastic'),
            password=es_config.get('password', 'elastic'),
            index_name=es_config.get('index_name', 'docs')
        )
        self.index_name = es_config.get('index_name', 'docs')
        self.projectinfo_index = "projectinfo"  # 添加projectinfo索引名称
        
    async def init(self):
        """初始化ES索引"""
        try:
            # 检查并创建主索引
            if not await self.es.index_exists(self.index_name):
                await self.es.init_index(self.index_name, self.ES_MAPPING)
                print(f"Successfully created new index: {self.index_name}")
            else:
                print(f"Index {self.index_name} already exists")

            # 检查并创建projectinfo索引
            if not await self.es.index_exists(self.projectinfo_index):
                await self.es.init_index(self.projectinfo_index, self.PROJECTINFO_MAPPING)
                print(f"Successfully created new index: {self.projectinfo_index}")
            else:
                print(f"Index {self.projectinfo_index} already exists")
                
            return True
                
        except Exception as e:
            print(f"Error initializing ES indices: {str(e)}")
            raise

    async def close(self):
        """关闭ES连接"""
        try:
            if hasattr(self, 'es') and self.es:
                await self.es.close()
                print("ES connection closed successfully")
        except Exception as e:
            print(f"Error closing ES connection: {str(e)}")
    
    async def add_chunks(self, chunks: List[Dict[str, Any]]) -> bool:
        """批量添加或更新知识片段"""
        try:
            operations = []
            for chunk in chunks:
                # 构建文档ID
                chunk_id = f"{chunk['doc_id']}_{chunk['page_num']}_{chunk['chunk_num']}"
                
                # 建upsert操作
                operations.extend([
                    {"update": {"_index": self.index_name, "_id": chunk_id}},
                    {"doc": chunk, "doc_as_upsert": True}
                ])
            
            if operations:
                result = await self.es.bulk(operations)
                if result.get('errors'):
                    errors = [item for item in result['items'] if 'error' in item['update']]
                    print(f"Bulk update had errors: {errors}")
                    return False
                return True
            return True
            
        except Exception as e:
            print(f"Error adding chunks: {str(e)}")
            return False
            
    async def search(
        self,
        query: Dict[str, Any],
        size: int = 10,
        source: Union[bool, List[str]] = True,
        highlight: Dict[str, Any] = None,
        sort: List[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """执行搜索并返回结果"""
        try:
            body = {
                "query": query,
                "size": size,
            }
            
            # 添加 _source
            if isinstance(source, list):
                body["_source"] = source  # 注意是 "_source" 而不是 "source"
            else:
                body["_source"] = source
            
            # 添加排序
            if sort is not None:
                body["sort"] = sort
            
            # 保存查询用于调试
            # file_path = 'query.json'
            # try:
            #     with open(file_path, 'w', encoding='utf-8') as file:
            #         json.dump(body, file, indent=4, ensure_ascii=False)
            #     print(f"Query saved to {file_path}")
            # except Exception as e:
            #     print(f"Error saving query to {file_path}: {e}")

            # 执行查询
            results = await self.es.search(body)
            return results
            
        except Exception as e:
            print(f"Error searching documents: {str(e)}")
            return {"hits": {"hits": []}}
    
    
    async def extract_document_info_multi(
        self,
        project_name: str,
        action: str,
        config: Dict[str, Dict[str, Any]],
        llm: Any
    ) -> Dict[str, Any]:
        """根据配置提取文档信息"""
        try:
            result = {"name": project_name}
            
            for field_name, field_config in config.items():
                if field_name == "name":
                    continue
                    
                # 1. 处理关键词
                keywords = field_config.get("keywords", [])
                keyword_should = []
                
                for kw in keywords:
                    # 1.1 基本匹配
                    keyword_should.extend([
                        # 完整短语匹配
                        {
                            "match_phrase": {
                                "content": {
                                    "query": kw,
                                    "boost": 2.0
                                }
                            }
                        }
                    ])
                    
                    # 1.2 处理可能被换行符分隔的情况
                    chars = list(kw)
                    if len(chars) > 1:
                        # 生成所有可能的分割组合
                        for i in range(len(chars)-1):
                            part1 = ''.join(chars[:i+1])
                            part2 = ''.join(chars[i+1:])
                            if len(part1.strip()) > 0 and len(part2.strip()) > 0:
                                keyword_should.append({
                                    "span_near": {
                                        "clauses": [
                                            {"span_term": {"content": part1}},
                                            {"span_term": {"content": part2}}
                                        ],
                                        "slop": 3,  # 允许中间有少量字符（包括换行符）
                                        "in_order": True,
                                        "boost": 1.5
                                    }
                                })
                        
                        # 每个字符单独匹配
                        keyword_should.append({
                            "span_near": {
                                "clauses": [
                                    {"span_term": {"content": char}}
                                    for char in chars
                                ],
                                "slop": len(chars) * 2,  # 允许字符间有更大间隔
                                "in_order": True,
                                "boost": 1.0
                            }
                        })
                
                # 2. 构建查询
                es_query = {
                    "bool": {
                        "must": [
                            {"term": {"project_name": project_name}}
                        ],
                        "should": keyword_should,
                        "minimum_should_match": 1
                    }
                }
                
                # 3. 执行搜索
                results = await self.search(
                    query=es_query,
                    size=10,
                    highlight={
                        "fields": {
                            "content": {}
                        },
                        "pre_tags": ["<em>"],
                        "post_tags": ["</em>"]
                    }
                )
                
                # 4. 处理结果
                contents = []
                seen_content = set()
                max_field_length = 4 * 1024
                current_length = 0
                
                for doc in results:
                    content = doc["content"]
                    if content not in seen_content:
                        content_length = len(content.encode('utf-8'))
                        if current_length + content_length <= max_field_length:
                            seen_content.add(content)
                            contents.append(content)
                            current_length += content_length
                
                # 5. 使用 LLM 提取信息
                if contents:
                    prompt = self._build_extraction_prompt(
                        field_name=field_name,
                        field_config=field_config,
                        keywords=keywords,
                        contents=contents
                    )
                    
                    field_value = await self._extract_field_value(
                        field_name=field_name,
                        prompt=prompt,
                        llm=llm
                    )
                    
                    result[field_name] = self._process_field_value(
                        field_name=field_name,
                        field_value=field_value
                    )
                else:
                    result[field_name] = "未提及"
            
            return result
            
        except Exception as e:
            print(f"Error extracting document info: {str(e)}")
            return {"name": project_name}

    def _build_extraction_prompt(
        self,
        field_name: str,
        field_config: Dict[str, Any],
        keywords: List[str],
        contents: List[str]
    ) -> str:
        """构建提取提示词"""
        if field_name == "type":
            return f"""请分析文档内容，从以下预定义类型中选择一个最匹配的：

    文档类型说明：
    - 决策会议：涉及重要决策讨论的会议文件
    - 印发文件：正式发布或下发的规章制度、工作方案等
    - 人事任免：涉及人员任命、免职、调动的文件
    - 表彰获奖：表扬先进、奖励通报等文件
    - 问责处分：处罚决定、警告通报等文件
    - 件回复：对请示、咨询等的正式回复
    - 请示批复：请示事项及其审批结果
    - 通知通报：一般性通知、情况通报等

    请根据文档内容特征，选择一个最匹配的类型。必须从上述类型中选择一个，不能返回其他类型。

    相关关词：{', '.join(keywords)}

    文本内容：
    {chr(10).join(contents)}"""
        else:
            # 修改日期相关的提示
            date_hint = """
            日期格式规则：
            1. 必须使用YYYY-MM-DD格式
            2. 年份必须是4位数字
            3. 月份必须是01-12
            4. 日期必须是01-31
            5. 如果只有年月信息，日期用01补充
            6. 如果只有年份信息，月日均用01补充
            例如：
            - 2023年 -> 2023-01-01
            - 2023年9月 -> 2023-09-01
            - 2023年9月15日 -> 2023-09-15
            """

            hint = date_hint if field_name in ["documentDate", "checkTime"] else field_config.get('hint', '')

            return f"""请从以下文本中提取"{field_config.get('query', '')}"的信息。

    提取要求：{hint}

    规则：
    1. 只提取文本中实际存在的信息
    2. 如果找不到相关信息，返回"未提及"
    3. 如果是日期，必须使用YYYY-MM-DD格式
    4. 金额需包含数字和单位
    5. 多个项目用分号分隔
    6. 直接返回提取的值，不要包含任何解释

    相关关键词：{', '.join(keywords)}

    文本内容：
    {chr(10).join(contents)}"""

    async def _extract_field_value(
            self,
            field_name: str,
            prompt: str,
            llm: Any
    ) -> str:
        """使用LLM提取字段值"""
        response = await llm.chat([
            {
                "role": "system",
                "content": "你是一个信息提取助手。请直接返回提取的值，不要包含任何解释。"
            },
            {"role": "user", "content": prompt}
        ])
        
        field_value = clean_response(response)
        if field_value.startswith('"') and field_value.endswith('"'):
            field_value = field_value[1:-1]
        
        return field_value.strip()

    def _process_field_value(
        self,
        field_name: str,
        field_value: str
    ) -> str:
        """处理提取的字段值"""
        if field_name == "type":
            if field_value not in self.VALID_TYPES:
                for valid_type in self.VALID_TYPES:
                    if valid_type in field_value:
                        return valid_type
                return "通知通报"
        
        field_type = FIELD_TYPES.get(field_name, FieldType.TEXT)
        processor = FIELD_PROCESSORS[field_type]
        return processor(field_value)
    
    async def extract_document_info(
        self,
        project_name: str,
        action: str,
        config: Dict[str, Dict[str, Any]],
        llm: Any
    ) -> Dict[str, Any]:
        """根据配置提取文档信息"""
        try:
            result = {"name": project_name}
            
            for field_name, field_config in config.items():
                if field_name == "name":  # 跳过name字段
                    continue
                    
                # 1. 使用关键词搜索
                keywords = field_config.get("keywords", [])
                keyword_should = []
                
                # 处理换行符分隔的关键词
                for kw in keywords:
                    # 精确短语匹配
                    keyword_should.append({
                        "match_phrase": {
                            "content": {
                                "query": kw,
                                "boost": 2.0  # 提高精确匹配的权重
                            }
                        }
                    })
                    
                    # 处理可能被换行符分隔的词
                    if field_name == "total_investment":
                        parts = kw.split()
                        if len(parts) > 1:
                            # 对于多词组合，添加跨行匹配
                            keyword_should.append({
                                "span_near": {
                                    "clauses": [
                                        {"span_term": {"content": part}} 
                                        for part in parts
                                    ],
                                    "slop": 3,  # 允许词之间有少量间隔
                                    "in_order": True
                                }
                            })
                        
                        # 添加数字和单位的匹配
                        keyword_should.extend([
                            {
                                "regexp": {
                                    "content": "\\d+(\\.\\d+)?\\s*(万|千|亿)?\\s*元"
                                }
                            },
                            {
                                "match_phrase": {
                                    "content": {
                                        "query": "总额",
                                        "boost": 1.2
                                    }
                                }
                            }
                        ])
                
                # 2. 构建查询
                es_query = {
                    "bool": {
                        "must": [
                            {"term": {"project_name": project_name}}
                        ],
                        "should": keyword_should,
                        "minimum_should_match": 1
                    }
                }
                
                # 3. 检索相关内容
                contents = []
                seen_content = set()
                max_field_length = 4 * 1024
                current_length = 0
                
                results = await self.search(es_query, size=10)
                
                for doc in results:
                    content = doc["content"]
                    if content not in seen_content:
                        content_length = len(content.encode('utf-8'))
                        if current_length + content_length <= max_field_length:
                            seen_content.add(content)
                            contents.append(content)
                            current_length += content_length
                
                if contents:
                    # 构建单字段提取提示词
                    if field_name == "type":
                        prompt = f"""请分析文档内容，从以下预定义类型中选择一个最匹配的：

文档类型说明：
- 决策会议：涉及重要决策讨论的会议文件
- 印发文件：正式发布或下发的规章制度、工作方案等
- 人事任免：涉及人员任命、免职、调动的文件
- 表彰获奖：表扬先进、奖励通报等文件
- 问责处分：处罚决定、警告通报等文件
- 件回复：对请示、咨询等的正式回复
- 请示批复：请示事项及其审批结果
- 通知通报：一般性通知、情况通报等

请根据文档内容特征，选择一个最匹配的类型。必须从上述类型中选择一个，不能返回其他类型。

相关关词：{', '.join(keywords)}

文本内容：
{chr(10).join(contents)}"""
                    else:
                        prompt = f"""请从以下文本中提取"{field_config.get('query', '')}"的信息。

提取要求：{field_config.get('hint', '')}

规则：
1. 只提取文本中实际存在的信息
2. 如果找不到相关信息，返回"未提及"
3. 如果是日期，格式为YYYY-MM
4. 金额需包含数字和单位
5. 多个项目用分号分隔
6. 直接返回提取的值，不要包含任何解释

相关关键词：{', '.join(keywords)}

文本内容：
{chr(10).join(contents)}"""

                    # 使用LLM提取字段值
                    response = await llm.chat([
                        {
                            "role": "system",
                            "content": "你是一个信息提取助手。请直接返回提取的值，不要包含任何解释。"
                        },
                        {"role": "user", "content": prompt}
                    ])
                    
                    # 清理响应
                    field_value = response.strip()
                    if field_value.startswith('"') and field_value.endswith('"'):
                        field_value = field_value[1:-1]
                    
                    # 处理type字段的特殊情况
                    if field_name == "type":
                        if field_value not in self.VALID_TYPES:
                            # 尝试找到最相似的类型
                            for valid_type in self.VALID_TYPES:
                                if valid_type in field_value:
                                    field_value = valid_type
                                    break
                            else:
                                field_value = "通知通报"  # 默认类型
                    
                    # 处理提取的字段值
                    field_type = FIELD_TYPES.get(field_name, FieldType.TEXT)
                    processor = FIELD_PROCESSORS[field_type]
                    result[field_name] = processor(field_value)
                else:
                    result[field_name] = "未提及"
            
            return result
            
        except Exception as e:
            print(f"Error extracting document info: {str(e)}")
            return {"name": project_name}
            
    async def get_context(
        self,
        question: str,
        question_embedding: List[float],
        project_name: str,
        project_type: str,
        year: Optional[str] = None,
        keywords: Optional[List[str]] = None
    ) -> Dict[str, List[str]]:
        """获取相关上下文和源文档信息"""
        try:
            should_clauses = [
                    {"match": {"content": {"query": question, "boost": 1.0}}},
                    {
                        "script_score": {
                            "query": {"match_all": {}},
                            "script": {
                                "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                                "params": {"query_vector": question_embedding}
                            },
                            "boost": 1.2
                        }
                    }
                ]

            if year:
                should_clauses.append({
                    "term": {"year": {"value": int(year), "boost": 0.8}}
                })

            if keywords:
                should_clauses.append({
                    "bool": {
                        "should": [
                            {"terms": {"metadata.doc_type": keywords, "boost": 1.5}},
                            {"match": {"metadata.doc_type": {"query": " ".join(keywords), "operator": "or", "boost": 1.2}}}
                        ],
                        "minimum_should_match": 1
                    }
                })

            query = {
                "bool": {
                    "should": should_clauses,
                    "filter": [
                        {
                            "bool": {
                                "should": [
                                    {"term": {"project_name.keyword": project_name}},
                                    {"match": {"project_name.text": {"query": project_name, "fuzziness": "AUTO"}}}
                                ],
                                "minimum_should_match": 1
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }
            # 执行搜索
            es_results = await self.search(
                query=query,
                size=15,
                source=True,  # 改为 True 表示返回所有字段
                sort=[
                    {"_score": "desc"},  # 先按相关性得分降序
                    {"page_num": "asc"}  # 再按页码升序
                ]
            )

            # 处理搜索结果
            context_list = []
            source_documents = []
            
            # es_results 现在是列表，每个元素已经包含了 _source 和 score
            for doc in es_results:
                # 添加到上下文列表
                context_list.append(doc.get('content', ''))
                source_documents.append(get_filename_from_url(doc.get('source', '').replace('json', 'pdf')))

            return {
                "context": context_list,
                "source_documents": source_documents
            }

        except Exception as e:
            print(f"Error getting context: {str(e)}")
            return {
                "context": [],
                "source_documents": []
            }
    
    
    async def get_context_newline(
        self,
        question: str,
        question_embedding: List[float],
        project_name: str,
        project_type: str,
        year: Optional[str] = None,
        keywords: Optional[List[str]] = None
    ) -> Dict[str, List[str]]:
        """获取相关上下文和源文档信息"""
        try:
            # 1. 预处理查询文本
            question = question.replace('\n', ' ').strip()
            
            # 2. 构建混合查询
            should_clauses = [
                # 向量相似度保持不变
                {
                    "script_score": {
                        "query": {"match_all": {}},
                        "script": {
                            "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                            "params": {"query_vector": question_embedding}
                        },
                        "boost": 0.7
                    }
                },
                # 添加跨行文本匹配
                {
                    "span_near": {
                        "clauses": [
                            {"span_term": {"content": term}} 
                            for term in question.split()
                        ],
                        "slop": 5,  # 允许词之间的最大距离
                        "in_order": False,  # 不要词的顺序完全匹配
                        "boost": 1.2
                    }
                },
                # 添加模糊匹配
                {
                    "match": {
                        "content": {
                            "query": question,
                            "operator": "or",
                            "minimum_should_match": "50%",  # 至少匹配一半的词
                            "fuzziness": "AUTO",  # 自动模糊匹配
                            "boost": 0.8
                        }
                    }
                }
            ]

            # 3. 年份匹配优化
            if year:
                should_clauses.append({
                    "term": {
                        "year": {
                            "value": int(year),
                            "boost": 1.5
                        }
                    }
                })

            # 4. 关键字匹配优化
            if keywords:
                keyword_clauses = []
                for keyword in keywords:
                    # 处理可能包含换行符的关键词
                    keyword_parts = keyword.split()
                    if len(keyword_parts) > 1:
                        # 对多词关键词使用 span_near
                        keyword_clauses.extend([
                            # 在 content 中搜索
                            {
                                "span_near": {
                                    "clauses": [
                                        {"span_term": {"content": part}} 
                                        for part in keyword_parts
                                    ],
                                    "slop": 3,
                                    "in_order": True,
                                    "boost": 1.5
                                }
                            },
                            # 在 source 中搜索
                            {
                                "match_phrase": {
                                    "source": {
                                        "query": keyword,
                                        "boost": 0.8
                                    }
                                }
                            }
                        ])
                    else:
                        # 单词关键词
                        keyword_clauses.extend([
                            {
                                "term": {
                                    "content": {
                                        "value": keyword,
                                        "boost": 1.5
                                    }
                                }
                            },
                            {
                                "term": {
                                    "source": {
                                        "value": keyword,
                                        "boost": 0.8
                                    }
                                }
                            }
                        ])
                
                should_clauses.append({
                    "bool": {
                        "should": keyword_clauses,
                        "minimum_should_match": 1
                    }
                })

            # 5. 构建完整查询
            query = {
                "bool": {
                    "must": [
                        {
                            "bool": {
                                "should": [
                                    # 在 content 中搜索
                                    {
                                        "match": {
                                            "content": {
                                                "query": question,
                                                "boost": 0.3
                                            }
                                        }
                                    },
                                    # 在 source 中搜索
                                    {
                                        "match": {
                                            "source": {
                                                "query": question,
                                                "boost": 0.2
                                            }
                                        }
                                    }
                                ],
                                "minimum_should_match": 1
                            }
                        }
                    ],
                    "should": should_clauses,
                    "filter": [
                        # 项目名称过滤
                        {
                            "bool": {
                                "should": [
                                    {"term": {"project_name": project_name}},
                                    {"match": {"project_name": project_name}}
                                ],
                                "minimum_should_match": 1
                            }
                        },
                        # 文档类型过滤
                        {
                            "term": {
                                "action": "文书档案" if project_type == "文书档案" else "项目档案"
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }

            # 6. 执行搜索
            es_results = await self.search(
                query=query,
                size=10,
                source=True,
                sort=[
                    {"_score": "desc"},
                    {"page_num": "asc"}
                ]
            )

            # 处理搜索结果
            context_list = []
            source_documents = []
            
            # es_results 现在是列表，每个元素已经包含了 _source 和 score
            for doc in es_results:
                # 添加到上下文列表
                context_list.append(doc.get('content', ''))
                source_documents.append(get_filename_from_url(doc.get('source', '').replace('json', 'pdf')))

            return {
                "context": context_list,
                "source_documents": source_documents
            }

        except Exception as e:
            print(f"Error getting context: {str(e)}")
            return {
                "context": [],
                "source_documents": []
            }
    
    
    
    async def get_context_multi(
        self,
        question: str,
        question_embedding: List[float],
        project_name: str,
        project_type: str,
        year: Optional[str] = None,
        keywords: Optional[List[str]] = None
    ) -> Dict[str, List[str]]:
        """获取相关上下文和源文档信息"""
        try:
            # 1. 预处理查询文本
            question = question.replace('\n', ' ').strip()
            
            # 2. 构建混合查询
            should_clauses = [
                # 向量相似度保持不变
                {
                    "script_score": {
                        "query": {"match_all": {}},
                        "script": {
                            "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                            "params": {"query_vector": question_embedding}
                        },
                        "boost": 0.7
                    }
                },
                # 多字段匹配
                {
                    "multi_match": {
                        "query": question,
                        "fields": ["content^2", "source"],  # content字段权重更高
                        "type": "best_fields",
                        "operator": "or",
                        "minimum_should_match": "50%",
                        "fuzziness": "AUTO",
                        "boost": 1.2
                    }
                },
                # 跨行文本匹配 - content
                {
                    "span_near": {
                        "clauses": [
                            {"span_term": {"content": term}} 
                            for term in question.split()
                        ],
                        "slop": 5,
                        "in_order": False,
                        "boost": 1.0
                    }
                },
                # source 字段匹配
                {
                    "match": {
                        "source": {
                            "query": question,
                            "boost": 0.5  # source字段权重较低
                        }
                    }
                }
            ]

            # 3. 年份匹配优化
            if year:
                should_clauses.append({
                    "term": {
                        "year": {
                            "value": int(year),
                            "boost": 1.5
                        }
                    }
                })

            # 4. 关键字匹配优化
            if keywords:
                keyword_clauses = []
                for keyword in keywords:
                    # 处理可能包含换行符的关键词
                    keyword_parts = keyword.split()
                    if len(keyword_parts) > 1:
                        # 对多词关键词使用 span_near
                        keyword_clauses.extend([
                            # 在 content 中搜索
                            {
                                "span_near": {
                                    "clauses": [
                                        {"span_term": {"content": part}} 
                                        for part in keyword_parts
                                    ],
                                    "slop": 3,
                                    "in_order": True,
                                    "boost": 1.5
                                }
                            },
                            # 在 source 中搜索
                            {
                                "match_phrase": {
                                    "source": {
                                        "query": keyword,
                                        "boost": 0.8
                                    }
                                }
                            }
                        ])
                    else:
                        # 单词关键词
                        keyword_clauses.extend([
                            {
                                "term": {
                                    "content": {
                                        "value": keyword,
                                        "boost": 1.5
                                    }
                                }
                            },
                            {
                                "term": {
                                    "source": {
                                        "value": keyword,
                                        "boost": 0.8
                                    }
                                }
                            }
                        ])
                
                should_clauses.append({
                    "bool": {
                        "should": keyword_clauses,
                        "minimum_should_match": 1
                    }
                })

            # 5. 构建完整查询
            query = {
                "bool": {
                    "must": [
                        {
                            "bool": {
                                "should": [
                                    # 在 content 中搜索
                                    {
                                        "match": {
                                            "content": {
                                                "query": question,
                                                "boost": 0.3
                                            }
                                        }
                                    },
                                    # 在 source 中搜索
                                    {
                                        "match": {
                                            "source": {
                                                "query": question,
                                                "boost": 0.2
                                            }
                                        }
                                    }
                                ],
                                "minimum_should_match": 1
                            }
                        }
                    ],
                    "should": should_clauses,
                    "filter": [
                        # 项目名称过滤
                        {
                            "bool": {
                                "should": [
                                    {"term": {"project_name": project_name}},
                                    {"match": {"project_name": project_name}}
                                ],
                                "minimum_should_match": 1
                            }
                        },
                        # 文档类型过滤
                        {
                            "term": {
                                "action": "文书档案" if project_type == "文书档案" else "项目档案"
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }

            # 6. 执行搜索
            es_results = await self.search(
                query=query,
                size=10,
                source=True,
                sort=[
                    {"_score": "desc"},
                    {"page_num": "asc"}
                ]
            )

            # 处理搜索结果
            context_list = []
            source_documents = []
            
            # es_results 现在是列表，每个元素已经包含了 _source 和 score
            for doc in es_results:
                # 添加到上下文列表
                context_list.append(doc.get('content', ''))
                source_documents.append(get_filename_from_url(doc.get('source', '').replace('json', 'pdf')))

            return {
                "context": context_list,
                "source_documents": source_documents
            }

        except Exception as e:
            print(f"Error getting context: {str(e)}")
            return {
                "context": [],
                "source_documents": []
            }

    async def update_project_info(self, project_data: Dict[str, Any], doc_id: str) -> bool:
        """更新项目信息到projectinfo索引
        
        Args:
            project_data: 项目数据字典
            doc_id: 文档ID
            
        Returns:
            bool: 操作是否成功
        """
        try:
            if not doc_id:
                print("Error: doc_id is required for update operation")
                return False
            
            # 预处理数据
            processed_data = {}
            
            # 添加文档类型标识
            if "documentType" in project_data:
                processed_data["docType"] = "文书档案"
                # 处理文书档案特有字段
                if project_data.get("documentDate"):
                    try:
                        date_str = project_data["documentDate"].replace('年', '-').replace('月', '-').replace('日', '')
                        # 确保日期格式完整
                        full_date = self._ensure_full_date(date_str)
                        if full_date:
                            processed_data["documentDate"] = full_date
                    except Exception as e:
                        print(f"Error processing documentDate: {e}")
            else:
                processed_data["docType"] = "项目档案"
                # 处理项目档案特有字段
                if project_data.get('projectYear'):
                    try:
                        year = project_data['projectYear'].replace('年', '').strip()
                        if year.isdigit():
                            processed_data['projectYear'] = int(year)
                    except Exception as e:
                        print(f"Error processing projectYear: {e}")
                
                if project_data.get('projectBeginEndYear'):
                    try:
                        begin_end = project_data['projectBeginEndYear'].replace('年', '-').replace('月', '').strip()
                        if begin_end:  # 只在非空时添加
                            processed_data['projectBeginEndYear'] = begin_end
                    except Exception as e:
                        print(f"Error processing projectBeginEndYear: {e}")
                
                if project_data.get('checkTime'):
                    try:
                        check_time = project_data['checkTime'].replace('年', '-').replace('月', '-').replace('日', '').strip()
                        # 确保日期格式完整
                        full_date = self._ensure_full_date(check_time)
                        if full_date:
                            processed_data['checkTime'] = full_date
                    except Exception as e:
                        print(f"Error processing checkTime: {e}")

            # 复制其他字段，排除日期字段和已处理字段
            exclude_fields = {'docType', 'documentDate', 'projectYear', 'projectBeginEndYear', 'checkTime'}
            for key, value in project_data.items():
                if key not in exclude_fields and value:  # 只复制非空值
                    processed_data[key] = value
            
            # 构建upsert操作
            operations = [
                {"update": {"_index": self.projectinfo_index, "_id": doc_id}},
                {"doc": processed_data, "doc_as_upsert": True}
            ]
            
            # 执行bulk操作
            result = await self.es.bulk(operations)
            
            if result.get('errors'):
                errors = [item for item in result['items'] if 'error' in item['update']]
                print(f"Bulk update had errors: {errors}")
                return False
            
            print(f"Successfully updated/created project {doc_id}")
            return True
            
        except Exception as e:
            print(f"Error updating project info: {str(e)}")
            return False

    def _ensure_full_date(self, date_str: str) -> Optional[str]:
        """确保日期格式为完整的YYYY-MM-DD格式
        
        Args:
            date_str: 输入的日期字符串
            
        Returns:
            Optional[str]: YYYY-MM-DD格式的日期字符串，如果无效则返回None
        """
        if not date_str or not date_str.strip():
            return None
        
        try:
            date_str = date_str.strip()
            
            # 如果已经是YYYY-MM-DD格式，直接返回
            if re.match(r'\d{4}-\d{2}-\d{2}$', date_str):
                return date_str
            
            parts = date_str.split('-')
            
            # 处理只有年份的情况
            if len(parts) == 1 and len(parts[0]) == 4 and parts[0].isdigit():
                return f"{parts[0]}-01-01"  # 年份转为年月日
            
            # 处理年月的情况
            if len(parts) == 2:
                year, month = parts
                if year.isdigit() and month.isdigit():
                    month_num = int(month)
                    if 1 <= month_num <= 12:
                        return f"{year}-{str(month_num).zfill(2)}-01"  # 年月转为年月日
            
            # 处理年月日的情况
            if len(parts) == 3:
                year, month, day = parts
                if year.isdigit() and month.isdigit() and day.isdigit():
                    month_num = int(month)
                    day_num = int(day)
                    if 1 <= month_num <= 12 and 1 <= day_num <= 31:
                        return f"{year}-{str(month_num).zfill(2)}-{str(day_num).zfill(2)}"  # 保持完整年月日
            
            return None
        except (ValueError, IndexError):
            return None

    async def get_project_info(self, project_no: str) -> Optional[Dict[str, Any]]:
        """根据项目编号获取项目信息

        Args:
            project_no: 项目编号

        Returns:
            Optional[Dict[str, Any]]: 项目信息字典，如果不存在返回None
        """
        try:
            result = await self.es.get(
                index=self.projectinfo_index,
                id=project_no
            )
            return result.get('_source')
        except Exception as e:
            print(f"Error getting project info: {str(e)}")
            return None

    async def delete_document_chunks(self, doc_id: str) -> bool:
        """根据doc_id删除ES中的文档分块数据

        Args:
            doc_id: 文档ID

        Returns:
            bool: 删除是否成功
        """
        try:
            # 构建删除查询
            query = {
                "query": {
                    "term": {
                        "doc_id": doc_id
                    }
                }
            }

            # 执行删除操作
            result = await self.es.delete_by_query(
                index=self.index_name,
                body=query,
                refresh=True
            )

            deleted_count = result.get('deleted', 0)
            print(f"Deleted {deleted_count} document chunks for doc_id: {doc_id}")
            return True

        except Exception as e:
            print(f"Error deleting document chunks for doc_id {doc_id}: {str(e)}")
            return False

    async def delete_project_info(self, doc_id: str) -> bool:
        """根据doc_id删除ES中的项目信息

        Args:
            doc_id: 文档ID

        Returns:
            bool: 删除是否成功
        """
        try:
            # 直接根据ID删除文档
            result = await self.es.delete(
                index=self.projectinfo_index,
                id=doc_id,
                ignore=[404]  # 忽略文档不存在的错误
            )

            print(f"Deleted project info for doc_id: {doc_id}")
            return True

        except Exception as e:
            print(f"Error deleting project info for doc_id {doc_id}: {str(e)}")
            return False