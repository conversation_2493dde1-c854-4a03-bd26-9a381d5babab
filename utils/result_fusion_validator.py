#!/usr/bin/env python3
"""
结果融合和验证器
对抽取结果进行融合、验证和质量评估
"""

import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

from utils.llm import LLM
from utils.log import log
from utils.config import config


class ResultFusionValidator:
    """结果融合和验证器"""
    
    def __init__(self):
        self.llm = LLM()
        
        # 验证配置
        self.min_confidence_threshold = config.get("validator.min_confidence", 0.5)
        self.enable_cross_validation = config.get("validator.cross_validation", True)
        
        # 字段验证规则
        self.validation_rules = self._load_validation_rules()
    
    def _load_validation_rules(self) -> Dict[str, Any]:
        """加载字段验证规则"""
        return {
            "project_name": {
                "min_length": 3,
                "max_length": 100,
                "forbidden_patterns": [r"^\d+$", r"^[a-zA-Z]+$"],  # 不能只是数字或字母
                "required_patterns": [r"[\u4e00-\u9fa5]"]  # 必须包含中文
            },
            "project_no": {
                "min_length": 3,
                "max_length": 50,
                "required_patterns": [r"[A-Z0-9\-]"],  # 必须包含字母数字或横线
                "format_patterns": [
                    r"^[A-Z]{2,4}-\d{4}-\d{3}$",  # 标准格式
                    r"^[A-Z0-9\-]{5,}$"  # 通用格式
                ]
            },
            "responsible_unit": {
                "min_length": 3,
                "max_length": 100,
                "forbidden_patterns": [r"^\d+$"],
                "required_patterns": [r"[\u4e00-\u9fa5]"],  # 必须包含中文
                "common_suffixes": ["办公室", "局", "委员会", "公司", "中心", "部门"]
            },
            "leader": {
                "min_length": 2,
                "max_length": 10,
                "required_patterns": [r"^[\u4e00-\u9fa5]{2,4}$"],  # 2-4个中文字符
                "forbidden_patterns": [r"\d", r"[a-zA-Z]"]  # 不能包含数字或字母
            },
            "total_investment": {
                "min_length": 2,
                "max_length": 50,
                "required_patterns": [r"\d"],  # 必须包含数字
                "format_patterns": [
                    r"[￥¥]?[\d,]+\.?\d*[万亿]?元?",
                    r"人民币[壹贰叁肆伍陆柒捌玖拾佰仟万亿]+元整"
                ]
            },
            "start_date": {
                "format_patterns": [
                    r"\d{4}年\d{1,2}月\d{1,2}日",
                    r"\d{4}年\d{1,2}月",
                    r"\d{4}-\d{1,2}-\d{1,2}",
                    r"\d{4}/\d{1,2}/\d{1,2}"
                ]
            },
            "end_date": {
                "format_patterns": [
                    r"\d{4}年\d{1,2}月\d{1,2}日",
                    r"\d{4}年\d{1,2}月",
                    r"\d{4}-\d{1,2}-\d{1,2}",
                    r"\d{4}/\d{1,2}/\d{1,2}"
                ]
            }
        }
    
    async def validate_and_fuse_results(
        self,
        extraction_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证和融合抽取结果"""
        try:
            log.info(f"开始结果验证和融合: {extraction_result.get('project_name', 'Unknown')}")
            
            extracted_fields = extraction_result.get("extracted_fields", {})
            field_analysis = extraction_result.get("field_analysis", {})
            
            # 验证各个字段
            validated_fields = {}
            validation_reports = {}
            
            for field_name, field_value in extracted_fields.items():
                validation_result = await self._validate_single_field(
                    field_name, field_value, field_analysis.get(field_name, {})
                )
                
                validated_fields[field_name] = validation_result["validated_value"]
                validation_reports[field_name] = validation_result["validation_report"]
            
            # 交叉验证
            cross_validation_result = await self._cross_validate_fields(validated_fields)
            
            # 计算整体质量分数
            quality_score = self._calculate_quality_score(
                validated_fields, validation_reports, cross_validation_result
            )
            
            # 生成最终结果
            final_result = {
                "project_name": extraction_result.get("project_name"),
                "action": extraction_result.get("action"),
                "validated_fields": validated_fields,
                "validation_reports": validation_reports,
                "cross_validation": cross_validation_result,
                "quality_assessment": {
                    "overall_score": quality_score,
                    "confidence_level": self._get_confidence_level(quality_score),
                    "completeness": self._calculate_completeness(validated_fields),
                    "reliability": self._calculate_reliability(validation_reports)
                },
                "processing_info": {
                    "validation_method": "multi_strategy_fusion",
                    "validation_time": datetime.now().isoformat(),
                    "original_extraction_time": extraction_result.get("extraction_time")
                }
            }
            
            log.info(f"结果验证完成: 质量分数 {quality_score:.2f}")
            return final_result
            
        except Exception as e:
            log.error(f"结果验证和融合失败: {e}")
            return {"error": str(e)}
    
    async def _validate_single_field(
        self,
        field_name: str,
        field_value: Any,
        field_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证单个字段"""
        try:
            if field_value is None:
                return {
                    "validated_value": None,
                    "validation_report": {
                        "is_valid": False,
                        "issues": ["field_value_is_null"],
                        "confidence": 0.0
                    }
                }
            
            field_str = str(field_value).strip()
            validation_rules = self.validation_rules.get(field_name, {})
            
            issues = []
            confidence_adjustments = []
            
            # 长度验证
            min_length = validation_rules.get("min_length", 0)
            max_length = validation_rules.get("max_length", 1000)
            
            if len(field_str) < min_length:
                issues.append(f"length_too_short_{len(field_str)}_min_{min_length}")
                confidence_adjustments.append(-0.3)
            elif len(field_str) > max_length:
                issues.append(f"length_too_long_{len(field_str)}_max_{max_length}")
                confidence_adjustments.append(-0.2)
            
            # 格式验证
            format_patterns = validation_rules.get("format_patterns", [])
            if format_patterns:
                format_valid = any(re.search(pattern, field_str) for pattern in format_patterns)
                if not format_valid:
                    issues.append("format_invalid")
                    confidence_adjustments.append(-0.4)
            
            # 必需模式验证
            required_patterns = validation_rules.get("required_patterns", [])
            for pattern in required_patterns:
                if not re.search(pattern, field_str):
                    issues.append(f"missing_required_pattern_{pattern}")
                    confidence_adjustments.append(-0.3)
            
            # 禁止模式验证
            forbidden_patterns = validation_rules.get("forbidden_patterns", [])
            for pattern in forbidden_patterns:
                if re.search(pattern, field_str):
                    issues.append(f"contains_forbidden_pattern_{pattern}")
                    confidence_adjustments.append(-0.4)
            
            # 特殊验证
            specialized_validation = await self._specialized_field_validation(
                field_name, field_str, validation_rules
            )
            issues.extend(specialized_validation["issues"])
            confidence_adjustments.extend(specialized_validation["confidence_adjustments"])
            
            # 计算最终置信度
            original_confidence = field_analysis.get("confidence", 0.5)
            final_confidence = max(0.0, original_confidence + sum(confidence_adjustments))
            
            # 应用置信度阈值
            is_valid = final_confidence >= self.min_confidence_threshold and len(issues) == 0
            
            return {
                "validated_value": field_str if is_valid else None,
                "validation_report": {
                    "is_valid": is_valid,
                    "issues": issues,
                    "confidence": round(final_confidence, 2),
                    "original_confidence": original_confidence,
                    "confidence_adjustments": confidence_adjustments
                }
            }
            
        except Exception as e:
            log.error(f"验证单个字段失败 {field_name}: {e}")
            return {
                "validated_value": None,
                "validation_report": {"error": str(e)}
            }
    
    async def _specialized_field_validation(
        self,
        field_name: str,
        field_value: str,
        validation_rules: Dict[str, Any]
    ) -> Dict[str, Any]:
        """专门的字段验证"""
        issues = []
        confidence_adjustments = []
        
        try:
            if field_name == "total_investment":
                # 金额验证
                if not re.search(r'\d', field_value):
                    issues.append("no_numeric_value")
                    confidence_adjustments.append(-0.5)
                
                # 检查是否有合理的金额范围
                amount_match = re.search(r'([\d,]+\.?\d*)', field_value)
                if amount_match:
                    amount_str = amount_match.group(1).replace(',', '')
                    try:
                        amount = float(amount_str)
                        if '万' in field_value:
                            amount *= 10000
                        elif '亿' in field_value:
                            amount *= 100000000
                        
                        # 合理性检查
                        if amount < 1000:  # 小于1000元可能不合理
                            issues.append("amount_too_small")
                            confidence_adjustments.append(-0.2)
                        elif amount > 1000000000000:  # 大于1万亿可能不合理
                            issues.append("amount_too_large")
                            confidence_adjustments.append(-0.2)
                    except ValueError:
                        issues.append("amount_parse_error")
                        confidence_adjustments.append(-0.3)
            
            elif field_name in ["start_date", "end_date"]:
                # 日期验证
                date_valid = self._validate_date_format(field_value)
                if not date_valid:
                    issues.append("invalid_date_format")
                    confidence_adjustments.append(-0.4)
            
            elif field_name == "responsible_unit":
                # 单位名称验证
                common_suffixes = validation_rules.get("common_suffixes", [])
                has_valid_suffix = any(field_value.endswith(suffix) for suffix in common_suffixes)
                if not has_valid_suffix:
                    issues.append("uncommon_unit_suffix")
                    confidence_adjustments.append(-0.1)  # 轻微降低置信度
            
        except Exception as e:
            log.error(f"专门字段验证失败: {e}")
            issues.append("specialized_validation_error")
            confidence_adjustments.append(-0.1)
        
        return {
            "issues": issues,
            "confidence_adjustments": confidence_adjustments
        }
    
    def _validate_date_format(self, date_str: str) -> bool:
        """验证日期格式"""
        date_patterns = [
            r'^\d{4}年\d{1,2}月\d{1,2}日$',
            r'^\d{4}年\d{1,2}月$',
            r'^\d{4}-\d{1,2}-\d{1,2}$',
            r'^\d{4}/\d{1,2}/\d{1,2}$'
        ]
        
        return any(re.match(pattern, date_str) for pattern in date_patterns)
    
    async def _cross_validate_fields(
        self,
        validated_fields: Dict[str, Any]
    ) -> Dict[str, Any]:
        """交叉验证字段"""
        try:
            if not self.enable_cross_validation:
                return {"enabled": False}
            
            cross_validation_results = {}
            
            # 验证项目名称和负责单位的一致性
            project_name = validated_fields.get("project_name")
            responsible_unit = validated_fields.get("responsible_unit")
            
            if project_name and responsible_unit:
                consistency_check = await self._check_name_unit_consistency(
                    project_name, responsible_unit
                )
                cross_validation_results["name_unit_consistency"] = consistency_check
            
            # 验证开始和结束日期的逻辑性
            start_date = validated_fields.get("start_date")
            end_date = validated_fields.get("end_date")
            
            if start_date and end_date:
                date_logic_check = self._check_date_logic(start_date, end_date)
                cross_validation_results["date_logic"] = date_logic_check
            
            # 验证项目名称和编号的关联性
            project_name = validated_fields.get("project_name")
            project_no = validated_fields.get("project_no")
            
            if project_name and project_no:
                name_no_consistency = self._check_name_no_consistency(project_name, project_no)
                cross_validation_results["name_no_consistency"] = name_no_consistency
            
            return {
                "enabled": True,
                "results": cross_validation_results,
                "overall_consistency": self._calculate_consistency_score(cross_validation_results)
            }
            
        except Exception as e:
            log.error(f"交叉验证失败: {e}")
            return {"enabled": True, "error": str(e)}
    
    async def _check_name_unit_consistency(
        self,
        project_name: str,
        responsible_unit: str
    ) -> Dict[str, Any]:
        """检查项目名称和负责单位的一致性"""
        try:
            # 使用LLM进行语义一致性检查
            consistency_prompt = f"""
请判断以下项目名称和负责单位是否具有合理的一致性：

项目名称：{project_name}
负责单位：{responsible_unit}

请从以下角度分析：
1. 负责单位是否有能力承担该类型项目
2. 项目名称是否与单位的职能范围匹配
3. 是否存在明显的不一致之处

请返回一个0-1之间的一致性分数，并简要说明理由。
格式：分数|理由
"""
            
            response = await self.llm.chat([
                {"role": "system", "content": "你是项目管理专家，请客观评估项目信息的一致性。"},
                {"role": "user", "content": consistency_prompt}
            ])
            
            if response and "|" in response:
                parts = response.split("|", 1)
                try:
                    score = float(parts[0].strip())
                    reason = parts[1].strip()
                    return {
                        "consistency_score": max(0.0, min(1.0, score)),
                        "reason": reason,
                        "method": "llm_semantic_analysis"
                    }
                except ValueError:
                    pass
            
            # 降级到简单的关键词匹配
            return self._simple_name_unit_consistency(project_name, responsible_unit)
            
        except Exception as e:
            log.error(f"名称单位一致性检查失败: {e}")
            return {"consistency_score": 0.5, "error": str(e)}
    
    def _simple_name_unit_consistency(
        self,
        project_name: str,
        responsible_unit: str
    ) -> Dict[str, Any]:
        """简单的名称单位一致性检查"""
        # 基于关键词的简单匹配
        project_keywords = set(re.findall(r'[\u4e00-\u9fa5]{2,}', project_name))
        unit_keywords = set(re.findall(r'[\u4e00-\u9fa5]{2,}', responsible_unit))
        
        common_keywords = project_keywords.intersection(unit_keywords)
        
        if common_keywords:
            score = min(1.0, len(common_keywords) * 0.3)
            return {
                "consistency_score": score,
                "reason": f"共同关键词: {', '.join(common_keywords)}",
                "method": "keyword_matching"
            }
        else:
            return {
                "consistency_score": 0.3,  # 默认中等一致性
                "reason": "无明显关键词重叠",
                "method": "keyword_matching"
            }
    
    def _check_date_logic(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """检查日期逻辑性"""
        try:
            # 简单的日期逻辑检查
            start_year = re.search(r'(\d{4})', start_date)
            end_year = re.search(r'(\d{4})', end_date)
            
            if start_year and end_year:
                start_y = int(start_year.group(1))
                end_y = int(end_year.group(1))
                
                if end_y < start_y:
                    return {
                        "is_logical": False,
                        "issue": "end_date_before_start_date",
                        "logic_score": 0.0
                    }
                elif end_y - start_y > 10:
                    return {
                        "is_logical": True,
                        "issue": "project_duration_very_long",
                        "logic_score": 0.7
                    }
                else:
                    return {
                        "is_logical": True,
                        "issue": None,
                        "logic_score": 1.0
                    }
            
            return {
                "is_logical": True,
                "issue": "date_format_unclear",
                "logic_score": 0.8
            }
            
        except Exception as e:
            log.error(f"日期逻辑检查失败: {e}")
            return {"is_logical": True, "logic_score": 0.5, "error": str(e)}
    
    def _check_name_no_consistency(self, project_name: str, project_no: str) -> Dict[str, Any]:
        """检查项目名称和编号的关联性"""
        # 简单的关联性检查
        name_chars = set(re.findall(r'[A-Z]', project_name.upper()))
        no_chars = set(re.findall(r'[A-Z]', project_no.upper()))
        
        common_chars = name_chars.intersection(no_chars)
        
        if common_chars:
            score = min(1.0, len(common_chars) * 0.2 + 0.5)
        else:
            score = 0.5  # 默认中等关联性
        
        return {
            "consistency_score": score,
            "common_elements": list(common_chars),
            "method": "character_matching"
        }
    
    def _calculate_consistency_score(
        self,
        cross_validation_results: Dict[str, Any]
    ) -> float:
        """计算整体一致性分数"""
        scores = []
        
        for result in cross_validation_results.values():
            if isinstance(result, dict):
                if "consistency_score" in result:
                    scores.append(result["consistency_score"])
                elif "logic_score" in result:
                    scores.append(result["logic_score"])
        
        return round(sum(scores) / len(scores), 2) if scores else 0.5
    
    def _calculate_quality_score(
        self,
        validated_fields: Dict[str, Any],
        validation_reports: Dict[str, Any],
        cross_validation_result: Dict[str, Any]
    ) -> float:
        """计算整体质量分数"""
        try:
            # 字段完整性分数 (40%)
            total_fields = len(validated_fields)
            valid_fields = len([v for v in validated_fields.values() if v is not None])
            completeness_score = valid_fields / total_fields if total_fields > 0 else 0
            
            # 字段置信度分数 (40%)
            confidences = []
            for report in validation_reports.values():
                if isinstance(report, dict) and "confidence" in report:
                    confidences.append(report["confidence"])
            
            confidence_score = sum(confidences) / len(confidences) if confidences else 0
            
            # 交叉验证分数 (20%)
            cross_validation_score = cross_validation_result.get("overall_consistency", 0.5)
            
            # 加权计算最终分数
            final_score = (
                completeness_score * 0.4 +
                confidence_score * 0.4 +
                cross_validation_score * 0.2
            )
            
            return round(final_score, 2)
            
        except Exception as e:
            log.error(f"计算质量分数失败: {e}")
            return 0.5
    
    def _get_confidence_level(self, quality_score: float) -> str:
        """获取置信度等级"""
        if quality_score >= 0.9:
            return "very_high"
        elif quality_score >= 0.8:
            return "high"
        elif quality_score >= 0.7:
            return "medium_high"
        elif quality_score >= 0.6:
            return "medium"
        elif quality_score >= 0.5:
            return "medium_low"
        else:
            return "low"
    
    def _calculate_completeness(self, validated_fields: Dict[str, Any]) -> float:
        """计算完整性"""
        total = len(validated_fields)
        valid = len([v for v in validated_fields.values() if v is not None])
        return round(valid / total, 2) if total > 0 else 0.0
    
    def _calculate_reliability(self, validation_reports: Dict[str, Any]) -> float:
        """计算可靠性"""
        confidences = []
        for report in validation_reports.values():
            if isinstance(report, dict) and "confidence" in report:
                confidences.append(report["confidence"])
        
        return round(sum(confidences) / len(confidences), 2) if confidences else 0.0
