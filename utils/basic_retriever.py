#!/usr/bin/env python3
"""
基础检索器
提供多维度检索功能：关键词、语义向量、混合检索
"""

from typing import Dict, List, Any, Optional, Union
from collections import defaultdict

from utils.llm import LLM
from utils.log import log
from utils.elasticsearch_client import ElasticsearchClient


class BasicRetriever:
    """基础检索器"""
    
    def __init__(self):
        self.llm = LLM()
        self.es_client = ElasticsearchClient()
        self._initialized = False
    
    async def initialize(self):
        """初始化检索器"""
        if not self._initialized:
            await self.es_client.initialize()
            self._initialized = True
            log.info("基础检索器初始化完成")
    
    async def keyword_search(
        self,
        index_name: str,
        keywords: Union[str, List[str]],
        project_filter: Optional[str] = None,
        size: int = 10,
        min_score: float = 0.1
    ) -> List[Dict[str, Any]]:
        """关键词检索"""
        try:
            await self.initialize()
            
            # 处理关键词
            if isinstance(keywords, list):
                keyword_str = " ".join(keywords)
            else:
                keyword_str = keywords
            
            # 构建查询 - 兼容现有字段结构
            query = {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "query": keyword_str,
                                "fields": ["content^2", "source"],  # 使用现有字段名
                                "type": "best_fields",
                                "fuzziness": "AUTO",
                                "minimum_should_match": "75%"
                            }
                        }
                    ]
                }
            }

            # 添加项目过滤
            if project_filter:
                query["bool"]["filter"] = [
                    {"term": {"project_name": project_filter}}  # 使用现有字段名
                ]
            
            # 执行搜索
            results = await self.es_client.search(
                index_name=index_name,
                query=query,
                size=size,
                sort=[{"_score": {"order": "desc"}}]
            )
            
            # 处理结果
            processed_results = self._process_search_results(results, min_score)
            
            log.debug(f"关键词检索完成: '{keyword_str}', 返回{len(processed_results)}个结果")
            return processed_results
            
        except Exception as e:
            log.error(f"关键词检索失败: {e}")
            return []
    
    async def semantic_search(
        self,
        index_name: str,
        query_text: str,
        project_filter: Optional[str] = None,
        size: int = 10,
        min_score: float = 0.7
    ) -> List[Dict[str, Any]]:
        """语义向量检索"""
        try:
            await self.initialize()
            
            # 生成查询向量
            query_vector = await self.llm.get_embedding(query_text)
            
            # 构建向量查询
            query = {
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                        "params": {"query_vector": query_vector}
                    }
                }
            }
            
            # 添加项目过滤
            if project_filter:
                query = {
                    "bool": {
                        "must": [query],
                        "filter": [
                            {"term": {"metadata.project_name": project_filter}}
                        ]
                    }
                }
            
            # 执行搜索
            results = await self.es_client.search(
                index_name=index_name,
                query=query,
                size=size,
                sort=[{"_score": {"order": "desc"}}]
            )
            
            # 处理结果
            processed_results = self._process_search_results(results, min_score)
            
            log.debug(f"语义检索完成: '{query_text}', 返回{len(processed_results)}个结果")
            return processed_results
            
        except Exception as e:
            log.error(f"语义检索失败: {e}")
            return []
    
    async def hybrid_search(
        self,
        index_name: str,
        keywords: Union[str, List[str]],
        semantic_query: str,
        project_filter: Optional[str] = None,
        keyword_weight: float = 0.4,
        semantic_weight: float = 0.6,
        size: int = 10,
        min_score: float = 0.3
    ) -> List[Dict[str, Any]]:
        """混合检索（关键词 + 语义）"""
        try:
            await self.initialize()
            
            # 处理关键词
            if isinstance(keywords, list):
                keyword_str = " ".join(keywords)
            else:
                keyword_str = keywords
            
            # 生成查询向量
            query_vector = await self.llm.get_embedding(semantic_query)
            
            # 构建混合查询
            query = {
                "bool": {
                    "should": [
                        # 关键词匹配
                        {
                            "multi_match": {
                                "query": keyword_str,
                                "fields": ["chunk_text^2", "metadata.source_name"],
                                "type": "best_fields",
                                "boost": keyword_weight
                            }
                        },
                        # 语义向量匹配
                        {
                            "script_score": {
                                "query": {"match_all": {}},
                                "script": {
                                    "source": f"cosineSimilarity(params.query_vector, 'embedding') * {semantic_weight}",
                                    "params": {"query_vector": query_vector}
                                }
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }
            
            # 添加项目过滤
            if project_filter:
                query["bool"]["filter"] = [
                    {"term": {"metadata.project_name": project_filter}}
                ]
            
            # 执行搜索
            results = await self.es_client.search(
                index_name=index_name,
                query=query,
                size=size,
                sort=[{"_score": {"order": "desc"}}]
            )
            
            # 处理结果
            processed_results = self._process_search_results(results, min_score)
            
            log.debug(f"混合检索完成: 关键词='{keyword_str}', 语义='{semantic_query}', 返回{len(processed_results)}个结果")
            return processed_results
            
        except Exception as e:
            log.error(f"混合检索失败: {e}")
            return []
    
    async def multi_field_search(
        self,
        index_name: str,
        field_queries: Dict[str, Dict[str, Any]],
        project_filter: Optional[str] = None,
        size: int = 20
    ) -> Dict[str, List[Dict[str, Any]]]:
        """多字段检索"""
        try:
            await self.initialize()
            
            results = {}
            
            for field_name, field_config in field_queries.items():
                try:
                    # 获取字段检索配置
                    keywords = field_config.get("keywords", [])
                    semantic_query = field_config.get("semantic_query", "")
                    field_size = field_config.get("size", size // len(field_queries))
                    
                    # 执行混合检索
                    field_results = await self.hybrid_search(
                        index_name=index_name,
                        keywords=keywords,
                        semantic_query=semantic_query,
                        project_filter=project_filter,
                        size=field_size
                    )
                    
                    results[field_name] = field_results
                    
                except Exception as e:
                    log.error(f"字段检索失败 {field_name}: {e}")
                    results[field_name] = []
            
            log.debug(f"多字段检索完成: {len(field_queries)}个字段")
            return results
            
        except Exception as e:
            log.error(f"多字段检索失败: {e}")
            return {}
    
    async def search_with_filters(
        self,
        index_name: str,
        query: str,
        filters: Dict[str, Any],
        search_type: str = "hybrid",
        size: int = 10
    ) -> List[Dict[str, Any]]:
        """带过滤条件的检索"""
        try:
            await self.initialize()
            
            # 构建过滤条件
            filter_clauses = []
            
            for field, value in filters.items():
                if isinstance(value, list):
                    filter_clauses.append({"terms": {field: value}})
                else:
                    filter_clauses.append({"term": {field: value}})
            
            # 根据检索类型选择查询方式
            if search_type == "keyword":
                base_query = {
                    "multi_match": {
                        "query": query,
                        "fields": ["chunk_text^2", "metadata.source_name"]
                    }
                }
            elif search_type == "semantic":
                query_vector = await self.llm.get_embedding(query)
                base_query = {
                    "script_score": {
                        "query": {"match_all": {}},
                        "script": {
                            "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                            "params": {"query_vector": query_vector}
                        }
                    }
                }
            else:  # hybrid
                query_vector = await self.llm.get_embedding(query)
                base_query = {
                    "bool": {
                        "should": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["chunk_text^2"],
                                    "boost": 0.4
                                }
                            },
                            {
                                "script_score": {
                                    "query": {"match_all": {}},
                                    "script": {
                                        "source": "cosineSimilarity(params.query_vector, 'embedding') * 0.6",
                                        "params": {"query_vector": query_vector}
                                    }
                                }
                            }
                        ]
                    }
                }
            
            # 组合查询和过滤
            final_query = {
                "bool": {
                    "must": [base_query],
                    "filter": filter_clauses
                }
            }
            
            # 执行搜索
            results = await self.es_client.search(
                index_name=index_name,
                query=final_query,
                size=size
            )
            
            return self._process_search_results(results)
            
        except Exception as e:
            log.error(f"带过滤条件检索失败: {e}")
            return []
    
    def _process_search_results(
        self,
        es_results: Dict[str, Any],
        min_score: float = 0.0
    ) -> List[Dict[str, Any]]:
        """处理ES搜索结果"""
        try:
            hits = es_results.get("hits", {}).get("hits", [])
            processed_results = []
            
            for hit in hits:
                score = hit.get("_score", 0)
                
                # 过滤低分结果
                if score < min_score:
                    continue
                
                source = hit.get("_source", {})
                metadata = source.get("metadata", {})
                
                result = {
                    "score": round(score, 4),
                    "content": source.get("content", ""),  # 兼容现有字段名
                    "source": source.get("source", ""),
                    "page_num": source.get("page_num", 0),
                    "doc_id": source.get("doc_id", ""),
                    "project_name": source.get("project_name", ""),
                    "action": source.get("action", ""),
                    "year": source.get("year", 0),
                    # 从metadata中获取额外信息
                    "chunk_id": metadata.get("chunk_id", ""),
                    "char_count": metadata.get("char_count", 0),
                    "indexed_at": metadata.get("indexed_at", "")
                }
                
                processed_results.append(result)
            
            return processed_results
            
        except Exception as e:
            log.error(f"处理搜索结果失败: {e}")
            return []
    
    async def get_retriever_stats(self) -> Dict[str, Any]:
        """获取检索器统计信息"""
        try:
            es_health = await self.es_client.health_check()
            
            return {
                "retriever_info": {
                    "initialized": self._initialized,
                    "supported_search_types": ["keyword", "semantic", "hybrid", "multi_field"]
                },
                "es_health": es_health
            }
            
        except Exception as e:
            log.error(f"获取检索器统计失败: {e}")
            return {"error": str(e)}
    
    async def test_search_capabilities(self, index_name: str) -> Dict[str, Any]:
        """测试检索能力"""
        try:
            test_results = {}
            
            # 测试关键词检索
            try:
                keyword_results = await self.keyword_search(
                    index_name, "项目 投资", size=3
                )
                test_results["keyword_search"] = {
                    "status": "success",
                    "result_count": len(keyword_results)
                }
            except Exception as e:
                test_results["keyword_search"] = {
                    "status": "failed",
                    "error": str(e)
                }
            
            # 测试语义检索
            try:
                semantic_results = await self.semantic_search(
                    index_name, "项目的总投资金额", size=3
                )
                test_results["semantic_search"] = {
                    "status": "success",
                    "result_count": len(semantic_results)
                }
            except Exception as e:
                test_results["semantic_search"] = {
                    "status": "failed",
                    "error": str(e)
                }
            
            # 测试混合检索
            try:
                hybrid_results = await self.hybrid_search(
                    index_name, "投资 金额", "项目的资金投入", size=3
                )
                test_results["hybrid_search"] = {
                    "status": "success",
                    "result_count": len(hybrid_results)
                }
            except Exception as e:
                test_results["hybrid_search"] = {
                    "status": "failed",
                    "error": str(e)
                }
            
            return test_results
            
        except Exception as e:
            log.error(f"测试检索能力失败: {e}")
            return {"error": str(e)}
