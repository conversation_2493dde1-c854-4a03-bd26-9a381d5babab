
import os
from typing import Union, List, Dict
from loguru import logger
import jieba
import math
from multiprocessing import Pool, cpu_count
from collections import Counter
import ollama
import numpy as np

pwd_path = os.path.abspath(os.path.dirname(__file__))




class BM25:
    def __init__(self, corpus, tokenizer=None):
        self.corpus_size = len(corpus)
        self.avgdl = 0
        self.doc_freqs = []
        self.idf = {}
        self.doc_len = []
        self.tokenizer = tokenizer

        if tokenizer:
            corpus = self._tokenize_corpus(corpus)

        nd = self._initialize(corpus)
        self._calc_idf(nd)

    def _initialize(self, corpus):
        nd = {}  # word -> number of documents with word
        num_doc = 0
        for document in corpus:
            self.doc_len.append(len(document))
            num_doc += len(document)

            frequencies = dict(Counter(document))
            self.doc_freqs.append(frequencies)

            for word, freq in frequencies.items():
                if word not in nd:
                    nd[word] = 0
                nd[word] += 1

        self.avgdl = num_doc / self.corpus_size
        return nd

    def _tokenize_corpus(self, corpus):
        pool = Pool(cpu_count())
        tokenized_corpus = pool.map(self.tokenizer, corpus)
        return tokenized_corpus

    def _calc_idf(self, nd):
        raise NotImplementedError()

    def get_scores(self, query):
        raise NotImplementedError()

    def get_top_n(self, query, documents, n=5):

        assert self.corpus_size == len(documents), "The documents given don't match the index corpus!"

        scores = self.get_scores(query)
        top_n = np.argsort(scores)[::-1][:n]
        return [documents[i] for i in top_n]


class BM25Okapi(BM25):
    def __init__(self, corpus, tokenizer=None, k1=1.5, b=0.75, epsilon=0.25):
        self.k1 = k1
        self.b = b
        self.epsilon = epsilon
        super().__init__(corpus, tokenizer)

    def _calc_idf(self, nd):
        """
        Calculates frequencies of terms in documents and in corpus.
        This algorithm sets a floor on the idf values to eps * average_idf
        """
        # collect idf sum to calculate an average idf for epsilon value
        idf_sum = 0
        # collect words with negative idf to set them a special epsilon value.
        # idf can be negative if word is contained in more than half of documents
        negative_idfs = []
        for word, freq in nd.items():
            idf = math.log(self.corpus_size - freq + 0.5) - math.log(freq + 0.5)
            self.idf[word] = idf
            idf_sum += idf
            if idf < 0:
                negative_idfs.append(word)
        self.average_idf = idf_sum / len(self.idf)

        eps = self.epsilon * self.average_idf
        for word in negative_idfs:
            self.idf[word] = eps

    def get_scores(self, query):
        """
        The ATIRE BM25 variant uses an idf function which uses a log(idf) score. To prevent negative idf scores,
        this algorithm also adds a floor to the idf value of epsilon.
        See [Trotman, A., X. Jia, M. Crane, Towards an Efficient and Effective Search Engine] for more info
        :param query: str
        :return: array
        """
        scores = np.zeros(self.corpus_size)
        doc_len = np.array(self.doc_len)
        for q in query:
            q_freq = np.array([(doc.get(q) or 0) for doc in self.doc_freqs])
            scores += (self.idf.get(q) or 0) * (q_freq * (self.k1 + 1) /
                                               (q_freq + self.k1 * (1 - self.b + self.b * doc_len / self.avgdl)))
        return scores


class BM25L(BM25):
    def __init__(self, corpus, tokenizer=None, k1=1.5, b=0.75, delta=0.5):
        # Algorithm specific parameters
        self.k1 = k1
        self.b = b
        self.delta = delta
        super().__init__(corpus, tokenizer)

    def _calc_idf(self, nd):
        for word, freq in nd.items():
            idf = math.log(self.corpus_size + 1) - math.log(freq + 0.5)
            self.idf[word] = idf

    def get_scores(self, query):
        scores = np.zeros(self.corpus_size)
        doc_len = np.array(self.doc_len)
        for q in query:
            q_freq = np.array([(doc.get(q) or 0) for doc in self.doc_freqs])
            ctd = q_freq / (1 - self.b + self.b * doc_len / self.avgdl)
            scores += (self.idf.get(q) or 0) * q_freq * (self.k1 + 1) * (ctd + self.delta) / \
                     (self.k1 + ctd + self.delta)
        return scores


class BM25Plus(BM25):
    def __init__(self, corpus, tokenizer=None, k1=1.5, b=0.75, delta=1):
        # Algorithm specific parameters
        self.k1 = k1
        self.b = b
        self.delta = delta
        super().__init__(corpus, tokenizer)

    def _calc_idf(self, nd):
        for word, freq in nd.items():
            idf = math.log((self.corpus_size + 1) / freq)
            self.idf[word] = idf

    def get_scores(self, query):
        scores = np.zeros(self.corpus_size)
        doc_len = np.array(self.doc_len)
        for q in query:
            q_freq = np.array([(doc.get(q) or 0) for doc in self.doc_freqs])
            scores += (self.idf.get(q) or 0) * (self.delta + (q_freq * (self.k1 + 1)) /
                                               (self.k1 * (1 - self.b + self.b * doc_len / self.avgdl) + q_freq))
        return scores
    
    
def load_stopwords():
    stopwords = set()
    files = ["stopwords.txt"]
    # files = ["baidu_stopwords.txt","hit_stopwords.txt"]
    for name in files:
        file_path = os.path.join(pwd_path, "templates", name)
        if file_path and os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    stopwords.add(line)
    return stopwords



class BM25Similarity():
    """
    Compute BM25OKapi similarity between two sentences and retrieves most
    similar sentence for a given corpus.
    """

    def __init__(self, corpus: Union[List[str], Dict[int, str]] = None):
        super().__init__()
        self.corpus = {}

        self.bm25 = None
        self.default_stopwords = load_stopwords()
        if corpus is not None:
            self.add_corpus(corpus)

    def __len__(self):
        """Get length of corpus."""
        return len(self.corpus)

    def __str__(self):
        base = f"Similarity: {self.__class__.__name__}, matching_model: BM25"
        if self.corpus:
            base += f", corpus size: {len(self.corpus)}"
        return base

    def add_corpus(self, corpus: Union[List[str], Dict[int, str]]):
        """
        Extend the corpus with new documents.

        Parameters
        ----------
        corpus : dict
        """
        corpus_new = {}
        start_id = len(self.corpus) if self.corpus else 0
        if isinstance(corpus, list):
            corpus = list(set(corpus))
            for id, doc in enumerate(corpus):
                if doc not in list(self.corpus.values()):
                    corpus_new[start_id + id] = doc
        else:
            for id, doc in corpus.items():
                if doc not in list(self.corpus.values()):
                    corpus_new[id] = doc
        self.corpus.update(corpus_new)
        logger.info(f"Add corpus done, new docs: {len(corpus_new)}, all corpus size: {len(self.corpus)}")
        self.build_bm25()

    def build_bm25(self):
        """build bm25 model."""
        corpus_texts = list(self.corpus.values())
        corpus_seg = [jieba.lcut(d) for d in corpus_texts]
        corpus_seg = [[w for w in doc if (w.strip().lower() not in self.default_stopwords) and
                       len(w.strip()) > 0] for doc in corpus_seg]
        self.bm25 = BM25Okapi(corpus_seg)
        logger.info(f"Total corpus: {len(self.corpus)}")


    def most_similar(self, query: str, topn=10):
        """
        Find the topn most similar texts to the query against the corpus.
        :param query: input query
        :param topn: int
        :return: List[Dict[str, float]], [{'corpus_id': corpus_id, 'score': similarity_score}, ...]
        """
        if not self.corpus:
            raise ValueError("corpus is None. Please add_corpus first, eg. `add_corpus(corpus)`")
        if not self.bm25:
            self.build_bm25()

        tokens = jieba.lcut(query)
        scores = self.bm25.get_scores(tokens)

        result = [{'corpus_id': corpus_id, 'score': score} for corpus_id, score in enumerate(scores)]
        result = sorted(result, key=lambda x: x['score'], reverse=True)[:topn]
        return result
    
    def search(self, query: str, topn: int = 10):
        topns = []
        res =  self.most_similar(query, topn=topn)
        for r in res:
            topns.append({self.corpus[r['corpus_id']]:r['score']})
        return topns

    def encode(self, text) -> np.ndarray:
        # 使用 ollama.embeddings 接口生成嵌入
        response = ollama.embeddings(prompt=text, model="xbv2")
        embedding = np.array(response['embedding'])
        return embedding
    
    def ncosine(self, a, b):
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
    
    def rerank(self, query: str, documents: List[str], top_k: int = None) -> List[str]:
        if not documents:
            return []

        try:
            # 计算BM25分数
            bm25_scores = self.search(query, topn=len(documents))
            bm25_scores = [list(score.values())[0] for score in bm25_scores]

            # 计算嵌入相似度
            query_embedding = self.encode(query)
            similarities = []
            for doc in documents:
                doc_embedding = self.encode(doc)
                similarity = self.ncosine(query_embedding, doc_embedding)
                similarities.append(similarity)

            # 归一化分数
            bm25_scores = np.array(bm25_scores)
            bm25_scores = (bm25_scores - bm25_scores.min()) / (bm25_scores.max() - bm25_scores.min())
            similarities = np.array(similarities)
            similarities = (similarities - similarities.min()) / (similarities.max() - similarities.min())

            # 结合两种分数
            combined_scores = [(doc, 0.5*sim + (1 - 0.5) * bm25)
                               for doc, sim, bm25 in zip(documents, similarities, bm25_scores)]

            # 根据组合分数对文档进行排序
            reranked = sorted(combined_scores, key=lambda x: x[1], reverse=True)
            
            # 提取排序后的文档
            reranked_docs = [doc for doc, _ in reranked]
            
            # 如果指定了top_k，则只返回前top_k个文档
            if top_k is not None:
                reranked_docs = reranked_docs[:top_k]
            
            return reranked_docs

        except Exception as e:
            logger.error(f"重新排序过程中发生错误: {e}")
            return documents  # 如果出错，返回原始文档列表



def test_bm25():
    """test_bm25"""
    text1 = '刘若英是个演员'
    text2 = '他唱歌很好听'
    m = BM25Similarity()
    zh_list = ['刘若英是个演员', '他唱歌很好听', 'women喜欢这首歌', '我不是演员吗']
    m.add_corpus(zh_list)
    res = m.most_similar('刘若英是演员', topn=10)
    print(res)
    for r in res:
        id = r['corpus_id']
        score = r['score']
        print(f'\t{m.corpus[id]}: {score:.4f}')
    print('-' * 50 + '\n')
    res = m.most_similar('唱歌很好听')
    print(res)
    for r in res:
        id = r['corpus_id']
        score = r['score']
        print(f'\t{m.corpus[id]}: {score:.4f}')
    print('-' * 50 + '\n')
    
    
def test_bm25_hardcase():
    """test_bm25 hardcase"""
    m = BM25Similarity()
    zh_list = [
        "开题报告",
        "项目计划",
        "BGE M3 is an embedding model supporting dense retrieval, lexical matching and multi-vector interaction.",
        "BM25 is a bag-of-words retrieval function that ranks a set of documents based on the query terms appearing in each document"
    ]
    m.add_corpus(zh_list)
    
    q = ['开题报告', '项目计划']
    for i in q:
        res = m.most_similar(i, topn=10)
        print('sim search: ', res)
        print('query:', i)        
        print("search res:")
        for r in res:
            id = r['corpus_id']
            score = r['score']
            print(f'\t{m.corpus[id]}: {score:.4f}')
        print('-' * 50 + '\n')
    topns = m.search("计划")
    for n in topns:
        print(f'{n}\n')  


if __name__ == '__main__':
    test_bm25()
    test_bm25_hardcase()
    m = BM25Similarity()
    query = "刘若英是演员"
    documents = ['刘若英是个演员', '他唱歌很好听', 'women喜欢这首歌', '我不是演员吗']
    m.add_corpus(documents)
    reranked = m.rerank(query, documents, top_k=3)
    print("\nReranked documents:")
    for doc in reranked:
        print(doc)