#!/usr/bin/env python3
"""
简化的文件加载器
不依赖 langchain，提供基本的文件加载功能
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from utils.logger import log


class SimpleFileLoader:
    """简化的文件加载器"""
    
    def __init__(self):
        pass
    
    def __call__(self, file_path: str) -> List[Dict[str, Any]]:
        """
        加载文件并返回页面数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            页面数据列表，格式: [{"page_num": 1, "content": "文本内容"}]
        """
        try:
            if not os.path.exists(file_path):
                log.error(f"文件不存在: {file_path}")
                return []
            
            # 检查是否是JSON文件
            if file_path.lower().endswith('.json'):
                return self._load_json_file(file_path)
            
            # 对于其他文件类型，返回空列表（因为我们主要处理JSON文件）
            log.warning(f"不支持的文件类型: {file_path}")
            return []
            
        except Exception as e:
            log.error(f"加载文件失败 {file_path}: {e}")
            return []
    
    def _load_json_file(self, file_path: str) -> List[Dict[str, Any]]:
        """加载JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 如果是标准的文档JSON格式
            if isinstance(data, dict) and 'pages' in data:
                return data['pages']
            
            # 如果直接是页面列表
            if isinstance(data, list):
                return data
            
            # 如果是其他格式，尝试转换
            if isinstance(data, dict):
                return [{"page_num": 1, "content": str(data)}]
            
            return [{"page_num": 1, "content": str(data)}]
            
        except json.JSONDecodeError as e:
            log.error(f"JSON文件格式错误 {file_path}: {e}")
            return []
        except Exception as e:
            log.error(f"读取JSON文件失败 {file_path}: {e}")
            return []


# 为了兼容原有代码，创建一个FileLoader别名
FileLoader = SimpleFileLoader


def load_file_content(file_path: str) -> str:
    """
    加载文件内容并返回文本
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件的文本内容
    """
    loader = SimpleFileLoader()
    pages = loader(file_path)
    
    if not pages:
        return ""
    
    # 合并所有页面的内容
    content_parts = []
    for page in pages:
        if isinstance(page, dict) and 'content' in page:
            content_parts.append(page['content'])
        elif isinstance(page, str):
            content_parts.append(page)
        else:
            content_parts.append(str(page))
    
    return "\n".join(content_parts)


def extract_pages_content(pages: List[Dict[str, Any]]) -> str:
    """
    从页面数据中提取文本内容
    
    Args:
        pages: 页面数据列表
        
    Returns:
        合并后的文本内容
    """
    if not pages:
        return ""
    
    content_parts = []
    for page in pages:
        if isinstance(page, dict):
            # 尝试不同的内容字段名
            content = (
                page.get('content') or 
                page.get('text') or 
                page.get('page_content') or 
                str(page)
            )
            content_parts.append(str(content))
        else:
            content_parts.append(str(page))
    
    return "\n".join(content_parts)


class MockDocument:
    """模拟langchain的Document类"""
    
    def __init__(self, page_content: str, metadata: Optional[Dict[str, Any]] = None):
        self.page_content = page_content
        self.metadata = metadata or {}
    
    def __str__(self):
        return f"Document(page_content='{self.page_content[:100]}...', metadata={self.metadata})"
    
    def __repr__(self):
        return self.__str__()


def create_mock_documents(pages: List[Dict[str, Any]]) -> List[MockDocument]:
    """
    创建模拟的Document对象列表
    
    Args:
        pages: 页面数据列表
        
    Returns:
        MockDocument对象列表
    """
    documents = []
    
    for i, page in enumerate(pages):
        if isinstance(page, dict):
            content = (
                page.get('content') or 
                page.get('text') or 
                page.get('page_content') or 
                str(page)
            )
            
            metadata = {
                'page_number': page.get('page_num', i + 1),
                'source': page.get('source', 'unknown')
            }
            
            # 添加其他元数据
            for key, value in page.items():
                if key not in ['content', 'text', 'page_content']:
                    metadata[key] = value
            
            documents.append(MockDocument(str(content), metadata))
        else:
            documents.append(MockDocument(str(page), {'page_number': i + 1}))
    
    return documents


if __name__ == "__main__":
    # 测试代码
    test_data = {
        "doc_id": "test_doc_001",
        "source": "test.pdf",
        "pages": [
            {"page_num": 1, "content": "这是第一页的内容。"},
            {"page_num": 2, "content": "这是第二页的内容。"},
            {"page_num": 3, "content": "这是第三页的内容。"}
        ]
    }
    
    # 创建测试JSON文件
    test_file = "test_document.json"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    # 测试加载器
    loader = SimpleFileLoader()
    pages = loader(test_file)
    
    print(f"加载了 {len(pages)} 页:")
    for page in pages:
        print(f"页面 {page.get('page_num', '?')}: {page.get('content', '')}")
    
    # 测试内容提取
    content = extract_pages_content(pages)
    print(f"\n合并内容:\n{content}")
    
    # 测试模拟文档创建
    documents = create_mock_documents(pages)
    print(f"\n创建了 {len(documents)} 个文档对象:")
    for doc in documents:
        print(doc)
    
    # 清理测试文件
    os.remove(test_file)
