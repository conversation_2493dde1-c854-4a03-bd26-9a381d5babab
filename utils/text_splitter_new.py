#!/usr/bin/env python3
"""
新的文本分割器
用于将长文本分割成较小的块
"""

import re
from typing import List


def split_text2(text: str, chunk_size: int = 1000, overlap: int = 100) -> List[str]:
    """
    将文本分割成指定大小的块
    """
    if not text or chunk_size <= 0:
        return []
    
    if len(text) <= chunk_size:
        return [text]
    
    chunks = []
    start = 0
    
    while start < len(text):
        end = start + chunk_size
        
        if end < len(text):
            sentence_end = -1
            for i in range(end, max(start + chunk_size // 2, start), -1):
                if text[i] in '。！？；.!?;':
                    sentence_end = i + 1
                    break
            
            if sentence_end > start:
                end = sentence_end
        
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        
        start = max(start + 1, end - overlap)
        
        if start >= len(text):
            break
    
    return chunks


class SimpleTextSplitter:
    """简单文本分割器类"""
    
    def __init__(self, chunk_size: int = 1000, overlap: int = 100):
        self.chunk_size = chunk_size
        self.overlap = overlap
    
    def split_text(self, text: str) -> List[str]:
        """分割文本"""
        return split_text2(text, self.chunk_size, self.overlap)


def split_text_simple(text: str, max_length: int = 1000) -> List[str]:
    """简单的文本分割函数"""
    splitter = SimpleTextSplitter(chunk_size=max_length, overlap=100)
    return splitter.split_text(text)


def split_chinese_text(text: str, max_length: int = 500) -> List[str]:
    """专门用于中文文本的分割函数"""
    if not text:
        return []
    
    # 简单的中文句子分割
    text = re.sub(r'([。！？；.!?;])([^\s])', r'\1\n\2', text)
    
    # 按行分割
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    
    chunks = []
    current_chunk = ""
    
    for line in lines:
        if len(current_chunk) + len(line) <= max_length:
            if current_chunk:
                current_chunk += line
            else:
                current_chunk = line
        else:
            if current_chunk:
                chunks.append(current_chunk)
            current_chunk = line
    
    if current_chunk:
        chunks.append(current_chunk)
    
    return chunks
