from typing import List, Dict, Any, Optional
import httpx
import json
from functools import wraps
import asyncio
import time
import re
from utils.config import config

def retry_with_exponential_backoff(
    max_retries: int = 3,
    base_delay: float = 1,
    max_delay: float = 10,
    exponential_base: float = 2,
    errors: tuple = (Exception,)
):
    """指数退避重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            retries = 0
            delay = base_delay

            while retries < max_retries:
                try:
                    return await func(*args, **kwargs)
                except errors as e:
                    retries += 1
                    if retries == max_retries:
                        print(f"Max retries ({max_retries}) reached for {func.__name__}. Last error: {str(e)}")
                        raise

                    # 计算下一次重试的延迟时间
                    delay = min(delay * exponential_base, max_delay)
                    print(f"Retry {retries}/{max_retries} for {func.__name__} after {delay:.1f}s. Error: {str(e)}")
                    await asyncio.sleep(delay)

            return await func(*args, **kwargs)
        return wrapper
    return decorator

def count_tokens(text: str) -> int:
    """简单的token计数方法
    
    基于以下规则粗略计算token数：
    1. 中文字符算1个token
    2. 英文单词算1个token
    3. 数字序列算1个token
    4. 标点符号算1个token
    """
    if not text:
        return 0
        
    # 分离中文字符
    chinese = re.findall(r'[\u4e00-\u9fff]', text)
    
    # 分离英文单词
    english = re.findall(r'[a-zA-Z]+', text)
    
    # 分离数字序列
    numbers = re.findall(r'\d+', text)
    
    # 分离标点符号
    punctuation = re.findall(r'[^\w\s\u4e00-\u9fff]', text)
    
    # 计算总token数
    return len(chinese) + len(english) + len(numbers) + len(punctuation)

def retry_with_exponential_backoff(
    max_retries: int = 3,
    base_delay: float = 1,
    max_delay: float = 10,
    exponential_base: float = 2,
    errors: tuple = (Exception,)
):
    """指数退避重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            retries = 0
            delay = base_delay
            
            while retries < max_retries:
                try:
                    return await func(*args, **kwargs)
                except errors as e:
                    retries += 1
                    if retries == max_retries:
                        print(f"Max retries ({max_retries}) reached. Last error: {str(e)}")
                        raise
                        
                    delay = min(delay * exponential_base, max_delay)
                    print(f"Retry {retries}/{max_retries} after {delay:.1f}s. Error: {str(e)}")
                    await asyncio.sleep(delay)
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

class LLM:
    """LLM服务客户端"""
    
    def __init__(
        self,
        api_url: str = config.get("llm.api_url"),
        token: str = config.get("llm.token"),
        model: str = config.get("llm.model"),
        embeding: str = config.get("llm.embeding"),
        temperature: float = config.get('llm.temperature'),
        max_tokens: int = config.get('llm.max_tokens'),
        max_context_length: int = config.get('llm.max_tokens')
    ):
        """初始化LLM客户端
        
        优先使用配置文件中的值，如果配置文件中没有相应配置，则使用传入的参数，
        如果参数也没有传入，最后才使用默认值。
        """
        # API URL和Token
        self.api_url =  api_url
        self.token =  token
        
        # 模型配置
        self.model =  model
        self.embeding = embeding
        self.temperature =  temperature
        self.max_tokens =  max_tokens
        self.max_context_length =  max_context_length
        
        # 设置请求头
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }

        # 添加限流控制
        self._last_request_time = 0
        self._min_request_interval = 0.1  # 最小请求间隔（秒）
        self._request_count = 0
        self._error_count = 0

    async def _rate_limit(self):
        """限流控制"""
        current_time = time.time()
        time_since_last_request = current_time - self._last_request_time

        if time_since_last_request < self._min_request_interval:
            sleep_time = self._min_request_interval - time_since_last_request
            await asyncio.sleep(sleep_time)

        self._last_request_time = time.time()
        self._request_count += 1

    def truncate_text(self, text: str, max_tokens: int) -> str:
        """截断文本到指定token数量"""
        if not text:
            return text
            
        # 按句子分割文本
        sentences = re.split(r'([。！？.!?])', text)
        sentences = [''.join(i) for i in zip(sentences[0::2], sentences[1::2] + [''])]
        
        result = []
        current_tokens = 0
        
        for sentence in sentences:
            sentence_tokens = count_tokens(sentence)
            if current_tokens + sentence_tokens <= max_tokens:
                result.append(sentence)
                current_tokens += sentence_tokens
            else:
                break
                
        return ''.join(result)
        
    def prepare_messages(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """准备消息，确保不超过上下文长度限制"""
        # 计算系统消息和用户最新消息的token数
        system_msg = next((msg for msg in messages if msg["role"] == "system"), None)
        user_msg = next((msg for msg in reversed(messages) if msg["role"] == "user"), None)
        
        system_tokens = count_tokens(system_msg["content"]) if system_msg else 0
        user_tokens = count_tokens(user_msg["content"]) if user_msg else 0
        
        # 保留的token数（为了响应留出空间）
        reserved_tokens = 1000
        
        # 计算历史消息可用的token数
        available_tokens = self.max_context_length - system_tokens - user_tokens - reserved_tokens
        
        if available_tokens <= 0:
            # 如果空间不够，只保留系统消息和截断后的用户消息
            truncated_user_content = self.truncate_text(
                user_msg["content"],
                self.max_context_length - system_tokens - reserved_tokens
            )
            return [
                msg for msg in messages 
                if msg["role"] == "system"
            ] + [{"role": "user", "content": truncated_user_content}]
        
        # 如果空间足够，保留所有消息，但可能需要截断较早的消息
        prepared_messages = []
        current_tokens = system_tokens + user_tokens
        
        # 首先添加系统消息（如果有）
        if system_msg:
            prepared_messages.append(system_msg)
        
        # 添加用户最新消息
        if user_msg:
            prepared_messages.append(user_msg)
        
        # 处理其他消息（从最新到最旧）
        other_messages = [
            msg for msg in reversed(messages) 
            if msg != system_msg and msg != user_msg
        ]
        
        for msg in other_messages:
            msg_tokens = count_tokens(msg["content"])
            if current_tokens + msg_tokens <= self.max_context_length - reserved_tokens:
                prepared_messages.insert(1, msg)  # 插入到系统消息之后，最新消息之前
                current_tokens += msg_tokens
            else:
                # 如果这条消息太长，尝试截断
                truncated_content = self.truncate_text(
                    msg["content"],
                    self.max_context_length - current_tokens - reserved_tokens
                )
                if truncated_content:
                    prepared_messages.insert(1, {
                        "role": msg["role"],
                        "content": truncated_content
                    })
                break  # 空间已满，停止添加更多消息
        
        return prepared_messages
        
    @retry_with_exponential_backoff(
        max_retries=3,
        base_delay=1,
        max_delay=10,
        errors=(httpx.TimeoutException, httpx.HTTPError)
    )
    async def chat(
        self,
        messages: List[Dict[str, str]],
        stream: bool = False
    ) -> str:
        """调用LLM聊天接口
        
        Args:
            messages: 对话历史
            stream: 是否使用流式响应
            
        Returns:
            str: LLM的响应文本
        """
        # 应用限流
        await self._rate_limit()

        try:
            request_data = {
                "model": self.model,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "stream": stream
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_url}/v1/chat/completions",
                    headers=self.headers,
                    json=request_data,
                    timeout=600.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if stream:
                        return data
                    return data["choices"][0]["message"]["content"].strip()
                else:
                    error_msg = f"LLM API call failed with status code {response.status_code}"
                    try:
                        error_data = response.json()
                        if "error" in error_data:
                            error_msg += f": {error_data['error']}"
                    except:
                        error_msg += f": {response.text[:200]}..."  # 限制错误信息长度

                    # 记录详细错误信息和统计
                    self._error_count += 1
                    print(f"Error calling LLM API: {error_msg} (Error count: {self._error_count})")
                    raise Exception(error_msg)

        except Exception as e:
            self._error_count += 1
            error_msg = f"Error calling LLM API: {str(e)} (Error count: {self._error_count})"
            print(error_msg)
            raise Exception(error_msg)

    @retry_with_exponential_backoff(
        max_retries=5,
        base_delay=2,
        max_delay=20,
        errors=(httpx.TimeoutException, httpx.HTTPError, Exception)
    )
    async def get_embedding(self, text: str) -> List[float]:
        """获取文本的嵌入向量
        
        Args:
            text: 输入文本
            
        Returns:
            List[float]: 嵌入向量
        """
        try:
            # 如果文本太长，进行截断
            max_length = 512
            if len(text) > max_length:
                text = text[:max_length]
            
            request_data = {
                "model": self.embeding,
                "prompt": text
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_url}/api/embeddings",
                    headers=self.headers,
                    json=request_data,
                    timeout=300.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if (
                        "data" in data 
                        and isinstance(data["data"], list) 
                        and len(data["data"]) > 0 
                        and "embedding" in data["data"][0]
                    ):
                        embedding = data["data"][0]["embedding"]
                        if len(embedding) != 1024:
                            raise Exception(f"Unexpected embedding dimension: {len(embedding)}")
                        return embedding
                    else:
                        raise Exception("Invalid response format from embeddings API")
                else:
                    error_msg = f"Embeddings API call failed with status code {response.status_code}"
                    try:
                        error_data = response.json()
                        if "error" in error_data:
                            error_msg += f": {error_data['error']}"
                    except:
                        error_msg += f": {response.text}"
                    raise Exception(error_msg)
                    
        except Exception as e:
            raise Exception(f"Error getting embedding: {str(e)}")

    @retry_with_exponential_backoff(
        max_retries=3,
        base_delay=1,
        max_delay=10,
        errors=(httpx.TimeoutException, httpx.HTTPError)
    )
    async def ocr_image(self, img_data: bytes) -> Dict[str, Any]:
        """OCR识别图像
        
        Args:
            img_data: 图像二进制数据
            
        Returns:
            Dict[str, Any]: OCR识别结果
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_url}/ocr/binary/",
                    params={
                        "wait": "true",
                        "timeout": "300",
                        "enable_seal_hw": "false"
                    },
                    headers={
                        "Authorization": f"Bearer {self.token}",
                        "accept": "application/json",
                        "Content-Type": "application/octet-stream"
                    },
                    content=img_data,
                    timeout=300.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result["status"] == "completed":
                        return result["result"]
                    else:
                        raise Exception(f"OCR failed: {result.get('error')}")
                else:
                    raise Exception(f"OCR request failed with status {response.status_code}: {response.text}")
                    
        except Exception as e:
            raise Exception(f"Error calling OCR service: {str(e)}") 