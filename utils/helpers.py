"""
用户和部门服务工具 - 提供便捷的开发函数
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, and_, or_
from auth.models import User, Department
import logging

logger = logging.getLogger(__name__)

class UserService:
    """用户服务"""
    
    @staticmethod
    async def get_user_by_id(db: AsyncSession, user_id: int) -> Optional[User]:
        """根据用户ID获取用户"""
        result = await db.execute(select(User).where(User.user_id == user_id))
        return result.scalars().first()
    
    @staticmethod
    async def get_user_by_username(db: AsyncSession, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        result = await db.execute(select(User).where(User.user_name == username))
        return result.scalars().first()
    
    @staticmethod
    async def get_users_by_department(db: AsyncSession, dept_id: int) -> List[User]:
        """获取部门下的所有用户"""
        result = await db.execute(
            select(User).where(
                and_(User.dept_id == dept_id, User.status == '0', User.del_flag == '0')
            )
        )
        return result.scalars().all()
    
    @staticmethod
    async def get_synced_users(db: AsyncSession, limit: int = 100) -> List[User]:
        """获取同步用户列表"""
        result = await db.execute(
            select(User).where(User.is_synced == True).limit(limit)
        )
        return result.scalars().all()
    
    @staticmethod
    async def create_local_user(db: AsyncSession, user_data: Dict[str, Any]) -> User:
        """创建本地用户"""
        # 生成本地用户ID（从10000开始避免与同步用户冲突）
        result = await db.execute(text("SELECT MAX(user_id) FROM users WHERE user_id >= 10000"))
        max_id = result.scalar() or 9999
        
        user = User(
            user_id=max_id + 1,
            user_name=user_data['username'],
            nick_name=user_data.get('nick_name', user_data['username']),
            email=user_data['email'],
            password=user_data['hashed_password'],
            dept_id=user_data.get('dept_id'),
            phonenumber=user_data.get('phone', ''),
            status='0',
            del_flag='0',
            create_by='system',
            update_by='system'
        )
        
        db.add(user)
        await db.commit()
        await db.refresh(user)
        return user

class DepartmentService:
    """部门服务"""
    
    @staticmethod
    async def get_department_by_id(db: AsyncSession, dept_id: int) -> Optional[Department]:
        """根据部门ID获取部门"""
        result = await db.execute(select(Department).where(Department.dept_id == dept_id))
        return result.scalars().first()
    
    @staticmethod
    async def get_department_hierarchy(db: AsyncSession, dept_id: int) -> List[Department]:
        """获取部门层级结构"""
        dept = await DepartmentService.get_department_by_id(db, dept_id)
        if not dept or not dept.ancestors:
            return [dept] if dept else []
        
        # 解析祖级列表
        ancestor_ids = [int(id) for id in dept.ancestors.split(',') if id != '0']
        
        if not ancestor_ids:
            return [dept]
        
        # 获取所有祖级部门
        result = await db.execute(
            select(Department).where(Department.dept_id.in_(ancestor_ids))
        )
        ancestors = result.scalars().all()
        
        # 按层级排序
        sorted_ancestors = []
        for ancestor_id in ancestor_ids:
            for ancestor in ancestors:
                if ancestor.dept_id == ancestor_id:
                    sorted_ancestors.append(ancestor)
                    break
        
        sorted_ancestors.append(dept)
        return sorted_ancestors
    
    @staticmethod
    async def get_department_children(db: AsyncSession, dept_id: int) -> List[Department]:
        """获取子部门列表"""
        result = await db.execute(
            select(Department).where(
                and_(
                    Department.parent_id == dept_id,
                    Department.status == '0',
                    Department.del_flag == '0'
                )
            )
        )
        return result.scalars().all()
    
    @staticmethod
    async def get_department_admin(db: AsyncSession, dept_id: int) -> Optional[User]:
        """获取部门管理员"""
        # 先查找部门负责人
        dept = await DepartmentService.get_department_by_id(db, dept_id)
        if dept and dept.leader:
            result = await db.execute(
                select(User).where(User.user_name == dept.leader)
            )
            admin = result.scalars().first()
            if admin:
                return admin
        
        # 查找部门中的管理员用户
        result = await db.execute(
            select(User).where(
                and_(
                    User.dept_id == dept_id,
                    User.user_type.in_(['01', '02']),  # 管理员类型
                    User.status == '0',
                    User.del_flag == '0'
                )
            )
        )
        return result.scalars().first()
    
    @staticmethod
    async def get_synced_departments(db: AsyncSession) -> List[Department]:
        """获取同步部门列表"""
        result = await db.execute(
            select(Department).where(Department.del_flag == '0')
        )
        return result.scalars().all()

class QueryService:
    """查询服务"""
    
    @staticmethod
    async def get_user_with_department(db: AsyncSession, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户及其部门信息"""
        result = await db.execute(text("""
            SELECT
                u.user_id, u.user_name, u.nick_name, u.email, u.phonenumber,
                u.status, u.del_flag,
                d.dept_id, d.dept_name, d.leader, d.ancestors
            FROM users u
            LEFT JOIN departments d ON u.dept_id = d.dept_id
            WHERE u.user_id = :user_id
        """), {"user_id": user_id})
        
        row = result.fetchone()
        if not row:
            return None
        
        return {
            "user": {
                "user_id": row[0],
                "user_name": row[1],
                "nick_name": row[2],
                "email": row[3],
                "phonenumber": row[4],
                "status": row[5],
                "del_flag": row[6]
            },
            "department": {
                "dept_id": row[7],
                "dept_name": row[8],
                "leader": row[9],
                "ancestors": row[10]
            } if row[7] else None
        }
    
    @staticmethod
    async def search_users(db: AsyncSession, keyword: str, dept_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """搜索用户"""
        conditions = [
            User.status == '0',
            User.del_flag == '0',
            or_(
                User.user_name.like(f"%{keyword}%"),
                User.nick_name.like(f"%{keyword}%"),
                User.email.like(f"%{keyword}%")
            )
        ]
        
        if dept_id:
            conditions.append(User.dept_id == dept_id)
        
        result = await db.execute(
            select(User).where(and_(*conditions)).limit(50)
        )
        users = result.scalars().all()
        
        # 获取用户的部门信息
        user_list = []
        for user in users:
            dept = None
            if user.dept_id:
                dept = await DepartmentService.get_department_by_id(db, user.dept_id)
            
            user_list.append({
                "user_id": user.user_id,
                "user_name": user.user_name,
                "nick_name": user.nick_name,
                "email": user.email,
                "department": dept.dept_name if dept else None,
                "is_synced": user.is_synced
            })
        
        return user_list
    
    @staticmethod
    async def get_department_tree(db: AsyncSession) -> List[Dict[str, Any]]:
        """获取部门树结构"""
        result = await db.execute(
            select(Department).where(
                and_(Department.status == '0', Department.del_flag == '0')
            ).order_by(Department.order_num, Department.dept_id)
        )
        departments = result.scalars().all()
        
        # 构建树结构
        dept_dict = {dept.dept_id: {
            "dept_id": dept.dept_id,
            "dept_name": dept.dept_name,
            "parent_id": dept.parent_id,
            "leader": dept.leader,
            "is_synced": dept.is_synced,
            "children": []
        } for dept in departments}
        
        # 构建父子关系
        root_depts = []
        for dept in departments:
            if dept.parent_id == 0 or dept.parent_id not in dept_dict:
                root_depts.append(dept_dict[dept.dept_id])
            else:
                dept_dict[dept.parent_id]["children"].append(dept_dict[dept.dept_id])
        
        return root_depts

# 便捷函数
async def get_user_department_path(db: AsyncSession, user_id: int) -> str:
    """获取用户的部门完整路径"""
    user = await UserService.get_user_by_id(db, user_id)
    if not user or not user.dept_id:
        return "无部门"

    hierarchy = await DepartmentService.get_department_hierarchy(db, user.dept_id)
    return " > ".join([dept.dept_name for dept in hierarchy])

async def check_user_department_permission(db: AsyncSession, user_id: int, target_dept_id: int) -> bool:
    """检查用户是否有目标部门的权限"""
    user = await UserService.get_user_by_id(db, user_id)
    if not user:
        return False

    # 如果是同一部门
    if user.dept_id == target_dept_id:
        return True

    # 检查是否是上级部门
    target_dept = await DepartmentService.get_department_by_id(db, target_dept_id)
    if target_dept and target_dept.ancestors:
        ancestor_ids = [int(id) for id in target_dept.ancestors.split(',') if id != '0']
        return user.dept_id in ancestor_ids
    
    return False
