from typing import Dict, Any, Optional, List
from enum import Enum
import asyncio
from datetime import datetime
import json

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

class TaskResult:
    def __init__(self, task_id: str, total_files: int = 0):
        self.task_id = task_id
        self.status = TaskStatus.PENDING
        self.progress = 0
        self.processed_files: List[Dict[str, Any]] = []
        self.failed_files: List[Dict[str, Any]] = []
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.error = None
        self.total_files = total_files
        # 新增：文件级别的进度跟踪
        self.file_progress: Dict[str, Dict[str, Any]] = {}  # 文件URL -> 进度信息
        self.task_type = None  # 任务类型：assembly, txt_info等
        self.task_params = {}  # 任务参数

        # HNGPT Agent工作流支持
        self.workflow_id = None  # 工作流ID
        self.agent_progress: Dict[str, Dict[str, Any]] = {}  # Agent ID -> 进度信息
        self.workflow_status = None  # 工作流状态
        self.execution_levels = []  # 执行层级信息

        # 调试日志
        print(f"🔍 TaskResult created: task_id={task_id}, total_files={total_files}")

    def to_dict(self) -> Dict[str, Any]:
        return {
            "task_id": self.task_id,
            "status": self.status.value,
            "progress": self.progress,
            "processed_files": self.processed_files,
            "failed_files": self.failed_files,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "error": self.error,
            "total_files": self.total_files,
            "file_progress": self.file_progress,
            "task_type": self.task_type,
            "task_params": self.task_params,
            # HNGPT Agent工作流信息
            "workflow_id": self.workflow_id,
            "agent_progress": self.agent_progress,
            "workflow_status": self.workflow_status,
            "execution_levels": self.execution_levels
        }

    def update_progress(self):
        """更新进度"""
        if self.total_files > 0:  # 防止除零错误
            processed = len(self.processed_files) + len(self.failed_files)
            # 确保进度在0-100之间
            self.progress = min(100, max(0, int((processed / self.total_files) * 100)))
            print(f"Task {self.task_id} progress: {self.progress}% ({processed}/{self.total_files})")
        else:
            # 当total_files为0时，根据任务状态设置进度
            if self.status == TaskStatus.COMPLETED:
                self.progress = 100
            elif self.status == TaskStatus.FAILED:
                self.progress = 0
            elif self.status == TaskStatus.RUNNING:
                # 如果有文件进度信息，基于文件进度计算
                if self.file_progress:
                    total_files = len(self.file_progress)
                    completed_files = sum(1 for fp in self.file_progress.values()
                                        if fp.get('status') == 'completed')
                    self.progress = int((completed_files / total_files) * 100) if total_files > 0 else 0
                else:
                    self.progress = 0
            else:
                self.progress = 0
        self.updated_at = datetime.now()

    def get_progress(self) -> int:
        """获取当前进度（0-100）"""
        if self.total_files > 0:
            processed = len(self.processed_files) + len(self.failed_files)
            return min(100, max(0, int((processed / self.total_files) * 100)))
        else:
            # 当total_files为0时，根据任务状态和文件进度计算
            if self.status == TaskStatus.COMPLETED:
                return 100
            elif self.status == TaskStatus.FAILED:
                return 0
            elif self.file_progress:
                total_files = len(self.file_progress)
                completed_files = sum(1 for fp in self.file_progress.values()
                                    if fp.get('status') == 'completed')
                return int((completed_files / total_files) * 100) if total_files > 0 else 0
            else:
                return 0

class TaskManager:
    def __init__(self):
        self.tasks: Dict[str, asyncio.Task] = {}
        self.results: Dict[str, TaskResult] = {}
        self._lock = asyncio.Lock()
        # 添加一个标志来控制是否立即清理完成的任务
        self.auto_cleanup_completed = False

    async def create_task(
        self,
        task_id: str,
        coro,
        total_files: int = 0,
        timeout: Optional[float] = None,
        task_type: str = "assembly",
        task_params: Dict[str, Any] = None
    ) -> TaskResult:
        """创建新任务"""
        async with self._lock:
            # 创建任务结果对象
            result = TaskResult(task_id, total_files)

            # 设置任务类型和参数
            result.task_type = task_type
            result.task_params = task_params or {}

            self.results[task_id] = result

            # 创建异步任务
            task = asyncio.create_task(coro)
            self.tasks[task_id] = task
            
            # 如果设置了超时，添加超时处理
            if timeout:
                try:
                    await asyncio.wait_for(task, timeout=timeout)
                except asyncio.TimeoutError:
                    result.status = TaskStatus.FAILED
                    result.error = "Task timeout"
                    
            return result

    async def get_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务结果（不会清理任务）"""
        result = self.results.get(task_id)
        if not result:
            return None

        task = self.tasks.get(task_id)
        if task and task.done():
            # 任务完成，清理任务对象但保留结果
            self.tasks.pop(task_id, None)

        return result.to_dict()

    async def get_result_and_cleanup(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务结果并清理（原来的行为）"""
        result = self.results.get(task_id)
        if not result:
            return None

        task = self.tasks.get(task_id)
        if not task:
            return result.to_dict()

        if task.done():
            # 任务完成，清理资源
            self.tasks.pop(task_id, None)
            result = self.results.pop(task_id, None)
            return result.to_dict() if result else None
        else:
            # 任务仍在进行中，返回当前状态
            return result.to_dict()

    async def update_result(
        self,
        task_id: str,
        status: Optional[TaskStatus] = None,
        processed_file: Optional[Dict[str, Any]] = None,
        failed_file: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None,
        total_files: Optional[int] = None,
        file_progress: Optional[Dict[str, Dict[str, Any]]] = None,
        task_type: Optional[str] = None,
        task_params: Optional[Dict[str, Any]] = None
    ):
        """更新任务结果"""
        result = self.results.get(task_id)
        if result:
            # 调试日志：记录total_files变化
            old_total_files = result.total_files

            if status:
                result.status = status
            if processed_file:
                result.processed_files.append(processed_file)
            if failed_file:
                result.failed_files.append(failed_file)
            if error:
                result.error = error
            if total_files is not None:
                result.total_files = total_files
                print(f"🔍 Task {task_id}: total_files updated from {old_total_files} to {total_files}")
            if file_progress:
                result.file_progress.update(file_progress)
            if task_type:
                result.task_type = task_type
            if task_params:
                result.task_params.update(task_params)
            result.update_progress()

            # 调试日志：记录最终状态
            if old_total_files != result.total_files:
                print(f"🔍 Task {task_id}: total_files changed from {old_total_files} to {result.total_files}")

    async def update_file_progress(
        self,
        task_id: str,
        file_url: str,
        status: str,
        progress: int = 0,
        message: str = "",
        error: str = None,
        stage: str = None,
        stage_detail: str = None
    ):
        """更新单个文件的处理进度"""
        result = self.results.get(task_id)
        if result:
            result.file_progress[file_url] = {
                "status": status,  # processing, completed, failed
                "progress": progress,  # 0-100
                "message": message,
                "error": error,
                "stage": stage,  # 当前处理阶段
                "stage_detail": stage_detail,  # 阶段详细信息
                "updated_at": datetime.now().isoformat()
            }
            result.updated_at = datetime.now()

    async def update_agent_progress(
        self,
        task_id: str,
        agent_id: str,
        agent_name: str,
        status: str,
        progress: int = 0,
        message: str = "",
        error: str = None,
        execution_time: float = 0.0,
        level: int = 0
    ):
        """更新单个Agent的执行进度"""
        result = self.results.get(task_id)
        if result:
            result.agent_progress[agent_id] = {
                "agent_name": agent_name,
                "status": status,  # pending, running, completed, failed, skipped
                "progress": progress,  # 0-100
                "message": message,
                "error": error,
                "execution_time": execution_time,
                "level": level,  # 执行层级
                "updated_at": datetime.now().isoformat()
            }
            result.updated_at = datetime.now()

    async def update_workflow_status(
        self,
        task_id: str,
        workflow_id: str = None,
        workflow_status: str = None,
        execution_levels: List[List[str]] = None
    ):
        """更新工作流状态"""
        result = self.results.get(task_id)
        if result:
            if workflow_id:
                result.workflow_id = workflow_id
            if workflow_status:
                result.workflow_status = workflow_status
            if execution_levels:
                result.execution_levels = execution_levels
            result.updated_at = datetime.now()

    async def clean_old_tasks(self, max_age_hours: int = 24, keep_completed_hours: int = 2):
        """清理旧任务

        Args:
            max_age_hours: 任务的最大保留时间（小时）
            keep_completed_hours: 已完成任务的保留时间（小时），用于前端查看
        """
        now = datetime.now()
        async with self._lock:
            for task_id in list(self.results.keys()):
                result = self.results[task_id]
                age = (now - result.created_at).total_seconds() / 3600

                # 对于已完成的任务，使用较短的保留时间
                if result.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                    if age > keep_completed_hours:
                        self.tasks.pop(task_id, None)
                        self.results.pop(task_id, None)
                        print(f"清理已完成任务: {task_id} (age: {age:.1f}h)")
                # 对于其他任务，使用默认的保留时间
                elif age > max_age_hours:
                    self.tasks.pop(task_id, None)
                    self.results.pop(task_id, None)
                    print(f"清理旧任务: {task_id} (age: {age:.1f}h)")

# 创建全局任务管理器实例
task_manager = TaskManager() 