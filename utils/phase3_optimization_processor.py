#!/usr/bin/env python3
"""
第三阶段处理器：质量评估和性能优化
整合高级质量评估和性能监控功能
"""

import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

from utils.phase2_intelligent_processor import Phase2IntelligentProcessor
from utils.advanced_quality_assessor import AdvancedQualityAssessor
from utils.performance_monitor import PerformanceMonitor
from utils.log import log
from utils.config import config


class Phase3OptimizationProcessor:
    """第三阶段优化处理器"""
    
    def __init__(self):
        # 集成第二阶段处理器
        self.phase2_processor = Phase2IntelligentProcessor()
        
        # 高级质量评估器
        self.quality_assessor = AdvancedQualityAssessor()
        
        # 性能监控器
        self.performance_monitor = PerformanceMonitor()
        
        # 优化配置
        self.enable_advanced_quality = config.get("phase3.advanced_quality", True)
        self.enable_performance_monitoring = config.get("phase3.performance_monitoring", True)
        self.enable_auto_optimization = config.get("phase3.auto_optimization", False)
        
        # 缓存配置
        self.enable_result_caching = config.get("phase3.result_caching", True)
        self.cache_ttl_hours = config.get("phase3.cache_ttl_hours", 24)
        self.result_cache = {}
        
        self._initialized = False
    
    async def initialize(self):
        """初始化第三阶段处理器"""
        if not self._initialized:
            # 初始化第二阶段处理器
            await self.phase2_processor.initialize()
            
            # 初始化质量评估器
            if self.enable_advanced_quality:
                await self.quality_assessor.initialize()
            
            # 初始化性能监控器
            if self.enable_performance_monitoring:
                await self.performance_monitor.initialize()
            
            self._initialized = True
            log.info("第三阶段优化处理器初始化完成")
    
    async def process_with_optimization(
        self,
        project_name: str,
        action: str,
        urls: List[str],
        target_fields: Optional[List[str]] = None,
        force_refresh: bool = False,
        optimization_level: str = "standard"  # "basic", "standard", "advanced"
    ) -> Dict[str, Any]:
        """带优化的智能处理"""
        try:
            await self.initialize()
            
            # 生成请求ID用于跟踪
            request_id = str(uuid.uuid4())
            
            # 开始性能监控
            if self.enable_performance_monitoring:
                self.performance_monitor.start_request_tracking(request_id, {
                    "project_name": project_name,
                    "action": action,
                    "urls_count": len(urls),
                    "optimization_level": optimization_level
                })
            
            start_time = datetime.now()
            
            try:
                # 检查缓存
                cache_key = self._generate_cache_key(project_name, action, urls, target_fields)
                if self.enable_result_caching and not force_refresh:
                    cached_result = self._get_cached_result(cache_key)
                    if cached_result:
                        log.info(f"使用缓存结果: {project_name}")
                        
                        # 结束性能监控
                        if self.enable_performance_monitoring:
                            self.performance_monitor.end_request_tracking(request_id, True)
                        
                        return self._add_optimization_metadata(cached_result, "cached", start_time)
                
                # 执行第二阶段智能处理
                phase2_result = await self.phase2_processor.process_intelligent_assembly(
                    project_name=project_name,
                    action=action,
                    urls=urls,
                    target_fields=target_fields,
                    force_refresh=force_refresh
                )
                
                if "error" in phase2_result:
                    raise Exception(phase2_result["error"])
                
                # 执行高级质量评估
                quality_assessment = None
                if self.enable_advanced_quality and optimization_level in ["standard", "advanced"]:
                    quality_assessment = await self._perform_advanced_quality_assessment(
                        phase2_result, {
                            "processing_time_seconds": (datetime.now() - start_time).total_seconds(),
                            "optimization_level": optimization_level,
                            "request_id": request_id
                        }
                    )
                
                # 应用优化策略
                optimized_result = await self._apply_optimization_strategies(
                    phase2_result, quality_assessment, optimization_level
                )
                
                # 缓存结果
                if self.enable_result_caching:
                    self._cache_result(cache_key, optimized_result)
                
                # 结束性能监控
                if self.enable_performance_monitoring:
                    self.performance_monitor.end_request_tracking(request_id, True)
                
                return self._add_optimization_metadata(optimized_result, "processed", start_time)
                
            except Exception as e:
                # 结束性能监控（失败）
                if self.enable_performance_monitoring:
                    self.performance_monitor.end_request_tracking(request_id, False, str(e))
                
                raise e
                
        except Exception as e:
            log.error(f"第三阶段优化处理失败: {e}")
            return {"error": str(e)}
    
    async def _perform_advanced_quality_assessment(
        self,
        phase2_result: Dict[str, Any],
        processing_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行高级质量评估"""
        try:
            log.info("开始高级质量评估")
            
            # 从第二阶段结果中提取验证结果
            validation_result = phase2_result.get("validation_result", {})
            
            # 如果没有验证结果，构造一个基础的验证结果
            if not validation_result:
                validation_result = {
                    "project_name": phase2_result.get("project_name"),
                    "action": phase2_result.get("action"),
                    "validated_fields": phase2_result.get("extracted_data", {}),
                    "validation_reports": {},
                    "cross_validation": {"enabled": False}
                }
                
                # 为每个字段创建基础验证报告
                for field_name, field_value in validation_result["validated_fields"].items():
                    validation_result["validation_reports"][field_name] = {
                        "is_valid": field_value is not None,
                        "confidence": 0.8 if field_value is not None else 0.0,
                        "validation_method": "basic"
                    }
            
            # 执行综合质量评估
            assessment_result = await self.quality_assessor.comprehensive_quality_assessment(
                validation_result, processing_context
            )
            
            if "error" in assessment_result:
                log.warning(f"质量评估出现错误: {assessment_result['error']}")
                return {"error": assessment_result["error"]}
            
            log.info(f"高级质量评估完成: 总分 {assessment_result.get('quality_assessment', {}).get('overall_score', 0):.3f}")
            return assessment_result
            
        except Exception as e:
            log.error(f"高级质量评估失败: {e}")
            return {"error": str(e)}
    
    async def _apply_optimization_strategies(
        self,
        phase2_result: Dict[str, Any],
        quality_assessment: Optional[Dict[str, Any]],
        optimization_level: str
    ) -> Dict[str, Any]:
        """应用优化策略"""
        try:
            optimized_result = phase2_result.copy()
            
            # 添加质量评估结果
            if quality_assessment and "error" not in quality_assessment:
                optimized_result["quality_assessment"] = quality_assessment.get("quality_assessment", {})
                optimized_result["detailed_analysis"] = quality_assessment.get("detailed_analysis", {})
                optimized_result["improvement_suggestions"] = quality_assessment.get("improvement_suggestions", [])
            
            # 根据优化级别应用不同策略
            if optimization_level == "advanced":
                optimized_result = await self._apply_advanced_optimizations(optimized_result, quality_assessment)
            elif optimization_level == "standard":
                optimized_result = await self._apply_standard_optimizations(optimized_result, quality_assessment)
            else:  # basic
                optimized_result = await self._apply_basic_optimizations(optimized_result)
            
            return optimized_result
            
        except Exception as e:
            log.error(f"应用优化策略失败: {e}")
            return phase2_result
    
    async def _apply_basic_optimizations(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """应用基础优化"""
        try:
            # 基础数据清理和格式化
            extracted_data = result.get("extracted_data", {})
            
            # 清理空值和格式化
            cleaned_data = {}
            for field_name, field_value in extracted_data.items():
                if field_value is not None:
                    # 基础字符串清理
                    if isinstance(field_value, str):
                        cleaned_value = field_value.strip()
                        if cleaned_value:
                            cleaned_data[field_name] = cleaned_value
                    else:
                        cleaned_data[field_name] = field_value
            
            result["extracted_data"] = cleaned_data
            result["optimization_applied"] = ["basic_data_cleaning"]
            
            return result
            
        except Exception as e:
            log.error(f"基础优化失败: {e}")
            return result
    
    async def _apply_standard_optimizations(
        self,
        result: Dict[str, Any],
        quality_assessment: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """应用标准优化"""
        try:
            # 先应用基础优化
            result = await self._apply_basic_optimizations(result)
            
            optimizations_applied = result.get("optimization_applied", [])
            
            # 基于质量评估的优化
            if quality_assessment and "error" not in quality_assessment:
                quality_score = quality_assessment.get("quality_assessment", {}).get("overall_score", 0)
                
                # 如果质量分数较低，尝试改进
                if quality_score < 0.7:
                    result = await self._improve_low_quality_result(result, quality_assessment)
                    optimizations_applied.append("low_quality_improvement")
                
                # 应用改进建议
                suggestions = quality_assessment.get("improvement_suggestions", [])
                if suggestions:
                    result = await self._apply_improvement_suggestions(result, suggestions)
                    optimizations_applied.append("improvement_suggestions_applied")
            
            result["optimization_applied"] = optimizations_applied
            
            return result
            
        except Exception as e:
            log.error(f"标准优化失败: {e}")
            return result
    
    async def _apply_advanced_optimizations(
        self,
        result: Dict[str, Any],
        quality_assessment: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """应用高级优化"""
        try:
            # 先应用标准优化
            result = await self._apply_standard_optimizations(result, quality_assessment)
            
            optimizations_applied = result.get("optimization_applied", [])
            
            # 高级语义优化
            if self.enable_auto_optimization:
                result = await self._apply_semantic_optimization(result)
                optimizations_applied.append("semantic_optimization")
            
            # 智能字段补全
            result = await self._intelligent_field_completion(result)
            optimizations_applied.append("intelligent_field_completion")
            
            # 结果一致性增强
            result = await self._enhance_result_consistency(result)
            optimizations_applied.append("consistency_enhancement")
            
            result["optimization_applied"] = optimizations_applied
            
            return result
            
        except Exception as e:
            log.error(f"高级优化失败: {e}")
            return result
    
    async def _improve_low_quality_result(
        self,
        result: Dict[str, Any],
        quality_assessment: Dict[str, Any]
    ) -> Dict[str, Any]:
        """改进低质量结果"""
        try:
            # 分析质量问题
            dimension_scores = quality_assessment.get("quality_assessment", {}).get("dimension_scores", {})
            
            # 针对性改进
            if dimension_scores.get("completeness", 1.0) < 0.6:
                # 尝试补全缺失字段
                result = await self._attempt_field_completion(result)
            
            if dimension_scores.get("accuracy", 1.0) < 0.6:
                # 尝试提高准确性
                result = await self._attempt_accuracy_improvement(result)
            
            return result
            
        except Exception as e:
            log.error(f"改进低质量结果失败: {e}")
            return result
    
    async def _apply_improvement_suggestions(
        self,
        result: Dict[str, Any],
        suggestions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """应用改进建议"""
        try:
            applied_suggestions = []
            
            for suggestion in suggestions:
                suggestion_type = suggestion.get("type", "")
                priority = suggestion.get("priority", "medium")
                
                # 只应用高优先级的建议
                if priority == "high":
                    if suggestion_type == "completeness":
                        result = await self._attempt_field_completion(result)
                        applied_suggestions.append(suggestion_type)
                    elif suggestion_type == "accuracy":
                        result = await self._attempt_accuracy_improvement(result)
                        applied_suggestions.append(suggestion_type)
            
            if applied_suggestions:
                result["applied_suggestions"] = applied_suggestions
            
            return result
            
        except Exception as e:
            log.error(f"应用改进建议失败: {e}")
            return result
    
    async def _apply_semantic_optimization(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """应用语义优化"""
        try:
            # 语义优化的简化实现
            extracted_data = result.get("extracted_data", {})
            
            # 这里可以添加更复杂的语义优化逻辑
            # 例如：字段值的语义验证、格式标准化等
            
            return result
            
        except Exception as e:
            log.error(f"语义优化失败: {e}")
            return result
    
    async def _intelligent_field_completion(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """智能字段补全"""
        try:
            # 智能字段补全的简化实现
            extracted_data = result.get("extracted_data", {})
            
            # 这里可以添加基于已有字段推断缺失字段的逻辑
            
            return result
            
        except Exception as e:
            log.error(f"智能字段补全失败: {e}")
            return result
    
    async def _enhance_result_consistency(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """增强结果一致性"""
        try:
            # 结果一致性增强的简化实现
            extracted_data = result.get("extracted_data", {})
            
            # 这里可以添加字段间一致性检查和修正的逻辑
            
            return result
            
        except Exception as e:
            log.error(f"增强结果一致性失败: {e}")
            return result
    
    async def _attempt_field_completion(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """尝试字段补全"""
        try:
            # 字段补全的简化实现
            return result
        except Exception as e:
            log.error(f"字段补全失败: {e}")
            return result
    
    async def _attempt_accuracy_improvement(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """尝试提高准确性"""
        try:
            # 准确性改进的简化实现
            return result
        except Exception as e:
            log.error(f"准确性改进失败: {e}")
            return result
    
    def _generate_cache_key(
        self,
        project_name: str,
        action: str,
        urls: List[str],
        target_fields: Optional[List[str]]
    ) -> str:
        """生成缓存键"""
        import hashlib
        
        key_data = f"{project_name}:{action}:{':'.join(sorted(urls))}"
        if target_fields:
            key_data += f":{':'.join(sorted(target_fields))}"
        
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        try:
            if cache_key in self.result_cache:
                cached_item = self.result_cache[cache_key]
                
                # 检查缓存是否过期
                cache_time = datetime.fromisoformat(cached_item["cached_at"])
                if (datetime.now() - cache_time).total_seconds() < self.cache_ttl_hours * 3600:
                    return cached_item["result"]
                else:
                    # 删除过期缓存
                    del self.result_cache[cache_key]
            
            return None
            
        except Exception as e:
            log.error(f"获取缓存结果失败: {e}")
            return None
    
    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """缓存结果"""
        try:
            self.result_cache[cache_key] = {
                "result": result,
                "cached_at": datetime.now().isoformat()
            }
            
            # 限制缓存大小
            if len(self.result_cache) > 1000:
                # 删除最旧的缓存项
                oldest_key = min(
                    self.result_cache.keys(),
                    key=lambda k: self.result_cache[k]["cached_at"]
                )
                del self.result_cache[oldest_key]
            
        except Exception as e:
            log.error(f"缓存结果失败: {e}")
    
    def _add_optimization_metadata(
        self,
        result: Dict[str, Any],
        processing_type: str,
        start_time: datetime
    ) -> Dict[str, Any]:
        """添加优化元数据"""
        try:
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result["optimization_metadata"] = {
                "phase": "phase3_optimization",
                "processing_type": processing_type,
                "processing_time_seconds": round(processing_time, 3),
                "timestamp": datetime.now().isoformat(),
                "advanced_quality_enabled": self.enable_advanced_quality,
                "performance_monitoring_enabled": self.enable_performance_monitoring,
                "result_caching_enabled": self.enable_result_caching
            }
            
            return result
            
        except Exception as e:
            log.error(f"添加优化元数据失败: {e}")
            return result

    async def get_performance_report(self, time_range_hours: int = 1) -> Dict[str, Any]:
        """获取性能报告"""
        try:
            if not self.enable_performance_monitoring:
                return {"error": "性能监控未启用"}

            return await self.performance_monitor.get_performance_report(time_range_hours)

        except Exception as e:
            log.error(f"获取性能报告失败: {e}")
            return {"error": str(e)}

    async def batch_process_with_optimization(
        self,
        projects: List[Dict[str, Any]],
        optimization_level: str = "standard",
        max_concurrent: int = 3
    ) -> Dict[str, Any]:
        """批量优化处理"""
        try:
            await self.initialize()

            import asyncio

            log.info(f"开始批量优化处理: {len(projects)}个项目")

            # 创建信号量控制并发数
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_single_project(project_info):
                async with semaphore:
                    try:
                        return await self.process_with_optimization(
                            project_name=project_info.get("project_name", ""),
                            action=project_info.get("action", ""),
                            urls=project_info.get("urls", []),
                            target_fields=project_info.get("target_fields"),
                            optimization_level=optimization_level
                        )
                    except Exception as e:
                        return {
                            "project_name": project_info.get("project_name", ""),
                            "error": str(e)
                        }

            # 并发处理所有项目
            start_time = datetime.now()
            results = await asyncio.gather(
                *[process_single_project(project) for project in projects],
                return_exceptions=True
            )
            processing_time = (datetime.now() - start_time).total_seconds()

            # 统计结果
            successful_results = []
            failed_results = []

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    failed_results.append({
                        "project_name": projects[i].get("project_name", f"项目{i+1}"),
                        "error": str(result)
                    })
                elif "error" in result:
                    failed_results.append(result)
                else:
                    successful_results.append(result)

            return {
                "batch_processing_summary": {
                    "total_projects": len(projects),
                    "successful_projects": len(successful_results),
                    "failed_projects": len(failed_results),
                    "success_rate": round(len(successful_results) / len(projects) * 100, 2),
                    "processing_time_seconds": round(processing_time, 2),
                    "optimization_level": optimization_level,
                    "max_concurrent": max_concurrent
                },
                "successful_results": successful_results,
                "failed_results": failed_results,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            log.error(f"批量优化处理失败: {e}")
            return {"error": str(e)}

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            health_status = {
                "overall_status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "components": {}
            }

            # 检查第二阶段处理器
            try:
                phase2_health = await self.phase2_processor.health_check()
                health_status["components"]["phase2_processor"] = {
                    "status": "healthy" if "error" not in phase2_health else "error",
                    "details": phase2_health
                }
            except Exception as e:
                health_status["components"]["phase2_processor"] = {
                    "status": "error",
                    "error": str(e)
                }
                health_status["overall_status"] = "degraded"

            # 检查质量评估器
            if self.enable_advanced_quality:
                try:
                    assessor_stats = await self.quality_assessor.get_assessor_stats()
                    health_status["components"]["quality_assessor"] = {
                        "status": "healthy" if "error" not in assessor_stats else "error",
                        "details": assessor_stats
                    }
                except Exception as e:
                    health_status["components"]["quality_assessor"] = {
                        "status": "error",
                        "error": str(e)
                    }
                    health_status["overall_status"] = "degraded"

            # 检查性能监控器
            if self.enable_performance_monitoring:
                try:
                    monitor_stats = await self.performance_monitor.get_monitor_stats()
                    health_status["components"]["performance_monitor"] = {
                        "status": "healthy" if "error" not in monitor_stats else "error",
                        "details": monitor_stats
                    }
                except Exception as e:
                    health_status["components"]["performance_monitor"] = {
                        "status": "error",
                        "error": str(e)
                    }
                    health_status["overall_status"] = "degraded"

            return health_status

        except Exception as e:
            log.error(f"健康检查失败: {e}")
            return {
                "overall_status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def get_optimization_capabilities(self) -> Dict[str, Any]:
        """获取优化能力信息"""
        try:
            return {
                "processor_info": {
                    "type": "phase3_optimization_processor",
                    "version": "1.0",
                    "initialized": self._initialized
                },
                "optimization_features": {
                    "advanced_quality_assessment": self.enable_advanced_quality,
                    "performance_monitoring": self.enable_performance_monitoring,
                    "auto_optimization": self.enable_auto_optimization,
                    "result_caching": self.enable_result_caching
                },
                "optimization_levels": {
                    "basic": {
                        "description": "基础数据清理和格式化",
                        "features": ["data_cleaning", "format_standardization"]
                    },
                    "standard": {
                        "description": "标准优化包含质量评估和改进建议",
                        "features": ["basic_optimizations", "quality_assessment", "improvement_suggestions"]
                    },
                    "advanced": {
                        "description": "高级优化包含语义分析和智能补全",
                        "features": ["standard_optimizations", "semantic_optimization", "intelligent_completion"]
                    }
                },
                "cache_configuration": {
                    "enabled": self.enable_result_caching,
                    "ttl_hours": self.cache_ttl_hours,
                    "current_cache_size": len(self.result_cache)
                }
            }

        except Exception as e:
            log.error(f"获取优化能力信息失败: {e}")
            return {"error": str(e)}

    async def clear_cache(self) -> Dict[str, Any]:
        """清理缓存"""
        try:
            cache_size_before = len(self.result_cache)
            self.result_cache.clear()

            return {
                "success": True,
                "message": "缓存已清理",
                "cache_size_before": cache_size_before,
                "cache_size_after": 0,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            log.error(f"清理缓存失败: {e}")
            return {"error": str(e)}
