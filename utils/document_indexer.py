#!/usr/bin/env python3
"""
文档索引器
负责文档切片、向量化和ES索引
"""

import os
import json
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

from utils.llm import LLM
from utils.log import log
from utils.config import config
from utils.elasticsearch_client import ElasticsearchClient
from utils.minio_client import MinioClient


class DocumentIndexer:
    """文档索引器"""
    
    def __init__(self):
        self.llm = LLM()
        self.es_client = ElasticsearchClient()
        self.minio = MinioClient()
        
        # 切片配置
        self.chunk_size = config.get("document_indexer.chunk_size", 512)
        self.chunk_overlap = config.get("document_indexer.chunk_overlap", 50)
        self.min_chunk_size = config.get("document_indexer.min_chunk_size", 100)
        
        # 索引配置
        self.index_prefix = config.get("document_indexer.index_prefix", "docs")
        self.batch_size = config.get("document_indexer.batch_size", 50)
        
        self._initialized = False
    
    async def initialize(self):
        """初始化索引器"""
        if not self._initialized:
            await self.es_client.initialize()
            self._initialized = True
            log.info("文档索引器初始化完成")
    
    async def index_project_documents(
        self,
        project_name: str,
        action: str,
        urls: List[str],
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """索引项目文档"""
        try:
            await self.initialize()
            
            # 生成索引名
            index_name = self._generate_index_name(project_name, action)
            
            # 如果强制刷新，删除现有索引
            if force_refresh:
                await self.es_client.delete_index(index_name)
                log.info(f"已删除现有索引: {index_name}")
            
            # 创建索引
            await self.es_client.create_index(index_name)
            
            # 处理文档
            total_chunks = 0
            success_docs = 0
            failed_docs = 0
            
            for url in urls:
                try:
                    chunks_count = await self._index_single_document(
                        url, project_name, action, index_name
                    )
                    
                    if chunks_count > 0:
                        total_chunks += chunks_count
                        success_docs += 1
                        log.info(f"文档索引成功: {url}, 切片数: {chunks_count}")
                    else:
                        failed_docs += 1
                        log.error(f"文档索引失败: {url}")
                        
                except Exception as e:
                    failed_docs += 1
                    log.error(f"处理文档异常 {url}: {e}")
            
            # 获取索引统计
            index_stats = await self.es_client.get_index_stats(index_name)
            
            result = {
                "index_name": index_name,
                "total_documents": len(urls),
                "success_documents": success_docs,
                "failed_documents": failed_docs,
                "total_chunks": total_chunks,
                "index_stats": index_stats,
                "indexed_at": datetime.now().isoformat()
            }
            
            log.info(f"项目文档索引完成: {project_name}, 成功{success_docs}/{len(urls)}, 总切片{total_chunks}")
            return result
            
        except Exception as e:
            log.error(f"索引项目文档失败: {e}")
            return {"error": str(e)}
    
    def _generate_index_name(self, project_name: str, action: str) -> str:
        """生成索引名 - 兼容现有架构，使用固定的docs索引"""
        # 使用现有的固定索引名称，保持兼容性
        return "docs"
    
    async def _index_single_document(
        self,
        url: str,
        project_name: str,
        action: str,
        index_name: str
    ) -> int:
        """索引单个文档"""
        try:
            # 1. 下载JSON文件
            json_url = os.path.splitext(url)[0] + ".json"
            json_path = self.minio.download_file(json_url)
            
            # 2. 读取文档内容
            with open(json_path, 'r', encoding='utf-8') as f:
                doc_data = json.load(f)
            
            # 3. 文档切片
            chunks = await self._chunk_document(doc_data, url)
            
            if not chunks:
                log.warning(f"文档无有效内容: {url}")
                return 0
            
            # 4. 生成向量
            chunks_with_vectors = await self._generate_embeddings(chunks)
            
            # 5. 准备ES文档
            es_documents = self._prepare_es_documents(
                chunks_with_vectors, project_name, action, url, doc_data.get("source", "")
            )
            
            # 6. 批量索引到ES
            result = await self.es_client.bulk_index_documents(index_name, es_documents)
            
            return result["success_count"]
            
        except Exception as e:
            log.error(f"索引单个文档失败 {url}: {e}")
            return 0
    
    async def _chunk_document(
        self,
        doc_data: Dict[str, Any],
        url: str
    ) -> List[Dict[str, Any]]:
        """文档切片"""
        try:
            chunks = []
            
            for page_num, page in enumerate(doc_data.get("pages", []), 1):
                content = page.get("content", "").strip()
                
                if not content or len(content) < self.min_chunk_size:
                    continue
                
                # 按段落分割
                paragraphs = content.split('\n\n')
                current_chunk = ""
                chunk_index = 0
                
                for paragraph in paragraphs:
                    paragraph = paragraph.strip()
                    if not paragraph:
                        continue
                    
                    # 检查是否需要创建新切片
                    if len(current_chunk) + len(paragraph) > self.chunk_size:
                        if current_chunk:
                            # 保存当前切片
                            chunks.append({
                                "chunk_text": current_chunk.strip(),
                                "page_number": page_num,
                                "chunk_index": chunk_index,
                                "chunk_id": f"page_{page_num}_chunk_{chunk_index}",
                                "char_count": len(current_chunk.strip())
                            })
                            chunk_index += 1
                        
                        # 开始新切片
                        current_chunk = paragraph
                    else:
                        # 添加到当前切片
                        if current_chunk:
                            current_chunk += "\n\n" + paragraph
                        else:
                            current_chunk = paragraph
                
                # 保存最后一个切片
                if current_chunk and len(current_chunk.strip()) >= self.min_chunk_size:
                    chunks.append({
                        "chunk_text": current_chunk.strip(),
                        "page_number": page_num,
                        "chunk_index": chunk_index,
                        "chunk_id": f"page_{page_num}_chunk_{chunk_index}",
                        "char_count": len(current_chunk.strip())
                    })
            
            log.debug(f"文档切片完成: {url}, 切片数: {len(chunks)}")
            return chunks
            
        except Exception as e:
            log.error(f"文档切片失败 {url}: {e}")
            return []
    
    async def _generate_embeddings(
        self,
        chunks: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """生成向量"""
        try:
            chunks_with_vectors = []
            
            # 批量生成向量
            texts = [chunk["chunk_text"] for chunk in chunks]
            
            # 分批处理以避免API限制
            for i in range(0, len(texts), self.batch_size):
                batch_texts = texts[i:i + self.batch_size]
                batch_chunks = chunks[i:i + self.batch_size]
                
                try:
                    # 批量生成向量
                    embeddings = await self._batch_generate_embeddings(batch_texts)
                    
                    # 组合结果
                    for chunk, embedding in zip(batch_chunks, embeddings):
                        chunk_with_vector = chunk.copy()
                        chunk_with_vector["embedding"] = embedding
                        chunks_with_vectors.append(chunk_with_vector)
                        
                except Exception as e:
                    log.error(f"批量生成向量失败: {e}")
                    # 降级到单个生成
                    for chunk in batch_chunks:
                        try:
                            embedding = await self.llm.get_embedding(chunk["chunk_text"])
                            chunk_with_vector = chunk.copy()
                            chunk_with_vector["embedding"] = embedding
                            chunks_with_vectors.append(chunk_with_vector)
                        except Exception as e2:
                            log.error(f"单个向量生成失败: {e2}")
                            # 使用零向量作为降级
                            chunk_with_vector = chunk.copy()
                            chunk_with_vector["embedding"] = [0.0] * 768  # 默认768维
                            chunks_with_vectors.append(chunk_with_vector)
            
            log.debug(f"向量生成完成: {len(chunks_with_vectors)}个切片")
            return chunks_with_vectors
            
        except Exception as e:
            log.error(f"生成向量失败: {e}")
            return chunks
    
    async def _batch_generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """批量生成向量"""
        # 如果LLM支持批量embedding，使用批量接口
        if hasattr(self.llm, 'get_embeddings_batch'):
            return await self.llm.get_embeddings_batch(texts)
        else:
            # 否则逐个生成
            embeddings = []
            for text in texts:
                embedding = await self.llm.get_embedding(text)
                embeddings.append(embedding)
            return embeddings
    
    def _prepare_es_documents(
        self,
        chunks: List[Dict[str, Any]],
        project_name: str,
        action: str,
        source_url: str,
        source_name: str
    ) -> List[Dict[str, Any]]:
        """准备ES文档 - 兼容现有架构"""
        es_documents = []

        # 生成doc_id（兼容现有逻辑）
        doc_id = hashlib.md5(f"{project_name}_{source_name}".encode()).hexdigest()

        for chunk in chunks:
            # 兼容现有ES mapping结构
            es_doc = {
                "content": chunk["chunk_text"],  # 使用content字段而不是chunk_text
                "embedding": chunk["embedding"],
                "project_name": project_name,
                "source": source_name,
                "page_num": chunk["page_number"],  # 使用page_num而不是page_number
                "doc_id": doc_id,
                "action": action,
                "metadata": {
                    "source_url": source_url,
                    "chunk_index": chunk["chunk_index"],
                    "chunk_id": chunk["chunk_id"],
                    "char_count": chunk["char_count"],
                    "indexed_at": datetime.now().isoformat()
                },
                "year": datetime.now().year
            }

            # 设置文档ID为chunk级别的唯一标识
            es_doc["_id"] = f"{doc_id}_{chunk['chunk_id']}"

            es_documents.append(es_doc)

        return es_documents
    
    async def get_index_info(self, project_name: str, action: str) -> Dict[str, Any]:
        """获取索引信息"""
        try:
            index_name = self._generate_index_name(project_name, action)
            
            if not await self.es_client.index_exists(index_name):
                return {"exists": False, "index_name": index_name}
            
            stats = await self.es_client.get_index_stats(index_name)
            
            return {
                "exists": True,
                "index_name": index_name,
                "document_count": stats.get("document_count", 0),
                "store_size": stats.get("store_size", 0),
                "indexing_total": stats.get("indexing_total", 0),
                "search_total": stats.get("search_total", 0)
            }
            
        except Exception as e:
            log.error(f"获取索引信息失败: {e}")
            return {"error": str(e)}
    
    async def delete_project_index(self, project_name: str, action: str) -> bool:
        """删除项目索引"""
        try:
            index_name = self._generate_index_name(project_name, action)
            return await self.es_client.delete_index(index_name)
            
        except Exception as e:
            log.error(f"删除项目索引失败: {e}")
            return False
    
    async def get_indexer_stats(self) -> Dict[str, Any]:
        """获取索引器统计信息"""
        try:
            es_health = await self.es_client.health_check()
            
            return {
                "indexer_info": {
                    "initialized": self._initialized,
                    "chunk_size": self.chunk_size,
                    "chunk_overlap": self.chunk_overlap,
                    "min_chunk_size": self.min_chunk_size,
                    "batch_size": self.batch_size
                },
                "es_health": es_health
            }
            
        except Exception as e:
            log.error(f"获取索引器统计失败: {e}")
            return {"error": str(e)}
