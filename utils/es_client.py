import os
import sys

# 添加项目根目录到 PYTHONPATH
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_dir)

from utils.config import config  # 修改为从 utils.config 导入

from elasticsearch import AsyncElasticsearch
from typing import Dict, Any, List, Optional
import json

class ESClient:
    """ES客户端封装"""
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 9200,
        username: str = "elastic",
        password: str = "elastic",
        index_name: str = "docs"
    ):
        """初始化ES客户端
        
        Args:
            host: ES主机地址
            port: ES端口号
            username: 用户名
            password: 密码
            index_name: 索引名称
        """
        # 构建完整的URL
        url = f"http://{host}:{port}"
        print(f"Connecting to Elasticsearch at {url}")  # 添加调试日志
        
        # 初始化ES客户端
        self.es = AsyncElasticsearch(
            hosts=[url],
            basic_auth=(username, password),
            verify_certs=False,
            request_timeout=30,  # 添加超时设置
            retry_on_timeout=True  # 添加重试设置
        )
        self.index_name = index_name

    async def init_index(self, index_name: str = None, mapping: Dict = None):
        """初始化索引
        
        Args:
            index_name: 索引名称，如果为None则使用默认索引
            mapping: 索引映射配置
        """
        try:
            # 使用提供的索引名称或默认索引名称
            index_name = index_name or self.index_name
            
            if not await self.es.indices.exists(index=index_name):
                await self.es.indices.create(
                    index=index_name,
                    body=mapping
                )
                print(f"Created index: {index_name}")
            else:
                print(f"Index {index_name} already exists")
            
        except Exception as e:
            print(f"Error initializing index: {str(e)}")
            raise

    async def add_document(self, doc_id: str, document: Dict):
        """添加文档"""
        try:
            await self.es.index(
                index=self.index_name,
                id=doc_id,
                body=document
            )
            return True
        except Exception as e:
            print(f"Error adding document: {str(e)}")
            return False

    async def search(
        self,
        body: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """搜索文档"""
        try:
            response = await self.es.search(
                index=self.index_name,
                body=body
            )
            
            return [hit["_source"] | {"score": hit["_score"]} for hit in response["hits"]["hits"]]
        except Exception as e:
            print(f"Error searching documents: {str(e)}")
            return []

    async def close(self):
        """关闭连接"""
        await self.es.close()

    async def index_exists(self, index_name: str) -> bool:
        """检查索引是否存在"""
        try:
            return await self.es.indices.exists(index=index_name)
        except Exception as e:
            print(f"Error checking index existence: {str(e)}")
            return False
            
    async def delete_index(self, index_name: str) -> bool:
        """删除索引"""
        try:
            if await self.index_exists(index_name):
                await self.es.indices.delete(index=index_name)
                return True
            return False
        except Exception as e:
            print(f"Error deleting index: {str(e)}")
            return False

    async def bulk(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """执行批量操作

        Args:
            operations: 批量操作的列表

        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            result = await self.es.bulk(
                operations=operations,
                refresh=True  # 立即刷新以便搜索
            )
            return result
        except Exception as e:
            print(f"Error executing bulk operation: {str(e)}")
            raise

    async def delete_by_query(self, index: str, body: Dict[str, Any], refresh: bool = True) -> Dict[str, Any]:
        """根据查询条件删除文档

        Args:
            index: 索引名称
            body: 查询条件
            refresh: 是否立即刷新

        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            result = await self.es.delete_by_query(
                index=index,
                body=body,
                refresh=refresh
            )
            return result
        except Exception as e:
            print(f"Error executing delete by query: {str(e)}")
            raise

    async def delete(self, index: str, id: str, ignore: List[int] = None) -> Dict[str, Any]:
        """删除指定文档

        Args:
            index: 索引名称
            id: 文档ID
            ignore: 忽略的HTTP状态码列表

        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            result = await self.es.delete(
                index=index,
                id=id,
                ignore=ignore or []
            )
            return result
        except Exception as e:
            print(f"Error deleting document: {str(e)}")
            raise