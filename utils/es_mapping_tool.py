import json
import argparse
import os
from typing import Dict, Any, List, Generator
from elasticsearch import Elasticsearch, helpers
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ESMappingTool:
    """ES mapping和数据导入导出工具"""
    
    def __init__(self, host: str = "localhost", port: int = 9200,
                 username: str = "elastic", password: str = "elastic"):
        """初始化ES客户端"""
        self.es = Elasticsearch(
            [f"http://{host}:{port}"],
            basic_auth=(username, password),
            verify_certs=False
        )
    
    def export_mapping(self, index_name: str, output_file: str) -> bool:
        """导出索引mapping"""
        try:
            mapping = self.es.indices.get_mapping(index=index_name)
            settings = self.es.indices.get_settings(index=index_name)
            
            index_config = {
                "settings": settings[index_name]["settings"]["index"],
                "mappings": mapping[index_name]["mappings"]
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(index_config, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Successfully exported mapping to {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting mapping: {e}")
            return False
    
    def export_data(self, index_name: str, output_file: str, batch_size: int = 1000) -> bool:
        """导出索引数据
        
        Args:
            index_name: 索引名称
            output_file: 输出文件路径
            batch_size: 每批次处理的文档数
        """
        try:
            # 使用scan获取所有文档
            docs = []
            total_docs = 0
            
            for doc in helpers.scan(
                self.es,
                index=index_name,
                size=batch_size,
                scroll='5m'
            ):
                doc_data = {
                    "_id": doc["_id"],
                    "_source": doc["_source"]
                }
                docs.append(doc_data)
                total_docs += 1
                
                if len(docs) >= batch_size:
                    self._write_batch(docs, output_file, total_docs == batch_size)
                    docs = []
                    
            # 写入剩余的文档
            if docs:
                self._write_batch(docs, output_file, False)
            
            logger.info(f"Successfully exported {total_docs} documents to {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting data: {e}")
            return False
    
    def _write_batch(self, docs: List[Dict], output_file: str, is_first: bool):
        """写入一批文档到文件"""
        mode = 'w' if is_first else 'a'
        with open(output_file, mode, encoding='utf-8') as f:
            for doc in docs:
                f.write(json.dumps(doc, ensure_ascii=False) + '\n')
    
    def import_mapping(self, index_name: str, input_file: str, force: bool = False) -> bool:
        """导入索引mapping"""
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                index_config = json.load(f)
            
            if self.es.indices.exists(index=index_name):
                if force:
                    logger.warning(f"Deleting existing index {index_name}")
                    self.es.indices.delete(index=index_name)
                else:
                    logger.error(f"Index {index_name} already exists. Use --force to overwrite")
                    return False
            
            self.es.indices.create(
                index=index_name,
                body=index_config
            )
            
            logger.info(f"Successfully imported mapping to index {index_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error importing mapping: {e}")
            return False
    
    def import_data(self, index_name: str, input_file: str, batch_size: int = 1000) -> bool:
        """导入索引数据"""
        try:
            total_docs = 0
            
            def doc_generator():
                with open(input_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        doc = json.loads(line)
                        yield {
                            "_index": index_name,
                            "_id": doc["_id"],
                            "_source": doc["_source"]
                        }
            
            # 使用bulk导入数据
            for success, info in helpers.parallel_bulk(
                self.es,
                doc_generator(),
                chunk_size=batch_size,
                thread_count=4,
                raise_on_error=False
            ):
                if success:
                    total_docs += 1
                else:
                    logger.error(f"Error importing document: {info}")
            
            logger.info(f"Successfully imported {total_docs} documents")
            return True
            
        except Exception as e:
            logger.error(f"Error importing data: {e}")
            return False
    
    def close(self):
        """关闭ES连接"""
        self.es.close()

def main():
    parser = argparse.ArgumentParser(description='ES Mapping and Data Tool')
    parser.add_argument('action', choices=['export-mapping', 'import-mapping', 
                                         'export-data', 'import-data', 'export-all', 'import-all'], 
                       help='Action to perform')
    parser.add_argument('--host', default='localhost', help='Elasticsearch host')
    parser.add_argument('--port', type=int, default=9200, help='Elasticsearch port')
    parser.add_argument('--username', default='elastic', help='Elasticsearch username')
    parser.add_argument('--password', default='elastic', help='Elasticsearch password')
    parser.add_argument('--index', required=True, help='Index name')
    parser.add_argument('--mapping-file', help='Mapping file path')
    parser.add_argument('--data-file', help='Data file path')
    parser.add_argument('--force', action='store_true', help='Force overwrite existing index')
    parser.add_argument('--batch-size', type=int, default=1000, help='Batch size for data operations')
    
    args = parser.parse_args()
    
    tool = ESMappingTool(
        host=args.host,
        port=args.port,
        username=args.username,
        password=args.password
    )
    
    try:
        if args.action == 'export-all':
            if not args.mapping_file or not args.data_file:
                logger.error("Both --mapping-file and --data-file are required for export-all")
                exit(1)
            success = (
                tool.export_mapping(args.index, args.mapping_file) and
                tool.export_data(args.index, args.data_file, args.batch_size)
            )
        elif args.action == 'import-all':
            if not args.mapping_file or not args.data_file:
                logger.error("Both --mapping-file and --data-file are required for import-all")
                exit(1)
            success = (
                tool.import_mapping(args.index, args.mapping_file, args.force) and
                tool.import_data(args.index, args.data_file, args.batch_size)
            )
        elif args.action == 'export-mapping':
            if not args.mapping_file:
                logger.error("--mapping-file is required for export-mapping")
                exit(1)
            success = tool.export_mapping(args.index, args.mapping_file)
        elif args.action == 'import-mapping':
            if not args.mapping_file:
                logger.error("--mapping-file is required for import-mapping")
                exit(1)
            success = tool.import_mapping(args.index, args.mapping_file, args.force)
        elif args.action == 'export-data':
            if not args.data_file:
                logger.error("--data-file is required for export-data")
                exit(1)
            success = tool.export_data(args.index, args.data_file, args.batch_size)
        else:  # import-data
            if not args.data_file:
                logger.error("--data-file is required for import-data")
                exit(1)
            success = tool.import_data(args.index, args.data_file, args.batch_size)
            
        if not success:
            exit(1)
            
    finally:
        tool.close()

if __name__ == "__main__":
    main() 