from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from pathlib import Path
import json
import re
import hashlib
from datetime import datetime
from utils.valid import parse_date, parse_number
from utils.config import config

class MySQLDatabase:
    def __init__(self,
                 host=None,
                 user=None,
                 password=None,
                 database=None,
                 port=None,
                 pool_size=5):
        # 使用config.yaml中的配置
        import urllib.parse

        # URL编码密码以处理特殊字符
        password = urllib.parse.quote_plus(config.get('mysql.password', 'startfrom2023'))
        username = config.get('mysql.username', 'root')
        host = config.get('mysql.host', '***********')
        port = config.get('mysql.port', 3306)
        database = config.get('mysql.database', 'hngpt')

        connection_string = f"mysql+asyncmy://{username}:{password}@{host}:{port}/{database}?charset=utf8mb4"
        print(f"MySQL连接字符串: mysql+asyncmy://{username}:***@{host}:{port}/{database}")

        self.engine = create_async_engine(
            connection_string,
            pool_size=pool_size,
            echo=False  # 改为False减少日志输出
        )
        
        self.async_session = sessionmaker(
            self.engine,
            expire_on_commit=False,
            class_=AsyncSession
        )
        
        self.COLUMN_MAPPING = {
            'project_extract': {
                'name': '项目名称',
                'start_date': '开始日期',
                'end_date': '截止日期',
                'total_investment': '总投资',
                'responsible_unit': '项目承建单位',
                'leader': '负责人',
                'research_points': '主要研究点',
                'innovation': '创新点',
                'main_deliverables': '主要交付成果',
                'patent': "专利"
            },
            'conference_extract': {
                'name': '文书名称',
                'date': '文书时间',
                'type': '文书类型',
                'organizer': '文书发起组织',
                'participants': '文书参与者',
                'summary': '文书摘要'
            }
        }

    async def create_tables(self):
        async with self.async_session() as session:
            try:
                # 创建项目表
                project_sql = """
                CREATE TABLE IF NOT EXISTS project_extract (
                    id BIGINT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    start_date DATE,
                    end_date DATE,
                    total_investment BIGINT,
                    responsible_unit TEXT,
                    leader TEXT,
                    research_points TEXT,
                    innovation TEXT,
                    main_deliverables TEXT,
                    patent TEXT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """
                await session.execute(text(project_sql))

                # 创建会议表
                conference_sql = """
                CREATE TABLE IF NOT EXISTS conference_extract (
                    id BIGINT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    date DATE,
                    type TEXT,
                    organizer TEXT,
                    participants TEXT,
                    summary TEXT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """
                await session.execute(text(conference_sql))

                await session.commit()
            except Exception as err:
                print(f"Error creating tables: {err}")
                await session.rollback()
                raise

    async def insert_project_extract(self, **kwargs):
        """插入项目数据"""
        async with self.async_session() as session:
            try:
                # 处理JSON字段
                data = {
                    'name': kwargs.get('name'),
                    'start_date': kwargs.get('start_date'),
                    'end_date': kwargs.get('end_date'),
                    'total_investment': kwargs.get('total_investment'),
                    'responsible_unit': kwargs.get('responsible_unit'),
                    'leader': kwargs.get('leader'),
                    'research_points': json.dumps(kwargs.get('research_points', [])),
                    'innovation': kwargs.get('innovation'),
                    'main_deliverables': json.dumps(kwargs.get('main_deliverables', [])),
                    'patent': kwargs.get('patent')
                }
                
                # 生成唯一ID (时间戳+随机数)
                import time
                import random
                id = int(time.time() * 1000) + random.randint(0, 9999)
                
                stmt = text("""
                    INSERT INTO project_extract
                    (id, name, start_date, end_date, total_investment, responsible_unit,
                     leader, research_points, innovation, main_deliverables, patent)
                    VALUES
                    (:id, :name, :start_date, :end_date, :total_investment, :responsible_unit,
                     :leader, :research_points, :innovation, :main_deliverables, :patent)
                """)
                data['id'] = id
                
                await session.execute(stmt, data)
                await session.commit()
                return True
            except Exception as e:
                await session.rollback()
                raise

    async def get_project_by_name(self, name):
        """根据名称查询项目"""
        async with self.async_session() as session:
            stmt = text("""
                SELECT * FROM project_extract
                WHERE name = :name
                LIMIT 1
            """)
            result = await session.execute(stmt, {'name': name})
            row = result.fetchone()
            if row:
                # 获取列名列表
                keys = result.keys()
                # 将元组转换为字典
                return dict(zip(keys, row))
            return None

    async def insert_conference_extract(self, **kwargs):
        """插入会议数据"""
        async with self.async_session() as session:
            try:
                # 生成唯一ID (时间戳+随机数)
                import time
                import random
                id = int(time.time() * 1000) + random.randint(0, 9999)
                
                stmt = text("""
                    INSERT INTO conference_extract
                    (id, name, date, type, organizer, participants, summary)
                    VALUES
                    (:id, :name, :date, :type, :organizer, :participants, :summary)
                """)
                
                data = kwargs.copy()
                data['id'] = id
                await session.execute(stmt, data)
                await session.commit()
                return True
            except Exception as e:
                await session.rollback()
                raise

    async def get_conference_by_name(self, name):
        """根据名称查询会议"""
        async with self.async_session() as session:
            stmt = text("""
                SELECT * FROM conference_extract
                WHERE name = :name
                LIMIT 1
            """)
            result = await session.execute(stmt, {'name': name})
            row = result.fetchone()
            if row:
                # 获取列名列表
                keys = result.keys()
                # 将元组转换为字典
                return dict(zip(keys, row))
            return None

    async def query_table(self, table_name, **filters):
        """通用表查询方法"""
        async with self.async_session() as session:
            where_clause = " AND ".join([f"{k} = :{k}" for k in filters])
            stmt = text(f"""
                SELECT * FROM {table_name}
                {f"WHERE {where_clause}" if filters else ""}
            """)
            result = await session.execute(stmt, filters)
            rows = result.fetchall()
            if not rows:
                return []
            # 获取列名列表
            keys = result.keys()
            # 将每行元组转换为字典
            return [dict(zip(keys, row)) for row in rows]