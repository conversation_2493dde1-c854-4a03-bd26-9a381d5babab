import os
import shutil
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk
# from utils.simple_file_loader import <PERSON>FileLoader as FileLoader
from typing import Dict
from utils.log import log
# from utils.sentence_vector import OSentenceVector

# 临时禁用OSentenceVector，使用简单实现
class OSentenceVector:
    """简单的向量化实现"""
    def __init__(self):
        pass

    def encode(self, text: str):
        """生成模拟向量"""
        import hashlib
        import numpy as np
        # 基于文本内容生成确定性的模拟向量
        text_hash = hashlib.md5(text.encode()).hexdigest()
        # 生成384维向量
        np.random.seed(int(text_hash[:8], 16))
        vector = np.random.normal(0, 1, 384)
        # 归一化
        vector = vector / np.linalg.norm(vector)
        return vector
from pathlib import Path
import numpy as np
from tqdm import tqdm
from utils.md5 import generate_md5
from utils.config import es_cfg,model_cfg,config
# from loaders.detect import update, split_text2

# 内联实现，避免导入loaders包
def update(file_path):
    """简单的文件更新函数"""
    return file_path

def split_text2(text: str, chunk_size: int = 1000, overlap: int = 100):
    """简单的文本分割函数"""
    if not text or chunk_size <= 0:
        return []

    if len(text) <= chunk_size:
        return [text]

    chunks = []
    start = 0

    while start < len(text):
        end = start + chunk_size

        # 如果不是最后一块，尝试在句子边界处分割
        if end < len(text):
            sentence_end = -1
            for i in range(end, max(start + chunk_size // 2, start), -1):
                if text[i] in '。！？；.!?;':
                    sentence_end = i + 1
                    break

            if sentence_end > start:
                end = sentence_end

        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)

        start = max(start + 1, end - overlap)

        if start >= len(text):
            break

    return chunks
# from bm25 import BM25Similarity
# from reranker import reranker
# from sumary import get_sumary
import threading
import hashlib
# import ollama

# 简单的BM25实现
class BM25Similarity:
    def __init__(self):
        pass

    def search(self, query, top_k=10):
        return []

# 简单的reranker实现
def reranker(query, docs, top_k=10):
    return docs[:top_k]

# 简单的摘要实现
def get_sumary(text):
    return text[:200] + "..." if len(text) > 200 else text

def generat_setting():
    settings = {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "index": {
                    "similarity": {
                        "default": {
                            "type": "BM25",
                            "b": 0.5,
                            "k1": 1.2
                        }
                    }
                }
            }
    return settings



# def generate_mapping(dims: int) -> Dict:
#     return {
#         'properties': {
#             'text': {'type': 'text'},
#             'vector': {'type': 'dense_vector', 'dims': dims, 'index': True, 'similarity': 'cosine'},
#             'metadata': {
#                 'properties': {
#                     'source': {'type': 'keyword'},
#                     'id': {'type': 'keyword'},
#                     'project_name': {'type': 'text', 'fields': {'keyword': {'type': 'keyword'}}}
#                 }
#             }
#         }
#     }

def generate_mapping(dims: int):
    return {
        "properties": {
            "text": {
                "type": "text",
                "analyzer": "ik_smart",
                "search_analyzer": "ik_max_word"
            },
            "vector": {
                "type": "dense_vector",
                "dims": dims,
                "index": True,
                "similarity": "cosine"
            },
            "metadata": {
                "properties": {
                    "source": {
                        "type": "keyword"
                    },
                    "id": {
                        "type": "keyword"
                    },
                    "project_name": {
                        "type": "keyword"
                    }
                }
            }
        }
    }

def generate_project_body(project_name, vec, size) -> Dict:
    query_body = {
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "metadata.project_name": project_name
                        }
                    },
                    {
                        "script_score": {
                            "query": {
                                "match_all": {}
                            },
                            "script": {
                                "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                                "params": {
                                    "query_vector": vec
                                }
                            }
                        }
                    }
                ]
            }
        },
        "size": size
    }
    return query_body

def generate_extract_body(project_name, keys, size) -> Dict:
    # should_queries = [{'match': {'text': key}} for key in keys]
    query_body = {
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "metadata.project_name": project_name
                        }
                    }
                ],
               "should": [
                            {"match": {"text": key}} for key in keys
                ],
                "minimum_should_match": 0
            }
        },
        "size": size
    }
    return query_body




def generate_search_query(vec, size) -> Dict:
    query = {
        "query":{
            "script_score": {
                "query": {
                    "match_all": {}
                },
                "script": {
                    "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                    "params": {
                        "query_vector": vec
                    }
                }
            }
        },
        "size": size
    }
    return query



def generate_knn_query(vec, size) -> Dict:
    query = {
        "knn": {
            "field": "vector",
            "query_vector": vec,
            "k": 10,
            "num_candidates": 100
        },
        "size": size
    }
    return query


def generate_hybrid_query(text, vec, size, knn_boost) -> Dict:
    query = {
        "query": {
            "match": {
                "text": {
                    "query": text,
                    "boost": 1 - knn_boost
                }
            }
        },
        "knn": {
            "field": "vector",
            "query_vector": vec,
            "k": 10,
            "num_candidates": 100,
            "boost": knn_boost
        },
        "size": size
    }
    return query


def load_file(filepath, chunk_size, chunk_overlap):
    # 简化的文件加载逻辑
    import json

    id = generate_md5(filepath)
    name = Path(filepath).name

    # 直接加载JSON文件
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if isinstance(data, dict) and 'pages' in data:
            pages = data['pages']
        elif isinstance(data, list):
            pages = data
        else:
            pages = [{"page_num": 1, "content": str(data)}]
    except Exception as e:
        log.error(f"加载文件失败 {filepath}: {e}")
        pages = []
    content = ""
    for item in pages:
        page_content = item.get("pageContent", [])
        if page_content and isinstance(page_content, list):
            # 过滤掉None值
            valid_content = [str(c) for c in page_content if c is not None]
            content += "\n".join(valid_content)
    content = content.strip()
    docs = split_text2(content, chunk_size, chunk_overlap)
    os.remove(filepath)
    return content,docs,name,id




class ES:
    def __init__(self):
        self.embed_lock = threading.Lock()
        # self.embedding = SentenceVector(model_name_or_path=model_cfg.get("embedding_model"))
        self.embedding = OSentenceVector()
        self.index_name = es_cfg.get("index_name")
        self.client = Elasticsearch(['http://{}:{}'.format(es_cfg.get("url"), es_cfg.get("port"))],
                                    basic_auth=(es_cfg.get("username"), es_cfg.get("passwd")),
                                    verify_certs=False)
        self.create_index()
        log.info(f"{self.client.info()}")
    
    def configure_settings(self):
        settings = generat_setting()
        self.client.indices.put_settings(index=self.index_name, settings=settings)
    
    def create_index(self):
        if not self.client.indices.exists(index=self.index_name):
            # dims = len(self.embedding.encode(["test"]))
            dims = len(self.embedding.encode("test"))
            mapping = generate_mapping(dims)
            settings = generat_setting()
            body = {
                "settings": settings,
                "mappings": mapping
            }
            self.client.indices.create(index=self.index_name, body=body)
            return f"创建{self.index_name}成功"    

    def add_fullindex(self, contenst, source, id):
        self.add_documents(contenst, source, id)
        return "插入成功"
    
    
    def add_documents(self, docs, source, id, project_name):
        content_nums = len(docs)
        embedings = []
        log.info(f'begin add_documents')
        with tqdm(total=content_nums, desc='Encoding embeddings') as pbar:
            for i in range(0, content_nums, 32):
                start_idx = i
                end_idx = start_idx + 32
                end_idx = content_nums if end_idx > content_nums else end_idx
                with self.embed_lock:
                    embbs = self.embedding.encode(docs[start_idx:end_idx])
                    embedings.append(embbs)
                pbar.update(end_idx - start_idx)
        
        embedings = np.vstack(embedings)
        log.info(f'begin add_documents Indexing documents')
        actions = []
        for i, doc in enumerate(docs):
            action = {
                '_index': self.index_name,
                '_source': {
                    'text': doc,
                    'vector': embedings[i].tolist(),
                    'metadata': {
                        'project_name':project_name,
                        'source': source,
                        'id': id
                    }
                }
            }
            actions.append(action)
        with tqdm(total=len(actions), desc='Bulk indexing') as pbar:
            success, _ = bulk(self.client, actions, raise_on_error=False)
            pbar.update(success)
        log.info(f'end add_documents')


    def upsert_documents(self, docs, source, id, project_name):
        content_nums = len(docs)
        embeddings = []

        log.info('Begin add_documents')

        # Generate embeddings
        with tqdm(total=content_nums, desc='Encoding embeddings') as pbar:
            for i in range(0, content_nums, 32):
                start_idx = i
                end_idx = min(start_idx + 32, content_nums)
                for doc in docs[start_idx:end_idx]:
                    emb = self.embedding.encode(doc)                     
                    embeddings.append(emb)
                pbar.update(end_idx - start_idx)
        
        embeddings = np.vstack(embeddings)

        # Prepare actions for bulk indexing
        actions = []
        for i, doc in enumerate(docs):
            # Use a combination of id and a hash of the text as the unique identifier
            doc_id = hashlib.sha256(doc.encode()).hexdigest()
            unique_id = f"{id}_{doc_id}"
            action = {
                '_op_type': 'update',
                '_index': self.index_name,
                '_id': unique_id,
                'doc_as_upsert': True,  # Use upsert
                'doc': {
                    'text': doc,
                    'vector': embeddings[i].tolist(),
                    'metadata': {
                        'source': source,
                        'id': id,
                        'project_name': project_name
                    }
                }
            }
            actions.append(action)

        # Perform bulk indexing
        with tqdm(total=len(actions), desc='Bulk indexing') as pbar:
            success, _ = bulk(self.client, actions, raise_on_error=False)
            pbar.update(success)

        log.info('End add_documents')


    def del_document(self, id):
        query = {
            "query": {
                "term": {
                    "metadata.id": id
                }
            }
        }

        response = self.client.delete_by_query(index=self.index_name, body=query)
        return response
    
    
    def doc_upload(self, file, chunk_size, chunk_overlap):
        log.info(f'begin doc_upload {file}')
        try:
            filename = os.path.split(file.name)[-1]
            file_path = 'data/tmp_files/' + filename
            shutil.move(file.name, file_path)
            file_path = update(file_path)
            content, docs,source,id = load_file(file_path, chunk_size, chunk_overlap)
            self.add_documents(docs, source, id)
            log.info(f'end doc_upload {filename}')
            return "插入成功"
        except Exception as e:
            log.error(f'failed doc_upload {e}')
            return "插入失败{e}"


    def add_file(self, file, chunk_size = config.get("chunk_size"), chunk_overlap=config.get("chunk_overlap"), project_name = ""):
        log.info(f'begin add_file {file}')
        try:
            filename = os.path.split(file)[-1]
            file_path = 'data/tmp_files/' + filename
            shutil.move(file, file_path)
            file_path = update(file_path)
            content, docs, source, id = load_file(file_path, chunk_size, chunk_overlap)
            if len(docs) == 0:
                log.error(f'failed add_file {source} doc is luanma')
                return False
            self.add_documents(docs, source, id, project_name)
            log.info(f'end add_file {file}')
            return True
        except Exception as e:
            log.error(f'failed add_file {e}')
            return False

    def assembly_file(self, url, content,doc_id, project_name):
        log.info(f'begin assembly {url}')
        try:
            name = Path(url).name
            chunk_size = config.get("chunk_size")
            chunk_overlap=config.get("chunk_overlap")
            docs = split_text2(content, chunk_size, chunk_overlap)
            if len(docs) == 0:
                log.error(f'failed assembly_file {name}')
                return False
            self.upsert_documents(docs, name, doc_id, project_name=project_name)
            log.info(f'success assembly_file {url} with {doc_id}')
            return True
        except Exception as e:
            log.error(f'failed assembly_file {url} {e}')
            return False
    
    
    
    
    
        
    def doc_extract(self, project_name, key, item):
        try:
            ranks = []
            source = {}
            keywords = item.get("keywords", [])
            if keywords and isinstance(keywords, list):
                # 过滤掉None值
                valid_keywords = [str(k) for k in keywords if k is not None]
                query = " ".join(valid_keywords)
            else:
                query = ""
            search_body = generate_extract_body(project_name, keywords, 20)
            print(search_body)
            response = self.client.search(index=es_cfg.get("index_name"), body=search_body)
            hits = [hit for hit in response["hits"]["hits"]]
            seen_content = set()
            info_context = ''
            max_info_length = 16 * 1024
            
            for i in hits:
                content = i['_source']['text']
                if content not in seen_content:
                    seen_content.add(content)
                    ranks.append(content)
                    source[content] = i['_source']['metadata']['source']+"/"+i['_source']['metadata']['id']

            ranks = self.embedding.rerankllm(item.get("query"),ranks)
            for text in ranks:
                # log.success(f"use {source[text]} {text}")
                content = text.replace('|', '').replace('||', '')
                if len(info_context.encode('utf-8')) + len(content.encode('utf-8')) <= max_info_length:
                    info_context += content + '\n'
                else:
                    break 
                  
            # ranks = reranker.rerank(ranks, query)
            # log.info(f"{key}----{item}\n")
            # for rank in ranks:
            #     log.info(f"{rank} ---> {source[rank]}")
            # for text in ranks:
            #     log.success(f"use {text}")
            #     content = text.replace('|', '').replace('||', '')
            #     if len(info_context.encode('utf-8')) + len(content.encode('utf-8')) <= max_info_length:
            #         info_context += content + '\n'
            #     else:
            #         break 

            info_context = info_context.strip()
            summary = get_sumary(info_context, key, keywords, item.get("hint"))
            return summary
        except Exception as e:
            print(e)
            return ""



    def doc_search(self, method, query,project_name, top_k, knn_boost):
        log.info("begin doc search")
        try:
            result = []
            ranks = []
            source = {}
            # query_vector = self.embedding.encode([query])
            query_vector = self.embedding.encode(query)
            if method == "近似查询":
                query_body = generate_knn_query(vec=query_vector, size=top_k*10)
            elif method == "混合查询":
                query_body = generate_hybrid_query(text=query, vec=query_vector, size=top_k*10, knn_boost=knn_boost)
            elif method == "project":
                query_body = generate_project_body(project_name=project_name,vec= query_vector, size = top_k*10)
            else:
                query_body = generate_search_query(vec=query_vector, size=top_k*10)
            response = self.client.search(index=self.index_name, body=query_body)
            hits = [hit for hit in response["hits"]["hits"]]
            seen_content = set()
            for i in hits:
                content = i['_source']['text']
                if content not in seen_content:
                    seen_content.add(content)
                    ranks.append(content)
                    source[content] = i['_source']['metadata']['source']
            log.info("end doc search")
            
            if len(ranks) > 0:
                m = BM25Similarity()
                m.add_corpus(ranks)
                ranks = m.search(query=query, topn= top_k)
                for rank in ranks:
                    for text,score in rank.items():
                        result.append({
                                    'content': text,
                                    'source': source[text],
                                    'score': score
                                })
            return result
        except Exception as e:
            self.client = Elasticsearch(['http://{}:{}'.format(es_cfg.get("url"), es_cfg.get("port"))],
                                    basic_auth=(es_cfg.get("username"), es_cfg.get("passwd")),
                                    verify_certs=False)
            log.error(f'failed doc_search {e}')

    def batch_upsert_documents(self, docs, project_name):
        """批量插入或更新文档到ES"""
        try:
            actions = []
            for doc in docs:
                action = {
                    "_index": self.index_name,
                    "_source": {
                        "content": doc.get("content", ""),
                        "embedding": doc.get("embedding", []),
                        "metadata": doc.get("metadata", {})
                    }
                }
                actions.append(action)

            if actions:
                bulk(self.client, actions)
                log.info(f"批量插入 {len(actions)} 个文档到ES")

        except Exception as e:
            log.error(f"批量插入文档失败: {e}")


# 为了兼容性，添加DocSearch别名
DocSearch = ES