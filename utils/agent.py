import sqlite3
import json
from collections import deque
from fastapi import FastAPI, Request
from pydantic import BaseModel
from utils.config import config,es_cfg,model_cfg
# from doc_search import ES
from utils.cdb import Database
from elasticsearch import Elasticsearch
# from utils.sentence_vector import OSentenceVector
from prompts import Prompts
from termcolor import colored 
import yaml
import re
from typing import List, Dict
from utils.utils import get_date, load_json
from utils.llm import LLM
from utils.eskg import ESKnowledge
from utils.sqkg import SQLKnowledge
import asyncio

def print_msg(message):
    role_to_color = {
        "system": "red",
        "user": "green",
        "assistant": "blue",
        "function": "magenta",
    }
    if message["role"] == "system":
        print(colored(f"system: {message['content']}\n", role_to_color[message["role"]]))
    elif message["role"] == "user":
        print(colored(f"user: {message['content']}\n", role_to_color[message["role"]]))
    elif message["role"] == "assistant" and message.get("tool_calls"):
        print(colored(f"""assistant: {message["tool_calls"][0]["function"]["name"]}\n""", role_to_color[message["role"]]))
    elif message["role"] == "assistant" and not message.get("tool_calls"):
        print(colored(f"assistant: {message['content']}\n", role_to_color[message["role"]]))
    elif message["role"] == "function":
        print(colored(f"function ({message['name']}): {message['content']}\n", role_to_color[message["role"]]))


def load_tasks_from_yaml(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def query_task(query, tasks):
    longest_keyword = ""
    matching_task = None
    
    for task in tasks['tasks']:
        for keyword in task['keywords']:
            if keyword in query and len(keyword) > len(longest_keyword):
                longest_keyword = keyword
                matching_task = task
    
    return matching_task


def generate_prompt(task, text):
    if task:
        prompt_template = task['prompt_template']
        return prompt_template.format(text=text)
    else:
        return "请提供更多详细信息。"


class Agent:
    def __init__(self, system_prompt="You are an assistant that helps users retrieve information."):
        self.system_prompt = system_prompt
        self.sql_knowledge = SQLKnowledge(config.get('sqlite.db_path'))
        self.es_knowledge = ESKnowledge(config.get('elasticsearch'))
        # self.embedding = OSentenceVector()
        self.model = config.get("llm.model")
        self.messages = deque(maxlen=20)  # Remember last 20 messages
        self.llm = LLM(
            api_url=config.get('llm.api_url'),
            token=config.get('llm.token'),
            model=self.model,
            temperature=config.get('llm.temperature', 0.6),
            max_tokens=config.get('llm.max_tokens', 16384)
        )
        # self.tasks_config = load_tasks_from_yaml(config.get("tasks_conf"))

        self.project_schema = """
        The table `project_extract` has the following columns:
        - id: INTEGER PRIMARY KEY
        - name: TEXT NOT NULL (项目名称)
        - start_date: DATE NOT NULL (开始日期)
        - end_date: DATE NOT NULL (结束日期)
        - total_investment: INTEGER NOT NULL (总投资)
        - responsible_unit: TEXT NOT NULL (承担单位)
        - leader: TEXT NOT NULL (负责人)
        - research_points: TEXT (主要研究点)
        - innovation: TEXT (创新点)
        - main_deliverables: TEXT (主要交付成果)
        - patent: TEXT (专利)
        """

        self.conference_schema = """
        The table `conference_extract` has the following columns:
        - id: INTEGER PRIMARY KEY (文书的ID)
        - name: TEXT NOT NULL (文书名称)
        - date: TEXT (书的发起时间)
        - type: TEXT (文书的类型)
        - organizer: TEXT (文书的发起组织)
        - participants: TEXT (文书的接收组织和个人)
        - summary: TEXT (文书的摘要)
        """

    def generate_llm_prompt(self, user_query: str, promptName: str) -> str:
        """
        生成LLM用于解析用户意图的提示
        """
        # Determine table based on promptName
        target_table = "project_extract" if promptName == "项目大事记" else "conference_extract"
        
        # Only include relevant schema based on the target table
        schema = self.project_schema if target_table == "project_extract" else self.conference_schema
        
        prompt = f"""
        请根据以下用户查询，生成相应的SQL查询语句。

        用户查询："{user_query}"

        数据库表结构：
        {schema}

        规则：
        1. 使用'{target_table}'表进行查询
        2. 日期条件统一使用 {'start_date/end_date' if target_table == 'project_extract' else 'date'} 字段
        3. 默认返回所有字段（SELECT *）

        输出格式：
        {{
            "table": "{target_table}",
            "query": "SELECT * FROM {target_table} WHERE <条件>"
        }}

        请直接按照上述输出格式返回，不要包含多余的解释。
        """
        return prompt
    
    async def generate_project_summary(self, project_record):
        prompt = Prompts.get_prompt("科技项目汇编(patent-modify)")
        context = f"""
        根据以下项目记录，生成一个详细的项目总结。总结应包括项目的背景、目标、实施步骤、成果和影响等方面。

        项目名称：{project_record['name']}
        开始日期：{project_record['start_date']}
        结束日期：{project_record['end_date']}
        总投资：{project_record['total_investment']}
        承担单位：{project_record['responsible_unit']}
        负责人：{project_record['leader']}
        主要研究点：{project_record['research_points']}
        创新点：{project_record['innovation']}
        主要交付成果：{project_record['main_deliverables']}
        专利：{project_record['patent']}

        """
        prompt = prompt.format_map({"context":context})
        messages=[{"role": "user", "content": prompt}]
        response = await self.llm.chat(messages)
        return response



    async def compile_summaries(self, summaries: List[str], user_query: str) -> str:
        """
        将所有总结文本拼接成一个汇编总结
        """
        initial_sentence = await self.generate_initial_sentence(user_query)
        compiled_summary = f"{initial_sentence.replace('回答：', '')}\n"
        
        for idx, summary in enumerate(summaries, 1):
            compiled_summary += f"{summary}\n\n"
        
        return compiled_summary

    async def generate_initial_sentence(self, user_query: str) -> str:
        """
        使用LLM生成回答的起始语句
        
        Args:
            user_query: 用户的问题
            
        Returns:
            str: 回答的起始语句
        """
        prompt = f"""请根据以下问题，生成一个回答的开场句。要求：
    1. 假设你已经知道答案
    2. 使用肯定和专业的语气
    3. 不要给出具体内容，只给出开场白
    4. 句子要简短自然
    5. 直接返回句子，不要解释

    问题：{user_query}

    示例：
    问题：这个项目的投资金额是多少？
    回答：根据项目文件显示，该项目的总投资金额为

    问题：项目的研究内容有哪些？
    回答：该项目主要包含以下研究内容："""

        messages = [
            {
                "role": "system",
                "content": "你是一个专业的回答助手，请生成简洁的开场白。"
            },
            {
                "role": "user", 
                "content": prompt
            }
        ]
        
        response = await self.llm.chat(messages)
        return response.strip()

    async def handle_note_query(self, user_query: str,promptName: str, prompt_template: str) -> str:
        """
        处理用户查询，返回相关的项目或会议总结
        """
        # 1. 生成LLM提示并获取响应
        prompt = self.generate_llm_prompt(user_query, promptName)
        messages=[
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": prompt}
        ]
        response = await self.llm.chat(messages)

        # 2. 解析LLM响应
        try:
            parsed_data = load_json(response)
        except json.JSONDecodeError as e:
            print(e)
            return "llm response wrong {response}"
        
        print(parsed_data)
        table = parsed_data.get("table")
        sql_query = parsed_data.get("query")

        if not table or not sql_query:
            return "无法理解您的查询。请尝试重新描述。"

        # 3. 执行SQL查询
        try:
            record_dicts = self.sql_knowledge.query(sql_query)
            if not record_dicts:
                return f"在指定的条件下未找到任何记录。"
            
            category_data = {
                    "问责处分": [],
                    "人事任免": [],
                    "表彰获奖": [],
                    "表彰获奖": [],
                    "决策会议": [],
                    "印发文件": []
            }

            summaries = []
            if table == 'project_extract':
                for record in record_dicts:
                    summary = await self.generate_project_summary(record)
                    summaries.append(summary)
            elif table == 'conference_extract':
                for record in record_dicts:
                    record_type = record.get('type')
                    if record_type == "决策会议":
                        category_data["决策会议"].append(record)
                    elif record_type == "印发文件":
                        category_data["印发文件"].append(record)
                    elif record_type == "人事任免":
                        category_data["人事任免"].append(record)
                    elif record_type == "表彰获奖":
                        category_data["表彰获奖"].append(record)
                    elif record_type == "问责处分":
                        category_data["问责处分"].append(record)
                summary = self.generate_conference_summary(category_data)
                summaries.append(summary)
            else:
                summary = "未知的表格类型。"
                summaries.append(summary)
              
            # 6. 拼接所有总结
            compiled_summary = await self.compile_summaries(summaries, user_query)
            return compiled_summary

        except Exception as e:
            print(f"执行SQL查询时出错: {str(e)}")
            return "在处理您的查询时发生错误。请稍后再试。"
        
    def generate_conference_summary(self, category_data) -> str:
        """
        根据各类别的数据生成会议总结
        """
        document = "# 公司重要会议、干部人事任免及调动、表彰奖惩信息等大事记\n\n"

        # （一）重要决策会议
        if category_data["决策会议"]:
            document += "## （一）重要决策会议:\n"
            for idx, record in enumerate(category_data["决策会议"], 1):
                document += f"1. **{get_date(record['date'])}**，召开了《{record['name']}》，由{record['organizer']}发起，参与者包括{record['participants']}，摘要：{record['summary']}\n\n"
        else:
            document += "## （一）重要决策会议:\n无记录。\n\n"

        # （二）印发重要文件
        if category_data["印发文件"]:
            document += "## （二）印发重要文件:\n"
            for idx, record in enumerate(category_data["印发文件"], 1):
                document += f"1. **{get_date(record['date'])}**，印发《{record['name']}》文件；发起组织：{record['organizer']}，摘要：{record['summary']}\n\n"
        else:
            document += "## （二）印发重要文件:\n经查找，无印发文件信息。\n\n"

        # （三）干部人事任免情况
        if category_data["人事任免"]:
            document += "## （三）干部人事任免情况:\n"
            for idx, record in enumerate(category_data["人事任免"], 1):
                document += f"1. **{get_date(record['date'])}**，{record['summary']}\n\n"
        else:
            document += "## （三）干部人事任免情况:\n经查找，无干部人事任免信息。\n\n"

        # （四）表彰获奖情况
        if category_data["表彰获奖"]:
            document += "## （四）表彰获奖情况:\n"
            for idx, record in enumerate(category_data["表彰获奖"], 1):
                document += f"1. **{get_date(record['date'])}**，{record['summary']}\n\n"
        else:
            document += "## （四）表彰获奖情况:\n经查找，无获奖、表彰信息。\n\n"

        # （五）问责处分情况
        if category_data["问责处分"]:
            document += "## （五）问责处分情况:\n"
            for idx, record in enumerate(category_data["问责处分"], 1):
                document += f"1. **{get_date(record['date'])}**，{record['summary']}\n\n"
        else:
            document += "## （五）问责处分情况:\n经查找，无处分、问责信息。\n\n"

        return document
        
    def add_message(self, role, content):
        msg = {"role": role, "content": content}
        print_msg(msg)
        self.messages.append(msg)

    def change_system_prompt(self, new_prompt: str):
        self.system_prompt = new_prompt

    def get_messages(self):
        return [{"role": "system", "content": self.system_prompt}] + list(self.messages)
    
    async def get_query_info(self, user_query):
        sys_prompt = f"""
            You are an expert in information retrieval. Your task is to analyze the user's query and extract the project name and related key information.
            
            User Query: "{user_query}"
            
            Please provide the project name (if any) and other key information in the following JSON format:
            
            {{
                "project_name": "extracted_project_name",
                "keywords": "extracted_key_information"
            }}

            其中:
            extracted_project_name为能提取到的最短项目名称
            extracted_key_information为用户查询的目的信息关键字
        """
        messages = [{"role":"user", "content":sys_prompt}]
        response = await self.llm.chat(messages)
        try:
            parsed_json = load_json(response)
        except json.JSONDecodeError as e:
            print(e)
            return None
        return parsed_json



   

    async def query_to_sql(self, query):
        prompt = f"""
        Convert the following natural language query into a SQL query based on the provided schema.
        
        Schema:
        {self.project_schema}
        
        Query: "{query}"
        
        Provide only the SQL query in response.
        """
        
        messages=[
            {"role": "system", "content": "You are an expert database engineer."},
            {"role": "user", "content": prompt}
        ]
        response = await self.llm.chat(messages)
        sql_query = response.strip()
        sql_pattern = re.compile(r'SELECT[\s\S]*?;', re.IGNORECASE)
        match = sql_pattern.search(sql_query)
        if match:
            return match.group(0).strip()
        else:
            return None

    async def query_sqlite(self, query):
        try:
            sql_query = await self.query_to_sql(query)
            results = self.sql_knowledge.query(sql_query)
            return f"根据{query}查库获得信息：{results}"
        except Exception as e:
            return f"query failed with error: {e}"

    # def search_elastic(self, info, query):
    #     vec = self.embedding.encode(query)
    #     matches = [{'match': {'text': key}} for key in info["keywords"]]
    #     query = {
    #         "query": {
    #             "bool": {
    #                 "must": [
    #                     {
    #                         "match": {
    #                             "metadata.project_name": {
    #                                 "query": info["project_name"],
    #                                 "fuzziness": "AUTO"
    #                             }
    #                         }
    #                     },
    #                     {
    #                         "script_score": {
    #                             "query": {
    #                                 "match_all": {}
    #                             },
    #                             "script": {
    #                                 "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
    #                                 "params": {
    #                                     "query_vector": vec
    #                                 }
    #                             }
    #                         }
    #                     }
    #                 ],
    #                 "should":[]
    #             }
    #         },
    #         "size": 10
    #     }
        
    #     query["query"]["bool"]["should"].extend(matches)
    #     hits = self.es_knowledge.search(query)['hits']['hits']
    #     contents = ""
    #     for i in hits:
    #         content = i['_source']['text']
    #         contents += content
    #     return contents

    # async def process_request(self, user_input):
    #     # Process the user input to determine what actions to take
    #     info = await self.get_query_info(user_input)
    #     if not info:
    #         return "请你提供更多信息"
    #     print(info)
    #     if info["project_name"] == "":
    #         info = await self.query_sqlite(user_input)
    #         self.add_message("user", f"已知信息：{info}")
    #     else:
    #         task = query_task(user_input, self.tasks_config)
    #         if not task:
    #             return "请你提供更多信息"
    #         keywords = task["keywords"]
    #         if "keywords" not in info or not isinstance(info["keywords"], list):
    #             info["keywords"] = [] 
    #         info["keywords"].extend(keywords)
    #         print(info["keywords"])
    #         text = self.search_elastic(info, user_input)
    #         prompt = generate_prompt(task, text)
    #         self.add_message("user", prompt)
    #         response = await self.llm.chat(self.get_messages())
    #         self.add_message("user", f"已知信息：{response}")

    #     self.add_message("user", user_input)
    #     response = await self.llm.chat(self.get_messages())
    #     self.add_message("assistant", response)
    #     return response

    def __del__(self):
        if hasattr(self, 'sql_knowledge'):
            self.sql_knowledge.close()


# # user_querys = ["项目参与单位有哪些","这个项目申报了哪些专利","这个项目投资金额多少","这个项目负责人是谁","项目什么时候开始的","项目什么时候结束的","项目有哪些创新点","项目有哪些成果","项目主要研究了什么"]
# user_querys = ["项目的财务结算报告详细信息"]
# # projects = ["海南电网三道防线仿真建模研究及安全稳定应用","数字化变电站可视化及间隔自动测试技术的研究与应用","敏感用户工业过程电压暂降耐受能力测试平台研究与应用"]
# projects = ["海南电网三道防线仿真建模研究及安全稳定应用"]
# queryTemplete = "请问{project}，{query}"

# if __name__=="__main__":
#     async def main():
#         agent = Agent()
#         try:
#             natural_language_query = "请你汇编投资额大于100万的项目"
#             res = await agent.handle_user_query(natural_language_query)
#             print(res)

#             natural_language_query = "请你汇编2018-2022年所有文书"
#             res = await agent.handle_user_query(natural_language_query)
#             print(res)
#         finally:
#             if hasattr(agent, 'sql_knowledge'):
#                 agent.sql_knowledge.close()

#     asyncio.run(main())
    