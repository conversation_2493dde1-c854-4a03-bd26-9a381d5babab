#!/usr/bin/env python3
"""
智能多维度检索引擎
基于ES实现多维度检索策略，支持语义检索、关键词检索、文档类型过滤等
"""

import asyncio
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass

from utils.llm import LLM
from utils.doc_search import DocSearch
from utils.logger import log


@dataclass
class SearchFilter:
    """搜索过滤器"""
    project_name: Optional[str] = None
    doc_types: Optional[List[str]] = None
    date_range: Optional[Dict[str, str]] = None  # {"start": "2023-01-01", "end": "2023-12-31"}
    keywords: Optional[List[str]] = None
    sources: Optional[List[str]] = None


@dataclass
class SearchResult:
    """搜索结果"""
    content: str
    score: float
    metadata: Dict[str, Any]
    doc_id: str
    source: str
    doc_types: List[str]
    chunk_index: int


class SmartRetriever:
    """智能检索引擎"""
    
    def __init__(self):
        self.llm = LLM()
        self.doc_search = DocSearch()
        
        # 检索策略权重配置
        self.strategy_weights = {
            "semantic": 0.6,      # 语义检索权重
            "keyword": 0.3,       # 关键词检索权重
            "hybrid": 0.1         # 混合检索权重
        }
        
        # 检索参数配置
        self.default_top_k = 20
        self.max_results = 100
        self.min_score_threshold = 0.1
        
    async def smart_search(
        self,
        query: str,
        search_filter: SearchFilter = None,
        strategy: str = "auto",
        top_k: int = None,
        include_metadata: bool = True
    ) -> List[SearchResult]:
        """
        智能搜索接口
        
        Args:
            query: 搜索查询
            search_filter: 搜索过滤器
            strategy: 检索策略 ("semantic", "keyword", "hybrid", "auto")
            top_k: 返回结果数量
            include_metadata: 是否包含元数据
            
        Returns:
            搜索结果列表
        """
        if not query.strip():
            return []
        
        top_k = top_k or self.default_top_k
        search_filter = search_filter or SearchFilter()
        
        try:
            # 1. 查询意图分析
            if strategy == "auto":
                strategy = await self._analyze_query_intent(query)
            
            # 2. 构建ES查询
            es_query = await self._build_es_query(query, search_filter, strategy)
            
            # 3. 执行搜索
            raw_results = await self._execute_search(es_query, top_k * 2)  # 获取更多结果用于重排序
            
            # 4. 结果后处理和重排序
            processed_results = await self._post_process_results(
                raw_results, query, strategy, top_k
            )
            
            # 5. 转换为标准格式
            search_results = self._convert_to_search_results(processed_results, include_metadata)
            
            log.info(f"智能搜索完成: query='{query}', strategy='{strategy}', results={len(search_results)}")
            return search_results
            
        except Exception as e:
            log.error(f"智能搜索失败: {e}")
            return []
    
    async def _analyze_query_intent(self, query: str) -> str:
        """分析查询意图，选择最佳检索策略"""
        try:
            intent_prompt = f"""
分析以下查询的意图，选择最佳的检索策略：

查询: {query}

检索策略说明：
- semantic: 适用于概念性、语义性查询，如"项目的技术方案"、"会议的主要内容"
- keyword: 适用于精确匹配查询，如具体的人名、地名、时间、编号等
- hybrid: 适用于复合查询，既包含概念又包含具体信息

请直接返回最佳策略名称（semantic/keyword/hybrid），不要解释。
"""
            
            response = await self.llm.chat([
                {
                    "role": "system",
                    "content": "你是一个查询意图分析助手。请直接返回策略名称。"
                },
                {"role": "user", "content": intent_prompt}
            ])
            
            strategy = response.strip().lower()
            if strategy in ["semantic", "keyword", "hybrid"]:
                return strategy
            else:
                return "semantic"  # 默认使用语义检索
                
        except Exception as e:
            log.error(f"查询意图分析失败: {e}")
            return "semantic"
    
    async def _build_es_query(
        self,
        query: str,
        search_filter: SearchFilter,
        strategy: str
    ) -> Dict[str, Any]:
        """构建ES查询"""
        
        # 基础查询结构
        es_query = {
            "query": {
                "bool": {
                    "must": [],
                    "filter": [],
                    "should": [],
                    "minimum_should_match": 0
                }
            },
            "highlight": {
                "fields": {
                    "content": {
                        "fragment_size": 150,
                        "number_of_fragments": 3
                    }
                }
            },
            "_source": ["content", "metadata", "embedding"]
        }
        
        # 1. 根据策略构建主查询
        if strategy == "semantic":
            # 语义检索：使用向量相似度
            query_vector = await self.llm.get_embedding(query)
            es_query["query"]["bool"]["must"].append({
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                        "params": {"query_vector": query_vector}
                    }
                }
            })
            
        elif strategy == "keyword":
            # 关键词检索：使用文本匹配
            es_query["query"]["bool"]["must"].append({
                "multi_match": {
                    "query": query,
                    "fields": ["content^2", "metadata.source"],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })
            
        elif strategy == "hybrid":
            # 混合检索：结合向量和文本
            query_vector = await self.llm.get_embedding(query)
            
            # 语义匹配
            es_query["query"]["bool"]["should"].append({
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                        "params": {"query_vector": query_vector}
                    },
                    "boost": 0.6
                }
            })
            
            # 文本匹配
            es_query["query"]["bool"]["should"].append({
                "multi_match": {
                    "query": query,
                    "fields": ["content^2", "metadata.source"],
                    "type": "best_fields",
                    "fuzziness": "AUTO",
                    "boost": 0.4
                }
            })
            
            es_query["query"]["bool"]["minimum_should_match"] = 1
        
        # 2. 添加过滤条件
        if search_filter.project_name:
            es_query["query"]["bool"]["filter"].append({
                "term": {"metadata.project_name": search_filter.project_name}
            })
        
        if search_filter.doc_types:
            es_query["query"]["bool"]["filter"].append({
                "terms": {"metadata.doc_types.keyword": search_filter.doc_types}
            })
        
        if search_filter.date_range:
            date_filter = {"range": {"metadata.created_at": {}}}
            if search_filter.date_range.get("start"):
                date_filter["range"]["metadata.created_at"]["gte"] = search_filter.date_range["start"]
            if search_filter.date_range.get("end"):
                date_filter["range"]["metadata.created_at"]["lte"] = search_filter.date_range["end"]
            es_query["query"]["bool"]["filter"].append(date_filter)
        
        if search_filter.keywords:
            for keyword in search_filter.keywords:
                es_query["query"]["bool"]["filter"].append({
                    "match": {"content": keyword}
                })
        
        if search_filter.sources:
            es_query["query"]["bool"]["filter"].append({
                "terms": {"metadata.source.keyword": search_filter.sources}
            })
        
        return es_query
    
    async def _execute_search(self, es_query: Dict[str, Any], size: int) -> List[Dict[str, Any]]:
        """执行ES搜索"""
        try:
            response = self.doc_search.client.search(
                index=self.doc_search.index_name,
                body=es_query,
                size=size
            )
            
            results = []
            for hit in response["hits"]["hits"]:
                result = {
                    "content": hit["_source"]["content"],
                    "score": hit["_score"],
                    "metadata": hit["_source"]["metadata"],
                    "highlight": hit.get("highlight", {})
                }
                results.append(result)
            
            return results
            
        except Exception as e:
            log.error(f"ES搜索执行失败: {e}")
            return []
    
    async def _post_process_results(
        self,
        raw_results: List[Dict[str, Any]],
        query: str,
        strategy: str,
        top_k: int
    ) -> List[Dict[str, Any]]:
        """结果后处理和重排序"""
        if not raw_results:
            return []
        
        try:
            # 1. 过滤低分结果
            filtered_results = [
                result for result in raw_results
                if result["score"] >= self.min_score_threshold
            ]
            
            # 2. 去重处理（基于内容相似度）
            deduplicated_results = await self._deduplicate_results(filtered_results)
            
            # 3. 重排序（可选：使用更复杂的排序算法）
            if strategy == "hybrid":
                reranked_results = await self._rerank_results(deduplicated_results, query)
            else:
                reranked_results = deduplicated_results
            
            # 4. 返回top_k结果
            return reranked_results[:top_k]
            
        except Exception as e:
            log.error(f"结果后处理失败: {e}")
            return raw_results[:top_k]
    
    async def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重处理"""
        if len(results) <= 1:
            return results
        
        deduplicated = []
        seen_contents = set()
        
        for result in results:
            content = result["content"]
            
            # 简单的内容去重（基于内容hash）
            content_hash = hash(content[:200])  # 使用前200字符计算hash
            
            if content_hash not in seen_contents:
                seen_contents.add(content_hash)
                deduplicated.append(result)
        
        return deduplicated
    
    async def _rerank_results(
        self,
        results: List[Dict[str, Any]],
        query: str
    ) -> List[Dict[str, Any]]:
        """使用LLM重排序结果"""
        if len(results) <= 3:
            return results
        
        try:
            # 构建重排序提示词
            rerank_prompt = self._build_rerank_prompt(results[:10], query)  # 只对前10个结果重排序
            
            response = await self.llm.chat([
                {
                    "role": "system",
                    "content": "你是一个搜索结果排序助手。请根据查询相关性对结果进行排序。"
                },
                {"role": "user", "content": rerank_prompt}
            ])
            
            # 解析排序结果
            reranked_indices = self._parse_rerank_response(response)
            
            # 重新排序
            reranked_results = []
            for idx in reranked_indices:
                if 0 <= idx < len(results):
                    reranked_results.append(results[idx])
            
            # 添加未排序的结果
            remaining_results = [
                results[i] for i in range(len(results))
                if i not in reranked_indices
            ]
            reranked_results.extend(remaining_results)
            
            return reranked_results
            
        except Exception as e:
            log.error(f"结果重排序失败: {e}")
            return results
    
    def _build_rerank_prompt(self, results: List[Dict[str, Any]], query: str) -> str:
        """构建重排序提示词"""
        prompt_parts = [
            f"查询: {query}\n",
            "请根据与查询的相关性对以下搜索结果进行排序（从最相关到最不相关）：\n"
        ]
        
        for i, result in enumerate(results):
            content_preview = result["content"][:200] + "..." if len(result["content"]) > 200 else result["content"]
            prompt_parts.append(f"结果{i}: {content_preview}\n")
        
        prompt_parts.append("\n请返回排序后的结果编号，用逗号分隔，如：2,0,4,1,3")
        
        return "\n".join(prompt_parts)
    
    def _parse_rerank_response(self, response: str) -> List[int]:
        """解析重排序响应"""
        try:
            # 提取数字序列
            numbers = re.findall(r'\d+', response)
            return [int(num) for num in numbers]
        except:
            return []
    
    def _convert_to_search_results(
        self,
        processed_results: List[Dict[str, Any]],
        include_metadata: bool
    ) -> List[SearchResult]:
        """转换为标准搜索结果格式"""
        search_results = []
        
        for result in processed_results:
            metadata = result.get("metadata", {})
            
            search_result = SearchResult(
                content=result["content"],
                score=result["score"],
                metadata=metadata if include_metadata else {},
                doc_id=metadata.get("doc_id", ""),
                source=metadata.get("source", ""),
                doc_types=metadata.get("doc_types", []),
                chunk_index=metadata.get("chunk_index", 0)
            )
            
            search_results.append(search_result)
        
        return search_results
    
    async def get_project_summary(self, project_name: str) -> Dict[str, Any]:
        """获取项目摘要信息"""
        try:
            # 构建聚合查询
            agg_query = {
                "query": {
                    "term": {"metadata.project_name": project_name}
                },
                "aggs": {
                    "doc_types": {
                        "terms": {"field": "metadata.doc_types.keyword", "size": 20}
                    },
                    "sources": {
                        "terms": {"field": "metadata.source.keyword", "size": 50}
                    },
                    "total_docs": {
                        "cardinality": {"field": "metadata.doc_id.keyword"}
                    }
                },
                "size": 0
            }
            
            response = self.doc_search.client.search(
                index=self.doc_search.index_name,
                body=agg_query
            )
            
            return {
                "project_name": project_name,
                "total_chunks": response["hits"]["total"]["value"],
                "total_documents": response["aggregations"]["total_docs"]["value"],
                "doc_types": [
                    {"type": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in response["aggregations"]["doc_types"]["buckets"]
                ],
                "sources": [
                    {"source": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in response["aggregations"]["sources"]["buckets"]
                ]
            }
            
        except Exception as e:
            log.error(f"获取项目摘要失败: {e}")
            return {"error": str(e)}
