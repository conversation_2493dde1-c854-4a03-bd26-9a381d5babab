from enum import Enum

class FieldType(Enum):
    DATE = "date"
    AMOUNT = "amount"
    TEXT = "text"
    LIST = "list"

FIELD_TYPES = {
    "start_date": FieldType.DATE,
    "end_date": FieldType.DATE,
    "date": FieldType.DATE,
    "total_investment": FieldType.AMOUNT,
    "research_points": FieldType.LIST,
    "innovation": FieldType.LIST,
    "main_deliverables": FieldType.LIST,
    "patent": FieldType.LIST,
    "responsible_unit": FieldType.TEXT,
    "leader": FieldType.TEXT,
    "name": FieldType.TEXT
} 