#!/usr/bin/env python3
"""
质量评估器
评估中间结果和最终融合结果的质量
"""

import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from collections import defaultdict

from utils.log import log


class QualityAssessor:
    """质量评估器"""
    
    def __init__(self):
        # 质量评估规则
        self.quality_rules = {
            "project_name": {
                "min_length": 5,
                "max_length": 100,
                "required_patterns": [r'项目|工程|建设|系统|平台'],
                "weight": 0.15
            },
            "total_amount": {
                "required_patterns": [r'\d+.*[万元]'],
                "reasonable_range": (10, 100000),  # 10万到10亿
                "weight": 0.20
            },
            "initiation_date": {
                "required_patterns": [r'\d{4}'],  # 至少包含年份
                "reasonable_range": (2000, 2030),
                "weight": 0.15
            },
            "responsible_unit": {
                "min_length": 3,
                "max_length": 50,
                "weight": 0.10
            },
            "project_leader": {
                "min_length": 2,
                "max_length": 10,
                "required_patterns": [r'^[\u4e00-\u9fa5]{2,4}$'],  # 中文姓名
                "weight": 0.10
            }
        }
    
    def assess_intermediate_result(
        self,
        doc_key: str,
        result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估单个文档的中间结果质量"""
        try:
            extracted_data = result.get("extracted_data", {})
            source = result.get("source", "")
            
            # 计算各维度质量分数
            quality_scores = {}
            total_weight = 0
            weighted_score = 0
            
            for field, data in extracted_data.items():
                if field in self.quality_rules and data and data != "null":
                    field_score = self._assess_field_quality(field, data)
                    quality_scores[field] = field_score
                    
                    weight = self.quality_rules[field]["weight"]
                    weighted_score += field_score * weight
                    total_weight += weight
            
            # 计算整体质量分数
            overall_score = weighted_score / total_weight if total_weight > 0 else 0.0
            
            # 计算完整性分数
            expected_fields = set(self.quality_rules.keys())
            present_fields = set(field for field, data in extracted_data.items() 
                               if data and data != "null")
            completeness_score = len(present_fields & expected_fields) / len(expected_fields)
            
            # 综合评估
            final_score = (overall_score * 0.7 + completeness_score * 0.3)
            
            return {
                "doc_key": doc_key,
                "source": source,
                "overall_quality": round(final_score, 2),
                "field_scores": quality_scores,
                "completeness": round(completeness_score, 2),
                "present_fields": list(present_fields),
                "missing_fields": list(expected_fields - present_fields),
                "assessment_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            log.error(f"评估中间结果质量失败 {doc_key}: {e}")
            return {"error": str(e)}
    
    def _assess_field_quality(self, field: str, value: str) -> float:
        """评估单个字段的质量"""
        if field not in self.quality_rules:
            return 0.5  # 默认分数
        
        rules = self.quality_rules[field]
        score = 1.0
        
        # 检查长度
        if "min_length" in rules:
            if len(value) < rules["min_length"]:
                score *= 0.5
        
        if "max_length" in rules:
            if len(value) > rules["max_length"]:
                score *= 0.8
        
        # 检查必需模式
        if "required_patterns" in rules:
            pattern_matched = False
            for pattern in rules["required_patterns"]:
                if re.search(pattern, value):
                    pattern_matched = True
                    break
            if not pattern_matched:
                score *= 0.3
        
        # 检查合理范围（针对数值字段）
        if "reasonable_range" in rules:
            if field.endswith("_amount"):
                score *= self._assess_amount_reasonableness(value, rules["reasonable_range"])
            elif field.endswith("_date"):
                score *= self._assess_date_reasonableness(value, rules["reasonable_range"])
        
        return round(score, 2)
    
    def _assess_amount_reasonableness(self, amount_str: str, range_tuple: tuple) -> float:
        """评估金额的合理性"""
        try:
            # 提取数字
            numbers = re.findall(r'[\d,]+\.?\d*', amount_str.replace(',', ''))
            if not numbers:
                return 0.2
            
            amount = float(numbers[0])
            
            # 转换为万元单位进行比较
            if '万' in amount_str:
                amount_wan = amount
            else:
                amount_wan = amount / 10000
            
            min_val, max_val = range_tuple
            if min_val <= amount_wan <= max_val:
                return 1.0
            elif amount_wan < min_val:
                return 0.3  # 金额过小
            else:
                return 0.5  # 金额过大但可能合理
                
        except Exception:
            return 0.2
    
    def _assess_date_reasonableness(self, date_str: str, range_tuple: tuple) -> float:
        """评估日期的合理性"""
        try:
            # 提取年份
            year_match = re.search(r'(\d{4})', date_str)
            if not year_match:
                return 0.2
            
            year = int(year_match.group(1))
            min_year, max_year = range_tuple
            
            if min_year <= year <= max_year:
                return 1.0
            else:
                return 0.2
                
        except Exception:
            return 0.2
    
    def assess_fusion_result(
        self,
        fusion_result: Dict[str, Any],
        intermediate_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估融合结果的质量"""
        try:
            merged_data = fusion_result.get("merged_data", {})
            field_analysis = fusion_result.get("field_analysis", {})
            
            # 评估融合质量
            fusion_quality = {}
            
            for field, value in merged_data.items():
                if field in self.quality_rules:
                    # 基础质量分数
                    base_score = self._assess_field_quality(field, value)
                    
                    # 融合置信度
                    analysis = field_analysis.get(field, {})
                    fusion_confidence = analysis.get("confidence", 0.5)
                    
                    # 来源数量加分
                    source_count = analysis.get("source_count", 1)
                    source_bonus = min(source_count * 0.1, 0.3)  # 最多加0.3分
                    
                    # 综合分数
                    final_score = min(base_score + source_bonus, 1.0)
                    
                    fusion_quality[field] = {
                        "base_quality": base_score,
                        "fusion_confidence": fusion_confidence,
                        "source_count": source_count,
                        "final_score": round(final_score, 2),
                        "method": analysis.get("method", "unknown")
                    }
            
            # 计算整体融合质量
            if fusion_quality:
                overall_fusion_score = sum(
                    item["final_score"] * self.quality_rules[field]["weight"]
                    for field, item in fusion_quality.items()
                    if field in self.quality_rules
                ) / sum(
                    self.quality_rules[field]["weight"]
                    for field in fusion_quality.keys()
                    if field in self.quality_rules
                )
            else:
                overall_fusion_score = 0.0
            
            # 评估一致性
            consistency_score = self._assess_consistency(intermediate_results, merged_data)
            
            return {
                "overall_fusion_quality": round(overall_fusion_score, 2),
                "field_quality": fusion_quality,
                "consistency_score": round(consistency_score, 2),
                "source_coverage": len(intermediate_results),
                "field_coverage": len(merged_data),
                "assessment_time": datetime.now().isoformat(),
                "quality_grade": self._get_quality_grade(overall_fusion_score)
            }
            
        except Exception as e:
            log.error(f"评估融合结果质量失败: {e}")
            return {"error": str(e)}
    
    def _assess_consistency(
        self,
        intermediate_results: Dict[str, Any],
        merged_data: Dict[str, Any]
    ) -> float:
        """评估中间结果的一致性"""
        try:
            if len(intermediate_results) < 2:
                return 1.0  # 单个文档，无一致性问题
            
            # 收集每个字段的所有值
            field_values = defaultdict(list)
            for result in intermediate_results.values():
                extracted_data = result.get("extracted_data", {})
                for field, value in extracted_data.items():
                    if value and value != "null":
                        field_values[field].append(value)
            
            # 计算一致性分数
            consistency_scores = []
            for field, values in field_values.items():
                if len(values) > 1:
                    unique_values = len(set(values))
                    total_values = len(values)
                    # 一致性 = 1 - (唯一值数量 - 1) / (总值数量 - 1)
                    field_consistency = 1 - (unique_values - 1) / (total_values - 1)
                    consistency_scores.append(field_consistency)
            
            return sum(consistency_scores) / len(consistency_scores) if consistency_scores else 1.0
            
        except Exception as e:
            log.error(f"评估一致性失败: {e}")
            return 0.5
    
    def _get_quality_grade(self, score: float) -> str:
        """获取质量等级"""
        if score >= 0.9:
            return "优秀"
        elif score >= 0.8:
            return "良好"
        elif score >= 0.7:
            return "中等"
        elif score >= 0.6:
            return "及格"
        else:
            return "需要改进"
    
    def generate_quality_report(
        self,
        intermediate_assessments: List[Dict[str, Any]],
        fusion_assessment: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成质量报告"""
        try:
            # 统计中间结果质量
            intermediate_scores = [
                assessment.get("overall_quality", 0.0)
                for assessment in intermediate_assessments
                if "overall_quality" in assessment
            ]
            
            avg_intermediate_quality = (
                sum(intermediate_scores) / len(intermediate_scores)
                if intermediate_scores else 0.0
            )
            
            # 识别质量问题
            quality_issues = []
            
            # 检查低质量文档
            low_quality_docs = [
                assessment for assessment in intermediate_assessments
                if assessment.get("overall_quality", 0.0) < 0.6
            ]
            
            if low_quality_docs:
                quality_issues.append({
                    "type": "低质量文档",
                    "count": len(low_quality_docs),
                    "details": [doc.get("source", "未知") for doc in low_quality_docs]
                })
            
            # 检查缺失字段
            all_missing_fields = set()
            for assessment in intermediate_assessments:
                missing_fields = assessment.get("missing_fields", [])
                all_missing_fields.update(missing_fields)
            
            if all_missing_fields:
                quality_issues.append({
                    "type": "常见缺失字段",
                    "fields": list(all_missing_fields)
                })
            
            return {
                "summary": {
                    "avg_intermediate_quality": round(avg_intermediate_quality, 2),
                    "fusion_quality": fusion_assessment.get("overall_fusion_quality", 0.0),
                    "consistency_score": fusion_assessment.get("consistency_score", 0.0),
                    "total_documents": len(intermediate_assessments),
                    "quality_grade": fusion_assessment.get("quality_grade", "未知")
                },
                "quality_issues": quality_issues,
                "recommendations": self._generate_recommendations(
                    intermediate_assessments, fusion_assessment
                ),
                "report_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            log.error(f"生成质量报告失败: {e}")
            return {"error": str(e)}
    
    def _generate_recommendations(
        self,
        intermediate_assessments: List[Dict[str, Any]],
        fusion_assessment: Dict[str, Any]
    ) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于整体质量给出建议
        overall_quality = fusion_assessment.get("overall_fusion_quality", 0.0)
        
        if overall_quality < 0.7:
            recommendations.append("建议检查源文档质量，确保包含完整的项目信息")
        
        # 基于一致性给出建议
        consistency = fusion_assessment.get("consistency_score", 0.0)
        if consistency < 0.8:
            recommendations.append("发现信息不一致，建议人工核实关键字段的准确性")
        
        # 基于缺失字段给出建议
        field_quality = fusion_assessment.get("field_quality", {})
        low_quality_fields = [
            field for field, quality in field_quality.items()
            if quality.get("final_score", 0.0) < 0.6
        ]
        
        if low_quality_fields:
            recommendations.append(f"以下字段质量较低，建议重点关注：{', '.join(low_quality_fields)}")
        
        return recommendations
