#!/usr/bin/env python3
"""
高级质量评估器
提供更精细的质量评估指标和分析
"""

import re
from datetime import datetime
from typing import Dict, List, Any, Optional
from collections import defaultdict

from utils.llm import LLM
from utils.log import log
from utils.config import config


class AdvancedQualityAssessor:
    """高级质量评估器"""
    
    def __init__(self):
        self.llm = LLM()
        
        # 评估配置
        self.enable_semantic_quality = config.get("quality_assessor.semantic_quality", True)
        self.enable_historical_comparison = config.get("quality_assessor.historical_comparison", True)
        self.quality_thresholds = config.get("quality_assessor.thresholds", {
            "excellent": 0.95,
            "good": 0.85,
            "acceptable": 0.75,
            "poor": 0.60
        })
        
        # 质量评估维度权重
        self.quality_dimensions = {
            "accuracy": 0.25,      # 准确性
            "completeness": 0.20,  # 完整性
            "consistency": 0.20,   # 一致性
            "reliability": 0.15,   # 可靠性
            "timeliness": 0.10,    # 时效性
            "relevance": 0.10      # 相关性
        }
        
        # 历史质量数据缓存
        self.quality_history = defaultdict(list)
        
        self._initialized = False
    
    async def initialize(self):
        """初始化评估器"""
        if not self._initialized:
            await self._load_quality_history()
            self._initialized = True
            log.info("高级质量评估器初始化完成")
    
    async def comprehensive_quality_assessment(
        self,
        validation_result: Dict[str, Any],
        processing_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """综合质量评估"""
        try:
            await self.initialize()
            
            log.info("开始综合质量评估")
            
            # 基础质量指标
            basic_quality = await self._assess_basic_quality(validation_result)
            
            # 高级质量指标
            advanced_quality = await self._assess_advanced_quality(
                validation_result, processing_context
            )
            
            # 语义质量评估
            semantic_quality = await self._assess_semantic_quality(validation_result)
            
            # 历史对比分析
            historical_analysis = await self._historical_comparison_analysis(
                validation_result, processing_context
            )
            
            # 综合评分计算
            overall_assessment = self._calculate_comprehensive_score(
                basic_quality, advanced_quality, semantic_quality
            )
            
            # 质量改进建议
            improvement_suggestions = await self._generate_improvement_suggestions(
                overall_assessment, validation_result
            )
            
            # 生成最终评估报告
            assessment_report = {
                "project_name": validation_result.get("project_name"),
                "action": validation_result.get("action"),
                "assessment_timestamp": datetime.now().isoformat(),
                
                # 质量评估结果
                "quality_assessment": {
                    "overall_score": overall_assessment["overall_score"],
                    "quality_grade": overall_assessment["quality_grade"],
                    "dimension_scores": overall_assessment["dimension_scores"],
                    "quality_level": self._get_quality_level(overall_assessment["overall_score"])
                },
                
                # 详细分析
                "detailed_analysis": {
                    "basic_quality": basic_quality,
                    "advanced_quality": advanced_quality,
                    "semantic_quality": semantic_quality,
                    "historical_analysis": historical_analysis
                },
                
                # 改进建议
                "improvement_suggestions": improvement_suggestions,
                
                # 元数据
                "assessment_metadata": {
                    "assessor_version": "1.0",
                    "assessment_method": "comprehensive_multi_dimensional"
                }
            }
            
            # 保存质量评估历史
            await self._save_quality_assessment(assessment_report)
            
            log.info(f"综合质量评估完成: 总分 {overall_assessment['overall_score']:.3f}")
            return assessment_report
            
        except Exception as e:
            log.error(f"综合质量评估失败: {e}")
            return {"error": str(e)}
    
    async def _assess_basic_quality(self, validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """基础质量评估"""
        try:
            validated_fields = validation_result.get("validated_fields", {})
            validation_reports = validation_result.get("validation_reports", {})
            
            # 准确性评估
            accuracy_score = self._calculate_accuracy_score(validation_reports)
            
            # 完整性评估
            completeness_score = self._calculate_completeness_score(validated_fields)
            
            # 一致性评估
            consistency_score = self._calculate_consistency_score(
                validation_result.get("cross_validation", {})
            )
            
            # 可靠性评估
            reliability_score = self._calculate_reliability_score(validation_reports)
            
            return {
                "accuracy": round(accuracy_score, 3),
                "completeness": round(completeness_score, 3),
                "consistency": round(consistency_score, 3),
                "reliability": round(reliability_score, 3)
            }
            
        except Exception as e:
            log.error(f"基础质量评估失败: {e}")
            return {"error": str(e)}
    
    async def _assess_advanced_quality(
        self,
        validation_result: Dict[str, Any],
        processing_context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """高级质量评估"""
        try:
            # 时效性评估
            timeliness_score = self._calculate_timeliness_score(processing_context)
            
            # 相关性评估
            relevance_score = await self._calculate_relevance_score(
                validation_result, processing_context
            )
            
            # 质量分布分析
            quality_distribution = self._analyze_quality_distribution(validation_result)
            
            return {
                "timeliness": round(timeliness_score, 3),
                "relevance": round(relevance_score, 3),
                "quality_distribution": quality_distribution
            }
            
        except Exception as e:
            log.error(f"高级质量评估失败: {e}")
            return {"error": str(e)}
    
    async def _assess_semantic_quality(self, validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """语义质量评估"""
        try:
            if not self.enable_semantic_quality:
                return {"enabled": False}
            
            validated_fields = validation_result.get("validated_fields", {})
            
            # 语义一致性检查
            semantic_consistency = await self._check_semantic_consistency(validated_fields)
            
            # 语义完整性检查
            semantic_completeness = self._calculate_completeness_score(validated_fields)
            
            # 综合语义质量分数
            semantic_score = (semantic_consistency * 0.6 + semantic_completeness * 0.4)
            
            return {
                "enabled": True,
                "semantic_score": round(semantic_score, 3),
                "semantic_consistency": round(semantic_consistency, 3),
                "semantic_completeness": round(semantic_completeness, 3)
            }
            
        except Exception as e:
            log.error(f"语义质量评估失败: {e}")
            return {"enabled": True, "error": str(e)}
    
    async def _historical_comparison_analysis(
        self,
        validation_result: Dict[str, Any],
        processing_context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """历史对比分析"""
        try:
            if not self.enable_historical_comparison:
                return {"enabled": False}
            
            action = validation_result.get("action", "")
            historical_data = self.quality_history.get(action, [])
            
            if len(historical_data) < 2:
                return {
                    "enabled": True,
                    "status": "insufficient_historical_data",
                    "historical_count": len(historical_data)
                }
            
            # 计算历史平均质量
            historical_scores = [item["overall_score"] for item in historical_data[-10:]]
            historical_average = sum(historical_scores) / len(historical_scores)
            
            return {
                "enabled": True,
                "historical_average": round(historical_average, 3),
                "historical_data_points": len(historical_data),
                "comparison_period": "last_10_assessments"
            }
            
        except Exception as e:
            log.error(f"历史对比分析失败: {e}")
            return {"enabled": True, "error": str(e)}
    
    def _calculate_accuracy_score(self, validation_reports: Dict[str, Any]) -> float:
        """计算准确性分数"""
        if not validation_reports:
            return 0.5
        
        confidences = []
        for report in validation_reports.values():
            if isinstance(report, dict) and "confidence" in report:
                confidences.append(report["confidence"])
        
        return sum(confidences) / len(confidences) if confidences else 0.5
    
    def _calculate_completeness_score(self, validated_fields: Dict[str, Any]) -> float:
        """计算完整性分数"""
        if not validated_fields:
            return 0.0
        
        total_fields = len(validated_fields)
        valid_fields = len([v for v in validated_fields.values() if v is not None])
        
        return valid_fields / total_fields
    
    def _calculate_consistency_score(self, cross_validation: Dict[str, Any]) -> float:
        """计算一致性分数"""
        if not cross_validation.get("enabled", False):
            return 0.5
        
        return cross_validation.get("overall_consistency", 0.5)
    
    def _calculate_reliability_score(self, validation_reports: Dict[str, Any]) -> float:
        """计算可靠性分数"""
        if not validation_reports:
            return 0.5
        
        valid_count = 0
        total_count = 0
        confidence_sum = 0
        
        for report in validation_reports.values():
            if isinstance(report, dict):
                total_count += 1
                if report.get("is_valid", False):
                    valid_count += 1
                confidence_sum += report.get("confidence", 0)
        
        if total_count == 0:
            return 0.5
        
        validity_ratio = valid_count / total_count
        avg_confidence = confidence_sum / total_count
        
        return (validity_ratio * 0.6 + avg_confidence * 0.4)
    
    def _calculate_timeliness_score(self, processing_context: Optional[Dict[str, Any]]) -> float:
        """计算时效性分数"""
        if not processing_context:
            return 0.8
        
        processing_time = processing_context.get("processing_time_seconds", 30)
        
        if processing_time <= 30:
            return 1.0
        elif processing_time <= 60:
            return 0.9
        elif processing_time <= 120:
            return 0.7
        else:
            return 0.5
    
    async def _calculate_relevance_score(
        self,
        validation_result: Dict[str, Any],
        processing_context: Optional[Dict[str, Any]]
    ) -> float:
        """计算相关性分数"""
        try:
            validated_fields = validation_result.get("validated_fields", {})
            action = validation_result.get("action", "")
            
            # 计算字段相关性
            field_relevance = self._calculate_field_relevance(validated_fields, action)
            
            return field_relevance
            
        except Exception as e:
            log.error(f"计算相关性分数失败: {e}")
            return 0.7
    
    def _calculate_field_relevance(self, validated_fields: Dict[str, Any], action: str) -> float:
        """计算字段相关性"""
        if action == "项目档案":
            required_fields = ["project_name", "responsible_unit", "total_investment"]
            important_fields = ["leader", "start_date", "end_date"]
        elif action == "文书档案":
            required_fields = ["name"]
            important_fields = ["date"]
        else:
            return 0.7
        
        required_score = sum(1 for field in required_fields if validated_fields.get(field)) / len(required_fields)
        important_score = sum(1 for field in important_fields if validated_fields.get(field)) / len(important_fields) if important_fields else 1.0
        
        return required_score * 0.8 + important_score * 0.2

    def _analyze_quality_distribution(self, validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """分析质量分布"""
        validation_reports = validation_result.get("validation_reports", {})

        if not validation_reports:
            return {"status": "no_validation_data"}

        confidences = []
        for report in validation_reports.values():
            if isinstance(report, dict):
                confidences.append(report.get("confidence", 0))

        if not confidences:
            return {"status": "no_confidence_data"}

        return {
            "confidence_distribution": {
                "mean": round(sum(confidences) / len(confidences), 3),
                "min": round(min(confidences), 3),
                "max": round(max(confidences), 3)
            },
            "quality_range": {
                "high_quality_fields": len([c for c in confidences if c >= 0.8]),
                "medium_quality_fields": len([c for c in confidences if 0.6 <= c < 0.8]),
                "low_quality_fields": len([c for c in confidences if c < 0.6])
            }
        }

    async def _check_semantic_consistency(self, validated_fields: Dict[str, Any]) -> float:
        """检查语义一致性"""
        try:
            if len(validated_fields) < 2:
                return 1.0

            # 构建一致性检查提示
            fields_text = "\n".join([f"{k}: {v}" for k, v in validated_fields.items() if v])

            consistency_prompt = f"""
请评估以下项目信息的语义一致性（0-1分）：

{fields_text}

评估要点：
1. 项目名称与其他信息是否匹配
2. 负责单位与项目类型是否合理
3. 时间信息是否逻辑一致

请只返回一个0-1之间的数字：
"""

            response = await self.llm.chat([
                {"role": "system", "content": "你是信息一致性评估专家。"},
                {"role": "user", "content": consistency_prompt}
            ])

            try:
                score = float(response.strip())
                return max(0.0, min(1.0, score))
            except ValueError:
                return 0.7

        except Exception as e:
            log.error(f"语义一致性检查失败: {e}")
            return 0.7

    def _calculate_comprehensive_score(
        self,
        basic_quality: Dict[str, Any],
        advanced_quality: Dict[str, Any],
        semantic_quality: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算综合评分"""
        try:
            # 提取各维度分数
            dimension_scores = {
                "accuracy": basic_quality.get("accuracy", 0.5),
                "completeness": basic_quality.get("completeness", 0.5),
                "consistency": basic_quality.get("consistency", 0.5),
                "reliability": basic_quality.get("reliability", 0.5),
                "timeliness": advanced_quality.get("timeliness", 0.8),
                "relevance": advanced_quality.get("relevance", 0.7)
            }

            # 加入语义质量分数
            if semantic_quality.get("enabled", False):
                semantic_score = semantic_quality.get("semantic_score", 0.7)
                dimension_scores["accuracy"] = (dimension_scores["accuracy"] * 0.7 + semantic_score * 0.3)

            # 计算加权总分
            overall_score = sum(
                score * self.quality_dimensions[dimension]
                for dimension, score in dimension_scores.items()
            )

            # 确定质量等级
            quality_grade = self._determine_quality_grade(overall_score)

            return {
                "overall_score": round(overall_score, 3),
                "quality_grade": quality_grade,
                "dimension_scores": {k: round(v, 3) for k, v in dimension_scores.items()}
            }

        except Exception as e:
            log.error(f"计算综合评分失败: {e}")
            return {
                "overall_score": 0.5,
                "quality_grade": "unknown",
                "dimension_scores": {}
            }

    def _determine_quality_grade(self, overall_score: float) -> str:
        """确定质量等级"""
        if overall_score >= self.quality_thresholds["excellent"]:
            return "excellent"
        elif overall_score >= self.quality_thresholds["good"]:
            return "good"
        elif overall_score >= self.quality_thresholds["acceptable"]:
            return "acceptable"
        elif overall_score >= self.quality_thresholds["poor"]:
            return "poor"
        else:
            return "very_poor"

    def _get_quality_level(self, score: float) -> str:
        """获取质量水平描述"""
        grade_descriptions = {
            "excellent": "优秀",
            "good": "良好",
            "acceptable": "可接受",
            "poor": "较差",
            "very_poor": "很差"
        }

        grade = self._determine_quality_grade(score)
        return grade_descriptions.get(grade, "未知")

    async def _generate_improvement_suggestions(
        self,
        overall_assessment: Dict[str, Any],
        validation_result: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成质量改进建议"""
        suggestions = []

        try:
            dimension_scores = overall_assessment.get("dimension_scores", {})

            # 基于各维度分数生成建议
            for dimension, score in dimension_scores.items():
                if score < 0.7:
                    suggestion = self._get_improvement_suggestion(dimension, score)
                    if suggestion:
                        suggestions.append(suggestion)

            return suggestions

        except Exception as e:
            log.error(f"生成改进建议失败: {e}")
            return [{"type": "error", "suggestion": f"生成建议时出错: {e}"}]

    def _get_improvement_suggestion(self, dimension: str, score: float) -> Optional[Dict[str, Any]]:
        """获取特定维度的改进建议"""
        suggestions_map = {
            "accuracy": {
                "suggestion": "提高抽取准确性",
                "actions": ["优化正则表达式模式", "改进LLM提示词", "增加专门化抽取方法"]
            },
            "completeness": {
                "suggestion": "提高信息完整性",
                "actions": ["增加更多检索关键词", "扩大检索范围", "检查文档是否包含所需信息"]
            },
            "consistency": {
                "suggestion": "提高信息一致性",
                "actions": ["加强交叉验证", "改进字段关联性检查", "优化语义一致性验证"]
            },
            "reliability": {
                "suggestion": "提高结果可靠性",
                "actions": ["提高置信度阈值", "增加验证规则", "改进多策略融合算法"]
            },
            "timeliness": {
                "suggestion": "提高处理效率",
                "actions": ["优化并行处理", "减少LLM调用次数", "启用结果缓存"]
            },
            "relevance": {
                "suggestion": "提高信息相关性",
                "actions": ["优化字段配置", "改进检索策略", "加强上下文理解"]
            }
        }

        if dimension in suggestions_map:
            suggestion_info = suggestions_map[dimension]
            return {
                "type": dimension,
                "priority": "high" if score < 0.5 else "medium",
                "current_score": round(score, 3),
                "suggestion": suggestion_info["suggestion"],
                "specific_actions": suggestion_info["actions"]
            }

        return None

    async def _load_quality_history(self):
        """加载历史质量数据"""
        try:
            log.info("加载历史质量数据（模拟）")
        except Exception as e:
            log.error(f"加载历史质量数据失败: {e}")

    async def _save_quality_assessment(self, assessment_report: Dict[str, Any]):
        """保存质量评估结果"""
        try:
            action = assessment_report.get("action", "")
            quality_score = assessment_report.get("quality_assessment", {}).get("overall_score", 0)

            history_item = {
                "timestamp": assessment_report.get("assessment_timestamp"),
                "overall_score": quality_score,
                "quality_grade": assessment_report.get("quality_assessment", {}).get("quality_grade"),
                "project_name": assessment_report.get("project_name")
            }

            self.quality_history[action].append(history_item)

            # 限制历史记录数量
            if len(self.quality_history[action]) > 100:
                self.quality_history[action] = self.quality_history[action][-100:]

            log.debug(f"质量评估结果已保存: {action}, 分数: {quality_score}")

        except Exception as e:
            log.error(f"保存质量评估结果失败: {e}")

    async def get_assessor_stats(self) -> Dict[str, Any]:
        """获取评估器统计信息"""
        try:
            total_assessments = sum(len(history) for history in self.quality_history.values())

            return {
                "assessor_info": {
                    "type": "advanced_quality_assessor",
                    "version": "1.0",
                    "initialized": self._initialized,
                    "semantic_quality_enabled": self.enable_semantic_quality,
                    "historical_comparison_enabled": self.enable_historical_comparison
                },
                "quality_dimensions": self.quality_dimensions,
                "quality_thresholds": self.quality_thresholds,
                "historical_data": {
                    "total_assessments": total_assessments,
                    "actions_tracked": len(self.quality_history)
                }
            }

        except Exception as e:
            log.error(f"获取评估器统计失败: {e}")
            return {"error": str(e)}
