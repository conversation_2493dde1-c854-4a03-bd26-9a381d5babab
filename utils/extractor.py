from typing import List, Dict, Any, Optional
from utils.llm import LLM
import re
import json
from utils.utils import load_json
# import dspy
from typing import Dict, Any, Optional
from utils.config import config

# class ProjectExtractor(dspy.Module):
#     def __init__(self):
#         super().__init__()
#         self.extractor = dspy.ChainOfThought("input: str, project_name: str -> output: dict")
        
#     def forward(self, text: str, project_name: str) -> Dict[str, Any]:
#         self.extractor.system_prompt = """
#         You are a precise information extractor. You must return a JSON object with EXACTLY these fields:
#         {{
#             "name": "project name (fixed as input project_name)",
#             "start_date": "YYYY-MM format",
#             "end_date": "YYYY-MM format",
#             "total_investment": "number with unit (e.g., 100万)",
#             "responsible_unit": "organization name",
#             "leader": "person name",
#             "research_points": "point1；point2；point3",
#             "innovation": "innovation1；innovation2；innovation3",
#             "main_deliverables": "deliverable1；deliverable2；deliverable3",
#             "patent": "发明专利：专利名称1；实用新型专利：专利名称2"
#         }}

#         Strict Rules:
#         1. You MUST return ALL fields listed above
#         2. If information is not found in the text, use "未提及" as the value
#         3. Dates MUST be in YYYY-MM format (e.g., 2023-01)
#         4. Multiple items MUST be separated by Chinese semicolons (；)
#         5. Patents MUST be prefixed with either "发明专利：" or "实用新型专利："
#         6. DO NOT include any fields not listed above
#         7. DO NOT modify or generate information - only extract from the input text
#         8. DO NOT return any explanation or additional text, only the JSON object

#         Example of correct format (DO NOT USE THESE VALUES, extract from input text):
#         {{
#             "name": "项目名称",
#             "start_date": "2023-01",
#             "end_date": "2024-12",
#             "total_investment": "100万",
#             "responsible_unit": "某单位名称",
#             "leader": "张三",
#             "research_points": "研究点1；研究点2；研究点3",
#             "innovation": "创新点1；创新点2；创新点3",
#             "main_deliverables": "成果1；成果2；成果3",
#             "patent": "发明专利：专利名称1；实用新型专利：专利名称2"
#         }}
#         """
        
#         result = self.extractor(input=text, project_name=project_name)
#         return self._post_process(result.output, project_name)
    
#     def _post_process(self, result: Dict[str, Any], project_name: str) -> Dict[str, Any]:
#         """后处理结果，确保格式正确"""
#         result['name'] = project_name
        
#         # 处理日期格式
#         for date_field in ["start_date", "end_date"]:
#             if result.get(date_field) and result[date_field] != "未提及":
#                 result[date_field] = self._format_date(result[date_field])
        
#         # 处理投资金额
#         if result.get("total_investment") and result["total_investment"] != "未提及":
#             result["total_investment"] = self._parse_investment(result["total_investment"])
            
#         return result

# class ConferenceExtractor(dspy.Module):
#     def __init__(self):
#         super().__init__()
#         self.extractor = dspy.ChainOfThought("input: str, conference_name: str -> output: dict")
        
#         self.type_rules = {
#             "决策会议": ["会议纪要"],
#             "表彰获奖": ["感谢信", "表扬", "嘉奖", "表彰"],
#             "问责处分": ["通报批评", "处分", "警告", "通报"],
#             "印发文件": ["印发", "通知"],
#             "人事任免": ["任职", "免职", "聘任", "解聘"],
#             "请示批复": ["请示", "批复", "审批"],
#             "函件回复": ["函", "回复"],
#             "通知通报": ["通知", "通报"]
#         }
        
#     def forward(self, text: str, conference_name: str) -> Dict[str, Any]:
#         self.extractor.system_prompt = f"""
#         You are a precise information extractor. You must return a JSON object with EXACTLY these fields:
#         {{
#             "name": "conference name (fixed as input conference_name)",
#             "date": "YYYY-MM format",
#             "type": "one of [{', '.join(self.type_rules.keys())}]",
#             "organizer": "organizer1；organizer2；organizer3",
#             "participants": "participant1；participant2；participant3",
#             "summary": "point1；point2；point3"
#         }}

#         Strict Rules:
#         1. You MUST return ALL fields listed above
#         2. If information is not found in the text, use "未提及" as the value (except for type)
#         3. Date MUST be in YYYY-MM format (e.g., 2023-01)
#         4. Multiple items MUST be separated by Chinese semicolons (；)
#         5. Type MUST be one of these values: {', '.join(self.type_rules.keys())}
#         6. DO NOT include any fields not listed above
#         7. DO NOT modify or generate information - only extract from the input text
#         8. DO NOT return any explanation or additional text, only the JSON object

#         Type selection rules:
#         {chr(10).join([f'- {k}: if contains {" or ".join(v)}' for k, v in self.type_rules.items()])}

#         Example of correct format (DO NOT USE THESE VALUES, extract from input text):
#         {{
#             "name": "会议名称",
#             "date": "2023-01",
#             "type": "决策会议",
#             "organizer": "组织方1；组织方2",
#             "participants": "参与者1；参与者2；参与者3",
#             "summary": "要点1；要点2；要点3"
#         }}
#         """
        
#         result = self.extractor(input=text, conference_name=conference_name)
#         return self._post_process(result.output, conference_name)
    
#     def _post_process(self, result: Dict[str, Any], conference_name: str) -> Dict[str, Any]:
#         """后处理结果，确保格式正确"""
#         result.name = conference_name
        
#         # 处理日期格式
#         if result.get("date") and result["date"] != "未提及":
#             result["date"] = self._format_date(result["date"])
            
#         return result

# class DspyExtractor:
#     """结构化信息抽取器"""
    
#     def __init__(self):
#         llm = dspy.LM('openai/hngpt-mini', api_base='http://*************:8888/v1', api_key='startfrom2023')
#         dspy.settings.configure(lm=llm)
#         self.project_extractor = ProjectExtractor()
#         self.conference_extractor = ConferenceExtractor()
        
#     async def extract_project(self, text: str, project_name: str) -> Optional[Dict[str, Any]]:
#         """提取项目信息"""
#         try:
#             return self.project_extractor(text, project_name)
#         except Exception as e:
#             print(f"Error extracting project info: {str(e)}")
#             return None
            
#     async def extract_conference(self, text: str, project_name: str) -> Optional[Dict[str, Any]]:
#         """提取会议信息"""
#         try:
#             return self.conference_extractor(text, project_name)
#         except Exception as e:
#             print(f"Error extracting conference info: {str(e)}")
#             return None
            
    
class Extractor:
    """结构化信息抽取器"""
    
    def __init__(self, llm: Optional[LLM] = None):
        """初始化提取器
        
        Args:
            llm: LLM实例，如果为None则创建新实例
        """
        self.llm = llm or LLM()  # 使用传入的LLM实例或创建新实例
        
    async def extract_project(self, text: str, project_name: str, config: Dict[str, Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """提取项目信息"""
        # 从配置文件加载关键词
        project_keywords = {
            "project_no": config.get("project_no", {}).get("keywords", []),  # 添加 project_no
            "start_date": config.get("start_date", {}).get("keywords", []),
            "end_date": config.get("end_date", {}).get("keywords", []),
            "total_investment": config.get("total_investment", {}).get("keywords", []),
            "responsible_unit": config.get("responsible_unit", {}).get("keywords", []),
            "leader": config.get("leader", {}).get("keywords", []),
            "research_points": config.get("research_points", {}).get("keywords", []),
            "innovation": config.get("innovation", {}).get("keywords", []),
            "main_deliverables": config.get("main_deliverables", {}).get("keywords", []),
            "patent": config.get("patent", {}).get("keywords", [])
        }

        prompt = f"""请从以下文本中提取项目"{project_name}"的相关信息，返回单个JSON对象。

需要提取的字段和规则：
1. name: "{project_name}"

2. project_no: 项目编号
   相关关键词：{', '.join(project_keywords['project_no'])}
   注意：
   - 必须从文本中提取实际的项目编号
   - 不要使用示例或默认值
   - 如果文本中未找到项目编号，返回"未提及"
   {config.get("project_no", {}).get("hint", "")}

3. start_date: 开始日期（YYYY-MM-DD格式）
   相关关键词：{', '.join(project_keywords['start_date'])}
   注意：
   - 必须从文本中提取实际的开始日期
   - 日期格式必须是YYYY-MM-DD
   - 如果只有年月信息，日期用01补充
   - 如果只有年份信息，月日均用01补充
   - 如果文本中未找到开始日期，返回"未提及"
   {config.get("start_date", {}).get("hint", "")}

4. end_date: 结束日期（YYYY-MM-DD格式）
   相关关键词：{', '.join(project_keywords['end_date'])}
   注意：
   - 必须从文本中提取实际的结束日期
   - 日期格式必须是YYYY-MM-DD
   - 如果只有年月信息，日期用01补充
   - 如果只有年份信息，月日均用01补充
   - 如果文本中未找到结束日期，返回"未提及"
   {config.get("end_date", {}).get("hint", "")}

5. total_investment: 总投资（数字+单位）
   相关关键词：{', '.join(project_keywords['total_investment'])}
   注意：
   - 必须从文本中提取实际的投资金额
   - 不要使用示例或默认值
   - 如果文本中未找到投资金额，返回"未提及"
   {config.get("total_investment", {}).get("hint", "")}

6. responsible_unit: 承担单位
   相关关键词：{', '.join(project_keywords['responsible_unit'])}
   注意：
   - 提取的承担单位需要去重，相似或重复的内容只保留一次
   - 必须是文本中实际出现的承担单位，不要使用示例或占位符
   - 如果某项信息缺失，对应位置用空值表示
   - 如果文本中未提到任何承担单位，返回"未提及"
   - 必须返回单个字符串，不要返回数组
   - 不要使用示例或默认值
   {config.get("responsible_unit", {}).get("hint", "")}
   
7. leader: 负责人
   相关关键词：{', '.join(project_keywords['leader'])}
   注意：
   - 提取的负责人需要去重，相似或重复的内容只保留一次
   - 必须是文本中实际出现的负责人，不要使用示例或占位符
   - 如果文本中未提到任何负责人，返回"未提及"
   - 必须返回单个字符串，不要返回数组
   - 不要使用示例或默认值
   {config.get("leader", {}).get("hint", "")}
   
8. research_points: 主要研究点
   相关关键词：{', '.join(project_keywords['research_points'])}
   注意：
   - 提取的研究点需要去重，相似或重复的内容只保留一次
   - 必须是文本中实际出现的研究内容，不要使用示例或占位符
   - 如果文本中未提到任何研究点，返回"未提及"
   - 必须返回单个字符串，不要返回数组
   - 不要使用示例或默认值
   {config.get("research_points", {}).get("hint", "")}

9. innovation: 创新点
   相关关键词：{', '.join(project_keywords['innovation'])}
   注意：
- 提取的创新点需要去重，相似或重复的内容只保留一次
   - 必须是文本中实际出现的创新内容，不要使用示例或占位符
   - 如果文本中未提到任何创新点，返回"未提及"
   - 必须返回单个字符串，不要返回数组
   - 不要使用示例或默认值
   {config.get("innovation", {}).get("hint", "")}
   
10. main_deliverables: 主要交付成果
    相关关键词：{', '.join(project_keywords['main_deliverables'])}
    注意：
    - 提取的成果需要去重，相似或重复的内容只保留一次
    - 必须是文本中实际出现的成果内容，不要使用示例或占位符
    - 如果文本中未提到任交付成果，返回"未提及"
    - 必须返回单个字符串，不要返回数组
    - 不要使用示例或默认值
    {config.get("main_deliverables", {}).get("hint", "")}

11. patent: 专利信息
    相关关键词：{', '.join(project_keywords['patent'])}
    注意：
    - 必须严格按照原文提取专利信息，不要生成或修改内容    
    - 专利信息格式要求：
      * 发明专利格式：专利名称|申请号/专利号|状态|日期
      * 实用新型格式：专利名称|申请号/专利号|状态|日期
      * 状态必须是：申请中 或 已授权
      * 日期格式：YYYY-MM-DD
      * 多个专利之间用中文分号（；）分隔
    - 如果某项信息缺失，对应位置用空值表示
    - 如果文本中未提到任何专利，返回"未提及"
    - 必须返回单个字符串，不要返回数组
    - 不要使用示例或默认值
    {config.get("patent", {}).get("hint", "")}

重要提示：
1. 严格遵循以下规则：
   - 所有内容必须来自原文，禁止生成或修改
   - 所有字段必须返回合法的JSON格式
   - 字符串中的引号需要正确转义
   - 多项内容使用中文分号（；）分隔
   - 所有字段必须返回字符串类型，不允许返回数组

2. 禁止使用示例或默认值：
   - 必须从文本中提取实际内容
   - 如果某字段在文本中未找到相关信息，返回"未提及"
   - 不要复制或使用任何示例内容

文本内容：
{text}
"""
        
        try:
            response = await self.llm.chat([
                {
                    "role": "system", 
                    "content": """You are a helpful assistant that extracts project information. 
                    Always return a valid JSON object with properly escaped strings.
                    Important: All fields must be strings, not arrays. 
                    Use Chinese semicolons (；) to separate multiple items within a string."""
                },
                {"role": "user", "content": prompt}
            ])
            
            # 验证JSON格式
            info = load_json(response)      
            if info:
                # 确保项目名称正确
                info["name"] = project_name
                
                # 处理可能是列表的字段，将其转换为分号分隔的字符串
                list_fields = ['research_points', 'innovation', 'main_deliverables', 'patent']
                for field in list_fields:
                    if field in info:
                        # 如果字段是列表，将其转换为字符串
                        if isinstance(info[field], list):
                            info[field] = '；'.join(str(item) for item in info[field])
                        # 如果字段不是字符串类型，转换为字符串
                        elif not isinstance(info[field], str):
                            info[field] = str(info[field])
                
                # 处理日期格式
                for date_field in ["start_date", "end_date"]:
                    if info.get(date_field) and info[date_field] != "未提及":
                        info[date_field] = self._format_date(info[date_field])
                
                # 处理投资金额
                if info.get("total_investment") and info["total_investment"] != "未提及":
                    info["total_investment"] = self._parse_investment(info["total_investment"])
                
                # 去除重复项
                for field in ['research_points', 'innovation', 'main_deliverables']:
                    if info.get(field) and info[field] != "未提及":
                        items = info[field].split('；')
                        # 使用dict.fromkeys()去重同时保持顺序
                        unique_items = list(dict.fromkeys(items))
                        info[field] = '；'.join(unique_items)
                
            return info
            
        except Exception as e:
            print(f"Error extracting project info: {str(e)}")
            return None
            
    async def extract_conference(self, text: str, project_name: str, config: Dict[str, Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """提取会议信息"""
        # 预定义的文档类型列表及其判断规则
        TYPE_RULES = {
            "决策会议": ["会议纪要"],
            "表彰获奖": ["感谢信", "表扬", "嘉奖", "表彰"],
            "问责处分": ["通报批评", "处分", "警告", "通报"],
            "印发文件": ["印发", "通知"],
            "人事任免": ["任职", "免职", "聘任", "解聘"],
            "请示批复": ["请示", "批复", "审批"],
            "函件回复": ["函", "回复"],
            "通知通报": ["通知", "通报"]  # 作为默认类型的判断条件
        }

        prompt = f"""请从以下文本中提取会议或文书相关信息，以JSON格式返回。

需要提取的字段：
1. name: 会议或文书名称（固定为：{project_name}）

2. date: 会议时间或文书发布时间（YYYY-MM-DD格式）
   相关关键词：会议时间, 开会时间, 会议日期, 活动时间, 研讨时间, 交流时间, 讨论时间, 座谈时间, 发布时间, 说明时间, 时间, 年月日
   注意：
   - 日期格式必须是YYYY-MM-DD
   - 如果只有年月信息，日期用01补充
   - 如果只有年份信息，月日均用01补充
   - 例如：
     * 2023年 -> 2023-01-01
     * 2023年9月 -> 2023-09-01
     * 2023年9月15日 -> 2023-09-15

3. type: 文档类型（必须从以下类型中选择一个最匹配的）：

文档类型判断规则：
- 决策会议：如果文档包含"会议纪要"等内容
- 表彰获奖：如果文档包含"感谢信"、"表扬"、"嘉奖"、"表彰"等内容
- 问责处分：如果文档包含"通报批评"、"处分"、"警告"等内容
- 印发文件：如果文档包含"印发"、"通知"等内容
- 人事任免：如果文档包含"任职"、"免职"、"聘任"、"解聘"等内容
- 请示批复：如果文档包含"请示"、"批复"、"审批"等内容
- 函件回复：如果文档包含"函"、"回复"等内容
- 通知通报：如果以上都不符合，但包含"通知"、"通报"等内容

请仔细分析文档标题和内容，根据上述规则选择最匹配的类型。

4. organizer: 发起组织（多个组织用分号分隔）
   相关关键词：会议或者发文中的组织

5. participants: 参会部门和人员或接收组织和个人（多个参与者用分号分隔）
   相关关键词：参会部门和参会人员, 发文的接收组织和个人

6. summary: 会议或文书的核心内容（多个内容点用分号分隔）
   相关关键词：关于举办, 关于应发, 工作简报, 情况通报, 整改目标, 免职, 任职, 建设意见方案的通知, 成果, 方案, 报告, 请示, 签报, 批复, 复函, 决定, 会议纪要, 感谢信, 备忘录, 通知

提取规则：
1. 只提取文本中实际出现的信息
2. 如果某字段（除type外）在文本中未提及，填写"未提及"
3. 日期必须是YYYY-MM格式
4. type字段必须从预定义类型中选择一个，不能返回"未提及"
5. 对于可能有多个项目的字段（organizer, participants, summary）：
   - 多个项目之间使用分号（；）分隔
   - 不要使用数组或列表格式
   - 去除重复或相似的内容

注意：
- 只返回一个JSON对象
- 会议或文书名称固定为传入的值
- 不要编造或推测信息
- type字段必须根据规则选择一个最匹配的类型
- 确保提取的是文本中真实存在的内容
- 列表类型的字段必须以分号分隔的字符串形式返回，不要返回数组

文本内容：
{text}

请直接返回JSON对象，不要包含任何解释或说明。"""
        try:
            response = await self.llm.chat([
                {
                    "role": "system",
                    "content": "你是一个信息提取助手。请直接返回JSON格式的结果，不要包含任何解释。"
                },
                {"role": "user", "content": prompt}
            ])
            
            info = load_json(response)
            if info:
                # 确保会议名称正确
                info["name"] = project_name
                
                # 处理日期格式
                if info.get("date") and info["date"] != "未提及":
                    info["date"] = self._format_date(info["date"])
                
            return info
            
        except Exception as e:
            print(f"Error extracting conference info: {str(e)}")
            return None
            
    def _format_date(self, date_str: str) -> str:
        """格式化日期为YYYY-MM-DD格式
        
        Args:
            date_str: 输入的日期字符串，支持多种格式
            
        Returns:
            str: YYYY-MM-DD格式的日期字符串，解析失败返回原字符串
        """
        try:
            # 清理输入字符串
            date_str = str(date_str).strip()
            
            # 匹配多种日期格式
            patterns = [
                # 标准格式：2023-12-15 或 2023/12/15 或 2023.12.15
                r"(\d{4})[-./\s]*(\d{1,2})[-./\s]*(\d{1,2})",
                # 中文格式：2023年12月15日
                r"(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})?[日]?",
                # 年月格式：2023-12 或 2023年12月
                r"(\d{4})[-年./\s]*(\d{1,2})[-月]?",
                # 纯年份格式：2023 或 2023年
                r"(\d{4})[年]?"
            ]
            
            for pattern in patterns:
                match = re.search(pattern, date_str)
                if match:
                    year = match.group(1)
                    month = match.group(2) if len(match.groups()) >= 2 else "01"
                    day = match.group(3) if len(match.groups()) >= 3 and match.group(3) else "01"
                    
                    # 验证年月日的有效性
                    year_num = int(year)
                    month_num = int(month)
                    day_num = int(day)
                    
                    if 1900 <= year_num <= 2100 and 1 <= month_num <= 12 and 1 <= day_num <= 31:
                        return f"{year}-{str(month_num).zfill(2)}-{str(day_num).zfill(2)}"
            
            return date_str
            
        except Exception as e:
            print(f"Error formatting date '{date_str}': {str(e)}")
            return date_str
            
    def _parse_investment(self, value: str) -> str:
        """解析投资金额，保留原始数字和单位
        
        Args:
            value: 输入的金额字符串
            
        Returns:
            str: 提取的金额字符串，格式为"数字 单位"
        """
        try:
            # 如果输入是数字，添加万元单位
            if isinstance(value, (int, float)):
                return f"{value}万"
                
            # 转换为字符串并清理空格
            value_str = str(value).strip()
            
            # 提取数字部分
            number = re.search(r"([\d.]+)", value_str)
            if not number:
                return "未提及"
                
            # 提取单位（如果有）
            unit = "万"  # 默认单位
            if "亿" in value_str:
                unit = "亿"
            elif "千万" in value_str:
                unit = "千万"
            elif "万" in value_str:
                unit = "万"
            else:
                unit = "万"
                return  f"{float(number.group(1))/10000}{unit}"    
            # 返回原始数字加单位
            return f"{number.group(1)}{unit}"
            
        except Exception as e:
            print(f"Error parsing investment value '{value}': {str(e)}")
            return "未提及"