#!/usr/bin/env python3
"""
时区工具类
统一处理系统中的时区问题
"""

from datetime import datetime, timezone, timedelta
from typing import Optional
from utils.config import config

# 中国标准时间时区
CST = timezone(timedelta(hours=8))

def get_system_timezone():
    """获取系统配置的时区"""
    tz_offset = config.get("system.time_zone_offset", "+08:00")
    if tz_offset == "+08:00":
        return CST
    else:
        # 解析其他时区偏移
        sign = 1 if tz_offset[0] == '+' else -1
        hours, minutes = map(int, tz_offset[1:].split(':'))
        offset = timedelta(hours=sign * hours, minutes=sign * minutes)
        return timezone(offset)

def now_with_timezone() -> datetime:
    """获取当前时间（带时区信息）"""
    return datetime.now(get_system_timezone())

def now_without_timezone() -> datetime:
    """获取当前时间（不带时区信息，用于数据库存储）"""
    return datetime.now(get_system_timezone()).replace(tzinfo=None)

def convert_to_system_timezone(dt: datetime) -> datetime:
    """将datetime转换为系统时区"""
    if dt is None:
        return None
    
    system_tz = get_system_timezone()
    
    if dt.tzinfo is None:
        # 假设无时区信息的datetime是系统时区时间
        return dt
    else:
        # 转换为系统时区后移除时区信息
        return dt.astimezone(system_tz).replace(tzinfo=None)

def format_datetime_for_display(dt: datetime) -> str:
    """格式化datetime用于显示"""
    if dt is None:
        return ""
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def parse_datetime_string(date_str: str) -> Optional[datetime]:
    """解析日期时间字符串"""
    if not date_str or date_str.strip() == "":
        return None
    
    # 尝试多种日期格式
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M:%S.%f",
        "%Y-%m-%d",
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d"
    ]
    
    for fmt in formats:
        try:
            dt = datetime.strptime(date_str.strip(), fmt)
            # 假设解析出的时间是系统时区时间
            return dt
        except ValueError:
            continue
    
    raise ValueError(f"无法解析日期时间字符串: {date_str}")

def get_mysql_timezone_setting() -> str:
    """获取MySQL时区设置字符串"""
    tz_offset = config.get("system.time_zone_offset", "+08:00")
    # URL编码+号
    return tz_offset.replace("+", "%2B")
