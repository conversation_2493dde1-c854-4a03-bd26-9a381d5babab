# from transformers import AutoTokenizer, AutoModel
# import torch
# import numpy as np
# from typing import List, Optional
# from sentence_transformers import SentenceTransformer
# from utils.log import logger
# from pathlib import Path
# import re
# import ollama
# import jieba
# from collections import Counter
# import math
# from openai import OpenAI
# model_name_or_path = str(Path(__file__).parent.parent/"data/models/xiaobu-embedding-v2")
# openai_api_key = "EMPTY"
# openai_api_base = f"http://127.0.0.1:11434/v1"

# # 创建一个 OpenAI 客户端，用于与 API 服务器进行交互
# client = OpenAI(
#     api_key=openai_api_key,
#     base_url=openai_api_base,
#     timeout=20
# )

# def predict(message):
#     history_openai_format = [{"role": "user", "content": message}]
#     try:
#         response = client.chat.completions.create(
#             model='hngpt',   # 使用的模型名称
#             messages=history_openai_format,  # 聊天历史
#             temperature=0,                   # 控制生成文本的随机性
#             stream=False,                    # 是否以流的形式接收响应
#             extra_body={'repetition_penalty': 1}
#         )
#         return response.choices[0].message.content.strip()
#     except Exception as e:
#         print(f"发生未预期的错误: {e}")
#         return "抱歉，发生了未知错误。请稍后再试。"

# class OSentenceVector:
#     def __init__(self,model_name: str = "xbv2",alpha: float = 0.5, k1: float = 1.5, b: float = 0.75, epsilon: float = 0.25) -> None:
#         self.model_name = model_name
#         self.llm_function = predict
#         self.alpha = alpha
#         self.k1 = k1
#         self.b = b
#         self.epsilon = epsilon
#         self.corpus_size = 0
#         self.avgdl = 0
#         self.doc_freqs = []
#         self.idf = {}
#         self.doc_len = []
#         self.corpus = []

       
#     def encode(self, text) -> np.ndarray:
#         # 使用 ollama.embeddings 接口生成嵌入
#         response = ollama.embeddings(prompt=text, model=self.model_name)
#         embedding = np.array(response['embedding'])
#         return embedding


#     def cosine(self, a, b):
#         return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
    

#     def cosine_pad(self, a: np.ndarray, b: np.ndarray) -> float:
#         # 取维度最少的向量，并将其扩展到和另一个向量相同的维度
#         if a.ndim > b.ndim:
#             b = np.expand_dims(b, axis=0)
#             b = np.repeat(b, a.shape[0], axis=0)
#         elif b.ndim > a.ndim:
#             a = np.expand_dims(a, axis=0)
#             a = np.repeat(a, b.shape[0], axis=0)
#         # 比较向量 a 和 b 的第0维的大小
#         dim = min(a.shape[0], b.shape[0])
#         # 计算向量 a 和 b 的 L2 范数
#         a_norm = np.linalg.norm(a[:dim], axis=0)
#         b_norm = np.linalg.norm(b[:dim], axis=0)
#         # 计算向量 a 和 b 的内积
#         dot_product = np.sum(a[:dim] * b[:dim], axis=0)
#         # 计算余弦相似度
#         cos_sim = dot_product / (a_norm * b_norm)
#         return float(np.mean(cos_sim))
        
#     def compare(self, a: str, b: str) -> float:
#         embed_a = self.encode(a)
#         embed_b = self.encode(b)
#         sim = self.cosine(embed_a, embed_b)
#         return sim


#     def ncosine(self, vector1, vector2):
#         dot_product = np.dot(vector1, vector2)
#         norm1 = np.linalg.norm(vector1)
#         norm2 = np.linalg.norm(vector2)
#         similarity = dot_product / (norm1 * norm2)
#         normalized_similarity = (similarity + 1) / 2
#         return normalized_similarity
    
#     def rerank(self, query: str, documents: List[str], top_k: int = None) -> List[str]:
#         query_embedding = self.encode(query)
        
#         # Calculate similarity scores for all documents
#         similarities = []
#         for doc in documents:
#             doc_embedding = self.encode(doc)
#             similarity = self.ncosine(query_embedding, doc_embedding)
#             similarities.append((doc, similarity))
        
#         # Sort documents by similarity score in descending order
#         reranked = sorted(similarities, key=lambda x: x[1], reverse=True)
        
#         # Extract only the documents from the sorted list
#         reranked_docs = [doc for doc, _ in reranked]
        
#         # Return top_k documents if specified, otherwise return all
#         if top_k is not None:
#             reranked_docs = reranked_docs[:top_k]
        
#         return reranked_docs
    

#     def rerankllm(self, query: str, documents: List[str], top_k: int = None) -> List[str]:
#         if not documents:
#             return []

#         try:
#             prompt = self._create_rerank_prompt(query, documents)
#             response = self.llm_function(prompt)
#             reranked = self._parse_llm_response(response, documents)

#             if top_k is not None:
#                 reranked = reranked[:top_k]

#             return reranked

#         except Exception as e:
#             print(f"An error occurred during reranking: {e}")
#             return documents  # 返回原始文档列表

#     def _create_rerank_prompt(self, query: str, documents: List[str]) -> str:
#         doc_list = "\n".join([f"Document {i+1}: {doc[:200]}..." for i, doc in enumerate(documents)])
#         prompt = f"""Task: Rerank the given documents based on their relevance to the query.

# Query: "{query}"

# Documents:
# {doc_list}

# Instructions:
# 1. Analyze each document's relevance to the query.
# 2. Rank the documents from most relevant to least relevant.

# Output Format:
# Please provide your answer in the following format:

# Ranking:
# 1. Document [number]
# 2. Document [number]
# 3. Document [number]
# ...

# Example Output:
# Ranking:
# 1. Document 3
# 2. Document 1
# 3. Document 4
# 4. Document 2

# Your Response:
# """
#         return prompt

#     def _parse_llm_response(self, response: str, documents: List[str]) -> List[str]:
#         try:
#             lines = response.strip().split('\n')
            
#             ranking_section = False
#             rankings = []

#             for line in lines:
#                 line = line.strip()
#                 if line == "Ranking:":
#                     ranking_section = True
#                     continue

#                 if ranking_section:
#                     match = re.search(r'Document (\d+)', line)
#                     if match:
#                         rankings.append(int(match.group(1)) - 1)  # Convert to 0-based index

#             if not rankings:
#                 raise ValueError("Could not parse rankings from the response")

#             # Create the reranked list
#             reranked = []
#             for rank in rankings:
#                 if rank < len(documents):
#                     reranked.append(documents[rank])

#             # Add any documents that were not ranked
#             for i, doc in enumerate(documents):
#                 if i not in rankings:
#                     reranked.append(doc)

#             return reranked

#         except Exception as e:
#             print(f"Error parsing LLM response: {e}")
#             return documents  # 返回原始文档列表
        
    
#     def add_corpus(self, corpus: List[str]):
#         self.corpus = corpus
#         tokenized_corpus = [list(jieba.cut(doc)) for doc in corpus]
#         self._initialize(tokenized_corpus)

#     def _initialize(self, corpus):
#         self.corpus_size = len(corpus)
#         nd = {}  # word -> number of documents with word
#         num_doc = 0
#         for document in corpus:
#             self.doc_len.append(len(document))
#             num_doc += len(document)

#             frequencies = dict(Counter(document))
#             self.doc_freqs.append(frequencies)

#             for word, freq in frequencies.items():
#                 if word not in nd:
#                     nd[word] = 0
#                 nd[word] += 1

#         self.avgdl = num_doc / self.corpus_size
#         self._calc_idf(nd)

#     def _calc_idf(self, nd):
#         idf_sum = 0
#         negative_idfs = []
#         for word, freq in nd.items():
#             idf = math.log(self.corpus_size - freq + 0.5) - math.log(freq + 0.5)
#             self.idf[word] = idf
#             idf_sum += idf
#             if idf < 0:
#                 negative_idfs.append(word)
#         self.average_idf = idf_sum / len(self.idf)

#         eps = self.epsilon * self.average_idf
#         for word in negative_idfs:
#             self.idf[word] = eps

#     def get_scores(self, query):
#         scores = np.zeros(self.corpus_size)
#         doc_len = np.array(self.doc_len)
#         for q in query:
#             q_freq = np.array([(doc.get(q) or 0) for doc in self.doc_freqs])
#             scores += (self.idf.get(q) or 0) * (q_freq * (self.k1 + 1) /
#                                                (q_freq + self.k1 * (1 - self.b + self.b * doc_len / self.avgdl)))
#         return scores

#     def rerankbm25(self, query: str, documents: List[str] = None, top_k: int = None) -> List[str]:
#         if documents is None:
#             documents = self.corpus
#         if not documents:
#             return []

#         try:
#             if len(documents) != len(self.corpus):
#                 self.add_corpus(documents)

#             # 计算BM25分数
#             tokenized_query = list(jieba.cut(query))
#             bm25_scores = self.get_scores(tokenized_query)

#             # 计算嵌入相似度
#             query_embedding = self.encode(query)
#             similarities = []
#             for doc in documents:
#                 doc_embedding = self.encode(doc)
#                 similarity = self.ncosine(query_embedding, doc_embedding)
#                 similarities.append(similarity)

#             # 归一化分数
#             bm25_scores = np.array(bm25_scores)
#             bm25_scores = (bm25_scores - bm25_scores.min()) / (bm25_scores.max() - bm25_scores.min() + 1e-8)
#             similarities = np.array(similarities)
#             similarities = (similarities - similarities.min()) / (similarities.max() - similarities.min() + 1e-8)

#             # 结合两种分数
#             combined_scores = [(doc, self.alpha * sim + (1 - self.alpha) * bm25)
#                                for doc, sim, bm25 in zip(documents, similarities, bm25_scores)]

#             # 根据组合分数对文档进行排序
#             reranked = sorted(combined_scores, key=lambda x: x[1], reverse=True)
            
#             # 提取排序后的文档
#             reranked_docs = [doc for doc, _ in reranked]
            
#             # 如果指定了top_k，则只返回前top_k个文档
#             if top_k is not None:
#                 reranked_docs = reranked_docs[:top_k]
            
#             return reranked_docs

#         except Exception as e:
#             logger.error(f"重新排序过程中发生错误: {e}")
#             return documents  # 如果出错，返回原始文档列表
    



# class Embeddings:
#     def __init__(self, model_path):
#         self.model = SentenceTransformer(model_path)

#     def embed_documents(self, text_list):
#         embeddings = self.model.encode(text_list)
#         encod_list = embeddings.tolist()
#         return encod_list

#     def embed_query(self, text):
#         embeddings = self.model.encode([text])
#         encod_list = embeddings.tolist()
#         return encod_list[0]


# class EncodeText:
#     def __init__(self, model_path: Optional[str] = None) -> None:
#         if model_path is None:
#             logger.critical("model_path is None.")
#             raise ValueError("model_path is None.")
#         self.model = SentenceTransformer(model_path)

#     def __call__(self, sentences: List[str]):
#         if not isinstance(sentences, List):
#             sentences = [sentences]
#         return self.model.encode(sentences, normalize_embeddings=True)




# class SentenceVector:
#     def __init__(self,
#                  model_name_or_path: str = model_name_or_path,
#                  device: str = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")) -> None:
#         self.model_name_or_path = model_name_or_path
#         self.device = device

#         self.tokenizer = AutoTokenizer.from_pretrained(self.model_name_or_path)

#         self.model = AutoModel.from_pretrained(self.model_name_or_path)
#         self.model.to(self.device)

#     def encode(self, text) -> np.ndarray:
#         if isinstance(text, str):
#             text = [text]
#         inputs = self.tokenizer(text, padding=True, truncation=True, return_tensors="pt")
#         inputs.to(device=self.device)
#         with torch.no_grad():
#             embeddings = self.model(**inputs, return_dict=True)

#         embeddings = embeddings.last_hidden_state[:, 0]
#         embeddings = embeddings / embeddings.norm(dim=1, keepdim=True)  
#         embeddings = embeddings.to('cpu').numpy()
        
#         return np.squeeze(embeddings)
    
    

#     def cosine_similarity(self, a: np.ndarray, b: np.ndarray) -> float:
#         # 计算向量 a 和 b 的 L2 范数
#         a_norm = np.linalg.norm(a)
#         b_norm = np.linalg.norm(b)
#         # 计算向量 a 和 b 的内积
#         dot_product = np.dot(a, b)
#         # 计算余弦相似度
#         cos_sim = dot_product / (a_norm * b_norm)
#         return cos_sim

#     def cosine(self, a: np.ndarray, b: np.ndarray) -> float:
#         # 取维度最少的向量，并将其扩展到和另一个向量相同的维度
#         if a.ndim > b.ndim:
#             b = np.expand_dims(b, axis=0)
#             b = np.repeat(b, a.shape[0], axis=0)
#         elif b.ndim > a.ndim:
#             a = np.expand_dims(a, axis=0)
#             a = np.repeat(a, b.shape[0], axis=0)
#         # 比较向量 a 和 b 的第0维的大小
#         dim = min(a.shape[0], b.shape[0])
#         # 计算向量 a 和 b 的 L2 范数
#         a_norm = np.linalg.norm(a[:dim], axis=1)
#         b_norm = np.linalg.norm(b[:dim], axis=1)
#         # 计算向量 a 和 b 的内积
#         dot_product = np.sum(a[:dim] * b[:dim], axis=1)
#         # 计算余弦相似度
#         cos_sim = dot_product / (a_norm * b_norm)
#         return float(np.mean(cos_sim))
        
#     def compare(self, a, b :str) -> float:
#         embed_a = self.encode([a])
#         embed_b = self.encode([b])
#         sim = self.cosine_similarity(embed_a, embed_b)
#         return sim


#     def similarity(self, embedding_a, embedding_b):
#         embedding_a = np.array(embedding_a).astype('float32')
#         embedding_b = np.array(embedding_b).astype('float32')
#         distance = np.linalg.norm(embedding_a - embedding_b)
#         similarity_score = 1 / (1 + distance)
#         return similarity_score
    

#     def ncosine_similarity(self, vector1, vector2):
#         dot_product = np.dot(vector1, vector2)
#         norm1 = np.linalg.norm(vector1)
#         norm2 = np.linalg.norm(vector2)
#         similarity = dot_product / (norm1 * norm2)
#         normalized_similarity = (similarity + 1) / 2
#         return normalized_similarity

# # SV = SentenceVector(model_name_or_path=model_name_or_path)
