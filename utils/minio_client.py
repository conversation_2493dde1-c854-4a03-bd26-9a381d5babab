from minio import <PERSON>o
from typing import List, Optional, Tuple
import os
from pathlib import Path
import tempfile
from urllib.parse import urlparse, unquote_plus, quote
from .config import config
import time

class MinioClient:
    def __init__(
        self,
        endpoint: str = None,
        access_key: str = None,
        secret_key: str = None,
        secure: bool = None,
        bucket_name: str = None
    ):
        """初始化 Minio 客户端"""
        try:
            self.client = Minio(
                endpoint or config.get('minio.endpoint'),
                access_key=access_key or config.get('minio.access_key'),
                secret_key=secret_key or config.get('minio.secret_key'),
                secure=secure if secure is not None else config.get('minio.secure', False)
            )
            
            # 在初始化时检查并创建bucket
            self.bucket_name = bucket_name or config.get('minio.bucket_name')
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                print(f"Created bucket: {self.bucket_name}")
            
            print(f"Successfully connected to MinIO server with bucket: {self.bucket_name}")
            
        except Exception as e:
            print(f"Error initializing MinioClient: {str(e)}")
            raise

    def _parse_url(self, url: str) -> Tuple[str, str]:
        """从URL中解析bucket_name和object_name"""
        url_parts = unquote_plus(url).lstrip("/").split("/")
        bucket_name = url_parts[0]
        object_name = "/".join(url_parts[1:])
        
        # 将Windows路径分隔符转换为Unix风格
        object_name = object_name.replace("\\", "/")
        
        # 处理中文和特殊字符
        # object_name = quote(object_name)
        
        return bucket_name, object_name

    def download_file(
        self,
        url: str,
        file_path: Optional[str] = None,
        filters: Optional[List[str]] = None
    ) -> str:
        """下载文件"""
        try:
            # 从URL解析bucket_name和object_name
            bucket_name, object_name = self._parse_url(url)
            
            # 检查文件扩展名
            if filters:
                ext = os.path.splitext(unquote_plus(object_name))[1].lower()
                if not any(ext.endswith(f.lower()) for f in filters):
                    print(f"File {url} does not match filters {filters}")
                    return ""

            if file_path is None:
                # 创建临时目录
                temp_dir = os.path.join(tempfile.gettempdir(), "minio_downloads")
                os.makedirs(temp_dir, exist_ok=True)
                
                # 保持目录结构，使用解码后的文件名
                file_path = os.path.join(temp_dir, unquote_plus(object_name))

            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 下载文件
            self.client.fget_object(bucket_name, object_name, file_path)
            print(f"Successfully downloaded file: {url} to {file_path}")
            return file_path
            
        except Exception as e:
            print(f"Error downloading file {url}: {str(e)}")
            return ""

    def download_directory(
        self,
        url: str,
        local_dir: Optional[str] = None,
        file_extensions: List[str] = ['.pdf']
    ) -> List[str]:
        """下载目录下的所有指定类型文件"""
        try:
            # 从URL解析bucket_name和prefix
            bucket_name, prefix = self._parse_url(url)
            
            # URL码prefix
            prefix = unquote_plus(prefix)
            
            # 规范化前缀
            prefix = prefix.rstrip('/') + '/'
            
            if local_dir is None:
                # 创建临时目录
                local_dir = os.path.join(tempfile.gettempdir(), "minio_downloads", prefix)
            
            # 确保本地目录存在
            os.makedirs(local_dir, exist_ok=True)
            
            # 获取目录下的所有文件
            objects = self.client.list_objects(bucket_name, prefix=prefix, recursive=True)
            
            downloaded_files = []
            for obj in objects:
                # URL解码object_name
                object_name = unquote_plus(obj.object_name)
                
                # 检查文件扩展名
                if any(object_name.lower().endswith(ext.lower()) for ext in file_extensions):
                    # 构建本地文件路径
                    file_name = os.path.basename(object_name)
                    local_path = os.path.join(local_dir, file_name)
                    
                    # 确保目录存在
                    os.makedirs(os.path.dirname(local_path), exist_ok=True)
                    
                    # 下载文件
                    try:
                        self.client.fget_object(bucket_name, obj.object_name, local_path)
                        downloaded_files.append(local_path)
                        print(f"Successfully downloaded file: {object_name} to {local_path}")
                    except Exception as e:
                        print(f"Error downloading {object_name}: {str(e)}")
                        continue
            
            return downloaded_files
            
        except Exception as e:
            print(f"Error downloading directory: {str(e)}")
            return []

    def upload_file(
        self,
        url: str,
        file_path: str
    ) -> bool:
        """上传文件到MinIO
        
        Args:
            url: 目标文件路径
            file_path: 本地文件路径
            
        Returns:
            bool: 上传是否成功
        """
        try:
            # 从URL解析bucket_name和object_name
            bucket_name, object_name = self._parse_url(url)
            
            # 执行上传
            self.client.fput_object(
                bucket_name,
                object_name,
                file_path
            )
            print(f"Successfully uploaded file {file_path} to {url}")
            return True
            
        except Exception as e:
            print(f"Error uploading file: {str(e)}")
            return False

    def is_directory(self, url: str) -> bool:
        """检查对象是否是目录"""
        try:
            bucket_name, object_name = self._parse_url(url)
            
            # 检查对象是否以斜杠结尾
            if object_name.endswith('/'):
                return True

            # 列出以该对象名为前缀的所有对象
            objects = list(self.client.list_objects(bucket_name, prefix=object_name, recursive=False))

            # 如果有多个对象或者唯一的对象以斜杠结尾，则认为是目录
            if len(objects) > 1 or (len(objects) == 1 and objects[0].object_name.endswith('/')):
                return True

            return False
        except Exception as e:
            print(f"Error checking if object is directory: {str(e)}")
            return False

    def list_files(
        self,
        url: str,
        recursive: bool = True
    ) -> List[str]:
        """列出文件"""
        try:
            # 从URL解析bucket_name和prefix
            bucket_name, object_name = self._parse_url(url)
            
            # 规范化前缀
            prefix = object_name.rstrip('/') + '/' if object_name else ''
            
            # 列出对象
            objects = self.client.list_objects(bucket_name, prefix=prefix, recursive=recursive)
            file_list = []
            
            for obj in objects:
                # 跳过目录对象
                if obj.object_name.endswith('/'):
                    continue
                file_list.append(os.path.join(bucket_name, obj.object_name))
                
            return file_list
                
        except Exception as e:
            print(f"Error listing files in {url}: {str(e)}")
            return []

    def _clean_directory(self, directory: str):
        """递归清理目录"""
        try:
            if not os.path.exists(directory):
                return
            
            # 首先删除目录中的所有文件
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                if os.path.isfile(item_path):
                    os.unlink(item_path)
                elif os.path.isdir(item_path):
                    self._clean_directory(item_path)
                
            # 然后删除目录本身
            os.rmdir(directory)
        except Exception as e:
            print(f"Error cleaning directory {directory}: {str(e)}")

    def _ensure_bucket_exists(self, bucket_name: str = None) -> bool:
        """确保 bucket 存在，如果不存在则创建
        
        Args:
            bucket_name: bucket名称，如果为None则使用默认bucket
            
        Returns:
            bool: 操作是否成功
        """
        try:
            bucket = bucket_name or self.bucket_name
            
            # 检查bucket是否存在
            if not self.client.bucket_exists(bucket):
                # 创建bucket
                self.client.make_bucket(bucket)
                print(f"Created new bucket: {bucket}")
                
            return True
            
        except Exception as e:
            print(f"Error ensuring bucket exists: {str(e)}")
            return False
        
    def check_file_exists(self, url: str) -> bool:
        try:
            bucket_name, object_name = self._parse_url(url)
            
            # Try to get object stats - this will raise exception if object doesn't exist
            self.client.stat_object(bucket_name, object_name)
            return True
            
        except Exception as e:
            # File doesn't exist or other error
            return False