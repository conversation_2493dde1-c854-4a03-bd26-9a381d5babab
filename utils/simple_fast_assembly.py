#!/usr/bin/env python3
"""
简化的快速汇编处理器
基于原有的 knowledge_control 逻辑，不依赖废弃的 loaders 模块
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from utils.llm import LLM
from utils.log import log
from utils.config import config
from knowledge_control import KnowledgeControl
from utils.minio_client import MinioClient
# DISABLED: from utils.intermediate_results_processor import IntermediateResultsProcessor

# 临时禁用IntermediateResultsProcessor，避免导入问题
class IntermediateResultsProcessor:
    """临时的IntermediateResultsProcessor替代类"""
    def __init__(self):
        pass

    async def process_intermediate_results(self, results, stage="unknown"):
        return {"error": "IntermediateResultsProcessor temporarily disabled"}

    def get_processing_stats(self):
        return {"stats": {}, "cache_size": 0}
from utils.quality_assessor import QualityAssessor


@dataclass
class SimpleFastAssemblyResult:
    """简化的快速汇编结果"""
    task_id: str
    project_name: str
    action: str
    status: str
    extracted_info: Optional[Dict[str, Any]] = None
    processing_time: float = 0.0
    total_documents: int = 0
    processed_documents: int = 0
    failed_documents: int = 0
    error_message: Optional[str] = None
    created_at: Optional[str] = None
    completed_at: Optional[str] = None


class SimpleFastAssemblyProcessor:
    """简化的快速汇编处理器"""
    
    def __init__(self):
        self.llm = LLM()
        self.knowledge_control = KnowledgeControl()
        self.minio = MinioClient()
        self.results_processor = IntermediateResultsProcessor()
        self.quality_assessor = QualityAssessor()
        
        # 批量处理配置
        self.batch_size = config.get("fast_assembly.batch_size", 8)  # 每批处理的文档数量
        self.max_concurrent = config.get("fast_assembly.max_concurrent", 5)  # 最大并发数
        self.chunk_batch_size = config.get("fast_assembly.chunk_batch_size", 20)  # 文本块批量处理大小

        # 任务内优化配置
        self.skip_duplicate_docs_in_task = config.get("fast_assembly.skip_duplicate_docs_in_task", True)  # 任务内跳过重复文档
        
        # 初始化标志
        self._initialized = False

        # 任务级中间结果缓存（用于信息聚合和纠错）
        self._task_processing_cache = {}     # 正在处理的文档状态
        self._intermediate_results_cache = {} # 中间结果缓存，用于信息融合
        self._current_task_id = None         # 当前任务ID
    
    async def init(self):
        """初始化处理器"""
        if self._initialized:
            return
        
        try:
            await self.knowledge_control.init()
            self._initialized = True
            log.info("简化快速汇编处理器初始化完成")
        except Exception as e:
            log.error(f"简化快速汇编处理器初始化失败: {e}")
            raise
    
    async def process_assembly_fast(
        self,
        project_name: str,
        action: str,
        urls: List[str],
        task_id: Optional[str] = None,
        task_manager = None,
        force_refresh: bool = False
    ) -> SimpleFastAssemblyResult:
        """
        快速处理文档汇编
        
        Args:
            project_name: 项目名称
            action: 文档类型（"项目档案" 或 "文书档案"）
            urls: 文档URL列表
            task_id: 任务ID
            task_manager: 任务管理器
            force_refresh: 是否强制刷新
            
        Returns:
            快速汇编结果
        """
        if not self._initialized:
            await self.init()
        
        task_id = task_id or f"fast_assembly_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now()
        
        result = SimpleFastAssemblyResult(
            task_id=task_id,
            project_name=project_name,
            action=action,
            status="processing",
            total_documents=len(urls),
            created_at=start_time.isoformat()
        )
        
        try:
            log.info(f"开始简化快速汇编处理: {project_name}, 类型: {action}, 文档数: {len(urls)}")

            # 设置当前任务ID并清理任务缓存
            self._current_task_id = task_id
            self._task_processing_cache.clear()
            self._intermediate_results_cache.clear()

            # 0. 清理项目的历史数据（ES和MySQL）
            await self._clear_project_data(project_name, action)

            # 1. 预检查和过滤文档（仅检查文件存在性，不检查缓存）
            valid_urls, skipped_urls = await self._precheck_documents_for_task(
                urls, project_name, action
            )

            log.info(f"文档预检查完成: 有效={len(valid_urls)}, 跳过={len(skipped_urls)}")

            # 2. 批量处理文档（基于原有的 knowledge_control 逻辑）
            processed_count, failed_count = await self._batch_process_documents(
                valid_urls, project_name, action, task_id, task_manager
            )

            # 3. 统计跳过的文档
            processed_count += len(skipped_urls)
            
            # 2. 评估中间结果质量
            intermediate_assessments = []
            for doc_key, result in self._intermediate_results_cache.items():
                assessment = self.quality_assessor.assess_intermediate_result(doc_key, result)
                intermediate_assessments.append(assessment)

            # 3. 基于中间结果进行信息融合和纠错
            extracted_info = await self.results_processor.merge_and_correct_intermediate_results(
                self._intermediate_results_cache, project_name, action
            )

            # 4. 评估融合结果质量
            fusion_assessment = self.quality_assessor.assess_fusion_result(
                extracted_info, self._intermediate_results_cache
            )

            # 5. 生成质量报告
            quality_report = self.quality_assessor.generate_quality_report(
                intermediate_assessments, fusion_assessment
            )

            # 6. 将质量信息添加到结果中
            extracted_info["quality_assessment"] = {
                "intermediate_assessments": intermediate_assessments,
                "fusion_assessment": fusion_assessment,
                "quality_report": quality_report
            }
            
            # 3. 完成处理
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            result.status = "completed"
            result.extracted_info = extracted_info
            result.processing_time = processing_time
            result.processed_documents = processed_count
            result.failed_documents = failed_count
            result.completed_at = end_time.isoformat()
            
            log.info(f"简化快速汇编处理完成: {project_name}, 耗时: {processing_time:.2f}s")
            return result
            
        except Exception as e:
            log.error(f"简化快速汇编处理失败: {e}")
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            result.status = "failed"
            result.error_message = str(e)
            result.processing_time = processing_time
            result.completed_at = end_time.isoformat()
            
            return result

    async def _clear_project_data(
        self,
        project_name: str,
        action: str
    ) -> bool:
        """清理项目的历史数据"""
        try:
            log.info(f"开始清理项目历史数据: {project_name}, 类型: {action}")

            # 1. 清理knowledge_control中的项目数据
            if hasattr(self.knowledge_control, 'clear_project_data'):
                await self.knowledge_control.clear_project_data(project_name)
                log.info(f"已清理knowledge_control数据: {project_name}")

            # 2. 清理ES中的项目数据（如果有ES集成）
            # TODO: 如果后续集成ES，在这里添加ES清理逻辑

            # 3. 清理MySQL中的项目数据（如果有结构化存储）
            # TODO: 如果后续集成MySQL结构化存储，在这里添加清理逻辑

            log.info(f"项目历史数据清理完成: {project_name}")
            return True

        except Exception as e:
            log.error(f"清理项目历史数据失败: {e}")
            return False

    async def _precheck_documents_for_task(
        self,
        urls: List[str],
        project_name: str,
        action: str
    ) -> tuple[List[str], List[str]]:
        """任务级文档预检查（仅检查文件存在性和任务内重复）"""
        valid_urls = []
        skipped_urls = []

        for url in urls:
            try:
                # 检查文档是否存在
                json_url = os.path.splitext(url)[0] + ".json"
                if not self.minio.check_file_exists(json_url):
                    log.warning(f"JSON文件不存在，跳过: {json_url}")
                    skipped_urls.append(url)
                    continue

                # 检查是否在当前任务中已经在处理
                task_cache_key = f"{self._current_task_id}_{url}"
                if self.skip_duplicate_docs_in_task and task_cache_key in self._task_processing_cache:
                    log.debug(f"任务内重复文档，跳过: {url}")
                    skipped_urls.append(url)
                    continue

                valid_urls.append(url)

            except Exception as e:
                log.error(f"预检查文档失败 {url}: {e}")
                skipped_urls.append(url)

        return valid_urls, skipped_urls

    async def _precheck_documents(
        self,
        urls: List[str],
        project_name: str,
        action: str,
        force_refresh: bool
    ) -> tuple[List[str], List[str]]:
        """预检查文档，过滤已处理或无效的文档"""
        valid_urls = []
        skipped_urls = []

        for url in urls:
            try:
                # 生成文档缓存键
                cache_key = f"{project_name}_{action}_{url}"

                # 检查是否强制刷新
                if force_refresh:
                    valid_urls.append(url)
                    continue

                # 检查缓存
                if self.enable_cache and cache_key in self._doc_cache:
                    skipped_urls.append(url)
                    log.debug(f"跳过已缓存文档: {url}")
                    continue

                # 检查文档是否存在
                json_url = os.path.splitext(url)[0] + ".json"
                if not self.minio.check_file_exists(json_url):
                    log.warning(f"JSON文件不存在，跳过: {json_url}")
                    skipped_urls.append(url)
                    continue

                # 检查是否正在处理中
                if cache_key in self._processing_cache:
                    log.debug(f"文档正在处理中，跳过: {url}")
                    skipped_urls.append(url)
                    continue

                valid_urls.append(url)

            except Exception as e:
                log.error(f"预检查文档失败 {url}: {e}")
                skipped_urls.append(url)

        return valid_urls, skipped_urls

    async def _batch_process_documents(
        self,
        urls: List[str],
        project_name: str,
        action: str,
        task_id: str,
        task_manager = None
    ) -> tuple[int, int]:
        """批量处理文档"""
        
        # 根据action类型获取对应的配置
        if action == "文书档案":
            extract_config = config.get('conference_extract', {})
        elif action == "项目档案":
            extract_config = config.get('project_extract', {})
        else:
            raise ValueError(f"不支持的文档类型: {action}")
        
        processed_count = 0
        failed_count = 0
        
        # 分批处理URL
        for i in range(0, len(urls), self.batch_size):
            batch_urls = urls[i:i + self.batch_size]
            
            # 并发处理当前批次
            tasks = [
                self._process_single_url(url, project_name, action, extract_config, task_id, task_manager)
                for url in batch_urls
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_count += 1
                    log.error(f"文档处理失败: {result}")
                elif result:
                    processed_count += 1
                else:
                    failed_count += 1
        
        return processed_count, failed_count
    
    async def _process_single_url(
        self,
        url: str,
        project_name: str,
        action: str,
        extract_config: Dict[str, Any],
        task_id: str,
        task_manager = None
    ) -> bool:
        """处理单个URL"""
        # 使用任务级缓存键
        task_cache_key = f"{task_id}_{url}"

        try:
            # 标记为正在处理（任务级）
            self._task_processing_cache[task_cache_key] = datetime.now()

            # 性能监控
            start_time = datetime.now()
            # 获取JSON文件路径
            json_url = os.path.splitext(url)[0] + ".json"
            
            # 下载JSON文件
            json_path = self.minio.download_file(json_url)
            if not json_path or not os.path.exists(json_path):
                log.error(f"JSON文件不存在: {json_url}")
                return False
            
            # 读取JSON内容
            with open(json_path, 'r', encoding='utf-8') as f:
                doc_data = json.load(f)
            
            # 验证数据
            if not doc_data or not doc_data.get("pages"):
                log.error(f"JSON文件内容无效: {json_url}")
                return False
            
            # 使用 knowledge_control 处理文档
            success = await self.knowledge_control.process_document(
                doc_data,
                project_name,
                action,
                config=extract_config
            )

            # 如果处理成功，提取并缓存中间结果
            if success:
                await self._extract_and_cache_intermediate_results(
                    doc_data, project_name, action, url
                )

            # 处理完成，记录性能
            processing_time = (datetime.now() - start_time).total_seconds()

            if success:
                log.info(f"文档处理成功: {json_url} (耗时: {processing_time:.2f}s)")
            else:
                log.error(f"文档处理失败: {json_url} (耗时: {processing_time:.2f}s)")

            return success

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            log.error(f"处理单个URL失败 {url}: {e} (耗时: {processing_time:.2f}s)")
            return False

        finally:
            # 清理任务级处理标记
            self._task_processing_cache.pop(task_cache_key, None)

    def _assess_document_complexity(self, doc_data: Dict[str, Any]) -> float:
        """评估文档复杂度"""
        try:
            complexity_score = 0.0

            # 1. 文档长度评估
            total_length = 0
            page_count = len(doc_data.get("pages", []))

            for page in doc_data.get("pages", []):
                content = page.get("content", "")
                total_length += len(content)

            # 长度评分 (0-0.3)
            if total_length > 10000:  # 超长文档
                complexity_score += 0.3
            elif total_length > 5000:  # 长文档
                complexity_score += 0.2
            elif total_length > 2000:  # 中等文档
                complexity_score += 0.1

            # 2. 页数评估 (0-0.2)
            if page_count > 20:
                complexity_score += 0.2
            elif page_count > 10:
                complexity_score += 0.15
            elif page_count > 5:
                complexity_score += 0.1

            # 3. 内容复杂度评估 (0-0.3)
            content_text = " ".join(page.get("content", "") for page in doc_data.get("pages", []))

            # 检查是否包含复杂结构
            complexity_indicators = [
                r'表\d+[：:]',  # 表格
                r'图\d+[：:]',  # 图表
                r'附件\d+',    # 附件
                r'第[一二三四五六七八九十\d]+章',  # 章节
                r'第[一二三四五六七八九十\d]+条',  # 条款
                r'\d+\.\d+\.\d+',  # 多级编号
                r'￥[\d,]+\.?\d*',  # 金额符号
                r'\d{4}年\d{1,2}月\d{1,2}日',  # 具体日期
            ]

            indicator_count = sum(1 for pattern in complexity_indicators
                                if re.search(pattern, content_text))

            complexity_score += min(indicator_count * 0.05, 0.3)

            # 4. 字段密度评估 (0-0.2)
            # 估算可能包含的字段数量
            field_indicators = [
                '项目名称', '项目编号', '负责人', '金额', '时间', '地点',
                '状态', '类型', '概述', '预算', '投资', '建设', '完成'
            ]

            field_count = sum(1 for indicator in field_indicators
                            if indicator in content_text)

            if field_count > 10:
                complexity_score += 0.2
            elif field_count > 7:
                complexity_score += 0.15
            elif field_count > 5:
                complexity_score += 0.1

            return min(complexity_score, 1.0)  # 限制在0-1之间

        except Exception as e:
            log.error(f"评估文档复杂度失败: {e}")
            return 0.5  # 默认中等复杂度

    async def _extract_and_cache_intermediate_results(
        self,
        doc_data: Dict[str, Any],
        project_name: str,
        action: str,
        url: str
    ):
        """从单个文档中提取中间结果并缓存"""
        try:
            # 智能选择提取方式
            doc_complexity = self._assess_document_complexity(doc_data)

            if doc_complexity > 0.7:  # 复杂文档使用分组提取
                log.info(f"文档复杂度高({doc_complexity:.2f})，使用分组提取: {url}")
                extracted_data = await self.results_processor.extract_structured_info_grouped(
                    doc_data, action, self.llm
                )
            else:  # 简单文档使用一次性提取
                log.info(f"文档复杂度低({doc_complexity:.2f})，使用一次性提取: {url}")
                extraction_prompt = self.results_processor.build_extraction_prompt(doc_data, action)

                response = await self.llm.chat([
                    {
                        "role": "system",
                        "content": "你是一个信息提取助手。请从文档中提取结构化信息，以JSON格式返回。"
                    },
                    {"role": "user", "content": extraction_prompt}
                ])

                extracted_data = self.results_processor.parse_extraction_response(response, action)

            if extracted_data:
                # 计算置信度
                confidence = self.results_processor.calculate_extraction_confidence(extracted_data)

                # 缓存中间结果
                doc_key = f"doc_{len(self._intermediate_results_cache)}"
                self._intermediate_results_cache[doc_key] = {
                    "url": url,
                    "source": doc_data.get("source", ""),
                    "extracted_data": extracted_data,
                    "extraction_time": datetime.now().isoformat(),
                    "confidence": confidence
                }

                log.info(f"中间结果已缓存: {url}, 字段数: {len(extracted_data)}, 置信度: {confidence}")

        except Exception as e:
            log.error(f"提取中间结果失败 {url}: {e}")

    async def _extract_summary_from_db(
        self,
        project_name: str,
        action: str
    ) -> Dict[str, Any]:
        """从数据库中提取汇总信息"""
        try:
            # 使用 knowledge_control 的汇总功能
            summary_result = await self.knowledge_control.get_project_summary(
                project_name, action
            )
            
            if summary_result and summary_result.get("summary"):
                return {
                    "project_name": project_name,
                    "action": action,
                    "summary": summary_result["summary"],
                    "extraction_method": "simple_fast_batch",
                    "extraction_time": datetime.now().isoformat()
                }
            else:
                # 如果没有汇总结果，返回基本信息
                return {
                    "project_name": project_name,
                    "action": action,
                    "summary": "文档处理完成，但未生成汇总信息",
                    "extraction_method": "simple_fast_batch",
                    "extraction_time": datetime.now().isoformat()
                }
                
        except Exception as e:
            log.error(f"从数据库提取汇总信息失败: {e}")
            return {
                "project_name": project_name,
                "action": action,
                "summary": f"汇总信息提取失败: {str(e)}",
                "extraction_method": "simple_fast_batch",
                "extraction_time": datetime.now().isoformat(),
                "error": str(e)
            }
    
    async def get_cached_result(
        self,
        project_name: str,
        action: str
    ) -> Optional[Dict[str, Any]]:
        """获取缓存的结果"""
        try:
            # 从 knowledge_control 获取项目汇总
            summary_result = await self.knowledge_control.get_project_summary(
                project_name, action
            )
            
            if summary_result and summary_result.get("summary"):
                return {
                    "project_name": project_name,
                    "action": action,
                    "cached_summary": summary_result["summary"],
                    "cache_time": datetime.now().isoformat()
                }
            
            return None
            
        except Exception as e:
            log.error(f"获取缓存结果失败: {e}")
            return None
    
    async def clear_project_cache(self, project_name: str) -> bool:
        """清除项目数据（包括ES和MySQL中的结果）"""
        try:
            # 清理项目的所有历史数据
            success = await self._clear_project_data(project_name, "all")

            if success:
                log.info(f"项目数据已清除: {project_name}")
            else:
                log.warning(f"项目数据清除可能不完整: {project_name}")

            return success

        except Exception as e:
            log.error(f"清除项目数据失败: {e}")
            return False
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        try:
            # 任务级缓存统计
            task_cache_stats = {
                "current_task_id": self._current_task_id,
                "task_processing_cache_size": len(self._task_processing_cache),
                "intermediate_results_cache_size": len(self._intermediate_results_cache),
                "active_processing_docs": list(self._task_processing_cache.keys()),
                "cached_intermediate_results": list(self._intermediate_results_cache.keys())
            }

            return {
                "processor_info": {
                    "type": "simple_fast_assembly",
                    "version": "3.0_task_scoped",
                    "initialized": self._initialized,
                    "batch_size": self.batch_size,
                    "max_concurrent": self.max_concurrent,
                    "chunk_batch_size": self.chunk_batch_size,
                    "skip_duplicate_docs_in_task": self.skip_duplicate_docs_in_task
                },
                "task_cache_stats": task_cache_stats,
                "knowledge_control_stats": {
                    "initialized": self.knowledge_control._initialized if hasattr(self.knowledge_control, '_initialized') else False
                },
                "design_principles": [
                    "每次汇编都清理历史数据，确保结果的完整性和一致性",
                    "中间结果缓存用于信息聚合和纠错，提升结果准确性",
                    "多文档信息融合，通过置信度和一致性分析纠正错误",
                    "任务完成后自动清理缓存，不跨任务复用",
                    "适当增加batch_size可以提升并发处理效率"
                ]
            }

        except Exception as e:
            log.error(f"获取处理统计失败: {e}")
            return {"error": str(e)}

    def clear_task_cache(self):
        """清理当前任务的缓存"""
        processing_count = len(self._task_processing_cache)
        intermediate_count = len(self._intermediate_results_cache)

        self._task_processing_cache.clear()
        self._intermediate_results_cache.clear()
        self._current_task_id = None

        log.info(f"已清理任务缓存: 处理缓存={processing_count}, 中间结果缓存={intermediate_count}")

    def get_task_cache_info(self) -> Dict[str, Any]:
        """获取任务缓存信息"""
        # 分析中间结果的质量
        intermediate_analysis = {}
        if self._intermediate_results_cache:
            confidences = [
                result.get("confidence", 0.0)
                for result in self._intermediate_results_cache.values()
            ]
            intermediate_analysis = {
                "avg_confidence": round(sum(confidences) / len(confidences), 2),
                "min_confidence": min(confidences),
                "max_confidence": max(confidences),
                "total_fields_extracted": sum(
                    len(result.get("extracted_data", {}))
                    for result in self._intermediate_results_cache.values()
                )
            }

        return {
            "current_task_id": self._current_task_id,
            "processing_docs": list(self._task_processing_cache.keys()),
            "processing_count": len(self._task_processing_cache),
            "intermediate_results_count": len(self._intermediate_results_cache),
            "intermediate_analysis": intermediate_analysis
        }
