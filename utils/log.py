import sys
from pathlib import Path

from loguru import logger

# 添加一个上下文变量来存储用户信息
from contextvars import ContextVar
user_context = ContextVar('user_id', default='anonymous')

# 全局标志，确保logger只初始化一次
_logger_initialized = False

def reset_logger():
    """重置logger状态，用于测试"""
    global _logger_initialized
    _logger_initialized = False
    logger.remove()

def get_logger(save_dir: str = "."):
    global _logger_initialized

    # 如果已经初始化过，直接返回logger
    if _logger_initialized:
        return logger

    # 更新 loguru 格式，简化以避免格式错误
    loguru_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )

    # 移除默认的handler
    logger.remove()

    # 添加控制台输出 (使用stdout确保在终端中可见)
    logger.add(sys.stdout, format=loguru_format, level="INFO", enqueue=True)

    # 确保日志目录存在
    log_path = Path(save_dir)
    log_path.mkdir(parents=True, exist_ok=True)

    # 添加文件输出
    save_file = log_path / "kg.log"
    logger.add(save_file, rotation="50 MB", retention=1, format=loguru_format, level="INFO", enqueue=True)

    # 标记为已初始化
    _logger_initialized = True

    logger.info(f"📝 日志系统初始化完成，文件路径: {save_file}")

    return logger


log_dir = Path(__file__).resolve().parent.parent / "data/logs"
log = get_logger(str(log_dir))
log.setlevel("DEBUG")

# 示例：如何在代码中使用上下文设置用户信息
# async def some_request_handler(user_id: int):
#     user_context.set(str(user_id))
#     log.info("处理请求") # 这条日志将包含 user_id
#     # ... 处理逻辑 ...

if __name__ == "__main__":
    log.info("默认用户测试")
    user_context.set("user123")
    log.info("设置用户 user123")
    log.warning("一个警告信息")
    user_context.set("admin")
    log.error("管理员错误信息")
    log.success("成功操作")
    log.critical("test")
    log.success("test")
    