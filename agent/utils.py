from enum import Enum
from typing import Any
import json

class Color(Enum):
    # 角色颜色
    SYSTEM = '\033[95m'    # 紫色
    USER = '\033[94m'      # 蓝色
    ASSISTANT = '\033[92m' # 绿色
    TOOL = '\033[93m'      # 黄色
    ERROR = '\033[91m'     # 红色
    
    # 其他颜色
    HEADER = '\033[95m'
    OK = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    
    # 样式
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

class Logger:
    @staticmethod
    def system(title: str, content: Any = None):
        """系统日志"""
        print(f"\n{Color.SYSTEM.value}{Color.BOLD.value}[System] {title}{Color.END.value}")
        if content:
            if isinstance(content, (dict, list)):
                print(f"{Color.SYSTEM.value}{json.dumps(content, ensure_ascii=False, indent=2)}{Color.END.value}")
            else:
                print(f"{Color.SYSTEM.value}{str(content)}{Color.END.value}")

    @staticmethod
    def user(title: str, content: Any = None):
        """用户日志"""
        print(f"\n{Color.USER.value}{Color.BOLD.value}[User] {title}{Color.END.value}")
        if content:
            print(f"{Color.USER.value}{str(content)}{Color.END.value}")

    @staticmethod
    def assistant(title: str, content: Any = None):
        """助手日志"""
        print(f"\n{Color.ASSISTANT.value}{Color.BOLD.value}[Assistant] {title}{Color.END.value}")
        if content:
            if isinstance(content, (dict, list)):
                print(f"{Color.ASSISTANT.value}{json.dumps(content, ensure_ascii=False, indent=2)}{Color.END.value}")
            else:
                print(f"{Color.ASSISTANT.value}{str(content)}{Color.END.value}")

    @staticmethod
    def tool(title: str, content: Any = None):
        """工具日志"""
        print(f"\n{Color.TOOL.value}{Color.BOLD.value}[Tool] {title}{Color.END.value}")
        if content:
            if isinstance(content, (dict, list)):
                print(f"{Color.TOOL.value}{json.dumps(content, ensure_ascii=False, indent=2)}{Color.END.value}")
            else:
                print(f"{Color.TOOL.value}{str(content)}{Color.END.value}")

    @staticmethod
    def error(title: str, content: Any = None):
        """错误日志"""
        print(f"\n{Color.ERROR.value}{Color.BOLD.value}[Error] {title}{Color.END.value}")
        if content:
            print(f"{Color.ERROR.value}{str(content)}{Color.END.value}")

    @staticmethod
    def divider():
        """打印分隔线"""
        print(f"\n{Color.SYSTEM.value}{'='*50}{Color.END.value}") 