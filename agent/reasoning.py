from dataclasses import dataclass
from typing import Dict, Optional, List, Any
import httpx
import json
import re
from utils.config import config
@dataclass
class ReasoningResult:
    thoughts: str
    needs_tool: bool = False
    tool_name: Optional[str] = None
    tool_args: Optional[Dict] = None
    tool_result: Optional[Any] = None
    
    def incorporate_tool_result(self, result: Any):
        """整合工具执行结果"""
        self.tool_result = result
        self.thoughts += f"\nTool result: {result}"

class Reasoning:
    def __init__(
        self,
        api_url: str = config.get("llm.api_url"),
        token: str = config.get("llm.token"),
        model: str = config.get("llm.model"),
    ):
        self.api_url = api_url
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        self.model = model

    def _build_thinking_prompt(
        self,
        messages: List[Dict[str, str]],
        memories: List[Dict[str, str]],
        knowledge: List[str],
        tools_desc: str
    ) -> str:
        """构建思考提示词"""
        prompt_parts = [
            "You are an AI assistant tasked with analyzing user input and determining the best response strategy.",
            "\nAvailable Tools:",
            tools_desc,
            
            "\nAnalysis Process:",
            "1. Understand the user's request",
            "2. Check if any available tools can help",
            "3. Choose the most appropriate response strategy:",
            "   - Use a tool if it matches the task exactly",
            "   - Use your knowledge if no tool fits",
            
            "\nGuidelines:",
            "1. ALWAYS use available tools when they match the task",
            "2. Keep responses concise and professional",
            "3. Focus on accuracy and reliability",
            "4. If unsure, prefer using your knowledge over tools",
            
            "\nRecent Messages:"
        ]
        
        # Add recent messages
        for msg in messages[-3:]:
            prompt_parts.append(f"{msg['role']}: {msg['content']}")
        
        # Add relevant memories if any
        if memories:
            prompt_parts.append("\nRelevant Memories:")
            for memory in memories:
                prompt_parts.append(f"Q: {memory['query']}\nA: {memory['response']}")
        
        # Add relevant knowledge if any
        if knowledge:
            prompt_parts.append("\nRelevant Knowledge:")
            prompt_parts.extend(knowledge)
        
        # Add output format instructions - 修改这里，分开添加每一行
        prompt_parts.append("\nPlease provide your analysis in the following format:")
        prompt_parts.append("Thought: [Analyze the request and explain your reasoning]")
        prompt_parts.append("Use Tool: [yes/no]")
        prompt_parts.append("Tool Name: [tool name if needed, otherwise leave empty]")
        prompt_parts.append("Parameters: [tool parameters if needed, otherwise leave empty]")
        
        prompt_parts.append("\nNotes:")
        prompt_parts.append("1. For calculator, only provide the mathematical expression (e.g., '23 + 45')")
        prompt_parts.append("2. If not using a tool, explain your response strategy in the Thought section")
        prompt_parts.append("3. Keep the format strict but make the content natural and helpful")
        
        return "\n".join(prompt_parts)

    def _parse_thinking_result(self, thoughts: str) -> ReasoningResult:
        """解析LLM的思考结果，增强容错性"""
        try:
            # 默认值
            thinking_content = ""
            needs_tool = False
            tool_name = None
            tool_args = None
            
            # 使用正则表达式进行更灵活的匹配
            thought_match = re.search(r"(?i)Thought:?\s*(.+?)(?=Use Tool:|$)", thoughts, re.DOTALL)
            if thought_match:
                thinking_content = thought_match.group(1).strip()
            
            # 检查是否使用工具
            use_tool_match = re.search(r"(?i)Use Tool:?\s*(yes|no|true|false)", thoughts)
            if use_tool_match:
                needs_tool = use_tool_match.group(1).lower() in ['yes', 'true']
            
            # 如果需要使用工具，解析工具名称和参数
            if needs_tool:
                # 工具名称匹配
                tool_match = re.search(r"(?i)Tool Name:?\s*(\w+)", thoughts)
                if tool_match:
                    tool_name = tool_match.group(1).strip().lower()
                    
                # 参数匹配
                param_match = re.search(r"(?i)Parameters:?\s*(.+?)(?=\n|$)", thoughts)
                if param_match:
                    param_value = param_match.group(1).strip()
                    
                    # 处理计算器参数
                    if tool_name == "calculator":
                        # 清理数学表达式，只保留数字和运算符
                        expression = re.sub(r'[^0-9+\-*/().\s]', '', param_value)
                        if expression:
                            tool_args = {"expression": expression.strip()}
                    else:
                        # 其他工具的参数处理
                        tool_args = {"value": param_value}
            
            # 验证和清理结果
            if not tool_name or tool_name.lower() in ['none', 'empty', '-']:
                tool_name = None
                needs_tool = False
                tool_args = None
            
            return ReasoningResult(
                thoughts=thinking_content or "Analyzing user request",
                needs_tool=needs_tool,
                tool_name=tool_name,
                tool_args=tool_args
            )
            
        except Exception as e:
            print(f"Error parsing thinking result: {str(e)}")
            # 返回安全的默认值
            return ReasoningResult(
                thoughts="Error occurred during analysis, falling back to direct response",
                needs_tool=False
            )

    async def think(
        self,
        messages: List[Dict[str, str]],
        memories: List[Dict[str, str]],
        knowledge: List[str],
        tools_desc: str
    ) -> ReasoningResult:
        """分析用户输入并决定下一步行动"""
        try:
            # 构建思考提示词，传入工具描述
            prompt = self._build_thinking_prompt(
                messages, 
                memories, 
                knowledge,
                tools_desc
            )
            
            # 调用LLM进行思考
            thoughts = await self._call_llm(prompt)
            
            # 解析思考结果
            result = self._parse_thinking_result(thoughts)
            
            # 验证结果的合理性
            if result.needs_tool and not result.tool_name:
                print("Warning: Tool needed but no tool name provided")
                result.needs_tool = False
            
            if result.tool_name and not result.tool_args:
                print("Warning: Tool specified but no parameters provided")
                result.needs_tool = False
                result.tool_name = None
            
            return result
            
        except Exception as e:
            print(f"Critical error in thinking process: {str(e)}")
            # 返回安全的默认值
            return ReasoningResult(
                thoughts="Error in analysis process, will provide direct response",
                needs_tool=False
            )

    async def _call_llm(self, prompt: str) -> str:
        """调用LLM"""
        try:
            async with httpx.AsyncClient() as client:
                request_data = {
                    "model": self.model,
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.6,
                    "max_tokens": 16384
                }
                
                response = await client.post(
                    f"{self.api_url}/v1/chat/completions",
                    headers=self.headers,
                    json=request_data,
                    timeout=300.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data["choices"][0]["message"]["content"]
                else:
                    raise Exception(f"LLM API call failed with status {response.status_code}")
                    
        except Exception as e:
            raise Exception(f"Error calling LLM: {str(e)}")
    