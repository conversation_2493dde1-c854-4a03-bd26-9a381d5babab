from typing import List, Dict, Any, Optional
import httpx
from .memory import Memory 
from .knowledge import Knowledge
from .tools import ToolRegistry
from .reasoning import Reasoning, ReasoningResult
from .utils import Logger
from utils.llm import LLM
from utils.sqkg import SQLKnowledge
from utils.config import config
import re

logger = Logger()

class O1Agent:
    # 系统提示模板
    SYSTEM_PROMPT = """作为专业分析助手，请用自然流畅的语言进行问题解答，遵循以下准则：
【核心原则】
1. 信息精准化
- 优先提取通知中的核心要素：发文单位、项目名称、工作目标、实施要求
- 关联政策背景（如"双碳目标"等）到具体工作内容

2. 表达结构化
- 采用"总-分-总"结构：
  首段：核心事实（谁+做什么+为什么）
  中间：分维度说明（政策背景→实施要点→预期成果）
  结尾：特别说明（如涉及时间节点/责任单位）

3. 衔接自然化
- 使用政策文件惯用表述（"为贯彻落实...""经研究决定..."）
- 用逻辑连接词替代符号（"基于""为此""旨在"）
- 关键数据直接融入叙述

"""

    def __init__(
        self,
        api_url: str = config.get("llm.api_url"),
        token: str = config.get("llm.token"),
        model: str = config.get("llm.model"),
        temperature: float = 0.7,
        max_tokens: int = 32000,
        debug: bool = True
    ):
        self.api_url = api_url
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.debug = debug
        
        # 初始化组件
        self.memory = Memory(api_url=api_url, token=token)
        self.knowledge = Knowledge()
        self.sql_knowledge = SQLKnowledge(config.get('sqlite.db_path'))
        self.tools = ToolRegistry()
        self.reasoning = Reasoning(api_url=api_url, token=token, model=model)
        self.llm = LLM(api_url=api_url, token=token, model=model)
        self._context = ""  # 添加上下文存储
        
    def set_context(self, context: str):
        """设置上下文信息"""
        self._context = context.strip() if context else ""
        
    async def chat(self, messages: List[Dict[str, str]]) -> Dict[str, str]:
        """处理用户对话"""
        try:
            # 获取最后一条用户消息
            user_message = messages[-1]["content"]
            
            # 构建上下文信息
            context_parts = []
            
            # 添加预设的上下文（如果有）
            if self._context:
                context_parts.append(f"预设上下文：\n{self._context}")
            
            # 获取完整上下文
            search_context = await self.knowledge.get_context(
                question=user_message,
                question_embedding=None,
                project_name="",
                project_type="",
                llm=self.llm
            )
            
            # 添加系统提示
            full_messages = [
                {"role": "system", "content": self.SYSTEM_PROMPT}
            ]
            
            # 添加ES知识库上下文
            if search_context.get("es_context", {}).get("context"):
                es_context = "\n".join(search_context["es_context"]["context"])
                context_parts.append(f"文档信息：\n{es_context}")
            
            # 添加SQL查询结果
            if search_context.get("sql_context"):
                # 添加项目信息
                if search_context["sql_context"].get("project_data"):
                    projects_info = "\n".join([
                        f"- 项目名称：{proj['name']}\n"
                        f"  开始日期：{proj['start_date']}\n"
                        f"  结束日期：{proj['end_date']}\n"
                        f"  总投资：{proj['total_investment']}元\n"
                        f"  承担单位：{proj['responsible_unit']}\n"
                        f"  负责人：{proj['leader']}\n"
                        f"  主要研究点：{proj['research_points']}\n"
                        f"  创新点：{proj['innovation']}\n"
                        f"  主要交付成果：{proj['main_deliverables']}\n"
                        f"  专利：{proj['patent']}"
                        for proj in search_context["sql_context"]["project_data"]
                    ])
                    context_parts.append(f"项目信息：\n{projects_info}")
                
                # 添加会议信息
                if search_context["sql_context"].get("conference_data"):
                    conference_info = "\n".join([
                        f"- 文书名称：{conf['name']}\n"
                        f"  发起时间：{conf['date']}\n"
                        f"  文书类型：{conf['type']}\n"
                        f"  发起组织：{conf['organizer']}\n"
                        f"  参与者：{conf['participants']}\n"
                        f"  摘要：{conf['summary']}"
                        for conf in search_context["sql_context"]["conference_data"]
                    ])
                    context_parts.append(f"会议信息：\n{conference_info}")
            
            # 合并所有上下文
            if context_parts:
                context_message = "请基于以下信息回答问题。如果信息不足，请明确指出：\n\n" + "\n\n".join(context_parts)
                if self.debug:
                    print(f"Context:\n{context_message}\n")
                full_messages.append({
                    "role": "system",
                    "content": context_message
                })
            
            # 添加用户消息历史
            full_messages.extend(messages)
            
            # 调用LLM进行对话
            response = await self.llm.chat(full_messages)
            
            return {
                "content": response,
                "role": "assistant"
            }
            
        except Exception as e:
            print(f"Error in O1Agent chat: {str(e)}")
            return {
                "content": f"处理出错: {str(e)}",
                "role": "assistant"
            }
            
    async def close(self):
        """关闭资源"""
        await self.knowledge.close()
        if hasattr(self, 'sql_knowledge'):
            self.sql_knowledge.close()

    async def chat_with_context(
        self, 
        messages: List[Dict[str, str]], 
        project_name: str,
        project_type: str,
        year: str,
        keywords: List[str],
        question_embedding: List[float]
    ) -> Dict[str, Any]:
        """处理用户对话"""
        try:
            # 获取最后一条用户消息
            question = messages[-1]["content"]
            
            # 构建上下文信息
            context_parts = []
            
            # 添加预设的上下文（如果有）
            if self._context:
                context_parts.append(f"预设上下文：\n{self._context}")
            
            # 获取完整上下文
            search_context = await self.knowledge.get_context(
                question=question,
                question_embedding=question_embedding,
                project_name=project_name,
                project_type=project_type,
                year=year,
                keywords=keywords,
                llm=self.llm
            )
            
            # 添加系统提示
            full_messages = [
                {"role": "system", "content": self.SYSTEM_PROMPT}
            ]
            
            # 添加ES知识库上下文
            if search_context.get("es_context", {}).get("context"):
                es_context = "\n".join(search_context["es_context"]["context"])
                context_parts.append(f"文档信息：\n{es_context}")
            
            # 添加SQL查询结果
            if search_context.get("sql_context"):
                formatted_sql_context = search_context["sql_context"].get("formatted_context")
                if formatted_sql_context:
                    context_parts.append(formatted_sql_context)
            
            # 合并所有上下文
            if context_parts:
                context_message = "请基于以下信息回答问题。如果信息不足，请明确指出：\n\n" + "\n\n".join(context_parts)
                if self.debug:
                    print(f"Context:\n{context_message}\n")
                full_messages.append({
                    "role": "system",
                    "content": context_message
                })
            
            # 添加用户消息历史
            full_messages.extend(messages)
            
            # 调用LLM进行对话
            response = await self.llm.chat(full_messages)
            
            return {
                "content": response,
                "source_documents": search_context.get("es_context", {}).get("source_documents", []),
                "role": "assistant"
            }
            
        except Exception as e:
            print(f"Error in O1Agent chat: {str(e)}")
            return {
                "content": f"处理出错: {str(e)}",
                "source_documents": [],
                "role": "assistant"
            }
            
    async def handle_user_query(self, question: str, question_embedding: List[float], project_name: str, project_type: str, year: str, keywords: List[str], prompt_template: str) -> str:
        """处理用户查询"""
        try:
            from utils.utils import collapse_think_response

            search_context = await self.knowledge.get_context(
                question=question,
                question_embedding=question_embedding,
                project_name=project_name,
                project_type=project_type,
                year=year,
                keywords=keywords,
                llm=self.llm
            )
            context = self._parse_context(search_context)
            user_query = prompt_template.format(query=question, context=context)
            messages = [
                {"role": "user", "content": user_query}
            ]
            raw_response = await self.llm.chat(messages)
            # 处理LLM响应，转换思考过程为可折叠HTML
            return collapse_think_response(raw_response)
        except Exception as e:
            print(f"Error in O1Agent handle_user_query: {str(e)}")
            return f"处理出错: {str(e)}"

    def _parse_context(self, search_context: Dict[str, Any], include_prefix: bool = True) -> str:
        """解析上下文信息为文本格式
        
        Args:
            search_context: 搜索上下文字典
            include_prefix: 是否包含提示前缀
            
        Returns:
            格式化的上下文文本
        """
        context_parts = []
        
        # 添加预设的上下文（如果有）
        if self._context:
            context_parts.append(f"预设上下文：\n{self._context}")
        
        # 添加ES知识库上下文
        if search_context.get("es_context", {}).get("context"):
            es_context = "\n".join(search_context["es_context"]["context"])
            context_parts.append(f"文档信息：\n{es_context}")
        
        # 添加SQL查询结果
        if search_context.get("sql_context"):
            # 添加项目信息
            if search_context["sql_context"].get("project_data"):
                projects_info = "\n".join([
                    f"- 项目名称：{proj['name']}\n"
                    f"  开始日期：{proj['start_date']}\n"
                    f"  结束日期：{proj['end_date']}\n"
                    f"  总投资：{proj['total_investment']}元\n"
                    f"  承担单位：{proj['responsible_unit']}\n"
                    f"  负责人：{proj['leader']}\n"
                    f"  主要研究点：{proj['research_points']}\n"
                    f"  创新点：{proj['innovation']}\n"
                    f"  主要交付成果：{proj['main_deliverables']}\n"
                    f"  专利：{proj['patent']}"
                    for proj in search_context["sql_context"]["project_data"]
                ])
                context_parts.append(f"项目信息：\n{projects_info}")
            
            # 添加会议信息
            if search_context["sql_context"].get("conference_data"):
                conference_info = "\n".join([
                    f"- 文书名称：{conf['name']}\n"
                    f"  发起时间：{conf['date']}\n"
                    f"  文书类型：{conf['type']}\n"
                    f"  发起组织：{conf['organizer']}\n"
                    f"  参与者：{conf['participants']}\n"
                    f"  摘要：{conf['summary']}"
                    for conf in search_context["sql_context"]["conference_data"]
                ])
                context_parts.append(f"会议信息：\n{conference_info}")
        
        # 合并所有上下文
        if not context_parts:
            return ""
        
        if include_prefix:
            return "请基于以下信息回答问题。如果信息不足，请明确指出：\n\n" + "\n\n".join(context_parts)
        return "\n\n".join(context_parts)
