from typing import List, Dict, Optional
import faiss
import numpy as np
import httpx
import json
import time
import asyncio

class Memory:
    def __init__(
        self, 
        api_url: str = "http://localhost:8888", 
        token: str = "startfrom2023", 
        embedding_dim: int = 1024,
        max_memories: int = 1000,
        retry_attempts: int = 2,
        retry_delay: float = 1.0
    ):
        self.api_url = api_url
        self.headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
        self.embedding_dim = embedding_dim
        self.max_memories = max_memories
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        
        # 初始化FAISS索引和记忆列表
        self.index = faiss.IndexFlatL2(embedding_dim)
        self.memories = []
        self.fallback_embeddings = {}  # 存储失败时的随机向量，确保一致性

    async def add(self, query: str, response: str) -> bool:
        """
        添加新的记忆
        
        Returns:
            bool: 添加是否成功
        """
        try:
            # 如果达到最大记忆数量，删除最旧的记忆
            if len(self.memories) >= self.max_memories:
                self._remove_oldest_memory()
            
            # 获取embedding
            text = f"{query} {response}"
            embedding = await self._get_embedding_with_retry(text)
            if embedding is None:
                return False
                
            # 添加到FAISS索引
            self.index.add(np.array([embedding]).astype('float32'))
            
            # 保存记忆
            self.memories.append({
                "query": query,
                "response": response,
                "timestamp": time.time()
            })
            return True
            
        except Exception as e:
            print(f"Error adding memory: {str(e)}")
            return False

    def _remove_oldest_memory(self):
        """删除最旧的记忆并更新索引"""
        try:
            # 删除最旧的记忆
            self.memories.pop(0)
            
            # 重建FAISS索引
            new_index = faiss.IndexFlatL2(self.embedding_dim)
            
            if len(self.memories) > 0:
                # 重新获取所有记忆的embedding
                embeddings = []
                for memory in self.memories:
                    text = f"{memory['query']} {memory['response']}"
                    if text in self.fallback_embeddings:
                        embeddings.append(self.fallback_embeddings[text])
                
                if embeddings:
                    new_index.add(np.array(embeddings).astype('float32'))
            
            self.index = new_index
            
        except Exception as e:
            print(f"Error removing old memory: {str(e)}")

    async def retrieve(self, query: str, k: int = 3) -> List[Dict[str, str]]:
        """检索相关记忆"""
        if len(self.memories) == 0:
            return []
            
        try:
            # 获取查询的embedding
            query_embedding = await self._get_embedding_with_retry(query)
            if query_embedding is None:
                return []
            
            # 搜索最相似的记忆
            k = min(k, len(self.memories))
            D, I = self.index.search(np.array([query_embedding]).astype('float32'), k)
            
            # 返回相关记忆并添加相似度分数
            results = []
            for score, idx in zip(D[0], I[0]):
                if idx >= 0 and idx < len(self.memories):
                    memory = self.memories[idx].copy()
                    memory['similarity_score'] = float(score)
                    results.append(memory)
                    
            return sorted(results, key=lambda x: x['similarity_score'])
            
        except Exception as e:
            print(f"Error retrieving memories: {str(e)}")
            return []

    async def _get_embedding_with_retry(self, text: str) -> Optional[np.ndarray]:
        """带重试机制的embedding获取"""
        for attempt in range(self.retry_attempts):
            try:
                embedding = await self._get_embedding(text)
                if embedding is not None:
                    return embedding
                    
            except Exception as e:
                if attempt == self.retry_attempts - 1:
                    print(f"All embedding attempts failed: {str(e)}")
                    # 使用或生成随机向量
                    if text not in self.fallback_embeddings:
                        self.fallback_embeddings[text] = np.random.rand(self.embedding_dim).astype('float32')
                    return self.fallback_embeddings[text]
                    
            await asyncio.sleep(self.retry_delay)
        
        return None

    async def _get_embedding(self, text: str) -> Optional[np.ndarray]:
        """获取文本的嵌入向量"""
        try:
            async with httpx.AsyncClient() as client:
                request_data = {
                    "input": text,
                    "model": "xbv2"
                }
                
                response = await client.post(
                    f"{self.api_url}/v1/embeddings",
                    headers=self.headers,
                    json=request_data,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    embedding = np.array(data["data"][0]["embedding"])
                    # 验证embedding维度
                    if embedding.shape[0] != self.embedding_dim:
                        raise ValueError(f"Embedding dimension mismatch. Expected {self.embedding_dim}, got {embedding.shape[0]}")
                    return embedding.astype('float32')
                else:
                    print(f"Embedding API error: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            print(f"Error in _get_embedding: {str(e)}")
            return None