from typing import Dict, Any, Callable, Awaitable, Optional, Union, List
from dataclasses import dataclass
import inspect
import asyncio
import json

@dataclass
class ToolDescription:
    name: str
    description: str
    parameters: Dict[str, str]
    function: Callable
    async_tool: bool = False  # 标记是否为异步工具

class ToolRegistry:
    def __init__(self):
        self.tools: Dict[str, ToolDescription] = {}
    
    def register(
        self,
        name: str,
        description: str,
        parameters: Dict[str, str],
        tool_func: Union[Callable, Awaitable]
    ):
        """
        注册新工具
        
        Args:
            name: 工具名称
            description: 工具描述
            parameters: 参数说明，格式为 {"参数名": "参数描述"}
            tool_func: 工具函数，支持同步或异步函数
        """
        # 检查工具名称是否已存在
        if name in self.tools:
            raise ValueError(f"Tool {name} already exists")
            
        # 检查函数签名是否与参数匹配
        sig = inspect.signature(tool_func)
        param_names = set(sig.parameters.keys())
        if not param_names.issubset(set(parameters.keys())):
            raise ValueError(f"Tool function parameters don't match the parameter description")
        
        self.tools[name] = ToolDescription(
            name=name,
            description=description,
            parameters=parameters,
            function=tool_func,
            async_tool=inspect.iscoroutinefunction(tool_func)
        )
    
    async def execute(self, tool_name: str, args: Dict[str, Any]) -> Any:
        """
        执行工具
        
        Args:
            tool_name: 工具名称
            args: 工具参数
            
        Returns:
            工具执行结果
            
        Raises:
            ValueError: 工具不存在或参数无效
            Exception: 工具执行失败
        """
        if tool_name not in self.tools:
            raise ValueError(f"Tool {tool_name} not found")
        
        tool = self.tools[tool_name]
        
        try:
            # 检查参数
            sig = inspect.signature(tool.function)
            bound_args = sig.bind(**args)
            bound_args.apply_defaults()
            
            # 执行工具函数
            if tool.async_tool:
                result = await tool.function(**bound_args.arguments)
            else:
                result = tool.function(**bound_args.arguments)
                
            # 尝试将结果转换为JSON字符串，确保可序列化
            try:
                json.dumps(result)
                return result
            except (TypeError, ValueError):
                return str(result)
                
        except TypeError as e:
            raise ValueError(f"Invalid arguments for tool {tool_name}: {str(e)}")
        except Exception as e:
            raise Exception(f"Tool execution failed: {str(e)}")
    
    def get_tool_descriptions(self) -> str:
        """获取所有工具的描述"""
        if not self.tools:
            return "No tools available."
            
        descriptions = []
        for tool in self.tools.values():
            params = "\n".join(f"- {name}: {desc}" 
                             for name, desc in tool.parameters.items())
            descriptions.append(
                f"Tool: {tool.name}\n"
                f"Description: {tool.description}\n"
                f"Parameters:\n{params}"
            )
        return "\n\n".join(descriptions)
    
    def list_tools(self) -> List[str]:
        """获取所有工具名称列表"""
        return list(self.tools.keys())
    
    def get_tool(self, name: str) -> Optional[ToolDescription]:
        """获取指定工具的描述"""
        return self.tools.get(name)