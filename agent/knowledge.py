from typing import List, Dict, Optional, Any
from utils.eskg import E<PERSON><PERSON>nowledge
from utils.sqkg import SQLKnowledge
from utils.config import es_cfg, config
from utils.utils import get_filename_from_url

class Knowledge:
    """为Agent提供知识查询的接口类"""
    
    def __init__(self):
        # 初始化ES知识库
        es_config = {
            "username": es_cfg.get('username'),
            "password": es_cfg.get('password'),
            "host": es_cfg.get('host'),
            "port": es_cfg.get('port'),
            "index_name": es_cfg.get('index_name')
        }
        self.eskg = ESKnowledge(es_config)
        
        # 初始化SQL知识库
        self.sqlkg = SQLKnowledge(config.get('sqlite.db_path'))
        
    async def init(self):
        """初始化知识库"""
        await self.eskg.init()
        
    async def query(
        self,
        query_text: str,
        project_name: Optional[str] = None,
        project_type: Optional[str] = None,
        year: Optional[str] = None,
        top_k: int = 5,
        min_score: float = 0.2
    ) -> Dict[str, List[str]]:
        """查询ES知识库"""
        try:
            # 构建查询条件
            must_clauses = [
                {
                    "match": {
                        "content": {
                            "query": query_text,
                            "boost": 1.0
                        }
                    }
                }
            ]
            
            # 添加过滤条件
            filter_clauses = []
            
            if project_name:
                filter_clauses.append({
                    "bool": {
                        "should": [
                            {"term": {"project_name": project_name}},
                            {"match": {"project_name": project_name}}
                        ],
                        "minimum_should_match": 1
                    }
                })
                
            if project_type:
                filter_clauses.append({
                    "term": {
                        "action": "文书档案" if project_type == "文书档案" else "项目档案"
                    }
                })
                
            if year:
                filter_clauses.append({
                    "term": {"year": int(year)}
                })
            
            # 构建完整查询
            query = {
                "bool": {
                    "must": must_clauses,
                    "filter": filter_clauses
                }
            }
            
            # 执行搜索
            results = await self.eskg.search(
                query=query,
                size=top_k,
                source=True,  # 返回所有字段
                sort=[
                    {"_score": "desc"},  # 按相关性得分降序
                    {"page_num": "asc"}  # 按页码升序
                ]
            )
            
            # 处理搜索结果
            context_list = []
            source_documents = []
            
            for doc in results:
                if doc.get("_score", 0) < min_score:
                    continue
                # 添加到上下文列表
                context_list.append(doc.get('content', ''))
                source_documents.append(
                    get_filename_from_url(doc.get('source', '').replace('json', 'pdf'))
                )
            
            return {
                "context": context_list,
                "source_documents": source_documents
            }
            
        except Exception as e:
            print(f"Error querying knowledge: {str(e)}")
            return {
                "context": [],
                "source_documents": []
            }
            
    async def get_context(
        self,
        question: str,
        question_embedding: List[float],
        project_name: str,
        project_type: str,
        year: Optional[str] = None,
        keywords: Optional[List[str]] = None,
        llm: Optional[Any] = None
    ) -> Dict[str, Any]:
        """获取问题相关的上下文，包括ES和SQL数据"""
        try:
            # 获取ES上下文
            es_context = await self.eskg.get_context(
                question=question,
                question_embedding=question_embedding,
                project_name=project_name,
                project_type=project_type,
                year=year,
                keywords=keywords
            )
            
            # 获取SQL上下文
            sql_context = None
            if llm:
                sql_context = await self.sqlkg.get_context(
                    question,
                    project_name,
                    project_type,
                    year,
                    keywords,
                    llm
                )
            
            # 合并结果
            result = {
                "es_context": {
                    "context": es_context.get("context", []),
                    "source_documents": es_context.get("source_documents", [])
                }
            }
            
            if sql_context:
                result["sql_context"] = {
                    "project_data": sql_context.get("project_data", []),
                    "conference_data": sql_context.get("conference_data", []),
                    "formatted_context": sql_context.get("formatted_context", "")
                }
            
            return result
            
        except Exception as e:
            print(f"Error getting context: {str(e)}")
            return {
                "es_context": {
                    "context": [],
                    "source_documents": [],
                },
                "sql_context": {
                    "project_data": [],
                    "conference_data": [],
                    "formatted_context": ""
                }
            }
        
    async def close(self):
        """关闭知识库连接"""
        await self.eskg.es.close()
        if hasattr(self, 'sqlkg'):
            self.sqlkg.close()