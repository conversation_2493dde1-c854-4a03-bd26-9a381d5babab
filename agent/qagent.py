import warnings
import json
import os
import numpy as np
import httpx                                                 
from typing import List, Dict, Any, Optional, Tuple, Callable
from elasticsearch import AsyncElasticsearch
import re
import time
import asyncio
import yaml
from utils.llm import LLM
from utils.config import config
from utils.utils import load_json
from utils.sqkg import SQLKnowledge


class QueryAgent:
    def __init__(
        self,
        api_url: str = config.get("llm.api_url"),
        token: str = config.get("llm.token"),
        model: str = config.get("llm.model"),
        temperature: float = 0.6,
        max_tokens: int = 16000,
        debug: bool = True
    ):
        self.api_url = api_url
        self.headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.debug = debug
        self.verbose = debug  # 确保debug和verbose参数保持一致
        self.index_name = config.get("elasticsearch.index_name")
        
        # 初始化Elasticsearch客户端
        es_host = config.get("elasticsearch.host")
        es_port = config.get("elasticsearch.port")
        es_url = f"http://{es_host}:{es_port}"
        self.es = AsyncElasticsearch(
            hosts=[es_url],
            basic_auth=(config.get("elasticsearch.username"), config.get("elasticsearch.password")),
            verify_certs=config.get("elasticsearch.verify_certs", False),
            request_timeout=config.get("elasticsearch.timeout", 30),
            max_retries=config.get("elasticsearch.max_retries", 3)
        )
        
        self.sqlkg = SQLKnowledge(config.get('sqlite.db_path'))
        self.llm = LLM(api_url=api_url, token=token, model=model,temperature=temperature,max_tokens=max_tokens)
        self._context = ""  # 添加上下文存储
        
        if self.debug:
            print(f"QueryAgent初始化完成，使用模型: {model}")
            print(f"Elasticsearch连接: {config.get('elasticsearch.host')}, 索引: {self.index_name}")

    async def chat_with_context(
        self, 
        messages: List[Dict[str, str]], 
        project_name: str,
        project_type: str,
        year: Optional[int] = None, 
        keywords: Optional[List[str]] = None,
        question_embedding: Optional[List[float]] = None,
        full_analysis: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """      
        Args:
            messages: 消息历史
            project_name: 项目名称
            project_type: 项目类型
            year: 年份
            keywords: 关键词列表
            question_embedding: 问题的向量表示（可选，如果未提供会自动生成）
            full_analysis: 完整的实体分析结果（可选，如果提供则直接使用）
            
        Returns:
            包含回答内容和来源文档的字典
        """
        try:
            # 获取最后一条用户消息
            if not messages or len(messages) == 0:
                return {
                    "content": "未提供问题",
                    "source_documents": []
                }
                
            question = messages[-1]["content"]
            
            # 如果未提供问题向量表示，则使用LLM生成
            if question_embedding is None:
                question_embedding = await self.llm.get_embedding(question)
            
            # 使用提供的分析结果或根据需要生成新的分析
            if self.debug:
                print(f"\n处理问题: '{question}'")
                print(f"项目名称: '{project_name}'")
                print(f"项目类型: '{project_type}'")
                
            # 优先使用传入的完整分析结果
            if full_analysis is not None:
                entity_analysis = full_analysis
                if self.debug:
                    print("使用传入的完整分析结果")
            # 否则根据传入的基本信息构建分析对象或使用LLM分析
            elif project_name:
                # 使用提供的项目信息构建简单的分析对象
                entity_analysis = {
                    "project_name": project_name,
                    "doc_type": project_type,
                    "year": year,
                    "is_amount_query": False,
                    "is_personnel_query": False,
                    "query_intent": "information_seeking",
                    "keywords": keywords or []
                }
                if self.debug:
                    print("使用提供的项目信息构建分析对象")
            else:
                # 使用LLM进行实体分析
                entity_analysis = await self.analyze_query(question)
                if self.debug:
                    print("使用LLM进行实体分析")
            
            # 调用现有的问答方法
            result = await self._process_question(question, entity_analysis, question_embedding)
            
            # 将结果转换为兼容的格式
            return {
                "content": result["answer"],
                "source_documents": result.get("metadata", {}).get("source_documents", []),
                "role": "assistant"
            }
            
        except Exception as e:
            print(f"Error in QueryAgent chat_with_context: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "content": f"处理出错: {str(e)}",
                "source_documents": [],
                "role": "assistant"
            }

    async def analyze_query(self, question: str, llm_instance = None) -> Dict[str, Any]:
        """
        Args:
            question: 需要分析的问题文本
            llm_instance: 可选的LLM实例，如果为None则使用self.llm
            
        Returns:
            dict: 包含提取信息的字典，包括project_name、doc_type、year等字段
        """
        # 确定要使用的LLM实例
        llm = llm_instance if llm_instance is not None else self.llm
        
        # 增强版分析提示
        analysis_prompt = f"""
        作为专业的自然语言处理专家，请仔细分析以下问题并精确提取核心信息，特别是项目名称和文档类型。
        
        问题: "{question}"
        
        【提取规则】
        1. 项目名称提取:
           - 如果文本中包含"关于"、"通知"、"通报"、"报告"等词且位于句首，说明这是一个文书标题，直接返回完整的文书标题，类型为"文书档案"
           - 优先查找包含"系统"、"平台"、"工程"、"研究"等词的最长短语
           - 如果短语后面跟着"的"，则将"的"之前的部分作为项目名称
           - 忽略问句中的疑问词（如"是什么"、"如何"等）
           - 请不要包含"这个项目"、"投资额"等非项目名称的部分
        
        2. 文档类型判断:
           - 如果项目名称以"关于"、"通知"、"通报"开头，通常为"文书档案"
           - 其他情况下，项目名称通常属于"项目档案"类型
           - 如果问题中明确提到文档类型（如"可行性研究报告"、"项目立项文件"等），则使用该类型
        
        请提取以下全部信息：
        1. 项目名称: 精确识别问题中提到的项目名称(如"电网故障综合分析平台的研究与应用")
        2. 文档类型: 识别问题是否涉及特定类型的文档，如"文书档案"或"项目档案"
        3. 时间信息: 提取相关年份或时间段
        4. 金额相关: 问题是否询问金额、投资、预算等信息
        5. 人员相关: 问题是否询问项目负责人、团队成员等人员信息
        6. 查询意图: 确定问题的主要意图(如"查询投资额"、"了解技术方案"等)
        7. 关键词: 提取问题中的关键词
        
        以JSON格式返回，确保项目名称和文档类型的提取非常精确：
        {{
          "project_name": "提取的项目名称，如果无法识别则返回空字符串",
          "doc_type": "提取的文档类型，如果没有则空字符串",
          "year": "提取的年份，如果没有则null",
          "is_amount_query": true/false,
          "is_personnel_query": true/false,
          "query_intent": "主要查询意图",
          "keywords": ["关键词1", "关键词2"]
        }}
        
        示例1:
        问题: "电网故障综合分析平台的研究与应用这个项目的投资额多少？"
        正确提取: {{
          "project_name": "电网故障综合分析平台的研究与应用",
          "doc_type": "项目档案",
          "year": null,
          "is_amount_query": true,
          "is_personnel_query": false,
          "query_intent": "查询投资额",
          "keywords": ["电网故障综合分析平台", "投资额"]
        }}
        
        示例2:
        问题: "关于加强公司品牌文化展示讲解工作的通知"
        正确提取: {{
          "project_name": "关于加强公司品牌文化展示讲解工作的通知",
          "doc_type": "文书档案",
          "year": null,
          "is_amount_query": false,
          "is_personnel_query": false,
          "query_intent": "通知",
          "keywords": ["关于加强公司品牌文化展示讲解工作的通知", "通知"]
        }}
        
        示例3:
        问题: "气象综合分析平台的研究与应用投资额度多少？"
        正确提取: {{
          "project_name": "气象综合分析平台的研究与应用",
          "doc_type": "项目档案",
          "year": null, 
          "is_amount_query": true,
          "is_personnel_query": false,
          "query_intent": "查询投资额",
          "keywords": ["气象综合分析平台", "投资额度"]
        }}
        
        请分析用户实际输入的文本，提取真实出现的项目名称和信息，不要使用示例中的名称。
        请仅返回JSON格式的结果，不要包含任何其他文字。
        """
        
        try:
            # 调用LLM获取分析结果
            result = await llm.chat([
                {
                    "role": "system", 
                    "content": "你是一个精确的JSON输出助手。你的任务是严格按照规则提取信息并只返回JSON格式数据。对于项目名称，要准确提取完整的项目名称，不要遗漏或截断。"
                },
                {
                    "role": "user", 
                    "content": analysis_prompt
                }
            ])
            
            if self.verbose:
                print("\n===== LLM实体提取结果 =====")
                print(result)
            
            # 清理和规范化输出
            result = result.strip()
            result = result.replace("<|im_start|>", "").replace("<|im_end|>", "").strip()
            
            # 使用增强的JSON解析
            analysis = load_json(result)
            
            if self.verbose:
                print("===== JSON解析结果 =====")
                print(json.dumps(analysis, ensure_ascii=False, indent=2))
            
            # 清理项目名称
            if analysis.get("project_name"):
                # 移除非项目名称的部分
                for phrase in ["这个项目", "项目的投资额", "的投资额", "的预算", "的经费"]:
                    if phrase in analysis["project_name"]:
                        analysis["project_name"] = analysis["project_name"].replace(phrase, "").strip()
                
                # 处理末尾的"项目"
                if analysis["project_name"].endswith("项目"):
                    analysis["project_name"] = analysis["project_name"][:-2].strip()
                
                if self.verbose:
                    print(f"\n【重要】最终提取的项目名称: '{analysis['project_name']}'")
            elif self.verbose:
                print("\n【警告】未能提取到项目名称!")
                
            # 确保doc_type有值
            if not analysis.get("doc_type"):
                # 基于项目名称判断文档类型
                project_name = analysis.get("project_name", "")
                if project_name and any(project_name.startswith(word) for word in ["关于", "通知", "通报", "报告"]):
                    analysis["doc_type"] = "文书档案"
                else:
                    analysis["doc_type"] = "项目档案"
            
            # 确保关键词列表
            if not analysis.get("keywords") or not isinstance(analysis.get("keywords"), list):
                analysis["keywords"] = question.split()
                
            return analysis
            
        except Exception as e:
            if self.verbose:
                print(f"使用增强JSON解析时出错: {str(e)}")
            
            # 提供更明确的错误信息和回退策略
            try:
                # 尝试更基础的方法：直接提取大括号内的内容
                start = result.find('{')
                end = result.rfind('}')
                if start != -1 and end != -1 and start < end:
                    json_str = result[start:end+1]
                    if self.verbose:
                        print(f"尝试提取JSON字符串: {json_str[:100]}...")
                    
                    # 尝试手动解析关键信息
                    project_match = re.search(r'"project_name"\s*:\s*"([^"]+)"', json_str)
                    doc_type_match = re.search(r'"doc_type"\s*:\s*"([^"]*)"', json_str)
                    is_amount_match = re.search(r'"is_amount_query"\s*:\s*(true|false)', json_str)
                    
                    fallback = {}
                    if project_match:
                        fallback["project_name"] = project_match.group(1)
                        if self.verbose:
                            print(f"手动提取到项目名称: '{fallback['project_name']}'")
                    if doc_type_match:
                        fallback["doc_type"] = doc_type_match.group(1)
                    if is_amount_match:
                        fallback["is_amount_query"] = is_amount_match.group(1).lower() == "true"
                    
                    # 填充缺失的字段
                    if "project_name" not in fallback or not fallback["project_name"]:
                        # 如果没有提取到名称，尝试使用原始文本的一部分
                        words = question.split("的")[0]  # 取第一个"的"之前的内容
                        fallback["project_name"] = words
                        
                    if "doc_type" not in fallback or not fallback["doc_type"]:
                        # 基于项目名称判断文档类型
                        if any(fallback.get("project_name", "").startswith(word) for word in ["关于", "通知", "通报", "报告"]):
                            fallback["doc_type"] = "文书档案"
                        else:
                            fallback["doc_type"] = "项目档案"
                    
                    # 添加其他必要字段的默认值
                    fallback.setdefault("year", None)
                    fallback.setdefault("is_amount_query", False)
                    fallback.setdefault("is_personnel_query", False)
                    fallback.setdefault("query_intent", "information_seeking")
                    fallback.setdefault("keywords", question.split())
                    
                    if fallback:
                        if self.verbose:
                            print("使用基础方法提取的部分字段")
                        return fallback
            except Exception as sub_error:
                if self.verbose:
                    print(f"基础提取方法也失败: {str(sub_error)}")
            
            # 最后的回退方案
            if self.verbose:
                print("所有解析方法均失败，使用默认分析")
                
            # 尝试从问题中提取一些基本信息
            words = question.split("的")[0] if "的" in question else question
            
            return {
                "project_name": words,
                "doc_type": "项目档案" if not any(question.startswith(word) for word in ["关于", "通知", "通报", "报告"]) else "文书档案",
                "year": None,
                "is_amount_query": "投资" in question or "金额" in question or "费用" in question,
                "is_personnel_query": "负责人" in question or "人员" in question or "团队" in question,
                "query_intent": "information_seeking",
                "keywords": question.split()
            }
                

    async def _execute_query(self, query: Dict[str, Any]) -> Dict[str, Any]:
        """执行ES查询并返回结果"""
        try:
            # 增强执行查询的输出
            print("\n===== 执行的ES查询 =====")
            print(f"查询类型: {type(query)}")
            
            # 输出查询的核心部分用于调试
            if self.verbose:
                should_clauses = query.get('query', {}).get('bool', {}).get('should', [])
                filter_clauses = query.get('query', {}).get('bool', {}).get('filter', [])
                
                print(f"查询包含 {len(should_clauses)} 个should子句和 {len(filter_clauses)} 个filter子句")
                
                # 检查是否有项目名称匹配
                has_project_term = False
                has_project_match = False
                has_vector_search = False
                
                for i, clause in enumerate(should_clauses):
                    if 'term' in clause and 'project_name.keyword' in clause.get('term', {}):
                        has_project_term = True
                        print(f"子句{i+1}: 包含project_name.keyword精确匹配")
                        if self.verbose:
                            term_value = clause['term']['project_name.keyword'].get('value', '')
                            term_boost = clause['term']['project_name.keyword'].get('boost', 1.0)
                            print(f"  - 匹配值: '{term_value}', 权重: {term_boost}")
                    
                    elif 'match' in clause and 'project_name' in clause.get('match', {}):
                        has_project_match = True
                        print(f"子句{i+1}: 包含project_name普通匹配")
                        
                    elif 'script_score' in clause:
                        has_vector_search = True
                        print(f"子句{i+1}: 包含向量相似度搜索")
                        
                if not has_project_term and not has_project_match:
                    print("【注意】查询中没有包含项目名称精确匹配或普通匹配条件")
                
                if not has_vector_search:
                    print("【注意】查询中没有包含向量相似度搜索条件")
            
            print("===== 查询结束 =====\n")
            
            # 执行查询
            results = await self.es.search(index=self.index_name, body=query)
            
            # 添加查询结果基本信息
            if self.verbose:
                hits_count = results['hits']['total']['value'] if isinstance(results['hits']['total'], dict) else results['hits']['total']
                print(f"查询返回 {hits_count} 条结果")
                
                if hits_count > 0:
                    print("前3条结果得分:")
                    for i, hit in enumerate(results['hits']['hits'][:3]):
                        score = hit.get('_score', 'N/A')
                        source = hit.get('_source', {})
                        project_name = source.get('project_name', 'N/A')
                        print(f"  {i+1}. 得分: {score}, 项目名称: '{project_name}'")
            
            return dict(results)
        except Exception as e:
            error_message = str(e)
            print(f"执行查询时出错: {error_message}")
            print(f"错误类型: {type(e).__name__}")
            
            # 尝试从错误信息中提取更多详情
            if hasattr(e, 'info'):
                if isinstance(e.info, dict) and 'error' in e.info:
                    error_details = e.info['error']
                    print("\n详细错误信息:")
                    if isinstance(error_details, dict):
                        if 'root_cause' in error_details:
                            for i, cause in enumerate(error_details['root_cause']):
                                print(f"根本原因 {i+1}: {json.dumps(cause, indent=2, ensure_ascii=False)}")
                        if 'reason' in error_details:
                            print(f"错误原因: {error_details['reason']}")
                        if 'type' in error_details:
                            print(f"错误类型: {error_details['type']}")
                    else:
                        print(error_details)
                        
            return None

    async def _generate_response(self, question: str, entity_analysis: Dict[str, Any], results: Dict[str, Any]) -> str:
        all_hits = []
        if results and 'hits' in results and 'hits' in results['hits']:
            all_hits.extend(results['hits']['hits'])
        
        # 从SQL数据库获取相关信息
        sql_context = None
        try:
            # 从ES结果中提取项目名称和类型（如果有）
            project_name = entity_analysis.get("project_name", "")
            project_type = entity_analysis.get("doc_type", "")
            year = entity_analysis.get("year", None)
            keywords = entity_analysis.get("keywords", [])
            
            # 尝试从搜索结果中提取项目信息
            if all_hits:
                for hit in all_hits[:3]:  # 只检查前三个结果
                    source = hit.get('_source', {})
                    if source.get('project_name'):
                        if project_name == "":
                            project_name = source.get('project_name')
                        break
                    
                # 尝试从metadata获取文档类型
                for hit in all_hits[:3]:
                    source = hit.get('_source', {})
                    if source.get('metadata', {}).get('doc_type'):
                        if len(keywords) == 0:
                            keywords.extend(source.get('metadata', {}).get('doc_type'))
                        break
                    
                # 提取年份
                for hit in all_hits[:3]:
                    source = hit.get('_source', {})
                    if source.get('year'):
                        if year is None:
                            year = source.get('year')
                        break
            
            # 调用SQLKnowledge获取数据库信息
            sql_context = await self.sqlkg.get_context(
                question=question,
                project_name=project_name,
                project_type=project_type,
                year=year,
                keywords=keywords,
                llm=self.llm
            )
            
            if self.verbose:
                print("\n===== SQL数据库信息获取成功 =====")
                print(f"相关项目数据：{len(sql_context.get('project_data', []))}条")
                print(f"相关会议/文书数据：{len(sql_context.get('conference_data', []))}条")
        except Exception as e:
            print(f"获取SQL数据库信息时出错: {str(e)}")
            sql_context = None
        
        if not all_hits and not (sql_context and (sql_context.get('project_data') or sql_context.get('conference_data'))):
            return f"我找不到与问题「{question}」相关的信息。请尝试使用不同的表述或检查索引中是否存在相关数据。"
        
        template = """你是一个专注于项目文档和文书档案分析和总结的专家助手。基于检索到的文档，请对用户问题提供全面、专业的回答。

问题: "{question}"

以下是从文档库中检索到的相关文档片段:
{documents}

{sql_information}

请基于这些信息，生成一个全面、精确的专业回答，要求:
1. 直接回答用户问题，不要说"根据检索结果"等引导语
2. 整合多个文档片段提供的信息，保持一致性和连贯性
3. 如有必要，对信息进行分类整理，帮助用户理解复杂概念
4. 如有多个不同观点或数据点，请清晰地分别呈现
5. 如果信息不足以完整回答问题，明确指出哪些信息缺失
6. 保持严谨性，不要编造或推测没有在文档中明确提及的信息
7. 使用领域中的专业术语，但确保解释清晰
8. 如果回答涉及数字或金额，务必精确引用

回答:
"""

        docs_text = ""
        field_priority = ["content", "project_name", "metadata", "doc_id", "year", "page_num"]
        
        for i, hit in enumerate(all_hits[:3]):
            score = hit.get('_score', 'N/A')
            source = hit.get('_source', {})
            
            docs_text += f"文档 {i+1} [得分: {score}]:\n"
            
            # 提取文档源路径、页码和内容
            doc_source = source.get('source', '未知来源')
            # 从路径中提取文件名
            file_name = doc_source.split('/')[-1] if '/' in doc_source else (doc_source.split('\\')[-1] if '\\' in doc_source else doc_source)
            page_num = source.get('page_num', '未标注页码')
            content = source.get('content', '')
            
            # 构建简洁的文档展示
            docs_text += f"- 文件名: {file_name}\n"
            docs_text += f"- 页码: {page_num}\n"
            
            # 处理内容
            if content:
                if len(content) > 500:
                    docs_text += f"- 内容: {content[:500]}... (文本已截断)\n"
                else:
                    docs_text += f"- 内容: {content}\n"
            
            # 添加项目名称(如果有)
            if 'project_name' in source and source['project_name']:
                docs_text += f"- 项目名称: {source['project_name']}\n"
                
            # 添加年份(如果有)
            if 'year' in source and source['year']:
                docs_text += f"- 年份: {source['year']}\n"
                
            docs_text += "\n"
        
        # 处理SQL数据库信息
        sql_information_text = ""
        if sql_context:
            sql_parts = []
            
            # 添加项目信息
            if sql_context.get("project_data"):
                projects_info = "\n".join([
                    f"- 项目名称：{proj.get('name','N/A')}\n"
                    f"  开始日期：{proj.get('start_date', 'N/A')}\n"
                    f"  结束日期：{proj.get('end_date', 'N/A')}\n"
                    f"  总投资：{proj.get('total_investment', 'N/A')}元\n"
                    f"  承担单位：{proj.get('responsible_unit', 'N/A')}\n"
                    f"  负责人：{proj.get('leader', 'N/A')}\n"
                    f"  主要研究点：{proj.get('research_points', 'N/A')}\n"
                    f"  创新点：{proj.get('innovation', 'N/A')}\n"
                    f"  主要交付成果：{proj.get('main_deliverables', 'N/A')}\n"
                    f"  专利：{proj.get('patent', 'N/A')}"
                    for proj in sql_context["project_data"]
                ])
                sql_parts.append(f"项目信息：\n{projects_info}")
            
            # 添加会议/文书信息
            if sql_context.get("conference_data"):
                conference_info = "\n".join([
                    f"- 文书名称：{conf.get('name', 'N/A')}\n"
                    f"  发起时间：{conf.get('date', 'N/A')}\n"
                    f"  文书类型：{conf.get('type', 'N/A')}\n"
                    f"  发起组织：{conf.get('organizer', 'N/A')}\n"
                    f"  参与者：{conf.get('participants', 'N/A')}\n"
                    f"  摘要：{conf.get('summary', 'N/A')}"
                    for conf in sql_context["conference_data"]
                ])
                sql_parts.append(f"文书信息：\n{conference_info}")
            
            # 使用格式化的上下文（如果有）
            if sql_context.get("formatted_context"):
                sql_parts.append(sql_context["formatted_context"])
                
            # 合并所有SQL信息
            if sql_parts:
                sql_information_text = "以下是从数据库中检索到的相关信息:\n" + "\n\n".join(sql_parts)
        
        # 打印用于生成响应的提示
        print("\n===== 用于总结的提示 =====")
        print("问题:", question)
        print("\n文档片段示例:")
        print(docs_text[:500] + "..." if len(docs_text) > 500 else docs_text)
        if sql_information_text:
            print("\nSQL数据库信息:")
            print(sql_information_text[:500] + "..." if len(sql_information_text) > 500 else sql_information_text)
        print("===== 提示结束 =====\n")
        
        # 构建完整的提示内容
        prompt_content = template.format(
            question=question, 
            documents=docs_text,
            sql_information=sql_information_text
        )
        
        try:
            # 直接使用LLM.chat方法
            response = await self.llm.chat([
                {
                    "role": "system", 
                    "content": "你是一个专注于项目文档和文书档案分析和总结的专家助手。"
                },
                {
                    "role": "user", 
                    "content": prompt_content
                }
            ])
            return response
        except Exception as e:
            # 处理异常
            print(f"生成响应时出错: {str(e)}")
            return f"处理您的问题时遇到了技术问题。错误信息: {str(e)}"

    async def _build_query(self, question, analysis, question_embedding):
        """构建优化的直接查询，优先级：1.项目名称匹配 2.语义向量匹配 3.内容关键词匹配"""
        if self.debug:
            print("\n===== 构建优化查询 =====")
            print(f"使用提取的项目名称: '{analysis.get('project_name', '')}'")
            
        # 使用扁平结构，所有子句都在同一层
        should_clauses = []
        
        # 1. 构建针对项目名称的精确查询（最高优先级）
        if analysis.get("project_name"):
            project_name = analysis.get("project_name")
            
            # 精确关键词匹配 - 最高权重
            should_clauses.append({
                "term": {
                    "project_name.keyword": {
                        "value": project_name,
                        "boost": 10.0  # 高权重确保优先匹配
                    }
                }
            })
            
            # 分词匹配 - 较高权重，添加临近度控制
            should_clauses.append({
                "match_phrase": {
                    "project_name.text": {
                        "query": project_name,
                        "boost": 8.0,
                        "slop": 2  # 允许词之间最多2个词的距离
                    }
                }
            })
            
            # 标准匹配 - 中等权重，添加模糊匹配
            should_clauses.append({
                "match": {
                    "project_name": {
                        "query": project_name,
                        "boost": 6.0,
                        "fuzziness": "1",  # 允许1个字符的错误
                        "prefix_length": 2  # 前缀必须匹配2个字符
                    }
                }
            })
            
            # 跨字段匹配 - 在多个字段中查找项目名称
            should_clauses.append({
                "multi_match": {
                    "query": project_name,
                    "fields": ["project_name^3", "content^2"],
                    "type": "best_fields",
                    "boost": 5.0,
                    "fuzziness": "1"
                }
            })
            
            # 添加更宽松的部分匹配 - 将项目名称分词后进行匹配
            parts = project_name.split()
            if len(parts) > 1:
                for part in parts:
                    if len(part) > 1:  # 避免单字匹配
                        should_clauses.append({
                            "match": {
                                "project_name": {
                                    "query": part,
                                    "boost": 3.0,
                                    "fuzziness": "1"
                                }
                            }
                        })
            
            # 在内容中匹配项目名称
            should_clauses.append({
                "match_phrase": {
                    "content": {
                        "query": project_name,
                        "boost": 3.0,
                        "slop": 3  # 允许词之间最多3个词的距离
                    }
                }
            })
            
            # 对长项目名称，添加部分匹配到内容
            if len(parts) > 2:
                for i in range(0, len(parts)-1):
                    phrase = " ".join(parts[i:i+2])
                    should_clauses.append({
                        "match_phrase": {
                            "content": {
                                "query": phrase,
                                "boost": 2.0,
                                "slop": 2
                            }
                        }
                    })
        
        # 2. 添加向量搜索（次优先级）
        should_clauses.append({
            "script_score": {
                "query": {
                    "match_all": {}  # 让向量搜索独立于其他条件
                },
                "script": {
                    "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0", 
                    "params": {"query_vector": question_embedding}
                },
                "boost": 5.0  # 提高向量搜索权重
            }
        })
        
        # 3. 添加内容匹配（最低优先级）
        should_clauses.append({
            "match": {
                "content": {
                    "query": question,
                    "boost": 1.0  # 最低权重
                }
            }
        })
        
        # 4. 添加关键词匹配
        keywords = analysis.get("keywords", [])
        for keyword in keywords:
            if keyword and len(keyword) > 1:
                should_clauses.append({
                    "multi_match": {
                        "query": keyword,
                        "fields": ["content^2", "project_name"],
                        "type": "best_fields",
                        "boost": 2.5,
                        "fuzziness": "1"
                    }
                })
        
        # 5. 不再使用filter子句，而是将doc_type作为should子句的一部分
        if analysis.get("doc_type"):
            should_clauses.append({
                "match": {
                    "metadata.doc_type": {
                        "query": analysis.get("doc_type"),
                        "boost": 1.0  # 较低权重
                    }
                }
            })
        
        if analysis.get("year") and isinstance(analysis.get("year"), (int, str)) and str(analysis.get("year")).isdigit():
            year = int(analysis.get("year"))
            should_clauses.append({
                "term": {
                    "year": {
                        "value": year,
                        "boost": 1.0
                    }
                }
            })
        
        # 6. 构建最终查询 - 完全移除filter
        query = {
            "query": {
                "bool": {
                    "should": should_clauses,
                    "minimum_should_match": 1  # 只需要匹配一个条件
                }
            },
            "_source": ["content", "project_name", "source", "page_num", "doc_id", "action", "year", "metadata"],
            "size": 15
        }
        
        return query

    async def _build_fallback_query(self, question, analysis, question_embedding):
        """当标准查询无结果时，构建一个更宽松的回退查询，保持优先级但降低匹配要求"""
        if self.debug:
            print("\n===== 构建回退查询 =====")
            print("使用更宽松的条件进行查询...")
        
        # 1. 构建项目名称匹配（优先级最高，但使用模糊匹配）
        project_matches = []
        if analysis.get("project_name"):
            project_name = analysis.get("project_name")
            
            # 使用模糊匹配替代精确匹配
            project_matches.append({
                "match": {
                    "project_name": {
                        "query": project_name,
                        "boost": 5.0,
                        "fuzziness": "AUTO"  # 添加模糊匹配
                    }
                }
            })
            
            # 拆分项目名称为独立词进行匹配
            parts = project_name.split()
            for part in parts:
                if len(part) > 1:  # 避免单字匹配
                    project_matches.append({
                        "match": {
                            "project_name": {
                                "query": part,
                                "boost": 3.0
                            }
                        }
                    })
            
            # 尝试在内容字段中匹配项目名称（提高权重）
            project_matches.append({
                "match": {
                    "content": {
                        "query": project_name,
                        "boost": 4.0,
                        "fuzziness": "AUTO"
                    }
                }
            })
        
        # 2. 向量搜索（提高权重，使其成为主要检索手段）
        vector_query = {
            "script_score": {
                "query": {
                    "match_all": {}
                },
                "script": {
                    "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                    "params": {"query_vector": question_embedding}
                },
                "boost": 6.0  # 大幅提高向量搜索权重，弥补项目名称匹配的不足
            }
        }
        
        # 3. 内容和关键词匹配（最低优先级，但更加宽松）
        content_matches = []
        
        # 原始问题的内容匹配
        content_matches.append({
            "match": {
                "content": {
                    "query": question,
                    "boost": 2.0,  # 提高权重
                    "fuzziness": "AUTO"  # 添加模糊匹配
                }
            }
        })
        
        # 关键词匹配 - 提高权重
        keywords = analysis.get("keywords", [])
        keyword_queries = []
        for keyword in keywords:
            if keyword and len(keyword) > 1:
                keyword_queries.append({
                    "match": {
                        "content": {
                            "query": keyword,
                            "boost": 2.5,  # 提高关键词权重
                            "fuzziness": "1"  # 允许1个字符的错误
                        }
                    }
                })
        
        # 构建最终查询 - 不再有嵌套bool查询，只有一个单一的should列表
        # 这样可以确保最大的召回率
        should_clauses = []
        
        # 添加项目名称匹配（如果有）
        should_clauses.extend(project_matches)
        
        # 添加向量搜索（关键的回退策略）
        should_clauses.append(vector_query)
        
        # 添加内容和关键词匹配
        should_clauses.extend(content_matches)
        should_clauses.extend(keyword_queries)
        
        # 最终查询 - 不设任何minimum_should_match确保最大召回
        query = {
            "query": {
                "bool": {
                    "should": should_clauses,
                    "minimum_should_match": 1
                }
            },
            "_source": ["content", "project_name", "source", "page_num", "doc_id", "action", "year", "metadata"],
            "size": 15
        }
        
        return query

    async def _process_question(self, question, entity_analysis, question_embedding):
        """处理直接查询模式，使用标准bool查询，并在需要时回退到更宽松的查询"""
        import time
        start_time = time.time()
        
        if self.verbose:
            print("\n正在使用优化的直接查询模式...")
            
        # 构建优化的混合查询
        optimized_query = await self._build_query(question, entity_analysis, question_embedding)
        
        if self.verbose:
            print("\n使用标准bool查询执行...")
        
        # 使用类的内部方法执行查询，而不是全局函数
        result = await self._execute_query(optimized_query)
        
        # 检查结果并可能执行回退查询
        if result:
            # 获取命中数
            hits_count = result['hits']['total']['value'] if isinstance(result['hits']['total'], dict) else result['hits']['total']
            
            # 如果没有结果，尝试使用回退查询
            if hits_count == 0:
                if self.verbose:
                    print("\n标准查询未找到结果，尝试回退查询...")
                
                # 构建回退查询
                fallback_query = await self._build_fallback_query(question, entity_analysis, question_embedding)
                
                # 执行回退查询
                fallback_result = await self._execute_query(fallback_query)
                
                if fallback_result:
                    fallback_hits_count = fallback_result['hits']['total']['value'] if isinstance(fallback_result['hits']['total'], dict) else fallback_result['hits']['total']
                    
                    if self.verbose:
                        print(f"\n回退查询找到 {fallback_hits_count} 条相关信息")
                    
                    if fallback_hits_count > 0:
                        # 使用回退结果
                        result = fallback_result
                        hits_count = fallback_hits_count
                        
                        if self.verbose:
                            print("\n已切换到回退查询结果")
            
            if self.verbose:
                print(f"\n共找到 {hits_count} 条相关信息")
                
            if hits_count > 0:
                if self.verbose:
                    print("\n正在生成综合回答...")
                    
                # 使用实例方法生成综合响应，而不是调用外部函数
                response = await self._generate_response(question, entity_analysis, result)
                
                if self.verbose:
                    print("\n最终回答:")
                    print(response)
                    
                # 提取源文档以便在chat_with_context中使用
                # 使用列表推导式提取每个文档的source字段
                source_documents = [hit['_source']['source'] for hit in result.get('hits', {}).get('hits', []) 
                                   if '_source' in hit and 'source' in hit['_source']]
                    
                return {
                    "answer": response,
                    "metadata": {
                        "project_name": entity_analysis.get("project_name", ""),
                        "query_count": 2 if hits_count == 0 else 1,  # 如果使用了回退查询，则计数为2
                        "hit_count": hits_count,
                        "successful_queries": ["fallback_query" if hits_count == 0 else "direct_query"],
                        "execution_time": time.time() - start_time,
                        "source_documents": source_documents  # 添加源文档信息
                    }
                }
            else:
                # 两次查询都没有结果
                no_result_msg = "很抱歉，我在系统中未找到与您问题相关的信息。这可能是因为：\n1. 文档库中没有包含这个项目的信息\n2. 项目名称表述方式与索引中存储的不完全一致\n\n建议您尝试：\n- 使用不同的表述方式重新提问\n- 尝试减少项目名称的细节，使用更通用的表述\n- 确认项目名称是否正确"
                
                if self.verbose:
                    print("\n" + no_result_msg)
                    
                return {
                    "answer": no_result_msg,
                    "metadata": {
                        "project_name": entity_analysis.get("project_name", ""),
                        "query_count": 2,  # 使用了两次查询
                        "hit_count": 0,
                        "successful_queries": [],
                        "execution_time": time.time() - start_time,
                        "source_documents": []  # 添加空源文档信息
                    }
                }
        else:
            error_msg = "查询执行过程中出现错误，请稍后重试或联系系统管理员。"
            
            if self.verbose:
                print("\n" + error_msg)
                
            return {
                "answer": error_msg,
                "metadata": {
                    "project_name": entity_analysis.get("project_name", ""),
                    "query_count": 1,
                    "hit_count": 0,
                    "successful_queries": [],
                    "execution_time": time.time() - start_time,
                    "source_documents": []
                }
            }

