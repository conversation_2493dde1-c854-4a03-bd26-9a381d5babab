#!/bin/bash

# 生产环境部署脚本
# 用于部署到 ************* 服务器

set -e

echo "🚀 开始生产环境部署..."

# 配置变量（自适应）
NGINX_ROOT="/usr/share/nginx/html"
DOCKER_CONTAINER="hngpt-app"

# 自动检测部署目标（可通过环境变量覆盖）
PRODUCTION_HOST="${DEPLOY_HOST:-auto}"
FRONTEND_PORT="${FRONTEND_PORT:-8080}"
BACKEND_PORT="${BACKEND_PORT:-18888}"

echo "🔧 部署配置："
echo "  - 目标主机: ${PRODUCTION_HOST} (auto=自动检测)"
echo "  - 前端端口: ${FRONTEND_PORT}"
echo "  - 后端端口: ${BACKEND_PORT}"

# 1. 构建前端
echo "📦 构建前端（生产环境）..."
cd front_end
npm run build:prod

# 2. 检查构建结果
if [ ! -d "dist" ]; then
    echo "❌ 前端构建失败，dist目录不存在"
    exit 1
fi

echo "✅ 前端构建完成"

# 3. 检查构建时配置
echo "🔧 检查构建时配置..."
if [ ! -f "dist/config.js" ]; then
    echo "❌ 构建时配置文件不存在，请检查构建过程"
    exit 1
fi

echo "✅ 构建时配置已生成"

# 6. 创建部署包
echo "📦 创建部署包..."
cd ..
tar -czf front_end.tar.gz -C front_end/dist .

# 7. 构建后端
echo "🔧 准备后端文件..."
tar -czf back_end.tar.gz \
    --exclude='front_end' \
    --exclude='*.tar.gz' \
    --exclude='.git' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    --exclude='node_modules' \
    --exclude='dist' \
    .

# 8. 创建更新包
tar -czf update.tar.gz front_end.tar.gz back_end.tar.gz

echo "✅ 部署包创建完成: update.tar.gz"

# 9. 创建部署说明
cat > DEPLOYMENT_INSTRUCTIONS.md << 'EOF'
# 生产环境部署说明

## 🎯 构建时配置特性

本系统采用**构建时配置注入**，确保配置的准确性和可靠性：

- ✅ **构建时配置**: 在构建过程中注入正确的环境配置
- ✅ **环境隔离**: 不同环境使用不同的配置文件
- ✅ **配置验证**: 构建时验证配置的正确性
- ✅ **简单可靠**: 避免运行时配置错误
- ✅ **生产优化**: 针对生产环境优化的配置

## 部署步骤

### 1. 上传文件
将 `update.tar.gz` 上传到生产服务器

### 2. 解压部署包
```bash
tar -xzf update.tar.gz
```

### 3. 部署前端
```bash
# 停止nginx（如果需要）
sudo systemctl stop nginx

# 备份现有文件
sudo mv /usr/share/nginx/html/search /usr/share/nginx/html/search.backup.$(date +%Y%m%d_%H%M%S)

# 解压前端文件
sudo mkdir -p /usr/share/nginx/html/search
sudo tar -xzf front_end.tar.gz -C /usr/share/nginx/html/search

# 设置权限
sudo chown -R nginx:nginx /usr/share/nginx/html/search
sudo chmod -R 755 /usr/share/nginx/html/search

# 启动nginx
sudo systemctl start nginx
```

### 4. 部署后端
```bash
# 停止现有容器
docker stop hngpt-app || true
docker rm hngpt-app || true

# 解压后端文件
tar -xzf back_end.tar.gz -C /path/to/backend

# 重新构建并启动容器
cd /path/to/backend
docker build -t hngpt-backend .
docker run -d --name hngpt-app --network host hngpt-backend
```

### 5. 验证部署
访问 http://YOUR_SERVER_IP:8080/search 验证前端
访问 http://YOUR_SERVER_IP:18888/docs 验证后端API

**注意**: 将 `YOUR_SERVER_IP` 替换为实际的服务器IP地址

## 故障排除

### 跨域问题
如果遇到跨域问题，检查：
1. nginx配置是否正确
2. 后端CORS设置是否包含生产域名
3. iframe沙箱设置是否正确

### 存储问题
如果遇到sessionStorage访问被拒绝：
1. 检查浏览器控制台是否有存储兼容性警告
2. 确认配置文件已正确加载
3. 验证iframe沙箱权限设置

### 🔍 环境诊断工具

在浏览器控制台执行以下命令进行环境诊断：

```javascript
// 完整环境诊断
diagnose()

// 查看当前配置
console.log('当前配置:', window.getEnvironmentConfig())

// 查看环境信息
console.log('环境信息:', window.CURRENT_ENV_INFO)

// 手动检查存储
console.log('存储状态:', {
  sessionStorage: window.isSessionStorageAvailable?.(),
  localStorage: window.isLocalStorageAvailable?.(),
  iframe: window.isInIframe?.(),
  crossOrigin: window.isCrossOriginIframe?.()
})
```

### 🎯 自适应配置说明

系统会根据访问地址自动配置：

1. **本地开发** (`localhost`, `127.0.0.1`):
   - 服务A: `http://localhost:8080`
   - 服务B: `http://localhost:2222`

2. **内网开发** (`192.168.*`, `10.*`, `172.*`):
   - 自动使用当前主机IP
   - 智能推断端口配置

3. **生产环境** (其他地址):
   - 自动使用当前访问地址
   - 启用生产级安全策略

### 🔧 手动配置覆盖

如需手动指定配置，可在页面加载前设置：

```javascript
// 覆盖服务B地址
window.MANUAL_SERVICE_B_URL = 'http://your-custom-address:port'

// 覆盖允许的域名
window.MANUAL_ALLOWED_ORIGINS = [
  'http://your-domain.com',
  'http://your-domain.com:8080'
]
```
EOF

echo "📋 部署说明已创建: DEPLOYMENT_INSTRUCTIONS.md"

# 10. 显示部署信息
echo ""
echo "🎉 生产环境部署包准备完成！"
echo ""
echo "📁 生成的文件："
echo "  - update.tar.gz (完整部署包)"
echo "  - front_end.tar.gz (前端文件)"
echo "  - back_end.tar.gz (后端文件)"
echo "  - DEPLOYMENT_INSTRUCTIONS.md (部署说明)"
echo ""
echo "🔧 自适应配置特性："
echo "  - 🎯 自动环境检测: 根据访问地址自动配置"
echo "  - 🔗 智能服务发现: 自动推断服务地址和端口"
echo "  - 🌐 动态域名支持: 无需硬编码IP地址"
echo "  - 🖼️ 跨域iframe支持: 自适应沙箱权限"
echo "  - 💾 存储降级策略: 智能选择最佳存储方案"
echo "  - 🔍 诊断工具: 浏览器控制台输入 diagnose() 查看环境信息"
echo ""
echo "📖 请查看 DEPLOYMENT_INSTRUCTIONS.md 了解详细部署步骤"
