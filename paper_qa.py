import httpx
import asyncio
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import json
from dataclasses import dataclass
import os
import tempfile
import io
import re
import hashlib
from datetime import datetime
import jieba
import sqlite3
from elasticsearch import AsyncElasticsearch
import aiofiles

# 图像处理依赖检查
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    print("Warning: numpy not available. Image processing will be limited.")
    NUMPY_AVAILABLE = False
    np = None

try:
    import fitz  # PyMuPDF
    FITZ_AVAILABLE = True
except ImportError:
    print("Warning: PyMuPDF (fitz) not available. PDF image processing will be disabled.")
    FITZ_AVAILABLE = False
    fitz = None

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    print("Warning: PIL not available. Image processing will be limited.")
    PIL_AVAILABLE = False
    Image = None

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    print("Warning: opencv-python not available. Image encoding will be disabled.")
    CV2_AVAILABLE = False
    cv2 = None
from utils.task_manager import TaskStatus, task_manager
# 导入自定义的客户端类
from utils.es_client import ESClient
from utils.minio_client import MinioClient
from utils.sqlite_client import SQLiteClient
from utils.config import config
from functools import wraps
import time
from knowledge_control import KnowledgeControl, ExtractConfig
from utils.llm import LLM

@dataclass
class Document:
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[Any] = None  # 使用 Any 替代 np.ndarray 以避免类型检查问题
    score: float = 0.0

@dataclass
class BaseResponsedata:
    """基础响应数据类"""
    data: Dict[str, Any]

def retry_with_exponential_backoff(
    max_retries: int = 3,
    base_delay: float = 1,
    max_delay: float = 10,
    exponential_base: float = 2,
    errors: tuple = (Exception,)
):
    """指数退避重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            retries = 0
            delay = base_delay
            
            while retries < max_retries:
                try:
                    return await func(*args, **kwargs)
                except errors as e:
                    retries += 1
                    if retries == max_retries:
                        print(f"Max retries ({max_retries}) reached. Last error: {str(e)}")
                        raise
                        
                    # 计算下一次重试的延迟时间
                    delay = min(delay * exponential_base, max_delay)
                    print(f"Retry {retries}/{max_retries} after {delay:.1f}s. Error: {str(e)}")
                    await asyncio.sleep(delay)
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

class PaperQA:
    def __init__(
        self,
        api_url: str = None,
        token: str = None,
        cache_dir: str = "cache",
        minio_config: Dict[str, Any] = None
    ):
        """初始化PaperQA"""
        self.api_url = api_url or config.get('llm.api_url')
        self.token = token or config.get('llm.token')
        self.cache_dir = cache_dir
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
        # 初始化MinIO客户端
        self.minio = MinioClient(**(minio_config or {}))
        
        # 初始化LLM客户端
        self.llm = LLM(
            api_url=self.api_url,
            token=self.token,
            model=config.get('llm.model'),
            temperature=config.get('llm.temperature', 0.1),
            max_tokens=config.get('llm.max_tokens', 32000)
        )
        
        # 创建缓存目录
        os.makedirs(self.cache_dir, exist_ok=True)

        # 初始化知识控制器
        # 优先使用 MySQL，如果没有配置则使用 SQLite
        mysql_config = config.get('mysql')
        if mysql_config:
            db_config = mysql_config
            print("🔧 使用 MySQL 数据库")
        else:
            db_config = config.get('sqlite.db_path')
            print("🔧 使用 SQLite 数据库")

        self.knowledge_control = KnowledgeControl(
            es_config=config.get('elasticsearch'),
            db_config=db_config
        )

    
    async def process_txt_info(
        self,
        urls: List[str],
        enable_ocr: bool = None,
        enable_seal: bool = None,
        enable_handwrite: bool = None,
        task_id: Optional[str] = None,
        task_manager = None,
        project_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """异步处理文本信息提取"""
        # 从配置文件读取图像处理默认设置
        if enable_ocr is None:
            enable_ocr = config.get('image_processing.enable_ocr', False)
        if enable_seal is None:
            enable_seal = config.get('image_processing.enable_seal', False)
        if enable_handwrite is None:
            enable_handwrite = config.get('image_processing.enable_handwrite', False)

        skip_on_error = config.get('image_processing.skip_on_error', True)

        print(f"Image processing settings: OCR={enable_ocr}, Seal={enable_seal}, Handwrite={enable_handwrite}, Skip on error={skip_on_error}")

        processed_files = []
        failed_files = []
        total_files = 0  # 总文件数
        processed_count = 0  # 已处理文件数
        
        # 首先计算总文件数
        for url in urls:
            try:
                if self.minio.is_directory(url):
                    # 列出目录下所有PDF文件
                    files = self.minio.list_files(
                        url=url,
                        recursive=True
                    )
                    total_files += len([f for f in files if f.lower().endswith('.pdf')])
                else:
                    total_files += 1
            except Exception as e:
                print(f"Error counting files in {url}: {str(e)}")
                total_files += 1  # 假设至少有一个文件
            
        # 在计算总文件数后更新任务状态
        if task_manager and task_id:
            await task_manager.update_result(
                task_id,
                status=TaskStatus.RUNNING,
                total_files=total_files,
                error=None
            )
        
        for url in urls:
            try:
                files_to_process = []
                
                # 检查是否是目录
                if self.minio.is_directory(url):
                    # 下载目录下的所有PDF文件
                    downloaded_files = self.minio.download_directory(
                        url=url,
                        file_extensions=['.pdf',".jpg",".png"] 
                    )
                    files_to_process.extend(downloaded_files)
                else:
                    # 下载单个文件
                    file_path = self.minio.download_file(
                        url=url,
                        filters=['.pdf',".jpg",".png"]
                    )
                    if file_path:
                        files_to_process.append(file_path)
                    else:
                        failed_files.append({
                            "url": url,
                            "error": "文件下载失败"
                        })
                        processed_count += 1
                        # 更新任务状态和进度
                        if task_manager and task_id:
                            await task_manager.update_result(
                                task_id,
                                failed_file={"url": url, "error": "文件下载失败"},
                                error="部分文件处理失败",
                                status=TaskStatus.RUNNING
                            )
                        continue
                
                # 处理所有文件
                for file_path in files_to_process:
                    json_path = None  # 初始化 json_path
                    try:
                        # 生成文档ID（使用稳定的路径基础策略）
                        doc_id = generate_md5(file_path, project_name)
                        
                        # 处文件容
                        pages = await self._process_file_content(
                            file_path,
                            enable_ocr,
                            enable_seal,
                            enable_handwrite
                        )

                        # 构建JSON文件的目标路径
                        if self.minio.is_directory(url):
                            # 如果是目录，保持目录结构
                            base_name = os.path.basename(file_path)
                            json_url = os.path.join(url, os.path.splitext(base_name)[0] + ".json")
                        else:
                            # 如果是个文件，直接替换扩展名
                            json_url = os.path.splitext(url)[0] + ".json"
                            
                            
                        # 生成JSON内容
                        json_content = {
                            "doc_id": doc_id,
                            "source": json_url,
                            "pages": pages,
                            "metadata": {
                                "enable_ocr": enable_ocr,
                                "enable_seal": enable_seal,
                                "enable_handwrite": enable_handwrite,
                                "processed_at": datetime.now().isoformat()
                            }
                        }

                        # 保JSON文件
                        json_path = file_path + ".json"
                        async with aiofiles.open(json_path, 'w', encoding='utf-8') as f:
                            await f.write(json.dumps(json_content, ensure_ascii=False, indent=2))

                        # 上传JSON文件
                        if self.minio.upload_file(json_url, json_path):
                            result = {
                                "url": url,
                                "json_url": json_url,
                                "doc_id": doc_id
                            }
                            processed_files.append(result)
                            processed_count += 1  # 更新处理计数
                            # 更新任务状态和进度
                            if task_manager and task_id:
                                await task_manager.update_result(
                                    task_id,
                                    processed_file=result,
                                    status=TaskStatus.RUNNING,
                                    error=None
                                )
                        else:
                            error_result = {
                                "url": url,
                                "error": "JSON上传失败"
                            }
                            failed_files.append(error_result)
                            processed_count += 1  # 更新处理计数
                            # 更新任务状态和进度
                            if task_manager and task_id:
                                await task_manager.update_result(
                                    task_id,
                                    failed_file=error_result,
                                    status=TaskStatus.RUNNING,
                                    error="JSON上传失败"
                                )

                    except Exception as e:
                        error_result = {
                            "url": url,
                            "error": str(e)
                        }
                        failed_files.append(error_result)
                        processed_count += 1  # 更新处理计数
                        # 更新任务状态和进度
                        if task_manager and task_id:
                            await task_manager.update_result(
                                task_id,
                                failed_file=error_result,
                                status=TaskStatus.RUNNING,
                                error=f"处理文件 {url} 失败: {str(e)}"
                            )
                    finally:
                        # 清理临时文件
                        try:
                            if os.path.exists(file_path):
                                os.remove(file_path)
                            if json_path and os.path.exists(json_path):
                                os.remove(json_path)
                        except Exception as e:
                            print(f"Error removing temporary files: {str(e)}")
                            
            except Exception as e:
                error_result = {
                    "url": url,
                    "error": str(e)
                }
                failed_files.append(error_result)
                processed_count += 1  # 更新处理计数
                # 更新任务状态和进度
                if task_manager and task_id:
                    await task_manager.update_result(
                        task_id,
                        failed_file=error_result,
                        status=TaskStatus.RUNNING,
                        error=f"处理文件 {url} 失败: {str(e)}"
                    )

        return {
            "task_id": task_id,
            "status": TaskStatus.COMPLETED.value,
            "processed": processed_files,
            "failed": failed_files,
            "total_files": total_files,
            "processed_count": processed_count,
            "progress": int((processed_count / total_files * 100) if total_files > 0 else 0)
        }

    async def _process_file_content(
        self,
        file_path: str,
        enable_ocr: bool,
        enable_seal: bool,
        enable_handwrite: bool
    ) -> List[Dict[str, Any]]:
        """异步处理文件内容"""
        pages = []
        
        # 根据文件类型选择处理方法
        ext = os.path.splitext(file_path)[1].lower()
        
        if ext == '.pdf':
            # 直接处理PDF文件，不使用线程池
            pages = await self._process_pdf(
                file_path,
                enable_ocr,
                enable_seal,
                enable_handwrite
            )
        elif ext == '.jpg' or ext == '.png':
            # 处理图像文件
            page = await self._process_image(
                file_path,
                enable_ocr,
                enable_seal,
                enable_handwrite
            )
            pages.append(page)
        else:
            # 处理其他类型文件
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                pages.append({
                    "page_num": 1,
                    "content": content
                })
        
        return pages

    async def _process_pdf(
        self,
        file_path: str,
        enable_ocr: bool,
        enable_seal: bool,
        enable_handwrite: bool
    ) -> List[Dict[str, Any]]:
        """处理PDF文件"""
        pages = []
        doc = fitz.open(file_path)
        
        try:
            # 限制并发数量
            semaphore = asyncio.Semaphore(4)
            
            async def process_page(page_num: int):
                async with semaphore:
                    page = doc[page_num]
                    return await self._process_pdf_page(
                        page,
                        page_num,
                        enable_ocr,
                        enable_seal,
                        enable_handwrite
                    )
            
            # 创建任务列表
            tasks = [process_page(page_num) for page_num in range(len(doc))]
            
            # 并发处理所有页面
            pages = await asyncio.gather(*tasks)
                
        finally:
            doc.close()
            
        return pages


    @retry_with_exponential_backoff(
        max_retries=3,
        base_delay=1,
        max_delay=10,
        errors=(httpx.TimeoutException, httpx.HTTPError)
    )
    async def _ocr_image(self, img_data: bytes, enable_handwrite=False) -> Dict[str, Any]:
        """调用OCR服务处理图像"""
        try:
            # 检查 OCR 服务 URL 是否配置
            if not self.api_url:
                print("Warning: OCR API URL not configured, skipping OCR processing")
                return None

            # 调用OCR API
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_url}/ocr/binary/",
                    params={
                        "wait": "true",
                        "timeout": "300",
                        "enable_seal_hw": f"{enable_handwrite}"
                    },
                    headers={
                        "Authorization": f"Bearer {self.token}",
                        "accept": "application/json",
                        "Content-Type": "application/octet-stream"
                    },
                    content=img_data,
                    timeout=300.0
                )
                
                print(f"OCR response status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"OCR result: {result}")

                    # 检查不同的响应格式
                    if isinstance(result, dict):
                        # 格式1: {"status": "completed", "result": {...}}
                        if "status" in result:
                            if result["status"] == "completed":
                                return result.get("result")
                            else:
                                print(f"OCR failed: {result.get('error', 'Unknown error')}")
                                return None

                        # 格式2: 直接返回结果对象 {"pageContent": "...", ...}
                        elif "pageContent" in result or "sealContent" in result or "handWriteContent" in result:
                            return result

                        # 格式3: 其他可能的格式
                        else:
                            print(f"Unknown OCR response format: {result}")
                            return result  # 尝试直接返回

                    # 如果不是字典格式，尝试直接返回
                    else:
                        print(f"OCR returned non-dict result: {type(result)}")
                        return result
                else:
                    print(f"OCR request failed with status code: {response.status_code}")
                    print(f"Response content: {response.text}")
                    return None
                
        except Exception as e:
            error_msg = f"Error calling OCR service: {str(e)}"
            print(error_msg)

            # 添加更详细的错误信息
            import traceback
            print(f"OCR service error traceback: {traceback.format_exc()}")

            # 如果是 KeyError，说明响应格式不符合预期
            if isinstance(e, KeyError):
                print(f"OCR service returned unexpected response format. Missing key: {str(e)}")
                print("This suggests the OCR service API format has changed or is different than expected.")

            raise  # 重新抛出异常以触发重试

    async def test_ocr_service(self) -> bool:
        """测试 OCR 服务是否可用"""
        try:
            # 创建一个简单的测试图像（1x1像素的白色图像）
            if not PIL_AVAILABLE:
                print("PIL not available, cannot create test image")
                return False

            from PIL import Image
            import io

            # 创建测试图像
            test_img = Image.new('RGB', (100, 50), color='white')
            img_buffer = io.BytesIO()
            test_img.save(img_buffer, format='JPEG')
            test_data = img_buffer.getvalue()

            print(f"Testing OCR service at: {self.api_url}/ocr/binary/")

            # 调用 OCR 服务
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_url}/ocr/binary/",
                    params={
                        "wait": "true",
                        "timeout": "30",
                        "enable_seal_hw": "false"
                    },
                    headers={
                        "Authorization": f"Bearer {self.token}",
                        "accept": "application/json",
                        "Content-Type": "application/octet-stream"
                    },
                    content=test_data,
                    timeout=30.0
                )

                print(f"OCR test response status: {response.status_code}")
                print(f"OCR test response headers: {dict(response.headers)}")

                if response.status_code == 200:
                    result = response.json()
                    print(f"OCR test result structure: {type(result)}")
                    print(f"OCR test result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                    print(f"OCR test result sample: {str(result)[:200]}...")
                    return True
                else:
                    print(f"OCR test failed with status: {response.status_code}")
                    print(f"OCR test response: {response.text[:200]}...")
                    return False

        except Exception as e:
            print(f"OCR service test failed: {str(e)}")
            import traceback
            print(f"OCR test error traceback: {traceback.format_exc()}")
            return False

    async def _process_image(
        self,
        image_path: str,
        enable_ocr: bool,
        enable_seal: bool,
        enable_handwrite: bool
    ) -> Dict[str, Any]:
        """处理图片文件
        
        Args:
            image_path: 图片文件路径
            enable_ocr: 是否启用OCR
            enable_seal: 是否检测印章
            enable_handwrite: 是否检测手写
            
        Returns:
            Dict包含处理结果
        """
        try:
            # 初始化结果
            result = {
                "page_num": 1,
                "content": "",
                "seal_info": None,
                "handwrite_info": None,
                "date_info": None
            }
            
            # 读取图片
            img_array = cv2.imread(image_path)
            if img_array is None:
                raise Exception(f"Failed to read image: {image_path}")
            
            # 确保图片是BGR格式
            if len(img_array.shape) == 2:  # 灰度图
                img_array = cv2.cvtColor(img_array, cv2.COLOR_GRAY2BGR)
            elif img_array.shape[2] == 4:  # RGBA图
                img_array = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
            
            # 转换为JPEG格式的字节数据
            success, img_data = cv2.imencode('.jpg', img_array)
            if not success:
                raise Exception("Failed to encode image to JPEG format")
            img_data = img_data.tobytes()
            
            # 调用OCR服务
            if enable_ocr or enable_seal or enable_handwrite:
                ocr_result = await self._ocr_image(img_data,enable_handwrite)
                if ocr_result:
                    result["content"] = ocr_result.get("pageContent", "")
                    if enable_seal:
                        result["seal_info"] = ocr_result.get("sealContent", [])
                    if enable_handwrite:
                        result["handwrite_info"] = ocr_result.get("handWriteContent", [])
                    result["date_info"] = ocr_result.get("dateContent", [])
                else:
                    # OCR失败，保存图片用于调试
                    debug_dir = os.path.join(self.cache_dir, "debug_images")
                    os.makedirs(debug_dir, exist_ok=True)
                    debug_image_path = os.path.join(debug_dir, f"ocr_failed_{os.path.basename(image_path)}.jpg")
                    with open(debug_image_path, "wb") as f:
                        f.write(img_data)
                    print(f"OCR failed, saved debug image to: {debug_image_path}")
                
            return result
            
        except Exception as e:
            print(f"Error processing image {image_path}: {str(e)}")
            return {
                "content": "",
                "error": str(e)
            }
            
    async def _process_pdf_page(
        self,
        page: Any,  # 使用 Any 替代 fitz.Page 以避免类型检查问题
        page_num: int,
        enable_ocr: bool,
        enable_seal: bool,
        enable_handwrite: bool
    ) -> Dict[str, Any]:
        """处理单个PDF页面"""
        try:
            # 获取页面文本
            text = page.get_text()
            
            # 初始化结果
            result = {
                "page_num": page_num + 1,
                "content": text,
                "seal_info": None,
                "handwrite_info": None,
                "date_info": None
            }
            
            # 如果启用OCR且页面没有文本，或者启用了印章/手检测
            if (enable_ocr and not text.strip()) or enable_seal or enable_handwrite:
                # 检查必要的依赖是否可用
                if not FITZ_AVAILABLE:
                    print(f"Warning: PyMuPDF not available, skipping image processing for page {page_num}")
                    return result

                if not NUMPY_AVAILABLE or not CV2_AVAILABLE:
                    print(f"Warning: numpy or opencv not available, skipping image processing for page {page_num}")
                    return result

                try:
                    # 获取页面图像
                    zoom = 300/72  # DPI=300
                    mat = fitz.Matrix(zoom, zoom)
                    pix = page.get_pixmap(matrix=mat)
                    
                    # 转换为numpy数组
                    img_array = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, -1)
                    
                    # 处理通道
                    channels = img_array.shape[2]
                    if channels == 4:
                        img_array = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
                    elif channels == 1:
                        img_array = cv2.cvtColor(img_array, cv2.COLOR_GRAY2BGR)
                    
                    # 转换为JPEG格式的字节数据
                    success, img_data = cv2.imencode('.jpg', img_array)
                    if not success:
                        raise Exception("Failed to encode image")
                    img_data = img_data.tobytes()
                    
                    # 调用OCR服务
                    ocr_result = await self._ocr_image(img_data, enable_handwrite)
                    if ocr_result:
                        # 如果页没有文本，使用OCR结果
                        if not text.strip():
                            result["content"] = ocr_result.get("pageContent", "")
                        
                        # 添加印章和手写信息
                        if enable_seal:
                            result["seal_info"] = ocr_result.get("sealContent", [])
                        if enable_handwrite:
                            result["handwrite_info"] = ocr_result.get("handWriteContent", [])
                        
                        # 添加日期信息
                        result["date_info"] = ocr_result.get("dateContent", [])
                    else:
                        # OCR失败，保存图片用于调试
                        debug_dir = os.path.join(self.cache_dir, "debug_images")
                        os.makedirs(debug_dir, exist_ok=True)
                        debug_image_path = os.path.join(debug_dir, f"ocr_failed_page_{page_num}.jpg")
                        with open(debug_image_path, "wb") as f:
                            f.write(img_data)
                        print(f"OCR failed, saved debug image to: {debug_image_path}")
                        
                except Exception as e:
                    error_msg = f"Error processing image for page {page_num}: {str(e)}"
                    print(error_msg)

                    # 详细错误信息
                    import traceback
                    print(f"Detailed error traceback: {traceback.format_exc()}")

                    # 尝试保存出错的图片（如果img_data存在）
                    try:
                        if 'img_data' in locals():
                            debug_dir = os.path.join(self.cache_dir, "debug_images")
                            os.makedirs(debug_dir, exist_ok=True)
                            debug_image_path = os.path.join(debug_dir, f"error_page_{page_num}.jpg")
                            with open(debug_image_path, "wb") as f:
                                f.write(img_data)
                            print(f"Error occurred, saved debug image to: {debug_image_path}")
                    except Exception as save_error:
                        print(f"Failed to save debug image: {str(save_error)}")

                    # 即使OCR失败，也返回原始文本
                    return result
            
            return result
            
        except Exception as e:
            print(f"Error processing page {page_num}: {str(e)}")
            return {
                "page_num": page_num + 1,
                "content": "",
                "error": str(e)
            }

    async def process_assembly(self, urls: List[str], project_name: str, action: str, task_id: str, task_manager = None):
        """处理文档集合"""
        try:
            # 初始化结果列表
            processed_files = []
            failed_files = []
            
            # 计算总文件数
            total_files = 0
            for url in urls:
                if self.minio.is_directory(url):
                    files = self.minio.list_files(url)
                    total_files += len([f for f in files if f.lower().endswith('.json')])
                else:
                    total_files += 1
            
            # 更新初始状态
            if task_manager and task_id:
                await task_manager.update_result(
                    task_id,
                    status=TaskStatus.RUNNING,
                    total_files=total_files
                )
            
            # 创建知识控制器
            await self.knowledge_control.init()
            
            # 根据action类型获取对应的配置
            if action == "文书档案":
                extract_config = config.get('conference_extract', {})
            elif action == "项目档案":
                extract_config = config.get('project_extract', {})
            else:
                print(f"Unsupported action type: {action}")
                return {
                    "task_id": task_id,
                    "project_name": project_name,
                    "action": action,
                    "progress": 100,
                    "status": TaskStatus.FAILED.value,
                    "processed": [],
                    "failed": [{"url": "N/A", "error": f"Unsupported action type: {action}"}]
                }
            
            # 定义单个URL的处理函数
            async def process_single_url(url: str):
                try:
                    json_files = []
                    if self.minio.is_directory(url):
                        files = self.minio.list_files(url)
                        json_files.extend([f for f in files if f.lower().endswith('.json')])
                    else:
                        json_url = os.path.splitext(url)[0] + ".json"
                        json_files.append(json_url)
                    
                    results = []
                    for json_url in json_files:
                        json_path = None
                        try:
                            # 下载JSON文件
                            json_path = self.minio.download_file(json_url)
                            if not json_path:
                                error_result = {"url": json_url, "error": "JSON文件不存在"}
                                if task_manager:
                                    await task_manager.update_result(task_id, failed_file=error_result)
                                results.append({"failed": error_result})
                                continue
                            
                            # 读取JSON内容
                            with open(json_path, 'r', encoding='utf-8') as f:
                                doc_data = json.load(f)
                            
                            # 检查doc_data是否有效
                            if not doc_data:
                                error_result = {"url": json_url, "error": "JSON文件内容为空"}
                                if task_manager:
                                    await task_manager.update_result(task_id, failed_file=error_result)
                                results.append({"failed": error_result})
                                continue
                            
                            # 检查必要字段
                            if not doc_data.get("doc_id"):
                                error_result = {"url": json_url, "error": "JSON文件缺少doc_id字段"}
                                if task_manager:
                                    await task_manager.update_result(task_id, failed_file=error_result)
                                results.append({"failed": error_result})
                                continue
                            
                            if not doc_data.get("pages"):
                                error_result = {"url": json_url, "error": "JSON文件缺少pages字段或pages为空"}
                                if task_manager:
                                    await task_manager.update_result(task_id, failed_file=error_result)
                                results.append({"failed": error_result})
                                continue
                            
                            # 更新文件进度：开始处理
                            if task_manager and task_id:
                                await task_manager.update_file_progress(
                                    task_id,
                                    json_url,
                                    "processing",
                                    0,
                                    "开始处理文档",
                                    stage="processing"
                                )

                            # 使用知识控制器处理文档
                            if await self.knowledge_control.process_document(
                                doc_data,
                                project_name,
                                action,
                                config=extract_config
                            ):
                                result = {"url": json_url, "doc_id": doc_data["doc_id"]}
                                if task_manager and task_id:
                                    # 更新任务结果
                                    await task_manager.update_result(
                                        task_id,
                                        processed_file=result
                                    )
                                    # 更新文件进度：处理完成
                                    await task_manager.update_file_progress(
                                        task_id,
                                        json_url,
                                        "completed",
                                        100,
                                        "文档处理完成",
                                        stage="completed"
                                    )
                                results.append({"processed": result})
                            else:
                                error_result = {"url": json_url, "error": "知识处理失败"}
                                if task_manager and task_id:
                                    # 更新任务结果
                                    await task_manager.update_result(
                                        task_id,
                                        failed_file=error_result
                                    )
                                    # 更新文件进度：处理失败
                                    await task_manager.update_file_progress(
                                        task_id,
                                        json_url,
                                        "failed",
                                        0,
                                        "文档处理失败",
                                        error="知识处理失败",
                                        stage="failed"
                                    )
                                results.append({"failed": error_result})
                                
                        except Exception as e:
                            error_result = {"url": json_url, "error": str(e)}
                            if task_manager and task_id:
                                # 更新任务结果
                                await task_manager.update_result(
                                    task_id,
                                    failed_file=error_result
                                )
                                # 更新文件进度：处理失败
                                await task_manager.update_file_progress(
                                    task_id,
                                    json_url,
                                    "failed",
                                    0,
                                    "文档处理异常",
                                    error=str(e),
                                    stage="failed"
                                )
                            results.append({"failed": error_result})
                        finally:
                            # 清理临时文件
                            if json_path and os.path.exists(json_path):
                                os.remove(json_path)
                    
                    return results
                    
                except Exception as e:
                    error_result = {"url": url, "error": str(e)}
                    if task_manager and task_id:
                        await task_manager.update_result(
                            task_id,
                            failed_file=error_result,
                            error=f"处理文件失败: {str(e)}"
                        )
                    return [{"failed": error_result}]
            
            # 限制并发数量
            semaphore = asyncio.Semaphore(4)
            
            async def process_with_semaphore(url: str):
                async with semaphore:
                    return await process_single_url(url)
            
            # 并发处理所有URL
            tasks = [process_with_semaphore(url) for url in urls]
            all_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 整理结果
            for url_results in all_results:
                if isinstance(url_results, Exception):
                    print(f"Error processing URL: {str(url_results)}")
                    continue
                    
                # url_results 是一个列表，包含每个文件的处理结果
                for result in url_results:
                    if isinstance(result, dict):
                        if "processed" in result:
                            processed_files.append(result["processed"])
                        elif "failed" in result:
                            failed_files.append(result["failed"])
            # 所有文件处理完成后，合并并存储最终结果
            extracted_info = {}
            if processed_files:
                finalize_result = await self.knowledge_control.finalize_project(project_name, action, config=extract_config)
                if isinstance(finalize_result, dict):
                    if finalize_result.get("success"):
                        extracted_info = finalize_result.get("extracted_info", {})
                        print(f"Successfully finalized project: {project_name}")
                    else:
                        print(f"Failed to finalize project: {project_name}, error: {finalize_result.get('error', 'Unknown error')}")
                else:
                    # 兼容旧的布尔返回值
                    if not finalize_result:
                        print(f"Failed to finalize project: {project_name}")

            return {
                "task_id": task_id,
                "project_name": project_name,
                "action": action,
                "progress": 100,
                "status": TaskStatus.COMPLETED.value,
                "processed": processed_files,
                "failed": failed_files,
                "extracted_info": extracted_info
            }
            
        except Exception as e:
            if task_manager and task_id:
                await task_manager.update_result(
                    task_id,
                    status="completed",
                    error=f"处理失败: {str(e)}"
                )
            print(f"Error processing assembly: {str(e)}")
            raise

    @retry_with_exponential_backoff(
        max_retries=3,  # 增加重试次数
        base_delay=2,   # 增加基础延迟
        max_delay=20,   # 增加最大延迟
        errors=(httpx.TimeoutException, httpx.HTTPError, Exception)  # 处理所有异常
    )
    async def _call_embedding(self, text: str) -> List[float]:
        """调用嵌入向量生成服务"""
        try:
            # 如果文本太长，进行截断
            max_length = 8000  # 设置一个合理的最大长度
            if len(text) > max_length:
                text = text[:max_length]
            
            request_data = {
                "model": "xbv2",
                "prompt": text
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_url}/api/embeddings",
                    headers=self.headers,
                    json=request_data,
                    timeout=300.0  # 增加超时时间
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if (
                        "data" in data 
                        and isinstance(data["data"], list) 
                        and len(data["data"]) > 0 
                        and "embedding" in data["data"][0]
                    ):
                        embedding = data["data"][0]["embedding"]
                        if len(embedding) != 1024:  # 验证维度
                            raise Exception(f"Unexpected embedding dimension: {len(embedding)}")
                        return embedding
                    else:
                        raise Exception("Invalid response format from embeddings API")
                else:
                    error_msg = f"Embeddings API call failed with status code {response.status_code}"
                    try:
                        error_data = response.json()
                        if "error" in error_data:
                            error_msg += f": {error_data['error']}"
                    except:
                        error_msg += f": {response.text}"
                    raise Exception(error_msg)
                    
        except Exception as e:
            print(f"Error in _call_embedding: {str(e)}")
            raise

    @retry_with_exponential_backoff(
        max_retries=3,
        base_delay=1,
        max_delay=10,
        errors=(httpx.TimeoutException, httpx.HTTPError)
    )
    async def _call_llm(self, prompt: str) -> str:
        """调用LLM服务
        
        Args:
            prompt: 提示词
            
        Returns:
            str: LLM的响应文本
            
        Raises:
            Exception: 当API调用失败时抛出异常
        """
        try:
            request_data = {
                "model": config.get("llm.model"),
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a helpful assistant that extracts structured information from text."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": config.get('llm.temperature'),
                "max_tokens": config.get('llm.max_tokens'),
                "stream": False
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_url}/v1/chat/completions",
                    headers=self.headers,
                    json=request_data,
                    timeout=300.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 验证响应格式
                    if (
                        "choices" in data 
                        and len(data["choices"]) > 0 
                        and "message" in data["choices"][0] 
                        and "content" in data["choices"][0]["message"]
                    ):
                        return data["choices"][0]["message"]["content"].strip()
                    else:
                        raise Exception("Invalid response format from LLM API")
                else:
                    error_msg = f"LLM API call failed with status code {response.status_code}"
                    try:
                        error_data = response.json()
                        if "error" in error_data:
                            error_msg += f": {error_data['error']}"
                    except:
                        error_msg += f": {response.text}"
                    raise Exception(error_msg)
                    
        except httpx.TimeoutException:
            raise Exception("LLM API request timed out")
        except httpx.HTTPError as e:
            raise Exception(f"HTTP error occurred: {str(e)}")
        except Exception as e:
            raise Exception(f"Error calling LLM API: {str(e)}")



def generate_md5(file_path: str, project_name: str = None) -> str:
    """
    生成稳定的文档ID（不依赖文件内容）

    Args:
        file_path: 文件路径
        project_name: 项目名称

    Returns:
        str: 32位MD5哈希值作为文档ID
    """
    try:
        # 导入文档ID生成器
        from utils.document_id_generator import DocumentIDGenerator

        # 使用简单组合策略：只统一路径分隔符，保留所有其他特征
        return DocumentIDGenerator.generate_simple_combined_id(
            file_path=file_path,
            project_name=project_name
        )

    except ImportError:
        # 如果导入失败，使用简单的路径基础策略
        print("Warning: 无法导入DocumentIDGenerator，使用简单策略")
        try:
            # 标准化文件路径
            normalized_path = file_path.strip().replace('\\', '/').lower()
            # 移除时间戳和版本号
            import re
            normalized_path = re.sub(r'_\d{10,}', '', normalized_path)
            normalized_path = re.sub(r'_v\d+', '', normalized_path)

            if project_name:
                combined_key = f"{project_name.strip()}:{normalized_path}"
            else:
                combined_key = normalized_path

            return hashlib.md5(combined_key.encode('utf-8')).hexdigest()

        except Exception as e:
            print(f"Error generating stable ID for {file_path}: {str(e)}")
            # 最后的后备方案
            fallback = f"{os.path.basename(file_path)}"
            return hashlib.md5(fallback.encode('utf-8')).hexdigest()

    except Exception as e:
        print(f"Error generating stable ID for {file_path}: {str(e)}")
        # 后备方案：使用文件路径
        try:
            normalized_path = file_path.strip().replace('\\', '/')
            return hashlib.md5(normalized_path.encode('utf-8')).hexdigest()
        except:
            fallback = f"{os.path.basename(file_path)}"
            return hashlib.md5(fallback.encode('utf-8')).hexdigest()

    async def _remove_similar_content(self, values: List[str], field: str = "general") -> List[str]:
        """一次性去除重复和相似内容"""
        if not values:
            return []

        # 对leader字段保持简单处理
        if field == "leader":
            combined_value = ";".join(values)
            combined_value = combined_value.strip()
            combined_value = re.sub(r'项目负责人[：:]\s*|负责人[：:]\s*', '', combined_value)

            if not combined_value or combined_value in ["未知", "未提及", "无", "[未知]", "项目负责人"]:
                return []

            names = set(
                name.strip()
                for name in re.split('[;；]', combined_value)
                if name.strip() and name.strip() not in ["未知", "未提及", "无", "[未知]", "项目负责人"]
            )

            return [min(names)] if names else []

        # 其他字段: 让LLM一次性处理所有内容
        prompt = f"""请分析以下内容列表,去除重复和相似内容,返回精简后的结果:

{chr(10).join(f'- {v}' for v in values)}

要求:
1. 去除完全重复的内容
2. 合并相似的内容,保留更完整的描述
3. 用中文分号(；)分隔不同内容
4. 确保信息准确完整
5. 直接返回处理后的内容,不要解释

示例返回格式:
内容1；内容2；内容3"""

        try:
            response = await self.llm.chat([
                {
                    "role": "system",
                    "content": "你是一个内容去重和合并专家。请直接返回处理后的内容,不要包含任何解释。"
                },
                {"role": "user", "content": prompt}
            ])

            # 清理并分割结果
            result = response.strip()
            if result.startswith('"') and result.endswith('"'):
                result = result[1:-1]

            return [item.strip() for item in result.split('；') if item.strip()]

        except Exception as e:
            print(f"Error removing similar content: {str(e)}")
            # 发生错误时返回去重后的原始内容
            return list(dict.fromkeys(v.strip() for v in values if v.strip()))

    async def close(self):
        """关闭所有连接"""
        try:
            # 关闭知识控制器连接
            if hasattr(self, 'knowledge_control') and self.knowledge_control:
                await self.knowledge_control.close()

            print("✅ PaperQA 连接已关闭")

        except Exception as e:
            print(f"❌ 关闭 PaperQA 连接失败: {e}")
