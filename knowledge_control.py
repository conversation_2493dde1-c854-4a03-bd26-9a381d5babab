from typing import List, Dict, Any, Optional, Union
from utils.eskg import ESKnowledge
from utils.sqkg import SQLKnowledge, ExtractConfig
import asyncio
import os
import re
import json
from utils.llm import LLM
import hashlib
from utils.extractor import Extractor
from datetime import datetime
from utils.utils import load_json, split_text
from utils.data_cleaner import clean_response
from utils.prompts import (
    MERGE_EXTRACTIONS_PROMPT,
    MERGE_FILES_PROMPT,
    SYSTEM_PROMPT,
    JSON_EXAMPLE
)
from utils.config import config

class KnowledgeControl:
    """知识控制器：统一管理ES和SQLite两个知识仓库"""
    
    # 定义文档类型和表名的映射关系
    TABLE_MAPPING = {
        "文书档案": "conference_extract",
        "项目档案": "project_extract"
    }

    # 表结构定义 - 与 ensure_extract_tables_exist() 中的定义保持一致
    TABLE_SCHEMAS = {
        "project_extract": """
        CREATE TABLE IF NOT EXISTS `project_extract` (
          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
          `project_name` varchar(500) NOT NULL COMMENT '项目名称',
          `project_key` varchar(255) NOT NULL COMMENT '项目唯一标识（基于项目名称生成）',
          `project_no` varchar(100) DEFAULT NULL COMMENT '项目编号',
          `start_date` date DEFAULT NULL COMMENT '开始日期',
          `end_date` date DEFAULT NULL COMMENT '结束日期',
          `total_investment` bigint(20) DEFAULT NULL COMMENT '总投资金额（单位：万元）',
          `responsible_unit` text COMMENT '承担单位',
          `leader` varchar(200) DEFAULT NULL COMMENT '项目负责人',
          `research_points` text COMMENT '主要研究内容',
          `innovation` text COMMENT '创新点',
          `main_deliverables` text COMMENT '主要交付成果',
          `patent` text COMMENT '专利信息',
          `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `uk_project_key` (`project_key`),
          UNIQUE KEY `uk_project_name` (`project_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目提取表-基于项目维度'
        """,
        "conference_extract": """
        CREATE TABLE IF NOT EXISTS `conference_extract` (
          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
          `conference_name` varchar(500) NOT NULL COMMENT '文书名称',
          `conference_key` varchar(255) NOT NULL COMMENT '文书唯一标识（基于文书名称生成）',
          `conference_no` varchar(100) DEFAULT NULL COMMENT '文书编号',
          `date` date DEFAULT NULL COMMENT '文书日期',
          `type` varchar(100) DEFAULT NULL COMMENT '文书类型',
          `organizer` text COMMENT '发起组织',
          `participants` text COMMENT '参与者/接收者',
          `summary` text COMMENT '文书摘要',
          `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `uk_conference_key` (`conference_key`),
          UNIQUE KEY `uk_conference_name` (`conference_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议文书提取表-基于文书维度'
        """
    }
    
    def __init__(
        self,
        es_config: Dict[str, Any],
        db_config  # 可以是字符串（SQLite路径）或字典（MySQL配置）
    ):
        self.es_knowledge = ESKnowledge(es_config)
        self.sql_knowledge = SQLKnowledge(db_config)
        self.llm = LLM()
        self.extractor = Extractor(self.llm)
        self.chunk_size = 256  # 每个文本块的大小
        self.chunk_overlap = 0  # 文本块之间的重叠大小
        self.max_concurrent_embeddings = 4  # 降低并发数到5
        self.merged_info_cache = {}  # 添加缓存字典
        
        # 初始化时加载关键字
        try:
            with open("templates/keywords.json", 'r', encoding='utf-8') as file:
                keywords_data = json.load(file)
            self.all_keywords = set()
            for option in keywords_data.get("options", []):
                for child in option.get("children", []):
                    self.all_keywords.add(child.get("value"))
        except FileNotFoundError:
            print(f"Error: The file templates/keywords.json was not found.")
        except json.JSONDecodeError:
            print(f"Error: The file templates/keywords.json is not a valid JSON.")
        except Exception as e:
            print(f"An unexpected error occurred: {e}")

    def _chunk_text(self, text: str) -> List[str]:
        """将文本分割成小块
        
        Args:
            text: 要分割的文本
            
        Returns:
            List[str]: 文本块列表
        """
        if not text.strip():
            return []
            
        chunks = []
        start = 0
        text_length = len(text)
        
        while start < text_length:
            # 计算当前块的结束位置
            end = start + self.chunk_size
            
            # 如果不是最后一块，尝试在适当的位置断开
            if end < text_length:
                # 在chunk_size范围内找最后一个句号或换行符
                for i in range(min(end + 100, text_length) - 1, start + self.chunk_size//2, -1):
                    if text[i] in ['。', '\n']:
                        end = i + 1
                        break
            else:
                end = text_length
            
            # 添加文本块
            chunks.append(text[start:end])
            
            # 更新起始位置，考虑重叠
            start = end - self.chunk_overlap
            
        return chunks
        
    async def init(self):
        """初始化知识仓库"""
        # 初始ES索引
        await self.es_knowledge.init()

        # 初始化数据库表（支持SQLite和MySQL）
        await self._init_database_tables()

    async def _init_database_tables(self):
        """初始化数据库表（支持SQLite和MySQL）"""
        try:
            # 检查是否使用MySQL - 通过db_type属性判断
            if hasattr(self.sql_knowledge, 'db_type') and self.sql_knowledge.db_type == "mysql":
                # 使用MySQL，需要创建MySQL表
                print("🔧 检测到MySQL配置，开始创建MySQL表...")
                await self._create_mysql_tables()
            else:
                # 使用SQLite，使用原有逻辑
                print("⚠️ SQLite模式暂不支持提取表创建，请使用MySQL")
        except Exception as e:
            print(f"❌ 初始化数据库表失败: {e}")
            import traceback
            traceback.print_exc()

    async def _create_mysql_tables(self):
        """创建MySQL表 - 使用 SQLAlchemy"""
        try:
            from database import SessionLocal
            from sqlalchemy import text

            async with SessionLocal() as session:
                for table_name, create_sql in self.TABLE_SCHEMAS.items():
                    await session.execute(text(create_sql))
                    print(f"✅ MySQL表创建成功: {table_name}")

                await session.commit()

        except Exception as e:
            print(f"❌ 创建MySQL表失败: {e}")
            raise


        
    async def _process_chunk(
        self,
        chunk_text: str,
        page: Dict[str, Any],
        chunk_num: int,
        doc_data: Dict[str, Any],
        project_name: str,
        action: str
    ) -> Optional[Dict[str, Any]]:
        """处理单个文本块"""
        try:
            # 生成向量
            embedding = await self.llm.get_embedding(chunk_text)
            
            # 从source中提取年份
            source = doc_data.get('source', '')
            year = None
            created_at = None
            
            # 尝试从source中提取年份
            year_pattern = r'(\d{4})'
            year_match = re.search(year_pattern, source)
            if year_match:
                year = int(year_match.group(1))  # 转换为整数
            else:
                current_year = datetime.now().year
                year = current_year
            

            
            # 构建 metadata
            metadata = doc_data.get("metadata", {})
            metadata.update({
                "doc_type": doc_data.get("doc_types", ["其他"]),
                "chunk_info": f"Page {page['page_num']} Chunk {chunk_num + 1}"
            })
            
            # 构建知识片段
            return {
                "content": chunk_text,
                "project_name": project_name,
                "source": doc_data.get("source"),
                "page_num": page["page_num"],
                "chunk_num": chunk_num,
                "doc_id": doc_data["doc_id"],
                "action": action,
                "metadata": metadata,
                "embedding": embedding,
                "year": year
            }
        except Exception as e:
            print(f"Error processing chunk {chunk_num} of page {page['page_num']}: {str(e)}")
            return None
            
    async def delete_old_document_data(
        self,
        doc_id: str,
        project_name: str,
        action: str
    ) -> bool:
        """根据doc_id删除旧的文档数据（仅删除ES分块数据，MySQL数据使用项目/文书键删除）"""
        try:
            print(f"🗑️ 开始删除旧文档数据: doc_id={doc_id}, project={project_name}, action={action}")

            # 1. 删除ES中的文档分块数据
            try:
                # 删除知识库中的相关文档分块
                await self.es_knowledge.delete_document_chunks(doc_id)
                print(f"✅ ES文档分块删除成功: doc_id={doc_id}")
            except Exception as e:
                print(f"⚠️ ES文档分块删除失败: {e}")

            # 2. 删除MySQL中的结构化数据 - 使用项目/文书键
            try:
                table_name = self.TABLE_MAPPING.get(action)
                if table_name:
                    # 根据action类型生成对应的键值
                    import hashlib
                    if action == "项目档案":
                        # 使用project_key删除项目档案记录
                        project_key = hashlib.md5(project_name.strip().encode('utf-8')).hexdigest()
                        success = self.sql_knowledge.delete_by_key(table_name, project_key)
                        if success:
                            print(f"✅ MySQL项目档案删除成功: table={table_name}, project_key={project_key}")
                        else:
                            print(f"⚠️ MySQL项目档案删除失败: table={table_name}, project_key={project_key}")
                    elif action == "文书档案":
                        # 对于文书档案，删除该项目相关的所有文书记录
                        # 注意：这里我们删除所有可能与该项目相关的文书，因为文书档案是基于文书名称的
                        # 实际上，在新的设计中，每个文书都有独立的conference_key
                        # 这里我们暂时跳过MySQL删除，因为文书档案的删除应该在具体处理文书时进行
                        print(f"ℹ️ 文书档案删除将在处理具体文书时进行: table={table_name}")
            except Exception as e:
                print(f"⚠️ MySQL数据删除异常: {e}")

            return True

        except Exception as e:
            print(f"❌ 删除旧文档数据失败: {e}")
            return False

    async def delete_document_by_name(
        self,
        conference_name: str,
        action: str
    ) -> bool:
        """根据文书名称删除特定的文书记录"""
        try:
            if action != "文书档案":
                print(f"⚠️ 此方法仅适用于文书档案，当前action: {action}")
                return False

            print(f"🗑️ 开始删除文书档案: conference_name={conference_name}")

            # 生成conference_key
            import hashlib
            conference_key = hashlib.md5(conference_name.strip().encode('utf-8')).hexdigest()

            # 删除MySQL中的文书记录
            try:
                table_name = self.TABLE_MAPPING.get(action)
                if table_name:
                    success = self.sql_knowledge.delete_by_key(table_name, conference_key)
                    if success:
                        print(f"✅ MySQL文书档案删除成功: table={table_name}, conference_key={conference_key}, conference_name={conference_name}")
                    else:
                        print(f"⚠️ MySQL文书档案删除失败: table={table_name}, conference_key={conference_key}, conference_name={conference_name}")
                    return success
            except Exception as e:
                print(f"⚠️ MySQL文书档案删除异常: {e}")
                return False

        except Exception as e:
            print(f"❌ 删除文书档案失败: {e}")
            return False

    async def delete_old_project_data(
        self,
        project_key: str,
        project_name: str,
        action: str
    ) -> bool:
        """根据project_key删除旧的项目汇总数据 - 适配新表结构"""
        try:
            print(f"🗑️ 开始删除旧项目数据: project_key={project_key}, project={project_name}, action={action}")

            # 1. 删除ES中的项目信息
            try:
                # 删除projectinfo索引中的项目信息（使用project_key作为文档ID）
                await self.es_knowledge.delete_project_info(project_key)
                print(f"✅ ES项目信息删除成功: project_key={project_key}")
            except Exception as e:
                print(f"⚠️ ES项目信息删除失败: {e}")

            # 2. 删除MySQL中的项目结构化数据
            try:
                table_name = self.TABLE_MAPPING.get(action)
                if table_name:
                    # 根据project_key删除MySQL记录（项目级删除）
                    success = self.sql_knowledge.delete_by_key(table_name, project_key)
                    if success:
                        print(f"✅ MySQL项目数据删除成功: table={table_name}, project_key={project_key}")
                    else:
                        print(f"⚠️ MySQL项目数据删除失败: table={table_name}, project_key={project_key}")
            except Exception as e:
                print(f"⚠️ MySQL项目数据删除异常: {e}")

            return True

        except Exception as e:
            print(f"❌ 删除旧项目数据失败: {e}")
            return False


    async def get_doc_types(self, doc_data: Dict[str, Any]) -> Optional[list[str]]:
            source = doc_data.get('source', '未知')
            if source == '未知':
                return ["其他"]
            doc_type_prompt = f"""请分析文档来源信息，从预定义的关键字中选择最相关的不大于3项的关键字组合来描述文档类型，并用分号分隔。
预定义关键字集合：
{', '.join(self.all_keywords)}

文档来源: {source}

要求：
1. 从预定义关键字集合中选择最匹配的关键字
2. 可以组合多项关键字来更准确地描述文档类型,但是组合数量不大于3项
3. 如果有多项类型描述，用分号分隔
4. 必须使用预定义关键字，不能自创描述
5. 选择的关键字组合要能准确反映文档的主要内容和类型
6. 不要包含任何解释，直接返回关键字组合

示例输出：
项目立项文件；可研报告、可研评审意见；课题研究论证材料"""

            doc_type_response = await self.llm.chat([
                {
                    "role": "system",
                    "content": "你是一个文档分类助手。请直接返回文档类型关键字组合，不要包含任何解释。不要使用<think>标签。"
                },
                {"role": "user", "content": doc_type_prompt}
            ])
            
            # 清理响应
            doc_type = clean_response(doc_type_response)
            
            # 移除引号
            if doc_type.startswith('"') and doc_type.endswith('"'):
                doc_type = doc_type[1:-1]
            
            # 验证返回的关键字是否都在预定义集合中
            doc_types = []
            for type_desc in doc_type.split('；'):  # 使用中文分号分割
                type_desc = type_desc.strip()
                if type_desc in self.all_keywords:
                    doc_types.append(type_desc)
            
            # 如果没有有效的文档类型，设置为其他
            if not doc_types:
                doc_types = ["其他"]
            
            return doc_types


    async def process_document(
        self,
        doc_data: Dict[str, Any],
        project_name: str,
        action: str,
        config: Dict[str, Dict[str, Any]] = None
    ) -> bool:
        """处理单个文档，提取信息并缓存"""
        try:
            # 验证action类型
            if action not in self.TABLE_MAPPING:
                raise ValueError(f"Invalid action type: {action}. Must be one of: {list(self.TABLE_MAPPING.keys())}")

            # 获取对应的表名
            table_name = self.TABLE_MAPPING[action]

            # 获取doc_id
            doc_id = doc_data.get("doc_id")
            if doc_id:
                # 删除ES中的文档分块数据
                try:
                    await self.es_knowledge.delete_document_chunks(doc_id)
                    print(f"✅ ES文档分块删除成功: doc_id={doc_id}")
                except Exception as e:
                    print(f"⚠️ ES文档分块删除失败: {e}")

                # 显示MySQL操作提示（说明将在后续的汇总阶段处理）
                if action == "项目档案":
                    import hashlib
                    project_key = hashlib.md5(project_name.strip().encode('utf-8')).hexdigest()
                    print(f"ℹ️ MySQL项目档案将在汇总阶段更新: table={table_name}, project_key={project_key}")
                elif action == "文书档案":
                    print(f"ℹ️ MySQL文书档案将在汇总阶段更新: table={table_name}")
            else:
                print(f"⚠️ 文档缺少doc_id，无法删除ES分块数据")
            

            doc_types = await self.get_doc_types(doc_data)
            doc_data["doc_types"] = doc_types
            # 1. 处理所有页面，存入ES
            all_chunks = []
            failed_chunks = []
            
            # 收集所有需要处理的文本块
            chunks_to_process = []
            for page in doc_data.get("pages", []):
                if not page or not page.get("content") or not page.get("page_num"):
                    continue
                
                # 对容进行分片
                text_chunks = self._chunk_text(page["content"])
                
                # 收集每个文块的处理任务
                for chunk_num, chunk_text in enumerate(text_chunks):
                    chunks_to_process.append({
                        "text": chunk_text,
                        "page": page,
                        "chunk_num": chunk_num
                    })
            
            # 分批并发处理文本块
            for i in range(0, len(chunks_to_process), self.max_concurrent_embeddings):
                batch = chunks_to_process[i:i + self.max_concurrent_embeddings]
                
                # 并发处理当前批次
                chunk_tasks = [
                    self._process_chunk(
                        chunk["text"],
                        chunk["page"],
                        chunk["chunk_num"],
                        doc_data,
                        project_name,
                        action
                    )
                    for chunk in batch
                ]
                
                # 等待所有任务完成
                results = await asyncio.gather(*chunk_tasks, return_exceptions=True)
                
                # 处理结果
                for j, result in enumerate(results):
                    if isinstance(result, Exception):
                        # 处理异常
                        failed_chunks.append({
                            "page_num": batch[j]["page"]["page_num"],
                            "chunk_num": batch[j]["chunk_num"],
                            "error": str(result)
                        })
                    elif result is not None:
                        all_chunks.append(result)
            
            # 如果有块都失败了，返回False
            if not all_chunks and failed_chunks:
                print(f"Failed to process all chunks: {failed_chunks}")
                return False
            
            # 批量存入ES，分批处理以减少失败风险
            batch_size = 20
            for i in range(0, len(all_chunks), batch_size):
                batch = all_chunks[i:i + batch_size]
                retry_count = 0
                max_retries = 3
                
                while retry_count < max_retries:
                    try:
                        if await self.es_knowledge.add_chunks(batch):
                            break
                        retry_count += 1
                        if retry_count < max_retries:
                            await asyncio.sleep(1 * retry_count)
                    except Exception as e:
                        print(f"Error adding batch to ES (attempt {retry_count + 1}): {str(e)}")
                        retry_count += 1
                        if retry_count == max_retries:
                            return False
                        await asyncio.sleep(1 * retry_count)
            
            # 2. 提取结构化信息，存入SQLite
            # 合并所有文本块
            combined_text = "".join(
                chunk["content"] 
                for chunk in all_chunks
            )
            
            # 提取结构化信息
            merged_info = await self._extract_and_merge_info(
                combined_text,
                project_name,
                action,
                config,
                doc_id  # 传递 doc_id
            )

            if not merged_info:
                return False
            return True
        except Exception as e:
            print(f"Error processing document: {str(e)}")
            return False
            
    async def finalize_project(
        self,
        project_name: str,
        action: str,
        config: Dict[str, Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理项目的所有文档信息，合并并存储最终结果"""
        try:
            if project_name not in self.merged_info_cache:
                print(f"No cached information found for project: {project_name}")
                return False
            
            cached_infos = self.merged_info_cache[project_name]
            if not cached_infos:
                return False
            
            # 从ES中提取信息并添加到缓存首部
            config_result = await self.es_knowledge.extract_document_info_multi(
                project_name,
                action,
                config,
                self.llm
            ) or {}
            if config_result:
                cached_infos.insert(0, config_result)
            
            # 1. 过滤掉缺失字段过多的info
            filtered_infos = []
            for info in cached_infos:
                empty_fields = sum(1 for value in info.values() if not value or value == "")
                if empty_fields < 4:  # 保留缺失字段少于4个的info
                    filtered_infos.append(info)
            
            if not filtered_infos:
                print(f"No valid information found after filtering for project: {project_name}")
                return False
            
            # 初始化最终结果
            final_info = {"name": project_name}
            
            if action == "项目档案":
                # 处理项目档案特有字段
                # 2. 处理日期字段 - 除最大最小值
                for date_field in ["start_date", "end_date"]:
                    valid_dates = []
                    for info in filtered_infos:
                        date_str = info.get(date_field)
                        if date_str and date_str != "未提及":
                            try:
                                date = datetime.strptime(date_str, "%Y-%m-%d")
                                valid_dates.append((date, date_str))
                            except ValueError:
                                continue
                
                    if valid_dates:
                        valid_dates.sort()
                        if len(valid_dates) > 3:
                            valid_dates = valid_dates[1:-1]  # 去除最大最小值
                        final_info[date_field] = valid_dates[len(valid_dates)//2][1]  # 使用中位数
                    else:
                        final_info[date_field] = "未提及"
                
                # 3. 处理投资金额
                best_investment = None
                best_investment_score = -1
                MIN_AMOUNT = 20
                TARGET_AMOUNT = 200

                for info in filtered_infos:
                    investment = info.get("total_investment")
                    if investment and investment != "未提及":
                        try:
                            number_match = re.search(r"([\d.]+)", investment)
                            if number_match:
                                amount = float(number_match.group(1))
                                
                                if "亿" in investment:
                                    amount *= 10000
                                elif "千万" in investment:
                                    amount *= 1000
                                elif "元" in investment and "万" not in investment:
                                    amount /= 10000
                                
                                if amount >= MIN_AMOUNT:
                                    distance_to_target = abs(amount - TARGET_AMOUNT)
                                    score = 1000 - distance_to_target
                                    if info.get("leader") and len(info["leader"].split("；")) == 1:
                                        score += 100
                                    score += sum(1 for v in info.values() if v and v != "未提及") * 10
                                    
                                    if score > best_investment_score:
                                        best_investment_score = score
                                        best_investment = info["total_investment"]
                                else:
                                    print(f"Investment amount {amount}万 below minimum threshold {MIN_AMOUNT}")
                        except (ValueError, AttributeError) as e:
                            print(f"Error parsing investment amount: {investment}, {str(e)}")
                            continue

                final_info["total_investment"] = best_investment or "未提及"
                
                # 4. 处理项目档案的其他字段
                project_fields = ["project_no", "responsible_unit", "leader", "research_points", 
                                "innovation", "main_deliverables", "patent"]
                
                for field in project_fields:
                    all_values = []
                    for info in filtered_infos:
                        if info.get(field) and info[field] != "未提及":
                            values = info[field].split(";；")
                            all_values.extend(values)
                    
                    if all_values:
                        final_info[field] = await self._optimize_content(all_values, field)
                    else:
                        final_info[field] = "未提及"
                    
            elif action == "文书档案":
                # 处理文书档案特有字段
                # 1. 处理日期字段
                date_values = []
                for info in filtered_infos:
                    date_str = info.get("date")
                    if date_str and date_str != "未提及":
                        try:
                            # 更新为完整的年月日格式
                            date = datetime.strptime(date_str, "%Y-%m-%d")
                            date_values.append((date, date_str))
                        except ValueError:
                            continue
                
                if date_values:
                    date_values.sort()
                    final_info["date"] = date_values[-1][1]  # 使用最新日期
                else:
                    final_info["date"] = "未提及"
                
                # 2. 处理文书档案的其他字段
                conference_fields = ["type", "organizer", "participants", "summary"]
                
                for field in conference_fields:
                    all_values = []
                    for info in filtered_infos:
                        if info.get(field) and info[field] != "未提及":
                            values = info[field].split(";；")
                            all_values.extend(values)
                    
                    if all_values:
                        final_info[field] = await self._optimize_content(all_values, field)
                    else:
                        final_info[field] = "未提及"
            
            # 生成项目唯一标识 - 基于项目名称而非文档ID
            import hashlib
            project_key = hashlib.md5(project_name.strip().encode('utf-8')).hexdigest()

            print(f"🔍 项目最终化: project={project_name}, action={action}, project_key={project_key}")

            # 注意：这里不需要再次删除项目数据，因为在extract_info中已经删除过了

            if action == "项目档案":
                # 构建项目档案的projectinfo数据
                project_no = final_info.get("project_no", "")
                project_info = {
                    "projectName": project_name,
                    "projectNo": project_no if project_no and project_no != "未提及" else "",
                    "projectYear": final_info.get("start_date", "").split("-")[0] if final_info.get("start_date") else "",
                    "projectBeginEndYear": (
                        f"{final_info.get('start_date', '')}至{final_info.get('end_date', '')}"
                        if final_info.get('start_date') and final_info.get('end_date')
                        else ""
                    ),
                    "mainCompany": final_info.get("responsible_unit", ""),
                    "auxiliaryCompany": "",
                    "money": self._process_money_in_wan(final_info.get("total_investment")),
                    "checkTime": "",
                    "skill": final_info.get("innovation", ""),
                    "content": final_info.get("research_points", ""),
                    "result": (
                        f"{final_info.get('main_deliverables', '')}"
                        + (f"；专利：{final_info.get('patent', '')}" if final_info.get('patent') and final_info.get('patent') != "未提及" else "")
                    ).strip()
                }
            else:
                # 构建文书档案的projectinfo数据
                project_info = {
                    "projectName": project_name,
                    "documentType": final_info.get("type", ""),
                    "documentDate": final_info.get("date", ""),
                    "organizer": final_info.get("organizer", ""),
                    "participants": final_info.get("participants", ""),
                    "summary": final_info.get("summary", "")
                }
            
            # 更新ES - 使用project_key作为文档ID
            if not await self.es_knowledge.update_project_info(project_info, project_key):
                print(f"Failed to update projectinfo index for project: {project_name}")

            # 准备SQL数据 - 根据action类型使用不同的表结构
            sql_data = {}

            if action == "项目档案":
                # 项目档案字段映射 - 基于项目维度，简化版本
                sql_data["project_key"] = project_key  # 项目唯一标识
                sql_data["project_name"] = final_info.get("name", "")
                sql_data["project_no"] = final_info.get("project_no", "")
                sql_data["start_date"] = final_info.get("start_date")
                sql_data["end_date"] = final_info.get("end_date")
                sql_data["total_investment"] = self._parse_investment_amount(final_info.get("total_investment"))
                sql_data["responsible_unit"] = final_info.get("responsible_unit", "")
                sql_data["leader"] = final_info.get("leader", "")
                sql_data["research_points"] = final_info.get("research_points", "")
                sql_data["innovation"] = final_info.get("innovation", "")
                sql_data["main_deliverables"] = final_info.get("main_deliverables", "")
                sql_data["patent"] = final_info.get("patent", "")
            else:
                # 文书档案字段映射 - 基于文书维度，简化版本
                conference_key = hashlib.md5(final_info.get("name", "").strip().encode('utf-8')).hexdigest()
                sql_data["conference_key"] = conference_key
                sql_data["conference_name"] = final_info.get("name", "")
                sql_data["conference_no"] = ""  # 可以后续从文档中提取
                sql_data["date"] = final_info.get("date")
                sql_data["type"] = final_info.get("type", "")
                sql_data["organizer"] = final_info.get("organizer", "")
                sql_data["participants"] = final_info.get("participants", "")
                sql_data["summary"] = final_info.get("summary", "")

            # 使用项目/文书唯一键进行upsert操作
            key_field = "project_key" if action == "项目档案" else "conference_key"
            if not await self.sql_knowledge.upsert(
                self.TABLE_MAPPING[action],
                sql_data,
                key_fields=[key_field]  # 使用项目或文书的唯一键
            ):
                return False
            
            del self.merged_info_cache[project_name]

            # 返回提取的信息和成功状态
            return {
                "success": True,
                "extracted_info": final_info,
                "project_key": project_key,
                "sql_data": sql_data
            }
            
        except Exception as e:
            print(f"Error finalizing project: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "extracted_info": {},
                "project_key": None,
                "sql_data": {}
            }

    def _parse_investment_amount(self, investment_str: str) -> int:
        """
        解析投资金额字符串，转换为万元单位的整数

        Args:
            investment_str: 投资金额字符串，如"200万元"、"1.5亿元"等

        Returns:
            int: 以万元为单位的投资金额，解析失败返回None
        """
        if not investment_str or investment_str == "未提及":
            return None

        try:
            import re
            # 提取数字部分
            number_match = re.search(r"([\d.]+)", investment_str)
            if not number_match:
                return None

            amount = float(number_match.group(1))

            # 根据单位转换为万元
            if "亿" in investment_str:
                amount *= 10000  # 亿转万
            elif "千万" in investment_str:
                amount *= 1000   # 千万转万
            elif "万" not in investment_str and "元" in investment_str:
                amount /= 10000  # 元转万
            # 如果已经是万元，不需要转换

            return int(amount) if amount > 0 else None

        except (ValueError, AttributeError) as e:
            print(f"Error parsing investment amount '{investment_str}': {e}")
            return None

    async def _optimize_content(self, values: List[str], field: str = "general") -> str:
        """优化内容:去重、合并相似内容并保持语义连贯
        
        Args:
            values: 需要优化的内容列表
            field: 字段名称,用于特殊处理特定字段
            
        Returns:
            str: 优化后的内容字符串
        """
        if not values:
            return "未提及"
        
        # 对leader和project_no字段进行特殊处理
        if field in ["leader", "project_no"]:
            # 统计有效值出现频率
            value_frequency = {}
            for value_str in values:
                # 确保value_str不是空白字符串且不包含空格
                value_str = value_str.strip() if value_str else ""
                if value_str and value_str not in ["未知", "未提及", "无", "[未知]"] and " " not in value_str:
                    # 拆分可能包含分号的字符串
                    items = re.split('[;；]', value_str)
                    for item in items:
                        item = item.strip()
                        # 确保item不包含空格
                        if item and " " not in item:
                            if field == "leader":
                                item = re.sub(r'项目负责人[：:]\s*|负责人[：:]\s*', '', item)
                            value_frequency[item] = value_frequency.get(item, 0) + 1
            
            if not value_frequency:
                return "未提及"
            
            # 返回出现频率最高的值
            sorted_values = sorted(value_frequency.items(), key=lambda x: (-x[1], x[0]))
            return sorted_values[0][0]
        
        # 对responsible_unit进行特殊处理
        elif field == "responsible_unit":
            cleaned_values = []
            for value in values:
                value = value.strip()
                if value and value not in ["未知", "未提及", "无", "[未知]"]:
                    cleaned_values.append(value)
            return "；".join(dict.fromkeys(cleaned_values)) if cleaned_values else "未提及"
        
        # 对其他字段使用LLM进行优化
        # 清理和验证文本
        cleaned_values = []
        for value in values:
            value = value.strip()
            if value and value not in ["未知", "未提及", "无", "[未知]"]:
                cleaned_values.append(value)
        
        if not cleaned_values:
            return "未提及"
            
        # 如果只有一个值，直接返回
        if len(cleaned_values) == 1:
            return cleaned_values[0]
        
        # 对于较短的内容，直接使用简单的去重
        if all(len(v) < 20 for v in cleaned_values):
            return "；".join(dict.fromkeys(cleaned_values))
        
        prompt = f"""请对以下{len(cleaned_values)}条内容进行简单合并：

{chr(10).join(f'{i+1}. {v}' for i, v in enumerate(cleaned_values))}

合并规则：
1. 完全相同的内容只保留一个
2. 内容相似时，保留描述更完整的那个
3. 不同的内容用中文分号（；）分隔
4. 不要添加新内容，只能使用原有内容
5. 不要解释，直接返回合并结果

示例输入：
1. 开发新型传感器
2. 研发新型智能传感器
3. 完成实验室建设
4. 建设实验室

示例输出：
研发新型智能传感器；完成实验室建设
"""
        
        try:
            response = await self.llm.chat([
                {
                    "role": "system",
                    "content": "你是一个文本合并助手。请严格按照规则合并文本，只输出合并结果。"
                },
                {"role": "user", "content": prompt}
            ])
            
            # 清理并验证结果
            result = response.strip()
            if result.startswith('"') and result.endswith('"'):
                result = result[1:-1]
            
            if not result:
                return "；".join(cleaned_values)
            
            # 验证LLM返回的结果是否只包含原始内容的组合
            result_parts = result.split("；")
            validated_parts = []
            
            for part in result_parts:
                part = part.strip()
                if part:
                    # 检查返回的内容是否基于原始内容
                    for original in cleaned_values:
                        if (part in original) or (original in part):
                            validated_parts.append(part)
                            break
            
            return "；".join(validated_parts) if validated_parts else "；".join(cleaned_values)
            
        except Exception as e:
            print(f"Error optimizing content: {str(e)}")
            return "；".join(cleaned_values)

    async def query(
        self,
        query: str,
        project_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """统一查询接口"""
        try:
            # 1. 从ES中检索相关知识片段
            chunks = await self.es_knowledge.search(query, project_name)
            
            # 2. 从SQLite中检索结构化信息
            sql = f"""
                SELECT * FROM projects 
                WHERE project_name = '{project_name}'
            """ if project_name else "SELECT * FROM projects"
            records = self.sql_knowledge.query(sql)
            
            # 3. 使用LLM生成回答
            context = "\n".join([
                f"知识片段 {i+1}:\n{chunk.content}\n"
                for i, chunk in enumerate(chunks[:3])  # 使用前3个最相关的片段
            ])
            
            if records:
                context += "\n结构化信息:\n" + json.dumps(records, ensure_ascii=False, indent=2)
            
            response = await self.llm.chat([
                {"role": "system", "content": "You are a helpful assistant that answers questions based on the provided information."},
                {"role": "user", "content": f"基于以下信息回答问题: {query}\n\n{context}"}
            ])
            
            return {
                "answer": response,
                "evidence": [
                    {
                        "content": chunk.content,
                        "metadata": chunk.metadata,
                        "score": chunk.score
                    }
                    for chunk in chunks[:3]
                ],
                "structured_data": records
            }
            
        except Exception as e:
            print(f"Error querying knowledge: {str(e)}")
            return {
                "answer": f"查询出错: {str(e)}",
                "evidence": [],
                "structured_data": []
            }
            
    async def extract_info(
        self,
        project_name: str,
        action: str,
        config: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """提取文档信息并存入数据库

        Args:
            project_name: 项目名称
            action: 文档类型（文书档案/项目档案）
            config: 配置信息

        Returns:
            Dict[str, Any]: 提取的结构化信息
        """
        try:
            # 生成项目唯一标识 - 基于项目名称
            import hashlib
            project_key = hashlib.md5(project_name.strip().encode('utf-8')).hexdigest()

            print(f"🔍 项目级汇编开始: project={project_name}, action={action}, project_key={project_key}")

            # 删除旧的项目级数据
            await self.delete_old_project_data(project_key, project_name, action)

            # 提取信息
            info = await self.es_knowledge.extract_document_info(
                project_name,
                action,
                config,
                self.llm
            )
            
            # 准备数据库存储数据 - 适配新表结构
            if action == "文书档案":
                conference_key = hashlib.md5(info.get("name", "").strip().encode('utf-8')).hexdigest()
                sql_data = {
                    "conference_key": conference_key,
                    "conference_name": info.get("name", ""),
                    "conference_no": "",
                    "date": info.get("date"),
                    "type": info.get("type", ""),
                    "organizer": info.get("organizer", ""),
                    "participants": info.get("participants", ""),
                    "summary": info.get("summary", "")
                }
                self.sql_knowledge.upsert(
                    "conference_extract",
                    sql_data,
                    key_fields=["conference_key"]
                )
            else:
                sql_data = {
                    "project_key": project_key,
                    "project_name": info.get("name", ""),
                    "project_no": info.get("project_no", ""),
                    "start_date": info.get("start_date"),
                    "end_date": info.get("end_date"),
                    "total_investment": self._parse_investment_amount(info.get("total_investment")),
                    "responsible_unit": info.get("responsible_unit", ""),
                    "leader": info.get("leader", ""),
                    "research_points": info.get("research_points", ""),
                    "innovation": info.get("innovation", ""),
                    "main_deliverables": info.get("main_deliverables", ""),
                    "patent": info.get("patent", "")
                }
                self.sql_knowledge.upsert(
                    "project_extract",
                    sql_data,
                    key_fields=["project_key"]
                )

            print(f"✅ 项目级数据存储成功: project={project_name}, project_key={project_key}")
            return info
            
        except Exception as e:
            print(f"Error extracting and storing info: {str(e)}")
            return {"name": project_name} 

    async def _extract_and_merge_info(
        self,
        text: str,
        project_name: str,
        action: str,
        config: Dict[str, Dict[str, Any]],
        doc_id: str = None
    ) -> Dict[str, Any]:
        """提取文档信息"""
        try:
            # 1. 将长文本分段
            chunks = split_text(text, max_tokens=5000)
            
            # 2. 对每个块分别进行提取
            chunk_results = []
            for chunk in chunks:
                # 使用专门方法提取
                method_result = await (
                    self.extractor.extract_conference(chunk, project_name, config)
                    if action == "文书档案"
                    else self.extractor.extract_project(chunk, project_name, config)
                ) or {}
              
                # 将两种提取结果都添加到chunk_results中
                if method_result:
                    # 添加 doc_id 到提取结果中
                    if doc_id:
                        method_result["doc_id"] = doc_id
                    chunk_results.append(method_result)

            # 3. 将结果添加到缓存中
            if not chunk_results:
                result = {"name": project_name}
                if doc_id:
                    result["doc_id"] = doc_id
                return result

            if project_name not in self.merged_info_cache:
                self.merged_info_cache[project_name] = []
            self.merged_info_cache[project_name].extend(chunk_results)
            
            # 4. 返回第一个结果作为示例
            return chunk_results[0]
            
        except Exception as e:
            print(f"Error extracting info: {str(e)}")
            return {"name": project_name}

    async def merge_field_values(self, field_name: str, values: List[str], llm: Any) -> str:
        """合并单个字段的多个值
        
        Args:
            field_name: 字段名称
            values: 需要合并的值列表
            llm: LLM实例
            
        Returns:
            str: 合并后的值
        """
        # 过滤掉"未提及"的值，并确保所有值都是字符串类型
        valid_values = [str(v) for v in values if v and v != "未提及"]
        if not valid_values:
            return "未提及"
        
        # 预定义的文档类型列表
        VALID_TYPES = [
            "决策会议", "印发文件", "人事任免", "表彰获奖",
            "问责处分", "函件回复", "请示批复", "通知通报"
        ]
        
        # 对于日期字段，使用规则合并
        if field_name in ["start_date", "end_date", "date"]:
            dates = [v for v in valid_values if re.match(r'\d{4}-\d{2}', v)]
            if not dates:
                return "未提及"
            
            # 统计每个日期出现的次数
            date_counts = {}
            for date in dates:
                date_counts[date] = date_counts.get(date, 0) + 1
            
            # 找出出现次数最多的日期
            max_count = max(date_counts.values())
            most_common_dates = [date for date, count in date_counts.items() if count == max_count]
            
            # 如果有多个日期出现次数相同，使用原来的逻辑
            if len(most_common_dates) > 1:
                return min(dates) if field_name in ["start_date", "date"] else max(dates)
            
            # 返回出现次数最多的日期
            return most_common_dates[0]
        
        # 对于金额字段，使用规则合并
        if field_name == "total_investment":
            amounts = [v for v in valid_values if re.search(r'\d', v)]
            if not amounts:
                return "未提及"
            
            # 保持原始顺序的金额列表
            ordered_amounts = []
            # 统计每个金额出现的次数
            amount_counts = {}
            for amount in amounts:
                if amount not in amount_counts:
                    ordered_amounts.append(amount)
                amount_counts[amount] = amount_counts.get(amount, 0) + 1
            
            # 找出最高出现次数
            max_count = max(amount_counts.values())
            # 从ordered_amounts中找出第一个出现次数等于max_count的金额
            for amount in ordered_amounts:
                if amount_counts[amount] == max_count:
                    return amount
        
        # 对于type字段，使用特殊处理
        if field_name == "type":
            # 遍历所有有效值，找到第一个匹配预定义类型的值
            for value in valid_values:
                if value in VALID_TYPES:
                    return value
            # 如果没有找到匹配的类型，返回默认类型
            return "通报"
        
        # 对于leader字段，使用出现频率最高的值，频率相同时保持原始顺序
        if field_name == "leader":
            # 保持原始顺序的leader列表
            ordered_leaders = []
            # 统计每个人名出现的次数
            leader_counts = {}
            for value in valid_values:
                # 清理value中的"项目负责人"等无关文字
                cleaned_value = value.replace("项目负责人", "").replace("负责人", "").strip()
                if cleaned_value:
                    if cleaned_value not in leader_counts:
                        ordered_leaders.append(cleaned_value)
                    leader_counts[cleaned_value] = leader_counts.get(cleaned_value, 0) + 1
            
            if not leader_counts:
                return "未提及"
            
            # 找出最高出现次数
            max_count = max(leader_counts.values())
            # 从ordered_leaders中找出第一个出现次数等于max_count的人名
            for leader in ordered_leaders:
                if leader_counts[leader] == max_count:
                    return leader
        
        # 对于需要 LLM 处理的字段，使用统一的处理逻辑
        text = "；".join(valid_values)
        
        # 定义字段特定的提示词
        FIELD_PROMPTS = {
            "responsible_unit": """
1. 识别出所提到的单位称
2. 保留所有不同的单位名称
3. 如果有多个承担单位，用分号分隔
4. 不要删除或合并任何单位名称""",
            
            "organizer": """
1. 识别出所有发起组织
2. 选择最规范、最完整的组织名称
3. 如果有多个组织，用分号分隔
4. 去除重复或相似的内容""",
            
            "participants": """
1. 识别出所有与的部门和人员
2. 选择最完整的描述方式
3. 如果有多个参与者，用分号分隔
4. 去除重复或相似的内容""",
            
            "summary": """
1. 识别出文档的主要内容点
2. 用简洁的语言描述每个要点
3. 按照逻辑顺序组织内容
4. 用分号分隔不同的内容点
5. 去除重复或相似的内容"""
        }
        
        # 构建提示词
        field_desc = {
            "responsible_unit": "项目承担单位",
            "organizer": "发起组织",
            "participants": "参与者",
            "summary": "文档内容"
        }.get(field_name, "内容")
        
        analysis_steps = FIELD_PROMPTS.get(field_name, """
1. 识别所有独立的信息点
2. 合并相似或重复的内容
3. 用简洁的语言描述每个要点
4. 按照逻辑顺序组织内容
5. 用分号分隔不同的内容
6. 确保信息的完整性和准确性""")
        
        prompt = f"""这些是关于{field_desc}的不同描述，请分析并提取最准确的信息：

{text}

分析思路：{analysis_steps}

直接返回处理后的内容，不要解释。"""
        
        try:
            response = await llm.chat([
                {
                    "role": "system",
                    "content": "你是一个信息提取和总结助手。请直接返回处理后的内容，不要包含任何解释。"
                },
                {"role": "user", "content": prompt}
            ])
            
            # 清理响应
            summary = response.strip()
            if summary.startswith('"') and summary.endswith('"'):
                summary = summary[1:-1]
            
            return summary or "未提及"
            
        except Exception as e:
            print(f"Error merging field {field_name}: {str(e)}")
            return valid_values[0] if valid_values else "未提及"

    def _process_money_in_wan(self, value: Any) -> int:
        """处理金额，保持以"万"为单位
        
        Args:
            value: 输入的金额值，可以是字符串（如"1.5亿"）或数字
            
        Returns:
            int: 以"万"为单位的整数金额
        """
        try:
            # 如果是空值或未提及
            if not value or value == "未提及":
                return 0
            
            # 如果是已经是数字
            if isinstance(value, (int, float)):
                return int(value)  # 假设输入的数字已经是以万为单位
            
            # 处理字符串格式
            value_str = str(value).strip()
            
            # 提取数字部分
            number_match = re.search(r"([\d.]+)", value_str)
            if not number_match:
                return 0
            
            amount = float(number_match.group(1))
            
            # 根据单位转换为"万"
            if "亿" in value_str:
                amount = amount * 10000  # 亿 -> 万
            elif "千万" in value_str:
                amount = amount * 1000   # 千万 -> 万
            # 如果是"万"或没有单位，保持原样
            
            return int(amount)  # 返回整数（单位：万）
        except Exception as e:
            print(f"Error processing money value: {str(e)}")
            return 0

    async def close(self):
        """关闭所有连接"""
        try:
            # 关闭ES连接
            if hasattr(self, 'es_knowledge') and self.es_knowledge:
                await self.es_knowledge.close()

            # 关闭SQL连接
            if hasattr(self, 'sql_knowledge') and self.sql_knowledge:
                if hasattr(self.sql_knowledge, 'db') and self.sql_knowledge.db:
                    if hasattr(self.sql_knowledge.db, 'close'):
                        self.sql_knowledge.db.close()

            print("✅ KnowledgeControl 连接已关闭")

        except Exception as e:
            print(f"❌ 关闭连接失败: {e}")