# For more information on configuration, see:
#   * Official English Documentation: http://nginx.org/en/docs/
#   * Official Russian Documentation: http://nginx.org/ru/docs/

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    proxy_request_buffering off;
    proxy_buffering off;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    server_tokens off;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 4096;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /etc/nginx/conf.d/*.conf;

    server {
        listen   80;

        location / {
            # 处理OPTIONS预检请求
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "*" always;
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-User-ID, X-Requested-With" always;
                add_header Access-Control-Allow-Credentials "true" always;
                add_header Access-Control-Max-Age "86400" always;
                add_header Content-Length 0;
                add_header Content-Type "text/plain charset=UTF-8";
                return 204;
            }
            root   /usr/share/nginx/html/search;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        # 安全头设置
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;

        # iframe 嵌入支持 - 允许被 service_a 嵌入
        add_header X-Frame-Options "ALLOWALL" always;

        # 第三方存储访问优化 - 关键配置
        add_header Cross-Origin-Embedder-Policy "unsafe-none" always;
        add_header Cross-Origin-Opener-Policy "unsafe-none" always;
        add_header Cross-Origin-Resource-Policy "cross-origin" always;

        # Permissions Policy - 允许存储访问和相关特性
        add_header Permissions-Policy "storage-access=*, camera=*, microphone=*, geolocation=*, fullscreen=*" always;

        # 第三方Cookie支持 - 允许跨站点存储
        add_header Set-Cookie "SameSite=None; Secure=false" always;

        # 存储分区控制 - 防止存储被分区
        add_header Origin-Agent-Cluster "?0" always;

        # CORS设置 - 支持跨域iframe通信和存储访问
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-User-ID, X-Requested-With" always;
        add_header Access-Control-Allow-Credentials "true" always;
        add_header Access-Control-Expose-Headers "Content-Length, Content-Range, X-User-ID" always;

        # CSP策略 - 宽松的iframe和存储策略
        add_header Content-Security-Policy "frame-ancestors 'self' http://*************:81 https://*************:81; frame-src 'self' http://*************:81 https://*************:81; default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';" always;


                # 后端API代理到容器
        location /dev-api {
            proxy_pass http://*************:18888;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_set_header X-Forwarded-Host $host;
            rewrite ^/dev-api/(.*)$ /$1 break;

            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        location /auth {
            proxy_pass http://*************:18888;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_set_header X-Forwarded-Host $host;
            rewrite ^/auth/(.*)$ /$1 break;

            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        location /api-s {
            proxy_pass http://*************:18888;
            proxy_read_timeout 600s;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Port $server_port;
            rewrite ^/api-s/(.*)$ /$1 break;
        }
        location /download {
            proxy_pass http://*************:18888;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 传递认证头到后端进行权限验证
            proxy_set_header Authorization $http_authorization;

            # 添加 CORS 头
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
        }
    }

    upstream server_main {
        server *************:8080;
    }
    upstream server_check {
        server *************:8889;
    }
    upstream server_ocr {
        server *************:8890;
    }
    upstream server_file {
        server *************:8891;
    }
    upstream server_thirdpartypush {
        server *************:8888;
    }
    upstream server_group {
        server *************:8099;
    }

    server {
        listen       81 default_server;
        listen       [::]:81 default_server;
        server_name  _;
        root         /usr/share/nginx/html/docarchive;
        client_max_body_size 4G;
		charset utf-8;

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;
 
        # 安全头设置
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;

        # iframe 支持 - service_a 可以嵌入其他页面
        add_header X-Frame-Options "SAMEORIGIN" always;

        # 第三方存储访问优化 - 宿主页面配置
        add_header Cross-Origin-Embedder-Policy "unsafe-none" always;
        add_header Cross-Origin-Opener-Policy "unsafe-none" always;
        add_header Cross-Origin-Resource-Policy "cross-origin" always;

        # Permissions Policy - 允许iframe中的存储访问和相关特性
        add_header Permissions-Policy "storage-access=*, camera=*, microphone=*, geolocation=*, fullscreen=*" always;

        # CORS设置 - 支持iframe通信和存储访问
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-User-ID, X-Requested-With" always;
        add_header Access-Control-Allow-Credentials "true" always;
        add_header Access-Control-Expose-Headers "Content-Length, Content-Range, X-User-ID" always;

        # CSP策略 - 允许嵌入front_end iframe并支持存储访问
        add_header Content-Security-Policy "frame-src 'self' http://************* https://*************; script-src 'self' 'unsafe-inline' 'unsafe-eval'; default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:;" always;

		location / {
            root   /usr/share/nginx/html/docarchive;
			try_files $uri $uri/ /index.html;
            index  index.html index.htm;
        }
		
		location /prod-api/ {
			proxy_set_header Host $http_host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header REMOTE-HOST $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_pass http://server_main/;
		}
		
		location /prod-api/8888/ {
			proxy_set_header Host $http_host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header REMOTE-HOST $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_pass http://server_thirdpartypush/;
		}
		
		location /prod-api/8889/ {
			proxy_set_header Host $http_host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header REMOTE-HOST $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_pass http://server_check/;
		}
		
		location /prod-api/8890/ {
			proxy_set_header Host $http_host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header REMOTE-HOST $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_pass http://server_ocr/;
		}
		
		location /prod-api/8891/ {
			proxy_set_header Host $http_host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header REMOTE-HOST $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_pass http://server_file/;
		}
		
		location /prod-api/8099/ {
			proxy_set_header Host $http_host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header REMOTE-HOST $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_pass http://server_group/;
		}

#        location / {
#	  try_files $uri $uri/ @router;
#          index index.html;
#        }
#      location @router {
#       rewrite ^.*$  /index.html last;
#      }

      location /docArchiveServer/  {
        proxy_pass http://127.0.0.1:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	proxy_set_header Upgrade $http_upgrade;
	proxy_set_header Connection "upgrade";
       }

      location /zstpServer0/  {
        proxy_pass http://127.0.0.1:40100/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       }

      location /zstpServer1/  {
        proxy_pass http://127.0.0.1:40101/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       }     

        error_page 404 /404.html;
            location = /40x.html {
        }

        error_page 500 502 503 504 /50x.html;
            location = /50x.html {
        }
    }
    
 upstream pythonScript {
    server **************:8686 weight=1;
    server **************:8686 weight=1;
    server **************:8686 weight=1;
    }
    server {
        listen   8089;
        server_name ocrCheck;
        location / {
        default_type application/json;
        proxy_pass http://pythonScript/api-script/script/unionDetect/$1?$args;
        proxy_read_timeout 1800s;
        proxy_send_timeout 1800s;
        }

    }

# Settings for a TLS enabled server.
#
#    server {
#        listen       443 ssl http2 default_server;
#        listen       [::]:443 ssl http2 default_server;
#        server_name  _;
#        root         /usr/share/nginx/html;
#
#        ssl_certificate "/etc/pki/nginx/server.crt";
#        ssl_certificate_key "/etc/pki/nginx/private/server.key";
#        ssl_session_cache shared:SSL:1m;
#        ssl_session_timeout  10m;
#        ssl_ciphers PROFILE=SYSTEM;
#        ssl_prefer_server_ciphers on;
#
#        # Load configuration files for the default server block.
#        include /etc/nginx/default.d/*.conf;
#
#        location / {
#        }
#
#        error_page 404 /404.html;
#            location = /40x.html {
#        }
#
#        error_page 500 502 503 504 /50x.html;
#            location = /50x.html {
#        }
#    }

}

