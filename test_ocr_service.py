#!/usr/bin/env python3
"""
测试 OCR 服务的脚本
用于诊断 OCR 服务的响应格式和可用性
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append('/workspace/hngpt')

from paper_qa import PaperQA
from utils.config import config

async def main():
    """测试 OCR 服务"""
    print("=== OCR 服务测试 ===")
    
    # 创建 PaperQA 实例
    qa = PaperQA(
        api_url=config.get('llm.api_url'),
        token=config.get('llm.token'),
        cache_dir="cache",
        minio_config=config.get('minio')
    )
    
    print(f"OCR API URL: {qa.api_url}")
    print(f"Token configured: {'Yes' if qa.token else 'No'}")
    
    # 测试 OCR 服务
    is_available = await qa.test_ocr_service()
    
    if is_available:
        print("✅ OCR 服务测试通过")
    else:
        print("❌ OCR 服务测试失败")
        print("\n可能的问题：")
        print("1. OCR 服务未启动或不可访问")
        print("2. API URL 配置错误")
        print("3. 认证 token 无效")
        print("4. OCR 服务 API 格式与预期不符")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(main())
