#!/bin/bash

# npm环境诊断脚本
# 用于调试docker exec中npm命令找不到的问题

CONTAINER_NAME="hngpt-dev"

echo "🔍 npm环境诊断脚本"
echo "==================="
echo "容器: $CONTAINER_NAME"
echo ""

# 检查容器状态
echo "1. 检查容器状态:"
if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "   ✅ 容器正在运行"
else
    echo "   ❌ 容器未运行"
    exit 1
fi
echo ""

# 比较环境变量
echo "2. 环境变量对比:"
echo "   容器内直接执行的PATH:"
docker exec "$CONTAINER_NAME" bash -c "echo \$PATH"

echo "   docker exec执行的PATH:"
docker exec "$CONTAINER_NAME" env | grep PATH

echo "   加载.bashrc后的PATH:"
docker exec "$CONTAINER_NAME" bash -c "source ~/.bashrc 2>/dev/null && echo \$PATH"
echo ""

# 查找npm位置
echo "3. 查找npm位置:"
echo "   which npm:"
docker exec "$CONTAINER_NAME" bash -c "which npm 2>/dev/null || echo 'npm not found in PATH'"

echo "   find npm:"
docker exec "$CONTAINER_NAME" bash -c "find /usr -name npm 2>/dev/null | head -5"

echo "   locate npm (如果可用):"
docker exec "$CONTAINER_NAME" bash -c "locate npm 2>/dev/null | head -5 || echo 'locate not available'"
echo ""

# 检查常见路径
echo "4. 检查常见npm路径:"
for path in "/usr/local/bin/npm" "/usr/bin/npm" "/opt/node/bin/npm" "/root/.nvm/versions/node/*/bin/npm"; do
    if docker exec "$CONTAINER_NAME" test -f "$path" 2>/dev/null; then
        echo "   ✅ $path 存在"
        docker exec "$CONTAINER_NAME" ls -la "$path"
    else
        echo "   ❌ $path 不存在"
    fi
done
echo ""

# 检查Node.js安装
echo "5. Node.js环境:"
echo "   Node.js路径:"
docker exec "$CONTAINER_NAME" bash -c "which node 2>/dev/null || echo 'node not found'"

echo "   Node.js版本:"
docker exec "$CONTAINER_NAME" bash -c "node --version 2>/dev/null || echo 'node command failed'"

echo "   npm版本 (如果能找到):"
NPM_PATH=$(docker exec "$CONTAINER_NAME" bash -c "which npm 2>/dev/null || find /usr -name npm 2>/dev/null | head -1")
if [ -n "$NPM_PATH" ]; then
    echo "   使用路径: $NPM_PATH"
    docker exec "$CONTAINER_NAME" bash -c "$NPM_PATH --version"
else
    echo "   ❌ 无法找到npm"
fi
echo ""

# 检查环境文件
echo "6. 环境配置文件:"
for file in "~/.bashrc" "~/.profile" "~/.bash_profile" "/etc/environment"; do
    echo "   检查 $file:"
    if docker exec "$CONTAINER_NAME" test -f "$file" 2>/dev/null; then
        echo "     ✅ 文件存在"
        docker exec "$CONTAINER_NAME" bash -c "grep -E '(PATH|NODE|NPM)' $file 2>/dev/null | head -3 || echo '     无相关配置'"
    else
        echo "     ❌ 文件不存在"
    fi
done
echo ""

# 测试不同的执行方式
echo "7. 测试不同执行方式:"

echo "   方式1: 直接执行"
docker exec "$CONTAINER_NAME" bash -c "npm --version 2>&1 || echo 'Failed'"

echo "   方式2: 加载.bashrc"
docker exec "$CONTAINER_NAME" bash -c "source ~/.bashrc 2>/dev/null && npm --version 2>&1 || echo 'Failed'"

echo "   方式3: 使用完整路径"
if [ -n "$NPM_PATH" ]; then
    docker exec "$CONTAINER_NAME" bash -c "$NPM_PATH --version 2>&1 || echo 'Failed'"
else
    echo "     无法测试，npm路径未找到"
fi

echo "   方式4: 登录shell"
docker exec -it "$CONTAINER_NAME" bash -l -c "npm --version 2>&1 || echo 'Failed'" 2>/dev/null || echo "     无法测试交互式shell"
echo ""

# 建议解决方案
echo "8. 建议解决方案:"
if [ -n "$NPM_PATH" ]; then
    echo "   ✅ 找到npm路径: $NPM_PATH"
    echo "   建议在脚本中使用完整路径:"
    echo "   docker exec $CONTAINER_NAME bash -c \"cd /workspace/hngpt/front_end && $NPM_PATH run build\""
else
    echo "   ❌ 未找到npm，可能需要:"
    echo "   1. 重新安装Node.js/npm"
    echo "   2. 检查容器镜像是否包含npm"
    echo "   3. 手动设置PATH环境变量"
fi
echo ""

echo "🔧 快速修复命令:"
echo "如果npm存在但PATH不正确，可以尝试:"
echo "docker exec $CONTAINER_NAME bash -c \"export PATH=\\\$PATH:/usr/local/bin:/usr/bin && cd /workspace/hngpt/front_end && npm run build\""
