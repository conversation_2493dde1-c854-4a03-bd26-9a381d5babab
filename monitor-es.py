#!/usr/bin/env python3
"""
Elasticsearch容器健康监控脚本
监控内存使用、容器状态等
"""

import subprocess
import time
import json
import logging
from datetime import datetime

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.stdout.strip(), result.returncode
    except Exception as e:
        logger.error(f"命令执行失败: {e}")
        return "", 1

def check_container_status():
    """检查容器状态"""
    output, code = run_command("docker ps --filter name=elasticsearch --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'")
    if code == 0 and output:
        logger.info(f"容器状态:\n{output}")
        return True
    else:
        logger.warning("Elasticsearch容器未运行")
        return False

def check_memory_usage():
    """检查内存使用情况"""
    # 检查容器内存使用
    output, code = run_command("docker stats elasticsearch --no-stream --format 'table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}'")
    if code == 0 and output:
        logger.info(f"容器资源使用:\n{output}")
    
    # 检查系统内存
    output, code = run_command("free -h")
    if code == 0:
        logger.info(f"系统内存使用:\n{output}")

def check_elasticsearch_health():
    """检查Elasticsearch健康状态"""
    import requests
    try:
        # 集群健康
        response = requests.get("http://localhost:9200/_cluster/health", 
                              auth=("elastic", "elastic"), timeout=5)
        if response.status_code == 200:
            health = response.json()
            logger.info(f"集群状态: {health['status']}")
            logger.info(f"节点数: {health['number_of_nodes']}")
            logger.info(f"数据节点数: {health['number_of_data_nodes']}")
        
        # 节点统计
        response = requests.get("http://localhost:9200/_nodes/stats", 
                              auth=("elastic", "elastic"), timeout=5)
        if response.status_code == 200:
            stats = response.json()
            for node_id, node_stats in stats['nodes'].items():
                jvm = node_stats['jvm']
                logger.info(f"JVM堆内存使用: {jvm['mem']['heap_used_percent']}%")
                logger.info(f"JVM堆内存: {jvm['mem']['heap_used_in_bytes'] / 1024 / 1024:.1f}MB / {jvm['mem']['heap_max_in_bytes'] / 1024 / 1024:.1f}MB")
        
    except Exception as e:
        logger.error(f"Elasticsearch健康检查失败: {e}")

def monitor_loop():
    """监控循环"""
    logger.info("开始监控Elasticsearch容器...")
    
    while True:
        try:
            logger.info("=" * 50)
            logger.info(f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 检查容器状态
            if check_container_status():
                # 检查内存使用
                check_memory_usage()
                
                # 检查ES健康状态
                check_elasticsearch_health()
            else:
                logger.warning("容器未运行，尝试重启...")
                run_command("docker start elasticsearch")
            
            # 等待下次检查
            time.sleep(60)  # 每分钟检查一次
            
        except KeyboardInterrupt:
            logger.info("监控停止")
            break
        except Exception as e:
            logger.error(f"监控过程中出错: {e}")
            time.sleep(60)

if __name__ == "__main__":
    monitor_loop()
