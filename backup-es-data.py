#!/usr/bin/env python3
"""
Elasticsearch数据备份脚本
在重建容器前备份数据
"""

import subprocess
import os
import sys
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(command, check=True):
    """执行shell命令"""
    try:
        logger.info(f"执行命令: {command}")
        result = subprocess.run(command, shell=True, check=check, 
                              capture_output=True, text=True)
        if result.stdout:
            logger.info(f"输出: {result.stdout}")
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        if check:
            sys.exit(1)
        return e

def backup_elasticsearch_data():
    """备份Elasticsearch数据"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"/workspace/es_backup_{timestamp}"
    
    logger.info("开始备份Elasticsearch数据...")
    
    # 1. 创建备份目录
    os.makedirs(backup_dir, exist_ok=True)
    
    # 2. 检查容器状态
    result = run_command("docker ps -a --filter name=elasticsearch", check=False)
    if result.returncode != 0:
        logger.error("Docker命令不可用或容器不存在")
        return False
    
    # 3. 备份Docker volume数据
    logger.info("备份Docker volume数据...")
    backup_cmd = f"""
    docker run --rm \
      -v es_data:/source:ro \
      -v {backup_dir}:/backup \
      alpine \
      sh -c "cd /source && tar czf /backup/es_data.tar.gz ."
    """
    
    result = run_command(backup_cmd, check=False)
    if result.returncode != 0:
        logger.warning("Docker volume备份失败，尝试其他方法...")
        
        # 4. 如果容器正在运行，尝试从容器内备份
        result = run_command("docker exec elasticsearch tar czf /tmp/es_backup.tar.gz /usr/share/elasticsearch/data", check=False)
        if result.returncode == 0:
            run_command(f"docker cp elasticsearch:/tmp/es_backup.tar.gz {backup_dir}/")
            logger.info("从运行中的容器备份成功")
        else:
            logger.warning("容器备份也失败了")
    
    # 5. 备份配置文件
    if os.path.exists("config.yaml"):
        run_command(f"cp config.yaml {backup_dir}/")
        logger.info("配置文件已备份")
    
    # 6. 创建恢复说明
    restore_instructions = f"""
# Elasticsearch数据恢复说明

## 备份信息
- 备份时间: {datetime.now().isoformat()}
- 备份目录: {backup_dir}

## 恢复步骤
1. 确保新容器已停止
2. 恢复数据:
   docker run --rm -v es_data:/target -v {backup_dir}:/backup alpine sh -c "cd /target && tar xzf /backup/es_data.tar.gz"
3. 启动新容器

## 备份文件
- es_data.tar.gz: Elasticsearch数据文件
- config.yaml: 配置文件
"""
    
    with open(f"{backup_dir}/README.md", "w", encoding="utf-8") as f:
        f.write(restore_instructions)
    
    logger.info(f"备份完成，备份目录: {backup_dir}")
    return backup_dir

if __name__ == "__main__":
    backup_dir = backup_elasticsearch_data()
    if backup_dir:
        print(f"✅ 备份成功: {backup_dir}")
    else:
        print("❌ 备份失败")
        sys.exit(1)
