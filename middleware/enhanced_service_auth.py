"""
增强的服务间认证中间件
实现防伪造、防重放攻击的服务A和服务B之间的安全认证
"""

from fastapi import HTTPException, status, Depends, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any
import hashlib
import hmac
import time
import json
import uuid
import sys
import os

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utils.config import config
from utils.log import log

security = HTTPBearer()

class EnhancedServiceAuthenticator:
    """增强的服务间认证器 - 防伪造和防重放攻击"""
    
    def __init__(self):
        self.service_configs = {}
        self.nonce_cache = {}  # 防重放攻击的nonce缓存
        self.load_service_configs()
    
    def load_service_configs(self):
        """加载服务认证配置"""
        # 从配置文件加载服务配置
        self.service_configs = {
            "service_a": {
                "identifier": config.get("service_auth.service_a.identifier", "RuoyiSystem"),
                "secret_key": config.get("service_auth.service_a.secret_key", "default_secret"),
                "allowed_ips": config.get("service_auth.service_a.allowed_ips", ["127.0.0.1"])
            },
            "service_b": {
                "identifier": config.get("service_auth.service_b.identifier", "FastAPIService"),
                "secret_key": config.get("service_auth.service_b.secret_key", "default_secret")
            }
        }
        
        # 安全配置
        self.signature_expire_seconds = config.get("service_auth.signature_expire_seconds", 300)
        self.max_clock_skew_seconds = config.get("service_auth.max_clock_skew_seconds", 60)
        self.nonce_cache_ttl = config.get("service_auth.nonce_cache_ttl_seconds", 3600)
        
        log.info("Enhanced service authenticator initialized")
    
    def generate_signature(self, service_name: str, timestamp: str, nonce: str, request_body: str = "") -> str:
        """生成请求签名"""
        service_config = self.service_configs.get(service_name)
        if not service_config:
            raise ValueError(f"Unknown service: {service_name}")
        
        # 构建签名字符串: service_name + timestamp + nonce + request_body
        sign_string = f"{service_name}{timestamp}{nonce}{request_body}"
        
        # 使用HMAC-SHA256生成签名
        signature = hmac.new(
            service_config["secret_key"].encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def verify_signature(self, service_name: str, timestamp: str, nonce: str, signature: str, request_body: str = "") -> bool:
        """验证请求签名"""
        try:
            # 验证时间戳（防止重放攻击）
            current_time = int(time.time())
            request_time = int(timestamp)
            
            if abs(current_time - request_time) > self.signature_expire_seconds:
                log.warning(f"Request timestamp expired: {timestamp}, current: {current_time}")
                return False
            
            # 验证nonce（防止重放攻击）
            if self.is_nonce_used(nonce):
                log.warning(f"Nonce already used: {nonce}")
                return False
            
            # 生成期望的签名
            expected_signature = self.generate_signature(service_name, timestamp, nonce, request_body)
            
            # 比较签名
            is_valid = hmac.compare_digest(signature, expected_signature)
            
            if is_valid:
                # 记录已使用的nonce
                self.mark_nonce_used(nonce)
                log.info(f"Service {service_name} signature verified successfully")
            else:
                log.warning(f"Invalid signature for service {service_name}")
            
            return is_valid
            
        except Exception as e:
            log.error(f"Error verifying signature: {str(e)}")
            return False
    
    def is_nonce_used(self, nonce: str) -> bool:
        """检查nonce是否已被使用"""
        current_time = time.time()
        
        # 清理过期的nonce
        expired_nonces = [n for n, t in self.nonce_cache.items() if current_time - t > self.nonce_cache_ttl]
        for n in expired_nonces:
            del self.nonce_cache[n]
        
        return nonce in self.nonce_cache
    
    def mark_nonce_used(self, nonce: str):
        """标记nonce为已使用"""
        self.nonce_cache[nonce] = time.time()
    
    def verify_ip_whitelist(self, service_name: str, client_ip: str) -> bool:
        """验证IP白名单"""
        service_config = self.service_configs.get(service_name)
        if not service_config:
            return False
        
        allowed_ips = service_config.get("allowed_ips", [])
        if not allowed_ips:  # 如果没有配置IP白名单，则允许所有IP
            return True
        
        return client_ip in allowed_ips
    
    def generate_nonce(self) -> str:
        """生成唯一的nonce"""
        return str(uuid.uuid4())

# 创建全局认证器实例
enhanced_service_authenticator = EnhancedServiceAuthenticator()

async def verify_enhanced_service_request(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """验证增强的服务间请求"""
    
    # 获取客户端IP
    client_ip = request.client.host
    
    # 获取请求头中的服务认证信息
    service_name = request.headers.get("X-Service-Name")
    timestamp = request.headers.get("X-Timestamp")
    nonce = request.headers.get("X-Nonce")
    signature = request.headers.get("X-Signature")
    
    if not all([service_name, timestamp, nonce, signature]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing required authentication headers: X-Service-Name, X-Timestamp, X-Nonce, X-Signature"
        )
    
    # 验证服务名称
    if service_name not in enhanced_service_authenticator.service_configs:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Unknown service: {service_name}"
        )
    
    # 验证IP白名单
    if not enhanced_service_authenticator.verify_ip_whitelist(service_name, client_ip):
        log.warning(f"IP {client_ip} not in whitelist for service {service_name}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="IP address not allowed"
        )
    
    # 读取请求体（用于签名验证）
    request_body = ""
    if request.method in ["POST", "PUT", "PATCH"]:
        try:
            body_bytes = await request.body()
            request_body = body_bytes.decode('utf-8') if body_bytes else ""
        except Exception as e:
            log.error(f"Error reading request body: {str(e)}")
            request_body = ""
    
    # 验证签名
    if not enhanced_service_authenticator.verify_signature(service_name, timestamp, nonce, signature, request_body):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid request signature"
        )
    
    log.info(f"Service {service_name} authenticated successfully from IP {client_ip}")
    
    return {
        "service_name": service_name,
        "timestamp": timestamp,
        "nonce": nonce,
        "client_ip": client_ip,
        "authenticated": True
    }

def create_service_request_headers(service_name: str, request_body: str = "") -> Dict[str, str]:
    """为服务A创建请求头（用于调用服务B）"""
    timestamp = str(int(time.time()))
    nonce = enhanced_service_authenticator.generate_nonce()
    signature = enhanced_service_authenticator.generate_signature(service_name, timestamp, nonce, request_body)
    
    return {
        "X-Service-Name": service_name,
        "X-Timestamp": timestamp,
        "X-Nonce": nonce,
        "X-Signature": signature
    }
