"""
服务A JWT认证中间件
专门处理来自服务A的JWT Token验证和用户信息提取
"""

from fastapi import HTTPException, status, Depends, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any, List
import jwt
from jwt.exceptions import InvalidTokenError as JWTError
from datetime import datetime, timedelta
import sys
import os

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utils.config import config
from utils.log import log
from database import SessionLocal
from auth.models import User, Department
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
# 安全增强器已删除 - 简化验证逻辑
# from middleware.security_enhancements import security_enhancer

# JWT配置 - 必须与服务A保持一致
SERVICE_A_JWT_SECRET = "shared-secret-key-between-service-a-and-b-2024"
SERVICE_A_JWT_ALGORITHM = "HS256"
ALLOWED_ISSUERS = ["RuoyiSystem"]
ALLOWED_AUDIENCES = ["FastAPIService"]

security = HTTPBearer()

class ServiceAUser:
    """服务A用户信息封装类"""
    
    def __init__(self, token_payload: Dict[str, Any]):
        self.user_id = int(token_payload.get("sub"))
        self.username = token_payload.get("username")
        self.nick_name = token_payload.get("nick_name")
        self.email = token_payload.get("email")
        self.phone = token_payload.get("phone")
        self.user_type = token_payload.get("user_type", "00")
        self.status = token_payload.get("status", "0")
        
        # 部门信息
        self.dept_id = int(token_payload.get("dept_id", 0)) if token_payload.get("dept_id") else None
        self.dept_name = token_payload.get("dept_name")
        
        # 角色和权限
        self.roles = token_payload.get("roles", [])
        self.permissions = token_payload.get("perms", [])
        
        # 系统信息
        self.issuer = token_payload.get("iss")
        self.audience = token_payload.get("aud")
        self.token_id = token_payload.get("jti")
        self.issued_at = token_payload.get("iat")
        self.expires_at = token_payload.get("exp")
        
        # 会话信息
        self.login_ip = token_payload.get("login_ip")
        self.login_time = token_payload.get("login_time")
        self.session_id = token_payload.get("session_id")
        self.source = token_payload.get("source", "iframe")
        self.redirect_url = token_payload.get("redirect_url")
        
        # 兼容性属性
        self.user_name = self.username
        self.phonenumber = self.phone
        self.is_active = self.status == "0"

    def has_role(self, role_name: str) -> bool:
        """检查是否有指定角色"""
        return role_name in self.roles

    def has_permission(self, permission: str) -> bool:
        """检查是否有指定权限"""
        return permission in self.permissions

    def is_admin(self) -> bool:
        """检查是否是管理员"""
        return "admin" in self.roles

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "user_id": self.user_id,
            "username": self.username,
            "nick_name": self.nick_name,
            "email": self.email,
            "phone": self.phone,
            "dept_id": self.dept_id,
            "dept_name": self.dept_name,
            "roles": self.roles,
            "permissions": self.permissions,
            "is_active": self.is_active
        }

class ServiceAAuthMiddleware:
    """服务A认证中间件"""
    
    def __init__(self):
        self.secret_key = SERVICE_A_JWT_SECRET
        self.algorithm = SERVICE_A_JWT_ALGORITHM
        self.allowed_issuers = ALLOWED_ISSUERS
        self.allowed_audiences = ALLOWED_AUDIENCES

    async def verify_service_a_token(self, token: str, request: Request = None) -> ServiceAUser:
        """验证服务A的JWT Token（增强安全验证）"""
        try:
            # 简化验证 - 统一使用基本JWT验证
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm],
                audience=self.allowed_audiences,
                options={"verify_aud": True}
            )
            
            # 验证签发方
            issuer = payload.get("iss")
            if issuer not in self.allowed_issuers:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=f"Invalid token issuer: {issuer}"
                )
            
            # 验证必要字段
            required_fields = ["sub", "username", "iss", "aud", "iat", "exp"]
            missing_fields = [field for field in required_fields if field not in payload]
            if missing_fields:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing required fields: {missing_fields}"
                )
            
            # 创建用户对象
            service_a_user = ServiceAUser(payload)
            
            # 记录访问日志
            log.info(f"Service A user authenticated: {service_a_user.user_name} "
                    f"from {service_a_user.login_ip} via {service_a_user.source}")
            
            return service_a_user
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.InvalidAudienceError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token audience"
            )
        except JWTError as e:
            log.error(f"JWT validation error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        except Exception as e:
            log.error(f"Unexpected error in token validation: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Token validation failed"
            )

    async def sync_user_to_local_db(self, service_a_user: ServiceAUser) -> Optional[User]:
        """将服务A用户信息同步到本地数据库"""
        try:
            async with SessionLocal() as db:
                # 查找现有用户
                result = await db.execute(
                    select(User).where(User.user_id == service_a_user.user_id)
                )
                existing_user = result.scalar_one_or_none()
                
                if existing_user:
                    # 更新现有用户信息
                    existing_user.user_name = service_a_user.user_name
                    existing_user.nick_name = service_a_user.nick_name
                    existing_user.email = service_a_user.email
                    existing_user.phonenumber = service_a_user.phone
                    existing_user.dept_id = service_a_user.dept_id
                    existing_user.user_type = service_a_user.user_type
                    existing_user.status = service_a_user.status
                    existing_user.is_active = service_a_user.is_active
                    existing_user.sync_time = datetime.utcnow()
                    existing_user.is_synced = True
                    
                    await db.commit()
                    await db.refresh(existing_user)
                    return existing_user
                else:
                    # 创建新用户
                    new_user = User(
                        user_id=service_a_user.user_id,
                        user_name=service_a_user.user_name,
                        nick_name=service_a_user.nick_name,
                        email=service_a_user.email,
                        phonenumber=service_a_user.phone,
                        dept_id=service_a_user.dept_id,
                        user_type=service_a_user.user_type,
                        status=service_a_user.status,
                        is_active=service_a_user.is_active,
                        hashed_password="",  # 服务A用户不需要密码
                        sync_time=datetime.utcnow(),
                        is_synced=True
                    )
                    
                    db.add(new_user)
                    await db.commit()
                    await db.refresh(new_user)
                    return new_user
                    
        except Exception as e:
            log.error(f"Error syncing user to local DB: {str(e)}")
            return None

# 中间件实例
service_a_auth = ServiceAAuthMiddleware()

async def get_service_a_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> ServiceAUser:
    """依赖项：获取服务A认证用户"""

    # 首先尝试从Authorization header获取token
    token = credentials.credentials if credentials else None

    # 如果header中没有token，尝试从URL参数获取
    if not token:
        token = request.query_params.get("token")

    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No authentication token provided"
        )

    # 验证token并获取用户信息（使用增强安全验证）
    service_a_user = await service_a_auth.verify_service_a_token(token, request)
    
    # 同步用户信息到本地数据库（异步执行，不阻塞请求）
    try:
        await service_a_auth.sync_user_to_local_db(service_a_user)
    except Exception as e:
        log.warning(f"User sync failed but continuing: {str(e)}")
    
    # 将用户信息添加到请求状态
    request.state.service_a_user = service_a_user
    
    return service_a_user

# 便捷的依赖项别名已删除 - RequireServiceAAuth 未被使用
