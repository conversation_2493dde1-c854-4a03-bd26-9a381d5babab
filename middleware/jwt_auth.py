"""
JWT认证中间件
重构版本：统一token处理逻辑，优化代码结构
"""

from typing import Optional, Dict, Any, Union
from fastapi import HTTPException, status, Depends, Request, Query
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from auth.models import User
from database import get_db
from utils.log import log
from services.user_service import UserService
from utils.jwt_utils import decode_jwt_token

# HTTP Bearer token scheme
security = HTTPBearer()
# 可选的 Bearer token scheme (不会在缺少时抛出异常)
optional_security = HTTPBearer(auto_error=False)

class JWTAuthService:
    """JWT认证服务 - 重构版本"""
    
    @staticmethod
    def decode_token(token: str) -> Optional[Dict[str, Any]]:
        """解码JWT token - 使用统一JWT工具类"""
        return decode_jwt_token(token)
    
    @staticmethod
    async def get_user_from_token(
        db: AsyncSession,
        token: str
    ) -> Optional[User]:
        """
        从JWT token获取用户信息
        重构优化：逻辑更清晰，减少嵌套层级
        """
        try:
            payload = JWTAuthService.decode_token(token)
            if not payload:
                return None
            
            # 提取关键字段
            user_id = payload.get("user_id")
            username = payload.get("user_name")  # 修改：使用 user_name 字段
            
            # 尝试通过user_id获取用户
            if user_id:
                user = await JWTAuthService._fetch_user_by_id(db, user_id)
                if user:
                    # 将JWT token中的额外信息添加到用户对象
                    JWTAuthService._enrich_user_from_token(user, payload)
                    return user

            # 尝试通过username获取用户
            if username:
                user = await JWTAuthService._fetch_user_by_username(db, username)
                if user:
                    # 将JWT token中的额外信息添加到用户对象
                    JWTAuthService._enrich_user_from_token(user, payload)
                    return user
            
            # 用户不存在，返回None
            log.warning(f"用户不存在: user_id={user_id}, username={username}")
            return None
            
        except Exception as e:
            log.exception(f"从token获取用户失败: {str(e)}")
            return None

    @staticmethod
    async def _fetch_user_by_id(db: AsyncSession, user_id: Any) -> Optional[User]:
        """通过user_id获取用户，使用原生SQL避免ORM关系问题"""
        try:
            from sqlalchemy import text
            sql = text("""
                SELECT user_id, user_name, nick_name, email, dept_id,
                       password, status
                FROM users
                WHERE user_id = :user_id AND del_flag = '0'
            """)

            result = await db.execute(sql, {"user_id": user_id})
            row = result.fetchone()

            if row:
                # 创建简单的用户对象，避免SQLAlchemy关系问题
                class SimpleUser:
                    def __init__(self):
                        pass

                user = SimpleUser()
                user.user_id = row[0]
                user.user_name = row[1]
                user.nick_name = row[2]
                user.email = row[3]
                user.dept_id = row[4]
                user.password = row[5]
                user.status = row[6]
                return user
            return None
        except Exception as e:
            log.error(f"通过user_id获取用户失败: {str(e)}")
            return None

    @staticmethod
    async def _fetch_user_by_username(db: AsyncSession, username: str) -> Optional[User]:
        """通过username获取用户，使用原生SQL避免ORM关系问题"""
        try:
            from sqlalchemy import text
            sql = text("""
                SELECT user_id, user_name, nick_name, email, dept_id,
                       password, status
                FROM users
                WHERE user_name = :username AND del_flag = '0'
            """)

            result = await db.execute(sql, {"username": username})
            row = result.fetchone()

            if row:
                # 创建简单的用户对象，避免SQLAlchemy关系问题
                class SimpleUser:
                    def __init__(self):
                        pass

                user = SimpleUser()
                user.user_id = row[0]
                user.user_name = row[1]
                user.nick_name = row[2]
                user.email = row[3]
                user.dept_id = row[4]
                user.password = row[5]
                user.status = row[6]
                return user
            return None
        except Exception as e:
            log.error(f"通过用户名查询用户失败: {str(e)}")
            return None

    @staticmethod
    def _enrich_user_from_token(user: User, payload: Dict[str, Any]) -> None:
        """
        将JWT token中的额外信息添加到用户对象
        这样可以在不修改数据库的情况下，为用户对象添加动态属性
        """
        try:
            # 添加roles信息（用于部门管理员判断）
            if 'roles' in payload:
                user_roles = payload['roles']
                # 确保角色列表不为空，如果为空或没有标准角色，默认为user
                if not user_roles or not any(role in ['admin', 'deptAdmin', 'user'] for role in user_roles):
                    user_roles = ['user']
                    log.info(f"⚠️ 用户 {user.user_id} 角色为空或非标准角色，默认设置为user")

                setattr(user, 'roles', user_roles)
                log.info(f"✅ 为用户 {user.user_id} 添加roles: {user_roles}")
            else:
                # 如果token中没有roles字段，默认设置为user
                setattr(user, 'roles', ['user'])
                log.warning(f"⚠️ JWT token中没有roles字段，默认设置为user角色，payload keys: {list(payload.keys())}")

            # 添加其他可能的JWT字段
            if 'dept_id' in payload and not user.dept_id:
                # 如果数据库中没有dept_id，使用token中的
                setattr(user, 'dept_id', payload['dept_id'])
                log.debug(f"为用户 {user.user_id} 添加dept_id: {payload['dept_id']}")

        except Exception as e:
            log.warning(f"丰富用户信息失败: {str(e)}")
            # 不抛出异常，继续使用基本用户信息



    @staticmethod
    def extract_user_info(token: str) -> Optional[Dict[str, Any]]:
        """
        从JWT token中提取用户信息（不查询数据库）
        重构：统一字段命名，增加类型提示
        """
        try:
            payload = JWTAuthService.decode_token(token)
            if not payload:
                return None
            
            return {
                "user_id": payload.get("user_id"),
                "username": payload.get("user_name"),  # 修改：使用 user_name 字段
                "dept_id": payload.get("dept_id"),
                "dept_name": payload.get("dept_name"),
                "nick_name": payload.get("nick_name", ""),  # 统一使用nick_name
                "exp": payload.get("exp"),
                "iat": payload.get("iat"),
                "roles": payload.get("roles", [])
            }
        except Exception as e:
            log.error(f"提取token用户信息失败: {str(e)}")
            return None


# 保留原始函数名 get_current_user_from_jwt
async def get_current_user_from_jwt(
    request: Request = None,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    从JWT token获取当前用户（强制认证）
    保留原始名称以兼容现有代码
    支持从Authorization header或URL参数获取token
    """
    # 尝试从Authorization header获取token
    token = credentials.credentials if credentials else None

    # 如果header中没有token，尝试从URL参数获取（兼容simple_service_a_user的功能）
    if not token and request:
        token = request.query_params.get("token")

    # 如果仍然没有token，抛出认证错误
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No authentication token provided",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 添加调试日志
    log.debug(f"收到JWT token: {token[:20] if token else 'None'}...")
    log.debug(f"Token长度: {len(token) if token else 0}")
    log.debug(f"Token段数: {len(token.split('.')) if token else 0}")

    user = await JWTAuthService.get_user_from_token(db, token)

    if not user:
        log.warning(f"JWT认证失败: 无效的用户凭证, token: {token[:20] if token else 'None'}...")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的用户凭证",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 检查用户状态（只对数据库用户进行状态检查）
    if hasattr(user, 'status') and user.status != '0':  # status='0'表示正常，'1'表示停用
        log.warning(f"JWT认证失败: 用户已被禁用, user_id: {user.user_id}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户已被禁用"
        )

    log.debug(f"JWT认证成功: user_id={user.user_id}, username={user.user_name}")
    return user


# 保留原始函数名 get_optional_user_from_jwt
async def get_optional_user_from_jwt(
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """
    可选的JWT用户获取函数，不强制要求认证
    保留原始名称以兼容现有代码
    """
    authorization = request.headers.get("Authorization")
    if not authorization or not authorization.startswith("Bearer "):
        return None
    
    token = authorization.split(" ", 1)[1]
    try:
        user = await JWTAuthService.get_user_from_token(db, token)
        return user if user and user.is_active else None
    except Exception as e:
        log.error(f"可选JWT认证失败: {str(e)}", exc_info=True)
        return None


# 保留原始函数名 get_user_from_token_payload
def get_user_from_token_payload(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    从JWT token中直接解码用户信息，不查询数据库
    保留原始名称以兼容现有代码
    """
    token = credentials.credentials
    user_info = JWTAuthService.extract_user_info(token)
    
    if user_info:
        # 确保user_id存在
        if not user_info.get("user_id"):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token中缺少用户ID"
            )
        return user_info
    
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证token"
    )


async def get_current_user_from_jwt_or_query(
    request: Request,
    token: Optional[str] = Query(None, description="JWT Token (查询参数)"),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(optional_security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    从JWT token获取当前用户 - 支持Header和查询参数两种方式
    优先使用Authorization Header，如果没有则使用查询参数中的token
    """
    jwt_token = None

    # 优先使用Authorization Header
    if credentials:
        jwt_token = credentials.credentials
        log.info("🔑 使用Authorization Header中的Token")
    # 如果没有Header，尝试使用查询参数
    elif token:
        jwt_token = token
        log.info("🔑 使用查询参数中的Token")

    if not jwt_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="缺少认证token，请在Authorization Header或token查询参数中提供"
        )

    # 使用现有的认证逻辑
    user = await JWTAuthService.get_user_from_token(db, jwt_token)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证token"
        )

    log.info(f"✅ 用户认证成功: {user.user_name} (ID: {user.user_id})")
    return user