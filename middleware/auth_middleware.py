"""
认证和权限中间件
"""

from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
import sys
import os

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from auth.service import get_current_user

# 统一使用配置文件中的JWT配置
from utils.config import config
from auth.models import User
from database import SessionLocal
from utils.log import log

# 统一使用配置文件中的密钥
SECRET_KEY = config.get("service_auth.jwt.secret_key", "default_jwt_secret")
ALGORITHM = config.get("service_auth.jwt.algorithm", "HS256")

security = HTTPBearer()

async def get_current_user_from_token(credentials: HTTPAuthorizationCredentials) -> Optional[User]:
    """从JWT token获取当前用户"""
    try:
        token = credentials.credentials
        # 使用统一JWT工具类解码token
        from utils.jwt_utils import decode_jwt_token

        payload = decode_jwt_token(token)
        if not payload:
            return None

        # 优先使用user_id（新格式）
        user_id = payload.get("user_id")
        username = payload.get("user_name") or payload.get("sub")  # 修改：使用 user_name 字段，兼容旧格式

        if user_id is None and username is None:
            return None
            
        # 使用数据库会话获取用户
        async with SessionLocal() as db:
            user = await get_current_user(db, token)
            return user
            
    except Exception as e:
        log.error(f"Error getting user from token: {str(e)}")
        return None

async def verify_document_access(user: User, document_id: int, permission_type: str) -> bool:
    """验证用户对文档的访问权限"""
    try:
        async with SessionLocal() as db:
            from auth.service import check_user_document_permission
            return await check_user_document_permission(db, user.user_id, document_id, permission_type)
    except Exception as e:
        log.error(f"Error verifying document access: {str(e)}")
        return False

class DocumentPermissionChecker:
    """文档权限检查器"""
    
    def __init__(self, permission_type: str):
        self.permission_type = permission_type
    
    async def __call__(self, request: Request, credentials: HTTPAuthorizationCredentials = security):
        # 获取当前用户
        user = await get_current_user_from_token(credentials)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 从请求路径中提取文档ID
        document_id = None
        path_parts = request.url.path.split('/')
        
        # 尝试从不同的路径模式中提取document_id
        if 'documents' in path_parts:
            try:
                doc_index = path_parts.index('documents')
                if doc_index + 1 < len(path_parts):
                    document_id = int(path_parts[doc_index + 1])
            except (ValueError, IndexError):
                pass
        
        # 从查询参数中获取document_id
        if not document_id:
            document_id = request.query_params.get('document_id')
            if document_id:
                try:
                    document_id = int(document_id)
                except ValueError:
                    document_id = None
        
        if not document_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Document ID not found in request"
            )
        
        # 检查权限
        has_permission = await verify_document_access(user, document_id, self.permission_type)
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions for {self.permission_type} access to document"
            )
        
        # 将用户信息添加到请求状态中
        request.state.current_user = user
        return user

# 预定义的权限检查器
require_read_permission = DocumentPermissionChecker("read")
require_write_permission = DocumentPermissionChecker("write")
require_delete_permission = DocumentPermissionChecker("delete")
require_share_permission = DocumentPermissionChecker("share")

async def log_user_action(request: Request, action: str, resource_type: str = None, resource_id: int = None, status: str = "success", user=None):
    """记录用户操作日志"""
    try:
        # 优先使用传入的用户对象，否则尝试从request.state获取
        if user:
            user_id = getattr(user, 'user_id', None)
        else:
            user = getattr(request.state, 'current_user', None)
            user_id = user.user_id if user else None
        
        # 获取客户端IP
        client_ip = request.client.host if request.client else None
        
        # 获取User-Agent
        user_agent = request.headers.get("user-agent")
        
        # 只记录到日志文件，不写入数据库
        log.info(f"用户操作: user_id={user_id}, action={action}, resource_type={resource_type}, "
                f"resource_id={resource_id}, status={status}, ip={client_ip}")
            
    except Exception as e:
        log.error(f"Error logging user action: {str(e)}")

class AuditLogMiddleware:
    """审计日志中间件"""
    
    def __init__(self, action: str, resource_type: str = None):
        self.action = action
        self.resource_type = resource_type
    
    async def __call__(self, request: Request):
        # 从路径中提取资源ID
        resource_id = None
        path_parts = request.url.path.split('/')
        
        # 尝试从路径末尾获取数字ID
        for part in reversed(path_parts):
            try:
                resource_id = int(part)
                break
            except ValueError:
                continue
        
        # 记录操作日志
        await log_user_action(request, self.action, self.resource_type, resource_id)
        
        return request

# 预定义的审计日志中间件
log_document_access = AuditLogMiddleware("document_access", "document")
log_document_download = AuditLogMiddleware("document_download", "document")
log_document_upload = AuditLogMiddleware("document_upload", "document")
log_permission_request = AuditLogMiddleware("permission_request", "permission_request")
