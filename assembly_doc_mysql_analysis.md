# /assembly_doc 接口 MySQL 操作分析报告

## 📋 概述

`/assembly_doc` 接口是一个文档汇编和结构化信息提取的核心接口，涉及多个 MySQL 操作来存储和管理提取的结构化数据。

## 🏗️ 涉及的 MySQL 表

### 1. `documents` 表（文档记录管理）
```sql
CREATE TABLE `documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `doc_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文档唯一标识（MD5哈希值）',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文档标题',
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `project_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目名称',
  `owner_id` bigint(20) NOT NULL COMMENT '文档所有者ID',
  `department_id` bigint(20) DEFAULT NULL COMMENT '所属部门ID',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件类型',
  `status` enum('active','archived','deleted') COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '文档状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `doc_id` (`doc_id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_project_name` (`project_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档记录表'
```

### 2. `project_extract` 表（项目档案）
```sql
CREATE TABLE IF NOT EXISTS `project_extract` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `project_name` varchar(500) NOT NULL COMMENT '项目名称',
  `project_key` varchar(255) NOT NULL COMMENT '项目唯一标识（基于项目名称生成）',
  `project_no` varchar(100) DEFAULT NULL COMMENT '项目编号',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `total_investment` bigint(20) DEFAULT NULL COMMENT '总投资金额（单位：万元）',
  `responsible_unit` text COMMENT '承担单位',
  `leader` varchar(200) DEFAULT NULL COMMENT '项目负责人',
  `research_points` text COMMENT '主要研究内容',
  `innovation` text COMMENT '创新点',
  `main_deliverables` text COMMENT '主要交付成果',
  `patent` text COMMENT '专利信息',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_key` (`project_key`),
  UNIQUE KEY `uk_project_name` (`project_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目档案提取表'
```

### 3. `conference_extract` 表（文书档案）
```sql
CREATE TABLE IF NOT EXISTS `conference_extract` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `conference_name` varchar(500) NOT NULL COMMENT '文书名称',
  `conference_key` varchar(255) NOT NULL COMMENT '文书唯一标识（基于文书名称生成）',
  `conference_no` varchar(100) DEFAULT NULL COMMENT '文书编号',
  `date` date DEFAULT NULL COMMENT '日期',
  `type` varchar(100) DEFAULT NULL COMMENT '文书类型',
  `organizer` text COMMENT '组织方',
  `participants` text COMMENT '参与方',
  `summary` text COMMENT '摘要',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_conference_key` (`conference_key`),
  UNIQUE KEY `uk_conference_name` (`conference_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文书档案提取表'
```

## 🔧 MySQL 操作流程

### 1. 表创建操作

**位置**: `knowledge_control.py` -> `_create_mysql_tables()`

```python
async def _create_mysql_tables(self):
    """创建MySQL表 - 使用 SQLAlchemy"""
    try:
        from database import SessionLocal
        from sqlalchemy import text

        async with SessionLocal() as session:
            for table_name, create_sql in self.TABLE_SCHEMAS.items():
                await session.execute(text(create_sql))
                print(f"✅ MySQL表创建成功: {table_name}")

            await session.commit()

    except Exception as e:
        print(f"❌ 创建MySQL表失败: {e}")
        raise
```

**触发时机**:
- 应用启动时通过 `knowledge_control.init()` 调用
- 检测到 MySQL 配置时自动创建表

### 2. 文档记录创建操作

**位置**: `app.py` -> `create_document_records()`

```python
async def create_document_records(
    db: AsyncSession,
    processed_files: List[Dict[str, Any]],
    project_name: str,
    owner_id: int,
    department_id: int = None
):
    """在汇编成功后创建文档记录"""
    try:
        for file_info in processed_files:
            try:
                # 从处理结果中提取信息
                file_url = file_info.get("url", "")
                doc_id = file_info.get("doc_id", "")

                if not doc_id or not file_url:
                    log.warning(f"跳过无效的文件信息: {file_info}")
                    continue

                # 检查文档是否已存在
                existing_doc = await DocumentService.get_document_by_doc_id(db, doc_id)
                if existing_doc:
                    log.info(f"文档已存在，跳过创建: doc_id={doc_id}")
                    continue

                # 生成文档标题
                file_name = file_url.split('/')[-1]
                title = os.path.splitext(file_name)[0] if file_name else project_name

                # 创建文档记录
                await DocumentService.create_document(
                    db=db,
                    doc_id=doc_id,
                    title=title,
                    file_path=file_url,
                    owner_id=owner_id,
                    department_id=department_id,
                    project_name=project_name
                )

                log.info(f"文档记录创建成功: doc_id={doc_id}, title={title}")

            except Exception as e:
                log.error(f"创建文档记录失败: {file_info}, 错误: {e}")
                continue

    except Exception as e:
        log.error(f"批量创建文档记录失败: {e}")
```

**触发时机**:
- 文档汇编任务成功完成后
- 在 `process_task()` 中调用，位于第1103-1110行

### 3. 数据插入/更新操作

**位置**: `app.py` -> `upsert_extract_data()`

#### 2.1 项目档案数据操作 (`action="项目档案"`)

```python
# 生成项目key
project_key = generate_key_from_name(project_name)  # MD5哈希

# REPLACE INTO SQL
replace_sql = """
REPLACE INTO `project_extract` (
    `project_name`, `project_key`, `project_no`, `start_date`, `end_date`,
    `total_investment`, `responsible_unit`, `leader`, `research_points`,
    `innovation`, `main_deliverables`, `patent`
) VALUES (
    :project_name, :project_key, :project_no, :start_date, :end_date,
    :total_investment, :responsible_unit, :leader, :research_points,
    :innovation, :main_deliverables, :patent
)
"""

params = {
    'project_name': project_name,
    'project_key': project_key,
    'project_no': extracted_data.get('project_no'),
    'start_date': extracted_data.get('start_date'),
    'end_date': extracted_data.get('end_date'),
    'total_investment': extracted_data.get('total_investment'),
    'responsible_unit': extracted_data.get('responsible_unit'),
    'leader': extracted_data.get('leader'),
    'research_points': extracted_data.get('research_points'),
    'innovation': extracted_data.get('innovation'),
    'main_deliverables': extracted_data.get('main_deliverables'),
    'patent': extracted_data.get('patent')
}
```

#### 2.2 文书档案数据操作 (`action="文书档案"`)

```python
# 生成文书key
conference_key = generate_key_from_name(project_name)  # MD5哈希

# REPLACE INTO SQL
replace_sql = """
REPLACE INTO `conference_extract` (
    `conference_name`, `conference_key`, `conference_no`, `date`, `type`,
    `organizer`, `participants`, `summary`
) VALUES (
    :conference_name, :conference_key, :conference_no, :date, :type,
    :organizer, :participants, :summary
)
"""

params = {
    'conference_name': project_name,
    'conference_key': conference_key,
    'conference_no': extracted_data.get('conference_no'),
    'date': extracted_data.get('date'),
    'type': extracted_data.get('type'),
    'organizer': extracted_data.get('organizer'),
    'participants': extracted_data.get('participants'),
    'summary': extracted_data.get('summary')
}
```

### 4. 通过 SQLKnowledge 的操作

**位置**: `knowledge_control.py` -> `finalize_project()`

```python
# 使用项目/文书唯一键进行upsert操作
key_field = "project_key" if action == "项目档案" else "conference_key"
if not await self.sql_knowledge.upsert(
    self.TABLE_MAPPING[action],  # "project_extract" 或 "conference_extract"
    sql_data,
    key_fields=[key_field]  # 使用项目或文书的唯一键
):
    return False
```

## 🔄 数据流转过程

### 完整的 MySQL 操作流程：

1. **应用启动阶段**:
   ```
   app.py 启动 → knowledge_control.init() → _create_mysql_tables() → 创建表结构
   ```

2. **文档汇编请求阶段**:
   ```
   /assembly_doc 接口 → process_task() → qa.process_assembly() → 提取结构化信息
   ```

3. **数据存储阶段**:
   ```
   提取完成 → create_document_records() → 创建documents表记录
            ↓
            upsert_extract_data() → REPLACE INTO 操作 → 存储到对应提取表
   ```

4. **备用存储路径**:
   ```
   knowledge_control.finalize_project() → sql_knowledge.upsert() → MySQLClient.upsert()
   ```

## 📊 使用的 MySQL 客户端

### 1. 主要路径 - SQLAlchemy (app.py)
```python
# 使用 FastAPI 的依赖注入获取数据库会话
db: AsyncSession = Depends(get_db)

# 执行 SQL
await db.execute(text(replace_sql), params)
await db.commit()
```

### 2. 备用路径 - MySQLClient (knowledge_control.py)
```python
# 通过 SQLKnowledge 调用 MySQLClient
await self.sql_knowledge.upsert(table_name, data, key_fields)

# MySQLClient 内部使用 SQLAlchemy
async with self.session_factory() as session:
    result = await session.execute(text(sql), params or {})
```

## 🔑 关键特性

### 1. 唯一键生成
```python
def generate_key_from_name(name: str) -> str:
    """根据名称生成唯一标识"""
    return hashlib.md5(name.encode('utf-8')).hexdigest()
```

### 2. REPLACE INTO 语义
- **自动处理插入/更新逻辑**
- 如果记录不存在（基于唯一键），执行 INSERT
- 如果记录存在，执行 DELETE + INSERT
- 基于 `project_key` 或 `conference_key` 唯一约束

### 3. 数据预处理
- **投资金额解析**: 将"200万元"、"1.5亿元"等转换为万元单位的整数
- **内容优化**: 去重、合并相似内容
- **字段映射**: 将提取的原始数据映射到数据库字段

## ⚠️ 注意事项

### 1. 事务管理
- 使用 `await db.commit()` 提交事务
- 异常时使用 `await db.rollback()` 回滚

### 2. 错误处理
- 数据库操作失败不影响主流程
- 记录详细的错误日志
- 提供降级处理机制

### 3. 性能考虑
- 使用连接池管理数据库连接
- 批量操作优化
- 异步操作避免阻塞

## 📈 统计信息

- **涉及表数量**: 3个 (`documents`, `project_extract`, `conference_extract`)
- **主要操作类型**: CREATE TABLE, INSERT, REPLACE INTO
- **数据库客户端**: SQLAlchemy (主要) + MySQLClient (备用)
- **事务支持**: 完整的事务管理和回滚机制
- **唯一性约束**: 基于 MD5 哈希的唯一键机制

## 🔄 documents 表操作详细说明

### 操作时机
**在文档汇编任务成功完成后**，系统会调用 `create_document_records()` 函数来创建文档记录。

### 具体步骤
1. **遍历处理成功的文件列表** (`result.get("processed", [])`)
2. **提取文件信息**: `file_url`, `doc_id`
3. **检查文档是否已存在**: 通过 `DocumentService.get_document_by_doc_id()` 查询
4. **生成文档标题**: 从文件路径提取文件名作为标题
5. **创建文档记录**: 调用 `DocumentService.create_document()` 插入数据库

### 存储的数据
- **doc_id**: 文档唯一标识（MD5哈希值）
- **title**: 文档标题（从文件名提取）
- **file_path**: 文件路径（原始URL）
- **project_name**: 项目名称
- **owner_id**: 文档所有者ID（当前用户ID）
- **department_id**: 所属部门ID
- **file_type**: 文件类型（从扩展名提取）
- **status**: 文档状态（默认为 'active'）

### 权限关联
创建的文档记录会与权限系统关联，支持：
- **文档访问权限控制**
- **部门级别的文档管理**
- **用户级别的文档所有权**
