FROM docker.elastic.co/elasticsearch/elasticsearch:8.14.3

# 创建插件目录
USER root
RUN mkdir -p /usr/share/elasticsearch/plugins/ik

# 复制本地的 IK 分词器文件
COPY elasticsearch-analysis-ik-8.14.3.zip /usr/share/elasticsearch/plugins/ik/
RUN cd /usr/share/elasticsearch/plugins/ik && \
    unzip elasticsearch-analysis-ik-8.14.3.zip && \
    rm elasticsearch-analysis-ik-8.14.3.zip && \
    chown -R elasticsearch:elasticsearch /usr/share/elasticsearch/plugins/ik

# 切换回elasticsearch用户
USER elasticsearch

# 设置必要的环境变量
ENV discovery.type=single-node
ENV xpack.security.enabled=true
ENV ELASTIC_PASSWORD=elastic

# 生产环境优化设置
ENV ES_JAVA_OPTS="\
    -Xms4g \
    -Xmx4g \
    -XX:+UseG1GC \
    -XX:G1ReservePercent=25 \
    -XX:InitiatingHeapOccupancyPercent=30 \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/var/lib/elasticsearch \
    -XX:ErrorFile=/var/log/elasticsearch/hs_err_pid%p.log \
    -Xlog:gc*,gc+age=trace,safepoint:file=/var/log/elasticsearch/gc.log:utctime,pid,tags:filecount=32,filesize=64m"

# 生产环境配置
ENV bootstrap.memory_lock=true
ENV network.host=0.0.0.0
ENV http.port=9200
ENV transport.port=9300

# 暴露Elasticsearch端口
EXPOSE 9200 9300