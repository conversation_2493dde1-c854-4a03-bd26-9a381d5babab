"""
安全的搜索API接口
代理Elasticsearch请求，提供权限控制和数据过滤
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
import httpx
import requests
import json
from utils.log import log
from utils.config import config
from middleware.jwt_auth import JWTAuthService

router = APIRouter(prefix="/search",tags=["search"])
security = HTTPBearer()

# Elasticsearch配置
ES_HOST = config.get("elasticsearch.host", "***********")
ES_PORT = config.get("elasticsearch.port", 9200)
ES_USERNAME = config.get("elasticsearch.username", "elastic")
ES_PASSWORD = config.get("elasticsearch.password", "elastic")

# 构建基础URL，处理配置中可能包含协议和端口的情况
if ES_HOST.startswith("http://") or ES_HOST.startswith("https://"):
    # 配置中已包含完整URL
    ES_BASE_URL = ES_HOST
else:
    # 配置中只有主机名或IP，需要添加协议和端口
    ES_BASE_URL = f"http://{ES_HOST}:{ES_PORT}"

class DocumentSearchRequest(BaseModel):
    """文档搜索请求"""
    query: Dict[str, Any] = Field(..., description="Elasticsearch查询DSL")
    highlight: Optional[Dict[str, Any]] = Field(None, description="高亮配置")
    source: Optional[Dict[str, Any]] = Field(None, description="字段过滤")
    from_: Optional[int] = Field(0, alias="from", description="分页起始位置")
    size: Optional[int] = Field(10, description="返回结果数量")
    sort: Optional[List[Dict[str, Any]]] = Field(None, description="排序配置")

class ProjectSearchRequest(BaseModel):
    """项目信息搜索请求"""
    query: Dict[str, Any] = Field(..., description="Elasticsearch查询DSL")
    highlight: Optional[Dict[str, Any]] = Field(None, description="高亮配置")
    from_: Optional[int] = Field(0, alias="from", description="分页起始位置")
    size: Optional[int] = Field(10, description="返回结果数量")

def validate_search_query(query: Dict[str, Any]) -> bool:
    """验证搜索查询的安全性"""
    
    # 检查是否包含危险的查询类型
    dangerous_queries = [
        "script",           # 脚本查询可能执行任意代码
        "delete_by_query",  # 删除操作
        "update_by_query",  # 更新操作
        "_delete_by_query", # 删除操作
        "_update_by_query", # 更新操作
    ]
    
    query_str = json.dumps(query).lower()
    for dangerous in dangerous_queries:
        if dangerous in query_str:
            log.warning(f"检测到危险查询类型: {dangerous}")
            return False
    
    return True

def filter_response_data(response_data: Dict[str, Any]) -> Dict[str, Any]:
    """过滤响应数据，移除敏感信息"""
    
    if "hits" in response_data and "hits" in response_data["hits"]:
        for hit in response_data["hits"]["hits"]:
            # 移除内部字段
            if "_source" in hit:
                source = hit["_source"]
                
                # 移除可能的敏感字段
                sensitive_fields = ["password", "token", "secret", "key", "internal"]
                for field in sensitive_fields:
                    if field in source:
                        del source[field]
                
                # 移除embedding向量（太大且不需要返回给前端）
                if "embedding" in source:
                    del source["embedding"]
                if "vector" in source:
                    del source["vector"]
    
    return response_data

async def proxy_elasticsearch_request(
    index: str, 
    request_data: Dict[str, Any],
    user_info: Dict[str, Any]
) -> Dict[str, Any]:
    """代理Elasticsearch请求"""
    
    try:
        # 构建ES请求URL
        url = f"{ES_BASE_URL}/{index}/_search"

        # 记录访问日志
        log.info(f"用户 {user_info.get('user_name')} 搜索索引 {index}")
        log.debug(f"搜索参数: {json.dumps(request_data, ensure_ascii=False)}")
        log.debug(f"ES连接URL: {url}")
        log.debug(f"ES认证: {ES_USERNAME}:***")

        # 先尝试使用requests测试连接（同步）
        try:
            test_response = requests.get(
                f"{ES_BASE_URL}/_cluster/health",
                auth=(ES_USERNAME, ES_PASSWORD),
                timeout=5
            )
            log.debug(f"ES连接测试成功: {test_response.status_code}")
        except Exception as test_e:
            log.error(f"ES连接测试失败: {test_e}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"无法连接到Elasticsearch: {str(test_e)}"
            )

        # 发送请求到Elasticsearch
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                url,
                json=request_data,
                auth=(ES_USERNAME, ES_PASSWORD),
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # 过滤敏感数据
                filtered_result = filter_response_data(result)
                
                log.info(f"搜索成功，返回 {filtered_result.get('hits', {}).get('total', {}).get('value', 0)} 条结果")
                return filtered_result
            else:
                log.error(f"Elasticsearch请求失败: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"搜索服务错误: {response.status_code}"
                )
                
    except httpx.TimeoutException as e:
        log.error(f"Elasticsearch请求超时: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="搜索服务超时，请稍后重试"
        )
    except Exception as e:
        log.error(f"搜索请求异常: {str(e)}")
        log.error(f"异常类型: {type(e)}")
        import traceback
        log.error(f"异常堆栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索服务内部错误: {str(e)}"
        )

@router.post("/docs", summary="文档搜索", description="搜索文档内容")
async def search_documents(
    request: DocumentSearchRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    文档搜索接口
    
    - 提供安全的文档搜索功能
    - 支持全文搜索、高亮显示
    - 自动过滤敏感数据
    """
    
    # 验证JWT token
    payload = JWTAuthService.decode_token(credentials.credentials)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证token"
        )

    # 从payload中提取用户信息
    user_info = {
        "user_id": payload.get("user_id"),
        "user_name": payload.get("user_name"),
        "nick_name": payload.get("nick_name", ""),
        "dept_id": payload.get("dept_id"),
        "dept_name": payload.get("dept_name", ""),
        "roles": payload.get("roles", [])
    }
    
    # 验证查询安全性
    if not validate_search_query(request.query):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="查询包含不安全的操作"
        )
    
    # 构建ES请求数据
    es_request = {
        "query": request.query,
        "from": request.from_,
        "size": min(request.size, 100),  # 限制最大返回数量
    }
    
    if request.highlight:
        es_request["highlight"] = request.highlight
    
    if request.source:
        es_request["_source"] = request.source
    
    if request.sort:
        es_request["sort"] = request.sort
    
    # 代理请求到Elasticsearch
    result = await proxy_elasticsearch_request("docs", es_request, user_info)
    
    return result

@router.post("/projects", summary="项目信息搜索", description="搜索项目信息")
async def search_projects(
    request: ProjectSearchRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    项目信息搜索接口
    
    - 提供安全的项目信息搜索功能
    - 支持多字段搜索、高亮显示
    - 自动过滤敏感数据
    """
    
    # 验证JWT token
    payload = JWTAuthService.decode_token(credentials.credentials)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证token"
        )

    # 从payload中提取用户信息
    user_info = {
        "user_id": payload.get("user_id"),
        "username": payload.get("username"),
        "nick_name": payload.get("nick_name", ""),
        "dept_id": payload.get("dept_id"),
        "dept_name": payload.get("dept_name", ""),
        "roles": payload.get("roles", [])
    }
    
    # 验证查询安全性
    if not validate_search_query(request.query):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="查询包含不安全的操作"
        )
    
    # 构建ES请求数据
    es_request = {
        "query": request.query,
        "from": request.from_,
        "size": min(request.size, 100),  # 限制最大返回数量
    }
    
    if request.highlight:
        es_request["highlight"] = request.highlight
    
    # 代理请求到Elasticsearch
    result = await proxy_elasticsearch_request("projectinfo", es_request, user_info)
    
    return result

@router.get("/health", summary="搜索服务健康检查")
async def search_health():
    """检查Elasticsearch连接状态"""
    
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(
                f"{ES_BASE_URL}/_cluster/health",
                auth=(ES_USERNAME, ES_PASSWORD)
            )
            
            if response.status_code == 200:
                health_data = response.json()
                return {
                    "status": "healthy",
                    "elasticsearch": {
                        "cluster_name": health_data.get("cluster_name"),
                        "status": health_data.get("status"),
                        "number_of_nodes": health_data.get("number_of_nodes")
                    }
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": f"Elasticsearch返回状态码: {response.status_code}"
                }
                
    except Exception as e:
        return {
            "status": "unhealthy", 
            "error": str(e)
        }
