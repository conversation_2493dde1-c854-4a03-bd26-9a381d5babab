"""
文档管理路由
提供文档的CRUD操作和查询功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from pydantic import BaseModel, Field
from database import get_db
from middleware.jwt_auth import get_current_user_from_jwt
from auth.models import User, DocumentStatus
from services.document_service import DocumentService
from utils.log import log


router = APIRouter(prefix="/documents", tags=["文档管理"])


# Pydantic 模型定义
class DocumentResponse(BaseModel):
    """文档响应模型"""
    id: int
    doc_id: str
    title: str
    file_path: str
    file_name: str
    file_type: Optional[str] = None
    project_name: Optional[str] = None
    owner_id: int
    owner_name: Optional[str] = None
    department_id: Optional[int] = None
    department_name: Optional[str] = None
    file_size: Optional[int] = None
    status: str
    created_at: str
    updated_at: str


class DocumentSearchRequest(BaseModel):
    """文档搜索请求模型"""
    title: Optional[str] = Field(None, description="文档标题关键词")
    project_name: Optional[str] = Field(None, description="项目名称关键词")
    file_type: Optional[str] = Field(None, description="文件类型")
    department_id: Optional[int] = Field(None, description="部门ID")
    status: Optional[str] = Field(None, description="文档状态")
    limit: int = Field(20, description="返回数量限制", ge=1, le=100)
    offset: int = Field(0, description="偏移量", ge=0)


class DocumentUpdateRequest(BaseModel):
    """文档更新请求模型"""
    title: Optional[str] = Field(None, description="文档标题")
    project_name: Optional[str] = Field(None, description="项目名称")
    department_id: Optional[int] = Field(None, description="部门ID")
    status: Optional[str] = Field(None, description="文档状态")


class BaseResponse(BaseModel):
    """基础响应模型"""
    code: int
    msg: str
    data: Optional[dict] = None


@router.get("/search", response_model=BaseResponse)
async def search_documents(
    title: Optional[str] = Query(None, description="文档标题关键词"),
    project_name: Optional[str] = Query(None, description="项目名称关键词"),
    file_type: Optional[str] = Query(None, description="文件类型"),
    department_id: Optional[int] = Query(None, description="部门ID"),
    status: Optional[str] = Query(None, description="文档状态"),
    limit: int = Query(20, description="返回数量限制", ge=1, le=100),
    offset: int = Query(0, description="偏移量", ge=0),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """搜索文档"""
    try:
        # 转换状态字符串为枚举
        doc_status = None
        if status:
            try:
                doc_status = DocumentStatus(status.upper())
            except ValueError:
                return BaseResponse(
                    code=400,
                    msg=f"无效的文档状态: {status}",
                    data=None
                )
        
        # 搜索文档
        documents = await DocumentService.search_documents(
            db=db,
            title=title,
            project_name=project_name,
            file_type=file_type,
            department_id=department_id,
            status=doc_status,
            limit=limit,
            offset=offset
        )
        
        # 格式化响应
        document_list = [DocumentService.format_document_info(doc) for doc in documents]
        
        return BaseResponse(
            code=200,
            msg="搜索成功",
            data={
                "documents": document_list,
                "total": len(document_list),
                "limit": limit,
                "offset": offset
            }
        )
        
    except Exception as e:
        log.error(f"搜索文档失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"搜索文档失败: {str(e)}",
            data=None
        )


@router.get("/{document_id}", response_model=BaseResponse)
async def get_document(
    document_id: int = Path(..., description="文档ID"),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """根据ID获取文档详情"""
    try:
        document_info = await DocumentService.get_document_with_details(db, document_id)

        if not document_info:
            return BaseResponse(
                code=404,
                msg="文档不存在",
                data=None
            )

        return BaseResponse(
            code=200,
            msg="获取成功",
            data=document_info
        )

    except Exception as e:
        log.error(f"获取文档失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"获取文档失败: {str(e)}",
            data=None
        )


@router.get("/by-doc-id/{doc_id}", response_model=BaseResponse)
async def get_document_by_doc_id(
    doc_id: str = Path(..., description="文档唯一标识"),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """根据doc_id获取文档详情"""
    try:
        document_info = await DocumentService.get_document_by_doc_id_with_details(db, doc_id)

        if not document_info:
            return BaseResponse(
                code=404,
                msg=f"文档不存在（doc_id: {doc_id}）",
                data=None
            )

        return BaseResponse(
            code=200,
            msg="获取成功",
            data=document_info
        )

    except Exception as e:
        log.error(f"根据doc_id获取文档失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"获取文档失败: {str(e)}",
            data=None
        )


@router.put("/{document_id}", response_model=BaseResponse)
async def update_document(
    request: DocumentUpdateRequest,
    document_id: int = Path(..., description="文档ID"),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """更新文档信息"""
    try:
        # 检查文档是否存在
        document = await DocumentService.get_document_by_id(db, document_id)
        if not document:
            return BaseResponse(
                code=404,
                msg="文档不存在",
                data=None
            )
        
        # 检查权限（只有文档所有者或管理员可以更新）
        if document.owner_id != current_user.user_id and current_user.user_id != 1:
            return BaseResponse(
                code=403,
                msg="无权限更新此文档",
                data=None
            )
        
        # 转换状态
        doc_status = None
        if request.status:
            try:
                doc_status = DocumentStatus(request.status.upper())
            except ValueError:
                return BaseResponse(
                    code=400,
                    msg=f"无效的文档状态: {request.status}",
                    data=None
                )
        
        # 更新文档
        updated_document = await DocumentService.update_document(
            db=db,
            document_id=document_id,
            title=request.title,
            project_name=request.project_name,
            department_id=request.department_id,
            status=doc_status
        )
        
        return BaseResponse(
            code=200,
            msg="更新成功",
            data=DocumentService.format_document_info(updated_document)
        )
        
    except Exception as e:
        log.error(f"更新文档失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"更新文档失败: {str(e)}",
            data=None
        )


@router.delete("/{document_id}", response_model=BaseResponse)
async def delete_document(
    document_id: int = Path(..., description="文档ID"),
    hard_delete: bool = Query(False, description="是否硬删除"),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """删除文档"""
    try:
        # 检查文档是否存在
        document = await DocumentService.get_document_by_id(db, document_id)
        if not document:
            return BaseResponse(
                code=404,
                msg="文档不存在",
                data=None
            )
        
        # 检查权限（只有文档所有者或管理员可以删除）
        if document.owner_id != current_user.user_id and current_user.user_id != 1:
            return BaseResponse(
                code=403,
                msg="无权限删除此文档",
                data=None
            )
        
        # 删除文档
        success = await DocumentService.delete_document(
            db=db,
            document_id=document_id,
            soft_delete=not hard_delete
        )
        
        if success:
            delete_type = "硬删除" if hard_delete else "软删除"
            return BaseResponse(
                code=200,
                msg=f"文档{delete_type}成功",
                data=None
            )
        else:
            return BaseResponse(
                code=500,
                msg="删除文档失败",
                data=None
            )
        
    except Exception as e:
        log.error(f"删除文档失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"删除文档失败: {str(e)}",
            data=None
        )


@router.get("/my/documents", response_model=BaseResponse)
async def get_my_documents(
    status: Optional[str] = Query(None, description="文档状态"),
    limit: int = Query(20, description="返回数量限制", ge=1, le=100),
    offset: int = Query(0, description="偏移量", ge=0),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户的文档列表"""
    try:
        # 转换状态
        doc_status = None
        if status:
            try:
                doc_status = DocumentStatus(status.upper())
            except ValueError:
                return BaseResponse(
                    code=400,
                    msg=f"无效的文档状态: {status}",
                    data=None
                )
        
        # 获取用户文档
        documents = await DocumentService.get_user_documents(
            db=db,
            user_id=current_user.user_id,
            status=doc_status,
            limit=limit,
            offset=offset
        )
        
        # 格式化响应
        document_list = [DocumentService.format_document_info(doc) for doc in documents]
        
        return BaseResponse(
            code=200,
            msg="获取成功",
            data={
                "documents": document_list,
                "total": len(document_list),
                "limit": limit,
                "offset": offset
            }
        )
        
    except Exception as e:
        log.error(f"获取用户文档列表失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"获取文档列表失败: {str(e)}",
            data=None
        )
