#!/usr/bin/env python3
"""
快速汇编路由
提供基于ES向量化的快速文档汇编接口
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from database import get_db
from middleware.jwt_auth import get_current_user_from_jwt
from auth.models import User
from utils.fast_assembly import FastAssemblyProcessor
from utils.enhanced_qa import EnhancedQA
from utils.logger import log


router = APIRouter(prefix="/api/fast-assembly", tags=["快速汇编"])


class FastAssemblyRequest(BaseModel):
    """快速汇编请求"""
    project_name: str
    action: str  # "项目档案" 或 "文书档案"
    urls: List[str]
    force_refresh: bool = False


class BaseResponse(BaseModel):
    """基础响应"""
    code: int
    msg: str
    data: Optional[Any] = None


class VectorizationRequest(BaseModel):
    """向量化请求"""
    project_name: str
    urls: List[str]
    force_refresh: bool = False


class QARequest(BaseModel):
    """问答请求"""
    question: str
    project_name: Optional[str] = None
    doc_type: Optional[str] = None
    search_strategy: str = "auto"
    include_structured: bool = True


# 全局实例
fast_processor = FastAssemblyProcessor()
enhanced_qa = EnhancedQA()


@router.post("/vectorize", response_model=BaseResponse)
async def vectorize_project(
    request: VectorizationRequest,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """
    项目文档向量化预处理
    
    将项目中的所有文档进行切片、向量化并存入ES，为后续快速检索做准备
    """
    try:
        await fast_processor.init()
        
        log.info(f"用户 {current_user.user_name} 请求向量化项目: {request.project_name}")
        
        # 执行向量化
        stats = await fast_processor.vectorizer.vectorize_project(
            project_name=request.project_name,
            urls=request.urls,
            force_refresh=request.force_refresh
        )
        
        return BaseResponse(
            code=200,
            msg="向量化完成",
            data={
                "project_name": request.project_name,
                "vectorization_stats": stats
            }
        )
        
    except Exception as e:
        log.error(f"向量化失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"向量化失败: {str(e)}",
            data=None
        )


@router.post("/extract", response_model=BaseResponse)
async def fast_extract(
    request: FastAssemblyRequest,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """
    快速结构化信息提取
    
    基于ES向量化数据进行快速的批量结构化信息提取
    """
    try:
        await fast_processor.init()
        
        log.info(f"用户 {current_user.user_name} 请求快速提取: {request.project_name}, 类型: {request.action}")
        
        # 执行快速汇编
        result = await fast_processor.process_assembly_fast(
            project_name=request.project_name,
            action=request.action,
            urls=request.urls,
            force_refresh=request.force_refresh
        )
        
        if result.status == "failed":
            return BaseResponse(
                code=500,
                msg=f"快速提取失败: {result.error_message}",
                data=None
            )
        
        return BaseResponse(
            code=200,
            msg="快速提取完成",
            data={
                "task_id": result.task_id,
                "project_name": result.project_name,
                "action": result.action,
                "extracted_info": result.extracted_info,
                "processing_time": result.processing_time,
                "total_documents": result.total_documents,
                "status": result.status
            }
        )
        
    except Exception as e:
        log.error(f"快速提取失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"快速提取失败: {str(e)}",
            data=None
        )


@router.post("/qa", response_model=BaseResponse)
async def enhanced_question_answer(
    request: QARequest,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """
    增强问答接口
    
    基于ES向量检索和MySQL结构化信息的多角度智能问答
    """
    try:
        await enhanced_qa.init()
        
        log.info(f"用户 {current_user.user_name} 提问: {request.question[:50]}...")
        
        # 执行问答
        response = await enhanced_qa.answer_question(
            question=request.question,
            project_name=request.project_name,
            doc_type=request.doc_type,
            search_strategy=request.search_strategy,
            include_structured=request.include_structured
        )
        
        return BaseResponse(
            code=200,
            msg="问答完成",
            data={
                "question": request.question,
                "answer": response.answer,
                "sources": response.sources,
                "structured_info": response.structured_info,
                "confidence": response.confidence,
                "processing_time": response.processing_time,
                "search_strategy": response.search_strategy
            }
        )
        
    except Exception as e:
        log.error(f"问答失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"问答失败: {str(e)}",
            data=None
        )


@router.get("/project/{project_name}/summary", response_model=BaseResponse)
async def get_project_summary(
    project_name: str,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取项目摘要信息"""
    try:
        await enhanced_qa.init()
        
        summary = await enhanced_qa.get_project_summary(project_name)
        
        return BaseResponse(
            code=200,
            msg="获取项目摘要成功",
            data=summary
        )
        
    except Exception as e:
        log.error(f"获取项目摘要失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"获取项目摘要失败: {str(e)}",
            data=None
        )


@router.get("/project/{project_name}/cache", response_model=BaseResponse)
async def get_cached_result(
    project_name: str,
    action: str = Query(..., description="文档类型：项目档案 或 文书档案"),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取缓存的提取结果"""
    try:
        await fast_processor.init()
        
        cached_result = await fast_processor.get_cached_result(project_name, action)
        
        if cached_result:
            return BaseResponse(
                code=200,
                msg="获取缓存结果成功",
                data=cached_result
            )
        else:
            return BaseResponse(
                code=404,
                msg="未找到缓存结果",
                data=None
            )
        
    except Exception as e:
        log.error(f"获取缓存结果失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"获取缓存结果失败: {str(e)}",
            data=None
        )


@router.delete("/project/{project_name}/cache", response_model=BaseResponse)
async def clear_project_cache(
    project_name: str,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """清除项目缓存"""
    try:
        await fast_processor.init()
        
        success = await fast_processor.clear_project_cache(project_name)
        
        if success:
            return BaseResponse(
                code=200,
                msg="清除项目缓存成功",
                data={"project_name": project_name}
            )
        else:
            return BaseResponse(
                code=500,
                msg="清除项目缓存失败",
                data=None
            )
        
    except Exception as e:
        log.error(f"清除项目缓存失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"清除项目缓存失败: {str(e)}",
            data=None
        )


@router.get("/stats", response_model=BaseResponse)
async def get_processing_stats(
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取处理统计信息"""
    try:
        await fast_processor.init()
        
        stats = await fast_processor.get_processing_stats()
        
        return BaseResponse(
            code=200,
            msg="获取统计信息成功",
            data=stats
        )
        
    except Exception as e:
        log.error(f"获取统计信息失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"获取统计信息失败: {str(e)}",
            data=None
        )


@router.get("/search/projects", response_model=BaseResponse)
async def search_similar_projects(
    query: str = Query(..., description="搜索关键词"),
    doc_type: Optional[str] = Query(None, description="文档类型"),
    limit: int = Query(10, description="返回结果数量", ge=1, le=50),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """搜索相似项目"""
    try:
        await enhanced_qa.init()
        
        projects = await enhanced_qa.search_similar_projects(
            query=query,
            doc_type=doc_type,
            limit=limit
        )
        
        return BaseResponse(
            code=200,
            msg="搜索完成",
            data={
                "query": query,
                "doc_type": doc_type,
                "total": len(projects),
                "projects": projects
            }
        )
        
    except Exception as e:
        log.error(f"搜索相似项目失败: {e}")
        return BaseResponse(
            code=500,
            msg=f"搜索失败: {str(e)}",
            data=None
        )
