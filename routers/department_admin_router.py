"""
部门管理员管理接口
提供部门管理员的查询、设置和管理功能
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload
from typing import List, Optional
from pydantic import BaseModel, Field

from database import get_db
from middleware.jwt_auth import get_current_user_from_jwt
from auth.models import User, Department
from services.document_permission_service import DocumentPermissionService
from utils.log import log

router = APIRouter(prefix="/department-admin", tags=["department-admin"])


# Pydantic 模型
class DepartmentInfo(BaseModel):
    """部门信息模型"""
    dept_id: int
    dept_name: str
    leader: Optional[str] = None
    leader_id: Optional[int] = None
    leader_nickname: Optional[str] = None
    parent_id: Optional[int] = None
    member_count: int = 0


class DepartmentAdminResponse(BaseModel):
    """部门管理员响应模型"""
    code: int
    msg: str
    data: Optional[dict] = None


class SetDepartmentLeaderRequest(BaseModel):
    """设置部门管理员请求模型"""
    dept_id: int = Field(..., description="部门ID")
    leader_user_id: int = Field(..., description="新管理员的用户ID")


@router.get("/departments", response_model=DepartmentAdminResponse)
async def get_departments_with_leaders(
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取所有部门及其管理员信息"""
    try:
        # 检查权限：只有系统管理员可以查看所有部门信息
        if current_user.user_id != 1:
            raise HTTPException(
                status_code=403,
                detail="只有系统管理员可以查看部门管理员信息"
            )
        
        # 查询所有部门信息（不加载关系，避免SQLAlchemy关系问题）
        result = await db.execute(
            select(Department).order_by(Department.dept_id)
        )
        departments = result.scalars().all()
        
        department_list = []
        for dept in departments:
            # 查找部门管理员
            leader_info = None
            if dept.leader_id:
                leader_result = await db.execute(
                    select(User).where(User.user_id == dept.leader_id)
                )
                leader = leader_result.scalar_one_or_none()
                if leader:
                    leader_info = {
                        "leader_id": leader.user_id,
                        "leader_nickname": leader.nick_name,
                        "leader_username": leader.user_name
                    }
            
            # 统计部门成员数量
            member_count_result = await db.execute(
                select(User).where(User.dept_id == dept.dept_id)
            )
            member_count = len(member_count_result.scalars().all())
            
            dept_info = DepartmentInfo(
                dept_id=dept.dept_id,
                dept_name=dept.dept_name,
                leader=leader_info["leader_username"] if leader_info else dept.leader,
                leader_id=leader_info["leader_id"] if leader_info else None,
                leader_nickname=leader_info["leader_nickname"] if leader_info else None,
                parent_id=dept.parent_id,
                member_count=member_count
            )
            department_list.append(dept_info.model_dump())
        
        return DepartmentAdminResponse(
            code=200,
            msg="获取部门信息成功",
            data={"departments": department_list}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"获取部门信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取部门信息失败: {str(e)}"
        )


@router.post("/set-leader", response_model=DepartmentAdminResponse)
async def set_department_leader(
    request: SetDepartmentLeaderRequest,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """设置部门管理员"""
    try:
        # 检查权限：只有系统管理员可以设置部门管理员
        if current_user.user_id != 1:
            raise HTTPException(
                status_code=403,
                detail="只有系统管理员可以设置部门管理员"
            )
        
        # 检查部门是否存在
        dept_result = await db.execute(
            select(Department).where(Department.dept_id == request.dept_id)
        )
        department = dept_result.scalar_one_or_none()
        
        if not department:
            raise HTTPException(
                status_code=404,
                detail="部门不存在"
            )
        
        # 检查用户是否存在
        user_result = await db.execute(
            select(User).where(User.user_id == request.leader_user_id)
        )
        new_leader = user_result.scalar_one_or_none()
        
        if not new_leader:
            raise HTTPException(
                status_code=404,
                detail="用户不存在"
            )
        
        # 检查用户是否属于该部门
        if new_leader.dept_id != request.dept_id:
            raise HTTPException(
                status_code=400,
                detail="用户不属于该部门，无法设置为部门管理员"
            )
        
        # 更新部门管理员
        department.leader = new_leader.user_name  # 保持向后兼容
        department.leader_id = new_leader.user_id  # 使用新的ID字段
        await db.commit()
        
        log.info(f"系统管理员 {current_user.user_name} 设置 {new_leader.user_name} 为部门 {department.dept_name} 的管理员")
        
        return DepartmentAdminResponse(
            code=200,
            msg=f"成功设置 {new_leader.nick_name} 为 {department.dept_name} 的管理员",
            data={
                "dept_id": department.dept_id,
                "dept_name": department.dept_name,
                "leader_id": new_leader.user_id,
                "leader_name": new_leader.user_name,
                "leader_nickname": new_leader.nick_name
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        log.error(f"设置部门管理员失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"设置部门管理员失败: {str(e)}"
        )


@router.get("/my-department", response_model=DepartmentAdminResponse)
async def get_my_department_info(
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户的部门信息和管理员权限"""
    try:
        if not current_user.dept_id:
            return DepartmentAdminResponse(
                code=200,
                msg="用户未分配部门",
                data={"department": None, "is_admin": False}
            )
        
        # 获取部门信息
        dept_result = await db.execute(
            select(Department).where(Department.dept_id == current_user.dept_id)
        )
        department = dept_result.scalar_one_or_none()
        
        if not department:
            return DepartmentAdminResponse(
                code=404,
                msg="部门不存在",
                data=None
            )
        
        # 检查是否为部门管理员
        is_admin = await DocumentPermissionService._is_department_admin(
            current_user, current_user.dept_id
        )
        
        # 如果是部门管理员，获取部门成员列表
        members = []
        if is_admin:
            members_result = await db.execute(
                select(User).where(User.dept_id == current_user.dept_id)
            )
            dept_members = members_result.scalars().all()
            
            for member in dept_members:
                members.append({
                    "user_id": member.user_id,
                    "user_name": member.user_name,
                    "nick_name": member.nick_name,
                    "email": member.email,
                    "is_active": member.is_active
                })
        
        return DepartmentAdminResponse(
            code=200,
            msg="获取部门信息成功",
            data={
                "department": {
                    "dept_id": department.dept_id,
                    "dept_name": department.dept_name,
                    "leader": department.leader,
                    "parent_id": department.parent_id
                },
                "is_admin": is_admin,
                "members": members if is_admin else []
            }
        )
        
    except Exception as e:
        log.error(f"获取部门信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取部门信息失败: {str(e)}"
        )


@router.get("/admin-permissions", response_model=DepartmentAdminResponse)
async def check_admin_permissions(
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """检查当前用户的管理员权限"""
    try:
        permissions = {
            "is_system_admin": current_user.user_id == 1,
            "is_department_admin": False,
            "managed_departments": []
        }
        
        # 检查部门管理员权限
        if current_user.dept_id:
            is_dept_admin = await DocumentPermissionService._is_department_admin(
                current_user, current_user.dept_id
            )
            permissions["is_department_admin"] = is_dept_admin
            
            if is_dept_admin:
                dept_result = await db.execute(
                    select(Department).where(Department.dept_id == current_user.dept_id)
                )
                department = dept_result.scalar_one_or_none()
                if department:
                    permissions["managed_departments"].append({
                        "dept_id": department.dept_id,
                        "dept_name": department.dept_name
                    })
        
        return DepartmentAdminResponse(
            code=200,
            msg="权限检查完成",
            data=permissions
        )
        
    except Exception as e:
        log.error(f"检查管理员权限失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"检查管理员权限失败: {str(e)}"
        )
