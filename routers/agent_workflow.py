#!/usr/bin/env python3
"""
Agent工作流API路由
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from agents.workflow_manager import workflow_monitor, WorkflowFactory
from agents.base_agent import AgentContext
from datetime import datetime
import json

router = APIRouter(prefix="/api/workflow", tags=["Agent工作流"])

class WorkflowCreateRequest(BaseModel):
    """创建工作流请求"""
    task_id: str
    file_url: str
    file_content: Dict[str, Any]
    workflow_type: str = "document_processing"
    metadata: Optional[Dict[str, Any]] = None

class WorkflowStatusResponse(BaseModel):
    """工作流状态响应"""
    workflow_id: str
    status: str
    progress: int
    agents: List[Dict[str, Any]]
    execution_order: List[str]
    results: Dict[str, Any]

@router.post("/create", summary="创建Agent工作流")
async def create_workflow(request: WorkflowCreateRequest):
    """创建并启动Agent工作流"""
    try:
        # 创建工作流
        if request.workflow_type == "document_processing":
            workflow = WorkflowFactory.create_document_workflow()
        else:
            raise HTTPException(status_code=400, detail=f"不支持的工作流类型: {request.workflow_type}")
        
        workflow_id = workflow.workflow.workflow_id
        
        # 注册到监控器
        workflow_monitor.register_workflow(workflow_id, workflow)
        
        # 创建上下文
        context = AgentContext(
            task_id=request.task_id,
            file_url=request.file_url,
            file_content=request.file_content,
            extracted_data={},
            metadata=request.metadata or {},
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 异步执行工作流（这里简化为同步，实际应该异步执行）
        result = await workflow.process_document(
            request.task_id, 
            request.file_url, 
            request.file_content
        )
        
        return {
            "code": 200,
            "msg": "工作流创建成功",
            "data": {
                "workflow_id": workflow_id,
                "status": "created",
                "result": result
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建工作流失败: {str(e)}")

@router.get("/status/{workflow_id}", summary="获取工作流状态")
async def get_workflow_status(workflow_id: str):
    """获取指定工作流的状态"""
    try:
        status = workflow_monitor.get_workflow_status(workflow_id)
        
        if "error" in status:
            raise HTTPException(status_code=404, detail="工作流不存在")
        
        return {
            "code": 200,
            "msg": "获取状态成功",
            "data": status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/list", summary="获取所有工作流")
async def list_workflows():
    """获取所有工作流状态"""
    try:
        workflows = workflow_monitor.get_all_workflows()
        
        return {
            "code": 200,
            "msg": "获取工作流列表成功",
            "data": workflows
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工作流列表失败: {str(e)}")

@router.get("/metrics", summary="获取工作流指标")
async def get_workflow_metrics():
    """获取工作流性能指标"""
    try:
        metrics = workflow_monitor.get_workflow_metrics()
        
        return {
            "code": 200,
            "msg": "获取指标成功",
            "data": metrics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")

@router.delete("/cancel/{workflow_id}", summary="取消工作流")
async def cancel_workflow(workflow_id: str):
    """取消指定的工作流"""
    try:
        # 注销工作流
        workflow_monitor.unregister_workflow(workflow_id)
        
        return {
            "code": 200,
            "msg": "工作流已取消",
            "data": {
                "workflow_id": workflow_id,
                "status": "cancelled"
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消工作流失败: {str(e)}")

@router.get("/agents/types", summary="获取可用的Agent类型")
async def get_agent_types():
    """获取所有可用的Agent类型"""
    agent_types = [
        {
            "type": "init",
            "name": "初始化Agent",
            "description": "准备处理环境",
            "stage": "init",
            "progress_weight": 5
        },
        {
            "type": "download",
            "name": "文件下载Agent",
            "description": "下载文档文件",
            "stage": "download",
            "progress_weight": 10
        },
        {
            "type": "validate",
            "name": "文件验证Agent",
            "description": "验证文件格式和内容",
            "stage": "validate",
            "progress_weight": 5
        },
        {
            "type": "parse",
            "name": "文档解析Agent",
            "description": "解析文档结构",
            "stage": "parse",
            "progress_weight": 10
        },
        {
            "type": "extract_basic",
            "name": "基础信息提取Agent",
            "description": "提取标题、作者、时间等基础信息",
            "stage": "extract_basic",
            "progress_weight": 15
        },
        {
            "type": "extract_structure",
            "name": "结构化信息提取Agent",
            "description": "提取章节、段落等结构信息",
            "stage": "extract_structure",
            "progress_weight": 15
        },
        {
            "type": "extract_entities",
            "name": "实体信息提取Agent",
            "description": "提取人名、地名、机构名等实体",
            "stage": "extract_entities",
            "progress_weight": 20
        },
        {
            "type": "extract_relations",
            "name": "关系信息提取Agent",
            "description": "提取实体间关系和语义关系",
            "stage": "extract_relations",
            "progress_weight": 15
        },
        {
            "type": "merge_info",
            "name": "信息合并Agent",
            "description": "合并和整理提取的信息",
            "stage": "merge_info",
            "progress_weight": 5
        },
        {
            "type": "validate_result",
            "name": "结果验证Agent",
            "description": "验证提取结果的完整性和准确性",
            "stage": "validate_result",
            "progress_weight": 5
        },
        {
            "type": "store",
            "name": "存储结果Agent",
            "description": "保存结构化信息到数据库",
            "stage": "store",
            "progress_weight": 5
        },
        {
            "type": "complete",
            "name": "完成Agent",
            "description": "完成所有处理步骤",
            "stage": "complete",
            "progress_weight": 5
        }
    ]
    
    return {
        "code": 200,
        "msg": "获取Agent类型成功",
        "data": {
            "agent_types": agent_types,
            "total_types": len(agent_types)
        }
    }

@router.post("/custom", summary="创建自定义工作流")
async def create_custom_workflow(agent_configs: List[Dict[str, Any]]):
    """根据配置创建自定义工作流"""
    try:
        # 创建自定义工作流
        workflow = WorkflowFactory.create_custom_workflow(agent_configs)
        workflow_id = workflow.workflow_id
        
        return {
            "code": 200,
            "msg": "自定义工作流创建成功",
            "data": {
                "workflow_id": workflow_id,
                "agent_count": len(agent_configs),
                "workflow_info": workflow.get_workflow_info()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建自定义工作流失败: {str(e)}")

@router.get("/debug/{workflow_id}", summary="获取工作流调试信息")
async def get_workflow_debug_info(workflow_id: str):
    """获取工作流的详细调试信息"""
    try:
        status = workflow_monitor.get_workflow_status(workflow_id)
        
        if "error" in status:
            raise HTTPException(status_code=404, detail="工作流不存在")
        
        # 添加调试信息
        debug_info = {
            "workflow_status": status,
            "agent_details": [],
            "execution_timeline": [],
            "performance_metrics": {}
        }
        
        # 获取Agent详细信息
        for agent_info in status.get("agents", []):
            debug_info["agent_details"].append({
                "agent_id": agent_info["agent_id"],
                "name": agent_info["name"],
                "status": agent_info["status"],
                "dependencies": agent_info["dependencies"],
                "stage": agent_info["stage"],
                "progress": agent_info["progress"]
            })
        
        return {
            "code": 200,
            "msg": "获取调试信息成功",
            "data": debug_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取调试信息失败: {str(e)}")
