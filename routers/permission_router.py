"""
权限管理相关接口
包括权限申请、审批、查询等功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Request, Header
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, text
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, model_validator
from datetime import datetime, timezone

from database import get_db
from middleware.jwt_auth import get_current_user_from_jwt, JWTAuthService, decode_jwt_token
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from auth.models import User, Document, DocumentPermission, PermissionType, PermissionRequest
from services.document_permission_service import DocumentPermissionService
from utils.log import log

router = APIRouter(prefix="/permissions", tags=["permissions"])
security = HTTPBearer()


def normalize_user_roles(user_roles: list) -> dict:
    """
    标准化用户角色处理

    Args:
        user_roles: 用户角色列表

    Returns:
        dict: 包含标准化角色判断结果
        {
            'is_admin': bool,           # 是否为系统管理员
            'is_dept_admin': bool,      # 是否为部门管理员
            'is_user': bool,            # 是否为普通用户（包括其他非管理员角色）
            'effective_role': str       # 有效角色：'admin', 'deptAdmin', 'user'
        }
    """
    if not user_roles:
        user_roles = []

    # 检查具体角色
    is_admin = 'admin' in user_roles
    is_dept_admin = 'deptAdmin' in user_roles

    # 任何非admin/deptAdmin角色的用户都按user处理
    is_user = not (is_admin or is_dept_admin)

    # 确定有效角色
    if is_admin:
        effective_role = 'admin'
    elif is_dept_admin:
        effective_role = 'deptAdmin'
    else:
        effective_role = 'user'  # 默认为普通用户

    return {
        'is_admin': is_admin,
        'is_dept_admin': is_dept_admin,
        'is_user': is_user,
        'effective_role': effective_role,
        'original_roles': user_roles
    }


# Pydantic 模型
class PermissionRequestCreate(BaseModel):
    """权限申请创建模型"""
    document_id: Optional[int] = Field(None, description="文档ID（与document_url、doc_id三选一）")
    document_url: Optional[str] = Field(None, description="文档URL路径（与document_id、doc_id三选一）")
    doc_id: Optional[str] = Field(None, description="文档唯一标识（与document_id、document_url三选一）")
    permission_type: str = Field(..., description="权限类型: read, download")
    reason: str = Field(..., description="申请理由")

    def __init__(self, **data):
        super().__init__(**data)
        # 验证必须提供document_id、document_url或doc_id中的一个
        provided_fields = [self.document_id, self.document_url, self.doc_id]
        provided_count = sum(1 for field in provided_fields if field is not None)

        if provided_count == 0:
            raise ValueError('必须提供document_id、document_url或doc_id中的一个')
        if provided_count > 1:
            raise ValueError('document_id、document_url和doc_id只能提供其中一个')


class PermissionRequestResponse(BaseModel):
    """权限申请响应模型"""
    id: int
    requester_id: int
    requester_name: str
    document_id: int
    document_title: str
    permission_type: str
    reason: str
    status: str
    reviewer_id: Optional[int] = None
    reviewer_name: Optional[str] = None
    review_comment: Optional[str] = None
    reviewed_at: Optional[datetime] = None
    created_at: datetime


class PermissionApprovalRequest(BaseModel):
    """权限审批请求模型"""
    approved: bool = Field(..., description="是否批准")
    comment: str = Field("", description="审批意见")


class DocumentAccessCheckRequest(BaseModel):
    """文档访问权限检查请求模型"""
    document_url: Optional[str] = Field(None, description="文档URL（兼容旧版本）")
    doc_id: Optional[str] = Field(None, description="文档ID（推荐使用）")
    permission_type: str = Field("read", description="权限类型: read, download")

    @model_validator(mode='after')
    def validate_document_identifier(self):
        """验证至少提供一个文档标识符"""
        if not self.doc_id and not self.document_url:
            raise ValueError('必须提供 doc_id 或 document_url 中的至少一个')
        return self


class BaseResponse(BaseModel):
    """基础响应模型"""
    code: int
    msg: str
    data: Optional[dict] = None


class PermissionListResponse(BaseResponse):
    """权限列表响应模型"""
    data: List[PermissionRequestResponse]


class PaginationInfo(BaseModel):
    """分页信息模型"""
    page: int
    size: int
    total: int
    pages: int


class FilterInfo(BaseModel):
    """过滤信息模型"""
    status: Optional[str] = None
    permission_type: Optional[str] = None
    reviewer_id: Optional[int] = None
    requester_name: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None


class AllRecordsData(BaseModel):
    """全部记录数据模型"""
    records: List[dict]  # 使用dict而不是PermissionRequestResponse，因为字段可能不完全匹配
    pagination: PaginationInfo
    filters: FilterInfo


class AllRecordsResponse(BaseResponse):
    """全部记录响应模型"""
    data: AllRecordsData


class DocumentPermissionCheckResponse(BaseModel):
    """文档权限检查响应模型"""
    success: bool
    has_permission: bool
    reason: str
    permission_source: Optional[str] = None  # system_admin, document_owner, department_admin, explicit_grant
    user_info: Optional[Dict[str, Any]] = None
    document_info: Optional[Dict[str, Any]] = None
    suggested_action: Optional[str] = None  # submit_permission_request


class PermissionCheckResponse(BaseResponse):
    """权限检查API响应模型"""
    data: DocumentPermissionCheckResponse


@router.get("/check/{document_id}", response_model=PermissionCheckResponse)
async def check_document_permission(
    document_id: int,
    permission_type: str = Query("read", description="权限类型: read, download"),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """
    检查用户是否能访问指定文档

    权限判断逻辑：
    1. 系统管理员（user_id=1）拥有所有权限
    2. 文档所有者拥有完全权限
    3. 部门管理员（role='admin'）可以访问本部门文档
    4. 其他用户需要通过权限申请获得授权

    Args:
        document_id: 文档ID
        permission_type: 权限类型 (read, download)
        current_user: 当前用户
        db: 数据库会话

    Returns:
        权限检查结果，包含详细的权限信息和建议操作
    """
    try:
        # 验证权限类型
        valid_permission_types = ["read", "download"]
        if permission_type not in valid_permission_types:
            raise HTTPException(
                status_code=400,
                detail="权限类型必须是 'read' 或 'download'"
            )

        # 提前提取user_id，避免在异步操作中访问current_user对象
        user_id = current_user.user_id

        # 调用权限检查服务
        result = await DocumentPermissionService.check_user_document_permission(
            db=db,
            user_id=user_id,
            document_id=document_id,
            permission_type=permission_type
        )

        if not result["success"]:
            return PermissionCheckResponse(
                code=400,
                msg=result["reason"],
                data=DocumentPermissionCheckResponse(**result)
            )

        # 根据权限检查结果返回相应的状态码和消息
        if result["has_permission"]:
            log.info(f"用户 {user_id} 对文档 {document_id} 拥有 {permission_type} 权限，来源: {result['permission_source']}")
            return PermissionCheckResponse(
                code=200,
                msg=f"拥有{permission_type}权限: {result['reason']}",
                data=DocumentPermissionCheckResponse(**result)
            )
        else:
            log.info(f"用户 {user_id} 对文档 {document_id} 无 {permission_type} 权限: {result['reason']}")
            return PermissionCheckResponse(
                code=403,
                msg=f"无{permission_type}权限: {result['reason']}",
                data=DocumentPermissionCheckResponse(**result)
            )

    except HTTPException:
        raise
    except Exception as e:
        log.error(f"检查文档权限失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"检查文档权限失败: {str(e)}"
        )




@router.post("/check-document-access",
    summary="检查文档访问权限",
    description="检查用户对指定文档的访问权限，支持doc_id和document_url两种方式",
    responses={
        200: {
            "description": "权限检查成功",
            "content": {
                "application/json": {
                    "examples": {
                        "has_permission": {
                            "summary": "有权限访问",
                            "value": {
                                "code": 200,
                                "msg": "有权限访问文档",
                                "url": "http://localhost:18888/download?url=/docs/project/requirements.pdf&token=abc123",
                                "read": True,
                                "user_info": {
                                    "user_id": 1,
                                    "user_name": "zhangsan",
                                    "nick_name": "张三",
                                    "dept_id": 1,
                                    "dept_name": "技术部"
                                }
                            }
                        },
                        "no_permission": {
                            "summary": "无权限访问",
                            "value": {
                                "code": 403,
                                "msg": "无权限访问文档，需要申请权限",
                                "url": None,
                                "read": False,
                                "user_info": {
                                    "user_id": 2,
                                    "user_name": "lisi",
                                    "nick_name": "李四",
                                    "dept_id": 2,
                                    "dept_name": "市场部"
                                }
                            }
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 400,
                        "msg": "必须提供 doc_id 或 document_url 中的至少一个",
                        "url": None,
                        "read": False,
                        "user_info": None
                    }
                }
            }
        },
        404: {
            "description": "文档不存在",
            "content": {
                "application/json": {
                    "example": {
                        "code": 404,
                        "msg": "文档不存在",
                        "url": None,
                        "read": False,
                        "user_info": None
                    }
                }
            }
        }
    }
)
async def check_document_access(
    request: DocumentAccessCheckRequest,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db),
    authorization: str = Header(None)
):
    """
    检查用户对文档的访问权限

    **功能说明：**
    - 检查用户是否有权限访问指定文档
    - 支持通过doc_id或document_url两种方式指定文档
    - 返回权限状态和下载链接（如果有权限）
    - 包含用户信息和建议操作

    **权限判断逻辑：**
    1. 系统管理员（user_id=1）拥有所有权限
    2. 文档所有者拥有完全权限
    3. 部门管理员（role='admin'）可以访问本部门文档
    4. 其他用户需要通过权限申请获得授权

    **请求示例：**
    ```bash
    # 使用doc_id检查权限
    curl -X POST "http://localhost:18888/api/permissions/check-document-access" \
         -H "Authorization: Bearer your_jwt_token" \
         -H "Content-Type: application/json" \
         -d '{
           "doc_id": "doc_20250701_083000_abc123",
           "permission_type": "read"
         }'

    # 使用document_url检查权限
    curl -X POST "http://localhost:18888/api/permissions/check-document-access" \
         -H "Authorization: Bearer your_jwt_token" \
         -H "Content-Type: application/json" \
         -d '{
           "document_url": "/docs/project/requirements.pdf",
           "permission_type": "download"
         }'
    ```

    **支持两种方式：**
    - **doc_id**: 文档ID（推荐使用，性能更好）
    - **document_url**: 文档URL（兼容旧版本）
    - **permission_type**: 权限类型 (read, download)

    返回格式：
    {
        "code": 200,
        "msg": "权限检查完成",
        "url": "文档下载URL",
        "permission": true/false,
        "has_permission": true/false,
        "permission_source": "权限来源",
        "user_info": {...},
        "document_info": {...}
    }
    """
    try:
        user_id = current_user.user_id

        # 从JWT token中获取角色信息
        user_roles = None
        if authorization and authorization.startswith("Bearer "):
            try:
                token = authorization.split(" ", 1)[1]
                payload = decode_jwt_token(token)
                if payload:
                    user_roles = payload.get('roles', [])
                    log.info(f"从JWT token获取到用户角色: {user_roles}")
            except Exception as e:
                log.warning(f"获取JWT token角色信息失败: {str(e)}")

        # 调用重构后的权限检查服务
        result = await DocumentPermissionService.check_document_access(
            db=db,
            user_id=user_id,
            doc_id=request.doc_id,
            document_url=request.document_url,
            permission_type=request.permission_type,
            user_roles=user_roles
        )

        # 调试信息
        log.info(f"权限检查结果: success={result.get('success')}")
        log.info(f"has_permission: {result.get('has_permission')}")
        log.info(f"permission_source: {result.get('permission_source')}")
        log.info(f"reason: {result.get('reason')}")
        log.info(f'message: {result.get("message")}')

        # 构建兼容的响应格式
        response_data = {
            "code": 200 if result.get("success", False) else 400,
            "msg": result.get("message", "权限检查完成"),
            "url": result.get("url", request.document_url),
            "permission": result.get("permission", False),
            "has_permission": result.get("has_permission", False),
            "permission_source": result.get("permission_source"),
            "user_info": result.get("user_info"),
            "document_info": result.get("document_info"),
            "doc_id": result.get("doc_id", request.doc_id),
            "error_code": result.get("error_code")
        }

        log.info(f"返回响应: code={response_data['code']}, permission={response_data['permission']},doc_id={response_data['doc_id']}")
        return response_data

    except Exception as e:
        log.error(f"检查文档访问权限失败: {str(e)}")
        return {
            "code": 500,
            "msg": f"检查文档访问权限失败: {str(e)}",
            "url": request.document_url,
            "permission": False,
            "has_permission": False,
            "permission_source": None,
            "user_info": None,
            "document_info": None,
            "doc_id": request.doc_id,
            "error_code": "INTERNAL_ERROR"
        }





@router.post("/request",
    response_model=BaseResponse,
    summary="创建权限申请",
    description="用户申请访问指定文档的权限",
    responses={
        200: {
            "description": "权限申请创建成功",
            "content": {
                "application/json": {
                    "example": {
                        "code": 200,
                        "msg": "权限申请已提交，等待审批",
                        "data": {
                            "request_id": 123,
                            "document_id": 456,
                            "document_title": "项目需求文档",
                            "permission_type": "read",
                            "status": "pending",
                            "reviewer": "系统管理员",
                            "created_at": "2025-07-01T08:30:00Z",
                            "estimated_review_time": "1-2个工作日"
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "examples": {
                        "missing_document": {
                            "summary": "缺少文档标识",
                            "value": {
                                "code": 400,
                                "msg": "必须提供document_id、document_url或doc_id中的一个",
                                "data": None
                            }
                        },
                        "duplicate_request": {
                            "summary": "重复申请",
                            "value": {
                                "code": 400,
                                "msg": "您已经对该文档提交过权限申请，请勿重复申请",
                                "data": None
                            }
                        }
                    }
                }
            }
        },
        403: {
            "description": "权限不足或已有权限",
            "content": {
                "application/json": {
                    "example": {
                        "code": 403,
                        "msg": "您已经拥有该文档的访问权限，无需申请",
                        "data": None
                    }
                }
            }
        },
        404: {
            "description": "文档不存在",
            "content": {
                "application/json": {
                    "example": {
                        "code": 404,
                        "msg": "指定的文档不存在",
                        "data": None
                    }
                }
            }
        }
    }
)
async def create_permission_request(
    request: PermissionRequestCreate,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """
    创建权限申请

    **功能说明：**
    - 用户申请访问指定文档的权限
    - 支持通过document_id、document_url或doc_id指定文档
    - 自动分配合适的审批人（部门管理员或系统管理员）
    - 防止重复申请

    **申请流程：**
    1. 验证文档存在性
    2. 检查用户是否已有权限（避免不必要的申请）
    3. 检查是否存在待处理的申请（防止重复）
    4. 创建权限申请记录
    5. 分配审批人并发送通知

    **审批人分配规则：**
    - 如果文档所有者有部门，则分配给部门管理员
    - 如果文档所有者无部门或部门无管理员，则分配给系统管理员
    - 系统管理员（user_id=1）可以审批所有申请

    **请求示例：**
    ```bash
    # 使用document_id申请
    curl -X POST "http://localhost:18888/api/permissions/request" \
         -H "Authorization: Bearer your_jwt_token" \
         -H "Content-Type: application/json" \
         -d '{
           "document_id": 123,
           "permission_type": "read",
           "reason": "需要查看该文档以完成项目分析工作"
         }'

    # 使用doc_id申请
    curl -X POST "http://localhost:18888/api/permissions/request" \
         -H "Authorization: Bearer your_jwt_token" \
         -H "Content-Type: application/json" \
         -d '{
           "doc_id": "doc_20250701_083000_abc123",
           "permission_type": "download",
           "reason": "需要下载文档进行离线分析"
         }'
    ```

    **权限类型说明：**
    - `read`: 查看权限，可以在线查看文档内容
    - `download`: 下载权限，可以下载文档到本地
    """
    try:
        # 提前提取user_id，避免在后续操作中访问current_user对象
        user_id = current_user.user_id

        # 调试日志：打印接收到的请求数据
        log.info(f"收到权限申请请求，用户ID: {user_id}")
        log.info(f"请求数据: document_id={request.document_id}, document_url={request.document_url}, doc_id={request.doc_id}")
        log.info(f"权限类型: {request.permission_type}, 申请理由: {request.reason}")

        # 根据document_id、document_url或doc_id查找文档（使用原生SQL避免关系加载问题）
        if request.document_id:
            # 使用原生SQL查询文档，避免关系加载
            from sqlalchemy import text
            sql = text("""
                SELECT id, title, file_path, project_name, owner_id, dept_id,
                       doc_id, created_at, updated_at
                FROM documents
                WHERE id = :document_id
            """)

            result = await db.execute(sql, {"document_id": request.document_id})
            row = result.fetchone()

            if row:
                # 手动创建Document对象
                document = Document()
                document.id = row[0]
                document.title = row[1]
                document.file_path = row[2]
                document.project_name = row[3]
                document.owner_id = row[4]
                document.dept_id = row[5]
                document.doc_id = row[6]
                document.created_at = row[7]
                document.updated_at = row[8]
            else:
                document = None
        elif request.document_url:
            # 通过URL查找文档
            document = await DocumentPermissionService._find_document_by_url(db, request.document_url)
        elif request.doc_id:
            # 通过doc_id查找文档
            document = await DocumentPermissionService.find_document_by_doc_id(db, request.doc_id)
        else:
            return BaseResponse(
                code=400,
                msg="必须提供document_id、document_url或doc_id中的一个",
                data=None
            )

        if not document:
            if request.document_id:
                error_msg = f"文档不存在（ID: {request.document_id}）"
            elif request.document_url:
                error_msg = f"文档不存在（URL: {request.document_url}）"
            else:
                error_msg = f"文档不存在（doc_id: {request.doc_id}）"

            return BaseResponse(
                code=404,
                msg=error_msg,
                data={
                    "suggestion": "请检查文档标识是否正确，或联系文档所有者确认文档是否存在"
                }
            )
        
        # 检查用户是否已经有权限
        # 支持字典和对象两种格式
        document_id_value = document.get("id") if isinstance(document, dict) else document.id
        permission_check = await DocumentPermissionService.check_user_document_permission(
            db=db,
            user_id=user_id,
            document_id=document_id_value,
            permission_type=request.permission_type
        )

        if permission_check["success"] and permission_check["has_permission"]:
            return BaseResponse(
                code=400,
                msg="您已经拥有该文档的访问权限",
                data={
                    "permission_source": permission_check.get("permission_source"),
                    "document_info": permission_check.get("document_info")
                }
            )
        
        # 创建权限申请
        # 支持字典和对象两种格式
        document_id_value = document.get("id") if isinstance(document, dict) else document.id
        result = await DocumentPermissionService.create_permission_request(
            db=db,
            requester_id=user_id,
            document_id=document_id_value,
            permission_type=request.permission_type,
            reason=request.reason
        )
        
        if result["success"]:
            # 支持字典和对象两种格式
            document_title = document.get("title") if isinstance(document, dict) else document.title
            log.info(f"用户 {user_id} 申请文档 {document_title} 的 {request.permission_type} 权限")
            return BaseResponse(
                code=200,
                msg=result["message"],
                data={
                    "request_id": result.get("request_id"),
                    "reviewer_id": result.get("reviewer_id"),
                    "reviewer_name": result.get("reviewer_name")
                }
            )
        else:
            return BaseResponse(
                code=400,
                msg=result["message"],
                data=None
            )
            
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"创建权限申请失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"创建权限申请失败: {str(e)}"
        )


@router.get("/requests", response_model=PermissionListResponse)
async def get_permission_requests(
    status_filter: Optional[str] = Query(None, description="状态过滤: pending, approved, rejected"),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取权限申请列表（用户查看自己的申请）"""
    try:
        # 使用原生SQL查询避免关系加载问题
        from sqlalchemy import text
        sql = text("""
            SELECT pr.id, pr.requester_id, pr.document_id, pr.permission_type,
                   pr.reason, pr.status, pr.reviewer_id, pr.review_comment,
                   pr.reviewed_at, pr.created_at,
                   u.nick_name as requester_name,
                   d.title as document_title,
                   r.nick_name as reviewer_name
            FROM permission_requests pr
            LEFT JOIN users u ON pr.requester_id = u.user_id
            LEFT JOIN documents d ON pr.document_id = d.id
            LEFT JOIN users r ON pr.reviewer_id = r.user_id
            WHERE pr.requester_id = :user_id
            {status_condition}
            ORDER BY pr.created_at DESC
        """)

        if status_filter:
            sql = text(sql.text.format(status_condition="AND pr.status = :status"))
            result = await db.execute(sql, {"user_id": current_user.user_id, "status": status_filter})
        else:
            sql = text(sql.text.format(status_condition=""))
            result = await db.execute(sql, {"user_id": current_user.user_id})

        requests = result.fetchall()

        response_data = []
        for req in requests:
            response_data.append(PermissionRequestResponse(
                id=req[0],  # id
                requester_id=req[1],  # requester_id
                requester_name=req[10] or "Unknown",  # requester_name
                document_id=req[2],  # document_id
                document_title=req[11] or "Unknown",  # document_title
                permission_type=req[3],  # permission_type
                reason=req[4],  # reason
                status=req[5],  # status
                reviewer_id=req[6],  # reviewer_id
                reviewer_name=req[12],  # reviewer_name
                review_comment=req[7],  # review_comment
                reviewed_at=req[8],  # reviewed_at
                created_at=req[9]  # created_at
            ))
        
        return PermissionListResponse(
            code=200,
            msg="获取权限申请列表成功",
            data=response_data
        )
        
    except Exception as e:
        log.error(f"获取权限申请列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取权限申请列表失败: {str(e)}"
        )


@router.get("/pending-reviews", response_model=PermissionListResponse)
async def get_pending_reviews(
    request: Request,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取待审批的权限申请（部门管理员查看）"""
    try:
        # 检查用户是否为部门管理员或系统管理员
        # 直接从请求头中获取JWT token并解析roles信息
        from utils.jwt_utils import decode_jwt_token

        # 从请求头获取token
        authorization = request.headers.get("Authorization", "")
        if not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=403,
                detail="缺少有效的认证token"
            )

        token = authorization.split(" ", 1)[1]
        payload = decode_jwt_token(token)

        if not payload:
            raise HTTPException(
                status_code=403,
                detail="无效的认证token"
            )

        # 从token中获取roles信息并标准化处理
        user_roles = payload.get('roles', [])
        role_info = normalize_user_roles(user_roles)

        log.info(f"🔍 权限检查调试信息:")
        log.info(f"  - 用户ID: {current_user.user_id}")
        log.info(f"  - 用户名: {current_user.user_name}")
        log.info(f"  - 部门ID: {current_user.dept_id}")
        log.info(f"  - Token中的roles: {user_roles}")
        log.info(f"  - 标准化角色: {role_info}")
        log.info(f"  - 用户对象的roles: {getattr(current_user, 'roles', 'NOT_SET')}")

        # 使用标准化的角色判断
        is_admin = role_info['is_admin']
        is_dept_admin = role_info['is_dept_admin'] and current_user.dept_id

        log.info(f"🎯 权限判断结果: is_admin={is_admin}, is_dept_admin={is_dept_admin}, effective_role={role_info['effective_role']}")

        if not (is_admin or is_dept_admin):
            raise HTTPException(
                status_code=403,
                detail=f"无权限查看待审批申请。用户有效角色: {role_info['effective_role']}, 需要admin或deptAdmin角色"
            )
        
        # 使用原生SQL查询避免枚举问题
        # 统一逻辑：所有用户只看分配给自己审批的申请
        from sqlalchemy import text
        sql = text("""
            SELECT pr.id, pr.requester_id, pr.document_id, pr.permission_type,
                   pr.reason, pr.status, pr.reviewer_id, pr.review_comment,
                   pr.reviewed_at, pr.created_at,
                   u.nick_name as requester_name,
                   COALESCE(d.title, CONCAT(d.project_name, '/', SUBSTRING_INDEX(d.file_path, '/', -1))) as document_title,
                   r.nick_name as reviewer_name
            FROM permission_requests pr
            LEFT JOIN users u ON pr.requester_id = u.user_id
            LEFT JOIN documents d ON pr.document_id = d.id
            LEFT JOIN users r ON pr.reviewer_id = r.user_id
            WHERE pr.status = 'pending' AND pr.reviewer_id = :reviewer_id
            ORDER BY pr.created_at DESC
        """)

        result = await db.execute(sql, {"reviewer_id": current_user.user_id})

        requests = result.fetchall()
        
        response_data = []
        for req in requests:
            try:
                response_data.append(PermissionRequestResponse(
                    id=req[0],  # id
                    requester_id=req[1],  # requester_id
                    requester_name=req[10] or "Unknown",  # requester_name
                    document_id=req[2],  # document_id
                    document_title=req[11] or "Unknown",  # document_title
                    permission_type=req[3],  # permission_type
                    reason=req[4],  # reason
                    status=req[5],  # status
                    reviewer_id=req[6],  # reviewer_id
                    reviewer_name=req[12],  # reviewer_name
                    review_comment=req[7],  # review_comment
                    reviewed_at=req[8],  # reviewed_at
                    created_at=req[9]  # created_at
                ))
            except Exception as item_error:
                log.error(f"处理权限申请 {req[0]} 时出错: {item_error}")
                continue
        
        return PermissionListResponse(
            code=200,
            msg="获取待审批申请列表成功",
            data=response_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"获取待审批申请列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取待审批申请列表失败: {str(e)}"
        )


@router.get("/my-approved", response_model=PermissionListResponse)
async def get_my_approved_requests(
    request: Request,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户已审批的权限申请记录（系统管理员和部门管理员）"""
    try:
        # 检查用户是否为系统管理员或部门管理员
        # 直接从请求头中获取JWT token并解析roles信息
        from utils.jwt_utils import decode_jwt_token

        # 从请求头获取token
        authorization = request.headers.get("Authorization", "")
        if not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=403,
                detail="缺少有效的认证token"
            )

        token = authorization.split(" ", 1)[1]
        payload = decode_jwt_token(token)

        if not payload:
            raise HTTPException(
                status_code=403,
                detail="无效的认证token"
            )

        # 从token中获取roles信息并标准化处理
        user_roles = payload.get('roles', [])
        role_info = normalize_user_roles(user_roles)

        # 使用标准化的角色判断
        is_system_admin = role_info['is_admin']
        is_dept_admin = role_info['is_dept_admin'] and current_user.dept_id

        log.info(f"🔍 my-approved权限检查: user_id={current_user.user_id}, roles={user_roles}, effective_role={role_info['effective_role']}, is_admin={is_system_admin}, is_dept_admin={is_dept_admin}")


        if not (is_system_admin or is_dept_admin):
            raise HTTPException(
                status_code=403,
                detail=f"只有系统管理员和部门管理员可以查看已审批记录。用户有效角色: {role_info['effective_role']}"
            )

        # 查询当前用户已审批的记录
        from sqlalchemy import text
        sql = text("""
            SELECT pr.id, pr.requester_id, pr.document_id, pr.permission_type,
                   pr.reason, pr.status, pr.reviewer_id, pr.review_comment,
                   pr.reviewed_at, pr.created_at,
                   u.nick_name as requester_name,
                   d.title as document_title,
                   dept.dept_name as requester_dept
            FROM permission_requests pr
            LEFT JOIN users u ON pr.requester_id = u.user_id
            LEFT JOIN documents d ON pr.document_id = d.id
            LEFT JOIN departments dept ON u.dept_id = dept.dept_id
            WHERE pr.reviewer_id = :reviewer_id
            AND pr.status IN ('approved', 'rejected')
            ORDER BY pr.reviewed_at DESC
        """)

        result = await db.execute(sql, {"reviewer_id": current_user.user_id})
        rows = result.fetchall()

        # 转换为字典格式
        requests = []
        for row in rows:
            requests.append({
                "id": row[0],
                "requester_id": row[1],
                "document_id": row[2],
                "permission_type": row[3],
                "reason": row[4],
                "status": row[5],
                "reviewer_id": row[6],
                "review_comment": row[7],
                "reviewed_at": row[8].isoformat() if row[8] else None,
                "created_at": row[9].isoformat() if row[9] else None,
                "requester_name": row[10],
                "document_title": row[11],
                "requester_dept": row[12]
            })

        return PermissionListResponse(
            code=200,
            msg="获取已审批记录成功",
            data=requests
        )

    except HTTPException:
        raise
    except Exception as e:
        log.error(f"获取已审批记录失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取已审批记录失败: {str(e)}"
        )


@router.get("/all-records", response_model=AllRecordsResponse)
async def get_all_permission_records(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态过滤: pending, approved, rejected, withdrawn"),
    permission_type: Optional[str] = Query(None, description="权限类型过滤: read, download"),
    reviewer_id: Optional[int] = Query(None, description="审批人ID过滤"),
    requester_name: Optional[str] = Query(None, description="申请人姓名过滤"),
    start_date: Optional[str] = Query(None, description="开始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """
    获取全部权限申请记录（系统管理员专用）

    支持功能：
    - 分页查询
    - 状态过滤 (pending, approved, rejected, withdrawn)
    - 权限类型过滤 (read, download)
    - 审批人过滤
    - 申请人姓名模糊搜索
    - 申请时间范围过滤
    """
    try:
        # 检查用户是否为系统管理员
        from utils.jwt_utils import decode_jwt_token

        # 从请求头获取token
        authorization = request.headers.get("Authorization", "")
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=403, detail="缺少有效的认证token")

        token = authorization.split(" ", 1)[1]
        payload = decode_jwt_token(token)

        if not payload:
            raise HTTPException(status_code=403, detail="无效的认证token")

        # 从token中获取roles信息并标准化处理
        user_roles = payload.get('roles', [])
        role_info = normalize_user_roles(user_roles)
        is_admin = role_info['is_admin']

        if not is_admin:
            raise HTTPException(
                status_code=403,
                detail=f"只有系统管理员可以查看全部记录。用户有效角色: {role_info['effective_role']}"
            )

        # 构建动态查询条件
        from sqlalchemy import text

        # 基础查询
        base_sql = """
            SELECT pr.id, pr.requester_id, pr.document_id, pr.permission_type,
                   pr.reason, pr.status, pr.reviewer_id, pr.review_comment,
                   pr.reviewed_at, pr.created_at,
                   u.nick_name as requester_name,
                   COALESCE(d.title, CONCAT(d.project_name, '/', SUBSTRING_INDEX(d.file_path, '/', -1))) as document_title,
                   dept.dept_name as requester_dept,
                   r.nick_name as reviewer_name
            FROM permission_requests pr
            LEFT JOIN users u ON pr.requester_id = u.user_id
            LEFT JOIN documents d ON pr.document_id = d.id
            LEFT JOIN departments dept ON u.dept_id = dept.dept_id
            LEFT JOIN users r ON pr.reviewer_id = r.user_id
        """

        # 构建WHERE条件
        where_conditions = []
        params = {}

        if status:
            where_conditions.append("pr.status = :status")
            params["status"] = status

        if permission_type:
            where_conditions.append("pr.permission_type = :permission_type")
            params["permission_type"] = permission_type

        if reviewer_id:
            where_conditions.append("pr.reviewer_id = :reviewer_id")
            params["reviewer_id"] = reviewer_id

        if requester_name:
            where_conditions.append("u.nick_name LIKE :requester_name")
            params["requester_name"] = f"%{requester_name}%"

        if start_date:
            where_conditions.append("DATE(pr.created_at) >= :start_date")
            params["start_date"] = start_date

        if end_date:
            where_conditions.append("DATE(pr.created_at) <= :end_date")
            params["end_date"] = end_date

        # 组装WHERE子句
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        # 计算总数的查询
        count_sql = f"""
            SELECT COUNT(*) as total
            FROM permission_requests pr
            LEFT JOIN users u ON pr.requester_id = u.user_id
            LEFT JOIN documents d ON pr.document_id = d.id
            LEFT JOIN departments dept ON u.dept_id = dept.dept_id
            LEFT JOIN users r ON pr.reviewer_id = r.user_id
            {where_clause}
        """

        # 分页查询
        offset = (page - 1) * size
        data_sql = f"""
            {base_sql}
            {where_clause}
            ORDER BY
                CASE WHEN pr.status = 'pending' THEN 0 ELSE 1 END,
                pr.created_at DESC
            LIMIT :limit OFFSET :offset
        """

        params.update({"limit": size, "offset": offset})

        # 执行查询
        count_result = await db.execute(text(count_sql), params)
        total = count_result.scalar()

        data_result = await db.execute(text(data_sql), params)
        rows = data_result.fetchall()

        log.info(f"全部记录查询: page={page}, size={size}, total={total}, filters={params}")

        # 转换为字典格式
        requests = []
        for row in rows:
            requests.append({
                "id": row[0],
                "requester_id": row[1],
                "document_id": row[2],
                "permission_type": row[3],
                "reason": row[4],
                "status": row[5],
                "reviewer_id": row[6],
                "review_comment": row[7],
                "reviewed_at": row[8].isoformat() if row[8] else None,
                "created_at": row[9].isoformat() if row[9] else None,
                "requester_name": row[10],
                "document_title": row[11],
                "requester_dept": row[12],
                "reviewer_name": row[13]
            })

        # 构建分页响应
        return AllRecordsResponse(
            code=200,
            msg=f"获取全部记录成功，共{total}条记录",
            data=AllRecordsData(
                records=requests,
                pagination=PaginationInfo(
                    page=page,
                    size=size,
                    total=total,
                    pages=(total + size - 1) // size
                ),
                filters=FilterInfo(
                    status=status,
                    permission_type=permission_type,
                    reviewer_id=reviewer_id,
                    requester_name=requester_name,
                    start_date=start_date,
                    end_date=end_date
                )
            )
        )

    except HTTPException:
        raise
    except Exception as e:
        log.error(f"获取全部记录失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取全部记录失败: {str(e)}"
        )


@router.get("/department-stats")
async def get_department_stats(
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取部门权限申请统计数据（系统管理员专用）"""
    try:
        # 检查用户是否为系统管理员
        if current_user.user_id != 1:
            raise HTTPException(
                status_code=403,
                detail="只有系统管理员可以查看部门统计"
            )

        # 查询部门统计数据
        from sqlalchemy import text
        sql = text("""
            SELECT
                dept.dept_id,
                dept.dept_name,
                COUNT(CASE WHEN pr.status = 'pending' THEN 1 END) as pending_count,
                COUNT(CASE WHEN pr.status = 'approved' THEN 1 END) as approved_count,
                COUNT(CASE WHEN pr.status = 'rejected' THEN 1 END) as rejected_count,
                COUNT(pr.id) as total_count
            FROM departments dept
            LEFT JOIN users u ON dept.dept_id = u.dept_id
            LEFT JOIN permission_requests pr ON u.user_id = pr.requester_id
            GROUP BY dept.dept_id, dept.dept_name
            ORDER BY dept.dept_name
        """)

        result = await db.execute(sql)
        rows = result.fetchall()

        # 转换为字典格式
        stats = []
        for row in rows:
            stats.append({
                "dept_id": row[0],
                "dept_name": row[1],
                "pending_count": row[2] or 0,
                "approved_count": row[3] or 0,
                "rejected_count": row[4] or 0,
                "total_count": row[5] or 0
            })

        return BaseResponse(
            code=200,
            msg="获取部门统计成功",
            data={"departments": stats}
        )

    except HTTPException:
        raise
    except Exception as e:
        log.error(f"获取部门统计失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取部门统计失败: {str(e)}"
        )


@router.post("/approve/{request_id}",
    response_model=BaseResponse,
    summary="审批权限申请",
    description="部门管理员或系统管理员审批用户的权限申请",
    responses={
        200: {
            "description": "审批成功",
            "content": {
                "application/json": {
                    "examples": {
                        "approved": {
                            "summary": "批准申请",
                            "value": {
                                "code": 200,
                                "msg": "权限申请已批准",
                                "data": {
                                    "request_id": 123,
                                    "status": "approved",
                                    "reviewer_name": "张三",
                                    "review_comment": "申请理由充分，批准访问",
                                    "reviewed_at": "2025-07-01T08:30:00Z",
                                    "permission_granted": {
                                        "document_id": 456,
                                        "document_title": "项目需求文档",
                                        "permission_type": "read",
                                        "valid_until": "2025-12-31T23:59:59Z"
                                    }
                                }
                            }
                        },
                        "rejected": {
                            "summary": "拒绝申请",
                            "value": {
                                "code": 200,
                                "msg": "权限申请已拒绝",
                                "data": {
                                    "request_id": 123,
                                    "status": "rejected",
                                    "reviewer_name": "张三",
                                    "review_comment": "申请理由不充分，暂不批准",
                                    "reviewed_at": "2025-07-01T08:30:00Z"
                                }
                            }
                        }
                    }
                }
            }
        },
        400: {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 400,
                        "msg": "权限申请已处理",
                        "data": None
                    }
                }
            }
        },
        403: {
            "description": "权限不足",
            "content": {
                "application/json": {
                    "example": {
                        "code": 403,
                        "msg": "无权限审批此申请",
                        "data": None
                    }
                }
            }
        },
        404: {
            "description": "申请不存在",
            "content": {
                "application/json": {
                    "example": {
                        "code": 404,
                        "msg": "权限申请不存在",
                        "data": None
                    }
                }
            }
        }
    }
)
async def approve_permission_request(
    request_id: int,
    approval: PermissionApprovalRequest,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    审批权限申请

    **功能说明：**
    - 部门管理员或系统管理员审批用户的权限申请
    - 支持批准或拒绝申请
    - 可以添加审批意见
    - 自动创建权限记录（如果批准）

    **审批权限：**
    - 系统管理员（user_id=1）可以审批所有申请
    - 部门管理员可以审批本部门文档的权限申请
    - 其他用户无审批权限

    **审批流程：**
    1. 验证申请存在且状态为待审批
    2. 检查当前用户的审批权限
    3. 更新申请状态和审批信息
    4. 如果批准，创建对应的权限记录
    5. 发送通知给申请人

    **请求示例：**
    ```bash
    # 批准申请
    curl -X POST "http://localhost:18888/api/permissions/approve/123" \
         -H "Authorization: Bearer your_jwt_token" \
         -H "Content-Type: application/json" \
         -d '{
           "approved": true,
           "comment": "申请理由充分，批准访问"
         }'

    # 拒绝申请
    curl -X POST "http://localhost:18888/api/permissions/approve/123" \
         -H "Authorization: Bearer your_jwt_token" \
         -H "Content-Type: application/json" \
         -d '{
           "approved": false,
           "comment": "申请理由不充分，建议补充说明后重新申请"
         }'
    ```

    **注意事项：**
    - 只能审批状态为"pending"的申请
    - 审批后状态不可逆转
    - 审批意见会发送给申请人
    """
    try:
        # 完全避免ORM，直接从JWT token获取用户信息
        from sqlalchemy import text
        from utils.jwt_utils import decode_jwt_token

        # 从请求头获取JWT token
        authorization = request.headers.get("Authorization")
        if not authorization or not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=401,
                detail="缺少认证token"
            )

        token = authorization.split(" ", 1)[1]

        # 解码JWT token获取用户信息
        payload = decode_jwt_token(token)
        if not payload:
            raise HTTPException(
                status_code=401,
                detail="无效的认证token"
            )

        reviewer_user_id = payload.get("user_id")
        if not reviewer_user_id:
            raise HTTPException(
                status_code=401,
                detail="Token中缺少用户ID"
            )

        log.info(f"收到权限审批请求: request_id={request_id}, approved={approval.approved}, user_id={reviewer_user_id}")

        # 检查权限申请是否存在 - 使用原生SQL避免关系加载
        sql = text("""
            SELECT pr.id, pr.requester_id, pr.document_id, pr.status,
                   d.department_id
            FROM permission_requests pr
            LEFT JOIN documents d ON pr.document_id = d.id
            WHERE pr.id = :request_id
        """)
        result = await db.execute(sql, {"request_id": request_id})
        req_data = result.fetchone()

        if not req_data:
            raise HTTPException(
                status_code=404,
                detail="权限申请不存在"
            )

        if req_data[3] != "pending":  # status字段是第4个（索引3）
            raise HTTPException(
                status_code=400,
                detail="权限申请已处理"
            )

        # 检查审批权限 - 使用JWT token中的角色信息
        # 从JWT token payload中获取用户信息
        user_dept_id = payload.get("dept_id")
        user_roles = payload.get("roles", [])
        role_info = normalize_user_roles(user_roles)

        # 使用标准化的角色判断
        is_admin = role_info['is_admin']
        is_dept_admin = role_info['is_dept_admin'] and user_dept_id

        log.info(f"🔍 审批权限检查: user_id={reviewer_user_id}, roles={user_roles}, effective_role={role_info['effective_role']}, is_admin={is_admin}, is_dept_admin={is_dept_admin}")


        if user_dept_id:
            document_dept_id = req_data[4]  # department_id字段（索引4）

            if document_dept_id == user_dept_id:
                # 创建临时用户字典用于权限检查
                user = {
                    'user_id': reviewer_user_id,
                    'dept_id': user_dept_id,
                    'roles': user_roles
                }
                is_dept_admin = await DocumentPermissionService._is_department_admin(
                    user, user_dept_id
                )

        if not (is_admin or is_dept_admin):
            raise HTTPException(
                status_code=403,
                detail=f"无权限审批此申请。用户有效角色: {role_info['effective_role']}"
            )

        # 执行审批
        result = await DocumentPermissionService.approve_permission_request(
            db=db,
            request_id=request_id,
            reviewer_id=reviewer_user_id,
            approved=approval.approved,
            comment=approval.comment
        )

        if result["success"]:
            action = "批准" if approval.approved else "拒绝"
            log.info(f"用户 {reviewer_user_id} {action}了权限申请 {request_id}")
            return BaseResponse(
                code=200,
                msg=result["message"],
                data={"approved": result["approved"]}
            )
        else:
            return BaseResponse(
                code=400,
                msg=result["message"],
                data=None
            )

    except HTTPException:
        raise
    except Exception as e:
        error_msg = str(e).replace("{", "{{").replace("}", "}}")
        log.error(f"审批权限申请异常: request_id={request_id}, error={error_msg}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"审批权限申请失败: {error_msg}"
        )


@router.post("/requests/{request_id}/withdraw", response_model=BaseResponse)
async def withdraw_permission_request(
    request_id: int,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """撤销权限申请"""
    try:
        # 检查权限申请是否存在 - 使用原生SQL避免对象加载
        from sqlalchemy import text
        check_sql = text("""
            SELECT id, requester_id, status
            FROM permission_requests
            WHERE id = :request_id
        """)
        result = await db.execute(check_sql, {"request_id": request_id})
        req_data = result.fetchone()

        if not req_data:
            raise HTTPException(
                status_code=404,
                detail="权限申请不存在"
            )

        # 检查是否是申请人本人
        if req_data[1] != current_user.user_id:  # requester_id
            raise HTTPException(
                status_code=403,
                detail="只能撤销自己的权限申请"
            )

        # 检查申请状态
        if req_data[2] != "pending":  # status
            raise HTTPException(
                status_code=400,
                detail="只能撤销待审批的申请"
            )

        # 更新申请状态为已撤销 - 使用原生SQL更新
        update_sql = text("""
            UPDATE permission_requests
            SET status = 'withdrawn', updated_at = NOW()
            WHERE id = :request_id
        """)
        await db.execute(update_sql, {"request_id": request_id})
        await db.commit()

        log.info(f"用户 {current_user.user_id} 撤销了权限申请 {request_id}")

        return BaseResponse(
            code=200,
            msg="权限申请已撤销",
            data={"request_id": request_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        log.error(f"撤销权限申请失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"撤销权限申请失败: {str(e)}"
        )


@router.delete("/requests/{request_id}", response_model=BaseResponse)
async def delete_permission_request(
    request_id: int,
    request: Request,
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """删除权限申请 - 优化版本，避免greenlet问题"""
    try:
        # 检查权限申请是否存在 - 使用原生SQL避免对象加载
        from sqlalchemy import text
        check_sql = text("""
            SELECT pr.id, pr.requester_id, pr.status, pr.document_id,
                   u.dept_id as requester_dept_id, d.owner_id as document_owner_id,
                   du.dept_id as document_owner_dept_id
            FROM permission_requests pr
            LEFT JOIN users u ON pr.requester_id = u.user_id
            LEFT JOIN documents d ON pr.document_id = d.id
            LEFT JOIN users du ON d.owner_id = du.user_id
            WHERE pr.id = :request_id
        """)
        result = await db.execute(check_sql, {"request_id": request_id})
        req_data = result.fetchone()

        if not req_data:
            raise HTTPException(
                status_code=404,
                detail="权限申请不存在"
            )

        # 提取申请信息
        request_id_db = req_data[0]
        requester_id = req_data[1]
        status = req_data[2]
        document_id = req_data[3]
        requester_dept_id = req_data[4]
        document_owner_id = req_data[5]
        document_owner_dept_id = req_data[6]

        # 获取当前用户角色信息 - 从JWT token中获取
        from utils.jwt_utils import decode_jwt_token

        # 从请求头获取token
        authorization = request.headers.get("Authorization", "")
        if not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=403,
                detail="缺少有效的认证token"
            )

        token = authorization.split(" ", 1)[1]
        payload = decode_jwt_token(token)

        if not payload:
            raise HTTPException(
                status_code=403,
                detail="无效的认证token"
            )

        # 从token中获取roles信息并标准化处理
        user_roles = payload.get('roles', [])
        role_info = normalize_user_roles(user_roles)

        # 使用标准化的角色判断
        is_system_admin = role_info['is_admin']
        current_user_dept_id = current_user.dept_id
        is_dept_admin = role_info['is_dept_admin'] and current_user_dept_id

        log.info(f"🔍 删除权限检查: user_id={current_user.user_id}, roles={user_roles}, effective_role={role_info['effective_role']}, is_admin={is_system_admin}, is_dept_admin={is_dept_admin}")


        # 权限检查逻辑
        can_delete = False
        delete_reason = ""
        is_own_request = (requester_id == current_user.user_id)

        if is_system_admin:
            # 系统管理员可以删除所有部门已审核的记录（包括已撤销的申请）
            if status in ['approved', 'rejected', 'withdrawn']:
                can_delete = True
                delete_reason = f"系统管理员删除已处理权限申请 {request_id}，状态: {status}"
            elif is_own_request:
                # 系统管理员也可以删除自己的任何申请
                can_delete = True
                delete_reason = f"系统管理员删除自己的权限申请 {request_id}，状态: {status}"
            else:
                raise HTTPException(
                    status_code=403,
                    detail="系统管理员只能删除已处理的申请（已审核/已撤销）或自己的申请"
                )

        elif is_dept_admin:
            # 部门管理员的删除权限
            is_same_dept = (current_user_dept_id == requester_dept_id)

            if is_own_request:
                # 部门管理员可以删除自己的任何申请
                can_delete = True
                delete_reason = f"部门管理员删除自己的权限申请 {request_id}，状态: {status}"
            elif status == 'withdrawn':
                # 已撤销的申请：部门管理员可以删除本部门的
                if is_same_dept:
                    can_delete = True
                    delete_reason = f"部门管理员删除本部门已撤销权限申请 {request_id}"
                else:
                    raise HTTPException(
                        status_code=403,
                        detail="部门管理员只能删除本部门的权限申请"
                    )
            elif status in ['approved', 'rejected']:
                # 已审核的申请：部门管理员可以删除本部门的
                if is_same_dept:
                    can_delete = True
                    delete_reason = f"部门管理员删除本部门已审核权限申请 {request_id}，状态: {status}"
                else:
                    raise HTTPException(
                        status_code=403,
                        detail="部门管理员只能删除本部门的权限申请"
                    )
            else:
                # 待审批的申请：部门管理员不能删除（除非是自己的）
                raise HTTPException(
                    status_code=403,
                    detail="部门管理员只能删除已处理的申请（已审核/已撤销）或自己的申请"
                )

        else:
            # 普通用户（包括其他非管理员角色）的删除权限
            if status == 'withdrawn':
                # 已撤销的申请：用户自己可以删除
                if is_own_request:
                    can_delete = True
                    delete_reason = f"用户删除自己已撤销的权限申请 {request_id}"
                else:
                    raise HTTPException(
                        status_code=403,
                        detail="只能删除自己的权限申请"
                    )
            elif status == 'pending':
                # 待审批的申请：只有用户自己可以删除
                if is_own_request:
                    can_delete = True
                    delete_reason = f"用户删除自己的待审批权限申请 {request_id}"
                else:
                    raise HTTPException(
                        status_code=403,
                        detail="只能删除自己的权限申请"
                    )
            else:
                # 已审批的申请：普通用户（包括其他非管理员角色）不能删除
                if not is_own_request:
                    raise HTTPException(
                        status_code=403,
                        detail="只能删除自己的权限申请"
                    )

                if status == 'approved':
                    raise HTTPException(
                        status_code=400,
                        detail="已批准的申请不能删除。如需撤销权限，请联系部门管理员。"
                    )
                elif status == 'rejected':
                    raise HTTPException(
                        status_code=400,
                        detail="已拒绝的申请不能删除。如需清理记录，请联系部门管理员。"
                    )
                else:
                    raise HTTPException(
                        status_code=400,
                        detail=f"状态为 '{status}' 的申请不能删除"
                    )

        # 记录删除操作日志
        if can_delete:
            print(f"🗑️ {delete_reason}")

        # 删除申请记录 - 根据权限使用不同的删除条件
        if is_system_admin or (is_dept_admin and not is_own_request):
            # 管理员删除其他人的申请，只需要检查申请ID
            delete_sql = text("""
                DELETE FROM permission_requests
                WHERE id = :request_id
            """)
            delete_result = await db.execute(delete_sql, {
                "request_id": request_id
            })
        else:
            # 普通用户（包括其他非管理员角色）或管理员删除自己的申请，需要检查申请人ID
            delete_sql = text("""
                DELETE FROM permission_requests
                WHERE id = :request_id AND requester_id = :user_id
            """)
            delete_result = await db.execute(delete_sql, {
                "request_id": request_id,
                "user_id": current_user.user_id
            })

        # 检查是否真的删除了记录
        if delete_result.rowcount == 0:
            raise HTTPException(
                status_code=404,
                detail="权限申请不存在或无权删除"
            )

        log.info(f"用户 {current_user.user_id} 删除了权限申请 {request_id}")

        return BaseResponse(
            code=200,
            msg="权限申请已删除",
            data={"request_id": request_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        log.error(f"删除权限申请失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"删除权限申请失败: {str(e)}"
        )


@router.get("/my-documents", response_model=BaseResponse)
async def get_my_accessible_documents(
    permission_type: str = Query("read", description="权限类型: read, download"),
    current_user: User = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """获取用户可访问的文档列表"""
    try:
        # 提前提取user_id，避免在异步操作中访问current_user对象
        user_id = current_user.user_id

        documents = await DocumentPermissionService.get_user_accessible_documents(
            db=db,
            user_id=user_id,
            permission_type=permission_type
        )

        return BaseResponse(
            code=200,
            msg="获取可访问文档列表成功",
            data={"documents": documents}
        )

    except Exception as e:
        log.error(f"获取可访问文档列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取可访问文档列表失败: {str(e)}"
        )


class RevokePermissionRequest(BaseModel):
    """撤销权限请求模型"""
    user_id: int = Field(..., description="用户ID")
    document_id: int = Field(..., description="文档ID")
    permission_type: str = Field(..., description="权限类型：read或download")
    granted_by: int = Field(..., description="授权人ID")


@router.delete("/revoke", response_model=BaseResponse)
async def revoke_permission(
    request: RevokePermissionRequest,
    current_user = Depends(get_current_user_from_jwt),
    db: AsyncSession = Depends(get_db)
):
    """
    撤销权限

    只有系统管理员可以撤销权限
    使用联合键（user_id，document_id，permission_type，granted_by）删除document_permissions表中的记录
    """
    try:
        # 检查是否为系统管理员（user_id=1）
        if current_user.user_id != 1:
            # 如果不是系统管理员，尝试从roles中检查
            user_roles = getattr(current_user, 'roles', [])
            role_info = normalize_user_roles(user_roles)

            if not role_info['is_admin']:
                raise HTTPException(
                    status_code=403,
                    detail=f"只有系统管理员可以撤销权限。当前用户ID: {current_user.user_id}, 角色: {role_info['effective_role']}"
                )

        log.info(f"🔍 撤销权限请求: user_id={request.user_id}, document_id={request.document_id}, permission_type={request.permission_type}, granted_by={request.granted_by}")

        # 验证权限类型
        if request.permission_type not in ['read', 'download']:
            raise HTTPException(
                status_code=400,
                detail="权限类型必须是 'read' 或 'download'"
            )

        # 查找要撤销的权限记录
        permission_query = select(DocumentPermission).where(
            and_(
                DocumentPermission.user_id == request.user_id,
                DocumentPermission.document_id == request.document_id,
                DocumentPermission.permission_type == request.permission_type,
                DocumentPermission.granted_by == request.granted_by
            )
        )

        result = await db.execute(permission_query)
        permission_record = result.scalar_one_or_none()

        if not permission_record:
            raise HTTPException(
                status_code=404,
                detail="未找到匹配的权限记录"
            )

        # 获取用户和文档信息用于日志
        user_query = select(User.user_name, User.nick_name).where(User.user_id == request.user_id)
        user_result = await db.execute(user_query)
        user_info = user_result.fetchone()

        document_query = select(Document.title).where(Document.id == request.document_id)
        doc_result = await db.execute(document_query)
        doc_info = doc_result.fetchone()

        user_name = user_info[0] if user_info else f"用户{request.user_id}"
        user_nick_name = user_info[1] if user_info else ""
        doc_title = doc_info[0] if doc_info else f"文档{request.document_id}"

        # 删除权限记录
        await db.delete(permission_record)

        # 查找并更新对应的权限申请记录状态为拒绝
        # 查找对应的权限申请记录
        request_query = select(PermissionRequest).where(
            and_(
                PermissionRequest.requester_id == request.user_id,
                PermissionRequest.document_id == request.document_id,
                PermissionRequest.permission_type == request.permission_type,
                PermissionRequest.status == 'approved'  # 只更新已批准的申请
            )
        )

        request_result = await db.execute(request_query)
        permission_request = request_result.scalar_one_or_none()

        if permission_request:
            # 更新申请状态为拒绝，并添加撤销说明
            current_comment = permission_request.review_comment or ""
            revoke_comment = f"已撤销 by {current_user.user_name}"

            # 如果原来有评论，在前面添加撤销信息
            if current_comment.strip():
                new_comment = f"{revoke_comment}；{current_comment}"
            else:
                new_comment = revoke_comment

            permission_request.status = 'rejected'
            permission_request.review_comment = new_comment
            permission_request.reviewed_at = datetime.now(timezone.utc)
            permission_request.reviewer_id = current_user.user_id

            log.info(f"📝 同时更新权限申请记录状态: 申请ID={permission_request.id}, 新状态=rejected, 评论='{new_comment}'")
        else:
            log.warning(f"⚠️ 未找到对应的权限申请记录进行状态更新")

        await db.commit()

        log.info(f"✅ 权限撤销成功: 用户 {user_name}({user_nick_name}) 对文档 '{doc_title}' 的 {request.permission_type} 权限已被撤销")

        return BaseResponse(
            code=200,
            msg="权限撤销成功",
            data={
                "user_id": request.user_id,
                "user_name": user_name,
                "user_nick_name": user_nick_name,
                "document_id": request.document_id,
                "document_title": doc_title,
                "permission_type": request.permission_type,
                "granted_by": request.granted_by,
                "revoked_by": current_user.user_id,
                "revoked_at": datetime.now(timezone.utc).isoformat()
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        log.error(f"撤销权限失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"撤销权限失败: {str(e)}"
        )
