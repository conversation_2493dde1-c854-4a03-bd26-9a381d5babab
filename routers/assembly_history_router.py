"""
汇编对话历史管理接口
提供汇编对话的存储、查询、删除等功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from typing import Optional
from datetime import datetime
import uuid

from database import get_db
from middleware.jwt_auth import get_current_user_from_jwt
from auth.models import AssemblyConversation, AssemblyType, AssemblyStatus, User
from auth.schemas import (
    AssemblyConversationCreate,
    AssemblyConversationUpdate
)
from utils.log import log

router = APIRouter(prefix="/assembly-history", tags=["assembly-history"])

class BaseResponse:
    """基础响应模型"""
    def __init__(self, code: int = 200, message: str = "success", data=None):
        self.code = code
        self.message = message
        self.data = data

@router.post("/conversations", response_model=dict)
async def create_assembly_conversation(
    conversation: AssemblyConversationCreate,
    request: Request,
    db: AsyncSession = Depends(get_db)
    # 临时注释掉JWT认证以解决greenlet问题
    # current_user: User = Depends(get_current_user_from_jwt)
):
    """创建汇编对话记录"""
    try:
        # 从JWT token中提取用户ID，避免完整的用户查询
        user_id = None
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            try:
                from middleware.jwt_auth import JWTAuthService
                payload = JWTAuthService.decode_token(token)
                if payload:
                    user_id = payload.get("user_id")
                    if isinstance(user_id, str):
                        user_id = int(user_id)
            except Exception as e:
                log.warning(f"解析JWT token失败: {e}")

        # 如果无法从token获取用户ID，使用默认值
        if not user_id:
            user_id = 10  # 默认dev001用户ID

        # 使用枚举对象
        log.debug(f"原始 assembly_type: {conversation.assembly_type}")
        log.debug(f"原始 status: {conversation.status}")

        # 验证并转换为枚举对象
        assembly_type_str = conversation.assembly_type.lower()
        if assembly_type_str == 'custom':
            assembly_type_enum = AssemblyType.custom
        elif assembly_type_str == 'template':
            assembly_type_enum = AssemblyType.template
        else:
            log.warning(f"无效的 assembly_type: {assembly_type_str}, 使用默认值 'custom'")
            assembly_type_enum = AssemblyType.custom

        status_str = conversation.status.lower()
        if status_str == 'pending':
            status_enum = AssemblyStatus.pending
        elif status_str == 'completed':
            status_enum = AssemblyStatus.completed
        elif status_str == 'failed':
            status_enum = AssemblyStatus.failed
        else:
            log.warning(f"无效的 status: {status_str}, 使用默认值 'completed'")
            status_enum = AssemblyStatus.completed

        log.debug(f"转换后 assembly_type: {assembly_type_enum}")
        log.debug(f"转换后 status: {status_enum}")

        # 使用原生SQL创建对话记录，避免SQLAlchemy关系问题
        from sqlalchemy import text
        import json
        from datetime import datetime

        # 准备数据
        now = datetime.now()

        # 将字典转换为JSON字符串
        request_data_json = json.dumps(conversation.request_data) if conversation.request_data else None
        response_data_json = json.dumps(conversation.response_data) if conversation.response_data else None
        echart_data_json = json.dumps(conversation.echart_data) if conversation.echart_data else None

        # 插入SQL
        insert_sql = text("""
            INSERT INTO assembly_conversations
            (user_id, session_id, assembly_type, request_data, response_data,
             echart_data, image_data, status, created_at, updated_at)
            VALUES
            (:user_id, :session_id, :assembly_type, :request_data, :response_data,
             :echart_data, :image_data, :status, :created_at, :updated_at)
        """)

        # 执行插入
        result = await db.execute(insert_sql, {
            "user_id": user_id,
            "session_id": conversation.session_id,
            "assembly_type": assembly_type_enum.value,  # 使用枚举的值
            "request_data": request_data_json,
            "response_data": response_data_json,
            "echart_data": echart_data_json,
            "image_data": conversation.image_data,
            "status": status_enum.value,  # 使用枚举的值
            "created_at": now,
            "updated_at": now
        })

        await db.commit()

        # 获取插入的ID
        conversation_id = result.lastrowid

        log.info(f"用户 {user_id} 创建汇编对话记录: {conversation_id}")

        return {
            "code": 200,
            "message": "汇编对话记录创建成功",
            "data": {
                "id": conversation_id,
                "session_id": conversation.session_id,
                "created_at": now.isoformat()
            }
        }

    except Exception as e:
        log.error(f"创建汇编对话记录失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"创建汇编对话记录失败: {str(e)}"
        )

@router.get("/conversations", response_model=dict)
async def get_assembly_conversations(
    request: Request,
    assembly_type: Optional[str] = Query(None, description="汇编类型过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db)
):
    """获取用户的汇编对话历史列表"""
    try:
        # 从JWT token中提取用户ID，避免完整的用户查询
        user_id = None
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            try:
                from middleware.jwt_auth import JWTAuthService
                payload = JWTAuthService.decode_token(token)
                if payload:
                    user_id = payload.get("user_id")
                    if isinstance(user_id, str):
                        user_id = int(user_id)
            except Exception as e:
                log.warning(f"解析JWT token失败: {e}")
                raise HTTPException(
                    status_code=401,
                    detail="无效的认证token"
                )

        if not user_id:
            raise HTTPException(
                status_code=401,
                detail="未提供有效的认证信息"
            )

        # 使用原生SQL避免ORM关系问题
        from sqlalchemy import text

        # 构建基础查询条件
        where_conditions = ["user_id = :user_id"]
        params = {"user_id": user_id}

        # 类型过滤
        if assembly_type:
            where_conditions.append("assembly_type = :assembly_type")
            params["assembly_type"] = assembly_type

        where_clause = " AND ".join(where_conditions)

        # 计算总数
        count_sql = text(f"""
            SELECT COUNT(*)
            FROM assembly_conversations
            WHERE {where_clause}
        """)

        total_result = await db.execute(count_sql, params)
        total = total_result.scalar() or 0

        # 分页查询
        offset = (page - 1) * page_size
        params.update({"offset": offset, "limit": page_size})

        query_sql = text(f"""
            SELECT id, user_id, session_id, assembly_type, request_data,
                   response_data, echart_data, image_data, status,
                   created_at, updated_at
            FROM assembly_conversations
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT :limit OFFSET :offset
        """)

        result = await db.execute(query_sql, params)
        conversation_rows = result.fetchall()
        
        # 转换为响应格式
        conversation_list = []
        for row in conversation_rows:
            conversation_list.append({
                "id": row[0],
                "user_id": row[1],
                "session_id": row[2],
                "assembly_type": row[3],
                "request_data": row[4],
                "response_data": row[5],
                "echart_data": row[6],
                "image_data": row[7],
                "status": row[8],
                "created_at": row[9].isoformat() if row[9] else None,
                "updated_at": row[10].isoformat() if row[10] else None
            })
        
        return {
            "code": 200,
            "message": "获取汇编对话历史成功",
            "data": {
                "conversations": conversation_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
        }
        
    except Exception as e:
        log.error(f"获取汇编对话历史失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取汇编对话历史失败: {str(e)}"
        )

@router.get("/conversations/{conversation_id}", response_model=dict)
async def get_assembly_conversation(
    conversation_id: int,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """获取单个汇编对话详情"""
    try:
        # 从JWT token中提取用户ID
        user_id = None
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            try:
                from middleware.jwt_auth import JWTAuthService
                payload = JWTAuthService.decode_token(token)
                if payload:
                    user_id = payload.get("user_id")
                    if isinstance(user_id, str):
                        user_id = int(user_id)
            except Exception as e:
                log.warning(f"解析JWT token失败: {e}")
                raise HTTPException(
                    status_code=401,
                    detail="无效的认证token"
                )

        if not user_id:
            raise HTTPException(
                status_code=401,
                detail="未提供有效的认证信息"
            )

        # 使用原生SQL查询单个对话
        from sqlalchemy import text
        sql = text("""
            SELECT id, user_id, session_id, assembly_type, request_data,
                   response_data, echart_data, image_data, status,
                   created_at, updated_at
            FROM assembly_conversations
            WHERE id = :conversation_id AND user_id = :user_id
        """)

        result = await db.execute(sql, {
            "conversation_id": conversation_id,
            "user_id": user_id
        })
        conversation_row = result.fetchone()

        if not conversation_row:
            raise HTTPException(
                status_code=404,
                detail="汇编对话记录不存在"
            )

        return {
            "code": 200,
            "message": "获取汇编对话详情成功",
            "data": {
                "id": conversation_row[0],
                "user_id": conversation_row[1],
                "session_id": conversation_row[2],
                "assembly_type": conversation_row[3],
                "request_data": conversation_row[4],
                "response_data": conversation_row[5],
                "echart_data": conversation_row[6],
                "image_data": conversation_row[7],
                "status": conversation_row[8],
                "created_at": conversation_row[9].isoformat() if conversation_row[9] else None,
                "updated_at": conversation_row[10].isoformat() if conversation_row[10] else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"获取汇编对话详情失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取汇编对话详情失败: {str(e)}"
        )

@router.put("/conversations/{conversation_id}", response_model=dict)
async def update_assembly_conversation(
    conversation_id: int,
    conversation_update: AssemblyConversationUpdate,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """更新汇编对话记录"""
    try:
        # 从JWT token中提取用户ID
        user_id = None
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            try:
                from middleware.jwt_auth import JWTAuthService
                payload = JWTAuthService.decode_token(token)
                if payload:
                    user_id = payload.get("user_id")
                    if isinstance(user_id, str):
                        user_id = int(user_id)
            except Exception as e:
                log.warning(f"解析JWT token失败: {e}")
                raise HTTPException(
                    status_code=401,
                    detail="无效的认证token"
                )

        if not user_id:
            raise HTTPException(
                status_code=401,
                detail="未提供有效的认证信息"
            )

        # 使用原生SQL查询和更新，避免SQLAlchemy关系问题
        from sqlalchemy import text
        import json
        from datetime import datetime

        # 首先检查记录是否存在
        check_sql = text("""
            SELECT id FROM assembly_conversations
            WHERE id = :conversation_id AND user_id = :user_id
        """)

        result = await db.execute(check_sql, {
            "conversation_id": conversation_id,
            "user_id": user_id
        })
        existing_record = result.fetchone()

        if not existing_record:
            raise HTTPException(
                status_code=404,
                detail="汇编对话记录不存在"
            )

        # 准备更新数据
        update_data = conversation_update.model_dump(exclude_unset=True)
        if not update_data:
            raise HTTPException(
                status_code=400,
                detail="没有提供要更新的数据"
            )

        # 构建更新SQL
        set_clauses = []
        params = {"conversation_id": conversation_id, "user_id": user_id, "updated_at": datetime.now()}

        for field, value in update_data.items():
            if field in ['response_data', 'echart_data'] and value is not None:
                # JSON字段需要序列化
                set_clauses.append(f"{field} = :{field}")
                params[field] = json.dumps(value)
            elif field in ['image_data', 'status']:
                set_clauses.append(f"{field} = :{field}")
                params[field] = value

        if set_clauses:
            set_clauses.append("updated_at = :updated_at")
            update_sql = text(f"""
                UPDATE assembly_conversations
                SET {', '.join(set_clauses)}
                WHERE id = :conversation_id AND user_id = :user_id
            """)

            await db.execute(update_sql, params)
            await db.commit()

        log.info(f"用户 {user_id} 更新汇编对话记录: {conversation_id}")

        return {
            "code": 200,
            "message": "汇编对话记录更新成功",
            "data": {
                "id": conversation_id,
                "updated_at": params["updated_at"].isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"更新汇编对话记录失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"更新汇编对话记录失败: {str(e)}"
        )

@router.delete("/conversations/{conversation_id}", response_model=dict)
async def delete_assembly_conversation(
    conversation_id: int,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """删除汇编对话记录"""
    try:
        # 从JWT token中提取用户ID
        user_id = None
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            try:
                from middleware.jwt_auth import JWTAuthService
                payload = JWTAuthService.decode_token(token)
                if payload:
                    user_id = payload.get("user_id")
                    if isinstance(user_id, str):
                        user_id = int(user_id)
            except Exception as e:
                log.warning(f"解析JWT token失败: {e}")
                raise HTTPException(
                    status_code=401,
                    detail="无效的认证token"
                )

        if not user_id:
            raise HTTPException(
                status_code=401,
                detail="未提供有效的认证信息"
            )

        # 使用原生SQL删除，避免SQLAlchemy关系问题
        from sqlalchemy import text

        # 首先检查记录是否存在
        check_sql = text("""
            SELECT id FROM assembly_conversations
            WHERE id = :conversation_id AND user_id = :user_id
        """)

        result = await db.execute(check_sql, {
            "conversation_id": conversation_id,
            "user_id": user_id
        })
        existing_record = result.fetchone()

        if not existing_record:
            raise HTTPException(
                status_code=404,
                detail="汇编对话记录不存在"
            )

        # 删除记录
        delete_sql = text("""
            DELETE FROM assembly_conversations
            WHERE id = :conversation_id AND user_id = :user_id
        """)

        await db.execute(delete_sql, {
            "conversation_id": conversation_id,
            "user_id": user_id
        })
        await db.commit()

        log.info(f"用户 {user_id} 删除汇编对话记录: {conversation_id}")

        return {
            "code": 200,
            "message": "汇编对话记录删除成功",
            "data": None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        log.error(f"删除汇编对话记录失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"删除汇编对话记录失败: {str(e)}"
        )

@router.post("/sessions/generate", response_model=dict)
async def generate_session_id(
    request: Request
):
    """生成新的会话ID"""
    try:
        # 从JWT token中提取用户ID
        user_id = None
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.split(" ")[1]
            try:
                from middleware.jwt_auth import JWTAuthService
                payload = JWTAuthService.decode_token(token)
                if payload:
                    user_id = payload.get("user_id")
                    if isinstance(user_id, str):
                        user_id = int(user_id)
            except Exception as e:
                log.warning(f"解析JWT token失败: {e}")
                raise HTTPException(
                    status_code=401,
                    detail="无效的认证token"
                )

        if not user_id:
            raise HTTPException(
                status_code=401,
                detail="未提供有效的认证信息"
            )

        session_id = f"session_{user_id}_{int(datetime.now().timestamp())}_{uuid.uuid4().hex[:8]}"

        return {
            "code": 200,
            "message": "会话ID生成成功",
            "data": {
                "session_id": session_id,
                "user_id": user_id,
                "created_at": datetime.now().isoformat()
            }
        }

    except Exception as e:
        log.error(f"生成会话ID失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"生成会话ID失败: {str(e)}"
        )






