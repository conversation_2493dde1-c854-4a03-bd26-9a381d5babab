{
    "timestamp": 99290.537562294,
    "tasks": [
        {
            "task_id": "73151b43-4b82-41fd-90cd-05a4c2910745",
            "status": "running",
            "progress": 0,
            "processed_files": [],
            "failed_files": [],
            "created_at": "2025-07-17T15:48:14.400558",
            "updated_at": "2025-07-17T15:48:14.445101",
            "error": null,
            "total_files": 1,
            "file_progress": {
                "docs/分省公司专业机构管理应用建设 （公司食堂管理系统）/分省公司专业机构管理应用建设 （公司食堂管理系统） .json": {
                    "status": "processing",
                    "progress": 10,
                    "message": "下载JSON文件",
                    "error": null,
                    "stage": "download",
                    "stage_detail": "正在下载文件",
                    "updated_at": "2025-07-17T15:48:14.445095"
                }
            },
            "task_type": "agent_assembly",
            "task_params": {
                "action": "项目档案",
                "project_name": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                "urls": [
                    "docs/分省公司专业机构管理应用建设 （公司食堂管理系统）/分省公司专业机构管理应用建设 （公司食堂管理系统） .json"
                ],
                "user_name": "admin",
                "workflow_mode": "agent_based"
            }
        }
    ]
}
{
    "timestamp": 99368.570991665,
    "tasks": [
        {
            "task_id": "73151b43-4b82-41fd-90cd-05a4c2910745",
            "status": "completed",
            "progress": 100,
            "processed_files": [
                {
                    "url": "docs/分省公司专业机构管理应用建设 （公司食堂管理系统）/分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                    "doc_id": "d1674d8cc1f9f49716e5b47e5005779a",
                    "workflow_id": "business-workflow-140079207730048",
                    "extracted_data": {
                        "init_info": {
                            "initialization_status": "failed",
                            "initialization_time": "2025-07-17T15:48:14.962138",
                            "task_id": "73151b43-4b82-41fd-90cd-05a4c2910745",
                            "project_name": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                            "action_type": "项目档案",
                            "file_count": 1,
                            "work_directory": "/tmp/hngpt_task_73151b43-4b82-41fd-90cd-05a4c2910745"
                        },
                        "context_validation": {
                            "valid": true,
                            "issues": [],
                            "details": {
                                "task_id": "valid",
                                "project_name": "valid",
                                "action": "valid",
                                "file_urls": "valid",
                                "config": "valid",
                                "action_type": "valid",
                                "file_urls_count": 1
                            }
                        },
                        "config_validation": {
                            "valid": false,
                            "issues": [
                                "Missing config section: chunk_size",
                                "Missing config section: chunk_overlap",
                                "Missing project_extract config for 项目档案",
                                "Missing MinIO config field: access_key",
                                "Missing MinIO config field: secret_key",
                                "Missing MinIO config field: bucket_name",
                                "Missing Elasticsearch config field: index_name"
                            ],
                            "config_sections": {
                                "minio": "present",
                                "elasticsearch": "present"
                            }
                        },
                        "work_directory": {
                            "created": true,
                            "path": "/tmp/hngpt_task_73151b43-4b82-41fd-90cd-05a4c2910745",
                            "subdirectories": [
                                "downloads",
                                "parsed",
                                "chunks",
                                "vectors",
                                "logs"
                            ],
                            "permissions": "read_write"
                        },
                        "service_validation": {
                            "all_available": true,
                            "services": {
                                "minio": {
                                    "available": true,
                                    "endpoint": "localhost:9000",
                                    "bucket": "unknown"
                                },
                                "elasticsearch": {
                                    "available": true,
                                    "host": "localhost:9200",
                                    "index": "unknown"
                                },
                                "llm": {
                                    "available": true,
                                    "model": "hngpt-mini",
                                    "endpoint": "http://*************:8888/v1"
                                }
                            }
                        },
                        "processing_stats": {
                            "task_start_time": "2025-07-17T15:48:14.962108",
                            "total_files": 1,
                            "processed_files": 0,
                            "failed_files": 0,
                            "total_chunks": 0,
                            "total_vectors": 0,
                            "extracted_records": 0,
                            "processing_stages": {
                                "init": "completed",
                                "minio_download": "pending",
                                "document_parse": "pending",
                                "text_chunking": "pending",
                                "vector_embedding": "pending",
                                "elasticsearch_storage": "pending",
                                "llm_extraction": "pending",
                                "database_storage": "pending"
                            },
                            "performance_metrics": {
                                "files_per_minute": 0,
                                "chunks_per_minute": 0,
                                "vectors_per_minute": 0
                            }
                        },
                        "init_summary": {
                            "status": "failed",
                            "ready_for_processing": false,
                            "next_stage": "failed"
                        },
                        "download_info": {
                            "download_time": "2025-07-17T15:48:15.563563",
                            "total_requested": 1,
                            "total_downloaded": 1,
                            "total_failed": 0,
                            "download_directory": "/tmp/hngpt_task_73151b43-4b82-41fd-90cd-05a4c2910745/downloads",
                            "download_method": "minio_client"
                        },
                        "download_results": [
                            {
                                "url": "docs/分省公司专业机构管理应用建设 （公司食堂管理系统）/分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                "local_path": "/tmp/hngpt_task_73151b43-4b82-41fd-90cd-05a4c2910745/downloads/分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                "file_name": "分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                "file_size": 578,
                                "file_hash": "ae52d680348a393233c2ec95d931ec6e",
                                "download_success": true,
                                "download_time": "2025-07-17T15:48:15.563522",
                                "error": null
                            }
                        ],
                        "file_validation": {
                            "total_files": 1,
                            "valid_files": 1,
                            "invalid_files": 0,
                            "validation_details": [
                                {
                                    "file_name": "分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                    "status": "valid",
                                    "size": 578,
                                    "hash": "ae52d680348a393233c2ec95d931ec6e"
                                }
                            ]
                        },
                        "file_manifest": {
                            "manifest_time": "2025-07-17T15:48:15.563541",
                            "files": [
                                {
                                    "name": "分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                    "path": "/tmp/hngpt_task_73151b43-4b82-41fd-90cd-05a4c2910745/downloads/分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                    "size": 578,
                                    "hash": "ae52d680348a393233c2ec95d931ec6e",
                                    "type": ".json",
                                    "url": "docs/分省公司专业机构管理应用建设 （公司食堂管理系统）/分省公司专业机构管理应用建设 （公司食堂管理系统） .json"
                                }
                            ],
                            "total_size": 578,
                            "file_types": {
                                ".json": 1
                            }
                        },
                        "download_statistics": {
                            "total_requests": 1,
                            "successful_downloads": 1,
                            "failed_downloads": 0,
                            "total_size": 578,
                            "average_file_size": 578.0,
                            "download_rate": 1.0
                        },
                        "supported_files": {
                            "valid_files": [],
                            "unsupported_files": [
                                {
                                    "file_name": "分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                    "extension": ".json",
                                    "reason": "unsupported_format"
                                }
                            ],
                            "supported_count": 0,
                            "unsupported_count": 1
                        },
                        "download_summary": {
                            "status": "completed",
                            "download_successful": true,
                            "files_ready_for_processing": 0,
                            "next_stage": "document_parse"
                        },
                        "parse_info": {
                            "parse_time": "2025-07-17T15:48:15.864428",
                            "total_files": 1,
                            "successful_parses": 1,
                            "failed_parses": 0,
                            "total_text_length": 292,
                            "parse_method": "integrated_parser"
                        },
                        "parse_results": [
                            {
                                "file_name": "分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                "file_path": "/tmp/hngpt_task_73151b43-4b82-41fd-90cd-05a4c2910745/downloads/分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                "parse_success": true,
                                "error": null,
                                "content": "title: 分省公司专业机构管理应用建设（公司食堂管理系统）\nproject_no: PROJ-2024-001\nstart_date: 2024-01-01\nend_date: 2024-12-31\ntotal_investment: 500万元\nresponsible_unit: 技术开发部\nleader: 张三\nresearch_points: 开发智能食堂管理系统，包括订餐、支付、库存管理等功能\ninnovation: 采用AI技术优化菜品推荐和库存预测\nmain_deliverables: 食堂管理系统软件、用户手册、技术文档\npatent: 智能订餐系统专利申请中",
                                "pages": [
                                    {
                                        "page_number": 1,
                                        "content": "title: 分省公司专业机构管理应用建设（公司食堂管理系统）\nproject_no: PROJ-2024-001\nstart_date: 2024-01-01\nend_date: 2024-12-31\ntotal_investment: 500万元\nresponsible_unit: 技术开发部\nleader: 张三\nresearch_points: 开发智能食堂管理系统，包括订餐、支付、库存管理等功能\ninnovation: 采用AI技术优化菜品推荐和库存预测\nmain_deliverables: 食堂管理系统软件、用户手册、技术文档\npatent: 智能订餐系统专利申请中",
                                        "metadata": {
                                            "source": "json_conversion"
                                        }
                                    }
                                ],
                                "metadata": {
                                    "file_type": "json",
                                    "original_data": {
                                        "title": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                                        "project_no": "PROJ-2024-001",
                                        "start_date": "2024-01-01",
                                        "end_date": "2024-12-31",
                                        "total_investment": "500万元",
                                        "responsible_unit": "技术开发部",
                                        "leader": "张三",
                                        "research_points": "开发智能食堂管理系统，包括订餐、支付、库存管理等功能",
                                        "innovation": "采用AI技术优化菜品推荐和库存预测",
                                        "main_deliverables": "食堂管理系统软件、用户手册、技术文档",
                                        "patent": "智能订餐系统专利申请中"
                                    },
                                    "page_count": 1,
                                    "text_length": 292
                                }
                            }
                        ],
                        "parse_validation": {
                            "total_files": 1,
                            "valid_parses": 1,
                            "invalid_parses": 0,
                            "validation_details": [
                                {
                                    "file_name": "分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                    "status": "valid",
                                    "content_length": 292,
                                    "page_count": 1
                                }
                            ]
                        },
                        "parse_statistics": {
                            "total_files": 1,
                            "successful_parses": 1,
                            "failed_parses": 0,
                            "total_text_length": 292,
                            "total_pages": 1,
                            "file_types": {
                                "json": 1
                            }
                        },
                        "extracted_texts": [
                            {
                                "file_name": "分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                "content": "title: 分省公司专业机构管理应用建设（公司食堂管理系统）\nproject_no: PROJ-2024-001\nstart_date: 2024-01-01\nend_date: 2024-12-31\ntotal_investment: 500万元\nresponsible_unit: 技术开发部\nleader: 张三\nresearch_points: 开发智能食堂管理系统，包括订餐、支付、库存管理等功能\ninnovation: 采用AI技术优化菜品推荐和库存预测\nmain_deliverables: 食堂管理系统软件、用户手册、技术文档\npatent: 智能订餐系统专利申请中",
                                "pages": [
                                    {
                                        "page_number": 1,
                                        "content": "title: 分省公司专业机构管理应用建设（公司食堂管理系统）\nproject_no: PROJ-2024-001\nstart_date: 2024-01-01\nend_date: 2024-12-31\ntotal_investment: 500万元\nresponsible_unit: 技术开发部\nleader: 张三\nresearch_points: 开发智能食堂管理系统，包括订餐、支付、库存管理等功能\ninnovation: 采用AI技术优化菜品推荐和库存预测\nmain_deliverables: 食堂管理系统软件、用户手册、技术文档\npatent: 智能订餐系统专利申请中",
                                        "metadata": {
                                            "source": "json_conversion"
                                        }
                                    }
                                ],
                                "metadata": {
                                    "file_type": "json",
                                    "original_data": {
                                        "title": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                                        "project_no": "PROJ-2024-001",
                                        "start_date": "2024-01-01",
                                        "end_date": "2024-12-31",
                                        "total_investment": "500万元",
                                        "responsible_unit": "技术开发部",
                                        "leader": "张三",
                                        "research_points": "开发智能食堂管理系统，包括订餐、支付、库存管理等功能",
                                        "innovation": "采用AI技术优化菜品推荐和库存预测",
                                        "main_deliverables": "食堂管理系统软件、用户手册、技术文档",
                                        "patent": "智能订餐系统专利申请中"
                                    },
                                    "page_count": 1,
                                    "text_length": 292
                                }
                            }
                        ],
                        "content_classification": {
                            "document_type": "项目档案",
                            "content_analysis": {
                                "project_related": true,
                                "keywords_found": [
                                    "项目",
                                    "开发",
                                    "系统"
                                ],
                                "confidence": 0.8
                            },
                            "relevance_score": 0.8
                        },
                        "parse_summary": {
                            "status": "completed",
                            "parsing_successful": true,
                            "text_ready_for_chunking": true,
                            "next_stage": "text_chunking"
                        },
                        "extraction_info": {
                            "extraction_time": "2025-07-17T15:49:56.930630",
                            "extraction_type": "项目档案",
                            "total_documents": 1,
                            "successful_extractions": 0,
                            "failed_extractions": 1,
                            "llm_model": "hngpt",
                            "extraction_method": "config_driven"
                        },
                        "extraction_results": [
                            {
                                "file_name": "分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                "extraction_success": false,
                                "error": "Extraction returned None",
                                "extracted_data": {},
                                "metadata": {
                                    "file_type": "json",
                                    "original_data": {
                                        "title": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                                        "project_no": "PROJ-2024-001",
                                        "start_date": "2024-01-01",
                                        "end_date": "2024-12-31",
                                        "total_investment": "500万元",
                                        "responsible_unit": "技术开发部",
                                        "leader": "张三",
                                        "research_points": "开发智能食堂管理系统，包括订餐、支付、库存管理等功能",
                                        "innovation": "采用AI技术优化菜品推荐和库存预测",
                                        "main_deliverables": "食堂管理系统软件、用户手册、技术文档",
                                        "patent": "智能订餐系统专利申请中"
                                    },
                                    "page_count": 1,
                                    "text_length": 292
                                }
                            }
                        ],
                        "extraction_validation": {
                            "total_files": 1,
                            "successful_extractions": 0,
                            "failed_extractions": 1,
                            "field_completeness": {},
                            "data_quality_score": 0.0,
                            "validation_details": [
                                {
                                    "file_name": "分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                    "status": "failed",
                                    "error": "Extraction returned None"
                                }
                            ]
                        },
                        "extraction_statistics": {
                            "total_files": 1,
                            "successful_extractions": 0,
                            "failed_extractions": 1,
                            "total_fields_extracted": 0,
                            "unique_fields": [],
                            "extraction_efficiency": 0.0,
                            "average_fields_per_document": 0.0
                        },
                        "merged_information": {
                            "project_name": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                            "action": "项目档案",
                            "task_id": "73151b43-4b82-41fd-90cd-05a4c2910745",
                            "merge_time": "2025-07-17T15:49:56.930613",
                            "source_files": [],
                            "merged_data": {},
                            "confidence_scores": {}
                        },
                        "extraction_report": {
                            "report_time": "2025-07-17T15:49:56.930624",
                            "task_info": {
                                "task_id": "73151b43-4b82-41fd-90cd-05a4c2910745",
                                "project_name": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                                "action": "项目档案"
                            },
                            "extraction_summary": {
                                "total_files": 1,
                                "successful_extractions": 0,
                                "failed_extractions": 1,
                                "fields_extracted": 0
                            },
                            "file_details": [
                                {
                                    "file_name": "分省公司专业机构管理应用建设 （公司食堂管理系统） .json",
                                    "extraction_success": false,
                                    "fields_count": 0,
                                    "error": "Extraction returned None"
                                }
                            ],
                            "merged_result": {
                                "project_name": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                                "action": "项目档案",
                                "task_id": "73151b43-4b82-41fd-90cd-05a4c2910745",
                                "merge_time": "2025-07-17T15:49:56.930613",
                                "source_files": [],
                                "merged_data": {},
                                "confidence_scores": {}
                            },
                            "recommendations": [
                                "部分文件提取失败，建议检查文件内容和格式",
                                "提取的字段较少，建议检查文档内容是否包含相关信息"
                            ]
                        },
                        "extraction_summary": {
                            "status": "completed",
                            "extraction_successful": false,
                            "structured_data_ready": true,
                            "next_stage": "database_storage"
                        },
                        "storage_info": {
                            "storage_time": "2025-07-17T15:49:57.732063",
                            "total_documents": 0,
                            "successful_storage": 0,
                            "failed_storage": 0,
                            "es_index": "docs",
                            "storage_method": "upsert_documents"
                        },
                        "storage_results": {
                            "storage_success": false,
                            "records_stored": 0,
                            "table_name": "",
                            "record_id": null,
                            "error": "MySQL connection pool not available",
                            "storage_details": {}
                        },
                        "storage_validation": {
                            "storage_successful": false,
                            "records_stored": 0,
                            "table_name": "",
                            "data_integrity": false,
                            "validation_details": {
                                "error": "MySQL connection pool not available",
                                "failure_reason": "Database storage failed"
                            }
                        },
                        "storage_statistics": {
                            "total_operations": 1,
                            "successful_records": 0,
                            "failed_records": 1,
                            "storage_efficiency": 0.0,
                            "database_size": 0,
                            "table_record_count": 0
                        },
                        "storage_report": {
                            "report_time": "2025-07-17T15:49:57.732004",
                            "task_info": {
                                "task_id": "73151b43-4b82-41fd-90cd-05a4c2910745",
                                "project_name": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                                "action": "项目档案"
                            },
                            "storage_summary": {
                                "total_files_processed": 0,
                                "successful_storage": 0,
                                "failed_storage": 0,
                                "total_documents_stored": 0
                            },
                            "file_details": [],
                            "es_info": {
                                "index_name": "docs",
                                "storage_method": "upsert_documents"
                            }
                        },
                        "retrieval_test": {
                            "test_time": "2025-07-17T15:49:57.732054",
                            "test_query": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                            "retrieval_success": false,
                            "results_count": 0,
                            "test_details": {
                                "message": "ES client does not support doc_search method"
                            }
                        },
                        "storage_summary": {
                            "status": "completed",
                            "storage_successful": false,
                            "documents_ready_for_search": 0,
                            "next_stage": "llm_extraction"
                        },
                        "database_storage_info": {
                            "storage_time": "2025-07-17T15:49:58.234434",
                            "database_type": "mysql",
                            "database_host": "localhost:3306",
                            "database_name": "hngpt",
                            "table_name": "project_extract",
                            "records_stored": 0,
                            "storage_method": "upsert"
                        },
                        "final_report": {
                            "report_time": "2025-07-17T15:49:58.234395",
                            "task_summary": {
                                "task_id": "73151b43-4b82-41fd-90cd-05a4c2910745",
                                "project_name": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                                "action": "项目档案",
                                "workflow_status": "failed",
                                "total_processing_time": 103.272271
                            },
                            "stage_summary": {
                                "initialization": {
                                    "status": "completed",
                                    "progress": 10
                                },
                                "minio_download": {
                                    "status": "completed",
                                    "progress": 20,
                                    "files_downloaded": 1
                                },
                                "document_parse": {
                                    "status": "completed",
                                    "progress": 35,
                                    "files_parsed": 1
                                },
                                "text_chunking": {
                                    "status": "completed",
                                    "progress": 50,
                                    "chunks_created": 0
                                },
                                "vector_embedding": {
                                    "status": "completed",
                                    "progress": 65,
                                    "vectors_generated": 0
                                },
                                "elasticsearch_storage": {
                                    "status": "completed",
                                    "progress": 80,
                                    "documents_stored": 0
                                },
                                "llm_extraction": {
                                    "status": "completed",
                                    "progress": 90,
                                    "extractions_completed": 0
                                },
                                "database_storage": {
                                    "status": "failed",
                                    "progress": 100,
                                    "records_stored": 0
                                }
                            },
                            "final_results": {
                                "structured_data_stored": false,
                                "database_table": "",
                                "record_id": null,
                                "searchable_documents": 0,
                                "workflow_completed": true
                            },
                            "performance_metrics": {
                                "files_processed": 1,
                                "text_chunks_created": 0,
                                "vectors_generated": 0,
                                "es_documents_stored": 0,
                                "database_records_stored": 0
                            },
                            "recommendations": [
                                "❌ 数据库存储失败，请检查数据库连接和权限",
                                "🔧 建议检查SQLite数据库路径和写入权限",
                                "⚠️ 未成功提取任何结构化信息，建议检查文档内容和LLM配置",
                                "⚠️ 向量化失败，建议检查向量化模型配置"
                            ]
                        },
                        "database_summary": {
                            "status": "completed",
                            "storage_successful": false,
                            "workflow_completed": true,
                            "final_stage": true
                        }
                    },
                    "status": "completed",
                    "message": "Agent工作流处理完成"
                }
            ],
            "failed_files": [],
            "created_at": "2025-07-17T15:48:14.400558",
            "updated_at": "2025-07-17T15:49:58.236365",
            "error": null,
            "total_files": 1,
            "file_progress": {
                "docs/分省公司专业机构管理应用建设 （公司食堂管理系统）/分省公司专业机构管理应用建设 （公司食堂管理系统） .json": {
                    "status": "completed",
                    "progress": 100,
                    "message": "Agent工作流处理完成",
                    "error": null,
                    "stage": "complete",
                    "stage_detail": "所有Agent执行完成",
                    "updated_at": "2025-07-17T15:49:58.234793"
                }
            },
            "task_type": "agent_assembly",
            "task_params": {
                "action": "项目档案",
                "project_name": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                "urls": [
                    "docs/分省公司专业机构管理应用建设 （公司食堂管理系统）/分省公司专业机构管理应用建设 （公司食堂管理系统） .json"
                ],
                "user_name": "admin",
                "workflow_mode": "agent_based"
            }
        }
    ]
}