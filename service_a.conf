# Service A配置 - nginx部署版本
# 前端静态文件 + 后端API代理 + iframe集成nginx前端
server {
    listen 8081;
    server_name _;  # 匹配任何域名，适应nps转发

    # Service A静态文件根目录
    root /opt/service_a/service_a_frontend/static;
    index test_host.html index.html;

    # 日志配置
    access_log /var/log/nginx/service_a_access.log;
    error_log /var/log/nginx/service_a_error.log;

    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # CORS设置 - 支持iframe集成
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-User-ID" always;
    add_header Access-Control-Allow-Credentials "true" always;

    # Service A前端静态文件
    location / {
        try_files $uri $uri/ /test_host.html;

        # HTML文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }

        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # 测试页面路由
    location /test {
        try_files /test_host.html =404;
    }

    # Service A后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8082;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 文件上传大小限制
    client_max_body_size 100M;

    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}
