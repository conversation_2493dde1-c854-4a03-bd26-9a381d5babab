"""
创建汇编对话历史表的数据库迁移脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from utils.config import config
from utils.log import log

# 数据库连接配置
DATABASE_URL = (
    f"mysql+asyncmy://{config.get('mysql.username', 'root')}:{config.get('mysql.password', 'startfrom2023')}"
    f"@{config.get('mysql.host', '***********')}:{config.get('mysql.port', 3306)}/{config.get('mysql.database', 'hngpt')}"
    "?charset=utf8mb4"
)

async def create_assembly_conversations_table():
    """创建汇编对话历史表"""
    engine = create_async_engine(DATABASE_URL)
    
    try:
        async with engine.begin() as conn:
            # 检查表是否已存在
            check_table_sql = """
            SELECT COUNT(*) as count 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'assembly_conversations'
            """
            
            result = await conn.execute(text(check_table_sql))
            table_exists = result.fetchone()[0] > 0
            
            if table_exists:
                log.info("表 assembly_conversations 已存在，跳过创建")
                return
            
            # 创建表的SQL
            create_table_sql = """
            CREATE TABLE `assembly_conversations` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` bigint(20) NOT NULL COMMENT '用户ID',
              `session_id` varchar(64) NOT NULL COMMENT '会话ID，用于分组相关的对话',
              `assembly_type` varchar(20) NOT NULL COMMENT '汇编类型：custom(自定义) 或 template(模板)',
              `request_data` json NOT NULL COMMENT '请求参数JSON',
              `response_data` json DEFAULT NULL COMMENT '响应结果JSON',
              `echart_data` json DEFAULT NULL COMMENT '图表数据JSON',
              `image_data` longtext DEFAULT NULL COMMENT '图片数据base64',
              `status` varchar(20) DEFAULT 'completed' COMMENT '状态：pending, completed, failed',
              `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
              `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_user_id` (`user_id`),
              KEY `idx_session_id` (`session_id`),
              KEY `idx_assembly_type` (`assembly_type`),
              KEY `idx_created_at` (`created_at`),
              CONSTRAINT `fk_assembly_conversations_user_id` 
                FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
            COMMENT='汇编对话历史表';
            """
            
            await conn.execute(text(create_table_sql))
            log.info("成功创建表 assembly_conversations")
            
    except Exception as e:
        log.error(f"创建表失败: {str(e)}")
        raise
    finally:
        await engine.dispose()

async def add_indexes():
    """添加额外的索引以优化查询性能"""
    engine = create_async_engine(DATABASE_URL)
    
    try:
        async with engine.begin() as conn:
            # 复合索引：用户ID + 创建时间（用于分页查询）
            index_sql_1 = """
            CREATE INDEX `idx_user_created` ON `assembly_conversations` (`user_id`, `created_at` DESC)
            """
            
            # 复合索引：用户ID + 汇编类型（用于类型过滤）
            index_sql_2 = """
            CREATE INDEX `idx_user_type` ON `assembly_conversations` (`user_id`, `assembly_type`)
            """
            
            try:
                await conn.execute(text(index_sql_1))
                log.info("成功创建索引 idx_user_created")
            except Exception as e:
                if "Duplicate key name" not in str(e):
                    log.warning(f"创建索引 idx_user_created 失败: {str(e)}")
            
            try:
                await conn.execute(text(index_sql_2))
                log.info("成功创建索引 idx_user_type")
            except Exception as e:
                if "Duplicate key name" not in str(e):
                    log.warning(f"创建索引 idx_user_type 失败: {str(e)}")
                    
    except Exception as e:
        log.error(f"添加索引失败: {str(e)}")
        raise
    finally:
        await engine.dispose()

async def verify_table_structure():
    """验证表结构是否正确"""
    engine = create_async_engine(DATABASE_URL)
    
    try:
        async with engine.begin() as conn:
            # 检查表结构
            describe_sql = "DESCRIBE assembly_conversations"
            result = await conn.execute(text(describe_sql))
            columns = result.fetchall()
            
            log.info("表 assembly_conversations 结构:")
            for column in columns:
                log.info(f"  {column[0]}: {column[1]} {column[2]} {column[3]} {column[4]} {column[5]}")
            
            # 检查索引
            show_indexes_sql = "SHOW INDEX FROM assembly_conversations"
            result = await conn.execute(text(show_indexes_sql))
            indexes = result.fetchall()
            
            log.info("表 assembly_conversations 索引:")
            for index in indexes:
                log.info(f"  {index[2]}: {index[4]} ({index[10]})")
                
    except Exception as e:
        log.error(f"验证表结构失败: {str(e)}")
        raise
    finally:
        await engine.dispose()

async def main():
    """主函数"""
    log.info("开始创建汇编对话历史表...")
    
    try:
        # 1. 创建表
        await create_assembly_conversations_table()
        
        # 2. 添加索引
        await add_indexes()
        
        # 3. 验证表结构
        await verify_table_structure()
        
        log.info("汇编对话历史表创建完成！")
        
    except Exception as e:
        log.error(f"迁移失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
