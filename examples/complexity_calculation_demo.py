#!/usr/bin/env python3
"""
文档复杂度计算演示
展示不同类型文档的复杂度计算过程
"""

import re
from typing import Dict, Any


class ComplexityCalculatorDemo:
    """复杂度计算演示器"""
    
    def __init__(self):
        self.complexity_indicators = [
            r'表\d+[：:]',                    # 表格
            r'图\d+[：:]',                    # 图表  
            r'附件\d+',                       # 附件
            r'第[一二三四五六七八九十\d]+章',   # 章节
            r'第[一二三四五六七八九十\d]+条',   # 条款
            r'\d+\.\d+\.\d+',                # 多级编号
            r'￥[\d,]+\.?\d*',                # 金额符号
            r'\d{4}年\d{1,2}月\d{1,2}日',     # 具体日期
        ]
        
        self.field_indicators = [
            '项目名称', '项目编号', '负责人', '负责单位',
            '金额', '投资', '预算', '费用', 
            '时间', '日期', '开始', '完成',
            '地点', '位置', '范围',
            '状态', '阶段', '进度',
            '类型', '性质', '分类',
            '概述', '描述', '说明',
            '建设', '实施', '开发'
        ]
    
    def demo_complexity_calculation(self):
        """演示不同文档的复杂度计算"""
        print("📊 文档复杂度计算演示")
        print("=" * 60)
        
        # 示例1：简单通知文档
        simple_doc = self._create_simple_document()
        simple_complexity = self._calculate_complexity_detailed(simple_doc, "简单通知文档")
        
        print()
        
        # 示例2：标准项目立项书
        standard_doc = self._create_standard_document()
        standard_complexity = self._calculate_complexity_detailed(standard_doc, "标准项目立项书")
        
        print()
        
        # 示例3：复杂可研报告
        complex_doc = self._create_complex_document()
        complex_complexity = self._calculate_complexity_detailed(complex_doc, "复杂可研报告")
        
        print()
        
        # 总结对比
        print("📋 复杂度对比总结")
        print("-" * 40)
        print(f"简单通知文档: {simple_complexity:.2f} → 推荐一次性提取")
        print(f"标准项目立项书: {standard_complexity:.2f} → 推荐一次性提取")  
        print(f"复杂可研报告: {complex_complexity:.2f} → 推荐分组提取")
        
        return {
            "simple": simple_complexity,
            "standard": standard_complexity,
            "complex": complex_complexity
        }
    
    def _create_simple_document(self) -> Dict[str, Any]:
        """创建简单文档示例"""
        return {
            "pages": [
                {
                    "content": """
关于召开项目启动会议的通知

各相关部门：

定于2023年3月20日上午9:00在会议室召开智慧城市项目启动会议。
请项目负责人张三及相关人员准时参加。

会议主要议题：
1. 项目启动安排
2. 工作分工确认
3. 时间节点讨论

市信息化办公室
2023年3月18日
"""
                }
            ]
        }
    
    def _create_standard_document(self) -> Dict[str, Any]:
        """创建标准文档示例"""
        return {
            "pages": [
                {
                    "content": """
智慧城市综合管理平台建设项目立项书

一、项目基本信息
项目名称：智慧城市综合管理平台建设项目
项目编号：ZHCS-2023-001
承担单位：市信息化办公室
项目负责人：张三
项目类型：信息化建设项目
建设地点：全市范围
项目状态：已立项

二、资金预算
项目总投资：人民币伍仟万元整（￥50,000,000元）
申请财政资金：4500万元
企业配套资金：500万元

三、时间安排
立项时间：2023年3月15日
计划开始时间：2023年4月1日
预计完成时间：2024年10月31日

四、项目概述
本项目旨在建设智慧城市综合管理平台，通过大数据、云计算、
物联网等先进技术，提升城市管理效率。
"""
                },
                {
                    "content": """
五、技术方案
采用云原生架构，包括：
- 数据采集层
- 数据处理层  
- 应用服务层
- 用户界面层

六、预期效果
1. 提升城市管理效率30%
2. 降低运营成本20%
3. 改善市民满意度

七、风险分析
技术风险、进度风险、资金风险等。
"""
                }
            ]
        }
    
    def _create_complex_document(self) -> Dict[str, Any]:
        """创建复杂文档示例"""
        return {
            "pages": [
                {
                    "content": """
智慧城市综合管理平台建设项目可行性研究报告

第一章 项目概述
1.1 项目背景
1.1.1 政策背景
1.1.2 技术背景
1.1.3 市场背景

1.2 项目基本信息
项目名称：智慧城市综合管理平台建设项目
项目编号：ZHCS-2023-001-FS
承担单位：市信息化办公室
项目负责人：张三（高级工程师）
联系电话：138-0000-0000
项目类型：重大信息化建设项目
建设性质：新建
建设地点：全市范围，重点覆盖主城区
项目状态：可研阶段

第二章 市场分析
2.1 需求分析
2.2 竞争分析
2.3 SWOT分析

表1：市场需求分析
需求类型    | 紧迫程度 | 投资规模
交通管理    | 高       | ￥15,000,000
环境监测    | 中       | ￥8,000,000
公共安全    | 高       | ￥12,000,000
政务服务    | 中       | ￥10,000,000
"""
                },
                {
                    "content": """
第三章 技术方案
3.1 总体架构
3.1.1 系统架构设计
3.1.2 技术路线选择
3.1.3 关键技术分析

3.2 详细设计
3.2.1 数据采集子系统
3.2.2 数据处理子系统
3.2.3 应用服务子系统
3.2.4 用户界面子系统

图1：系统总体架构图
图2：数据流程图
图3：部署架构图

第四章 投资估算
4.1 投资概算
项目总投资：人民币伍仟万元整（￥50,000,000元）

表2：投资明细表
类别          | 金额(万元) | 占比
软件开发      | 3000      | 60%
硬件采购      | 1500      | 30%
实施服务      | 500       | 10%
总计          | 5000      | 100%

4.2 资金来源
财政资金：￥45,000,000元（90%）
企业配套：￥5,000,000元（10%）

4.3 资金使用计划
2023年4月-6月：￥15,000,000元
2023年7月-12月：￥20,000,000元
2024年1月-10月：￥15,000,000元
"""
                },
                {
                    "content": """
第五章 实施计划
5.1 总体进度安排
项目建设期：18个月
立项时间：2023年3月15日
开工时间：2023年4月1日
完成时间：2024年10月31日
验收时间：2024年11月30日

5.2 详细进度计划
第一阶段（2023年4月-6月）：需求分析与设计
第二阶段（2023年7月-12月）：系统开发
第三阶段（2024年1月-6月）：系统集成与测试
第四阶段（2024年7月-10月）：试运行与优化

表3：里程碑计划
里程碑                | 计划完成时间
需求分析完成          | 2023年5月31日
概要设计完成          | 2023年6月30日
详细设计完成          | 2023年8月31日
系统开发完成          | 2023年12月31日
系统测试完成          | 2024年6月30日
试运行完成            | 2024年10月31日

第六章 风险分析
6.1 技术风险
6.2 进度风险
6.3 资金风险
6.4 市场风险

第七章 效益分析
7.1 经济效益
7.2 社会效益
7.3 环境效益

第八章 结论与建议
8.1 可行性结论
8.2 实施建议

附件1：技术规范书
附件2：投资明细表
附件3：进度计划表
"""
                }
            ]
        }
    
    def _calculate_complexity_detailed(self, doc_data: Dict[str, Any], doc_name: str) -> float:
        """详细计算并展示复杂度"""
        print(f"📄 {doc_name}")
        print("-" * 30)
        
        # 1. 文档长度评估
        total_length = 0
        page_count = len(doc_data.get("pages", []))
        
        for page in doc_data.get("pages", []):
            content = page.get("content", "")
            total_length += len(content)
        
        if total_length > 10000:
            length_score = 0.3
            length_level = "超长"
        elif total_length > 5000:
            length_score = 0.2
            length_level = "长"
        elif total_length > 2000:
            length_score = 0.1
            length_level = "中等"
        else:
            length_score = 0.0
            length_level = "短"
        
        print(f"   文档长度: {total_length}字符 ({length_level}) → 得分: {length_score}")
        
        # 2. 页数评估
        if page_count > 20:
            page_score = 0.2
            page_level = "超多页"
        elif page_count > 10:
            page_score = 0.15
            page_level = "多页"
        elif page_count > 5:
            page_score = 0.1
            page_level = "中等页数"
        else:
            page_score = 0.0
            page_level = "少页"
        
        print(f"   页数: {page_count}页 ({page_level}) → 得分: {page_score}")
        
        # 3. 内容结构复杂度
        content_text = " ".join(page.get("content", "") for page in doc_data.get("pages", []))
        
        found_indicators = []
        for pattern in self.complexity_indicators:
            if re.search(pattern, content_text):
                found_indicators.append(pattern)
        
        structure_score = min(len(found_indicators) * 0.05, 0.3)
        print(f"   结构复杂度: 发现{len(found_indicators)}个指示器 → 得分: {structure_score}")
        
        if found_indicators:
            print(f"     发现的结构: {', '.join(found_indicators[:3])}{'...' if len(found_indicators) > 3 else ''}")
        
        # 4. 字段密度评估
        found_fields = []
        for indicator in self.field_indicators:
            if indicator in content_text:
                found_fields.append(indicator)
        
        field_count = len(found_fields)
        if field_count > 10:
            field_score = 0.2
            field_level = "高密度"
        elif field_count > 7:
            field_score = 0.15
            field_level = "中高密度"
        elif field_count > 5:
            field_score = 0.1
            field_level = "中等密度"
        else:
            field_score = 0.0
            field_level = "低密度"
        
        print(f"   字段密度: {field_count}个字段 ({field_level}) → 得分: {field_score}")
        print(f"     发现的字段: {', '.join(found_fields[:5])}{'...' if len(found_fields) > 5 else ''}")
        
        # 5. 总分计算
        total_score = length_score + page_score + structure_score + field_score
        total_score = min(total_score, 1.0)  # 限制在0-1之间
        
        print(f"   总复杂度: {total_score:.2f}")
        
        # 6. 推荐策略
        if total_score > 0.7:
            strategy = "分组提取（4次LLM调用）"
        else:
            strategy = "一次性提取（1次LLM调用）"
        
        print(f"   推荐策略: {strategy}")
        
        return total_score


def main():
    """主函数"""
    demo = ComplexityCalculatorDemo()
    results = demo.demo_complexity_calculation()
    
    print("\n🎯 计算方法总结:")
    print("=" * 60)
    print("复杂度 = 文档长度(0-0.3) + 页数(0-0.2) + 结构复杂度(0-0.3) + 字段密度(0-0.2)")
    print("阈值: complexity > 0.7 → 分组提取, ≤ 0.7 → 一次性提取")
    print()
    print("各维度权重设计理由:")
    print("• 文档长度(30%): 直接影响LLM处理能力")
    print("• 结构复杂度(30%): 影响信息提取难度") 
    print("• 页数(20%): 反映内容分散程度")
    print("• 字段密度(20%): 影响字段遗漏概率")


if __name__ == "__main__":
    main()
