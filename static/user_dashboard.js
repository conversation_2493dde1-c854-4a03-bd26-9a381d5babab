// 用户仪表板JavaScript

const API_BASE = '/auth';
let authToken = localStorage.getItem('authToken');
let currentUser = null;

// 获取正确的登录页面路径
function getLoginPath() {
    const currentUrl = window.location.href;

    // Vue/Node开发环境检测：端口5173或3000
    const isVueDev = currentUrl.includes(':5173') || currentUrl.includes(':3000');

    const loginPath = isVueDev ? '/login.html' : '/login.html';



    return loginPath;
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    if (!authToken) {
        window.location.href = getLoginPath();
        return;
    }
    
    loadCurrentUser();
    showTab('documents'); // 默认显示文档列表
});

// 加载当前用户信息
async function loadCurrentUser() {
    try {
        const response = await fetch(`${API_BASE}/users/me`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            currentUser = await response.json();
            document.getElementById('currentUser').textContent = currentUser.user_name;
        } else {
            throw new Error('获取用户信息失败');
        }
    } catch (error) {
        console.error('加载用户信息失败:', error);
        logout();
    }
}

// 显示标签页
function showTab(tabName) {
    // 隐藏所有标签内容
    const tabs = document.querySelectorAll('.tab-content');
    tabs.forEach(tab => tab.style.display = 'none');
    
    // 移除所有标签的活跃状态
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => button.classList.remove('active'));
    
    // 显示选中的标签
    document.getElementById(tabName).style.display = 'block';
    
    // 设置对应按钮为活跃状态
    const activeButton = document.querySelector(`[onclick="showTab('${tabName}')"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }
    
    // 根据标签加载相应内容
    switch(tabName) {
        case 'documents':
            loadDocuments();
            break;
        case 'requests':
            loadPermissionRequests();
            break;
    }
}

// 加载文档列表
async function loadDocuments() {
    try {
        const response = await fetch(`${API_BASE}/documents`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const documents = await response.json();
            displayDocuments(documents);
        } else {
            throw new Error('获取文档列表失败');
        }
    } catch (error) {
        console.error('加载文档失败:', error);
        showAlert('加载文档失败: ' + error.message, 'error');
    }
}

// 显示文档列表
function displayDocuments(documents) {
    const container = document.getElementById('documentsList');
    
    if (!documents || documents.length === 0) {
        container.innerHTML = '<p>暂无文档</p>';
        return;
    }

    let html = `
        <table class="table">
            <thead>
                <tr>
                    <th>文档标题</th>
                    <th>文件名</th>
                    <th>上传时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;

    documents.forEach(doc => {
        html += `
            <tr>
                <td>${doc.title}</td>
                <td>${doc.file_name}</td>
                <td>${new Date(doc.created_at).toLocaleDateString()}</td>
                <td>
                    <button class="btn btn-primary btn-sm" onclick="requestPermission(${doc.id})">申请权限</button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table>';
    container.innerHTML = html;
}

// 申请文档权限
function requestPermission(documentId) {
    // 这里可以实现权限申请功能
    showAlert('权限申请功能开发中...', 'info');
}

// 加载权限申请列表
async function loadPermissionRequests() {
    try {
        const response = await fetch(`${API_BASE}/permission-requests`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const requests = await response.json();
            displayPermissionRequests(requests);
        } else {
            throw new Error('获取权限申请失败');
        }
    } catch (error) {
        console.error('加载权限申请失败:', error);
        showAlert('加载权限申请失败: ' + error.message, 'error');
    }
}

// 显示权限申请列表
function displayPermissionRequests(requests) {
    const container = document.getElementById('permissionRequestsList');
    
    if (!requests || requests.length === 0) {
        container.innerHTML = '<p>暂无权限申请</p>';
        return;
    }

    let html = `
        <table class="table">
            <thead>
                <tr>
                    <th>文档</th>
                    <th>权限类型</th>
                    <th>申请理由</th>
                    <th>申请时间</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;

    requests.forEach(req => {
        const permissionText = getPermissionTypeText(req.permission_type);
        const statusText = getStatusText(req.status);
        
        html += `
            <tr>
                <td>${req.document?.title || '未知文档'}</td>
                <td>${permissionText}</td>
                <td>${req.reason}</td>
                <td>${new Date(req.created_at).toLocaleDateString()}</td>
                <td>
                    <span class="status-badge status-${req.status}">
                        ${statusText}
                    </span>
                </td>
                <td>
                    ${req.status === 'pending' ? `
                        <button class="btn btn-warning btn-sm" onclick="withdrawRequest(${req.id})">撤销</button>
                        <button class="btn btn-secondary btn-sm" onclick="deleteRequest(${req.id})">删除</button>
                    ` : `
                        <button class="btn btn-secondary btn-sm" onclick="deleteRequest(${req.id})">删除</button>
                    `}
                </td>
            </tr>
        `;
    });

    html += '</tbody></table>';
    container.innerHTML = html;
}

// 权限类型映射
function getPermissionTypeText(type) {
    const types = {
        'read': '查看',
        'download': '下载',
        'write': '编辑',
        'delete': '删除',
        'share': '分享'
    };
    return types[type] || type;
}

// 状态映射
function getStatusText(status) {
    const statuses = {
        'pending': '待审核',
        'approved': '已批准',
        'rejected': '已拒绝'
    };
    return statuses[status] || status;
}

// 撤销权限申请
async function withdrawRequest(requestId) {
    if (!confirm('确定要撤销这个权限申请吗？')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/permission-requests/${requestId}/withdraw`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            showAlert('权限申请已撤销', 'success');
            loadPermissionRequests();
        } else {
            const error = await response.json();
            throw new Error(error.detail || '撤销失败');
        }
    } catch (error) {
        showAlert('撤销失败: ' + error.message, 'error');
    }
}

// 删除权限申请
async function deleteRequest(requestId) {
    if (!confirm('确定要删除这个权限申请吗？删除后无法恢复。')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/permission-requests/${requestId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            showAlert('权限申请已删除', 'success');
            loadPermissionRequests();
        } else {
            const error = await response.json();
            throw new Error(error.detail || '删除失败');
        }
    } catch (error) {
        showAlert('删除失败: ' + error.message, 'error');
    }
}

// 退出登录
function logout() {
    // 清除本地存储的认证信息
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    
    // 显示退出提示
    showAlert('已成功退出登录', 'success');
    
    // 延迟跳转到登录页面
    setTimeout(() => {
        window.location.href = getLoginPath();
    }, 1000);
}

// 显示提示信息
function showAlert(message, type = 'info') {
    // 创建提示元素
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px;
        border-radius: 4px;
        color: white;
        z-index: 1000;
        max-width: 300px;
    `;
    
    // 设置背景色
    const colors = {
        'success': '#28a745',
        'error': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8'
    };
    alert.style.backgroundColor = colors[type] || colors.info;
    
    alert.textContent = message;
    document.body.appendChild(alert);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 3000);
}
