<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自适应配置测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .badge-success { background: #28a745; color: white; }
        .badge-warning { background: #ffc107; color: black; }
        .badge-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 自适应配置测试页面</h1>
        <p>此页面用于测试和验证自适应环境配置系统的工作状态。</p>
        
        <div class="grid">
            <div>
                <h3>📍 当前环境信息</h3>
                <div id="current-env"></div>
            </div>
            
            <div>
                <h3>🔗 服务地址配置</h3>
                <div id="service-config"></div>
            </div>
        </div>
        
        <div class="grid">
            <div>
                <h3>✅ 允许的域名</h3>
                <div id="allowed-origins"></div>
            </div>
            
            <div>
                <h3>💾 存储兼容性</h3>
                <div id="storage-status"></div>
            </div>
        </div>
        
        <div class="container">
            <h3>🖼️ iframe 测试</h3>
            <div id="iframe-status"></div>
            <button onclick="testIframe()">测试 iframe 创建</button>
            <button onclick="clearIframe()">清除 iframe</button>
            <div id="iframe-container" style="margin-top: 20px;"></div>
        </div>
        
        <div class="container">
            <h3>🔍 完整诊断报告</h3>
            <button onclick="runDiagnosis()">运行完整诊断</button>
            <button onclick="exportConfig()">导出配置</button>
            <pre id="diagnosis-output"></pre>
        </div>
    </div>

    <!-- 加载自适应配置 -->
    <script src="/static/config/production.js"></script>
    
    <script>
        // 初始化配置
        let config = null;
        
        function initConfig() {
            try {
                config = window.getEnvironmentConfig();
                console.log('配置加载成功:', config);
                updateDisplay();
            } catch (e) {
                console.error('配置加载失败:', e);
                document.getElementById('current-env').innerHTML = 
                    '<div class="status error">❌ 配置系统加载失败: ' + e.message + '</div>';
            }
        }
        
        function updateDisplay() {
            if (!config) return;
            
            // 当前环境信息
            const envInfo = window.EnvironmentDetector.getCurrentHost();
            const envType = window.EnvironmentDetector.detectEnvironment();
            
            document.getElementById('current-env').innerHTML = `
                <div class="status info">
                    <strong>环境类型:</strong> <span class="badge badge-${envType === 'production' ? 'danger' : 'success'}">${envType}</span><br>
                    <strong>主机名:</strong> ${envInfo.hostname}<br>
                    <strong>端口:</strong> ${envInfo.port || '默认'}<br>
                    <strong>协议:</strong> ${envInfo.protocol}<br>
                    <strong>完整地址:</strong> ${envInfo.origin}
                </div>
            `;
            
            // 服务配置
            document.getElementById('service-config').innerHTML = `
                <div class="status success">
                    <strong>服务A:</strong> ${config.services.serviceA?.url || '未配置'}<br>
                    <strong>服务B:</strong> ${config.services.serviceB?.url || '未配置'}
                </div>
            `;
            
            // 允许的域名
            const originsHtml = config.allowedOrigins.map(origin => 
                `<span class="badge badge-success">${origin}</span>`
            ).join(' ');
            document.getElementById('allowed-origins').innerHTML = `
                <div class="status info">${originsHtml}</div>
            `;
            
            // 存储状态
            updateStorageStatus();
            
            // iframe状态
            updateIframeStatus();
        }
        
        function updateStorageStatus() {
            const sessionAvailable = isSessionStorageAvailable();
            const localAvailable = isLocalStorageAvailable();
            const inIframe = isInIframe();
            const crossOrigin = isCrossOriginIframe();
            
            document.getElementById('storage-status').innerHTML = `
                <div class="status ${sessionAvailable ? 'success' : 'warning'}">
                    <strong>sessionStorage:</strong> <span class="badge badge-${sessionAvailable ? 'success' : 'warning'}">${sessionAvailable ? '可用' : '不可用'}</span><br>
                    <strong>localStorage:</strong> <span class="badge badge-${localAvailable ? 'success' : 'danger'}">${localAvailable ? '可用' : '不可用'}</span><br>
                    <strong>iframe环境:</strong> <span class="badge badge-${inIframe ? 'warning' : 'success'}">${inIframe ? '是' : '否'}</span><br>
                    <strong>跨域iframe:</strong> <span class="badge badge-${crossOrigin ? 'warning' : 'success'}">${crossOrigin ? '是' : '否'}</span>
                </div>
            `;
        }
        
        function updateIframeStatus() {
            const inIframe = isInIframe();
            const crossOrigin = isCrossOriginIframe();
            
            let statusClass = 'info';
            let statusText = '正常环境';
            
            if (inIframe && crossOrigin) {
                statusClass = 'warning';
                statusText = '跨域iframe环境 - 可能影响存储访问';
            } else if (inIframe) {
                statusClass = 'info';
                statusText = '同域iframe环境';
            }
            
            document.getElementById('iframe-status').innerHTML = `
                <div class="status ${statusClass}">${statusText}</div>
            `;
        }
        
        function testIframe() {
            if (!config) {
                alert('配置未加载');
                return;
            }
            
            const container = document.getElementById('iframe-container');
            container.innerHTML = '';
            
            const iframe = document.createElement('iframe');
            iframe.src = config.services.serviceB.url + '/?test=true';
            iframe.width = '100%';
            iframe.height = '400px';
            iframe.style.border = '1px solid #ddd';
            iframe.style.borderRadius = '4px';
            
            if (config.iframe && config.iframe.sandbox) {
                iframe.sandbox = config.iframe.sandbox;
            }
            
            container.appendChild(iframe);
        }
        
        function clearIframe() {
            document.getElementById('iframe-container').innerHTML = '';
        }
        
        function runDiagnosis() {
            if (window.diagnoseEnvironment) {
                const result = window.diagnoseEnvironment();
                document.getElementById('diagnosis-output').textContent = 
                    JSON.stringify(result, null, 2);
            } else {
                document.getElementById('diagnosis-output').textContent = 
                    '诊断工具不可用';
            }
        }
        
        function exportConfig() {
            const exportData = {
                config: config,
                environment: window.CURRENT_ENV_INFO,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], 
                { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `adaptive-config-${Date.now()}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
        }
        
        // 存储检测函数
        function isSessionStorageAvailable() {
            try {
                const test = '__test__';
                sessionStorage.setItem(test, test);
                sessionStorage.removeItem(test);
                return true;
            } catch (e) {
                return false;
            }
        }
        
        function isLocalStorageAvailable() {
            try {
                const test = '__test__';
                localStorage.setItem(test, test);
                localStorage.removeItem(test);
                return true;
            } catch (e) {
                return false;
            }
        }
        
        function isInIframe() {
            try {
                return window.self !== window.top;
            } catch (e) {
                return true;
            }
        }
        
        function isCrossOriginIframe() {
            try {
                if (!isInIframe()) return false;
                const parentOrigin = window.parent.location.origin;
                return parentOrigin !== window.location.origin;
            } catch (e) {
                return true;
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initConfig, 100); // 等待配置文件加载
        });
        
        // 定期更新状态
        setInterval(updateStorageStatus, 5000);
    </script>
</body>
</html>
