// 权限申请系统JavaScript

const API_BASE = '/auth';
let authToken = localStorage.getItem('authToken');
let currentUser = null;

// 获取正确的登录页面路径
function getLoginPath() {
    const currentUrl = window.location.href;

    // Vue/Node开发环境检测：端口5173或3000
    const isVueDev = currentUrl.includes(':5173') || currentUrl.includes(':3000');

    const loginPath = isVueDev ? '/static/login.html' : '/login.html';



    return loginPath;
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    if (!authToken) {
        window.location.href = getLoginPath();
        return;
    }

    initializePage();
});

async function initializePage() {
    try {
        await loadCurrentUser();
        await loadDocuments();
        await loadMyRequests();
        await loadMyPermissions();
        
        // 绑定表单提交事件
        document.getElementById('permissionRequestForm').addEventListener('submit', handlePermissionRequest);
        
    } catch (error) {
        console.error('初始化页面失败:', error);
        showAlert('页面初始化失败: ' + error.message, 'error');
    }
}

// 加载当前用户信息
async function loadCurrentUser() {
    try {
        const response = await fetch(`${API_BASE}/users/me`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取用户信息失败');
        }

        currentUser = await response.json();
        document.getElementById('currentUsername').textContent = currentUser.user_name;
        document.getElementById('currentUserEmail').textContent = currentUser.email;
        
    } catch (error) {
        console.error('加载用户信息失败:', error);
        throw error;
    }
}

// 加载文档列表
async function loadDocuments() {
    try {
        const response = await fetch(`${API_BASE}/documents`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取文档列表失败');
        }

        const documents = await response.json();
        const select = document.getElementById('documentSelect');
        
        // 清空现有选项
        select.innerHTML = '<option value="">请选择文档</option>';
        
        documents.forEach(doc => {
            const option = document.createElement('option');
            option.value = doc.id;
            option.textContent = `${doc.title} (${doc.file_name})`;
            select.appendChild(option);
        });
        
    } catch (error) {
        console.error('加载文档列表失败:', error);
        showAlert('加载文档列表失败: ' + error.message, 'error');
    }
}

// 处理权限申请提交
async function handlePermissionRequest(event) {
    event.preventDefault();
    
    const documentId = document.getElementById('documentSelect').value;
    const permissionType = document.getElementById('permissionType').value;
    const reason = document.getElementById('requestReason').value;
    
    if (!documentId || !permissionType || !reason.trim()) {
        showAlert('请填写完整的申请信息', 'error');
        return;
    }
    
    try {
        showLoading(true);
        
        const response = await fetch(`${API_BASE}/permission-requests?document_id=${documentId}&permission_type=${permissionType}&reason=${encodeURIComponent(reason)}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '提交申请失败');
        }

        showAlert('权限申请提交成功，请等待审核', 'success');
        
        // 清空表单
        document.getElementById('permissionRequestForm').reset();
        
        // 重新加载申请列表
        await loadMyRequests();
        
    } catch (error) {
        console.error('提交权限申请失败:', error);
        showAlert('提交申请失败: ' + error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// 加载我的权限申请
async function loadMyRequests() {
    try {
        const response = await fetch(`${API_BASE}/permission-requests`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取权限申请失败');
        }

        const requests = await response.json();
        
        // 过滤出当前用户的申请
        const myRequests = requests.filter(req => req.requester_id === currentuser.user_id);
        
        displayMyRequests(myRequests);
        
    } catch (error) {
        console.error('加载权限申请失败:', error);
        document.getElementById('myRequestsList').innerHTML = '<p>加载失败: ' + error.message + '</p>';
    }
}

// 显示我的权限申请
function displayMyRequests(requests) {
    const container = document.getElementById('myRequestsList');
    
    if (!requests || requests.length === 0) {
        container.innerHTML = '<p>暂无权限申请记录</p>';
        return;
    }
    
    let html = `
        <table class="table">
            <thead>
                <tr>
                    <th>文档</th>
                    <th>权限类型</th>
                    <th>申请理由</th>
                    <th>申请时间</th>
                    <th>状态</th>
                    <th>审核意见</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    requests.forEach(req => {
        const statusClass = req.status === 'pending' ? 'status-pending' : 
                           req.status === 'approved' ? 'status-approved' : 'status-rejected';
        const statusText = req.status === 'pending' ? '待审核' : 
                          req.status === 'approved' ? '已批准' : '已拒绝';
        
        html += `
            <tr>
                <td>${req.document?.title || '未知文档'}</td>
                <td>${getPermissionTypeText(req.permission_type)}</td>
                <td>${req.reason}</td>
                <td>${new Date(req.created_at).toLocaleDateString()}</td>
                <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                <td>${req.review_comment || '-'}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table>';
    container.innerHTML = html;
}

// 加载我的文档权限
async function loadMyPermissions() {
    try {
        // 这里需要一个获取用户权限的API，暂时显示提示信息
        document.getElementById('myPermissionsList').innerHTML = '<p>功能开发中，请联系管理员查看您的文档权限</p>';
        
    } catch (error) {
        console.error('加载文档权限失败:', error);
        document.getElementById('myPermissionsList').innerHTML = '<p>加载失败: ' + error.message + '</p>';
    }
}

// 工具函数
function getPermissionTypeText(type) {
    const types = {
        'read': '查看',
        'write': '编辑',
        'delete': '删除',
        'share': '分享'
    };
    return types[type] || type;
}

function showLoading(show) {
    document.getElementById('loading').style.display = show ? 'flex' : 'none';
}

function showAlert(message, type = 'success') {
    // 移除现有的提示
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    const mainContent = document.querySelector('.main-content');
    mainContent.insertBefore(alertDiv, mainContent.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

function logout() {
    // 清除本地存储的认证信息
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');

    // 显示退出提示
    showAlert('已成功退出登录', 'success');

    // 延迟跳转到登录页面
    setTimeout(() => {
        window.location.href = getLoginPath();
    }, 1000);
}
