<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 文档管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(-45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        @keyframes gradientBG {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 几何图形背景 */
        .geometric-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 8s ease-in-out infinite;
        }

        .shape.circle {
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
        }

        .shape.triangle {
            width: 0;
            height: 0;
            background: transparent;
            border-left: 25px solid transparent;
            border-right: 25px solid transparent;
            border-bottom: 43px solid rgba(255, 255, 255, 0.3);
        }

        .shape.square {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(45deg);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }

        .register-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 50px 40px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 10;
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .logo {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
            animation: bounce 2s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .logo-icon i {
            font-size: 2.5rem;
            color: white;
        }

        .logo h1 {
            color: white;
            font-size: 2rem;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .logo p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .input-wrapper {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 18px 20px 18px 55px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-group input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .input-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
        }

        .password-strength {
            margin-top: 8px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .password-strength-bar {
            height: 100%;
            width: 0%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-weak { background: #ff6b6b; width: 33%; }
        .strength-medium { background: #feca57; width: 66%; }
        .strength-strong { background: #48dbfb; width: 100%; }

        .register-button {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
        }

        .register-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }

        .register-button:active {
            transform: translateY(-1px);
        }

        .register-button.loading {
            pointer-events: none;
        }

        .button-text {
            transition: opacity 0.3s ease;
        }

        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .loading-spinner.show {
            opacity: 1;
        }

        .loading .button-text {
            opacity: 0;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            font-size: 0.95rem;
            display: none;
            animation: messageSlide 0.3s ease-out;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.error {
            background: rgba(244, 67, 54, 0.2);
            color: #ffcdd2;
            border: 1px solid rgba(244, 67, 54, 0.3);
        }

        .message.success {
            background: rgba(76, 175, 80, 0.2);
            color: #c8e6c9;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .links {
            text-align: center;
            margin-top: 30px;
        }

        .links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            display: inline-block;
            padding: 10px 20px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .links a:hover {
            color: white;
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .terms {
            text-align: center;
            margin-top: 20px;
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .terms a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
        }

        .terms a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .register-container {
                margin: 20px;
                padding: 40px 30px;
            }
            
            .logo h1 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- 几何图形背景 -->
    <div class="geometric-bg" id="geometricBg"></div>

    <div class="register-container">
        <div class="logo">
            <div class="logo-icon">
                <i class="fas fa-user-plus"></i>
            </div>
            <h1>加入我们</h1>
            <p>创建您的文档管理账户</p>
        </div>

        <div id="registerMessage" class="message"></div>

        <form id="registerForm">
            <div class="form-group">
                <label for="username">
                    <i class="fas fa-user"></i> 用户名
                </label>
                <div class="input-wrapper">
                    <input type="text" id="username" placeholder="请输入用户名" required>
                    <i class="fas fa-user input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i> 邮箱地址
                </label>
                <div class="input-wrapper">
                    <input type="email" id="email" placeholder="请输入邮箱地址" required>
                    <i class="fas fa-envelope input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i> 密码
                </label>
                <div class="input-wrapper">
                    <input type="password" id="password" placeholder="请输入密码（至少6位）" required>
                    <i class="fas fa-lock input-icon"></i>
                </div>
                <div class="password-strength">
                    <div class="password-strength-bar" id="strengthBar"></div>
                </div>
            </div>

            <div class="form-group">
                <label for="confirmPassword">
                    <i class="fas fa-lock"></i> 确认密码
                </label>
                <div class="input-wrapper">
                    <input type="password" id="confirmPassword" placeholder="请再次输入密码" required>
                    <i class="fas fa-lock input-icon"></i>
                </div>
            </div>

            <button type="submit" class="register-button" id="registerButton">
                <span class="button-text">
                    <i class="fas fa-user-plus"></i> 立即注册
                </span>
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
            </button>
        </form>

        <div class="terms">
            注册即表示您同意我们的 
            <a href="#" onclick="alert('服务条款')">服务条款</a> 和 
            <a href="#" onclick="alert('隐私政策')">隐私政策</a>
        </div>

        <div class="links">
            <a href="login.html">
                <i class="fas fa-sign-in-alt"></i> 已有账户？立即登录
            </a>
        </div>
    </div>

    <script>
        // 创建几何图形背景
        function createGeometricShapes() {
            const container = document.getElementById('geometricBg');
            const shapes = ['circle', 'triangle', 'square'];
            const shapeCount = 20;

            for (let i = 0; i < shapeCount; i++) {
                const shape = document.createElement('div');
                const shapeType = shapes[Math.floor(Math.random() * shapes.length)];
                
                shape.className = `shape ${shapeType}`;
                
                if (shapeType === 'circle' || shapeType === 'square') {
                    const size = Math.random() * 50 + 20;
                    shape.style.width = size + 'px';
                    shape.style.height = size + 'px';
                }
                
                shape.style.left = Math.random() * 100 + '%';
                shape.style.top = Math.random() * 100 + '%';
                shape.style.animationDelay = Math.random() * 8 + 's';
                shape.style.animationDuration = (Math.random() * 4 + 4) + 's';
                
                container.appendChild(shape);
            }
        }

        // 密码强度检测
        function checkPasswordStrength(password) {
            const strengthBar = document.getElementById('strengthBar');
            let strength = 0;
            
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            strengthBar.className = 'password-strength-bar';
            
            if (strength >= 1 && strength <= 2) {
                strengthBar.classList.add('strength-weak');
            } else if (strength === 3) {
                strengthBar.classList.add('strength-medium');
            } else if (strength >= 4) {
                strengthBar.classList.add('strength-strong');
            }
        }

        // 显示消息
        function showMessage(message, type = 'error') {
            const messageEl = document.getElementById('registerMessage');
            messageEl.textContent = message;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    messageEl.style.display = 'none';
                }, 3000);
            }
        }

        // 清除消息
        function clearMessage() {
            document.getElementById('registerMessage').style.display = 'none';
        }

        // 注册处理
        async function handleRegister(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const registerButton = document.getElementById('registerButton');

            // 基本验证
            if (!username || !email || !password || !confirmPassword) {
                showMessage('请填写所有字段', 'error');
                return;
            }

            if (password.length < 6) {
                showMessage('密码长度至少6位', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('两次输入的密码不一致', 'error');
                return;
            }

            // 显示加载状态
            registerButton.classList.add('loading');
            clearMessage();

            try {
                const response = await fetch('/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_name: username,
                        email,
                        password
                    })
                });

                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.detail || '注册失败');
                }

                showMessage('注册成功！正在跳转到登录页面...', 'success');
                
                // 清空表单
                document.getElementById('registerForm').reset();
                document.getElementById('strengthBar').className = 'password-strength-bar';
                
                // 延迟跳转到登录页面
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);

            } catch (error) {
                showMessage(error.message, 'error');
            } finally {
                registerButton.classList.remove('loading');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            createGeometricShapes();
            
            // 绑定表单提交事件
            document.getElementById('registerForm').addEventListener('submit', handleRegister);
            
            // 密码强度检测
            document.getElementById('password').addEventListener('input', (e) => {
                checkPasswordStrength(e.target.value);
            });

            // 回车键注册
            document.getElementById('confirmPassword').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    handleRegister(e);
                }
            });
        });
    </script>
</body>
</html>
