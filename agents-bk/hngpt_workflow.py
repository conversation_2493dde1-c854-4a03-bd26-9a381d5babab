#!/usr/bin/env python3
"""
HNGPT专用Agent工作流
基于assembly_doc接口的实现，专门用于HNGPT项目档案和文书档案的处理
"""

import asyncio
import json
import os
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

from agents.workflow_manager import WorkflowFactory, workflow_monitor
from agents.base_agent import BaseAgent
from agents.hngpt_base_agent import WorkflowContext
from utils.task_manager import TaskManager, TaskStatus
from utils.logger import log


class HNGPTWorkflow:
    """HNGPT专用工作流处理器"""
    
    def __init__(self, task_manager: TaskManager, qa_instance):
        self.task_manager = task_manager
        self.qa = qa_instance
        self.workflow_id = str(uuid.uuid4())
        
    async def process_hngpt_request(
        self,
        task_id: str,
        action: str,
        project_name: str,
        urls: List[str],
        user_name: str
    ) -> Dict[str, Any]:
        """
        处理HNGPT请求
        
        Args:
            task_id: 任务ID
            action: 操作类型（项目档案/文书档案）
            project_name: 项目名称
            urls: 文档URL列表
            user_name: 用户名
            
        Returns:
            处理结果字典
        """
        try:
            log.info(f"开始处理HNGPT任务 {task_id}: {action} - {project_name}")
            
            # 更新任务状态
            await self.task_manager.update_result(
                task_id,
                status=TaskStatus.RUNNING,
                task_type="hngpt_workflow",
                task_params={
                    "action": action,
                    "project_name": project_name,
                    "urls": urls,
                    "user_name": user_name,
                    "workflow_id": self.workflow_id,
                    "workflow_mode": "hngpt_agent_based"
                }
            )
            
            # ========== 阶段1：文件预处理和验证 ==========
            log.info(f"阶段1: 文件预处理和验证 - {len(urls)} 个文件")
            
            # 检查并生成缺失的JSON文件
            missing_urls = []
            for url in urls:
                json_url = os.path.splitext(url)[0] + ".json"
                if not self.qa.minio.check_file_exists(json_url):
                    missing_urls.append(url)
            
            # 生成缺失的JSON文件
            if missing_urls:
                log.info(f"需要生成JSON文件: {len(missing_urls)} 个")
                try:
                    txt_result = await self.qa.process_txt_info(
                        urls=missing_urls,
                        enable_ocr=False,
                        enable_seal=False,
                        enable_handwrite=False,
                        task_id=task_id,
                        task_manager=self.task_manager,
                        project_name=project_name
                    )
                    if txt_result.get("failed"):
                        log.warning(f"部分文件JSON生成失败: {txt_result['failed']}")
                except Exception as e:
                    log.error(f"JSON生成阶段错误: {str(e)}")
            
            # ========== 阶段2：使用Agent工作流处理每个文件 ==========
            log.info(f"阶段2: Agent工作流处理")
            
            processed_files = []
            failed_files = []
            all_extracted_data = []
            
            for url in urls:
                file_result = await self._process_single_file(
                    task_id, url, project_name, action
                )
                
                if file_result["success"]:
                    processed_files.append(file_result["data"])
                    if file_result["data"].get("extracted_data"):
                        all_extracted_data.append(file_result["data"]["extracted_data"])
                else:
                    failed_files.append(file_result["data"])
            
            # ========== 阶段3：汇总和提取项目级信息 ==========
            log.info(f"阶段3: 汇总和提取项目级信息")
            
            project_extract_result = await self._extract_project_info(
                task_id, action, project_name, all_extracted_data, user_name
            )
            
            # ========== 阶段4：存储结果 ==========
            log.info(f"阶段4: 存储结果")
            
            storage_result = await self._store_results(
                task_id, project_name, action, project_extract_result, processed_files
            )
            
            # 计算总体结果
            total_files = len(urls)
            success_files = len(processed_files)
            failed_files_count = len(failed_files)
            
            # 更新最终状态
            if success_files == total_files:
                final_status = TaskStatus.COMPLETED
                final_message = f"HNGPT处理完成: {success_files}/{total_files} 文件成功"
            elif success_files > 0:
                final_status = TaskStatus.COMPLETED
                final_message = f"HNGPT部分完成: {success_files}/{total_files} 文件成功"
            else:
                final_status = TaskStatus.FAILED
                final_message = f"HNGPT处理失败: 所有文件处理失败"
            
            await self.task_manager.update_result(
                task_id,
                status=final_status,
                message=final_message
            )
            
            return {
                "success": success_files > 0,
                "task_id": task_id,
                "workflow_id": self.workflow_id,
                "action": action,
                "project_name": project_name,
                "total_files": total_files,
                "processed_files": success_files,
                "failed_files": failed_files_count,
                "project_extract": project_extract_result,
                "storage_result": storage_result,
                "processed_file_details": processed_files,
                "failed_file_details": failed_files
            }
            
        except Exception as e:
            log.error(f"HNGPT工作流处理异常: {str(e)}")
            
            await self.task_manager.update_result(
                task_id,
                status=TaskStatus.FAILED,
                message=f"HNGPT工作流异常: {str(e)}"
            )
            
            return {
                "success": False,
                "error": str(e),
                "task_id": task_id,
                "workflow_id": self.workflow_id
            }
    
    async def _process_single_file(
        self, 
        task_id: str, 
        url: str, 
        project_name: str, 
        action: str
    ) -> Dict[str, Any]:
        """处理单个文件"""
        
        file_start_time = datetime.now()
        json_url = os.path.splitext(url)[0] + ".json"
        
        try:
            # 初始化文件处理状态
            await self.task_manager.update_file_progress(
                task_id, url, "processing", 0, "开始处理文件",
                stage="init", stage_detail="初始化文件处理"
            )
            
            # 检查JSON文件是否存在
            if not self.qa.minio.check_file_exists(json_url):
                error_msg = "对应的JSON文件不存在，需要先进行OCR处理"
                await self.task_manager.update_file_progress(
                    task_id, url, "failed", 0, "JSON文件不存在",
                    error=error_msg, stage="validation", stage_detail="文件验证失败"
                )
                return {
                    "success": False,
                    "data": {
                        "url": url,
                        "error": error_msg,
                        "duration": (datetime.now() - file_start_time).total_seconds()
                    }
                }
            
            # 下载并解析JSON文件
            await self.task_manager.update_file_progress(
                task_id, url, "processing", 25, "下载和解析JSON文件",
                stage="parse", stage_detail="解析文件内容"
            )
            
            json_path = self.qa.minio.download_file(json_url)
            if not json_path:
                error_msg = "JSON文件下载失败"
                await self.task_manager.update_file_progress(
                    task_id, url, "failed", 25, "文件下载失败",
                    error=error_msg, stage="download", stage_detail="下载失败"
                )
                return {
                    "success": False,
                    "data": {
                        "url": url,
                        "error": error_msg,
                        "duration": (datetime.now() - file_start_time).total_seconds()
                    }
                }
            
            with open(json_path, 'r', encoding='utf-8') as f:
                file_content = json.load(f)
            
            # 生成或获取doc_id
            doc_id = file_content.get("doc_id")
            if not doc_id:
                import hashlib
                doc_id_source = f"{project_name}_{url}"
                doc_id = hashlib.md5(doc_id_source.encode('utf-8')).hexdigest()
                file_content["doc_id"] = doc_id
            
            # 创建并执行Agent工作流
            await self.task_manager.update_file_progress(
                task_id, url, "processing", 50, "执行Agent工作流",
                stage="vectorize", stage_detail="向量化和存储"
            )
            
            # 创建文档处理工作流
            workflow = WorkflowFactory.create_document_workflow(self.task_manager)
            workflow_id = workflow.workflow_id
            
            # 注册工作流到监控器
            workflow_monitor.register_workflow(workflow_id, workflow)
            
            try:
                # 执行Agent工作流
                workflow_result = await workflow.process_document(
                    task_id, json_url, file_content
                )
                
                file_duration = (datetime.now() - file_start_time).total_seconds()
                
                if workflow_result["success"]:
                    await self.task_manager.update_file_progress(
                        task_id, url, "completed", 100,
                        f"处理完成，耗时 {file_duration:.1f}秒",
                        stage="completed", stage_detail="处理成功"
                    )
                    
                    return {
                        "success": True,
                        "data": {
                            "url": url,
                            "doc_id": doc_id,
                            "workflow_id": workflow_id,
                            "extracted_data": workflow_result.get("extracted_data", {}),
                            "duration": file_duration,
                            "start_time": file_start_time.isoformat(),
                            "end_time": datetime.now().isoformat()
                        }
                    }
                else:
                    error_msg = workflow_result.get('error', 'Agent工作流处理失败')
                    await self.task_manager.update_file_progress(
                        task_id, url, "failed", 100,
                        f"处理失败，耗时 {file_duration:.1f}秒",
                        error=error_msg, stage="agent_workflow_failed",
                        stage_detail="Agent工作流失败"
                    )
                    
                    return {
                        "success": False,
                        "data": {
                            "url": url,
                            "doc_id": doc_id,
                            "error": error_msg,
                            "workflow_id": workflow_id,
                            "duration": file_duration
                        }
                    }
                    
            finally:
                # 注销工作流
                workflow_monitor.unregister_workflow(workflow_id)
                
        except Exception as e:
            file_duration = (datetime.now() - file_start_time).total_seconds()
            error_msg = f"处理异常: {str(e)}"
            
            await self.task_manager.update_file_progress(
                task_id, url, "failed", 0,
                f"处理异常，耗时 {file_duration:.1f}秒",
                error=error_msg, stage="exception", stage_detail="处理异常"
            )
            
            return {
                "success": False,
                "data": {
                    "url": url,
                    "error": error_msg,
                    "duration": file_duration
                }
            }
    
    async def _extract_project_info(
        self,
        task_id: str,
        action: str,
        project_name: str,
        all_extracted_data: List[Dict],
        user_name: str
    ) -> Dict[str, Any]:
        """提取项目级信息"""
        
        try:
            log.info(f"开始提取项目级信息: {action} - {project_name}")
            
            # 这里实现项目级信息提取逻辑
            # 基于所有文件的extracted_data进行汇总分析
            
            if action == "项目档案":
                return await self._extract_project_archive_info(
                    task_id, project_name, all_extracted_data, user_name
                )
            elif action == "文书档案":
                return await self._extract_document_archive_info(
                    task_id, project_name, all_extracted_data, user_name
                )
            else:
                # 默认按项目档案处理
                return await self._extract_project_archive_info(
                    task_id, project_name, all_extracted_data, user_name
                )
                
        except Exception as e:
            log.error(f"项目信息提取异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "extracted_at": datetime.now().isoformat()
            }
    
    async def _extract_project_archive_info(
        self,
        task_id: str,
        project_name: str,
        all_extracted_data: List[Dict],
        user_name: str
    ) -> Dict[str, Any]:
        """提取项目档案信息"""
        
        # 实现项目档案的具体提取逻辑
        # 这里可以调用LLM进行智能提取
        
        return {
            "success": True,
            "type": "项目档案",
            "project_name": project_name,
            "extracted_info": {
                "project_summary": "项目概要信息",
                "key_technologies": ["技术1", "技术2"],
                "project_phases": ["阶段1", "阶段2"],
                "stakeholders": ["相关方1", "相关方2"]
            },
            "source_documents": len(all_extracted_data),
            "extracted_by": user_name,
            "extracted_at": datetime.now().isoformat()
        }
    
    async def _extract_document_archive_info(
        self,
        task_id: str,
        project_name: str,
        all_extracted_data: List[Dict],
        user_name: str
    ) -> Dict[str, Any]:
        """提取文书档案信息"""
        
        # 实现文书档案的具体提取逻辑
        
        return {
            "success": True,
            "type": "文书档案",
            "project_name": project_name,
            "extracted_info": {
                "document_summary": "文书概要信息",
                "key_points": ["要点1", "要点2"],
                "document_types": ["类型1", "类型2"],
                "related_parties": ["相关方1", "相关方2"]
            },
            "source_documents": len(all_extracted_data),
            "extracted_by": user_name,
            "extracted_at": datetime.now().isoformat()
        }
    
    async def _store_results(
        self,
        task_id: str,
        project_name: str,
        action: str,
        project_extract_result: Dict,
        processed_files: List[Dict]
    ) -> Dict[str, Any]:
        """存储处理结果"""
        
        try:
            # 这里实现结果存储逻辑
            # 可以存储到数据库或其他持久化存储
            
            log.info(f"存储HNGPT处理结果: {project_name}")
            
            return {
                "success": True,
                "stored_at": datetime.now().isoformat(),
                "project_name": project_name,
                "action": action,
                "files_stored": len(processed_files)
            }
            
        except Exception as e:
            log.error(f"结果存储异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "attempted_at": datetime.now().isoformat()
            }


class HNGPTWorkflowFactory:
    """HNGPT工作流工厂"""
    
    @staticmethod
    def create_hngpt_workflow(task_manager: TaskManager, qa_instance) -> HNGPTWorkflow:
        """创建HNGPT工作流实例"""
        return HNGPTWorkflow(task_manager, qa_instance)
