#!/usr/bin/env python3
"""
HNGPT Agent系统与TaskManager集成适配器
"""

import asyncio
from typing import Dict, Any, Optional
from datetime import datetime

try:
    from .hngpt_base_agent import WorkflowContext, AgentStatus
    from .hngpt_workflow_dag import HNGPTWorkflowDAG, WorkflowStatus
    from .hngpt_workflow_executor import HNGPTWorkflowExecutor, ExecutionConfig, ExecutionResult
except ImportError:
    from agents.hngpt_base_agent import WorkflowContext, AgentStatus
    from agents.hngpt_workflow_dag import HNGPTWorkflowDAG, WorkflowStatus
    from agents.hngpt_workflow_executor import HNGPTWorkflowExecutor, ExecutionConfig, ExecutionResult

class HNGPTTaskIntegration:
    """HNGPT Agent系统与TaskManager集成适配器"""
    
    def __init__(self, task_manager):
        self.task_manager = task_manager
        self.active_workflows: Dict[str, HNGPTWorkflowDAG] = {}
        self.active_executors: Dict[str, HNGPTWorkflowExecutor] = {}
    
    async def create_workflow_task(
        self,
        task_id: str,
        dag: HNGPTWorkflowDAG,
        context: WorkflowContext,
        execution_config: ExecutionConfig = None
    ) -> ExecutionResult:
        """创建并执行HNGPT工作流任务"""
        
        # 注册工作流
        self.active_workflows[task_id] = dag

        # 创建任务（如果不存在）
        if task_id not in self.task_manager.results:
            from utils.task_manager import TaskResult, TaskStatus
            task_result = TaskResult(task_id=task_id, total_files=1)
            task_result.task_type = "hngpt_workflow"
            task_result.task_params = {
                "workflow_id": dag.workflow_id,
                "workflow_name": dag.name,
                "workflow_mode": "hngpt_agent_based",
                "project_name": context.get_data("project_name", ""),
                "action": context.get_data("action", ""),
                "user_name": context.get_data("user_name", "admin")
            }
            self.task_manager.results[task_id] = task_result

        # 初始化任务状态
        await self.task_manager.update_result(
            task_id=task_id,
            status=None  # 保持当前状态
        )
        
        # 更新工作流信息
        execution_levels = dag.build_execution_levels()
        await self.task_manager.update_workflow_status(
            task_id=task_id,
            workflow_id=dag.workflow_id,
            workflow_status=WorkflowStatus.RUNNING.value,
            execution_levels=[[node_id for node_id in level] for level in execution_levels]
        )
        
        # 初始化所有Agent状态，按层级顺序
        for level_index, level_nodes in enumerate(execution_levels):
            for node_id in level_nodes:
                node = dag.nodes[node_id]
                await self.task_manager.update_agent_progress(
                    task_id=task_id,
                    agent_id=node_id,
                    agent_name=node.agent.metadata.name,
                    status=AgentStatus.PENDING.value,
                    progress=0,
                    message="等待执行",
                    level=level_index  # 使用实际的层级索引
                )
        
        # 创建执行器
        if execution_config is None:
            execution_config = ExecutionConfig(
                max_concurrent_agents=5,
                timeout_seconds=3600,
                progress_callback=self._create_progress_callback(task_id)
            )
        else:
            # 添加进度回调
            execution_config.progress_callback = self._create_progress_callback(task_id)
        
        executor = HNGPTWorkflowExecutor(dag, execution_config)
        self.active_executors[task_id] = executor
        
        try:
            # 执行工作流
            result = await executor.execute(context)
            
            # 更新最终状态
            final_status = "completed" if result.success else "failed"
            await self.task_manager.update_workflow_status(
                task_id=task_id,
                workflow_status=final_status
            )
            
            # 更新任务状态
            from utils.task_manager import TaskStatus
            await self.task_manager.update_result(
                task_id=task_id,
                status=TaskStatus.COMPLETED if result.success else TaskStatus.FAILED,
                error=result.error_message if not result.success else None
            )
            
            return result
            
        except Exception as e:
            # 处理异常
            await self.task_manager.update_workflow_status(
                task_id=task_id,
                workflow_status="failed"
            )
            
            from utils.task_manager import TaskStatus
            await self.task_manager.update_result(
                task_id=task_id,
                status=TaskStatus.FAILED,
                error=str(e)
            )
            
            raise e
        
        finally:
            # 清理资源
            self.active_workflows.pop(task_id, None)
            self.active_executors.pop(task_id, None)
    
    def _create_progress_callback(self, task_id: str):
        """创建进度回调函数"""
        async def progress_callback(progress_data: Dict[str, Any]):
            """工作流进度回调"""
            try:
                # 更新整体进度
                overall_progress = progress_data.get("progress", 0)
                current_level = progress_data.get("current_level", 0)
                total_levels = progress_data.get("total_levels", 1)
                
                # 更新任务进度
                await self.task_manager.update_result(
                    task_id=task_id,
                    status=None  # 保持当前状态
                )
                
                # 如果有当前执行的Agent信息，更新Agent状态
                if "current_agents" in progress_data:
                    for agent_info in progress_data["current_agents"]:
                        await self.task_manager.update_agent_progress(
                            task_id=task_id,
                            agent_id=agent_info["agent_id"],
                            agent_name=agent_info["agent_name"],
                            status=agent_info["status"],
                            progress=agent_info.get("progress", 0),
                            message=agent_info.get("message", ""),
                            error=agent_info.get("error"),
                            execution_time=agent_info.get("execution_time", 0.0),
                            level=current_level
                        )
                
            except Exception as e:
                print(f"进度回调失败: {e}")
        
        return progress_callback
    
    async def update_agent_status(
        self,
        task_id: str,
        agent_id: str,
        status: AgentStatus,
        progress: int = 0,
        message: str = "",
        error: str = None,
        execution_time: float = 0.0
    ):
        """更新Agent状态（供执行器调用）"""
        dag = self.active_workflows.get(task_id)
        if dag and agent_id in dag.nodes:
            node = dag.nodes[agent_id]
            await self.task_manager.update_agent_progress(
                task_id=task_id,
                agent_id=agent_id,
                agent_name=node.agent.metadata.name,
                status=status.value,
                progress=progress,
                message=message,
                error=error,
                execution_time=execution_time,
                level=node.level
            )
    
    def get_workflow_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流状态"""
        dag = self.active_workflows.get(task_id)
        executor = self.active_executors.get(task_id)
        
        if dag and executor:
            return {
                "workflow_id": dag.workflow_id,
                "workflow_name": dag.name,
                "status": dag.status.value,
                "nodes_count": len(dag.nodes),
                "execution_levels": len(dag.execution_levels),
                "completed_agents": len(executor.completed_agents),
                "failed_agents": len(executor.failed_agents),
                "execution_summary": executor.get_execution_summary()
            }
        
        return None

# 创建全局集成实例（需要在app.py中初始化）
hngpt_task_integration = None

def initialize_hngpt_integration(task_manager):
    """初始化HNGPT任务集成"""
    global hngpt_task_integration
    hngpt_task_integration = HNGPTTaskIntegration(task_manager)
    return hngpt_task_integration
