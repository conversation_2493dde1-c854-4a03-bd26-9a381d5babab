#!/usr/bin/env python3
"""
结构化信息提取Agent - 负责提取文档的结构化信息
"""

import asyncio
import re
from typing import Dict, Any, List, Optional
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class StructureExtractAgent(BaseAgent):
    """结构化信息提取Agent - 提取章节、段落、列表、表格等结构信息"""
    
    def __init__(self):
        super().__init__(name="结构化信息提取Agent")
        self.add_dependency("extract_basic")  # 依赖基础信息提取Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """提取文档结构化信息"""
        await asyncio.sleep(0.6)  # 模拟提取时间
        
        # 获取基础信息
        parse_info = context.get_extracted_data("parse_info", {})
        parsed_pages = context.get_extracted_data("parsed_pages", [])
        document_structure = context.get_extracted_data("document_structure", {})
        
        full_text = parse_info.get("full_text", "")
        
        # 提取结构化信息
        chapter_structure = self._extract_chapters(full_text, parsed_pages)
        section_structure = self._extract_sections(full_text, parsed_pages)
        paragraph_structure = self._extract_paragraphs(parsed_pages)
        list_structure = self._extract_lists(full_text, parsed_pages)
        table_structure = self._extract_tables(full_text, parsed_pages)
        
        # 构建文档大纲
        document_outline = self._build_document_outline(
            chapter_structure, section_structure, paragraph_structure
        )
        
        # 分析内容层次
        content_hierarchy = self._analyze_content_hierarchy(
            chapter_structure, section_structure, list_structure
        )
        
        # 提取引用和参考
        references = self._extract_references(full_text)
        
        return {
            "structure_info": {
                "extraction_time": datetime.now().isoformat(),
                "total_chapters": len(chapter_structure.get("chapters", [])),
                "total_sections": len(section_structure.get("sections", [])),
                "total_paragraphs": len(paragraph_structure.get("paragraphs", [])),
                "total_lists": len(list_structure.get("lists", [])),
                "total_tables": len(table_structure.get("tables", [])),
                "structure_complexity": self._assess_structure_complexity(
                    chapter_structure, section_structure, list_structure
                )
            },
            "chapter_structure": chapter_structure,
            "section_structure": section_structure,
            "paragraph_structure": paragraph_structure,
            "list_structure": list_structure,
            "table_structure": table_structure,
            "document_outline": document_outline,
            "content_hierarchy": content_hierarchy,
            "references": references,
            "structure_summary": {
                "status": "completed",
                "structure_detected": True,
                "outline_generated": len(document_outline.get("outline", [])) > 0,
                "hierarchy_depth": content_hierarchy.get("max_depth", 0),
                "next_stage": "extract_entities"
            }
        }
    
    def _extract_chapters(self, full_text: str, parsed_pages: List[Dict]) -> Dict[str, Any]:
        """提取章节信息"""
        chapter_info = {
            "chapters": [],
            "chapter_count": 0,
            "numbering_style": "unknown"
        }
        
        # 章节模式
        chapter_patterns = [
            (r'第([一二三四五六七八九十]+)章\s*([^\n]*)', "chinese_number"),
            (r'第(\d+)章\s*([^\n]*)', "arabic_number"),
            (r'Chapter\s+(\d+)[:\s]*([^\n]*)', "english"),
            (r'([一二三四五六七八九十]+)、\s*([^\n]*)', "chinese_item")
        ]
        
        chapters = []
        for pattern, style in chapter_patterns:
            matches = re.finditer(pattern, full_text, re.IGNORECASE)
            for match in matches:
                chapter_num = match.group(1)
                chapter_title = match.group(2).strip()
                
                # 查找章节在哪一页
                page_number = self._find_content_page(match.start(), parsed_pages)
                
                chapters.append({
                    "number": chapter_num,
                    "title": chapter_title,
                    "full_title": match.group(0),
                    "page_number": page_number,
                    "position": match.start(),
                    "numbering_style": style
                })
        
        # 按位置排序
        chapters.sort(key=lambda x: x["position"])
        
        # 确定主要编号风格
        if chapters:
            style_counts = {}
            for chapter in chapters:
                style = chapter["numbering_style"]
                style_counts[style] = style_counts.get(style, 0) + 1
            
            main_style = max(style_counts.items(), key=lambda x: x[1])[0]
            chapter_info["numbering_style"] = main_style
            
            # 只保留主要风格的章节
            chapter_info["chapters"] = [c for c in chapters if c["numbering_style"] == main_style]
        
        chapter_info["chapter_count"] = len(chapter_info["chapters"])
        return chapter_info
    
    def _extract_sections(self, full_text: str, parsed_pages: List[Dict]) -> Dict[str, Any]:
        """提取小节信息"""
        section_info = {
            "sections": [],
            "section_count": 0,
            "numbering_style": "unknown"
        }
        
        # 小节模式
        section_patterns = [
            (r'(\d+\.\d+)\s*([^\n]*)', "decimal"),
            (r'(\d+\.\d+\.\d+)\s*([^\n]*)', "three_level"),
            (r'\(([一二三四五六七八九十]+)\)\s*([^\n]*)', "chinese_paren"),
            (r'\((\d+)\)\s*([^\n]*)', "arabic_paren")
        ]
        
        sections = []
        for pattern, style in section_patterns:
            matches = re.finditer(pattern, full_text)
            for match in matches:
                section_num = match.group(1)
                section_title = match.group(2).strip()
                
                # 过滤掉太短或太长的标题
                if len(section_title) < 2 or len(section_title) > 100:
                    continue
                
                page_number = self._find_content_page(match.start(), parsed_pages)
                
                sections.append({
                    "number": section_num,
                    "title": section_title,
                    "full_title": match.group(0),
                    "page_number": page_number,
                    "position": match.start(),
                    "numbering_style": style,
                    "level": self._determine_section_level(section_num, style)
                })
        
        # 按位置排序
        sections.sort(key=lambda x: x["position"])
        
        # 确定主要编号风格
        if sections:
            style_counts = {}
            for section in sections:
                style = section["numbering_style"]
                style_counts[style] = style_counts.get(style, 0) + 1
            
            if style_counts:
                main_style = max(style_counts.items(), key=lambda x: x[1])[0]
                section_info["numbering_style"] = main_style
                section_info["sections"] = [s for s in sections if s["numbering_style"] == main_style]
        
        section_info["section_count"] = len(section_info["sections"])
        return section_info
    
    def _extract_paragraphs(self, parsed_pages: List[Dict]) -> Dict[str, Any]:
        """提取段落信息"""
        paragraph_info = {
            "paragraphs": [],
            "paragraph_count": 0,
            "average_length": 0
        }
        
        paragraphs = []
        total_length = 0
        
        for page in parsed_pages:
            content = page.get("cleaned_content", "")
            page_number = page.get("page_number", 0)
            
            # 按换行分割段落
            lines = content.split('\n')
            
            for line in lines:
                line = line.strip()
                if len(line) > 20:  # 认为长度超过20的行为段落
                    paragraphs.append({
                        "content": line[:200] + "..." if len(line) > 200 else line,
                        "full_content": line,
                        "length": len(line),
                        "page_number": page_number,
                        "word_count": len(re.findall(r'[\u4e00-\u9fff]', line)) + len(re.findall(r'\b[a-zA-Z]+\b', line))
                    })
                    total_length += len(line)
        
        paragraph_info["paragraphs"] = paragraphs
        paragraph_info["paragraph_count"] = len(paragraphs)
        paragraph_info["average_length"] = total_length / max(1, len(paragraphs))
        
        return paragraph_info
    
    def _extract_lists(self, full_text: str, parsed_pages: List[Dict]) -> Dict[str, Any]:
        """提取列表信息"""
        list_info = {
            "lists": [],
            "list_count": 0,
            "list_types": []
        }
        
        # 列表模式
        list_patterns = [
            (r'^[•·\-\*]\s+(.+)$', "bullet"),
            (r'^\d+\.\s+(.+)$', "numbered"),
            (r'^\d+\)\s+(.+)$', "numbered_paren"),
            (r'^[a-zA-Z]\.\s+(.+)$', "lettered"),
            (r'^[一二三四五六七八九十]+\.\s+(.+)$', "chinese_numbered")
        ]
        
        lists = []
        current_list = None
        
        for page in parsed_pages:
            content = page.get("cleaned_content", "")
            page_number = page.get("page_number", 0)
            lines = content.split('\n')
            
            for line in lines:
                line = line.strip()
                if not line:
                    # 空行结束当前列表
                    if current_list and len(current_list["items"]) > 1:
                        lists.append(current_list)
                    current_list = None
                    continue
                
                # 检查是否匹配列表模式
                matched = False
                for pattern, list_type in list_patterns:
                    match = re.match(pattern, line, re.MULTILINE)
                    if match:
                        item_content = match.group(1).strip()
                        
                        # 如果是新的列表或不同类型，创建新列表
                        if not current_list or current_list["type"] != list_type:
                            if current_list and len(current_list["items"]) > 1:
                                lists.append(current_list)
                            
                            current_list = {
                                "type": list_type,
                                "page_number": page_number,
                                "items": [],
                                "start_line": line
                            }
                        
                        current_list["items"].append({
                            "content": item_content,
                            "full_line": line
                        })
                        matched = True
                        break
                
                if not matched and current_list:
                    # 非列表行，结束当前列表
                    if len(current_list["items"]) > 1:
                        lists.append(current_list)
                    current_list = None
        
        # 处理最后一个列表
        if current_list and len(current_list["items"]) > 1:
            lists.append(current_list)
        
        # 统计列表类型
        list_types = {}
        for lst in lists:
            list_type = lst["type"]
            list_types[list_type] = list_types.get(list_type, 0) + 1
        
        list_info["lists"] = lists
        list_info["list_count"] = len(lists)
        list_info["list_types"] = list(list_types.keys())
        
        return list_info
    
    def _extract_tables(self, full_text: str, parsed_pages: List[Dict]) -> Dict[str, Any]:
        """提取表格信息（简化版）"""
        table_info = {
            "tables": [],
            "table_count": 0,
            "table_indicators": []
        }
        
        # 查找表格指示词
        table_indicators = [
            "表格", "表", "Table", "数据表", "统计表", "明细表"
        ]
        
        tables = []
        for indicator in table_indicators:
            pattern = rf'{indicator}[^\n]*'
            matches = re.finditer(pattern, full_text, re.IGNORECASE)
            
            for match in matches:
                table_title = match.group(0).strip()
                page_number = self._find_content_page(match.start(), parsed_pages)
                
                tables.append({
                    "title": table_title,
                    "page_number": page_number,
                    "position": match.start(),
                    "indicator": indicator
                })
        
        # 查找可能的表格结构（简单的行列结构）
        lines = full_text.split('\n')
        for i, line in enumerate(lines):
            # 查找包含多个分隔符的行（可能是表格）
            if line.count('|') >= 2 or line.count('\t') >= 2:
                page_number = self._find_line_page(i, parsed_pages)
                tables.append({
                    "title": f"表格结构 (第{i+1}行)",
                    "page_number": page_number,
                    "position": i,
                    "indicator": "structure_detected",
                    "content_preview": line[:100]
                })
        
        table_info["tables"] = tables[:10]  # 限制数量
        table_info["table_count"] = len(table_info["tables"])
        table_info["table_indicators"] = list(set(t["indicator"] for t in tables))
        
        return table_info
    
    def _build_document_outline(self, chapters: Dict, sections: Dict, paragraphs: Dict) -> Dict[str, Any]:
        """构建文档大纲"""
        outline_info = {
            "outline": [],
            "depth": 0,
            "structure_type": "flat"
        }
        
        outline = []
        
        # 添加章节到大纲
        for chapter in chapters.get("chapters", []):
            outline_item = {
                "level": 1,
                "type": "chapter",
                "number": chapter["number"],
                "title": chapter["title"],
                "page": chapter["page_number"],
                "children": []
            }
            
            # 查找该章节下的小节
            chapter_sections = []
            for section in sections.get("sections", []):
                if section["page_number"] >= chapter["page_number"]:
                    # 简单判断：如果小节在章节之后的页面，认为属于该章节
                    chapter_sections.append({
                        "level": 2,
                        "type": "section",
                        "number": section["number"],
                        "title": section["title"],
                        "page": section["page_number"]
                    })
            
            outline_item["children"] = chapter_sections[:5]  # 限制数量
            outline.append(outline_item)
        
        # 如果没有章节，直接添加小节
        if not chapters.get("chapters"):
            for section in sections.get("sections", []):
                outline.append({
                    "level": 1,
                    "type": "section",
                    "number": section["number"],
                    "title": section["title"],
                    "page": section["page_number"],
                    "children": []
                })
        
        outline_info["outline"] = outline
        outline_info["depth"] = max(2 if any(item["children"] for item in outline) else 1, 1)
        outline_info["structure_type"] = "hierarchical" if outline_info["depth"] > 1 else "flat"
        
        return outline_info
    
    def _analyze_content_hierarchy(self, chapters: Dict, sections: Dict, lists: Dict) -> Dict[str, Any]:
        """分析内容层次结构"""
        hierarchy = {
            "max_depth": 1,
            "hierarchy_type": "simple",
            "level_distribution": {},
            "structure_quality": "medium"
        }
        
        levels = []
        
        # 统计各级别内容
        if chapters.get("chapters"):
            levels.append("chapter")
            hierarchy["level_distribution"]["chapters"] = len(chapters["chapters"])
        
        if sections.get("sections"):
            levels.append("section")
            hierarchy["level_distribution"]["sections"] = len(sections["sections"])
        
        if lists.get("lists"):
            levels.append("list")
            hierarchy["level_distribution"]["lists"] = len(lists["lists"])
        
        # 确定层次深度
        hierarchy["max_depth"] = len(levels)
        
        # 确定层次类型
        if hierarchy["max_depth"] >= 3:
            hierarchy["hierarchy_type"] = "complex"
            hierarchy["structure_quality"] = "high"
        elif hierarchy["max_depth"] == 2:
            hierarchy["hierarchy_type"] = "moderate"
            hierarchy["structure_quality"] = "medium"
        else:
            hierarchy["hierarchy_type"] = "simple"
            hierarchy["structure_quality"] = "low"
        
        return hierarchy
    
    def _extract_references(self, full_text: str) -> Dict[str, Any]:
        """提取引用和参考信息"""
        references = {
            "references": [],
            "reference_count": 0,
            "reference_types": []
        }
        
        # 引用模式
        reference_patterns = [
            (r'\[(\d+)\]', "numbered"),
            (r'\(([^)]+\d{4}[^)]*)\)', "author_year"),
            (r'参考文献[：:]([^\n]+)', "bibliography"),
            (r'引用[：:]([^\n]+)', "citation")
        ]
        
        found_references = []
        reference_types = set()
        
        for pattern, ref_type in reference_patterns:
            matches = re.finditer(pattern, full_text)
            for match in matches:
                found_references.append({
                    "content": match.group(1),
                    "full_match": match.group(0),
                    "type": ref_type,
                    "position": match.start()
                })
                reference_types.add(ref_type)
        
        references["references"] = found_references[:20]  # 限制数量
        references["reference_count"] = len(found_references)
        references["reference_types"] = list(reference_types)
        
        return references
    
    def _find_content_page(self, position: int, parsed_pages: List[Dict]) -> int:
        """根据文本位置查找对应页面"""
        current_pos = 0
        for page in parsed_pages:
            content = page.get("cleaned_content", "")
            if current_pos <= position < current_pos + len(content):
                return page.get("page_number", 1)
            current_pos += len(content) + 1  # +1 for newline
        return 1
    
    def _find_line_page(self, line_number: int, parsed_pages: List[Dict]) -> int:
        """根据行号查找对应页面"""
        current_line = 0
        for page in parsed_pages:
            content = page.get("cleaned_content", "")
            lines_in_page = content.count('\n') + 1
            if current_line <= line_number < current_line + lines_in_page:
                return page.get("page_number", 1)
            current_line += lines_in_page
        return 1
    
    def _determine_section_level(self, section_num: str, style: str) -> int:
        """确定小节级别"""
        if style == "decimal":
            return section_num.count('.') + 1
        elif style == "three_level":
            return section_num.count('.') + 1
        else:
            return 2
    
    def _assess_structure_complexity(self, chapters: Dict, sections: Dict, lists: Dict) -> str:
        """评估结构复杂度"""
        score = 0
        
        if chapters.get("chapter_count", 0) > 0:
            score += 2
        if sections.get("section_count", 0) > 5:
            score += 2
        if lists.get("list_count", 0) > 3:
            score += 1
        
        if score >= 4:
            return "high"
        elif score >= 2:
            return "medium"
        else:
            return "low"
    
    def get_stage_name(self) -> str:
        return "extract_structure"
    
    def get_progress_percentage(self) -> int:
        return 60
    
    def get_stage_description(self) -> str:
        return "提取章节、段落、列表等结构化信息"
