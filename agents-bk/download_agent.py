#!/usr/bin/env python3
"""
文件下载Agent - 负责下载和获取文件内容
"""

import asyncio
import json
import os
import hashlib
from typing import Dict, Any
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class DownloadAgent(BaseAgent):
    """文件下载Agent - 下载和验证文件内容"""
    
    def __init__(self):
        super().__init__(name="文件下载Agent")
        self.add_dependency("init")  # 依赖初始化Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """下载和处理文件内容"""
        await asyncio.sleep(0.3)  # 模拟下载时间
        
        # 获取初始化信息
        init_info = context.get_extracted_data("init_info", {})
        processing_dir = init_info.get("processing_dir", "/tmp")
        
        # 模拟文件下载过程
        download_info = await self._simulate_download(context.file_url, processing_dir)
        
        # 验证文件内容
        content_validation = self._validate_content(context.file_content)
        
        # 计算文件哈希
        file_hash = self._calculate_file_hash(context.file_content)
        
        # 提取文件元信息
        file_metadata = self._extract_file_metadata(context.file_url, context.file_content)
        
        return {
            "download_info": download_info,
            "content_validation": content_validation,
            "file_hash": file_hash,
            "file_metadata": file_metadata,
            "download_summary": {
                "status": "completed",
                "download_time": datetime.now().isoformat(),
                "file_size": len(str(context.file_content)),
                "content_valid": content_validation["is_valid"],
                "next_stage": "validate"
            }
        }
    
    async def _simulate_download(self, file_url: str, processing_dir: str) -> Dict[str, Any]:
        """模拟文件下载过程"""
        # 模拟不同类型的下载
        if file_url.startswith("http"):
            download_method = "http_download"
            await asyncio.sleep(0.2)  # 模拟网络下载时间
        elif file_url.startswith("docs/"):
            download_method = "minio_download"
            await asyncio.sleep(0.1)  # 模拟MinIO下载时间
        else:
            download_method = "local_file"
        
        # 生成本地文件路径
        filename = os.path.basename(file_url)
        local_path = os.path.join(processing_dir, filename)
        
        return {
            "download_method": download_method,
            "source_url": file_url,
            "local_path": local_path,
            "download_success": True,
            "download_duration": 0.3,
            "file_exists": True,
            "download_timestamp": datetime.now().isoformat()
        }
    
    def _validate_content(self, file_content: Dict[str, Any]) -> Dict[str, Any]:
        """验证文件内容格式"""
        validation_results = {
            "is_valid": True,
            "validation_errors": [],
            "content_type": "unknown",
            "structure_valid": True
        }
        
        if not file_content:
            validation_results["is_valid"] = False
            validation_results["validation_errors"].append("文件内容为空")
            return validation_results
        
        # 检查是否为JSON格式的文档
        if "pages" in file_content:
            validation_results["content_type"] = "document_json"
            pages = file_content.get("pages", [])
            
            if not isinstance(pages, list):
                validation_results["is_valid"] = False
                validation_results["validation_errors"].append("pages字段必须是数组")
            elif len(pages) == 0:
                validation_results["validation_errors"].append("文档页面为空")
            else:
                # 验证页面结构
                for i, page in enumerate(pages):
                    if not isinstance(page, dict):
                        validation_results["validation_errors"].append(f"第{i+1}页格式错误")
                    elif "content" not in page:
                        validation_results["validation_errors"].append(f"第{i+1}页缺少content字段")
        
        # 检查元数据
        if "metadata" in file_content:
            metadata = file_content["metadata"]
            if not isinstance(metadata, dict):
                validation_results["validation_errors"].append("metadata字段格式错误")
        
        validation_results["is_valid"] = len(validation_results["validation_errors"]) == 0
        return validation_results
    
    def _calculate_file_hash(self, file_content: Dict[str, Any]) -> Dict[str, Any]:
        """计算文件内容哈希"""
        content_str = json.dumps(file_content, sort_keys=True, ensure_ascii=False)
        
        # 计算多种哈希
        md5_hash = hashlib.md5(content_str.encode('utf-8')).hexdigest()
        sha256_hash = hashlib.sha256(content_str.encode('utf-8')).hexdigest()
        
        return {
            "md5": md5_hash,
            "sha256": sha256_hash,
            "content_length": len(content_str),
            "hash_algorithm": "md5+sha256",
            "calculated_at": datetime.now().isoformat()
        }
    
    def _extract_file_metadata(self, file_url: str, file_content: Dict[str, Any]) -> Dict[str, Any]:
        """提取文件元信息"""
        metadata = {
            "original_url": file_url,
            "file_extension": os.path.splitext(file_url)[1],
            "estimated_pages": 0,
            "estimated_words": 0,
            "has_metadata": False,
            "content_language": "zh-CN"  # 默认中文
        }
        
        # 统计页面和字数
        if "pages" in file_content:
            pages = file_content["pages"]
            metadata["estimated_pages"] = len(pages)
            
            total_words = 0
            for page in pages:
                content = page.get("content", "")
                total_words += len(content)
            metadata["estimated_words"] = total_words
        
        # 检查是否有元数据
        if "metadata" in file_content:
            metadata["has_metadata"] = True
            doc_metadata = file_content["metadata"]
            metadata.update({
                "title": doc_metadata.get("title", ""),
                "author": doc_metadata.get("author", ""),
                "creation_date": doc_metadata.get("creation_date", "")
            })
        
        return metadata
    
    def get_stage_name(self) -> str:
        return "download"
    
    def get_progress_percentage(self) -> int:
        return 15
    
    def get_stage_description(self) -> str:
        return "下载文件内容并进行基础验证"
