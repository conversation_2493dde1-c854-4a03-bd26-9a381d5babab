#!/usr/bin/env python3
"""
文档解析Agent - 负责解析文档结构和内容
"""

import asyncio
import re
from typing import Dict, Any, List
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class ParseAgent(BaseAgent):
    """文档解析Agent - 解析文档结构和提取文本内容"""
    
    def __init__(self):
        super().__init__(name="文档解析Agent")
        self.add_dependency("validate")  # 依赖验证Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """解析文档结构和内容"""
        await asyncio.sleep(0.4)  # 模拟解析时间
        
        # 获取验证信息
        validation_report = context.get_extracted_data("validation_report", {})
        if not validation_report.get("overall_valid", False):
            raise ValueError("文档验证失败，无法进行解析")
        
        pages = context.file_content.get("pages", [])
        
        # 解析页面内容
        parsed_pages = self._parse_pages(pages)
        
        # 提取全文内容
        full_text = self._extract_full_text(parsed_pages)
        
        # 分析文档结构
        document_structure = self._analyze_document_structure(full_text)
        
        # 提取文本统计信息
        text_statistics = self._calculate_text_statistics(full_text, parsed_pages)
        
        # 识别内容类型
        content_classification = self._classify_content(full_text)
        
        return {
            "parse_info": {
                "parse_time": datetime.now().isoformat(),
                "total_pages": len(parsed_pages),
                "total_text_length": len(full_text),
                "parsing_success": True,
                "full_text": full_text
            },
            "parsed_pages": parsed_pages,
            "document_structure": document_structure,
            "text_statistics": text_statistics,
            "content_classification": content_classification,
            "parse_summary": {
                "status": "completed",
                "pages_processed": len(parsed_pages),
                "text_extracted": len(full_text) > 0,
                "structure_identified": len(document_structure.get("sections", [])) > 0,
                "next_stage": "extract_basic"
            }
        }
    
    def _parse_pages(self, pages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析页面内容"""
        parsed_pages = []
        
        for i, page in enumerate(pages):
            content = page.get("content", "")
            page_number = page.get("page_number", i + 1)
            
            # 清理和标准化内容
            cleaned_content = self._clean_content(content)
            
            # 分析页面结构
            page_structure = self._analyze_page_structure(cleaned_content)
            
            # 提取页面元素
            page_elements = self._extract_page_elements(cleaned_content)
            
            parsed_page = {
                "page_number": page_number,
                "original_content": content,
                "cleaned_content": cleaned_content,
                "content_length": len(cleaned_content),
                "structure": page_structure,
                "elements": page_elements,
                "has_content": bool(cleaned_content.strip()),
                "processing_time": datetime.now().isoformat()
            }
            
            parsed_pages.append(parsed_page)
        
        return parsed_pages
    
    def _clean_content(self, content: str) -> str:
        """清理文本内容"""
        if not content:
            return ""
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', content)
        
        # 移除特殊字符
        cleaned = re.sub(r'[^\w\s\u4e00-\u9fff.,;:!?()[]{}""''—\-]', '', cleaned)
        
        # 标准化换行
        cleaned = re.sub(r'\n+', '\n', cleaned)
        
        return cleaned.strip()
    
    def _analyze_page_structure(self, content: str) -> Dict[str, Any]:
        """分析页面结构"""
        structure = {
            "has_title": False,
            "has_headings": False,
            "has_paragraphs": False,
            "has_lists": False,
            "line_count": 0,
            "paragraph_count": 0
        }
        
        if not content:
            return structure
        
        lines = content.split('\n')
        structure["line_count"] = len(lines)
        
        # 检查标题（通常是第一行且较短）
        if lines and len(lines[0]) < 100:
            structure["has_title"] = True
        
        # 检查标题和段落
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否为标题
            if (re.match(r'^第[一二三四五六七八九十\d]+章', line) or
                re.match(r'^\d+\.', line) or
                re.match(r'^[一二三四五六七八九十]+、', line)):
                structure["has_headings"] = True
            
            # 检查是否为列表
            if (re.match(r'^[•·\-\*]\s', line) or
                re.match(r'^\d+\)', line) or
                re.match(r'^\([一二三四五六七八九十\d]+\)', line)):
                structure["has_lists"] = True
            
            # 计算段落
            if len(line) > 20:  # 认为长度超过20的行为段落
                structure["paragraph_count"] += 1
        
        structure["has_paragraphs"] = structure["paragraph_count"] > 0
        
        return structure
    
    def _extract_page_elements(self, content: str) -> Dict[str, List[str]]:
        """提取页面元素"""
        elements = {
            "titles": [],
            "headings": [],
            "paragraphs": [],
            "lists": [],
            "numbers": [],
            "dates": []
        }
        
        if not content:
            return elements
        
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 提取标题
            if re.match(r'^第[一二三四五六七八九十\d]+章', line):
                elements["titles"].append(line)
            
            # 提取标题
            elif re.match(r'^\d+\.', line) or re.match(r'^[一二三四五六七八九十]+、', line):
                elements["headings"].append(line)
            
            # 提取列表项
            elif (re.match(r'^[•·\-\*]\s', line) or
                  re.match(r'^\d+\)', line) or
                  re.match(r'^\([一二三四五六七八九十\d]+\)', line)):
                elements["lists"].append(line)
            
            # 提取段落
            elif len(line) > 20:
                elements["paragraphs"].append(line[:100] + "..." if len(line) > 100 else line)
        
        # 提取数字
        numbers = re.findall(r'\d+\.?\d*[万亿%元]', content)
        elements["numbers"] = list(set(numbers))[:10]  # 去重并限制数量
        
        # 提取日期
        dates = re.findall(r'\d{4}年\d{1,2}月\d{1,2}日|\d{4}-\d{1,2}-\d{1,2}', content)
        elements["dates"] = list(set(dates))[:5]  # 去重并限制数量
        
        return elements
    
    def _extract_full_text(self, parsed_pages: List[Dict[str, Any]]) -> str:
        """提取全文内容"""
        full_text_parts = []
        
        for page in parsed_pages:
            cleaned_content = page.get("cleaned_content", "")
            if cleaned_content:
                full_text_parts.append(cleaned_content)
        
        return "\n".join(full_text_parts)
    
    def _analyze_document_structure(self, full_text: str) -> Dict[str, Any]:
        """分析文档整体结构"""
        structure = {
            "sections": [],
            "hierarchy": {},
            "toc_detected": False,
            "structure_type": "unknown"
        }
        
        if not full_text:
            return structure
        
        # 提取章节
        chapters = re.findall(r'第[一二三四五六七八九十\d]+章[^\n]*', full_text)
        sections = re.findall(r'\d+\.\d*[^\n]*', full_text)
        
        structure["sections"] = chapters + sections[:10]  # 限制数量
        
        # 判断结构类型
        if chapters:
            structure["structure_type"] = "章节式"
        elif sections:
            structure["structure_type"] = "条目式"
        elif re.search(r'目录|contents', full_text, re.IGNORECASE):
            structure["toc_detected"] = True
            structure["structure_type"] = "目录式"
        else:
            structure["structure_type"] = "自由式"
        
        # 构建层次结构
        structure["hierarchy"] = {
            "chapters": len(chapters),
            "sections": len(sections),
            "estimated_depth": min(3, max(1, len(chapters) + (1 if sections else 0)))
        }
        
        return structure
    
    def _calculate_text_statistics(self, full_text: str, parsed_pages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算文本统计信息"""
        stats = {
            "total_characters": len(full_text),
            "total_words": 0,
            "total_sentences": 0,
            "total_paragraphs": 0,
            "average_page_length": 0,
            "language_detected": "zh-CN"
        }
        
        if not full_text:
            return stats
        
        # 计算词数（中文按字符计算）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', full_text))
        english_words = len(re.findall(r'\b[a-zA-Z]+\b', full_text))
        stats["total_words"] = chinese_chars + english_words
        
        # 计算句子数
        sentences = re.split(r'[。！？.!?]', full_text)
        stats["total_sentences"] = len([s for s in sentences if s.strip()])
        
        # 计算段落数
        paragraphs = full_text.split('\n')
        stats["total_paragraphs"] = len([p for p in paragraphs if p.strip()])
        
        # 计算平均页面长度
        if parsed_pages:
            total_length = sum(page.get("content_length", 0) for page in parsed_pages)
            stats["average_page_length"] = total_length / len(parsed_pages)
        
        # 语言检测
        if chinese_chars > english_words:
            stats["language_detected"] = "zh-CN"
        elif english_words > chinese_chars:
            stats["language_detected"] = "en-US"
        else:
            stats["language_detected"] = "mixed"
        
        return stats
    
    def _classify_content(self, full_text: str) -> Dict[str, Any]:
        """分类文档内容"""
        classification = {
            "document_type": "unknown",
            "content_category": "general",
            "technical_level": "medium",
            "confidence": 0.5
        }
        
        if not full_text:
            return classification
        
        text_lower = full_text.lower()
        
        # 文档类型识别
        if any(keyword in text_lower for keyword in ['方案', '设计', '架构']):
            classification["document_type"] = "技术方案"
            classification["confidence"] = 0.8
        elif any(keyword in text_lower for keyword in ['报告', '总结', '分析']):
            classification["document_type"] = "报告"
            classification["confidence"] = 0.7
        elif any(keyword in text_lower for keyword in ['手册', '指南', '说明']):
            classification["document_type"] = "说明文档"
            classification["confidence"] = 0.7
        elif any(keyword in text_lower for keyword in ['合同', '协议', '条款']):
            classification["document_type"] = "合同协议"
            classification["confidence"] = 0.9
        
        # 内容分类
        if any(keyword in text_lower for keyword in ['技术', '系统', '开发', '代码']):
            classification["content_category"] = "技术"
        elif any(keyword in text_lower for keyword in ['管理', '流程', '制度', '规范']):
            classification["content_category"] = "管理"
        elif any(keyword in text_lower for keyword in ['财务', '预算', '成本', '费用']):
            classification["content_category"] = "财务"
        
        # 技术水平评估
        technical_keywords = ['api', 'database', '数据库', '算法', '架构', 'framework']
        tech_count = sum(1 for keyword in technical_keywords if keyword in text_lower)
        
        if tech_count >= 3:
            classification["technical_level"] = "high"
        elif tech_count >= 1:
            classification["technical_level"] = "medium"
        else:
            classification["technical_level"] = "low"
        
        return classification
    
    def get_stage_name(self) -> str:
        return "parse"
    
    def get_progress_percentage(self) -> int:
        return 30
    
    def get_stage_description(self) -> str:
        return "解析文档结构和提取文本内容"
