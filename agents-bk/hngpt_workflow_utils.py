#!/usr/bin/env python3
"""
HNGPT工作流实用工具
"""

import json
import yaml
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

try:
    from .hngpt_base_agent import HNGPTBaseAgent, WorkflowContext
    from .hngpt_workflow_dag import HNGPTWorkflowDAG
    from .hngpt_workflow_executor import ExecutionResult
except ImportError:
    from agents.hngpt_base_agent import HNGPTBaseAgent, WorkflowContext
    from agents.hngpt_workflow_dag import HNGPTWorkflowDAG
    from agents.hngpt_workflow_executor import ExecutionResult

logger = logging.getLogger(__name__)

class HNGPTWorkflowValidator:
    """工作流验证器"""
    
    @staticmethod
    def validate_dag(dag: HNGPTWorkflowDAG) -> Dict[str, Any]:
        """验证DAG结构"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }
        
        try:
            # 基础验证
            if not dag.nodes:
                validation_result["errors"].append("工作流为空，没有任何Agent")
                validation_result["valid"] = False
                return validation_result
            
            # 检查循环依赖
            try:
                dag.build_execution_levels()
            except ValueError as e:
                validation_result["errors"].append(f"检测到循环依赖: {str(e)}")
                validation_result["valid"] = False
                return validation_result
            
            # 检查数据流
            HNGPTWorkflowValidator._validate_data_flow(dag, validation_result)
            
            # 检查性能优化机会
            HNGPTWorkflowValidator._check_optimization_opportunities(dag, validation_result)
            
            # 检查Agent配置
            HNGPTWorkflowValidator._validate_agent_configs(dag, validation_result)
            
        except Exception as e:
            validation_result["errors"].append(f"验证过程中发生异常: {str(e)}")
            validation_result["valid"] = False
        
        return validation_result
    
    @staticmethod
    def _validate_data_flow(dag: HNGPTWorkflowDAG, result: Dict[str, Any]):
        """验证数据流"""
        for node_id, node in dag.nodes.items():
            agent = node.agent
            
            # 检查必需输入是否有提供者
            for input_schema in agent.input_schemas:
                if input_schema.required and input_schema.name not in ["task_id", "workflow_id"]:
                    providers = []
                    
                    # 检查依赖的Agent是否能提供这个数据
                    for dep_id in node.dependencies:
                        if dep_id in dag.nodes:
                            dep_agent = dag.nodes[dep_id].agent
                            for output_schema in dep_agent.output_schemas:
                                if output_schema.name == input_schema.name:
                                    providers.append(dep_id)
                    
                    if not providers:
                        result["warnings"].append({
                            "type": "missing_data_provider",
                            "message": f"Agent {agent.metadata.name} 需要输入 '{input_schema.name}' 但没有依赖能提供",
                            "agent": agent.metadata.name,
                            "missing_input": input_schema.name
                        })
    
    @staticmethod
    def _check_optimization_opportunities(dag: HNGPTWorkflowDAG, result: Dict[str, Any]):
        """检查优化机会"""
        execution_levels = dag.execution_levels
        
        if not execution_levels:
            return
        
        # 检查并行化机会
        sequential_levels = [level for level in execution_levels if len(level) == 1]
        if len(sequential_levels) > len(execution_levels) * 0.7:
            result["suggestions"].append({
                "type": "parallelization",
                "message": "大部分Agent顺序执行，考虑增加并行处理以提高性能",
                "sequential_ratio": len(sequential_levels) / len(execution_levels)
            })
        
        # 检查长链路
        if len(execution_levels) > 10:
            result["suggestions"].append({
                "type": "chain_length",
                "message": f"执行链路较长({len(execution_levels)}层)，考虑拆分或优化",
                "chain_length": len(execution_levels)
            })
    
    @staticmethod
    def _validate_agent_configs(dag: HNGPTWorkflowDAG, result: Dict[str, Any]):
        """验证Agent配置"""
        for node_id, node in dag.nodes.items():
            agent = node.agent
            
            # 检查超时配置
            if agent.timeout_seconds > 3600:  # 1小时
                result["warnings"].append({
                    "type": "long_timeout",
                    "message": f"Agent {agent.metadata.name} 超时时间过长({agent.timeout_seconds}秒)",
                    "agent": agent.metadata.name,
                    "timeout": agent.timeout_seconds
                })
            
            # 检查重试配置
            retry_config = agent.retry_config
            if retry_config.get("max_retries", 0) > 5:
                result["warnings"].append({
                    "type": "excessive_retries",
                    "message": f"Agent {agent.metadata.name} 重试次数过多({retry_config['max_retries']})",
                    "agent": agent.metadata.name,
                    "max_retries": retry_config["max_retries"]
                })

class HNGPTWorkflowExporter:
    """工作流导出器"""
    
    @staticmethod
    def export_to_json(dag: HNGPTWorkflowDAG, include_results: bool = False) -> str:
        """导出为JSON格式"""
        workflow_data = dag.get_workflow_info()
        
        if not include_results:
            # 移除执行结果数据
            for node_data in workflow_data["nodes"].values():
                node_data.pop("result", None)
        
        return json.dumps(workflow_data, indent=2, ensure_ascii=False)
    
    @staticmethod
    def export_to_yaml(dag: HNGPTWorkflowDAG) -> str:
        """导出为YAML格式"""
        workflow_data = dag.get_workflow_info()
        
        # 简化数据结构用于YAML导出
        simplified_data = {
            "workflow_id": workflow_data["workflow_id"],
            "name": workflow_data["name"],
            "agents": []
        }
        
        for node_id, node_data in workflow_data["nodes"].items():
            agent_info = node_data["agent_info"]
            simplified_data["agents"].append({
                "id": node_id,
                "name": agent_info["metadata"]["name"],
                "type": agent_info["metadata"]["category"],
                "dependencies": node_data["dependencies"],
                "parallel_group": node_data["parallel_group"],
                "condition": node_data["condition"]
            })
        
        return yaml.dump(simplified_data, default_flow_style=False, allow_unicode=True)
    
    @staticmethod
    def export_mermaid(dag: HNGPTWorkflowDAG) -> str:
        """导出为Mermaid图表"""
        return dag.visualize_mermaid()
    
    @staticmethod
    def export_execution_report(
        dag: HNGPTWorkflowDAG, 
        result: ExecutionResult,
        include_details: bool = True
    ) -> str:
        """导出执行报告"""
        report_lines = [
            f"# 工作流执行报告",
            f"",
            f"**工作流ID**: {dag.workflow_id}",
            f"**工作流名称**: {dag.name}",
            f"**执行状态**: {'✅ 成功' if result.success else '❌ 失败'}",
            f"**执行时间**: {result.execution_time:.2f} 秒",
            f"**开始时间**: {result.start_time}",
            f"**结束时间**: {result.end_time}",
            f"",
            f"## 统计信息",
            f"",
            f"- 总Agent数: {result.statistics.get('total_agents', 0)}",
            f"- 成功完成: {result.statistics.get('completed_agents', 0)}",
            f"- 执行失败: {result.statistics.get('failed_agents', 0)}",
            f"- 跳过执行: {result.statistics.get('skipped_agents', 0)}",
            f"- 平均执行时间: {result.statistics.get('average_agent_time', 0):.2f} 秒",
            f""
        ]
        
        if not result.success:
            report_lines.extend([
                f"## 错误信息",
                f"",
                f"```",
                f"{result.error_message}",
                f"```",
                f""
            ])
        
        if include_details:
            report_lines.extend([
                f"## Agent执行详情",
                f""
            ])
            
            for node_id, agent_result in result.results.items():
                node = dag.nodes.get(node_id)
                if node:
                    agent_name = node.agent.metadata.name
                    status_icon = "✅" if agent_result.success else "❌"
                    
                    report_lines.extend([
                        f"### {status_icon} {agent_name}",
                        f"",
                        f"- **状态**: {agent_result.status.value}",
                        f"- **执行时间**: {agent_result.execution_time:.2f} 秒",
                        f"- **时间戳**: {agent_result.timestamp}",
                        f""
                    ])
                    
                    if agent_result.errors:
                        report_lines.extend([
                            f"**错误信息**:",
                            f"```"
                        ])
                        for error in agent_result.errors:
                            report_lines.append(f"{error['type']}: {error['message']}")
                        report_lines.extend([f"```", f""])
                    
                    if agent_result.warnings:
                        report_lines.extend([
                            f"**警告信息**:",
                            f"```"
                        ])
                        for warning in agent_result.warnings:
                            report_lines.append(f"{warning['type']}: {warning['message']}")
                        report_lines.extend([f"```", f""])
        
        return "\n".join(report_lines)

class HNGPTWorkflowImporter:
    """工作流导入器"""
    
    @staticmethod
    def import_from_json(json_data: str) -> HNGPTWorkflowDAG:
        """从JSON导入工作流"""
        try:
            data = json.loads(json_data)
            return HNGPTWorkflowImporter._build_dag_from_data(data)
        except Exception as e:
            raise ValueError(f"JSON导入失败: {str(e)}")
    
    @staticmethod
    def import_from_yaml(yaml_data: str) -> HNGPTWorkflowDAG:
        """从YAML导入工作流"""
        try:
            data = yaml.safe_load(yaml_data)
            return HNGPTWorkflowImporter._build_dag_from_data(data)
        except Exception as e:
            raise ValueError(f"YAML导入失败: {str(e)}")
    
    @staticmethod
    def _build_dag_from_data(data: Dict[str, Any]) -> HNGPTWorkflowDAG:
        """从数据构建DAG"""
        # 这里需要根据实际的Agent注册表来重建DAG
        # 简化实现，实际使用时需要完善
        dag = HNGPTWorkflowDAG(
            workflow_id=data.get("workflow_id"),
            name=data.get("name", "imported_workflow")
        )
        
        # 注意：这里需要实际的Agent实例化逻辑
        # 当前只是示例结构
        
        return dag

class HNGPTWorkflowMonitor:
    """工作流监控器"""
    
    def __init__(self):
        self.active_workflows: Dict[str, HNGPTWorkflowDAG] = {}
        self.execution_history: List[Dict[str, Any]] = []
    
    def register_workflow(self, dag: HNGPTWorkflowDAG):
        """注册工作流"""
        self.active_workflows[dag.workflow_id] = dag
        logger.info(f"注册工作流: {dag.workflow_id}")
    
    def unregister_workflow(self, workflow_id: str):
        """注销工作流"""
        if workflow_id in self.active_workflows:
            dag = self.active_workflows.pop(workflow_id)
            
            # 保存到历史记录
            self.execution_history.append({
                "workflow_id": workflow_id,
                "name": dag.name,
                "status": dag.status.value,
                "execution_stats": dag.execution_stats,
                "unregistered_at": datetime.now().isoformat()
            })
            
            logger.info(f"注销工作流: {workflow_id}")
    
    def get_active_workflows(self) -> List[Dict[str, Any]]:
        """获取活跃工作流列表"""
        return [
            {
                "workflow_id": dag.workflow_id,
                "name": dag.name,
                "status": dag.status.value,
                "node_count": len(dag.nodes),
                "created_at": dag.created_at
            }
            for dag in self.active_workflows.values()
        ]
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流状态"""
        if workflow_id in self.active_workflows:
            dag = self.active_workflows[workflow_id]
            return dag.get_workflow_info()
        return None
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """获取执行统计"""
        total_workflows = len(self.execution_history) + len(self.active_workflows)
        completed_workflows = len([h for h in self.execution_history if h["status"] == "completed"])
        
        return {
            "total_workflows": total_workflows,
            "active_workflows": len(self.active_workflows),
            "completed_workflows": completed_workflows,
            "success_rate": completed_workflows / total_workflows if total_workflows > 0 else 0,
            "execution_history": self.execution_history[-10:]  # 最近10个
        }

# 全局监控器实例
workflow_monitor = HNGPTWorkflowMonitor()
