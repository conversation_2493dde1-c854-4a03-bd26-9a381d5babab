#!/usr/bin/env python3
"""
业务Agent基类 - 基于实际业务流程的Agent系统
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field

class BusinessAgentStatus(Enum):
    """Agent状态枚举"""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class BusinessAgentResult:
    """Agent执行结果"""
    success: bool
    status: BusinessAgentStatus
    output_data: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    execution_time: float = 0.0
    stage_name: str = ""
    progress_percentage: int = 0

@dataclass
class BusinessAgentContext:
    """业务Agent上下文"""
    task_id: str
    project_name: str
    action: str  # "项目档案" 或 "文书档案"
    file_urls: List[str]
    config: Dict[str, Any]
    extracted_data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def update_extracted_data(self, key: str, value: Any) -> None:
        """更新提取的数据"""
        self.extracted_data[key] = value
        self.updated_at = datetime.now().isoformat()
    
    def get_extracted_data(self, key: str, default: Any = None) -> Any:
        """获取提取的数据"""
        return self.extracted_data.get(key, default)
    
    def get_config_section(self, section: str) -> Dict[str, Any]:
        """获取配置节"""
        if self.action == "项目档案":
            return self.config.get("project_extract", {})
        elif self.action == "文书档案":
            return self.config.get("conference_extract", {})
        else:
            return {}

class BusinessBaseAgent(ABC):
    """业务Agent基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.dependencies: List[str] = []
        self.status = BusinessAgentStatus.NOT_STARTED
        
    def add_dependency(self, dependency: str) -> None:
        """添加依赖"""
        if dependency not in self.dependencies:
            self.dependencies.append(dependency)
    
    def check_dependencies(self, context: BusinessAgentContext) -> bool:
        """检查依赖是否满足"""
        for dep in self.dependencies:
            if dep not in context.extracted_data:
                return False
        return True
    
    async def execute(self, context: BusinessAgentContext) -> BusinessAgentResult:
        """执行Agent"""
        start_time = datetime.now()
        
        try:
            # 检查依赖
            if not self.check_dependencies(context):
                missing_deps = [dep for dep in self.dependencies if dep not in context.extracted_data]
                return BusinessAgentResult(
                    success=False,
                    status=BusinessAgentStatus.FAILED,
                    error_message=f"Missing dependencies: {missing_deps}",
                    stage_name=self.get_stage_name(),
                    progress_percentage=self.get_progress_percentage()
                )
            
            # 更新状态
            self.status = BusinessAgentStatus.RUNNING
            
            # 执行处理逻辑
            output_data = await self.process(context)
            
            # 计算执行时间
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # 更新状态
            self.status = BusinessAgentStatus.COMPLETED
            
            return BusinessAgentResult(
                success=True,
                status=BusinessAgentStatus.COMPLETED,
                output_data=output_data,
                execution_time=execution_time,
                stage_name=self.get_stage_name(),
                progress_percentage=self.get_progress_percentage()
            )
            
        except Exception as e:
            # 计算执行时间
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # 更新状态
            self.status = BusinessAgentStatus.FAILED
            
            return BusinessAgentResult(
                success=False,
                status=BusinessAgentStatus.FAILED,
                error_message=str(e),
                execution_time=execution_time,
                stage_name=self.get_stage_name(),
                progress_percentage=self.get_progress_percentage()
            )
    
    @abstractmethod
    async def process(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """处理逻辑 - 子类必须实现"""
        pass
    
    @abstractmethod
    def get_stage_name(self) -> str:
        """获取阶段名称"""
        pass
    
    @abstractmethod
    def get_progress_percentage(self) -> int:
        """获取进度百分比"""
        pass
    
    @abstractmethod
    def get_stage_description(self) -> str:
        """获取阶段描述"""
        pass
    
    def get_status(self) -> BusinessAgentStatus:
        """获取当前状态"""
        return self.status
    
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status == BusinessAgentStatus.COMPLETED
    
    def is_failed(self) -> bool:
        """是否失败"""
        return self.status == BusinessAgentStatus.FAILED
    
    def reset(self) -> None:
        """重置状态"""
        self.status = BusinessAgentStatus.NOT_STARTED

class BusinessAgentWorkflow:
    """业务Agent工作流管理器"""
    
    def __init__(self, agents: List[BusinessBaseAgent]):
        self.agents = agents
        self.current_agent_index = 0
        self.workflow_status = BusinessAgentStatus.NOT_STARTED
        
    async def execute_workflow(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """执行完整工作流"""
        self.workflow_status = BusinessAgentStatus.RUNNING
        workflow_results = {}
        
        try:
            for i, agent in enumerate(self.agents):
                self.current_agent_index = i
                
                # 执行Agent
                result = await agent.execute(context)
                
                # 记录结果
                workflow_results[agent.get_stage_name()] = {
                    "success": result.success,
                    "execution_time": result.execution_time,
                    "progress": result.progress_percentage,
                    "status": result.status.value
                }
                
                if result.success:
                    # 更新上下文
                    for key, value in result.output_data.items():
                        context.update_extracted_data(key, value)
                else:
                    # Agent失败，停止工作流
                    self.workflow_status = BusinessAgentStatus.FAILED
                    workflow_results["workflow_error"] = {
                        "failed_agent": agent.get_stage_name(),
                        "error_message": result.error_message
                    }
                    break
            else:
                # 所有Agent成功完成
                self.workflow_status = BusinessAgentStatus.COMPLETED
                
        except Exception as e:
            self.workflow_status = BusinessAgentStatus.FAILED
            workflow_results["workflow_exception"] = {
                "error": str(e),
                "current_agent": self.agents[self.current_agent_index].get_stage_name() if self.current_agent_index < len(self.agents) else "unknown"
            }
        
        # 添加工作流总结
        workflow_results["workflow_summary"] = {
            "status": self.workflow_status.value,
            "total_agents": len(self.agents),
            "completed_agents": sum(1 for agent in self.agents if agent.is_completed()),
            "failed_agents": sum(1 for agent in self.agents if agent.is_failed()),
            "overall_progress": self._calculate_overall_progress()
        }
        
        return workflow_results
    
    def _calculate_overall_progress(self) -> int:
        """计算整体进度"""
        if not self.agents:
            return 0
        
        completed_agents = sum(1 for agent in self.agents if agent.is_completed())
        return int((completed_agents / len(self.agents)) * 100)
    
    def get_current_progress(self) -> Dict[str, Any]:
        """获取当前进度"""
        if self.current_agent_index >= len(self.agents):
            current_agent = None
            current_progress = 100
        else:
            current_agent = self.agents[self.current_agent_index]
            current_progress = current_agent.get_progress_percentage()
        
        return {
            "workflow_status": self.workflow_status.value,
            "overall_progress": self._calculate_overall_progress(),
            "current_agent": current_agent.get_stage_name() if current_agent else "completed",
            "current_agent_progress": current_progress,
            "completed_agents": [agent.get_stage_name() for agent in self.agents if agent.is_completed()],
            "failed_agents": [agent.get_stage_name() for agent in self.agents if agent.is_failed()]
        }
    
    def reset_workflow(self) -> None:
        """重置工作流"""
        self.workflow_status = BusinessAgentStatus.NOT_STARTED
        self.current_agent_index = 0
        for agent in self.agents:
            agent.reset()

# 工具函数
def create_business_context(
    task_id: str,
    project_name: str,
    action: str,
    file_urls: List[str],
    config: Dict[str, Any],
    metadata: Optional[Dict[str, Any]] = None
) -> BusinessAgentContext:
    """创建业务Agent上下文"""
    return BusinessAgentContext(
        task_id=task_id,
        project_name=project_name,
        action=action,
        file_urls=file_urls,
        config=config,
        metadata=metadata or {}
    )

def validate_business_context(context: BusinessAgentContext) -> bool:
    """验证业务上下文"""
    required_fields = ["task_id", "project_name", "action", "file_urls", "config"]
    
    for field in required_fields:
        if not hasattr(context, field) or not getattr(context, field):
            return False
    
    # 验证action类型
    if context.action not in ["项目档案", "文书档案"]:
        return False
    
    # 验证file_urls
    if not isinstance(context.file_urls, list) or len(context.file_urls) == 0:
        return False
    
    return True
