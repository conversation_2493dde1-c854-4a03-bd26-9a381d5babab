#!/usr/bin/env python3
"""
关系信息提取Agent - 负责提取实体间关系、语义关系、依赖关系
"""

import asyncio
import re
from typing import Dict, Any, List, Tuple
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class RelationExtractAgent(BaseAgent):
    """关系信息提取Agent - 提取实体间的各种关系"""
    
    def __init__(self):
        super().__init__(name="关系信息提取Agent")
        self.add_dependency("extract_entities")  # 依赖实体信息提取Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """提取实体间关系信息"""
        await asyncio.sleep(0.8)  # 模拟提取时间
        
        # 获取实体信息
        parse_info = context.get_extracted_data("parse_info", {})
        person_entities = context.get_extracted_data("person_entities", {})
        organization_entities = context.get_extracted_data("organization_entities", {})
        location_entities = context.get_extracted_data("location_entities", {})
        entity_associations = context.get_extracted_data("entity_associations", {})
        
        full_text = parse_info.get("full_text", "")
        
        # 提取各类关系
        employment_relations = self._extract_employment_relations(full_text, person_entities, organization_entities)
        location_relations = self._extract_location_relations(full_text, person_entities, organization_entities, location_entities)
        hierarchical_relations = self._extract_hierarchical_relations(full_text, person_entities, organization_entities)
        collaboration_relations = self._extract_collaboration_relations(full_text, person_entities, organization_entities)
        temporal_relations = self._extract_temporal_relations(full_text, context.get_extracted_data("date_entities", {}))
        
        # 语义关系分析
        semantic_relations = self._analyze_semantic_relations(full_text)
        
        # 依赖关系分析
        dependency_relations = self._analyze_dependency_relations(full_text, context.get_extracted_data("structure_info", {}))
        
        # 关系统计和质量评估
        relation_statistics = self._calculate_relation_statistics(
            employment_relations, location_relations, hierarchical_relations,
            collaboration_relations, temporal_relations, semantic_relations, dependency_relations
        )
        
        # 关系网络分析
        relation_network = self._build_relation_network(
            employment_relations, location_relations, hierarchical_relations, collaboration_relations
        )
        
        return {
            "relation_info": {
                "extraction_time": datetime.now().isoformat(),
                "total_relations": relation_statistics["total_count"],
                "relation_types": len(relation_statistics["type_counts"]),
                "average_confidence": relation_statistics["average_confidence"],
                "network_density": relation_network["density"]
            },
            "employment_relations": employment_relations,
            "location_relations": location_relations,
            "hierarchical_relations": hierarchical_relations,
            "collaboration_relations": collaboration_relations,
            "temporal_relations": temporal_relations,
            "semantic_relations": semantic_relations,
            "dependency_relations": dependency_relations,
            "relation_statistics": relation_statistics,
            "relation_network": relation_network,
            "relation_summary": {
                "status": "completed",
                "relations_extracted": relation_statistics["total_count"] > 0,
                "high_confidence_relations": relation_statistics["high_confidence_count"],
                "network_complexity": relation_network["complexity"],
                "next_stage": "merge_info"
            }
        }
    
    def _extract_employment_relations(self, text: str, persons: Dict, organizations: Dict) -> Dict[str, Any]:
        """提取雇佣关系"""
        employment = {
            "relations": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 雇佣关系模式
        employment_patterns = [
            r'([^，。！？\s]{2,8})(担任|任职|就职于|工作于|服务于)([^，。！？\s]{3,20})',
            r'([^，。！？\s]{3,20})(的|之)(总裁|总经理|经理|主任|部长|科长|工程师|专家)([^，。！？\s]{2,8})',
            r'([^，。！？\s]{2,8})(是|为)([^，。！？\s]{3,20})(的|之)(员工|职员|成员)',
            r'([^，。！？\s]{3,20})(聘请|雇佣|招聘)([^，。！？\s]{2,8})'
        ]
        
        person_names = [p.get("name", "") for p in persons.get("entities", [])]
        org_names = [o.get("name", "") for o in organizations.get("entities", [])]
        
        found_relations = []
        
        for pattern in employment_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                groups = match.groups()
                
                # 尝试识别人员和机构
                person = None
                organization = None
                relation_type = "employment"
                
                for group in groups:
                    if group in person_names:
                        person = group
                    elif group in org_names:
                        organization = group
                
                if person and organization:
                    found_relations.append({
                        "person": person,
                        "organization": organization,
                        "relation_type": relation_type,
                        "confidence": 0.8,
                        "evidence": match.group(0),
                        "position": match.start(),
                        "source": "employment_pattern"
                    })
        
        employment["relations"] = found_relations
        employment["count"] = len(found_relations)
        employment["confidence"] = sum(r["confidence"] for r in found_relations) / max(1, len(found_relations))
        
        return employment
    
    def _extract_location_relations(self, text: str, persons: Dict, organizations: Dict, locations: Dict) -> Dict[str, Any]:
        """提取位置关系"""
        location_relations = {
            "relations": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 位置关系模式
        location_patterns = [
            r'([^，。！？\s]{2,20})(位于|坐落于|设在|在)([^，。！？\s]{2,15})',
            r'([^，。！？\s]{2,15})(的|之)([^，。！？\s]{2,20})',
            r'([^，。！？\s]{2,8})(来自|来源于)([^，。！？\s]{2,15})',
            r'([^，。！？\s]{2,15})(地区|区域)(的|之)([^，。！？\s]{2,20})'
        ]
        
        person_names = [p.get("name", "") for p in persons.get("entities", [])]
        org_names = [o.get("name", "") for o in organizations.get("entities", [])]
        location_names = [l.get("name", "") for l in locations.get("entities", [])]
        
        found_relations = []
        
        for pattern in location_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                groups = match.groups()
                
                entity = None
                location = None
                entity_type = "unknown"
                
                for group in groups:
                    if group in person_names:
                        entity = group
                        entity_type = "person"
                    elif group in org_names:
                        entity = group
                        entity_type = "organization"
                    elif group in location_names:
                        location = group
                
                if entity and location:
                    found_relations.append({
                        "entity": entity,
                        "entity_type": entity_type,
                        "location": location,
                        "relation_type": "located_at",
                        "confidence": 0.7,
                        "evidence": match.group(0),
                        "position": match.start(),
                        "source": "location_pattern"
                    })
        
        location_relations["relations"] = found_relations
        location_relations["count"] = len(found_relations)
        location_relations["confidence"] = sum(r["confidence"] for r in found_relations) / max(1, len(found_relations))
        
        return location_relations
    
    def _extract_hierarchical_relations(self, text: str, persons: Dict, organizations: Dict) -> Dict[str, Any]:
        """提取层次关系"""
        hierarchical = {
            "relations": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 层次关系模式
        hierarchy_patterns = [
            r'([^，。！？\s]{2,8})(领导|管理|负责|主管)([^，。！？\s]{2,20})',
            r'([^，。！？\s]{2,20})(隶属于|归属于|属于)([^，。！？\s]{2,20})',
            r'([^，。！？\s]{2,20})(的|之)(上级|下级|子公司|分公司|部门)([^，。！？\s]{2,20})',
            r'([^，。！？\s]{2,8})(是|为)([^，。！？\s]{2,20})(的|之)(负责人|主管|经理)'
        ]
        
        person_names = [p.get("name", "") for p in persons.get("entities", [])]
        org_names = [o.get("name", "") for o in organizations.get("entities", [])]
        
        found_relations = []
        
        for pattern in hierarchy_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                groups = match.groups()
                
                superior = None
                subordinate = None
                relation_type = "hierarchical"
                
                # 简化的层次关系识别
                entities_in_match = []
                for group in groups:
                    if group in person_names or group in org_names:
                        entities_in_match.append(group)
                
                if len(entities_in_match) >= 2:
                    superior = entities_in_match[0]
                    subordinate = entities_in_match[1]
                    
                    # 根据关键词判断关系方向
                    if any(keyword in match.group(0) for keyword in ["领导", "管理", "负责", "主管"]):
                        relation_type = "manages"
                    elif any(keyword in match.group(0) for keyword in ["隶属于", "归属于", "属于"]):
                        relation_type = "belongs_to"
                        superior, subordinate = subordinate, superior  # 交换顺序
                    
                    found_relations.append({
                        "superior": superior,
                        "subordinate": subordinate,
                        "relation_type": relation_type,
                        "confidence": 0.6,
                        "evidence": match.group(0),
                        "position": match.start(),
                        "source": "hierarchy_pattern"
                    })
        
        hierarchical["relations"] = found_relations
        hierarchical["count"] = len(found_relations)
        hierarchical["confidence"] = sum(r["confidence"] for r in found_relations) / max(1, len(found_relations))
        
        return hierarchical
    
    def _extract_collaboration_relations(self, text: str, persons: Dict, organizations: Dict) -> Dict[str, Any]:
        """提取协作关系"""
        collaboration = {
            "relations": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 协作关系模式
        collaboration_patterns = [
            r'([^，。！？\s]{2,20})(与|和|同)([^，。！？\s]{2,20})(合作|协作|配合)',
            r'([^，。！？\s]{2,20})(参与|加入)([^，。！？\s]{2,20})(项目|工作|活动)',
            r'([^，。！？\s]{2,20})(共同|一起|联合)([^，。！？\s]{2,20})',
            r'([^，。！？\s]{2,20})(支持|协助|帮助)([^，。！？\s]{2,20})'
        ]
        
        person_names = [p.get("name", "") for p in persons.get("entities", [])]
        org_names = [o.get("name", "") for o in organizations.get("entities", [])]
        all_entities = person_names + org_names
        
        found_relations = []
        
        for pattern in collaboration_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                groups = match.groups()
                
                entities_in_match = []
                for group in groups:
                    if group in all_entities:
                        entities_in_match.append(group)
                
                if len(entities_in_match) >= 2:
                    entity1 = entities_in_match[0]
                    entity2 = entities_in_match[1]
                    
                    # 判断关系类型
                    relation_type = "collaboration"
                    if "合作" in match.group(0) or "协作" in match.group(0):
                        relation_type = "cooperates_with"
                    elif "参与" in match.group(0) or "加入" in match.group(0):
                        relation_type = "participates_in"
                    elif "支持" in match.group(0) or "协助" in match.group(0):
                        relation_type = "supports"
                    
                    found_relations.append({
                        "entity1": entity1,
                        "entity2": entity2,
                        "relation_type": relation_type,
                        "confidence": 0.6,
                        "evidence": match.group(0),
                        "position": match.start(),
                        "source": "collaboration_pattern"
                    })
        
        collaboration["relations"] = found_relations
        collaboration["count"] = len(found_relations)
        collaboration["confidence"] = sum(r["confidence"] for r in found_relations) / max(1, len(found_relations))
        
        return collaboration
    
    def _extract_temporal_relations(self, text: str, dates: Dict) -> Dict[str, Any]:
        """提取时间关系"""
        temporal = {
            "relations": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 时间关系模式
        temporal_patterns = [
            r'([^，。！？\s]{2,20})(开始于|始于|起始于)([^，。！？\s]{2,20})',
            r'([^，。！？\s]{2,20})(结束于|终于|完成于)([^，。！？\s]{2,20})',
            r'([^，。！？\s]{2,20})(在|于)([^，。！？\s]{2,20})(期间|时期)',
            r'([^，。！？\s]{2,20})(之前|之后|同时)([^，。！？\s]{2,20})'
        ]
        
        date_strings = [d.get("date", "") for d in dates.get("entities", [])]
        
        found_relations = []
        
        for pattern in temporal_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                groups = match.groups()
                
                event = None
                time_ref = None
                
                for group in groups:
                    if group in date_strings:
                        time_ref = group
                    elif len(group) > 2:  # 可能是事件描述
                        event = group
                
                if event and time_ref:
                    # 判断时间关系类型
                    relation_type = "temporal"
                    if "开始" in match.group(0) or "始于" in match.group(0):
                        relation_type = "starts_at"
                    elif "结束" in match.group(0) or "终于" in match.group(0):
                        relation_type = "ends_at"
                    elif "期间" in match.group(0) or "时期" in match.group(0):
                        relation_type = "during"
                    
                    found_relations.append({
                        "event": event,
                        "time": time_ref,
                        "relation_type": relation_type,
                        "confidence": 0.7,
                        "evidence": match.group(0),
                        "position": match.start(),
                        "source": "temporal_pattern"
                    })
        
        temporal["relations"] = found_relations
        temporal["count"] = len(found_relations)
        temporal["confidence"] = sum(r["confidence"] for r in found_relations) / max(1, len(found_relations))
        
        return temporal
    
    def _analyze_semantic_relations(self, text: str) -> Dict[str, Any]:
        """分析语义关系"""
        semantic = {
            "relations": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 语义关系模式
        semantic_patterns = [
            (r'([^，。！？\s]{2,20})(导致|引起|造成)([^，。！？\s]{2,20})', "causes"),
            (r'([^，。！？\s]{2,20})(由于|因为|基于)([^，。！？\s]{2,20})', "caused_by"),
            (r'([^，。！？\s]{2,20})(包括|包含|含有)([^，。！？\s]{2,20})', "includes"),
            (r'([^，。！？\s]{2,20})(属于|是)([^，。！？\s]{2,20})(的一种|的类型)', "is_a"),
            (r'([^，。！？\s]{2,20})(用于|用来|为了)([^，。！？\s]{2,20})', "used_for")
        ]
        
        found_relations = []
        
        for pattern, relation_type in semantic_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                if len(match.groups()) >= 2:
                    subject = match.group(1)
                    object_entity = match.group(3) if len(match.groups()) >= 3 else match.group(2)
                    
                    found_relations.append({
                        "subject": subject,
                        "object": object_entity,
                        "relation_type": relation_type,
                        "confidence": 0.5,
                        "evidence": match.group(0),
                        "position": match.start(),
                        "source": "semantic_pattern"
                    })
        
        semantic["relations"] = found_relations[:15]  # 限制数量
        semantic["count"] = len(semantic["relations"])
        semantic["confidence"] = sum(r["confidence"] for r in semantic["relations"]) / max(1, len(semantic["relations"]))
        
        return semantic
    
    def _analyze_dependency_relations(self, text: str, structure_info: Dict) -> Dict[str, Any]:
        """分析依赖关系"""
        dependency = {
            "relations": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 基于文档结构分析依赖关系
        chapters = structure_info.get("chapter_structure", {}).get("chapters", [])
        sections = structure_info.get("section_structure", {}).get("sections", [])
        
        found_relations = []
        
        # 章节间的依赖关系
        for i in range(len(chapters) - 1):
            current_chapter = chapters[i]
            next_chapter = chapters[i + 1]
            
            found_relations.append({
                "predecessor": current_chapter.get("title", ""),
                "successor": next_chapter.get("title", ""),
                "relation_type": "precedes",
                "confidence": 0.8,
                "evidence": f"章节顺序: {current_chapter.get('number')} -> {next_chapter.get('number')}",
                "source": "structural_dependency"
            })
        
        # 小节间的依赖关系
        for i in range(len(sections) - 1):
            current_section = sections[i]
            next_section = sections[i + 1]
            
            # 只考虑同级别的小节
            if current_section.get("level") == next_section.get("level"):
                found_relations.append({
                    "predecessor": current_section.get("title", ""),
                    "successor": next_section.get("title", ""),
                    "relation_type": "follows",
                    "confidence": 0.6,
                    "evidence": f"小节顺序: {current_section.get('number')} -> {next_section.get('number')}",
                    "source": "structural_dependency"
                })
        
        dependency["relations"] = found_relations
        dependency["count"] = len(found_relations)
        dependency["confidence"] = sum(r["confidence"] for r in found_relations) / max(1, len(found_relations))
        
        return dependency
    
    def _calculate_relation_statistics(self, *relation_groups) -> Dict[str, Any]:
        """计算关系统计信息"""
        stats = {
            "total_count": 0,
            "type_counts": {},
            "average_confidence": 0.0,
            "high_confidence_count": 0
        }
        
        all_relations = []
        total_confidence = 0
        
        type_names = ["employment", "location", "hierarchical", "collaboration", "temporal", "semantic", "dependency"]
        
        for i, group in enumerate(relation_groups):
            type_name = type_names[i] if i < len(type_names) else f"type_{i}"
            count = group.get("count", 0)
            confidence = group.get("confidence", 0.0)
            
            stats["type_counts"][type_name] = count
            stats["total_count"] += count
            total_confidence += confidence * count
            
            # 收集高置信度关系
            for relation in group.get("relations", []):
                if relation.get("confidence", 0) >= 0.7:
                    stats["high_confidence_count"] += 1
                all_relations.append(relation)
        
        if stats["total_count"] > 0:
            stats["average_confidence"] = total_confidence / stats["total_count"]
        
        return stats
    
    def _build_relation_network(self, *relation_groups) -> Dict[str, Any]:
        """构建关系网络"""
        network = {
            "nodes": set(),
            "edges": [],
            "density": 0.0,
            "complexity": "low"
        }
        
        # 收集所有节点和边
        for group in relation_groups:
            for relation in group.get("relations", []):
                # 添加节点
                if "person" in relation:
                    network["nodes"].add(relation["person"])
                if "organization" in relation:
                    network["nodes"].add(relation["organization"])
                if "entity" in relation:
                    network["nodes"].add(relation["entity"])
                if "entity1" in relation:
                    network["nodes"].add(relation["entity1"])
                if "entity2" in relation:
                    network["nodes"].add(relation["entity2"])
                if "superior" in relation:
                    network["nodes"].add(relation["superior"])
                if "subordinate" in relation:
                    network["nodes"].add(relation["subordinate"])
                
                # 添加边
                network["edges"].append({
                    "relation_type": relation.get("relation_type"),
                    "confidence": relation.get("confidence", 0)
                })
        
        # 计算网络密度
        node_count = len(network["nodes"])
        edge_count = len(network["edges"])
        
        if node_count > 1:
            max_edges = node_count * (node_count - 1) / 2
            network["density"] = edge_count / max_edges
        
        # 评估复杂度
        if node_count > 10 and edge_count > 15:
            network["complexity"] = "high"
        elif node_count > 5 and edge_count > 8:
            network["complexity"] = "medium"
        else:
            network["complexity"] = "low"
        
        # 转换nodes为列表以便JSON序列化
        network["nodes"] = list(network["nodes"])
        network["node_count"] = len(network["nodes"])
        network["edge_count"] = edge_count
        
        return network
    
    def get_stage_name(self) -> str:
        return "extract_relations"
    
    def get_progress_percentage(self) -> int:
        return 85
    
    def get_stage_description(self) -> str:
        return "提取实体间关系、语义关系和依赖关系"
