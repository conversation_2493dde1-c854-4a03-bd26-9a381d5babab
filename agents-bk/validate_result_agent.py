#!/usr/bin/env python3
"""
结果验证Agent - 负责验证提取结果的完整性、准确性和一致性
"""

import asyncio
import re
from typing import Dict, Any, List
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class ValidateResultAgent(BaseAgent):
    """结果验证Agent - 验证最终提取结果的质量"""
    
    def __init__(self):
        super().__init__(name="结果验证Agent")
        self.add_dependency("merge_info")  # 依赖信息合并Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """验证提取结果的质量"""
        await asyncio.sleep(0.5)  # 模拟验证时间
        
        # 获取合并后的结果
        merged_result = context.get_extracted_data("merged_result", {})
        merge_info = context.get_extracted_data("merge_info", {})
        
        # 执行各项验证
        completeness_validation = self._validate_completeness(merged_result, context)
        accuracy_validation = self._validate_accuracy(merged_result, context)
        consistency_validation = self._validate_consistency(merged_result, context)
        quality_validation = self._validate_quality(merged_result, context)
        
        # 数据完整性检查
        data_integrity_check = self._check_data_integrity(merged_result, context)
        
        # 逻辑一致性检查
        logical_consistency_check = self._check_logical_consistency(merged_result, context)
        
        # 格式规范性检查
        format_compliance_check = self._check_format_compliance(merged_result)
        
        # 生成验证报告
        validation_report = self._generate_validation_report(
            completeness_validation, accuracy_validation, consistency_validation,
            quality_validation, data_integrity_check, logical_consistency_check,
            format_compliance_check
        )
        
        # 生成改进建议
        improvement_suggestions = self._generate_improvement_suggestions(validation_report, merged_result)
        
        # 计算最终验证分数
        final_validation_score = self._calculate_final_score(validation_report)
        
        return {
            "validation_info": {
                "validation_time": datetime.now().isoformat(),
                "validation_passed": validation_report["overall_passed"],
                "validation_score": final_validation_score,
                "critical_issues": len(validation_report["critical_issues"]),
                "warnings": len(validation_report["warnings"]),
                "data_quality": validation_report["data_quality_level"]
            },
            "completeness_validation": completeness_validation,
            "accuracy_validation": accuracy_validation,
            "consistency_validation": consistency_validation,
            "quality_validation": quality_validation,
            "data_integrity_check": data_integrity_check,
            "logical_consistency_check": logical_consistency_check,
            "format_compliance_check": format_compliance_check,
            "validation_report": validation_report,
            "improvement_suggestions": improvement_suggestions,
            "validation_summary": {
                "status": "completed",
                "validation_passed": validation_report["overall_passed"],
                "final_score": final_validation_score,
                "ready_for_storage": validation_report["overall_passed"] and final_validation_score >= 0.7,
                "next_stage": "store" if validation_report["overall_passed"] else "failed"
            }
        }
    
    def _validate_completeness(self, merged_result: Dict, context: AgentContext) -> Dict[str, Any]:
        """验证结果完整性"""
        completeness = {
            "score": 0.0,
            "missing_fields": [],
            "incomplete_sections": [],
            "completeness_issues": []
        }
        
        # 检查必需字段
        required_fields = [
            "document_metadata.title",
            "document_metadata.author",
            "document_metadata.document_type",
            "content_summary.abstract",
            "structure_summary.structure_type",
            "entity_summary.total_entities",
            "relation_summary.total_relations"
        ]
        
        missing_count = 0
        for field_path in required_fields:
            if not self._get_nested_value(merged_result, field_path):
                completeness["missing_fields"].append(field_path)
                missing_count += 1
        
        # 检查内容完整性
        document_metadata = merged_result.get("document_metadata", {})
        entity_summary = merged_result.get("entity_summary", {})
        relation_summary = merged_result.get("relation_summary", {})
        
        # 检查元数据完整性
        if not document_metadata.get("title") or document_metadata.get("title") == "未知标题":
            completeness["incomplete_sections"].append("document_title")
        
        if not document_metadata.get("author") or document_metadata.get("author") == "未知作者":
            completeness["incomplete_sections"].append("document_author")
        
        # 检查实体提取完整性
        if entity_summary.get("total_entities", 0) == 0:
            completeness["incomplete_sections"].append("entity_extraction")
            completeness["completeness_issues"].append("未提取到任何实体信息")
        
        # 检查关系提取完整性
        if relation_summary.get("total_relations", 0) == 0:
            completeness["incomplete_sections"].append("relation_extraction")
            completeness["completeness_issues"].append("未提取到任何关系信息")
        
        # 计算完整性分数
        field_completeness = (len(required_fields) - missing_count) / len(required_fields)
        section_completeness = max(0, (4 - len(completeness["incomplete_sections"])) / 4)
        
        completeness["score"] = (field_completeness * 0.6 + section_completeness * 0.4)
        
        return completeness
    
    def _validate_accuracy(self, merged_result: Dict, context: AgentContext) -> Dict[str, Any]:
        """验证结果准确性"""
        accuracy = {
            "score": 0.0,
            "accuracy_issues": [],
            "confidence_scores": {},
            "low_confidence_items": []
        }
        
        # 检查实体提取准确性
        entity_summary = merged_result.get("entity_summary", {})
        important_entities = entity_summary.get("important_entities", {})
        
        # 统计高置信度实体比例
        high_confidence_entities = important_entities.get("high_confidence", [])
        total_entities = entity_summary.get("total_entities", 1)
        
        entity_accuracy = len(high_confidence_entities) / max(1, total_entities)
        accuracy["confidence_scores"]["entities"] = entity_accuracy
        
        # 检查关系提取准确性
        relation_summary = merged_result.get("relation_summary", {})
        total_relations = relation_summary.get("total_relations", 0)
        
        # 基于关系网络密度评估准确性
        network_analysis = relation_summary.get("network_analysis", {})
        network_density = network_analysis.get("density", 0.0)
        
        # 合理的网络密度范围
        if 0.1 <= network_density <= 0.5:
            relation_accuracy = 1.0
        elif network_density < 0.1:
            relation_accuracy = network_density * 10
            accuracy["accuracy_issues"].append("关系网络密度过低，可能存在遗漏")
        else:
            relation_accuracy = max(0.0, 1.0 - (network_density - 0.5) * 2)
            accuracy["accuracy_issues"].append("关系网络密度过高，可能存在误判")
        
        accuracy["confidence_scores"]["relations"] = relation_accuracy
        
        # 检查文档分类准确性
        document_metadata = merged_result.get("document_metadata", {})
        content_summary = merged_result.get("content_summary", {})
        
        doc_type = document_metadata.get("document_type", "")
        content_type = content_summary.get("content_type", "")
        
        if doc_type != "未知类型" and content_type != "未知":
            type_accuracy = 1.0 if doc_type == content_type else 0.5
        else:
            type_accuracy = 0.3
            accuracy["accuracy_issues"].append("文档类型识别不准确")
        
        accuracy["confidence_scores"]["document_type"] = type_accuracy
        
        # 计算总体准确性分数
        accuracy["score"] = (
            entity_accuracy * 0.4 +
            relation_accuracy * 0.4 +
            type_accuracy * 0.2
        )
        
        return accuracy
    
    def _validate_consistency(self, merged_result: Dict, context: AgentContext) -> Dict[str, Any]:
        """验证结果一致性"""
        consistency = {
            "score": 0.0,
            "consistency_issues": [],
            "conflicting_information": [],
            "cross_validation_results": {}
        }
        
        # 检查元数据一致性
        document_metadata = merged_result.get("document_metadata", {})
        content_summary = merged_result.get("content_summary", {})
        
        # 检查页数一致性
        metadata_pages = document_metadata.get("page_count", 0)
        if metadata_pages == 0:
            consistency["consistency_issues"].append("页数信息缺失")
        
        # 检查字数一致性
        metadata_words = document_metadata.get("word_count", 0)
        readability_words = content_summary.get("readability", {}).get("total_words", 0)
        
        if abs(metadata_words - readability_words) > metadata_words * 0.1:  # 允许10%误差
            consistency["conflicting_information"].append({
                "field": "word_count",
                "metadata_value": metadata_words,
                "content_value": readability_words,
                "discrepancy": abs(metadata_words - readability_words)
            })
        
        # 检查实体-关系一致性
        entity_summary = merged_result.get("entity_summary", {})
        relation_summary = merged_result.get("relation_summary", {})
        
        total_entities = entity_summary.get("total_entities", 0)
        total_relations = relation_summary.get("total_relations", 0)
        
        # 关系数量应该与实体数量相匹配
        expected_relation_ratio = 0.3  # 期望关系数量是实体数量的30%
        actual_ratio = total_relations / max(1, total_entities)
        
        if abs(actual_ratio - expected_relation_ratio) > 0.2:
            consistency["consistency_issues"].append(
                f"实体-关系比例异常: 实际{actual_ratio:.2f}, 期望约{expected_relation_ratio:.2f}"
            )
        
        # 检查结构一致性
        structure_summary = merged_result.get("structure_summary", {})
        chapter_count = structure_summary.get("chapter_count", 0)
        section_count = structure_summary.get("section_count", 0)
        
        # 章节数量合理性检查
        if chapter_count > 0 and section_count / max(1, chapter_count) > 10:
            consistency["consistency_issues"].append("小节数量相对于章节数量过多")
        
        # 计算一致性分数
        issue_penalty = len(consistency["consistency_issues"]) * 0.1
        conflict_penalty = len(consistency["conflicting_information"]) * 0.15
        
        consistency["score"] = max(0.0, 1.0 - issue_penalty - conflict_penalty)
        
        return consistency
    
    def _validate_quality(self, merged_result: Dict, context: AgentContext) -> Dict[str, Any]:
        """验证结果质量"""
        quality = {
            "score": 0.0,
            "quality_issues": [],
            "quality_metrics": {},
            "improvement_areas": []
        }
        
        # 获取质量指标
        quality_metrics = merged_result.get("quality_metrics", {})
        
        # 评估各项质量指标
        data_integrity = quality_metrics.get("data_integrity", 0.0)
        completeness_score = quality_metrics.get("completeness_score", 0.0)
        information_richness = quality_metrics.get("information_richness", 0.0)
        extraction_confidence = quality_metrics.get("extraction_confidence", 0.0)
        
        quality["quality_metrics"] = {
            "data_integrity": data_integrity,
            "completeness": completeness_score,
            "information_richness": information_richness,
            "extraction_confidence": extraction_confidence
        }
        
        # 识别质量问题
        if data_integrity < 0.7:
            quality["quality_issues"].append("数据完整性不足")
            quality["improvement_areas"].append("提高数据验证标准")
        
        if completeness_score < 0.6:
            quality["quality_issues"].append("信息完整性不足")
            quality["improvement_areas"].append("增强信息提取覆盖度")
        
        if information_richness < 0.5:
            quality["quality_issues"].append("信息丰富度不足")
            quality["improvement_areas"].append("扩展实体和关系提取范围")
        
        if extraction_confidence < 0.6:
            quality["quality_issues"].append("提取置信度偏低")
            quality["improvement_areas"].append("优化提取算法和模式匹配")
        
        # 计算总体质量分数
        quality["score"] = (
            data_integrity * 0.3 +
            completeness_score * 0.3 +
            information_richness * 0.2 +
            extraction_confidence * 0.2
        )
        
        return quality
    
    def _check_data_integrity(self, merged_result: Dict, context: AgentContext) -> Dict[str, Any]:
        """检查数据完整性"""
        integrity = {
            "passed": True,
            "integrity_score": 0.0,
            "missing_data": [],
            "corrupted_data": [],
            "data_anomalies": []
        }
        
        # 检查必需数据字段
        required_data = [
            ("document_metadata", dict),
            ("content_summary", dict),
            ("structure_summary", dict),
            ("entity_summary", dict),
            ("relation_summary", dict)
        ]
        
        for field_name, expected_type in required_data:
            field_value = merged_result.get(field_name)
            if field_value is None:
                integrity["missing_data"].append(field_name)
                integrity["passed"] = False
            elif not isinstance(field_value, expected_type):
                integrity["corrupted_data"].append(f"{field_name}: 期望{expected_type.__name__}, 实际{type(field_value).__name__}")
                integrity["passed"] = False
        
        # 检查数据异常
        entity_summary = merged_result.get("entity_summary", {})
        if entity_summary.get("total_entities", 0) > 1000:
            integrity["data_anomalies"].append("实体数量异常过多")
        
        relation_summary = merged_result.get("relation_summary", {})
        if relation_summary.get("total_relations", 0) > 500:
            integrity["data_anomalies"].append("关系数量异常过多")
        
        # 计算完整性分数
        missing_penalty = len(integrity["missing_data"]) * 0.2
        corruption_penalty = len(integrity["corrupted_data"]) * 0.3
        anomaly_penalty = len(integrity["data_anomalies"]) * 0.1
        
        integrity["integrity_score"] = max(0.0, 1.0 - missing_penalty - corruption_penalty - anomaly_penalty)
        
        return integrity
    
    def _check_logical_consistency(self, merged_result: Dict, context: AgentContext) -> Dict[str, Any]:
        """检查逻辑一致性"""
        logical = {
            "passed": True,
            "consistency_score": 0.0,
            "logical_errors": [],
            "inconsistencies": []
        }
        
        # 检查时间逻辑
        document_metadata = merged_result.get("document_metadata", {})
        creation_date = document_metadata.get("creation_date", "")
        processing_date = document_metadata.get("processing_date", "")
        
        if creation_date and processing_date:
            # 简化的日期比较（实际应该解析日期）
            if creation_date > processing_date:
                logical["logical_errors"].append("创建日期晚于处理日期")
                logical["passed"] = False
        
        # 检查数量逻辑
        structure_summary = merged_result.get("structure_summary", {})
        page_count = document_metadata.get("page_count", 0)
        chapter_count = structure_summary.get("chapter_count", 0)
        
        if chapter_count > page_count and page_count > 0:
            logical["logical_errors"].append("章节数量超过页面数量")
        
        # 检查关系逻辑
        relation_summary = merged_result.get("relation_summary", {})
        network_analysis = relation_summary.get("network_analysis", {})
        node_count = network_analysis.get("node_count", 0)
        edge_count = network_analysis.get("edge_count", 0)
        
        if edge_count > node_count * (node_count - 1) / 2:
            logical["logical_errors"].append("关系数量超过理论最大值")
        
        # 计算逻辑一致性分数
        error_penalty = len(logical["logical_errors"]) * 0.3
        inconsistency_penalty = len(logical["inconsistencies"]) * 0.2
        
        logical["consistency_score"] = max(0.0, 1.0 - error_penalty - inconsistency_penalty)
        
        return logical
    
    def _check_format_compliance(self, merged_result: Dict) -> Dict[str, Any]:
        """检查格式规范性"""
        format_check = {
            "passed": True,
            "compliance_score": 0.0,
            "format_errors": [],
            "format_warnings": []
        }
        
        # 检查必需字段格式
        document_metadata = merged_result.get("document_metadata", {})
        
        # 检查标题格式
        title = document_metadata.get("title", "")
        if len(title) > 200:
            format_check["format_warnings"].append("标题过长")
        elif len(title) < 2:
            format_check["format_errors"].append("标题过短或缺失")
            format_check["passed"] = False
        
        # 检查日期格式
        creation_date = document_metadata.get("creation_date", "")
        if creation_date and not re.match(r'\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?', creation_date):
            format_check["format_warnings"].append("日期格式不标准")
        
        # 检查数值格式
        page_count = document_metadata.get("page_count", 0)
        word_count = document_metadata.get("word_count", 0)
        
        if not isinstance(page_count, int) or page_count < 0:
            format_check["format_errors"].append("页数格式错误")
            format_check["passed"] = False
        
        if not isinstance(word_count, int) or word_count < 0:
            format_check["format_errors"].append("字数格式错误")
            format_check["passed"] = False
        
        # 计算格式规范性分数
        error_penalty = len(format_check["format_errors"]) * 0.3
        warning_penalty = len(format_check["format_warnings"]) * 0.1
        
        format_check["compliance_score"] = max(0.0, 1.0 - error_penalty - warning_penalty)
        
        return format_check
    
    def _generate_validation_report(self, *validation_results) -> Dict[str, Any]:
        """生成验证报告"""
        report = {
            "overall_passed": True,
            "overall_score": 0.0,
            "critical_issues": [],
            "warnings": [],
            "data_quality_level": "high",
            "validation_summary": {}
        }
        
        # 收集所有验证结果
        validation_names = [
            "completeness", "accuracy", "consistency", "quality",
            "data_integrity", "logical_consistency", "format_compliance"
        ]
        
        total_score = 0
        failed_validations = []
        
        for i, validation in enumerate(validation_results):
            name = validation_names[i] if i < len(validation_names) else f"validation_{i}"
            score = validation.get("score", 0.0) or validation.get("integrity_score", 0.0) or validation.get("consistency_score", 0.0) or validation.get("compliance_score", 0.0)
            
            report["validation_summary"][name] = {
                "score": score,
                "passed": validation.get("passed", score >= 0.6)
            }
            
            total_score += score
            
            if not report["validation_summary"][name]["passed"]:
                failed_validations.append(name)
                report["overall_passed"] = False
            
            # 收集问题
            issues = (validation.get("completeness_issues", []) +
                     validation.get("accuracy_issues", []) +
                     validation.get("consistency_issues", []) +
                     validation.get("quality_issues", []) +
                     validation.get("missing_data", []) +
                     validation.get("logical_errors", []) +
                     validation.get("format_errors", []))
            
            warnings = (validation.get("format_warnings", []) +
                       validation.get("data_anomalies", []) +
                       validation.get("improvement_areas", []))
            
            report["critical_issues"].extend(issues)
            report["warnings"].extend(warnings)
        
        # 计算总体分数
        report["overall_score"] = total_score / len(validation_results)
        
        # 确定数据质量等级
        if report["overall_score"] >= 0.8:
            report["data_quality_level"] = "high"
        elif report["overall_score"] >= 0.6:
            report["data_quality_level"] = "medium"
        else:
            report["data_quality_level"] = "low"
        
        report["failed_validations"] = failed_validations
        
        return report
    
    def _generate_improvement_suggestions(self, validation_report: Dict, merged_result: Dict) -> Dict[str, Any]:
        """生成改进建议"""
        suggestions = {
            "priority_improvements": [],
            "optional_improvements": [],
            "technical_recommendations": [],
            "process_improvements": []
        }
        
        # 基于验证结果生成建议
        failed_validations = validation_report.get("failed_validations", [])
        
        for validation in failed_validations:
            if validation == "completeness":
                suggestions["priority_improvements"].append("增强信息提取的完整性，确保所有必需字段都被正确提取")
            elif validation == "accuracy":
                suggestions["priority_improvements"].append("提高实体和关系提取的准确性，优化识别算法")
            elif validation == "consistency":
                suggestions["priority_improvements"].append("加强数据一致性检查，解决信息冲突")
            elif validation == "data_integrity":
                suggestions["priority_improvements"].append("修复数据完整性问题，确保所有必需数据存在")
        
        # 基于质量分数生成建议
        overall_score = validation_report.get("overall_score", 0.0)
        
        if overall_score < 0.7:
            suggestions["technical_recommendations"].extend([
                "优化文档解析算法，提高文本提取质量",
                "改进实体识别模式，增加识别准确率",
                "加强关系提取逻辑，减少误判"
            ])
        
        if overall_score < 0.5:
            suggestions["process_improvements"].extend([
                "重新评估文档处理流程，识别瓶颈环节",
                "增加人工审核环节，确保关键信息准确性",
                "建立质量反馈机制，持续改进提取效果"
            ])
        
        # 可选改进建议
        suggestions["optional_improvements"].extend([
            "增加更多实体类型的识别支持",
            "扩展关系类型的提取范围",
            "优化文档结构分析算法",
            "增强多语言文档处理能力"
        ])
        
        return suggestions
    
    def _calculate_final_score(self, validation_report: Dict) -> float:
        """计算最终验证分数"""
        overall_score = validation_report.get("overall_score", 0.0)
        critical_issues = len(validation_report.get("critical_issues", []))
        
        # 根据关键问题数量调整分数
        issue_penalty = min(0.3, critical_issues * 0.05)
        
        final_score = max(0.0, overall_score - issue_penalty)
        
        return final_score
    
    def _get_nested_value(self, data: Dict, path: str) -> Any:
        """获取嵌套字典值"""
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def get_stage_name(self) -> str:
        return "validate_result"
    
    def get_progress_percentage(self) -> int:
        return 95
    
    def get_stage_description(self) -> str:
        return "验证提取结果的完整性、准确性和一致性"
