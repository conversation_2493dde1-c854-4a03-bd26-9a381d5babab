#!/usr/bin/env python3
"""
完成Agent - 负责生成最终报告，计算质量指标，完成整个处理流程
"""

import asyncio
import json
from typing import Dict, Any, List
from datetime import datetime, timedelta
from .base_agent import BaseAgent, AgentContext

class CompleteAgent(BaseAgent):
    """完成Agent - 生成最终处理报告并完成整个工作流"""
    
    def __init__(self):
        super().__init__(name="完成Agent")
        self.add_dependency("store")  # 依赖存储结果Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """完成整个处理流程并生成最终报告"""
        await asyncio.sleep(0.3)  # 模拟完成处理时间
        
        # 获取存储结果
        storage_info = context.get_extracted_data("storage_info", {})
        storage_statistics = context.get_extracted_data("storage_statistics", {})
        
        # 检查存储是否成功
        if not storage_info.get("storage_success", False):
            raise ValueError("存储失败，无法完成处理流程")
        
        # 生成最终处理报告
        final_report = self._generate_final_report(context)
        
        # 计算整体质量指标
        overall_quality_metrics = self._calculate_overall_quality_metrics(context)
        
        # 生成处理性能报告
        performance_report = self._generate_performance_report(context)
        
        # 生成数据统计报告
        data_statistics_report = self._generate_data_statistics_report(context)
        
        # 生成用户友好的摘要
        user_summary = self._generate_user_summary(context)
        
        # 生成技术报告
        technical_report = self._generate_technical_report(context)
        
        # 计算处理成功率
        success_metrics = self._calculate_success_metrics(context)
        
        # 生成后续建议
        recommendations = self._generate_recommendations(context)
        
        # 创建完成状态
        completion_status = self._create_completion_status(context, success_metrics)
        
        return {
            "completion_info": {
                "completion_time": datetime.now().isoformat(),
                "processing_successful": completion_status["overall_success"],
                "total_processing_time": completion_status["total_processing_time"],
                "final_quality_score": overall_quality_metrics["final_quality_score"],
                "data_completeness": overall_quality_metrics["data_completeness"],
                "processing_efficiency": performance_report["overall_efficiency"]
            },
            "final_report": final_report,
            "overall_quality_metrics": overall_quality_metrics,
            "performance_report": performance_report,
            "data_statistics_report": data_statistics_report,
            "user_summary": user_summary,
            "technical_report": technical_report,
            "success_metrics": success_metrics,
            "recommendations": recommendations,
            "completion_status": completion_status,
            "completion_summary": {
                "status": "completed",
                "processing_successful": completion_status["overall_success"],
                "final_score": overall_quality_metrics["final_quality_score"],
                "ready_for_use": completion_status["ready_for_use"],
                "next_steps": recommendations.get("immediate_actions", [])
            }
        }
    
    def _generate_final_report(self, context: AgentContext) -> Dict[str, Any]:
        """生成最终处理报告"""
        merged_result = context.get_extracted_data("merged_result", {})
        document_metadata = merged_result.get("document_metadata", {})
        
        report = {
            "report_id": f"report_{context.task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "document_info": {
                "title": document_metadata.get("title", "未知文档"),
                "author": document_metadata.get("author", "未知作者"),
                "document_type": document_metadata.get("document_type", "未知类型"),
                "processing_date": datetime.now().isoformat(),
                "source_url": document_metadata.get("source_url", ""),
                "page_count": document_metadata.get("page_count", 0),
                "word_count": document_metadata.get("word_count", 0)
            },
            "processing_summary": {
                "total_stages": 12,
                "completed_stages": self._count_completed_stages(context),
                "processing_start": context.created_at,
                "processing_end": datetime.now().isoformat(),
                "processing_duration": self._calculate_processing_duration(context)
            },
            "extraction_results": {
                "entities_extracted": context.get_extracted_data("entity_info", {}).get("total_entities", 0),
                "relations_extracted": context.get_extracted_data("relation_info", {}).get("total_relations", 0),
                "structure_elements": self._count_structure_elements(context),
                "data_points_total": context.get_extracted_data("merge_info", {}).get("total_data_points", 0)
            },
            "quality_assessment": {
                "validation_passed": context.get_extracted_data("validation_info", {}).get("validation_passed", False),
                "data_integrity": context.get_extracted_data("merged_result", {}).get("quality_metrics", {}).get("data_integrity", 0.0),
                "extraction_confidence": context.get_extracted_data("merged_result", {}).get("quality_metrics", {}).get("extraction_confidence", 0.0),
                "overall_quality": context.get_extracted_data("merged_result", {}).get("quality_metrics", {}).get("overall_quality", 0.0)
            },
            "storage_results": {
                "storage_successful": context.get_extracted_data("storage_info", {}).get("storage_success", False),
                "records_stored": context.get_extracted_data("storage_statistics", {}).get("total_records", 0),
                "storage_size": context.get_extracted_data("storage_statistics", {}).get("estimated_size", 0)
            }
        }
        
        return report
    
    def _calculate_overall_quality_metrics(self, context: AgentContext) -> Dict[str, Any]:
        """计算整体质量指标"""
        validation_info = context.get_extracted_data("validation_info", {})
        merged_result = context.get_extracted_data("merged_result", {})
        quality_metrics = merged_result.get("quality_metrics", {})
        
        metrics = {
            "final_quality_score": 0.0,
            "data_completeness": 0.0,
            "information_richness": 0.0,
            "processing_accuracy": 0.0,
            "data_consistency": 0.0,
            "technical_quality": 0.0,
            "user_satisfaction_estimate": 0.0,
            "quality_breakdown": {}
        }
        
        # 从各个阶段收集质量指标
        validation_score = validation_info.get("validation_score", 0.0)
        data_integrity = quality_metrics.get("data_integrity", 0.0)
        completeness_score = quality_metrics.get("completeness_score", 0.0)
        information_richness = quality_metrics.get("information_richness", 0.0)
        extraction_confidence = quality_metrics.get("extraction_confidence", 0.0)
        
        # 计算各项指标
        metrics["data_completeness"] = completeness_score
        metrics["information_richness"] = information_richness
        metrics["processing_accuracy"] = extraction_confidence
        metrics["data_consistency"] = validation_score
        metrics["technical_quality"] = data_integrity
        
        # 计算用户满意度估计
        entity_count = context.get_extracted_data("entity_info", {}).get("total_entities", 0)
        relation_count = context.get_extracted_data("relation_info", {}).get("total_relations", 0)
        
        content_richness = min(1.0, (entity_count + relation_count) / 50)  # 期望50个数据点
        metrics["user_satisfaction_estimate"] = (
            metrics["data_completeness"] * 0.3 +
            content_richness * 0.3 +
            metrics["processing_accuracy"] * 0.4
        )
        
        # 计算最终质量分数
        metrics["final_quality_score"] = (
            metrics["data_completeness"] * 0.2 +
            metrics["information_richness"] * 0.2 +
            metrics["processing_accuracy"] * 0.2 +
            metrics["data_consistency"] * 0.2 +
            metrics["technical_quality"] * 0.2
        )
        
        # 质量分解
        metrics["quality_breakdown"] = {
            "excellent": metrics["final_quality_score"] >= 0.9,
            "good": 0.7 <= metrics["final_quality_score"] < 0.9,
            "acceptable": 0.5 <= metrics["final_quality_score"] < 0.7,
            "poor": metrics["final_quality_score"] < 0.5,
            "quality_level": self._determine_quality_level(metrics["final_quality_score"])
        }
        
        return metrics
    
    def _generate_performance_report(self, context: AgentContext) -> Dict[str, Any]:
        """生成处理性能报告"""
        processing_start = datetime.fromisoformat(context.created_at.replace('Z', '+00:00'))
        processing_end = datetime.now()
        total_duration = (processing_end - processing_start).total_seconds()
        
        report = {
            "overall_efficiency": 0.0,
            "processing_speed": 0.0,
            "resource_utilization": "moderate",
            "bottleneck_analysis": [],
            "performance_metrics": {
                "total_processing_time": total_duration,
                "average_stage_time": total_duration / 12,  # 12个阶段
                "data_throughput": 0.0,
                "memory_efficiency": "good",
                "cpu_utilization": "moderate"
            },
            "stage_performance": self._analyze_stage_performance(context),
            "optimization_suggestions": []
        }
        
        # 计算数据吞吐量
        total_data_points = context.get_extracted_data("merge_info", {}).get("total_data_points", 0)
        if total_duration > 0:
            report["performance_metrics"]["data_throughput"] = total_data_points / total_duration
        
        # 计算处理速度（基于字数）
        word_count = context.get_extracted_data("merged_result", {}).get("document_metadata", {}).get("word_count", 0)
        if total_duration > 0 and word_count > 0:
            report["processing_speed"] = word_count / total_duration  # 字/秒
        
        # 计算整体效率
        expected_time = max(10, word_count / 100)  # 期望每100字1秒
        if expected_time > 0:
            report["overall_efficiency"] = min(1.0, expected_time / total_duration)
        
        # 瓶颈分析
        if total_duration > 60:  # 超过1分钟
            report["bottleneck_analysis"].append("总处理时间较长，建议优化算法")
        
        if report["processing_speed"] < 10:  # 每秒处理少于10字
            report["bottleneck_analysis"].append("文本处理速度较慢")
        
        # 优化建议
        if report["overall_efficiency"] < 0.7:
            report["optimization_suggestions"].extend([
                "优化文本解析算法",
                "并行化实体提取过程",
                "缓存常用模式匹配结果"
            ])
        
        return report
    
    def _generate_data_statistics_report(self, context: AgentContext) -> Dict[str, Any]:
        """生成数据统计报告"""
        entity_summary = context.get_extracted_data("merged_result", {}).get("entity_summary", {})
        relation_summary = context.get_extracted_data("merged_result", {}).get("relation_summary", {})
        structure_summary = context.get_extracted_data("merged_result", {}).get("structure_summary", {})
        
        report = {
            "data_overview": {
                "total_entities": entity_summary.get("total_entities", 0),
                "total_relations": relation_summary.get("total_relations", 0),
                "structure_elements": structure_summary.get("chapter_count", 0) + structure_summary.get("section_count", 0),
                "data_diversity": entity_summary.get("entity_diversity", 0) + relation_summary.get("relation_diversity", 0)
            },
            "entity_distribution": entity_summary.get("entity_counts", {}),
            "relation_distribution": relation_summary.get("relation_counts", {}),
            "structure_analysis": {
                "document_type": structure_summary.get("structure_type", "unknown"),
                "hierarchy_depth": structure_summary.get("hierarchy_depth", 1),
                "complexity": structure_summary.get("structure_complexity", "simple")
            },
            "quality_indicators": {
                "high_confidence_entities": len(context.get_extracted_data("important_entities", {}).get("high_confidence", [])),
                "network_density": relation_summary.get("network_analysis", {}).get("density", 0.0),
                "information_density": context.get_extracted_data("merged_result", {}).get("key_information", {}).get("information_density", 0.0)
            },
            "data_insights": self._generate_data_insights(entity_summary, relation_summary, structure_summary)
        }
        
        return report
    
    def _generate_user_summary(self, context: AgentContext) -> Dict[str, Any]:
        """生成用户友好的摘要"""
        merged_result = context.get_extracted_data("merged_result", {})
        document_metadata = merged_result.get("document_metadata", {})
        document_abstract = merged_result.get("document_abstract", {})
        key_information = merged_result.get("key_information", {})
        
        summary = {
            "document_title": document_metadata.get("title", "未知文档"),
            "processing_status": "处理完成",
            "processing_time": self._format_processing_time(context),
            "key_findings": {
                "main_topics": document_abstract.get("key_topics", [])[:5],
                "key_persons": key_information.get("key_persons", [])[:3],
                "key_organizations": key_information.get("key_organizations", [])[:3],
                "important_dates": key_information.get("important_dates", [])[:3]
            },
            "document_insights": {
                "document_scope": document_abstract.get("document_scope", "moderate"),
                "target_audience": document_abstract.get("target_audience", "general_professionals"),
                "technical_level": merged_result.get("content_summary", {}).get("technical_level", "medium"),
                "estimated_reading_time": merged_result.get("metadata_info", {}).get("content_info", {}).get("estimated_reading_time", 5)
            },
            "quality_summary": {
                "overall_quality": self._format_quality_level(context.get_extracted_data("overall_quality_metrics", {}).get("final_quality_score", 0.0)),
                "data_completeness": f"{context.get_extracted_data('overall_quality_metrics', {}).get('data_completeness', 0.0)*100:.0f}%",
                "extraction_accuracy": f"{context.get_extracted_data('overall_quality_metrics', {}).get('processing_accuracy', 0.0)*100:.0f}%"
            },
            "next_steps": [
                "数据已保存到数据库，可以进行查询和分析",
                "可以通过API接口访问提取的结构化信息",
                "建议定期检查数据质量和完整性"
            ]
        }
        
        return summary
    
    def _generate_technical_report(self, context: AgentContext) -> Dict[str, Any]:
        """生成技术报告"""
        report = {
            "system_performance": {
                "agent_execution_summary": self._summarize_agent_execution(context),
                "memory_usage": "适中",
                "processing_efficiency": context.get_extracted_data("performance_report", {}).get("overall_efficiency", 0.0),
                "error_rate": self._calculate_error_rate(context)
            },
            "data_processing_details": {
                "text_parsing_accuracy": 0.85,  # 估算值
                "entity_extraction_precision": context.get_extracted_data("entity_info", {}).get("extraction_confidence", 0.0),
                "relation_extraction_recall": context.get_extracted_data("relation_info", {}).get("average_confidence", 0.0),
                "structure_analysis_completeness": 0.80  # 估算值
            },
            "validation_results": {
                "validation_passed": context.get_extracted_data("validation_info", {}).get("validation_passed", False),
                "critical_issues": context.get_extracted_data("validation_info", {}).get("critical_issues", 0),
                "warnings": context.get_extracted_data("validation_info", {}).get("warnings", 0),
                "data_integrity_score": context.get_extracted_data("validation_report", {}).get("overall_score", 0.0)
            },
            "storage_details": {
                "storage_success": context.get_extracted_data("storage_info", {}).get("storage_success", False),
                "records_stored": context.get_extracted_data("storage_statistics", {}).get("total_records", 0),
                "storage_efficiency": context.get_extracted_data("storage_statistics", {}).get("storage_efficiency", 0.0),
                "indexing_status": "completed"
            },
            "system_recommendations": self._generate_system_recommendations(context)
        }
        
        return report
    
    def _calculate_success_metrics(self, context: AgentContext) -> Dict[str, Any]:
        """计算处理成功率指标"""
        metrics = {
            "overall_success_rate": 0.0,
            "stage_success_rates": {},
            "critical_failures": 0,
            "warnings": 0,
            "success_factors": [],
            "failure_factors": []
        }
        
        # 计算各阶段成功率
        stage_results = [
            ("initialization", True),  # 假设初始化成功
            ("download", context.get_extracted_data("download_info", {}).get("download_success", False)),
            ("validation", context.get_extracted_data("validation_report", {}).get("overall_valid", False)),
            ("parsing", context.get_extracted_data("parse_info", {}).get("parsing_success", False)),
            ("basic_extraction", context.get_extracted_data("basic_info", {}) != {}),
            ("structure_extraction", context.get_extracted_data("structure_info", {}) != {}),
            ("entity_extraction", context.get_extracted_data("entity_info", {}).get("total_entities", 0) > 0),
            ("relation_extraction", context.get_extracted_data("relation_info", {}).get("total_relations", 0) > 0),
            ("information_merge", context.get_extracted_data("merge_info", {}).get("merge_success", False)),
            ("result_validation", context.get_extracted_data("validation_info", {}).get("validation_passed", False)),
            ("storage", context.get_extracted_data("storage_info", {}).get("storage_success", False)),
            ("completion", True)  # 如果到达这里就是成功
        ]
        
        successful_stages = sum(1 for _, success in stage_results if success)
        total_stages = len(stage_results)
        
        metrics["overall_success_rate"] = successful_stages / total_stages
        
        for stage_name, success in stage_results:
            metrics["stage_success_rates"][stage_name] = success
            if not success:
                metrics["critical_failures"] += 1
                metrics["failure_factors"].append(f"{stage_name}阶段失败")
            else:
                metrics["success_factors"].append(f"{stage_name}阶段成功")
        
        # 统计警告
        metrics["warnings"] = (
            context.get_extracted_data("validation_info", {}).get("warnings", 0) +
            len(context.get_extracted_data("validation_report", {}).get("warnings", []))
        )
        
        return metrics
    
    def _generate_recommendations(self, context: AgentContext) -> Dict[str, Any]:
        """生成后续建议"""
        overall_quality = context.get_extracted_data("overall_quality_metrics", {}).get("final_quality_score", 0.0)
        success_rate = context.get_extracted_data("success_metrics", {}).get("overall_success_rate", 0.0)
        
        recommendations = {
            "immediate_actions": [],
            "long_term_improvements": [],
            "data_usage_suggestions": [],
            "system_optimizations": []
        }
        
        # 基于质量分数的建议
        if overall_quality < 0.6:
            recommendations["immediate_actions"].extend([
                "检查并修复数据质量问题",
                "重新运行关键提取阶段",
                "人工审核提取结果"
            ])
        elif overall_quality < 0.8:
            recommendations["immediate_actions"].append("对低置信度数据进行人工验证")
        
        # 基于成功率的建议
        if success_rate < 0.8:
            recommendations["immediate_actions"].append("分析失败阶段并进行修复")
        
        # 数据使用建议
        entity_count = context.get_extracted_data("entity_info", {}).get("total_entities", 0)
        relation_count = context.get_extracted_data("relation_info", {}).get("total_relations", 0)
        
        if entity_count > 0:
            recommendations["data_usage_suggestions"].append("利用实体信息进行知识图谱构建")
        
        if relation_count > 0:
            recommendations["data_usage_suggestions"].append("基于关系信息进行网络分析")
        
        # 长期改进建议
        recommendations["long_term_improvements"].extend([
            "建立数据质量监控机制",
            "优化实体和关系提取算法",
            "增加更多文档类型的支持",
            "建立用户反馈收集系统"
        ])
        
        # 系统优化建议
        performance_efficiency = context.get_extracted_data("performance_report", {}).get("overall_efficiency", 0.0)
        if performance_efficiency < 0.7:
            recommendations["system_optimizations"].extend([
                "优化处理算法性能",
                "增加并行处理能力",
                "优化数据库存储结构"
            ])
        
        return recommendations
    
    def _create_completion_status(self, context: AgentContext, success_metrics: Dict) -> Dict[str, Any]:
        """创建完成状态"""
        processing_start = datetime.fromisoformat(context.created_at.replace('Z', '+00:00'))
        processing_end = datetime.now()
        
        status = {
            "overall_success": success_metrics.get("overall_success_rate", 0.0) >= 0.8,
            "completion_time": datetime.now().isoformat(),
            "total_processing_time": (processing_end - processing_start).total_seconds(),
            "ready_for_use": (
                success_metrics.get("overall_success_rate", 0.0) >= 0.8 and
                context.get_extracted_data("storage_info", {}).get("storage_success", False) and
                context.get_extracted_data("validation_info", {}).get("validation_passed", False)
            ),
            "final_status": "completed",
            "quality_level": self._determine_quality_level(
                context.get_extracted_data("overall_quality_metrics", {}).get("final_quality_score", 0.0)
            ),
            "completion_summary": f"文档处理{'成功' if success_metrics.get('overall_success_rate', 0.0) >= 0.8 else '部分成功'}完成"
        }
        
        return status
    
    # 辅助方法
    def _count_completed_stages(self, context: AgentContext) -> int:
        """计算已完成的阶段数"""
        completed = 0
        stage_keys = [
            "init_info", "download_info", "validation_report", "parse_info",
            "basic_info", "structure_info", "entity_info", "relation_info",
            "merge_info", "validation_info", "storage_info"
        ]
        
        for key in stage_keys:
            if context.get_extracted_data(key, {}):
                completed += 1
        
        return completed + 1  # +1 for completion stage
    
    def _calculate_processing_duration(self, context: AgentContext) -> str:
        """计算处理持续时间"""
        start_time = datetime.fromisoformat(context.created_at.replace('Z', '+00:00'))
        end_time = datetime.now()
        duration = end_time - start_time
        
        if duration.total_seconds() < 60:
            return f"{duration.total_seconds():.1f}秒"
        elif duration.total_seconds() < 3600:
            return f"{duration.total_seconds()/60:.1f}分钟"
        else:
            return f"{duration.total_seconds()/3600:.1f}小时"
    
    def _count_structure_elements(self, context: AgentContext) -> int:
        """计算结构元素数量"""
        structure_info = context.get_extracted_data("structure_info", {})
        return (
            structure_info.get("total_chapters", 0) +
            structure_info.get("total_sections", 0) +
            structure_info.get("total_paragraphs", 0) +
            structure_info.get("total_lists", 0)
        )
    
    def _determine_quality_level(self, score: float) -> str:
        """确定质量等级"""
        if score >= 0.9:
            return "excellent"
        elif score >= 0.7:
            return "good"
        elif score >= 0.5:
            return "acceptable"
        else:
            return "poor"
    
    def _format_quality_level(self, score: float) -> str:
        """格式化质量等级"""
        level = self._determine_quality_level(score)
        level_map = {
            "excellent": "优秀",
            "good": "良好",
            "acceptable": "可接受",
            "poor": "较差"
        }
        return level_map.get(level, "未知")
    
    def _format_processing_time(self, context: AgentContext) -> str:
        """格式化处理时间"""
        return self._calculate_processing_duration(context)
    
    def _analyze_stage_performance(self, context: AgentContext) -> Dict[str, Any]:
        """分析各阶段性能"""
        # 简化的阶段性能分析
        return {
            "fastest_stage": "initialization",
            "slowest_stage": "entity_extraction",
            "average_stage_time": "0.5秒",
            "performance_bottlenecks": ["复杂文档解析", "大量实体提取"]
        }
    
    def _generate_data_insights(self, entity_summary: Dict, relation_summary: Dict, structure_summary: Dict) -> List[str]:
        """生成数据洞察"""
        insights = []
        
        total_entities = entity_summary.get("total_entities", 0)
        total_relations = relation_summary.get("total_relations", 0)
        
        if total_entities > 50:
            insights.append("文档包含丰富的实体信息，适合进行深度分析")
        
        if total_relations > 20:
            insights.append("实体间关系复杂，可以构建知识网络")
        
        if structure_summary.get("hierarchy_depth", 1) > 2:
            insights.append("文档结构层次清晰，便于信息组织")
        
        return insights
    
    def _summarize_agent_execution(self, context: AgentContext) -> Dict[str, str]:
        """总结Agent执行情况"""
        return {
            "total_agents": "12",
            "successful_agents": "12",
            "failed_agents": "0",
            "execution_efficiency": "高效"
        }
    
    def _calculate_error_rate(self, context: AgentContext) -> float:
        """计算错误率"""
        critical_issues = context.get_extracted_data("validation_info", {}).get("critical_issues", 0)
        total_operations = 100  # 假设总操作数
        return critical_issues / total_operations if total_operations > 0 else 0.0
    
    def _generate_system_recommendations(self, context: AgentContext) -> List[str]:
        """生成系统建议"""
        return [
            "定期更新实体识别模式",
            "优化关系提取算法",
            "增强文档结构分析能力",
            "建立质量监控机制"
        ]
    
    def get_stage_name(self) -> str:
        return "complete"
    
    def get_progress_percentage(self) -> int:
        return 100
    
    def get_stage_description(self) -> str:
        return "生成最终报告，完成整个处理流程"
