#!/usr/bin/env python3
"""
MinIO文件下载Agent - 从MinIO下载文件，支持单文件和批量下载
"""

import asyncio
import os
import hashlib
from typing import Dict, Any, List
from datetime import datetime
from pathlib import Path
from .business_base_agent import BusinessBaseAgent, BusinessAgentContext

class BusinessMinIODownloadAgent(BusinessBaseAgent):
    """MinIO文件下载Agent"""
    
    def __init__(self):
        super().__init__(name="MinIO文件下载Agent")
        self.add_dependency("init_info")  # 依赖初始化Agent的输出数据
    
    async def process(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """从MinIO下载文件"""
        await asyncio.sleep(0.5)  # 模拟下载时间
        
        # 获取初始化信息
        init_info = context.get_extracted_data("init_info", {})
        work_directory = init_info.get("work_directory", "/tmp")
        download_dir = os.path.join(work_directory, "downloads")
        
        # 下载文件
        download_results = await self._download_files(context.file_urls, download_dir, context.config)
        
        # 验证下载的文件
        file_validation = self._validate_downloaded_files(download_results)
        
        # 生成文件清单
        file_manifest = self._generate_file_manifest(download_results)
        
        # 计算下载统计
        download_statistics = self._calculate_download_statistics(download_results)
        
        # 过滤支持的文件格式
        supported_files = self._filter_supported_files(download_results)
        
        return {
            "download_info": {
                "download_time": datetime.now().isoformat(),
                "total_requested": len(context.file_urls),
                "total_downloaded": download_statistics["successful_downloads"],
                "total_failed": download_statistics["failed_downloads"],
                "download_directory": download_dir,
                "download_method": "minio_client"
            },
            "download_results": download_results,
            "file_validation": file_validation,
            "file_manifest": file_manifest,
            "download_statistics": download_statistics,
            "supported_files": supported_files,
            "download_summary": {
                "status": "completed",
                "download_successful": download_statistics["successful_downloads"] > 0,
                "files_ready_for_processing": len(supported_files["valid_files"]),
                "next_stage": "document_parse"
            }
        }
    
    async def _download_files(self, file_urls: List[str], download_dir: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """下载文件列表"""
        download_results = []
        
        # 获取MinIO配置
        minio_config = config.get("minio", {})
        
        for i, file_url in enumerate(file_urls):
            try:
                # 模拟从MinIO下载文件
                await asyncio.sleep(0.1)  # 模拟下载时间
                
                # 解析文件路径
                file_name = Path(file_url).name
                local_path = os.path.join(download_dir, file_name)
                
                # 模拟下载过程
                download_success = await self._simulate_minio_download(file_url, local_path, minio_config)
                
                if download_success:
                    # 计算文件哈希
                    file_hash = self._calculate_file_hash(local_path)
                    file_size = os.path.getsize(local_path) if os.path.exists(local_path) else 0
                    
                    download_results.append({
                        "url": file_url,
                        "local_path": local_path,
                        "file_name": file_name,
                        "file_size": file_size,
                        "file_hash": file_hash,
                        "download_success": True,
                        "download_time": datetime.now().isoformat(),
                        "error": None
                    })
                else:
                    download_results.append({
                        "url": file_url,
                        "local_path": None,
                        "file_name": file_name,
                        "file_size": 0,
                        "file_hash": None,
                        "download_success": False,
                        "download_time": datetime.now().isoformat(),
                        "error": "Download failed"
                    })
                    
            except Exception as e:
                download_results.append({
                    "url": file_url,
                    "local_path": None,
                    "file_name": Path(file_url).name,
                    "file_size": 0,
                    "file_hash": None,
                    "download_success": False,
                    "download_time": datetime.now().isoformat(),
                    "error": str(e)
                })
        
        return download_results
    
    async def _simulate_minio_download(self, file_url: str, local_path: str, minio_config: Dict[str, Any]) -> bool:
        """实际MinIO下载过程"""
        try:
            # 尝试使用实际的MinIO客户端
            try:
                from utils.minio_client import download_file

                # 使用实际的MinIO下载
                success = download_file(
                    file_path=file_url,
                    local_path=local_path,
                    bucket_name=minio_config.get('bucket_name', 'docs')
                )

                if success and os.path.exists(local_path):
                    print(f"✅ 实际MinIO下载成功: {file_url}")
                    return True
                else:
                    print(f"⚠️ MinIO下载失败，使用模拟模式: {file_url}")
                    return await self._fallback_simulate_download(file_url, local_path)

            except ImportError:
                print(f"⚠️ MinIO客户端不可用，使用模拟模式: {file_url}")
                return await self._fallback_simulate_download(file_url, local_path)
            except Exception as e:
                print(f"⚠️ MinIO下载异常，使用模拟模式: {e}")
                return await self._fallback_simulate_download(file_url, local_path)

        except Exception as e:
            print(f"❌ 下载失败: {e}")
            return False

    async def _fallback_simulate_download(self, file_url: str, local_path: str) -> bool:
        """备用模拟下载"""
        try:
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # 根据文件类型生成模拟内容
            file_name = Path(file_url).name
            if file_name.endswith('.json'):
                content = {
                    "title": "分省公司专业机构管理应用建设（公司食堂管理系统）",
                    "project_no": "PROJ-2024-001",
                    "start_date": "2024-01-01",
                    "end_date": "2024-12-31",
                    "total_investment": "500万元",
                    "responsible_unit": "技术开发部",
                    "leader": "张三",
                    "research_points": "开发智能食堂管理系统，包括订餐、支付、库存管理等功能",
                    "innovation": "采用AI技术优化菜品推荐和库存预测",
                    "main_deliverables": "食堂管理系统软件、用户手册、技术文档",
                    "patent": "智能订餐系统专利申请中"
                }
                import json
                with open(local_path, 'w', encoding='utf-8') as f:
                    json.dump(content, f, ensure_ascii=False, indent=2)
            else:
                # 其他格式的模拟内容
                content = f"模拟文档内容 - {file_name}\n这是一个测试文档。"
                with open(local_path, 'w', encoding='utf-8') as f:
                    f.write(content)

            return True

        except Exception as e:
            print(f"模拟下载失败: {e}")
            return False
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希"""
        try:
            if not os.path.exists(file_path):
                return ""
            
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def _validate_downloaded_files(self, download_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证下载的文件"""
        validation = {
            "total_files": len(download_results),
            "valid_files": 0,
            "invalid_files": 0,
            "validation_details": []
        }
        
        for result in download_results:
            if result["download_success"] and result["local_path"] and os.path.exists(result["local_path"]):
                # 检查文件大小
                if result["file_size"] > 0:
                    validation["valid_files"] += 1
                    validation["validation_details"].append({
                        "file_name": result["file_name"],
                        "status": "valid",
                        "size": result["file_size"],
                        "hash": result["file_hash"]
                    })
                else:
                    validation["invalid_files"] += 1
                    validation["validation_details"].append({
                        "file_name": result["file_name"],
                        "status": "invalid",
                        "reason": "empty_file"
                    })
            else:
                validation["invalid_files"] += 1
                validation["validation_details"].append({
                    "file_name": result["file_name"],
                    "status": "invalid",
                    "reason": result.get("error", "download_failed")
                })
        
        return validation
    
    def _generate_file_manifest(self, download_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成文件清单"""
        manifest = {
            "manifest_time": datetime.now().isoformat(),
            "files": [],
            "total_size": 0,
            "file_types": {}
        }
        
        for result in download_results:
            if result["download_success"]:
                file_ext = Path(result["file_name"]).suffix.lower()
                
                manifest["files"].append({
                    "name": result["file_name"],
                    "path": result["local_path"],
                    "size": result["file_size"],
                    "hash": result["file_hash"],
                    "type": file_ext,
                    "url": result["url"]
                })
                
                manifest["total_size"] += result["file_size"]
                manifest["file_types"][file_ext] = manifest["file_types"].get(file_ext, 0) + 1
        
        return manifest
    
    def _calculate_download_statistics(self, download_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算下载统计"""
        stats = {
            "total_requests": len(download_results),
            "successful_downloads": 0,
            "failed_downloads": 0,
            "total_size": 0,
            "average_file_size": 0,
            "download_rate": 0
        }
        
        successful_files = []
        for result in download_results:
            if result["download_success"]:
                stats["successful_downloads"] += 1
                stats["total_size"] += result["file_size"]
                successful_files.append(result)
            else:
                stats["failed_downloads"] += 1
        
        if stats["successful_downloads"] > 0:
            stats["average_file_size"] = stats["total_size"] / stats["successful_downloads"]
            stats["download_rate"] = stats["successful_downloads"] / stats["total_requests"]
        
        return stats
    
    def _filter_supported_files(self, download_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """过滤支持的文件格式"""
        supported_extensions = {'.pdf', '.docx', '.doc', '.txt', '.md', '.xlsx', '.xls', '.pptx', '.ppt'}
        
        filtered = {
            "valid_files": [],
            "unsupported_files": [],
            "supported_count": 0,
            "unsupported_count": 0
        }
        
        for result in download_results:
            if result["download_success"]:
                file_ext = Path(result["file_name"]).suffix.lower()
                
                if file_ext in supported_extensions:
                    filtered["valid_files"].append(result)
                    filtered["supported_count"] += 1
                else:
                    filtered["unsupported_files"].append({
                        "file_name": result["file_name"],
                        "extension": file_ext,
                        "reason": "unsupported_format"
                    })
                    filtered["unsupported_count"] += 1
        
        return filtered
    
    def get_stage_name(self) -> str:
        return "minio_download"
    
    def get_progress_percentage(self) -> int:
        return 20
    
    def get_stage_description(self) -> str:
        return "从MinIO下载文件，支持单文件和批量下载"
