#!/usr/bin/env python3
"""
文档处理相关的Agent实现
"""

import asyncio
import json
from typing import Dict, Any
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class InitAgent(BaseAgent):
    """初始化Agent"""
    
    def __init__(self):
        super().__init__(name="初始化Agent")
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """初始化处理环境"""
        await asyncio.sleep(0.2)  # 模拟处理时间
        
        return {
            "init_info": {
                "processing_start_time": datetime.now().isoformat(),
                "file_url": context.file_url,
                "task_id": context.task_id,
                "initialized": True
            }
        }
    
    def get_stage_name(self) -> str:
        return "init"
    
    def get_progress_percentage(self) -> int:
        return 5

class DownloadAgent(BaseAgent):
    """文件下载Agent"""
    
    def __init__(self):
        super().__init__(name="文件下载Agent")
        self.add_dependency("init")  # 依赖初始化Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """下载文件"""
        await asyncio.sleep(0.3)
        
        if not context.file_content:
            raise ValueError("文件内容为空")
        
        return {
            "download_info": {
                "file_size": len(str(context.file_content)),
                "download_time": datetime.now().isoformat(),
                "download_success": True
            }
        }
    
    def get_stage_name(self) -> str:
        return "download"
    
    def get_progress_percentage(self) -> int:
        return 15

class ValidateAgent(BaseAgent):
    """文件验证Agent"""
    
    def __init__(self):
        super().__init__(name="文件验证Agent")
        self.add_dependency("download")
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """验证文件格式和内容"""
        await asyncio.sleep(0.2)
        
        # 验证必要字段
        required_fields = ["doc_id", "pages"]
        missing_fields = []
        
        for field in required_fields:
            if field not in context.file_content:
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"缺少必要字段: {', '.join(missing_fields)}")
        
        return {
            "validation_info": {
                "validation_time": datetime.now().isoformat(),
                "page_count": len(context.file_content.get("pages", [])),
                "validation_passed": True,
                "validated_fields": required_fields
            }
        }
    
    def get_stage_name(self) -> str:
        return "validate"
    
    def get_progress_percentage(self) -> int:
        return 20

class ParseAgent(BaseAgent):
    """文档解析Agent"""
    
    def __init__(self):
        super().__init__(name="文档解析Agent")
        self.add_dependency("validate")
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """解析文档结构"""
        await asyncio.sleep(0.4)
        
        pages = context.file_content.get("pages", [])
        total_text = ""
        page_info = []
        
        for i, page in enumerate(pages):
            if "content" in page:
                page_text = page["content"]
                total_text += page_text + "\n"
                page_info.append({
                    "page_number": i + 1,
                    "text_length": len(page_text),
                    "has_content": bool(page_text.strip())
                })
        
        return {
            "parse_info": {
                "parse_time": datetime.now().isoformat(),
                "total_text_length": len(total_text),
                "parsed_pages": len(pages),
                "page_details": page_info,
                "full_text": total_text[:1000] + "..." if len(total_text) > 1000 else total_text
            }
        }
    
    def get_stage_name(self) -> str:
        return "parse"
    
    def get_progress_percentage(self) -> int:
        return 30

class BasicExtractAgent(BaseAgent):
    """基础信息提取Agent"""
    
    def __init__(self):
        super().__init__(name="基础信息提取Agent")
        self.add_dependency("parse")
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """提取基础信息"""
        await asyncio.sleep(0.5)
        
        # 从解析结果中获取文本
        parse_info = context.get_extracted_data("parse_info", {})
        full_text = parse_info.get("full_text", "")
        
        # 这里应该调用AI模型提取基础信息
        # 暂时返回模拟数据
        basic_info = {
            "title": self._extract_title(full_text),
            "author": self._extract_author(full_text),
            "date": self._extract_date(full_text),
            "document_type": self._extract_document_type(full_text),
            "language": "中文",
            "word_count": len(full_text.replace(" ", ""))
        }
        
        return {
            "basic_info": basic_info,
            "extraction_metadata": {
                "extraction_time": datetime.now().isoformat(),
                "extraction_method": "rule_based",
                "confidence_score": 0.85
            }
        }
    
    def _extract_title(self, text: str) -> str:
        """提取标题"""
        lines = text.split('\n')
        for line in lines[:10]:  # 检查前10行
            line = line.strip()
            if len(line) > 5 and len(line) < 100:
                return line
        return "未识别标题"
    
    def _extract_author(self, text: str) -> str:
        """提取作者"""
        # 简单的规则匹配
        if "作者" in text:
            return "规则提取的作者"
        return "未识别作者"
    
    def _extract_date(self, text: str) -> str:
        """提取日期"""
        import re
        date_pattern = r'\d{4}年\d{1,2}月\d{1,2}日'
        match = re.search(date_pattern, text)
        if match:
            return match.group()
        return datetime.now().strftime("%Y年%m月%d日")
    
    def _extract_document_type(self, text: str) -> str:
        """提取文档类型"""
        if "报告" in text:
            return "报告"
        elif "通知" in text:
            return "通知"
        elif "方案" in text:
            return "方案"
        return "其他文档"
    
    def get_stage_name(self) -> str:
        return "extract_basic"
    
    def get_progress_percentage(self) -> int:
        return 45

class StructureExtractAgent(BaseAgent):
    """结构化信息提取Agent"""
    
    def __init__(self):
        super().__init__(name="结构化信息提取Agent")
        self.add_dependency("extract_basic")
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """提取结构化信息"""
        await asyncio.sleep(0.6)
        
        parse_info = context.get_extracted_data("parse_info", {})
        full_text = parse_info.get("full_text", "")
        
        # 提取文档结构
        structure_info = {
            "chapters": self._extract_chapters(full_text),
            "sections": self._extract_sections(full_text),
            "paragraphs": self._extract_paragraphs(full_text),
            "lists": self._extract_lists(full_text),
            "tables": self._extract_tables(full_text)
        }
        
        return {
            "structure_info": structure_info,
            "structure_metadata": {
                "extraction_time": datetime.now().isoformat(),
                "total_chapters": len(structure_info["chapters"]),
                "total_sections": len(structure_info["sections"]),
                "total_paragraphs": len(structure_info["paragraphs"])
            }
        }
    
    def _extract_chapters(self, text: str) -> list:
        """提取章节"""
        import re
        chapter_pattern = r'^第[一二三四五六七八九十\d]+章\s+(.+)$'
        chapters = []
        for line in text.split('\n'):
            match = re.match(chapter_pattern, line.strip())
            if match:
                chapters.append({
                    "title": match.group(1),
                    "level": 1,
                    "position": text.find(line)
                })
        return chapters
    
    def _extract_sections(self, text: str) -> list:
        """提取节"""
        import re
        section_pattern = r'^第[一二三四五六七八九十\d]+节\s+(.+)$'
        sections = []
        for line in text.split('\n'):
            match = re.match(section_pattern, line.strip())
            if match:
                sections.append({
                    "title": match.group(1),
                    "level": 2,
                    "position": text.find(line)
                })
        return sections
    
    def _extract_paragraphs(self, text: str) -> list:
        """提取段落"""
        paragraphs = []
        for i, para in enumerate(text.split('\n\n')):
            para = para.strip()
            if len(para) > 20:  # 过滤太短的段落
                paragraphs.append({
                    "id": i + 1,
                    "content": para[:200] + "..." if len(para) > 200 else para,
                    "length": len(para)
                })
        return paragraphs[:10]  # 只返回前10个段落
    
    def _extract_lists(self, text: str) -> list:
        """提取列表"""
        import re
        list_pattern = r'^[（(]\d+[）)]\s+(.+)$'
        lists = []
        for line in text.split('\n'):
            match = re.match(list_pattern, line.strip())
            if match:
                lists.append({
                    "content": match.group(1),
                    "type": "numbered"
                })
        return lists[:5]  # 只返回前5个列表项
    
    def _extract_tables(self, text: str) -> list:
        """提取表格"""
        # 简单的表格检测
        tables = []
        if "表" in text and "|" in text:
            tables.append({
                "type": "detected",
                "description": "检测到可能的表格结构"
            })
        return tables
    
    def get_stage_name(self) -> str:
        return "extract_structure"
    
    def get_progress_percentage(self) -> int:
        return 60

class EntityExtractAgent(BaseAgent):
    """实体信息提取Agent"""
    
    def __init__(self):
        super().__init__(name="实体信息提取Agent")
        self.add_dependency("extract_structure")
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """提取实体信息"""
        await asyncio.sleep(0.7)
        
        parse_info = context.get_extracted_data("parse_info", {})
        full_text = parse_info.get("full_text", "")
        
        # 提取各类实体
        entities = {
            "persons": self._extract_persons(full_text),
            "organizations": self._extract_organizations(full_text),
            "locations": self._extract_locations(full_text),
            "dates": self._extract_dates(full_text),
            "numbers": self._extract_numbers(full_text),
            "keywords": self._extract_keywords(full_text)
        }
        
        return {
            "entities": entities,
            "entity_metadata": {
                "extraction_time": datetime.now().isoformat(),
                "total_entities": sum(len(v) for v in entities.values()),
                "extraction_method": "rule_based_ner"
            }
        }
    
    def _extract_persons(self, text: str) -> list:
        """提取人名"""
        import re
        # 简单的人名模式
        person_patterns = [
            r'[张王李赵刘陈杨黄周吴徐孙胡朱高林何郭马罗梁宋郑谢韩唐冯于董萧程曹袁邓许傅沈曾彭吕苏卢蒋蔡贾丁魏薛叶阎余潘杜戴夏钟汪田任姜范方石姚谭廖邹熊金陆郝孔白崔康毛邱秦江史顾侯邵孟龙万段雷钱汤尹黎易常武乔贺赖龚文][一-龯]{1,3}'
        ]
        
        persons = []
        for pattern in person_patterns:
            matches = re.findall(pattern, text)
            for match in matches[:5]:  # 限制数量
                if match not in [p["name"] for p in persons]:
                    persons.append({
                        "name": match,
                        "type": "person",
                        "confidence": 0.7
                    })
        return persons
    
    def _extract_organizations(self, text: str) -> list:
        """提取机构名"""
        import re
        org_patterns = [
            r'[一-龯]*公司',
            r'[一-龯]*部门',
            r'[一-龯]*委员会',
            r'[一-龯]*管理局'
        ]
        
        organizations = []
        for pattern in org_patterns:
            matches = re.findall(pattern, text)
            for match in matches[:3]:
                if match not in [o["name"] for o in organizations]:
                    organizations.append({
                        "name": match,
                        "type": "organization",
                        "confidence": 0.8
                    })
        return organizations
    
    def _extract_locations(self, text: str) -> list:
        """提取地名"""
        import re
        location_patterns = [
            r'[一-龯]*省',
            r'[一-龯]*市',
            r'[一-龯]*区',
            r'[一-龯]*县'
        ]
        
        locations = []
        for pattern in location_patterns:
            matches = re.findall(pattern, text)
            for match in matches[:3]:
                if match not in [l["name"] for l in locations]:
                    locations.append({
                        "name": match,
                        "type": "location",
                        "confidence": 0.8
                    })
        return locations
    
    def _extract_dates(self, text: str) -> list:
        """提取日期"""
        import re
        date_patterns = [
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{4}-\d{1,2}-\d{1,2}',
            r'\d{4}/\d{1,2}/\d{1,2}'
        ]
        
        dates = []
        for pattern in date_patterns:
            matches = re.findall(pattern, text)
            for match in matches[:3]:
                if match not in [d["value"] for d in dates]:
                    dates.append({
                        "value": match,
                        "type": "date",
                        "confidence": 0.9
                    })
        return dates
    
    def _extract_numbers(self, text: str) -> list:
        """提取数字"""
        import re
        number_patterns = [
            r'\d+\.?\d*万',
            r'\d+\.?\d*亿',
            r'\d+\.?\d*%',
            r'\d+\.?\d*元'
        ]
        
        numbers = []
        for pattern in number_patterns:
            matches = re.findall(pattern, text)
            for match in matches[:5]:
                if match not in [n["value"] for n in numbers]:
                    numbers.append({
                        "value": match,
                        "type": "number",
                        "confidence": 0.9
                    })
        return numbers
    
    def _extract_keywords(self, text: str) -> list:
        """提取关键词"""
        # 简单的关键词提取
        common_words = ['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这']
        words = text.split()
        word_freq = {}
        
        for word in words:
            if len(word) > 1 and word not in common_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 取频率最高的前10个词作为关键词
        keywords = []
        for word, freq in sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]:
            keywords.append({
                "word": word,
                "frequency": freq,
                "type": "keyword"
            })
        
        return keywords
    
    def get_stage_name(self) -> str:
        return "extract_entities"
    
    def get_progress_percentage(self) -> int:
        return 80

class RelationExtractAgent(BaseAgent):
    """关系信息提取Agent"""

    def __init__(self):
        super().__init__(name="关系信息提取Agent")
        self.add_dependency("extract_entities")

    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """提取关系信息"""
        await asyncio.sleep(0.8)

        entities = context.get_extracted_data("entities", {})

        # 提取实体间关系
        relations = {
            "entity_relations": self._extract_entity_relations(entities),
            "semantic_relations": self._extract_semantic_relations(context),
            "dependency_relations": self._extract_dependency_relations(context)
        }

        return {
            "relations": relations,
            "relation_metadata": {
                "extraction_time": datetime.now().isoformat(),
                "total_relations": sum(len(v) for v in relations.values()),
                "extraction_method": "rule_based_relation"
            }
        }

    def _extract_entity_relations(self, entities: Dict[str, Any]) -> list:
        """提取实体间关系"""
        relations = []
        persons = entities.get("persons", [])
        organizations = entities.get("organizations", [])

        # 简单的关系推断
        for person in persons[:3]:
            for org in organizations[:2]:
                relations.append({
                    "subject": person["name"],
                    "predicate": "工作于",
                    "object": org["name"],
                    "confidence": 0.6
                })

        return relations

    def _extract_semantic_relations(self, context: AgentContext) -> list:
        """提取语义关系"""
        basic_info = context.get_extracted_data("basic_info", {})
        structure_info = context.get_extracted_data("structure_info", {})

        relations = []

        # 文档与章节的关系
        title = basic_info.get("title", "")
        chapters = structure_info.get("chapters", [])

        for chapter in chapters[:3]:
            relations.append({
                "subject": title,
                "predicate": "包含章节",
                "object": chapter["title"],
                "confidence": 0.9
            })

        return relations

    def _extract_dependency_relations(self, context: AgentContext) -> list:
        """提取依赖关系"""
        structure_info = context.get_extracted_data("structure_info", {})
        chapters = structure_info.get("chapters", [])
        sections = structure_info.get("sections", [])

        relations = []

        # 章节与节的依赖关系
        for i, chapter in enumerate(chapters[:2]):
            for section in sections[:2]:
                relations.append({
                    "subject": chapter["title"],
                    "predicate": "包含",
                    "object": section["title"],
                    "confidence": 0.7
                })

        return relations

    def get_stage_name(self) -> str:
        return "extract_relations"

    def get_progress_percentage(self) -> int:
        return 85

class MergeInfoAgent(BaseAgent):
    """信息合并Agent"""

    def __init__(self):
        super().__init__(name="信息合并Agent")
        self.add_dependency("extract_relations")

    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """合并和整理提取的信息"""
        await asyncio.sleep(0.3)

        # 收集所有提取的信息
        merged_info = {
            "document_summary": self._create_document_summary(context),
            "extracted_content": self._merge_extracted_content(context),
            "statistics": self._calculate_statistics(context)
        }

        return {
            "merged_info": merged_info,
            "merge_metadata": {
                "merge_time": datetime.now().isoformat(),
                "total_data_points": len(context.extracted_data),
                "merge_strategy": "comprehensive"
            }
        }

    def _create_document_summary(self, context: AgentContext) -> Dict[str, Any]:
        """创建文档摘要"""
        basic_info = context.get_extracted_data("basic_info", {})
        structure_info = context.get_extracted_data("structure_info", {})
        entities = context.get_extracted_data("entities", {})

        return {
            "title": basic_info.get("title", "未知标题"),
            "author": basic_info.get("author", "未知作者"),
            "document_type": basic_info.get("document_type", "未知类型"),
            "chapter_count": len(structure_info.get("chapters", [])),
            "entity_count": sum(len(v) for v in entities.values()) if entities else 0,
            "word_count": basic_info.get("word_count", 0)
        }

    def _merge_extracted_content(self, context: AgentContext) -> Dict[str, Any]:
        """合并提取的内容"""
        return {
            "basic_info": context.get_extracted_data("basic_info", {}),
            "structure_info": context.get_extracted_data("structure_info", {}),
            "entities": context.get_extracted_data("entities", {}),
            "relations": context.get_extracted_data("relations", {})
        }

    def _calculate_statistics(self, context: AgentContext) -> Dict[str, Any]:
        """计算统计信息"""
        entities = context.get_extracted_data("entities", {})
        relations = context.get_extracted_data("relations", {})

        return {
            "entity_statistics": {
                "persons": len(entities.get("persons", [])),
                "organizations": len(entities.get("organizations", [])),
                "locations": len(entities.get("locations", [])),
                "dates": len(entities.get("dates", [])),
                "numbers": len(entities.get("numbers", []))
            },
            "relation_statistics": {
                "entity_relations": len(relations.get("entity_relations", [])),
                "semantic_relations": len(relations.get("semantic_relations", [])),
                "dependency_relations": len(relations.get("dependency_relations", []))
            }
        }

    def get_stage_name(self) -> str:
        return "merge_info"

    def get_progress_percentage(self) -> int:
        return 90

class ValidateResultAgent(BaseAgent):
    """结果验证Agent"""

    def __init__(self):
        super().__init__(name="结果验证Agent")
        self.add_dependency("merge_info")

    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """验证提取结果的完整性和准确性"""
        await asyncio.sleep(0.2)

        validation_result = {
            "completeness_check": self._check_completeness(context),
            "accuracy_check": self._check_accuracy(context),
            "consistency_check": self._check_consistency(context)
        }

        overall_score = (
            validation_result["completeness_check"]["score"] +
            validation_result["accuracy_check"]["score"] +
            validation_result["consistency_check"]["score"]
        ) / 3

        return {
            "validation_result": {
                **validation_result,
                "overall_score": overall_score,
                "validation_passed": overall_score >= 0.7
            },
            "validation_metadata": {
                "validation_time": datetime.now().isoformat(),
                "validation_method": "rule_based_validation"
            }
        }

    def _check_completeness(self, context: AgentContext) -> Dict[str, Any]:
        """检查完整性"""
        required_fields = ["basic_info", "structure_info", "entities", "relations"]
        missing_fields = []

        for field in required_fields:
            if not context.get_extracted_data(field):
                missing_fields.append(field)

        score = (len(required_fields) - len(missing_fields)) / len(required_fields)

        return {
            "score": score,
            "missing_fields": missing_fields,
            "status": "passed" if score >= 0.8 else "failed"
        }

    def _check_accuracy(self, context: AgentContext) -> Dict[str, Any]:
        """检查准确性"""
        # 简单的准确性检查
        basic_info = context.get_extracted_data("basic_info", {})
        entities = context.get_extracted_data("entities", {})

        issues = []

        # 检查基础信息
        if not basic_info.get("title") or basic_info.get("title") == "未识别标题":
            issues.append("标题提取可能不准确")

        # 检查实体数量合理性
        total_entities = sum(len(v) for v in entities.values()) if entities else 0
        if total_entities == 0:
            issues.append("未提取到任何实体")
        elif total_entities > 100:
            issues.append("实体数量过多，可能存在误识别")

        score = max(0, 1 - len(issues) * 0.2)

        return {
            "score": score,
            "issues": issues,
            "status": "passed" if score >= 0.7 else "warning"
        }

    def _check_consistency(self, context: AgentContext) -> Dict[str, Any]:
        """检查一致性"""
        # 检查数据一致性
        basic_info = context.get_extracted_data("basic_info", {})
        structure_info = context.get_extracted_data("structure_info", {})

        inconsistencies = []

        # 检查文档类型与结构的一致性
        doc_type = basic_info.get("document_type", "")
        chapters = structure_info.get("chapters", [])

        if doc_type == "报告" and len(chapters) == 0:
            inconsistencies.append("报告类型文档通常应包含章节结构")

        score = max(0, 1 - len(inconsistencies) * 0.3)

        return {
            "score": score,
            "inconsistencies": inconsistencies,
            "status": "passed" if score >= 0.7 else "warning"
        }

    def get_stage_name(self) -> str:
        return "validate_result"

    def get_progress_percentage(self) -> int:
        return 95

class StoreAgent(BaseAgent):
    """存储结果Agent - 实现ES和MySQL存储"""

    def __init__(self):
        super().__init__(name="存储结果Agent")
        self.add_dependency("validate_result")

    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """保存结构化信息到ES和MySQL数据库"""
        await asyncio.sleep(0.3)

        # 获取提取的数据
        merged_info = context.get_extracted_data("merged_info", {})
        validation_result = context.get_extracted_data("validation_result", {})

        # 从上下文获取项目信息
        project_name = context.metadata.get("project_name", "未知项目")
        action = context.metadata.get("action", "项目档案")

        storage_results = {
            "es_storage": False,
            "mysql_storage": False,
            "stored_records": 0,
            "errors": []
        }

        try:
            # 1. ES存储 - 更新项目信息索引
            es_success = await self._store_to_elasticsearch(
                project_name, merged_info, context
            )
            storage_results["es_storage"] = es_success

            # 2. MySQL存储 - 存储到conference_extract或project_extract表
            mysql_success = await self._store_to_mysql(
                project_name, action, merged_info, context
            )
            storage_results["mysql_storage"] = mysql_success

            if mysql_success:
                storage_results["stored_records"] = 1

        except Exception as e:
            error_msg = f"存储过程异常: {str(e)}"
            storage_results["errors"].append(error_msg)
            print(error_msg)

        # 判断整体存储是否成功
        overall_success = storage_results["es_storage"] and storage_results["mysql_storage"]

        return {
            "storage_result": {
                "storage_success": overall_success,
                "es_storage": storage_results["es_storage"],
                "mysql_storage": storage_results["mysql_storage"],
                "stored_records": storage_results["stored_records"],
                "validation_passed": validation_result.get("validation_passed", False),
                "errors": storage_results["errors"]
            },
            "storage_metadata": {
                "storage_time": datetime.now().isoformat(),
                "storage_method": "elasticsearch_mysql",
                "project_name": project_name,
                "action": action,
                "backup_created": overall_success
            }
        }

    async def _store_to_elasticsearch(
        self,
        project_name: str,
        merged_info: Dict[str, Any],
        context: AgentContext
    ) -> bool:
        """存储到Elasticsearch"""
        try:
            # 导入ES相关模块
            from utils.eskg import ESKnowledge
            from utils.config import config

            # 获取ES配置
            es_config = {
                'host': config.get('elasticsearch.host', 'http://***********:9200'),
                'port': config.get('elasticsearch.port', 9200),
                'username': config.get('elasticsearch.username', 'elastic'),
                'password': config.get('elasticsearch.password', 'elastic')
            }

            print(f"ES配置: {es_config}")

            # 创建ES客户端
            es_knowledge = ESKnowledge(es_config)

            # 生成doc_id
            doc_id = context.metadata.get("doc_id") or str(hash(project_name))

            # 更新项目信息到ES
            success = await es_knowledge.update_project_info(merged_info, doc_id)

            if success:
                print(f"✅ ES存储成功: project={project_name}, doc_id={doc_id}")
            else:
                print(f"❌ ES存储失败: project={project_name}")

            return success

        except Exception as e:
            print(f"❌ ES存储异常: {str(e)}")
            return False

    async def _store_to_mysql(
        self,
        project_name: str,
        action: str,
        merged_info: Dict[str, Any],
        context: AgentContext
    ) -> bool:
        """存储到MySQL数据库"""
        try:
            # 导入MySQL相关模块
            from utils.cdb_mysql import MySQLDatabase

            # 创建MySQL客户端
            mysql_db = MySQLDatabase()

            # 确保表存在
            await mysql_db.create_tables()

            # 准备存储数据
            storage_data = merged_info.copy()
            storage_data["name"] = project_name

            # 根据action类型选择表名
            if action == "文书档案":
                table_name = "conference_extract"
                success = await mysql_db.insert_conference_extract(**storage_data)
            else:
                table_name = "project_extract"
                success = await mysql_db.insert_project_extract(**storage_data)

            if success:
                print(f"✅ MySQL存储成功: table={table_name}, project={project_name}")
            else:
                print(f"❌ MySQL存储失败: table={table_name}, project={project_name}")

            return success

        except Exception as e:
            print(f"❌ MySQL存储异常: {str(e)}")
            return False

    def _simulate_storage(self, merged_info: Dict[str, Any]) -> Dict[str, int]:
        """模拟存储操作"""
        return {
            "document_records": 1,
            "entity_records": len(merged_info.get("extracted_content", {}).get("entities", {}).get("persons", [])),
            "relation_records": len(merged_info.get("extracted_content", {}).get("relations", {}).get("entity_relations", [])),
            "structure_records": len(merged_info.get("extracted_content", {}).get("structure_info", {}).get("chapters", []))
        }

    def get_stage_name(self) -> str:
        return "store"

    def get_progress_percentage(self) -> int:
        return 98

class CompleteAgent(BaseAgent):
    """完成Agent"""

    def __init__(self):
        super().__init__(name="完成Agent")
        self.add_dependency("store")

    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """完成所有处理步骤"""
        await asyncio.sleep(0.1)

        # 生成最终报告
        final_report = {
            "task_completed": True,
            "completion_time": datetime.now().isoformat(),
            "processing_summary": self._generate_summary(context),
            "quality_metrics": self._calculate_quality_metrics(context)
        }

        return {
            "completion_result": final_report,
            "completion_metadata": {
                "total_processing_time": self._calculate_total_time(context),
                "success_rate": 1.0,
                "final_status": "completed"
            }
        }

    def _generate_summary(self, context: AgentContext) -> Dict[str, Any]:
        """生成处理摘要"""
        merged_info = context.get_extracted_data("merged_info", {})
        document_summary = merged_info.get("document_summary", {})

        return {
            "document_title": document_summary.get("title", "未知"),
            "total_entities": document_summary.get("entity_count", 0),
            "total_chapters": document_summary.get("chapter_count", 0),
            "processing_stages": len(context.extracted_data)
        }

    def _calculate_quality_metrics(self, context: AgentContext) -> Dict[str, Any]:
        """计算质量指标"""
        validation_result = context.get_extracted_data("validation_result", {})

        return {
            "overall_quality_score": validation_result.get("overall_score", 0.0),
            "completeness_score": validation_result.get("completeness_check", {}).get("score", 0.0),
            "accuracy_score": validation_result.get("accuracy_check", {}).get("score", 0.0),
            "consistency_score": validation_result.get("consistency_check", {}).get("score", 0.0)
        }

    def _calculate_total_time(self, context: AgentContext) -> float:
        """计算总处理时间"""
        init_info = context.get_extracted_data("init_info", {})
        start_time = init_info.get("processing_start_time")

        if start_time:
            start = datetime.fromisoformat(start_time)
            end = datetime.now()
            return (end - start).total_seconds()

        return 0.0

    def get_stage_name(self) -> str:
        return "complete"

    def get_progress_percentage(self) -> int:
        return 100
