#!/usr/bin/env python3
"""
业务LLM提取Agent - 基于配置提取结构化信息
"""

import asyncio
import os
import sys
import json
from typing import Dict, Any, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, '/workspace/hngpt')

from .business_base_agent import BusinessBaseAgent, BusinessAgentContext

# 导入实际的Extractor
try:
    from utils.extractor import Extractor
    from utils.llm import LLM
except ImportError:
    print("警告: 无法导入Extractor或LLM，使用备用实现")
    
    class LLM:
        """备用的LLM实现"""
        async def chat(self, messages):
            return "模拟LLM响应"
    
    class Extractor:
        """备用的Extractor实现"""
        def __init__(self, llm=None):
            self.llm = llm or LLM()
        
        async def extract_project(self, text, project_name, config):
            """模拟项目信息提取"""
            return {
                "project_no": "PROJ-2024-001",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "total_investment": "500万元",
                "responsible_unit": "技术开发部",
                "leader": "张三",
                "research_points": "智能系统开发",
                "innovation": "AI技术应用",
                "main_deliverables": "软件系统",
                "patent": "专利申请中"
            }
        
        async def extract_conference(self, text, project_name, config):
            """模拟会议信息提取"""
            return {
                "name": project_name,
                "date": "2024-01-15",
                "type": "决策会议",
                "organizer": "技术部",
                "participants": "张三、李四、王五",
                "summary": "讨论项目进展和技术方案"
            }

class BusinessLLMExtractionAgent(BusinessBaseAgent):
    """业务LLM提取Agent"""
    
    def __init__(self):
        super().__init__(name="业务LLM提取Agent")
        self.add_dependency("storage_info")  # 依赖ES存储Agent
        self.extractor = None
        self.llm = None
    
    async def process(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """基于配置提取结构化信息"""
        await asyncio.sleep(1.0)  # 模拟LLM提取时间
        
        # 初始化LLM和Extractor
        await self._initialize_extractor(context.config)
        
        # 获取存储信息和文本内容
        storage_info = context.get_extracted_data("storage_info", {})
        extracted_texts = context.get_extracted_data("extracted_texts", [])
        
        # 执行信息提取
        extraction_results = await self._extract_information(extracted_texts, context)
        
        # 验证提取结果
        extraction_validation = self._validate_extraction(extraction_results, context.action)
        
        # 计算提取统计
        extraction_statistics = self._calculate_extraction_statistics(extraction_results)
        
        # 合并和优化提取结果
        merged_information = self._merge_extraction_results(extraction_results, context)
        
        # 生成提取报告
        extraction_report = self._generate_extraction_report(extraction_results, merged_information, context)
        
        return {
            "extraction_info": {
                "extraction_time": datetime.now().isoformat(),
                "extraction_type": context.action,
                "total_documents": len(extracted_texts),
                "successful_extractions": extraction_statistics["successful_extractions"],
                "failed_extractions": extraction_statistics["failed_extractions"],
                "llm_model": "hngpt",
                "extraction_method": "config_driven"
            },
            "extraction_results": extraction_results,
            "extraction_validation": extraction_validation,
            "extraction_statistics": extraction_statistics,
            "merged_information": merged_information,
            "extraction_report": extraction_report,
            "extraction_summary": {
                "status": "completed",
                "extraction_successful": extraction_statistics["successful_extractions"] > 0,
                "structured_data_ready": bool(merged_information),
                "next_stage": "database_storage"
            }
        }
    
    async def _initialize_extractor(self, config: Dict[str, Any]):
        """初始化LLM和Extractor"""
        if self.extractor is None:
            try:
                # 初始化LLM
                self.llm = LLM()
                
                # 初始化Extractor
                self.extractor = Extractor(llm=self.llm)
                print("✅ LLM和Extractor初始化成功")
            except Exception as e:
                print(f"⚠️ LLM和Extractor初始化失败，使用备用模式: {e}")
                self.llm = LLM()
                self.extractor = Extractor(llm=self.llm)
    
    async def _extract_information(self, extracted_texts: List[Dict[str, Any]], context: BusinessAgentContext) -> List[Dict[str, Any]]:
        """执行信息提取"""
        extraction_results = []
        
        for text_info in extracted_texts:
            try:
                file_name = text_info.get("file_name", "unknown")
                content = text_info.get("content", "")
                metadata = text_info.get("metadata", {})
                
                if not content.strip():
                    extraction_results.append({
                        "file_name": file_name,
                        "extraction_success": False,
                        "error": "Empty content",
                        "extracted_data": {},
                        "metadata": metadata
                    })
                    continue
                
                # 根据action类型选择提取方法
                if context.action == "项目档案":
                    extracted_data = await self.extractor.extract_project(
                        text=content,
                        project_name=context.project_name,
                        config=context.config.get("project_extract", {})
                    )
                elif context.action == "文书档案":
                    extracted_data = await self.extractor.extract_conference(
                        text=content,
                        project_name=context.project_name,
                        config=context.config.get("conference_extract", {})
                    )
                else:
                    extracted_data = None
                
                if extracted_data:
                    extraction_results.append({
                        "file_name": file_name,
                        "extraction_success": True,
                        "error": None,
                        "extracted_data": extracted_data,
                        "metadata": metadata,
                        "extraction_details": {
                            "extraction_type": context.action,
                            "content_length": len(content),
                            "fields_extracted": len(extracted_data),
                            "extraction_time": datetime.now().isoformat()
                        }
                    })
                else:
                    extraction_results.append({
                        "file_name": file_name,
                        "extraction_success": False,
                        "error": "Extraction returned None",
                        "extracted_data": {},
                        "metadata": metadata
                    })
                
            except Exception as e:
                extraction_results.append({
                    "file_name": text_info.get("file_name", "unknown"),
                    "extraction_success": False,
                    "error": str(e),
                    "extracted_data": {},
                    "metadata": text_info.get("metadata", {})
                })
        
        return extraction_results
    
    def _validate_extraction(self, extraction_results: List[Dict[str, Any]], action: str) -> Dict[str, Any]:
        """验证提取结果"""
        validation = {
            "total_files": len(extraction_results),
            "successful_extractions": 0,
            "failed_extractions": 0,
            "field_completeness": {},
            "data_quality_score": 0.0,
            "validation_details": []
        }
        
        # 根据action类型定义必需字段
        if action == "项目档案":
            required_fields = ["project_no", "start_date", "end_date", "total_investment", "responsible_unit", "leader"]
            optional_fields = ["research_points", "innovation", "main_deliverables", "patent"]
        elif action == "文书档案":
            required_fields = ["name", "date", "type", "organizer"]
            optional_fields = ["participants", "summary"]
        else:
            required_fields = []
            optional_fields = []
        
        all_fields = required_fields + optional_fields
        field_counts = {field: 0 for field in all_fields}
        total_successful = 0
        
        for result in extraction_results:
            if result["extraction_success"]:
                validation["successful_extractions"] += 1
                total_successful += 1
                extracted_data = result["extracted_data"]
                
                # 统计字段完整性
                valid_fields = 0
                total_fields = len(all_fields)
                
                for field in all_fields:
                    if field in extracted_data and extracted_data[field] and extracted_data[field] != "未提及":
                        field_counts[field] += 1
                        valid_fields += 1
                
                field_completeness = valid_fields / total_fields if total_fields > 0 else 0
                
                validation["validation_details"].append({
                    "file_name": result["file_name"],
                    "status": "success",
                    "fields_extracted": len(extracted_data),
                    "field_completeness": field_completeness,
                    "required_fields_present": sum(1 for f in required_fields if f in extracted_data and extracted_data[f] != "未提及"),
                    "optional_fields_present": sum(1 for f in optional_fields if f in extracted_data and extracted_data[f] != "未提及")
                })
            else:
                validation["failed_extractions"] += 1
                validation["validation_details"].append({
                    "file_name": result["file_name"],
                    "status": "failed",
                    "error": result.get("error", "unknown")
                })
        
        # 计算字段完整性
        if total_successful > 0:
            for field, count in field_counts.items():
                validation["field_completeness"][field] = count / total_successful
        
        # 计算数据质量分数
        if all_fields:
            avg_completeness = sum(validation["field_completeness"].values()) / len(all_fields)
            success_rate = validation["successful_extractions"] / validation["total_files"]
            validation["data_quality_score"] = (avg_completeness + success_rate) / 2
        
        return validation
    
    def _calculate_extraction_statistics(self, extraction_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算提取统计"""
        stats = {
            "total_files": len(extraction_results),
            "successful_extractions": 0,
            "failed_extractions": 0,
            "total_fields_extracted": 0,
            "unique_fields": set(),
            "extraction_efficiency": 0.0,
            "average_fields_per_document": 0.0
        }
        
        for result in extraction_results:
            if result["extraction_success"]:
                stats["successful_extractions"] += 1
                extracted_data = result["extracted_data"]
                
                # 统计有效字段（非空且不是"未提及"）
                valid_fields = 0
                for field, value in extracted_data.items():
                    if value and value != "未提及":
                        valid_fields += 1
                        stats["unique_fields"].add(field)
                
                stats["total_fields_extracted"] += valid_fields
            else:
                stats["failed_extractions"] += 1
        
        # 计算效率指标
        if stats["total_files"] > 0:
            stats["extraction_efficiency"] = stats["successful_extractions"] / stats["total_files"]
        
        if stats["successful_extractions"] > 0:
            stats["average_fields_per_document"] = stats["total_fields_extracted"] / stats["successful_extractions"]
        
        # 转换set为list以便JSON序列化
        stats["unique_fields"] = list(stats["unique_fields"])
        
        return stats
    
    def _merge_extraction_results(self, extraction_results: List[Dict[str, Any]], context: BusinessAgentContext) -> Dict[str, Any]:
        """合并和优化提取结果"""
        merged = {
            "project_name": context.project_name,
            "action": context.action,
            "task_id": context.task_id,
            "merge_time": datetime.now().isoformat(),
            "source_files": [],
            "merged_data": {},
            "confidence_scores": {}
        }
        
        # 收集所有成功的提取结果
        successful_extractions = [r for r in extraction_results if r["extraction_success"]]
        
        if not successful_extractions:
            return merged
        
        # 如果只有一个成功的提取结果，直接使用
        if len(successful_extractions) == 1:
            result = successful_extractions[0]
            merged["merged_data"] = result["extracted_data"].copy()
            merged["source_files"] = [result["file_name"]]
            merged["confidence_scores"] = {field: 1.0 for field in merged["merged_data"]}
        else:
            # 多个结果需要合并
            field_values = {}
            field_sources = {}
            
            for result in successful_extractions:
                merged["source_files"].append(result["file_name"])
                extracted_data = result["extracted_data"]
                
                for field, value in extracted_data.items():
                    if value and value != "未提及":
                        if field not in field_values:
                            field_values[field] = []
                            field_sources[field] = []
                        
                        field_values[field].append(value)
                        field_sources[field].append(result["file_name"])
            
            # 合并字段值
            for field, values in field_values.items():
                if len(values) == 1:
                    merged["merged_data"][field] = values[0]
                    merged["confidence_scores"][field] = 1.0
                else:
                    # 多个值时选择最常见的，或者合并
                    if field in ["research_points", "innovation", "main_deliverables", "participants", "summary"]:
                        # 这些字段可以合并
                        unique_values = list(dict.fromkeys(values))  # 去重保持顺序
                        merged["merged_data"][field] = "；".join(unique_values)
                        merged["confidence_scores"][field] = 0.8
                    else:
                        # 其他字段选择第一个值
                        merged["merged_data"][field] = values[0]
                        merged["confidence_scores"][field] = 0.6
        
        return merged
    
    def _generate_extraction_report(self, extraction_results: List[Dict[str, Any]], merged_information: Dict[str, Any], context: BusinessAgentContext) -> Dict[str, Any]:
        """生成提取报告"""
        report = {
            "report_time": datetime.now().isoformat(),
            "task_info": {
                "task_id": context.task_id,
                "project_name": context.project_name,
                "action": context.action
            },
            "extraction_summary": {
                "total_files": len(extraction_results),
                "successful_extractions": sum(1 for r in extraction_results if r["extraction_success"]),
                "failed_extractions": sum(1 for r in extraction_results if not r["extraction_success"]),
                "fields_extracted": len(merged_information.get("merged_data", {}))
            },
            "file_details": [],
            "merged_result": merged_information,
            "recommendations": []
        }
        
        # 文件详情
        for result in extraction_results:
            report["file_details"].append({
                "file_name": result["file_name"],
                "extraction_success": result["extraction_success"],
                "fields_count": len(result["extracted_data"]) if result["extraction_success"] else 0,
                "error": result.get("error")
            })
        
        # 生成建议
        if report["extraction_summary"]["failed_extractions"] > 0:
            report["recommendations"].append("部分文件提取失败，建议检查文件内容和格式")
        
        if len(merged_information.get("merged_data", {})) < 5:
            report["recommendations"].append("提取的字段较少，建议检查文档内容是否包含相关信息")
        
        if not report["recommendations"]:
            report["recommendations"].append("信息提取完成，数据质量良好")
        
        return report
    
    def get_stage_name(self) -> str:
        return "llm_extraction"
    
    def get_progress_percentage(self) -> int:
        return 90
    
    def get_stage_description(self) -> str:
        return "基于配置提取结构化信息"
