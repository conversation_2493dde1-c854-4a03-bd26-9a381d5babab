#!/usr/bin/env python3
"""
初始化Agent - 负责初始化文档处理环境
"""

import asyncio
import json
import os
from typing import Dict, Any
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class InitAgent(BaseAgent):
    """初始化Agent - 设置处理环境和基础信息"""
    
    def __init__(self):
        super().__init__(name="初始化Agent")
        # 初始化Agent没有依赖
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """初始化处理环境"""
        await asyncio.sleep(0.2)  # 模拟初始化时间
        
        # 验证输入参数
        if not context.task_id:
            raise ValueError("task_id不能为空")
        if not context.file_url:
            raise ValueError("file_url不能为空")
        
        # 创建处理目录
        processing_dir = f"/tmp/agent_processing/{context.task_id}"
        os.makedirs(processing_dir, exist_ok=True)
        
        # 初始化基础信息
        init_info = {
            "processing_start_time": datetime.now().isoformat(),
            "task_id": context.task_id,
            "file_url": context.file_url,
            "processing_dir": processing_dir,
            "initialized": True,
            "agent_version": "1.0.0",
            "processing_mode": "agent_workflow",
            "file_size": len(str(context.file_content)) if context.file_content else 0,
            "initialization_status": "success"
        }
        
        # 设置元数据
        metadata_info = {
            "workflow_id": context.metadata.get("workflow_id", "unknown"),
            "processing_pipeline": "document_analysis",
            "expected_stages": 12,
            "current_stage": 1,
            "stage_name": "initialization"
        }
        
        # 环境检查
        environment_check = {
            "temp_dir_available": os.path.exists("/tmp"),
            "processing_dir_created": os.path.exists(processing_dir),
            "memory_available": True,  # 简化检查
            "dependencies_loaded": True
        }
        
        return {
            "init_info": init_info,
            "metadata_info": metadata_info,
            "environment_check": environment_check,
            "initialization_summary": {
                "status": "completed",
                "duration": 0.2,
                "next_stage": "download",
                "ready_for_processing": all(environment_check.values())
            }
        }
    
    def get_stage_name(self) -> str:
        return "init"
    
    def get_progress_percentage(self) -> int:
        return 5
    
    def get_stage_description(self) -> str:
        return "初始化处理环境和基础配置"
    
    def validate_input(self, context: AgentContext) -> bool:
        """验证输入数据"""
        if not context.task_id or not context.file_url:
            return False
        return True
    
    def cleanup(self, context: AgentContext):
        """清理资源"""
        processing_dir = f"/tmp/agent_processing/{context.task_id}"
        if os.path.exists(processing_dir):
            try:
                import shutil
                shutil.rmtree(processing_dir)
            except Exception as e:
                print(f"清理目录失败: {e}")
