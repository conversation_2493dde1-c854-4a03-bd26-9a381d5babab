#!/usr/bin/env python3
"""
HNGPT标准化Agent基础框架
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json

class AgentStatus(Enum):
    """Agent状态枚举"""
    PENDING = "pending"
    READY = "ready"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"

class DataType(Enum):
    """数据类型枚举"""
    TEXT = "text"
    JSON = "json"
    BINARY = "binary"
    LIST = "list"
    DICT = "dict"
    FILE_PATH = "file_path"
    URL = "url"

@dataclass
class DataSchema:
    """数据模式定义"""
    name: str
    data_type: DataType
    required: bool = True
    description: str = ""
    default_value: Any = None
    validation_rules: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AgentMetadata:
    """Agent元数据"""
    name: str
    version: str = "1.0.0"
    description: str = ""
    author: str = ""
    tags: List[str] = field(default_factory=list)
    category: str = "general"
    estimated_duration: float = 0.0  # 预估执行时间(秒)
    resource_requirements: Dict[str, Any] = field(default_factory=dict)

@dataclass
class WorkflowContext:
    """标准化工作流上下文"""
    task_id: str
    workflow_id: str
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def get_data(self, key: str, default: Any = None) -> Any:
        """获取数据"""
        return self.data.get(key, default)
    
    def set_data(self, key: str, value: Any) -> None:
        """设置数据"""
        self.data[key] = value
        self.updated_at = datetime.now().isoformat()
    
    def has_data(self, key: str) -> bool:
        """检查数据是否存在"""
        return key in self.data
    
    def remove_data(self, key: str) -> None:
        """移除数据"""
        if key in self.data:
            del self.data[key]
            self.updated_at = datetime.now().isoformat()
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """获取元数据"""
        return self.metadata.get(key, default)
    
    def set_metadata(self, key: str, value: Any) -> None:
        """设置元数据"""
        self.metadata[key] = value
        self.updated_at = datetime.now().isoformat()

@dataclass
class AgentResult:
    """标准化Agent执行结果"""
    success: bool
    status: AgentStatus
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    metrics: Dict[str, Any] = field(default_factory=dict)
    errors: List[Dict[str, Any]] = field(default_factory=list)
    warnings: List[Dict[str, Any]] = field(default_factory=list)
    execution_time: float = 0.0
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def add_error(self, error_type: str, message: str, details: Dict[str, Any] = None):
        """添加错误信息"""
        self.errors.append({
            "type": error_type,
            "message": message,
            "details": details or {},
            "timestamp": datetime.now().isoformat()
        })
    
    def add_warning(self, warning_type: str, message: str, details: Dict[str, Any] = None):
        """添加警告信息"""
        self.warnings.append({
            "type": warning_type,
            "message": message,
            "details": details or {},
            "timestamp": datetime.now().isoformat()
        })
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "success": self.success,
            "status": self.status.value,
            "data": self.data,
            "metadata": self.metadata,
            "metrics": self.metrics,
            "errors": self.errors,
            "warnings": self.warnings,
            "execution_time": self.execution_time,
            "timestamp": self.timestamp
        }

class HNGPTBaseAgent(ABC):
    """HNGPT标准化Agent基类"""
    
    def __init__(self, agent_id: str = None):
        self.agent_id = agent_id or f"{self.__class__.__name__}_{uuid.uuid4().hex[:8]}"
        self.status = AgentStatus.PENDING
        self.metadata = self._get_metadata()
        self.input_schemas: List[DataSchema] = self._define_input_schemas()
        self.output_schemas: List[DataSchema] = self._define_output_schemas()
        self.dependencies: List[str] = self._define_dependencies()
        self.parallel_group: Optional[str] = self._get_parallel_group()
        self.retry_config = self._get_retry_config()
        self.timeout_seconds = self._get_timeout_seconds()
        
        # 运行时状态
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.retry_count = 0
        self.last_error: Optional[Exception] = None
    
    @abstractmethod
    def _get_metadata(self) -> AgentMetadata:
        """获取Agent元数据"""
        pass
    
    @abstractmethod
    def _define_input_schemas(self) -> List[DataSchema]:
        """定义输入数据模式"""
        pass
    
    @abstractmethod
    def _define_output_schemas(self) -> List[DataSchema]:
        """定义输出数据模式"""
        pass
    
    def _define_dependencies(self) -> List[str]:
        """定义依赖的Agent类型（可选重写）"""
        return []
    
    def _get_parallel_group(self) -> Optional[str]:
        """获取并行组标识（可选重写）"""
        return None
    
    def _get_retry_config(self) -> Dict[str, Any]:
        """获取重试配置（可选重写）"""
        return {
            "max_retries": 3,
            "retry_delay": 1.0,
            "exponential_backoff": True
        }
    
    def _get_timeout_seconds(self) -> float:
        """获取超时时间（可选重写）"""
        return 300.0  # 默认5分钟
    
    @abstractmethod
    async def process(self, context: WorkflowContext) -> Dict[str, Any]:
        """
        Agent的核心处理逻辑
        
        Args:
            context: 工作流上下文
            
        Returns:
            Dict[str, Any]: 处理结果数据
            
        Raises:
            Exception: 处理过程中的任何异常
        """
        pass
    
    def validate_inputs(self, context: WorkflowContext) -> bool:
        """验证输入数据"""
        missing_required = []
        invalid_data = []
        
        for schema in self.input_schemas:
            if schema.required and not context.has_data(schema.name):
                missing_required.append(schema.name)
            elif context.has_data(schema.name):
                value = context.get_data(schema.name)
                if not self._validate_data_type(value, schema):
                    invalid_data.append({
                        "field": schema.name,
                        "expected_type": schema.data_type.value,
                        "actual_type": type(value).__name__
                    })
        
        if missing_required:
            raise ValueError(f"Agent {self.metadata.name} missing required inputs: {missing_required}")
        
        if invalid_data:
            raise ValueError(f"Agent {self.metadata.name} invalid input data: {invalid_data}")
        
        return True
    
    def _validate_data_type(self, value: Any, schema: DataSchema) -> bool:
        """验证数据类型"""
        if schema.data_type == DataType.TEXT:
            return isinstance(value, str)
        elif schema.data_type == DataType.JSON:
            return isinstance(value, (dict, list))
        elif schema.data_type == DataType.LIST:
            return isinstance(value, list)
        elif schema.data_type == DataType.DICT:
            return isinstance(value, dict)
        elif schema.data_type == DataType.FILE_PATH:
            return isinstance(value, str) and len(value) > 0
        elif schema.data_type == DataType.URL:
            return isinstance(value, str) and (value.startswith('http') or value.startswith('file'))
        else:
            return True  # 其他类型暂时不验证
    
    def validate_outputs(self, output_data: Dict[str, Any]) -> bool:
        """验证输出数据"""
        missing_required = []
        
        for schema in self.output_schemas:
            if schema.required and schema.name not in output_data:
                missing_required.append(schema.name)
        
        if missing_required:
            raise ValueError(f"Agent {self.metadata.name} missing required outputs: {missing_required}")
        
        return True
    
    async def execute(self, context: WorkflowContext) -> AgentResult:
        """执行Agent"""
        self.start_time = datetime.now()
        self.status = AgentStatus.RUNNING
        
        result = AgentResult(
            success=False,
            status=AgentStatus.RUNNING,
            metadata={
                "agent_id": self.agent_id,
                "agent_name": self.metadata.name,
                "agent_version": self.metadata.version
            }
        )
        
        try:
            # 验证输入
            self.validate_inputs(context)
            
            # 执行处理逻辑
            output_data = await asyncio.wait_for(
                self.process(context),
                timeout=self.timeout_seconds
            )
            
            # 验证输出
            self.validate_outputs(output_data)
            
            # 成功完成
            self.end_time = datetime.now()
            execution_time = (self.end_time - self.start_time).total_seconds()
            
            result.success = True
            result.status = AgentStatus.COMPLETED
            result.data = output_data
            result.execution_time = execution_time
            result.metrics = {
                "start_time": self.start_time.isoformat(),
                "end_time": self.end_time.isoformat(),
                "retry_count": self.retry_count
            }
            
            self.status = AgentStatus.COMPLETED
            
        except asyncio.TimeoutError:
            self.status = AgentStatus.FAILED
            result.status = AgentStatus.FAILED
            result.add_error("timeout", f"Agent execution timed out after {self.timeout_seconds} seconds")
            
        except Exception as e:
            self.status = AgentStatus.FAILED
            result.status = AgentStatus.FAILED
            result.add_error("execution_error", str(e), {"exception_type": type(e).__name__})
            self.last_error = e
            
        finally:
            if self.end_time is None:
                self.end_time = datetime.now()
            result.execution_time = (self.end_time - self.start_time).total_seconds()
        
        return result
    
    def get_info(self) -> Dict[str, Any]:
        """获取Agent信息"""
        return {
            "agent_id": self.agent_id,
            "metadata": {
                "name": self.metadata.name,
                "version": self.metadata.version,
                "description": self.metadata.description,
                "category": self.metadata.category,
                "tags": self.metadata.tags
            },
            "status": self.status.value,
            "input_schemas": [
                {
                    "name": schema.name,
                    "type": schema.data_type.value,
                    "required": schema.required,
                    "description": schema.description
                }
                for schema in self.input_schemas
            ],
            "output_schemas": [
                {
                    "name": schema.name,
                    "type": schema.data_type.value,
                    "required": schema.required,
                    "description": schema.description
                }
                for schema in self.output_schemas
            ],
            "dependencies": self.dependencies,
            "parallel_group": self.parallel_group,
            "estimated_duration": self.metadata.estimated_duration
        }
    
    def can_execute(self, completed_agents: List[str]) -> bool:
        """检查是否可以执行（依赖是否满足）"""
        return all(dep in completed_agents for dep in self.dependencies)
    
    def __str__(self) -> str:
        return f"{self.metadata.name}({self.agent_id})"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__} id={self.agent_id} status={self.status.value}>"
