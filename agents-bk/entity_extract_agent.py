#!/usr/bin/env python3
"""
实体信息提取Agent - 负责提取人名、机构、地名、日期、数字、关键词等实体
"""

import asyncio
import re
from typing import Dict, Any, List, Set
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class EntityExtractAgent(BaseAgent):
    """实体信息提取Agent - 提取各类命名实体和关键信息"""
    
    def __init__(self):
        super().__init__(name="实体信息提取Agent")
        self.add_dependency("extract_structure")  # 依赖结构化信息提取Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """提取文档中的实体信息"""
        await asyncio.sleep(0.7)  # 模拟提取时间
        
        # 获取结构化信息
        parse_info = context.get_extracted_data("parse_info", {})
        basic_info = context.get_extracted_data("basic_info", {})
        structure_info = context.get_extracted_data("structure_info", {})
        
        full_text = parse_info.get("full_text", "")
        
        # 提取各类实体
        person_entities = self._extract_persons(full_text)
        organization_entities = self._extract_organizations(full_text)
        location_entities = self._extract_locations(full_text)
        date_entities = self._extract_dates(full_text)
        number_entities = self._extract_numbers(full_text)
        keyword_entities = self._extract_keywords(full_text)
        technical_entities = self._extract_technical_terms(full_text)
        
        # 实体统计和分析
        entity_statistics = self._calculate_entity_statistics(
            person_entities, organization_entities, location_entities,
            date_entities, number_entities, keyword_entities, technical_entities
        )
        
        # 实体关联分析
        entity_associations = self._analyze_entity_associations(
            person_entities, organization_entities, location_entities
        )
        
        # 重要实体识别
        important_entities = self._identify_important_entities(
            person_entities, organization_entities, location_entities,
            keyword_entities, technical_entities
        )
        
        return {
            "entity_info": {
                "extraction_time": datetime.now().isoformat(),
                "total_entities": entity_statistics["total_count"],
                "entity_types": len([t for t in entity_statistics["type_counts"].values() if t > 0]),
                "extraction_confidence": entity_statistics["average_confidence"],
                "text_coverage": entity_statistics["text_coverage"]
            },
            "person_entities": person_entities,
            "organization_entities": organization_entities,
            "location_entities": location_entities,
            "date_entities": date_entities,
            "number_entities": number_entities,
            "keyword_entities": keyword_entities,
            "technical_entities": technical_entities,
            "entity_statistics": entity_statistics,
            "entity_associations": entity_associations,
            "important_entities": important_entities,
            "entity_summary": {
                "status": "completed",
                "entities_extracted": entity_statistics["total_count"] > 0,
                "high_confidence_entities": len(important_entities.get("high_confidence", [])),
                "entity_diversity": len(entity_statistics["type_counts"]),
                "next_stage": "extract_relations"
            }
        }
    
    def _extract_persons(self, text: str) -> Dict[str, Any]:
        """提取人名实体"""
        persons = {
            "entities": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 中文姓名模式
        chinese_name_patterns = [
            r'([张王李赵刘陈杨黄周吴徐孙胡朱高林何郭马罗梁宋郑谢韩唐冯于董萧程曹袁邓许傅沈曾彭吕苏卢蒋蔡贾丁魏薛叶阎余潘杜戴夏钟汪田任姜范方石姚谭廖邹熊金陆郝孔白崔康毛邱秦江史顾侯邵孟龙万段雷钱汤尹黎易常武乔贺赖龚文][一-龯]{1,3})',
            r'([A-Z][a-z]+ [A-Z][a-z]+)',  # 英文姓名
            r'(Dr\.|Prof\.|Mr\.|Ms\.|Mrs\.)\s+([A-Z][a-z]+)',  # 带称谓的姓名
        ]
        
        # 职位和称谓模式
        title_patterns = [
            r'([张王李赵刘陈杨黄周吴徐孙胡朱高林何郭马罗梁宋郑谢韩唐冯于董萧程曹袁邓许傅沈曾彭吕苏卢蒋蔡贾丁魏薛叶阎余潘杜戴夏钟汪田任姜范方石姚谭廖邹熊金陆郝孔白崔康毛邱秦江史顾侯邵孟龙万段雷钱汤尹黎易常武乔贺赖龚文][一-龯]{1,3})(总裁|总经理|经理|主任|部长|科长|工程师|专家|教授|博士)',
            r'(总裁|总经理|经理|主任|部长|科长|工程师|专家|教授|博士)([张王李赵刘陈杨黄周吴徐孙胡朱高林何郭马罗梁宋郑谢韩唐冯于董萧程曹袁邓许傅沈曾彭吕苏卢蒋蔡贾丁魏薛叶阎余潘杜戴夏钟汪田任姜范方石姚谭廖邹熊金陆郝孔白崔康毛邱秦江史顾侯邵孟龙万段雷钱汤尹黎易常武乔贺赖龚文][一-龯]{1,3})'
        ]
        
        found_persons = []
        
        # 提取姓名
        for pattern in chinese_name_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                name = match.group(1) if match.groups() else match.group(0)
                if len(name) >= 2 and len(name) <= 4:  # 合理的姓名长度
                    found_persons.append({
                        "name": name,
                        "type": "person",
                        "confidence": 0.7,
                        "position": match.start(),
                        "context": text[max(0, match.start()-20):match.end()+20],
                        "source": "name_pattern"
                    })
        
        # 提取带职位的人名
        for pattern in title_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                if len(match.groups()) >= 2:
                    name = match.group(1) if match.group(1) else match.group(2)
                    title = match.group(2) if match.group(1) else match.group(1)
                    
                    found_persons.append({
                        "name": name,
                        "type": "person",
                        "title": title,
                        "confidence": 0.9,
                        "position": match.start(),
                        "context": text[max(0, match.start()-20):match.end()+20],
                        "source": "title_pattern"
                    })
        
        # 去重和排序
        unique_persons = self._deduplicate_entities(found_persons, "name")
        unique_persons.sort(key=lambda x: x["confidence"], reverse=True)
        
        persons["entities"] = unique_persons[:20]  # 限制数量
        persons["count"] = len(persons["entities"])
        persons["confidence"] = sum(p["confidence"] for p in persons["entities"]) / max(1, len(persons["entities"]))
        
        return persons
    
    def _extract_organizations(self, text: str) -> Dict[str, Any]:
        """提取机构实体"""
        organizations = {
            "entities": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 机构名称模式
        org_patterns = [
            r'([^\s]{2,20}(公司|企业|集团|有限公司|股份有限公司|Co\.|Corp\.|Inc\.|Ltd\.))',
            r'([^\s]{2,20}(大学|学院|研究院|研究所|实验室))',
            r'([^\s]{2,20}(部门?|司|局|厅|委员会|管理局))',
            r'([^\s]{2,20}(银行|保险|证券|基金))',
            r'([^\s]{2,20}(医院|诊所|卫生院))',
            r'([^\s]{2,20}(协会|学会|联合会|商会))'
        ]
        
        found_orgs = []
        
        for pattern in org_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                org_name = match.group(1)
                if len(org_name) >= 3 and len(org_name) <= 30:  # 合理的机构名长度
                    
                    # 判断机构类型
                    org_type = "company"
                    if any(suffix in org_name for suffix in ["大学", "学院", "研究院", "研究所"]):
                        org_type = "academic"
                    elif any(suffix in org_name for suffix in ["部", "司", "局", "厅", "委员会"]):
                        org_type = "government"
                    elif any(suffix in org_name for suffix in ["银行", "保险", "证券"]):
                        org_type = "financial"
                    elif any(suffix in org_name for suffix in ["医院", "诊所"]):
                        org_type = "medical"
                    
                    found_orgs.append({
                        "name": org_name,
                        "type": "organization",
                        "org_type": org_type,
                        "confidence": 0.8,
                        "position": match.start(),
                        "context": text[max(0, match.start()-20):match.end()+20],
                        "source": "org_pattern"
                    })
        
        # 去重和排序
        unique_orgs = self._deduplicate_entities(found_orgs, "name")
        unique_orgs.sort(key=lambda x: x["confidence"], reverse=True)
        
        organizations["entities"] = unique_orgs[:15]  # 限制数量
        organizations["count"] = len(organizations["entities"])
        organizations["confidence"] = sum(o["confidence"] for o in organizations["entities"]) / max(1, len(organizations["entities"]))
        
        return organizations
    
    def _extract_locations(self, text: str) -> Dict[str, Any]:
        """提取地名实体"""
        locations = {
            "entities": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 地名模式
        location_patterns = [
            r'([^\s]{2,10}(省|市|县|区|镇|村|街道|路|街|巷|号))',
            r'([^\s]{2,15}(国|州|府|郡))',
            r'(北京|上海|天津|重庆|广州|深圳|杭州|南京|武汉|成都|西安|沈阳|大连|青岛|厦门|宁波|苏州|无锡|佛山|东莞)',
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:\s+City|\s+State|\s+Province)?)'
        ]
        
        found_locations = []
        
        for pattern in location_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                location_name = match.group(1) if match.groups() else match.group(0)
                if len(location_name) >= 2 and len(location_name) <= 20:
                    
                    # 判断地名类型
                    loc_type = "city"
                    if location_name.endswith("省"):
                        loc_type = "province"
                    elif location_name.endswith(("县", "区")):
                        loc_type = "district"
                    elif location_name.endswith(("路", "街", "巷")):
                        loc_type = "street"
                    elif location_name.endswith("国"):
                        loc_type = "country"
                    
                    found_locations.append({
                        "name": location_name,
                        "type": "location",
                        "loc_type": loc_type,
                        "confidence": 0.7,
                        "position": match.start(),
                        "context": text[max(0, match.start()-20):match.end()+20],
                        "source": "location_pattern"
                    })
        
        # 去重和排序
        unique_locations = self._deduplicate_entities(found_locations, "name")
        unique_locations.sort(key=lambda x: x["confidence"], reverse=True)
        
        locations["entities"] = unique_locations[:15]  # 限制数量
        locations["count"] = len(locations["entities"])
        locations["confidence"] = sum(l["confidence"] for l in locations["entities"]) / max(1, len(locations["entities"]))
        
        return locations
    
    def _extract_dates(self, text: str) -> Dict[str, Any]:
        """提取日期实体"""
        dates = {
            "entities": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 日期模式
        date_patterns = [
            (r'(\d{4}年\d{1,2}月\d{1,2}日)', "chinese_full"),
            (r'(\d{4}年\d{1,2}月)', "chinese_month"),
            (r'(\d{4}-\d{1,2}-\d{1,2})', "iso_date"),
            (r'(\d{4}/\d{1,2}/\d{1,2})', "slash_date"),
            (r'(\d{4}\.\d{1,2}\.\d{1,2})', "dot_date"),
            (r'(\d{1,2}月\d{1,2}日)', "month_day"),
            (r'(\d{4}年)', "year_only")
        ]
        
        found_dates = []
        
        for pattern, date_type in date_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                date_str = match.group(1)
                
                found_dates.append({
                    "date": date_str,
                    "type": "date",
                    "date_type": date_type,
                    "confidence": 0.9,
                    "position": match.start(),
                    "context": text[max(0, match.start()-20):match.end()+20],
                    "source": "date_pattern"
                })
        
        # 去重和排序
        unique_dates = self._deduplicate_entities(found_dates, "date")
        unique_dates.sort(key=lambda x: x["position"])
        
        dates["entities"] = unique_dates[:10]  # 限制数量
        dates["count"] = len(dates["entities"])
        dates["confidence"] = sum(d["confidence"] for d in dates["entities"]) / max(1, len(dates["entities"]))
        
        return dates
    
    def _extract_numbers(self, text: str) -> Dict[str, Any]:
        """提取数字实体"""
        numbers = {
            "entities": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 数字模式
        number_patterns = [
            (r'(\d+\.?\d*[万亿千百十]?元)', "money"),
            (r'(\d+\.?\d*%)', "percentage"),
            (r'(\d+\.?\d*[万亿千百十]?人)', "population"),
            (r'(\d+\.?\d*[万亿千百十]?个)', "quantity"),
            (r'(\d+\.?\d*[公里米厘]米?)', "distance"),
            (r'(\d+\.?\d*[公斤千克吨])', "weight"),
            (r'(\d{1,3}(?:,\d{3})*(?:\.\d+)?)', "large_number")
        ]
        
        found_numbers = []
        
        for pattern, number_type in number_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                number_str = match.group(1)
                
                found_numbers.append({
                    "number": number_str,
                    "type": "number",
                    "number_type": number_type,
                    "confidence": 0.8,
                    "position": match.start(),
                    "context": text[max(0, match.start()-20):match.end()+20],
                    "source": "number_pattern"
                })
        
        # 去重和排序
        unique_numbers = self._deduplicate_entities(found_numbers, "number")
        unique_numbers.sort(key=lambda x: x["position"])
        
        numbers["entities"] = unique_numbers[:20]  # 限制数量
        numbers["count"] = len(numbers["entities"])
        numbers["confidence"] = sum(n["confidence"] for n in numbers["entities"]) / max(1, len(numbers["entities"]))
        
        return numbers
    
    def _extract_keywords(self, text: str) -> Dict[str, Any]:
        """提取关键词实体"""
        keywords = {
            "entities": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 提取中文词汇
        chinese_words = re.findall(r'[\u4e00-\u9fff]{2,8}', text)
        
        # 计算词频
        word_freq = {}
        for word in chinese_words:
            if len(word) >= 2:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 过滤常见词汇
        common_words = {
            '这个', '一个', '可以', '需要', '进行', '通过', '实现', '系统', '管理', '建设',
            '应用', '技术', '方案', '项目', '工作', '服务', '功能', '内容', '信息', '数据',
            '用户', '提供', '支持', '开发', '设计', '使用', '相关', '主要', '重要', '基本'
        }
        
        # 选择高频且有意义的词汇
        significant_words = []
        for word, freq in sorted(word_freq.items(), key=lambda x: x[1], reverse=True):
            if word not in common_words and freq >= 2:
                confidence = min(0.9, freq / 10)  # 频率越高置信度越高
                significant_words.append({
                    "keyword": word,
                    "type": "keyword",
                    "frequency": freq,
                    "confidence": confidence,
                    "source": "frequency_analysis"
                })
        
        keywords["entities"] = significant_words[:15]  # 限制数量
        keywords["count"] = len(keywords["entities"])
        keywords["confidence"] = sum(k["confidence"] for k in keywords["entities"]) / max(1, len(keywords["entities"]))
        
        return keywords
    
    def _extract_technical_terms(self, text: str) -> Dict[str, Any]:
        """提取技术术语实体"""
        technical_terms = {
            "entities": [],
            "count": 0,
            "confidence": 0.0
        }
        
        # 技术术语模式
        tech_patterns = [
            (r'\b([A-Z]{2,10})\b', "acronym"),  # 缩写词
            (r'\b([a-zA-Z]+\.[a-zA-Z]+(?:\.[a-zA-Z]+)*)\b', "domain_tech"),  # 域名技术
            (r'\b([a-zA-Z]+_[a-zA-Z_]+)\b', "underscore_term"),  # 下划线术语
            (r'\b([a-zA-Z]+(?:[A-Z][a-z]*)+)\b', "camel_case"),  # 驼峰命名
            (r'(API|SDK|HTTP|HTTPS|JSON|XML|SQL|HTML|CSS|JavaScript|Python|Java|MySQL|Redis)', "known_tech")
        ]
        
        found_terms = []
        
        for pattern, term_type in tech_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                term = match.group(1)
                if len(term) >= 2:
                    
                    # 根据类型调整置信度
                    confidence = 0.6
                    if term_type == "known_tech":
                        confidence = 0.9
                    elif term_type == "acronym" and len(term) <= 5:
                        confidence = 0.8
                    
                    found_terms.append({
                        "term": term,
                        "type": "technical",
                        "term_type": term_type,
                        "confidence": confidence,
                        "position": match.start(),
                        "context": text[max(0, match.start()-20):match.end()+20],
                        "source": "tech_pattern"
                    })
        
        # 去重和排序
        unique_terms = self._deduplicate_entities(found_terms, "term")
        unique_terms.sort(key=lambda x: x["confidence"], reverse=True)
        
        technical_terms["entities"] = unique_terms[:15]  # 限制数量
        technical_terms["count"] = len(technical_terms["entities"])
        technical_terms["confidence"] = sum(t["confidence"] for t in technical_terms["entities"]) / max(1, len(technical_terms["entities"]))
        
        return technical_terms
    
    def _deduplicate_entities(self, entities: List[Dict], key_field: str) -> List[Dict]:
        """去重实体"""
        seen = set()
        unique_entities = []
        
        for entity in entities:
            key = entity.get(key_field, "").lower()
            if key and key not in seen:
                seen.add(key)
                unique_entities.append(entity)
        
        return unique_entities
    
    def _calculate_entity_statistics(self, *entity_groups) -> Dict[str, Any]:
        """计算实体统计信息"""
        stats = {
            "total_count": 0,
            "type_counts": {},
            "average_confidence": 0.0,
            "text_coverage": 0.0
        }
        
        all_entities = []
        total_confidence = 0
        
        type_names = ["persons", "organizations", "locations", "dates", "numbers", "keywords", "technical_terms"]
        
        for i, group in enumerate(entity_groups):
            type_name = type_names[i] if i < len(type_names) else f"type_{i}"
            count = group.get("count", 0)
            confidence = group.get("confidence", 0.0)
            
            stats["type_counts"][type_name] = count
            stats["total_count"] += count
            total_confidence += confidence * count
            
            all_entities.extend(group.get("entities", []))
        
        if stats["total_count"] > 0:
            stats["average_confidence"] = total_confidence / stats["total_count"]
        
        # 计算文本覆盖率（简化计算）
        if all_entities:
            unique_positions = set()
            for entity in all_entities:
                pos = entity.get("position", 0)
                unique_positions.update(range(pos, pos + 10))  # 假设每个实体覆盖10个字符
            stats["text_coverage"] = min(1.0, len(unique_positions) / 1000)  # 假设文本长度1000字符
        
        return stats
    
    def _analyze_entity_associations(self, persons: Dict, organizations: Dict, locations: Dict) -> Dict[str, Any]:
        """分析实体关联"""
        associations = {
            "person_org_associations": [],
            "person_location_associations": [],
            "org_location_associations": [],
            "association_count": 0
        }
        
        # 简化的关联分析：基于上下文距离
        person_entities = persons.get("entities", [])
        org_entities = organizations.get("entities", [])
        location_entities = locations.get("entities", [])
        
        # 人员-机构关联
        for person in person_entities:
            person_pos = person.get("position", 0)
            for org in org_entities:
                org_pos = org.get("position", 0)
                if abs(person_pos - org_pos) < 100:  # 距离小于100字符认为有关联
                    associations["person_org_associations"].append({
                        "person": person.get("name", ""),
                        "organization": org.get("name", ""),
                        "confidence": 0.6,
                        "distance": abs(person_pos - org_pos)
                    })
        
        # 人员-地点关联
        for person in person_entities:
            person_pos = person.get("position", 0)
            for location in location_entities:
                location_pos = location.get("position", 0)
                if abs(person_pos - location_pos) < 80:
                    associations["person_location_associations"].append({
                        "person": person.get("name", ""),
                        "location": location.get("name", ""),
                        "confidence": 0.5,
                        "distance": abs(person_pos - location_pos)
                    })
        
        # 机构-地点关联
        for org in org_entities:
            org_pos = org.get("position", 0)
            for location in location_entities:
                location_pos = location.get("position", 0)
                if abs(org_pos - location_pos) < 60:
                    associations["org_location_associations"].append({
                        "organization": org.get("name", ""),
                        "location": location.get("name", ""),
                        "confidence": 0.7,
                        "distance": abs(org_pos - location_pos)
                    })
        
        associations["association_count"] = (
            len(associations["person_org_associations"]) +
            len(associations["person_location_associations"]) +
            len(associations["org_location_associations"])
        )
        
        return associations
    
    def _identify_important_entities(self, *entity_groups) -> Dict[str, Any]:
        """识别重要实体"""
        important = {
            "high_confidence": [],
            "high_frequency": [],
            "contextually_important": [],
            "summary": {}
        }
        
        all_entities = []
        for group in entity_groups:
            all_entities.extend(group.get("entities", []))
        
        # 高置信度实体
        for entity in all_entities:
            if entity.get("confidence", 0) >= 0.8:
                important["high_confidence"].append({
                    "name": entity.get("name") or entity.get("keyword") or entity.get("term") or entity.get("date") or entity.get("number"),
                    "type": entity.get("type"),
                    "confidence": entity.get("confidence")
                })
        
        # 高频实体（主要是关键词）
        for group in entity_groups:
            for entity in group.get("entities", []):
                if entity.get("frequency", 0) >= 3:
                    important["high_frequency"].append({
                        "name": entity.get("keyword", ""),
                        "type": entity.get("type"),
                        "frequency": entity.get("frequency")
                    })
        
        # 上下文重要实体（带职位的人名等）
        for group in entity_groups:
            for entity in group.get("entities", []):
                if entity.get("title") or entity.get("org_type") == "government":
                    important["contextually_important"].append({
                        "name": entity.get("name", ""),
                        "type": entity.get("type"),
                        "context": entity.get("title") or entity.get("org_type"),
                        "confidence": entity.get("confidence")
                    })
        
        important["summary"] = {
            "high_confidence_count": len(important["high_confidence"]),
            "high_frequency_count": len(important["high_frequency"]),
            "contextually_important_count": len(important["contextually_important"]),
            "total_important": len(important["high_confidence"]) + len(important["high_frequency"]) + len(important["contextually_important"])
        }
        
        return important
    
    def get_stage_name(self) -> str:
        return "extract_entities"
    
    def get_progress_percentage(self) -> int:
        return 80
    
    def get_stage_description(self) -> str:
        return "提取人名、机构、地名、日期、数字等实体信息"
