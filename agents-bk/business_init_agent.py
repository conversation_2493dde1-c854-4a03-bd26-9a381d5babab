#!/usr/bin/env python3
"""
业务初始化Agent - 初始化处理环境，验证配置和权限
"""

import asyncio
import os
import tempfile
from typing import Dict, Any
from datetime import datetime
from .business_base_agent import BusinessBaseAgent, BusinessAgentContext

class BusinessInitAgent(BusinessBaseAgent):
    """业务初始化Agent - 验证环境和配置"""
    
    def __init__(self):
        super().__init__(name="业务初始化Agent")
        # 无依赖
    
    async def process(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """初始化处理环境"""
        await asyncio.sleep(0.2)  # 模拟初始化时间
        
        # 验证上下文
        context_validation = self._validate_context(context)
        
        # 验证配置
        config_validation = self._validate_config(context)
        
        # 创建工作目录
        work_directory = self._create_work_directory(context.task_id)
        
        # 验证依赖服务
        service_validation = await self._validate_services(context)
        
        # 初始化统计信息
        processing_stats = self._initialize_stats(context)
        
        # 检查整体初始化状态
        initialization_success = (
            context_validation["valid"] and
            config_validation["valid"] and
            work_directory["created"] and
            service_validation["all_available"]
        )
        
        return {
            "init_info": {
                "initialization_status": "success" if initialization_success else "failed",
                "initialization_time": datetime.now().isoformat(),
                "task_id": context.task_id,
                "project_name": context.project_name,
                "action_type": context.action,
                "file_count": len(context.file_urls),
                "work_directory": work_directory["path"]
            },
            "context_validation": context_validation,
            "config_validation": config_validation,
            "work_directory": work_directory,
            "service_validation": service_validation,
            "processing_stats": processing_stats,
            "init_summary": {
                "status": "completed" if initialization_success else "failed",
                "ready_for_processing": initialization_success,
                "next_stage": "minio_download" if initialization_success else "failed"
            }
        }
    
    def _validate_context(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """验证上下文"""
        validation = {
            "valid": True,
            "issues": [],
            "details": {}
        }
        
        # 检查必需字段
        required_fields = {
            "task_id": context.task_id,
            "project_name": context.project_name,
            "action": context.action,
            "file_urls": context.file_urls,
            "config": context.config
        }
        
        for field_name, field_value in required_fields.items():
            if not field_value:
                validation["valid"] = False
                validation["issues"].append(f"Missing required field: {field_name}")
            else:
                validation["details"][field_name] = "valid"
        
        # 验证action类型
        if context.action not in ["项目档案", "文书档案"]:
            validation["valid"] = False
            validation["issues"].append(f"Invalid action type: {context.action}")
        else:
            validation["details"]["action_type"] = "valid"
        
        # 验证文件URL列表
        if not isinstance(context.file_urls, list) or len(context.file_urls) == 0:
            validation["valid"] = False
            validation["issues"].append("file_urls must be a non-empty list")
        else:
            validation["details"]["file_urls_count"] = len(context.file_urls)
        
        return validation
    
    def _validate_config(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """验证配置"""
        validation = {
            "valid": True,
            "issues": [],
            "config_sections": {}
        }
        
        # 检查基础配置
        required_config_sections = [
            "minio", "elasticsearch", "chunk_size", "chunk_overlap"
        ]
        
        for section in required_config_sections:
            if section not in context.config:
                validation["valid"] = False
                validation["issues"].append(f"Missing config section: {section}")
            else:
                validation["config_sections"][section] = "present"
        
        # 检查提取配置
        if context.action == "项目档案":
            if "project_extract" not in context.config:
                validation["valid"] = False
                validation["issues"].append("Missing project_extract config for 项目档案")
            else:
                project_config = context.config["project_extract"]
                validation["config_sections"]["project_extract"] = {
                    "fields_count": len(project_config),
                    "fields": list(project_config.keys())
                }
        
        elif context.action == "文书档案":
            if "conference_extract" not in context.config:
                validation["valid"] = False
                validation["issues"].append("Missing conference_extract config for 文书档案")
            else:
                conference_config = context.config["conference_extract"]
                validation["config_sections"]["conference_extract"] = {
                    "fields_count": len(conference_config),
                    "fields": list(conference_config.keys())
                }
        
        # 验证MinIO配置
        if "minio" in context.config:
            minio_config = context.config["minio"]
            required_minio_fields = ["endpoint", "access_key", "secret_key", "bucket_name"]
            
            for field in required_minio_fields:
                if field not in minio_config:
                    validation["valid"] = False
                    validation["issues"].append(f"Missing MinIO config field: {field}")
        
        # 验证ES配置
        if "elasticsearch" in context.config:
            es_config = context.config["elasticsearch"]
            # 根据实际config.yaml调整字段名
            required_es_fields = ["host", "index_name"]  # 实际配置使用host而不是url和port

            for field in required_es_fields:
                if field not in es_config:
                    validation["valid"] = False
                    validation["issues"].append(f"Missing Elasticsearch config field: {field}")
        
        return validation
    
    def _create_work_directory(self, task_id: str) -> Dict[str, Any]:
        """创建工作目录"""
        try:
            # 创建临时工作目录
            base_temp_dir = tempfile.gettempdir()
            work_dir = os.path.join(base_temp_dir, f"hngpt_task_{task_id}")
            
            # 创建目录结构
            os.makedirs(work_dir, exist_ok=True)
            os.makedirs(os.path.join(work_dir, "downloads"), exist_ok=True)
            os.makedirs(os.path.join(work_dir, "parsed"), exist_ok=True)
            os.makedirs(os.path.join(work_dir, "chunks"), exist_ok=True)
            os.makedirs(os.path.join(work_dir, "vectors"), exist_ok=True)
            os.makedirs(os.path.join(work_dir, "logs"), exist_ok=True)
            
            return {
                "created": True,
                "path": work_dir,
                "subdirectories": [
                    "downloads", "parsed", "chunks", "vectors", "logs"
                ],
                "permissions": "read_write"
            }
            
        except Exception as e:
            return {
                "created": False,
                "error": str(e),
                "path": None
            }
    
    async def _validate_services(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """验证依赖服务"""
        validation = {
            "all_available": True,
            "services": {}
        }
        
        # 验证MinIO连接（模拟）
        try:
            # 这里应该实际测试MinIO连接
            # 现在模拟验证
            await asyncio.sleep(0.1)
            validation["services"]["minio"] = {
                "available": True,
                "endpoint": context.config.get("minio", {}).get("endpoint", "unknown"),
                "bucket": context.config.get("minio", {}).get("bucket_name", "unknown")
            }
        except Exception as e:
            validation["all_available"] = False
            validation["services"]["minio"] = {
                "available": False,
                "error": str(e)
            }
        
        # 验证Elasticsearch连接（模拟）
        try:
            # 这里应该实际测试ES连接
            # 现在模拟验证
            await asyncio.sleep(0.1)
            es_config = context.config.get("elasticsearch", {})
            validation["services"]["elasticsearch"] = {
                "available": True,
                "host": es_config.get("host", "unknown"),
                "index": es_config.get("index_name", "unknown")
            }
        except Exception as e:
            validation["all_available"] = False
            validation["services"]["elasticsearch"] = {
                "available": False,
                "error": str(e)
            }
        
        # 验证LLM服务（模拟）
        try:
            # 这里应该实际测试LLM连接
            await asyncio.sleep(0.1)
            validation["services"]["llm"] = {
                "available": True,
                "model": "hngpt-mini",
                "endpoint": "http://*************:8888/v1"
            }
        except Exception as e:
            validation["all_available"] = False
            validation["services"]["llm"] = {
                "available": False,
                "error": str(e)
            }
        
        return validation
    
    def _initialize_stats(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """初始化处理统计信息"""
        return {
            "task_start_time": datetime.now().isoformat(),
            "total_files": len(context.file_urls),
            "processed_files": 0,
            "failed_files": 0,
            "total_chunks": 0,
            "total_vectors": 0,
            "extracted_records": 0,
            "processing_stages": {
                "init": "completed",
                "minio_download": "pending",
                "document_parse": "pending",
                "text_chunking": "pending",
                "vector_embedding": "pending",
                "elasticsearch_storage": "pending",
                "llm_extraction": "pending",
                "database_storage": "pending"
            },
            "performance_metrics": {
                "files_per_minute": 0,
                "chunks_per_minute": 0,
                "vectors_per_minute": 0
            }
        }
    
    def get_stage_name(self) -> str:
        return "init"
    
    def get_progress_percentage(self) -> int:
        return 10
    
    def get_stage_description(self) -> str:
        return "初始化处理环境，验证配置和权限"
