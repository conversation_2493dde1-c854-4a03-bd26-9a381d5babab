#!/usr/bin/env python3
"""
业务Elasticsearch存储Agent - 将向量化文本块存储到ES
"""

import asyncio
import os
import sys
import hashlib
import numpy as np
from typing import Dict, Any, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, '/workspace/hngpt')

from .business_base_agent import BusinessBaseAgent, BusinessAgentContext

# 导入实际的ES客户端
try:
    from utils.doc_search import ES
    from elasticsearch import Elasticsearch
    from elasticsearch.helpers import bulk
except ImportError:
    print("警告: 无法导入Elasticsearch相关模块，使用备用实现")
    
    class ES:
        """备用的ES实现"""
        def __init__(self):
            self.index_name = "docs"
            self.client = None
        
        def upsert_documents(self, docs, source, doc_id, project_name):
            """模拟ES存储"""
            print(f"模拟ES存储: {len(docs)}个文档到索引 {self.index_name}")
            return True

class BusinessElasticsearchStorageAgent(BusinessBaseAgent):
    """业务Elasticsearch存储Agent"""
    
    def __init__(self):
        super().__init__(name="业务Elasticsearch存储Agent")
        self.add_dependency("vectorization_info")  # 依赖向量化Agent
        self.es_client = None
    
    async def process(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """将向量化文本块存储到ES"""
        await asyncio.sleep(0.8)  # 模拟ES存储时间
        
        # 初始化ES客户端
        await self._initialize_es_client(context.config)
        
        # 获取向量化信息
        vectorization_info = context.get_extracted_data("vectorization_info", {})
        vectorization_results = context.get_extracted_data("vectorization_results", [])
        
        # 存储到ES
        storage_results = await self._store_to_elasticsearch(vectorization_results, context)
        
        # 验证存储结果
        storage_validation = self._validate_storage(storage_results)
        
        # 计算存储统计
        storage_statistics = self._calculate_storage_statistics(storage_results)
        
        # 生成存储报告
        storage_report = self._generate_storage_report(storage_results, context)
        
        # 测试检索功能
        retrieval_test = await self._test_retrieval(context)
        
        return {
            "storage_info": {
                "storage_time": datetime.now().isoformat(),
                "total_documents": storage_statistics["total_documents"],
                "successful_storage": storage_statistics["successful_storage"],
                "failed_storage": storage_statistics["failed_storage"],
                "es_index": self.es_client.index_name if self.es_client else "unknown",
                "storage_method": "upsert_documents"
            },
            "storage_results": storage_results,
            "storage_validation": storage_validation,
            "storage_statistics": storage_statistics,
            "storage_report": storage_report,
            "retrieval_test": retrieval_test,
            "storage_summary": {
                "status": "completed",
                "storage_successful": storage_statistics["successful_storage"] > 0,
                "documents_ready_for_search": storage_statistics["searchable_documents"],
                "next_stage": "llm_extraction"
            }
        }
    
    async def _initialize_es_client(self, config: Dict[str, Any]):
        """初始化ES客户端"""
        if self.es_client is None:
            try:
                # 尝试使用实际的ES客户端
                self.es_client = ES()
                print("✅ Elasticsearch客户端初始化成功")
            except Exception as e:
                print(f"⚠️ Elasticsearch客户端初始化失败，使用备用模式: {e}")
                self.es_client = ES()  # 使用备用实现
    
    async def _store_to_elasticsearch(self, vectorization_results: List[Dict[str, Any]], context: BusinessAgentContext) -> List[Dict[str, Any]]:
        """存储向量化结果到ES"""
        storage_results = []
        
        for vector_result in vectorization_results:
            try:
                file_name = vector_result.get("file_name", "unknown")
                vectorized_chunks = vector_result.get("vectorized_chunks", [])
                
                if not vector_result.get("vectorization_success") or not vectorized_chunks:
                    storage_results.append({
                        "file_name": file_name,
                        "storage_success": False,
                        "error": "No vectorized chunks to store",
                        "stored_count": 0,
                        "metadata": vector_result.get("metadata", {})
                    })
                    continue
                
                # 准备文档数据
                docs_to_store = []
                for chunk in vectorized_chunks:
                    content = chunk.get("content", "")
                    if content.strip():  # 只存储非空内容
                        docs_to_store.append(content)
                
                if not docs_to_store:
                    storage_results.append({
                        "file_name": file_name,
                        "storage_success": False,
                        "error": "No valid content to store",
                        "stored_count": 0,
                        "metadata": vector_result.get("metadata", {})
                    })
                    continue
                
                # 生成文档ID
                doc_id = self._generate_document_id(file_name, context.task_id)
                
                # 使用ES的upsert_documents方法存储
                try:
                    success = self.es_client.upsert_documents(
                        docs=docs_to_store,
                        source=file_name,
                        id=doc_id,
                        project_name=context.project_name
                    )
                    
                    if success:
                        storage_results.append({
                            "file_name": file_name,
                            "storage_success": True,
                            "error": None,
                            "stored_count": len(docs_to_store),
                            "document_id": doc_id,
                            "project_name": context.project_name,
                            "metadata": vector_result.get("metadata", {}),
                            "storage_details": {
                                "index_name": self.es_client.index_name,
                                "chunk_count": len(vectorized_chunks),
                                "stored_chunks": len(docs_to_store)
                            }
                        })
                    else:
                        storage_results.append({
                            "file_name": file_name,
                            "storage_success": False,
                            "error": "ES upsert_documents failed",
                            "stored_count": 0,
                            "metadata": vector_result.get("metadata", {})
                        })
                        
                except Exception as es_error:
                    storage_results.append({
                        "file_name": file_name,
                        "storage_success": False,
                        "error": f"ES storage error: {str(es_error)}",
                        "stored_count": 0,
                        "metadata": vector_result.get("metadata", {})
                    })
                
            except Exception as e:
                storage_results.append({
                    "file_name": vector_result.get("file_name", "unknown"),
                    "storage_success": False,
                    "error": str(e),
                    "stored_count": 0,
                    "metadata": vector_result.get("metadata", {})
                })
        
        return storage_results
    
    def _generate_document_id(self, file_name: str, task_id: str) -> str:
        """生成文档ID"""
        # 基于文件名和任务ID生成唯一ID
        content = f"{file_name}_{task_id}_{datetime.now().isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _validate_storage(self, storage_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证存储结果"""
        validation = {
            "total_files": len(storage_results),
            "successful_files": 0,
            "failed_files": 0,
            "total_documents": 0,
            "stored_documents": 0,
            "validation_details": []
        }
        
        for result in storage_results:
            validation["total_documents"] += result.get("stored_count", 0)
            
            if result["storage_success"]:
                validation["successful_files"] += 1
                validation["stored_documents"] += result.get("stored_count", 0)
                validation["validation_details"].append({
                    "file_name": result["file_name"],
                    "status": "success",
                    "stored_count": result.get("stored_count", 0),
                    "document_id": result.get("document_id", "unknown"),
                    "project_name": result.get("project_name", "unknown")
                })
            else:
                validation["failed_files"] += 1
                validation["validation_details"].append({
                    "file_name": result["file_name"],
                    "status": "failed",
                    "error": result.get("error", "unknown")
                })
        
        return validation
    
    def _calculate_storage_statistics(self, storage_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算存储统计"""
        stats = {
            "total_files": len(storage_results),
            "successful_files": 0,
            "failed_files": 0,
            "total_documents": 0,
            "successful_storage": 0,
            "failed_storage": 0,
            "searchable_documents": 0,
            "storage_efficiency": 0.0,
            "project_distribution": {}
        }
        
        for result in storage_results:
            if result["storage_success"]:
                stats["successful_files"] += 1
                stored_count = result.get("stored_count", 0)
                stats["successful_storage"] += stored_count
                stats["searchable_documents"] += stored_count
                
                # 统计项目分布
                project_name = result.get("project_name", "unknown")
                stats["project_distribution"][project_name] = stats["project_distribution"].get(project_name, 0) + stored_count
            else:
                stats["failed_files"] += 1
                stats["failed_storage"] += result.get("stored_count", 0)
        
        stats["total_documents"] = stats["successful_storage"] + stats["failed_storage"]
        
        # 计算存储效率
        if stats["total_documents"] > 0:
            stats["storage_efficiency"] = stats["successful_storage"] / stats["total_documents"]
        
        return stats
    
    def _generate_storage_report(self, storage_results: List[Dict[str, Any]], context: BusinessAgentContext) -> Dict[str, Any]:
        """生成存储报告"""
        report = {
            "report_time": datetime.now().isoformat(),
            "task_info": {
                "task_id": context.task_id,
                "project_name": context.project_name,
                "action": context.action
            },
            "storage_summary": {
                "total_files_processed": len(storage_results),
                "successful_storage": sum(1 for r in storage_results if r["storage_success"]),
                "failed_storage": sum(1 for r in storage_results if not r["storage_success"]),
                "total_documents_stored": sum(r.get("stored_count", 0) for r in storage_results if r["storage_success"])
            },
            "file_details": [],
            "es_info": {
                "index_name": self.es_client.index_name if self.es_client else "unknown",
                "storage_method": "upsert_documents"
            }
        }
        
        for result in storage_results:
            report["file_details"].append({
                "file_name": result["file_name"],
                "storage_success": result["storage_success"],
                "stored_count": result.get("stored_count", 0),
                "document_id": result.get("document_id", "unknown"),
                "error": result.get("error")
            })
        
        return report
    
    async def _test_retrieval(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """测试检索功能"""
        test_result = {
            "test_time": datetime.now().isoformat(),
            "test_query": context.project_name,
            "retrieval_success": False,
            "results_count": 0,
            "test_details": {}
        }
        
        try:
            # 模拟检索测试
            if hasattr(self.es_client, 'doc_search'):
                # 使用项目名称作为测试查询
                search_results = self.es_client.doc_search(
                    method="project",
                    query=context.project_name,
                    project_name=context.project_name,
                    top_k=5,
                    knn_boost=1.0
                )
                
                test_result["retrieval_success"] = True
                test_result["results_count"] = len(search_results) if search_results else 0
                test_result["test_details"] = {
                    "search_method": "project",
                    "query_used": context.project_name,
                    "results_preview": search_results[:2] if search_results else []
                }
            else:
                test_result["test_details"] = {
                    "message": "ES client does not support doc_search method"
                }
                
        except Exception as e:
            test_result["test_details"] = {
                "error": str(e),
                "message": "Retrieval test failed"
            }
        
        return test_result
    
    def get_stage_name(self) -> str:
        return "elasticsearch_storage"
    
    def get_progress_percentage(self) -> int:
        return 80
    
    def get_stage_description(self) -> str:
        return "将向量化文本块存储到Elasticsearch"
