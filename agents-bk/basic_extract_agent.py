#!/usr/bin/env python3
"""
基础信息提取Agent - 负责提取文档的基础信息
"""

import asyncio
import re
from typing import Dict, Any, List, Optional
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class BasicExtractAgent(BaseAgent):
    """基础信息提取Agent - 提取标题、作者、类型等基础信息"""
    
    def __init__(self):
        super().__init__(name="基础信息提取Agent")
        self.add_dependency("parse")  # 依赖解析Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """提取文档基础信息"""
        await asyncio.sleep(0.5)  # 模拟提取时间
        
        # 获取解析信息
        parse_info = context.get_extracted_data("parse_info", {})
        parsed_pages = context.get_extracted_data("parsed_pages", [])
        text_statistics = context.get_extracted_data("text_statistics", {})
        content_classification = context.get_extracted_data("content_classification", {})
        
        full_text = parse_info.get("full_text", "")
        
        # 提取基础信息
        title_info = self._extract_title(full_text, parsed_pages, context.file_content)
        author_info = self._extract_author(full_text, context.file_content)
        date_info = self._extract_dates(full_text, context.file_content)
        document_type = self._determine_document_type(full_text, content_classification)
        
        # 提取关键元数据
        metadata_info = self._extract_metadata(context.file_content, full_text)
        
        # 计算文档指标
        document_metrics = self._calculate_document_metrics(text_statistics, parsed_pages)
        
        # 提取摘要信息
        summary_info = self._extract_summary(full_text, parsed_pages)
        
        return {
            "basic_info": {
                "title": title_info["title"],
                "author": author_info["author"],
                "creation_date": date_info["creation_date"],
                "document_type": document_type["type"],
                "language": text_statistics.get("language_detected", "zh-CN"),
                "word_count": text_statistics.get("total_words", 0),
                "page_count": len(parsed_pages),
                "extraction_time": datetime.now().isoformat()
            },
            "title_info": title_info,
            "author_info": author_info,
            "date_info": date_info,
            "document_type": document_type,
            "metadata_info": metadata_info,
            "document_metrics": document_metrics,
            "summary_info": summary_info,
            "extraction_summary": {
                "status": "completed",
                "confidence_score": self._calculate_confidence_score(
                    title_info, author_info, date_info, document_type
                ),
                "extracted_fields": self._count_extracted_fields(
                    title_info, author_info, date_info, document_type
                ),
                "next_stage": "extract_structure"
            }
        }
    
    def _extract_title(self, full_text: str, parsed_pages: List[Dict], file_content: Dict) -> Dict[str, Any]:
        """提取文档标题"""
        title_info = {
            "title": "未识别标题",
            "confidence": 0.0,
            "source": "unknown",
            "alternatives": []
        }
        
        # 1. 从元数据中获取标题
        metadata = file_content.get("metadata", {})
        if metadata.get("title"):
            title_info.update({
                "title": metadata["title"],
                "confidence": 0.9,
                "source": "metadata"
            })
            return title_info
        
        # 2. 从第一页提取标题
        if parsed_pages:
            first_page = parsed_pages[0]
            content = first_page.get("cleaned_content", "")
            lines = content.split('\n')
            
            for line in lines[:3]:  # 检查前3行
                line = line.strip()
                if line and len(line) < 100:  # 标题通常较短
                    # 检查是否像标题
                    if (not re.match(r'^\d+\.', line) and  # 不是编号
                        not re.match(r'^第[一二三四五六七八九十\d]+章', line) and  # 不是章节
                        len(line) > 5):  # 有一定长度
                        
                        title_info.update({
                            "title": line,
                            "confidence": 0.7,
                            "source": "first_page"
                        })
                        break
        
        # 3. 从全文中查找可能的标题
        if title_info["confidence"] < 0.5:
            title_candidates = self._find_title_candidates(full_text)
            if title_candidates:
                best_candidate = title_candidates[0]
                title_info.update({
                    "title": best_candidate["text"],
                    "confidence": best_candidate["confidence"],
                    "source": "content_analysis",
                    "alternatives": [c["text"] for c in title_candidates[1:3]]
                })
        
        return title_info
    
    def _find_title_candidates(self, text: str) -> List[Dict[str, Any]]:
        """查找标题候选项"""
        candidates = []
        lines = text.split('\n')
        
        for i, line in enumerate(lines[:10]):  # 只检查前10行
            line = line.strip()
            if not line:
                continue
            
            score = 0
            
            # 位置权重（越靠前分数越高）
            position_score = (10 - i) / 10 * 0.3
            score += position_score
            
            # 长度权重（20-80字符最佳）
            length = len(line)
            if 20 <= length <= 80:
                length_score = 0.3
            elif 10 <= length <= 100:
                length_score = 0.2
            else:
                length_score = 0.1
            score += length_score
            
            # 内容特征权重
            if any(keyword in line for keyword in ['方案', '报告', '系统', '管理', '设计']):
                score += 0.2
            
            if not re.match(r'^\d+\.', line):  # 不是编号开头
                score += 0.1
            
            if line.endswith('。'):  # 不以句号结尾
                score -= 0.1
            
            candidates.append({
                "text": line,
                "confidence": min(1.0, score),
                "position": i
            })
        
        # 按置信度排序
        candidates.sort(key=lambda x: x["confidence"], reverse=True)
        return candidates
    
    def _extract_author(self, full_text: str, file_content: Dict) -> Dict[str, Any]:
        """提取作者信息"""
        author_info = {
            "author": "未知作者",
            "confidence": 0.0,
            "source": "unknown",
            "department": "",
            "contact": ""
        }
        
        # 1. 从元数据中获取作者
        metadata = file_content.get("metadata", {})
        if metadata.get("author"):
            author_info.update({
                "author": metadata["author"],
                "confidence": 0.9,
                "source": "metadata"
            })
            return author_info
        
        # 2. 从文本中查找作者模式
        author_patterns = [
            r'作者[：:]\s*([^\n\s]+)',
            r'编写[：:]\s*([^\n\s]+)',
            r'起草[：:]\s*([^\n\s]+)',
            r'负责人[：:]\s*([^\n\s]+)',
            r'([张王李赵刘陈杨黄周吴徐孙胡朱高林何郭马罗梁宋郑谢韩唐冯于董萧程曹袁邓许傅沈曾彭吕苏卢蒋蔡贾丁魏薛叶阎余潘杜戴夏钟汪田任姜范方石姚谭廖邹熊金陆郝孔白崔康毛邱秦江史顾侯邵孟龙万段雷钱汤尹黎易常武乔贺赖龚文][一-龯]{1,3})\s*编写',
        ]
        
        for pattern in author_patterns:
            matches = re.findall(pattern, full_text)
            if matches:
                author_info.update({
                    "author": matches[0],
                    "confidence": 0.6,
                    "source": "content_pattern"
                })
                break
        
        # 3. 查找部门信息
        dept_patterns = [
            r'([^\n]*部门?[^\n]*)',
            r'([^\n]*公司[^\n]*)',
            r'([^\n]*团队[^\n]*)'
        ]
        
        for pattern in dept_patterns:
            matches = re.findall(pattern, full_text)
            if matches:
                author_info["department"] = matches[0][:50]  # 限制长度
                break
        
        return author_info
    
    def _extract_dates(self, full_text: str, file_content: Dict) -> Dict[str, Any]:
        """提取日期信息"""
        date_info = {
            "creation_date": "",
            "last_modified": "",
            "effective_date": "",
            "confidence": 0.0,
            "source": "unknown",
            "all_dates": []
        }
        
        # 1. 从元数据中获取日期
        metadata = file_content.get("metadata", {})
        if metadata.get("creation_date"):
            date_info.update({
                "creation_date": metadata["creation_date"],
                "confidence": 0.9,
                "source": "metadata"
            })
        
        # 2. 从文本中提取日期
        date_patterns = [
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})',
            r'(\d{4}\.\d{1,2}\.\d{1,2})'
        ]
        
        all_dates = []
        for pattern in date_patterns:
            matches = re.findall(pattern, full_text)
            all_dates.extend(matches)
        
        # 去重并排序
        unique_dates = list(set(all_dates))
        unique_dates.sort()
        date_info["all_dates"] = unique_dates[:5]  # 限制数量
        
        # 如果没有从元数据获取到创建日期，使用第一个找到的日期
        if not date_info["creation_date"] and unique_dates:
            date_info.update({
                "creation_date": unique_dates[0],
                "confidence": 0.5,
                "source": "content_extraction"
            })
        
        return date_info
    
    def _determine_document_type(self, full_text: str, content_classification: Dict) -> Dict[str, Any]:
        """确定文档类型"""
        type_info = {
            "type": "未知文档",
            "confidence": 0.0,
            "category": "general",
            "subcategory": "",
            "keywords": []
        }
        
        # 使用内容分类结果
        if content_classification:
            type_info.update({
                "type": content_classification.get("document_type", "未知文档"),
                "confidence": content_classification.get("confidence", 0.5),
                "category": content_classification.get("content_category", "general")
            })
        
        # 基于关键词进一步细化
        text_lower = full_text.lower()
        
        type_keywords = {
            "技术方案": ["方案", "设计", "架构", "技术", "系统"],
            "项目报告": ["报告", "项目", "总结", "分析"],
            "用户手册": ["手册", "指南", "说明", "操作"],
            "合同协议": ["合同", "协议", "条款", "约定"],
            "管理制度": ["制度", "规范", "流程", "管理"],
            "财务文档": ["财务", "预算", "成本", "费用", "会计"]
        }
        
        best_match = {"type": "未知文档", "score": 0, "keywords": []}
        
        for doc_type, keywords in type_keywords.items():
            score = 0
            matched_keywords = []
            
            for keyword in keywords:
                count = text_lower.count(keyword)
                if count > 0:
                    score += count
                    matched_keywords.append(keyword)
            
            if score > best_match["score"]:
                best_match = {
                    "type": doc_type,
                    "score": score,
                    "keywords": matched_keywords
                }
        
        if best_match["score"] > 0:
            type_info.update({
                "type": best_match["type"],
                "confidence": min(0.9, best_match["score"] / 10),
                "keywords": best_match["keywords"]
            })
        
        return type_info
    
    def _extract_metadata(self, file_content: Dict, full_text: str) -> Dict[str, Any]:
        """提取元数据信息"""
        metadata_info = {
            "has_metadata": False,
            "metadata_fields": [],
            "file_info": {},
            "content_info": {}
        }
        
        # 检查文件元数据
        metadata = file_content.get("metadata", {})
        if metadata:
            metadata_info["has_metadata"] = True
            metadata_info["metadata_fields"] = list(metadata.keys())
            metadata_info["file_info"] = metadata
        
        # 提取内容元信息
        metadata_info["content_info"] = {
            "estimated_reading_time": max(1, len(full_text) // 500),  # 按500字/分钟计算
            "complexity_level": self._assess_complexity(full_text),
            "content_density": len(full_text) / max(1, full_text.count('\n')),
            "has_technical_content": any(keyword in full_text.lower() 
                                       for keyword in ['api', '数据库', '算法', '架构', '系统'])
        }
        
        return metadata_info
    
    def _assess_complexity(self, text: str) -> str:
        """评估内容复杂度"""
        if not text:
            return "low"
        
        # 计算复杂度指标
        avg_sentence_length = len(text) / max(1, text.count('。') + text.count('.'))
        technical_terms = len(re.findall(r'[A-Z]{2,}|[a-z]+_[a-z]+|\w+\(\)', text))
        
        if avg_sentence_length > 50 or technical_terms > 20:
            return "high"
        elif avg_sentence_length > 30 or technical_terms > 10:
            return "medium"
        else:
            return "low"
    
    def _calculate_document_metrics(self, text_stats: Dict, parsed_pages: List) -> Dict[str, Any]:
        """计算文档指标"""
        metrics = {
            "readability_score": 0.0,
            "information_density": 0.0,
            "structure_score": 0.0,
            "completeness_score": 0.0
        }
        
        if not text_stats or not parsed_pages:
            return metrics
        
        total_words = text_stats.get("total_words", 0)
        total_sentences = text_stats.get("total_sentences", 0)
        
        # 可读性分数（基于平均句长）
        if total_sentences > 0:
            avg_sentence_length = total_words / total_sentences
            metrics["readability_score"] = max(0, min(1, (50 - avg_sentence_length) / 50))
        
        # 信息密度（基于内容与页面比）
        if parsed_pages:
            content_pages = sum(1 for page in parsed_pages if page.get("has_content"))
            metrics["information_density"] = content_pages / len(parsed_pages)
        
        # 结构分数（基于页面结构完整性）
        structured_pages = sum(1 for page in parsed_pages 
                             if page.get("structure", {}).get("has_headings"))
        if parsed_pages:
            metrics["structure_score"] = structured_pages / len(parsed_pages)
        
        # 完整性分数（基于内容长度和页面数）
        if total_words > 100 and len(parsed_pages) > 0:
            metrics["completeness_score"] = min(1.0, total_words / 1000)
        
        return metrics
    
    def _extract_summary(self, full_text: str, parsed_pages: List) -> Dict[str, Any]:
        """提取摘要信息"""
        summary_info = {
            "abstract": "",
            "key_points": [],
            "main_topics": [],
            "summary_available": False
        }
        
        if not full_text:
            return summary_info
        
        # 查找摘要或概述部分
        abstract_patterns = [
            r'摘要[：:]\s*([^\n]{50,500})',
            r'概述[：:]\s*([^\n]{50,500})',
            r'简介[：:]\s*([^\n]{50,500})'
        ]
        
        for pattern in abstract_patterns:
            matches = re.findall(pattern, full_text)
            if matches:
                summary_info["abstract"] = matches[0]
                summary_info["summary_available"] = True
                break
        
        # 如果没有找到摘要，生成简单摘要
        if not summary_info["abstract"]:
            sentences = re.split(r'[。！？]', full_text)
            meaningful_sentences = [s.strip() for s in sentences 
                                  if s.strip() and len(s.strip()) > 20]
            if meaningful_sentences:
                summary_info["abstract"] = meaningful_sentences[0][:200] + "..."
        
        # 提取关键点（基于标题和重要句子）
        key_points = []
        for page in parsed_pages:
            elements = page.get("elements", {})
            headings = elements.get("headings", [])
            key_points.extend(headings[:3])  # 每页最多3个标题
        
        summary_info["key_points"] = key_points[:10]  # 限制数量
        
        # 提取主要话题（基于高频词汇）
        words = re.findall(r'[\u4e00-\u9fff]{2,}', full_text)
        word_freq = {}
        for word in words:
            if len(word) >= 2:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 排除常见词汇
        common_words = {'这个', '一个', '可以', '需要', '进行', '通过', '实现', '系统', '管理'}
        main_topics = [word for word, freq in sorted(word_freq.items(), 
                                                    key=lambda x: x[1], reverse=True)
                      if word not in common_words][:10]
        
        summary_info["main_topics"] = main_topics
        
        return summary_info
    
    def _calculate_confidence_score(self, title_info: Dict, author_info: Dict, 
                                   date_info: Dict, document_type: Dict) -> float:
        """计算总体置信度分数"""
        scores = [
            title_info.get("confidence", 0) * 0.3,
            author_info.get("confidence", 0) * 0.2,
            date_info.get("confidence", 0) * 0.2,
            document_type.get("confidence", 0) * 0.3
        ]
        return sum(scores)
    
    def _count_extracted_fields(self, title_info: Dict, author_info: Dict, 
                               date_info: Dict, document_type: Dict) -> int:
        """计算成功提取的字段数量"""
        count = 0
        if title_info.get("confidence", 0) > 0.3:
            count += 1
        if author_info.get("confidence", 0) > 0.3:
            count += 1
        if date_info.get("confidence", 0) > 0.3:
            count += 1
        if document_type.get("confidence", 0) > 0.3:
            count += 1
        return count
    
    def get_stage_name(self) -> str:
        return "extract_basic"
    
    def get_progress_percentage(self) -> int:
        return 45
    
    def get_stage_description(self) -> str:
        return "提取文档标题、作者、类型等基础信息"
