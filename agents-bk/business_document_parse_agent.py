#!/usr/bin/env python3
"""
业务文档解析Agent - 解析各种格式文档，提取文本内容
"""

import asyncio
import os
import json
from typing import Dict, Any, List
from datetime import datetime
from pathlib import Path
from .business_base_agent import BusinessBaseAgent, BusinessAgentContext

class BusinessDocumentParseAgent(BusinessBaseAgent):
    """业务文档解析Agent"""
    
    def __init__(self):
        super().__init__(name="业务文档解析Agent")
        self.add_dependency("download_info")  # 依赖MinIO下载Agent
    
    async def process(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """解析下载的文档"""
        await asyncio.sleep(0.3)  # 模拟解析时间
        
        # 获取下载信息
        download_info = context.get_extracted_data("download_info", {})
        supported_files = context.get_extracted_data("supported_files", {})
        download_results = context.get_extracted_data("download_results", [])

        # 如果supported_files为空，尝试从download_results获取
        valid_files = supported_files.get("valid_files", [])
        if not valid_files and download_results:
            # 从download_results中筛选成功下载的文件
            valid_files = [result for result in download_results if result.get("download_success")]

        # 解析文件
        parse_results = await self._parse_files(valid_files)
        
        # 验证解析结果
        parse_validation = self._validate_parsed_content(parse_results)
        
        # 生成解析统计
        parse_statistics = self._calculate_parse_statistics(parse_results)
        
        # 提取文本内容
        extracted_texts = self._extract_text_content(parse_results)
        
        # 分类文档内容
        content_classification = self._classify_document_content(extracted_texts, context.action)
        
        return {
            "parse_info": {
                "parse_time": datetime.now().isoformat(),
                "total_files": len(valid_files),
                "successful_parses": parse_statistics["successful_parses"],
                "failed_parses": parse_statistics["failed_parses"],
                "total_text_length": parse_statistics["total_text_length"],
                "parse_method": "integrated_parser"
            },
            "parse_results": parse_results,
            "parse_validation": parse_validation,
            "parse_statistics": parse_statistics,
            "extracted_texts": extracted_texts,
            "content_classification": content_classification,
            "parse_summary": {
                "status": "completed",
                "parsing_successful": parse_statistics["successful_parses"] > 0,
                "text_ready_for_chunking": len(extracted_texts) > 0,
                "next_stage": "text_chunking"
            }
        }
    
    async def _parse_files(self, valid_files: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析文件列表"""
        parse_results = []
        
        for file_info in valid_files:
            try:
                file_path = file_info.get("local_path")
                file_name = file_info.get("file_name", "")
                
                if not file_path or not os.path.exists(file_path):
                    parse_results.append({
                        "file_name": file_name,
                        "file_path": file_path,
                        "parse_success": False,
                        "error": "File not found",
                        "content": "",
                        "pages": [],
                        "metadata": {}
                    })
                    continue
                
                # 根据文件扩展名选择解析方法
                file_ext = Path(file_name).suffix.lower()
                
                if file_ext == '.json':
                    result = await self._parse_json_file(file_path, file_name)
                elif file_ext == '.pdf':
                    result = await self._parse_pdf_file(file_path, file_name)
                elif file_ext in ['.docx', '.doc']:
                    result = await self._parse_word_file(file_path, file_name)
                elif file_ext == '.txt':
                    result = await self._parse_text_file(file_path, file_name)
                else:
                    result = await self._parse_generic_file(file_path, file_name)
                
                parse_results.append(result)
                
            except Exception as e:
                parse_results.append({
                    "file_name": file_info.get("file_name", "unknown"),
                    "file_path": file_info.get("local_path"),
                    "parse_success": False,
                    "error": str(e),
                    "content": "",
                    "pages": [],
                    "metadata": {}
                })
        
        return parse_results
    
    async def _parse_json_file(self, file_path: str, file_name: str) -> Dict[str, Any]:
        """解析JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # 将JSON数据转换为文本内容
            if isinstance(json_data, dict):
                content_parts = []
                for key, value in json_data.items():
                    if isinstance(value, str):
                        content_parts.append(f"{key}: {value}")
                    else:
                        content_parts.append(f"{key}: {str(value)}")
                
                content = "\n".join(content_parts)
                
                # 创建页面结构
                pages = [{
                    "page_number": 1,
                    "content": content,
                    "metadata": {"source": "json_conversion"}
                }]
                
            else:
                content = str(json_data)
                pages = [{
                    "page_number": 1,
                    "content": content,
                    "metadata": {"source": "json_conversion"}
                }]
            
            return {
                "file_name": file_name,
                "file_path": file_path,
                "parse_success": True,
                "error": None,
                "content": content,
                "pages": pages,
                "metadata": {
                    "file_type": "json",
                    "original_data": json_data,
                    "page_count": 1,
                    "text_length": len(content)
                }
            }
            
        except Exception as e:
            return {
                "file_name": file_name,
                "file_path": file_path,
                "parse_success": False,
                "error": f"JSON parse error: {str(e)}",
                "content": "",
                "pages": [],
                "metadata": {}
            }
    
    async def _parse_pdf_file(self, file_path: str, file_name: str) -> Dict[str, Any]:
        """解析PDF文件（模拟）"""
        try:
            # 这里应该使用实际的PDF解析库
            # 现在模拟PDF内容
            content = f"PDF文档内容 - {file_name}\n这是从PDF文件解析出的文本内容。\n包含多页内容和格式信息。"
            
            pages = [
                {
                    "page_number": 1,
                    "content": f"第一页内容 - {file_name}",
                    "metadata": {"source": "pdf_page_1"}
                },
                {
                    "page_number": 2,
                    "content": f"第二页内容 - {file_name}",
                    "metadata": {"source": "pdf_page_2"}
                }
            ]
            
            return {
                "file_name": file_name,
                "file_path": file_path,
                "parse_success": True,
                "error": None,
                "content": content,
                "pages": pages,
                "metadata": {
                    "file_type": "pdf",
                    "page_count": len(pages),
                    "text_length": len(content)
                }
            }
            
        except Exception as e:
            return {
                "file_name": file_name,
                "file_path": file_path,
                "parse_success": False,
                "error": f"PDF parse error: {str(e)}",
                "content": "",
                "pages": [],
                "metadata": {}
            }
    
    async def _parse_word_file(self, file_path: str, file_name: str) -> Dict[str, Any]:
        """解析Word文件（模拟）"""
        try:
            # 这里应该使用实际的Word解析库
            content = f"Word文档内容 - {file_name}\n这是从Word文件解析出的文本内容。"
            
            pages = [{
                "page_number": 1,
                "content": content,
                "metadata": {"source": "word_conversion"}
            }]
            
            return {
                "file_name": file_name,
                "file_path": file_path,
                "parse_success": True,
                "error": None,
                "content": content,
                "pages": pages,
                "metadata": {
                    "file_type": "word",
                    "page_count": 1,
                    "text_length": len(content)
                }
            }
            
        except Exception as e:
            return {
                "file_name": file_name,
                "file_path": file_path,
                "parse_success": False,
                "error": f"Word parse error: {str(e)}",
                "content": "",
                "pages": [],
                "metadata": {}
            }
    
    async def _parse_text_file(self, file_path: str, file_name: str) -> Dict[str, Any]:
        """解析文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            pages = [{
                "page_number": 1,
                "content": content,
                "metadata": {"source": "text_file"}
            }]
            
            return {
                "file_name": file_name,
                "file_path": file_path,
                "parse_success": True,
                "error": None,
                "content": content,
                "pages": pages,
                "metadata": {
                    "file_type": "text",
                    "page_count": 1,
                    "text_length": len(content)
                }
            }
            
        except Exception as e:
            return {
                "file_name": file_name,
                "file_path": file_path,
                "parse_success": False,
                "error": f"Text parse error: {str(e)}",
                "content": "",
                "pages": [],
                "metadata": {}
            }
    
    async def _parse_generic_file(self, file_path: str, file_name: str) -> Dict[str, Any]:
        """解析通用文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            pages = [{
                "page_number": 1,
                "content": content,
                "metadata": {"source": "generic_file"}
            }]
            
            return {
                "file_name": file_name,
                "file_path": file_path,
                "parse_success": True,
                "error": None,
                "content": content,
                "pages": pages,
                "metadata": {
                    "file_type": "generic",
                    "page_count": 1,
                    "text_length": len(content)
                }
            }
            
        except Exception as e:
            return {
                "file_name": file_name,
                "file_path": file_path,
                "parse_success": False,
                "error": f"Generic parse error: {str(e)}",
                "content": "",
                "pages": [],
                "metadata": {}
            }
    
    def _validate_parsed_content(self, parse_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证解析内容"""
        validation = {
            "total_files": len(parse_results),
            "valid_parses": 0,
            "invalid_parses": 0,
            "validation_details": []
        }
        
        for result in parse_results:
            if result["parse_success"] and result["content"]:
                validation["valid_parses"] += 1
                validation["validation_details"].append({
                    "file_name": result["file_name"],
                    "status": "valid",
                    "content_length": len(result["content"]),
                    "page_count": len(result["pages"])
                })
            else:
                validation["invalid_parses"] += 1
                validation["validation_details"].append({
                    "file_name": result["file_name"],
                    "status": "invalid",
                    "error": result.get("error", "unknown")
                })
        
        return validation
    
    def _calculate_parse_statistics(self, parse_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算解析统计"""
        stats = {
            "total_files": len(parse_results),
            "successful_parses": 0,
            "failed_parses": 0,
            "total_text_length": 0,
            "total_pages": 0,
            "file_types": {}
        }
        
        for result in parse_results:
            if result["parse_success"]:
                stats["successful_parses"] += 1
                stats["total_text_length"] += len(result["content"])
                stats["total_pages"] += len(result["pages"])
                
                file_type = result.get("metadata", {}).get("file_type", "unknown")
                stats["file_types"][file_type] = stats["file_types"].get(file_type, 0) + 1
            else:
                stats["failed_parses"] += 1
        
        return stats
    
    def _extract_text_content(self, parse_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取文本内容"""
        extracted_texts = []
        
        for result in parse_results:
            if result["parse_success"]:
                extracted_texts.append({
                    "file_name": result["file_name"],
                    "content": result["content"],
                    "pages": result["pages"],
                    "metadata": result["metadata"]
                })
        
        return extracted_texts
    
    def _classify_document_content(self, extracted_texts: List[Dict[str, Any]], action: str) -> Dict[str, Any]:
        """分类文档内容"""
        classification = {
            "document_type": action,
            "content_analysis": {},
            "relevance_score": 0.0
        }
        
        if action == "项目档案":
            # 分析项目相关内容
            project_keywords = ["项目", "开发", "系统", "技术", "管理", "建设"]
            classification["content_analysis"] = {
                "project_related": True,
                "keywords_found": project_keywords[:3],  # 模拟找到的关键词
                "confidence": 0.8
            }
            classification["relevance_score"] = 0.8
            
        elif action == "文书档案":
            # 分析文书相关内容
            conference_keywords = ["会议", "纪要", "决议", "通知", "文件"]
            classification["content_analysis"] = {
                "conference_related": True,
                "keywords_found": conference_keywords[:2],
                "confidence": 0.7
            }
            classification["relevance_score"] = 0.7
        
        return classification
    
    def get_stage_name(self) -> str:
        return "document_parse"
    
    def get_progress_percentage(self) -> int:
        return 35
    
    def get_stage_description(self) -> str:
        return "解析各种格式文档，提取文本内容"
