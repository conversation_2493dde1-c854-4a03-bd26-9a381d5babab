# HNGPT标准化Agent系统

## 📋 概述

HNGPT标准化Agent系统是一个高效、可扩展的工作流框架，用于构建和执行复杂的Agent工作流。该系统基于有向无环图(DAG)设计，支持并行执行、条件分支、错误处理和实时监控。

## 🌟 主要特性

- **标准化接口**: 统一的Agent输入输出接口，确保数据流的一致性
- **DAG工作流**: 基于有向无环图的工作流设计，支持复杂依赖关系
- **并行执行**: 自动识别和并行执行独立的Agent任务
- **条件执行**: 支持基于上下文数据的条件执行
- **错误处理**: 完善的错误处理和恢复机制
- **可视化**: 内置Mermaid图表生成，直观展示工作流结构
- **监控与统计**: 实时监控工作流执行状态和性能指标
- **可扩展性**: 插件式架构，易于添加新的Agent类型

## 🔧 核心组件

### 1. 基础类

- **HNGPTBaseAgent**: 标准化Agent基类，定义了统一的接口和生命周期
- **WorkflowContext**: 工作流上下文，用于Agent间数据传递
- **AgentMetadata**: Agent元数据，包含名称、版本、描述等信息
- **DataSchema**: 数据模式定义，用于输入输出验证

### 2. 工作流构建

- **HNGPTWorkflowDAG**: 工作流DAG构建器，用于创建和管理工作流
- **AgentNode**: Agent节点定义，包含依赖关系和执行条件
- **HNGPTWorkflowFactory**: 工作流工厂，提供常用工作流模板

### 3. 工作流执行

- **HNGPTWorkflowExecutor**: 工作流执行器，负责按DAG顺序执行Agent
- **ExecutionConfig**: 执行配置，控制并发度、超时等参数
- **ExecutionResult**: 执行结果，包含统计信息和性能指标

### 4. 工具与实用程序

- **HNGPTWorkflowValidator**: 工作流验证器，检查DAG结构和数据流
- **HNGPTWorkflowExporter**: 工作流导出器，支持JSON、YAML和Mermaid格式
- **HNGPTWorkflowMonitor**: 工作流监控器，跟踪活跃工作流和执行历史

## 🚀 快速开始

### 基础工作流示例

```python
from agents import (
    HNGPTWorkflowFactory, HNGPTWorkflowExecutor, WorkflowContext
)

async def run_basic_workflow():
    # 创建工作流
    dag = HNGPTWorkflowFactory.create_document_processing_workflow()
    
    # 创建执行器
    executor = HNGPTWorkflowFactory.create_executor(dag)
    
    # 创建上下文
    context = WorkflowContext(
        task_id="example_task_001",
        workflow_id=dag.workflow_id
    )
    
    # 设置输入数据
    context.set_data("task_id", "example_task_001")
    context.set_data("project_name", "示例项目")
    context.set_data("action", "项目档案")
    context.set_data("file_urls", ["docs/example.json"])
    
    # 执行工作流
    result = await executor.execute(context)
    
    # 处理结果
    if result.success:
        print("工作流执行成功")
        print(f"提取的数据: {context.get_data('extracted_data')}")
    else:
        print(f"工作流执行失败: {result.error_message}")
```

### 自定义Agent示例

```python
from agents import (
    HNGPTBaseAgent, AgentMetadata, DataSchema, DataType
)

class MyCustomAgent(HNGPTBaseAgent):
    """自定义Agent示例"""
    
    def _get_metadata(self) -> AgentMetadata:
        return AgentMetadata(
            name="MyCustomAgent",
            version="1.0.0",
            description="自定义Agent示例",
            category="custom",
            tags=["example", "custom"]
        )
    
    def _define_input_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="input_data",
                data_type=DataType.DICT,
                required=True,
                description="输入数据"
            )
        ]
    
    def _define_output_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="output_data",
                data_type=DataType.DICT,
                required=True,
                description="输出数据"
            )
        ]
    
    def _define_dependencies(self) -> List[str]:
        return ["InitAgent"]  # 依赖InitAgent
    
    async def process(self, context: WorkflowContext) -> Dict[str, Any]:
        # 获取输入数据
        input_data = context.get_data("input_data")
        
        # 处理逻辑
        output_data = {
            "result": "处理完成",
            "timestamp": datetime.now().isoformat()
        }
        
        return {
            "output_data": output_data
        }
```

### 自定义工作流示例

```python
from agents import (
    HNGPTWorkflowDAG, HNGPTWorkflowExecutor
)

# 创建自定义工作流
dag = HNGPTWorkflowDAG(name="custom_workflow")

# 创建Agent实例
init_agent = HNGPTInitAgent()
custom_agent = MyCustomAgent()
storage_agent = HNGPTStorageAgent()

# 添加Agent到DAG
init_id = dag.add_agent(init_agent)
custom_id = dag.add_agent(custom_agent, dependencies=[init_id])
storage_id = dag.add_agent(storage_agent, dependencies=[custom_id])

# 创建执行器
executor = HNGPTWorkflowExecutor(dag)

# 执行工作流
result = await executor.execute(context)
```

## 📊 可视化工作流

```python
from agents import HNGPTWorkflowExporter

# 导出Mermaid图表
mermaid_code = HNGPTWorkflowExporter.export_mermaid(dag)
print(mermaid_code)
```

生成的Mermaid图表示例:

```mermaid
graph TD
    init_001["InitAgent"] --> download_001["DownloadAgent"]
    download_001 --> parse_001["ParseAgent"]
    parse_001 --> extract_001["ExtractionAgent"]
    extract_001 --> storage_001["StorageAgent"]
    
    classDef completed fill:#d4edda,stroke:#155724,stroke-width:2px
    classDef failed fill:#f8d7da,stroke:#721c24,stroke-width:2px
    classDef running fill:#fff3cd,stroke:#856404,stroke-width:2px
```

## 🔄 并行执行

```python
# 创建并行工作流
dag = HNGPTWorkflowFactory.create_parallel_extraction_workflow()

# 添加并行Agent
entity_agent = EntityExtractionAgent()
relation_agent = RelationExtractionAgent()
keyword_agent = KeywordExtractionAgent()

# 设置并行组
entity_id = dag.add_agent(entity_agent, dependencies=[parse_id], parallel_group="extractors")
relation_id = dag.add_agent(relation_agent, dependencies=[parse_id], parallel_group="extractors")
keyword_id = dag.add_agent(keyword_agent, dependencies=[parse_id], parallel_group="extractors")

# 合并结果
merge_id = dag.add_agent(merge_agent, dependencies=[entity_id, relation_id, keyword_id])
```

## 🔍 工作流验证

```python
from agents import HNGPTWorkflowValidator

# 验证工作流
validation = HNGPTWorkflowValidator.validate_dag(dag)

if validation["valid"]:
    print("工作流验证通过")
else:
    print("工作流验证失败:")
    for error in validation["errors"]:
        print(f"- {error}")
    
    for warning in validation["warnings"]:
        print(f"- 警告: {warning['message']}")
```

## 📈 性能监控

```python
from agents import HNGPTWorkflowMonitor, hngpt_workflow_monitor

# 注册工作流
hngpt_workflow_monitor.register_workflow(dag)

# 获取活跃工作流
active_workflows = hngpt_workflow_monitor.get_active_workflows()
print(f"活跃工作流数量: {len(active_workflows)}")

# 获取执行统计
stats = hngpt_workflow_monitor.get_execution_statistics()
print(f"总工作流数: {stats['total_workflows']}")
print(f"成功率: {stats['success_rate'] * 100:.1f}%")
```

## 🧪 测试

运行测试脚本验证系统功能:

```bash
python test_hngpt_agents.py
```

## 📚 更多资源

- 查看`agents/hngpt_workflow_examples.py`获取更多使用示例
- 查看`agents/hngpt_document_agents.py`了解预定义Agent实现
- 查看`agents/hngpt_workflow_factory.py`了解工作流模板

## 🔧 与现有系统的区别

相比于原有的Agent系统，HNGPT标准化Agent系统有以下改进:

1. **标准化接口**: 统一的输入输出接口，确保数据流的一致性
2. **并行执行支持**: 自动识别和并行执行独立的Agent任务
3. **条件执行**: 支持基于上下文数据的条件执行
4. **错误处理**: 更完善的错误处理和恢复机制
5. **可视化**: 内置Mermaid图表生成，直观展示工作流结构
6. **性能优化**: 通过并行执行提高性能
7. **可扩展性**: 更灵活的插件式架构

## 🤝 贡献

欢迎贡献新的Agent实现或工作流模板！请确保遵循以下准则:

1. 继承`HNGPTBaseAgent`基类并实现必要的方法
2. 提供完整的输入输出模式定义
3. 添加详细的文档和使用示例
4. 编写单元测试验证功能
