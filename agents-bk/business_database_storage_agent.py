#!/usr/bin/env python3
"""
业务数据库存储Agent - 存储结构化信息到SQLite
"""

import asyncio
import os
import sys
import json
from typing import Dict, Any, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, '/workspace/hngpt')

from .business_base_agent import BusinessBaseAgent, BusinessAgentContext

# 导入MySQL相关模块
try:
    import aiomysql
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    print("警告: 无法导入MySQL相关模块，请安装 aiomysql 和 pymysql")
    MYSQL_AVAILABLE = False

class BusinessDatabaseStorageAgent(BusinessBaseAgent):
    """业务数据库存储Agent"""
    
    def __init__(self):
        super().__init__(name="业务数据库存储Agent")
        self.add_dependency("extraction_info")  # 依赖LLM提取Agent
        self.db_pool = None
        self.mysql_config = None
    
    async def process(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """存储结构化信息到数据库"""
        await asyncio.sleep(0.5)  # 模拟数据库存储时间
        
        # 初始化MySQL数据库连接
        await self._initialize_mysql_database(context.config)
        
        # 获取提取信息
        extraction_info = context.get_extracted_data("extraction_info", {})
        merged_information = context.get_extracted_data("merged_information", {})
        
        # 存储到MySQL数据库
        storage_results = await self._store_to_mysql_database(merged_information, context)
        
        # 验证存储结果
        storage_validation = self._validate_database_storage(storage_results)
        
        # 计算存储统计
        storage_statistics = await self._calculate_storage_statistics(storage_results)
        
        # 生成最终报告
        final_report = self._generate_final_report(storage_results, context)
        
        # 清理资源
        await self._cleanup_resources(context)
        
        return {
            "database_storage_info": {
                "storage_time": datetime.now().isoformat(),
                "database_type": "mysql",
                "database_host": self.mysql_config.get("host", "unknown") if self.mysql_config else "unknown",
                "database_name": self.mysql_config.get("database", "unknown") if self.mysql_config else "unknown",
                "table_name": self._get_table_name(context.action),
                "records_stored": storage_statistics["successful_records"],
                "storage_method": "upsert"
            },
            "storage_results": storage_results,
            "storage_validation": storage_validation,
            "storage_statistics": storage_statistics,
            "final_report": final_report,
            "database_summary": {
                "status": "completed",
                "storage_successful": storage_statistics["successful_records"] > 0,
                "workflow_completed": True,
                "final_stage": True
            }
        }
    
    async def _initialize_mysql_database(self, config: Dict[str, Any]):
        """初始化MySQL数据库连接"""
        if not MYSQL_AVAILABLE:
            print("❌ MySQL模块不可用，请安装 aiomysql 和 pymysql")
            return

        try:
            # 获取MySQL配置
            self.mysql_config = config.get("mysql", {})

            if not self.mysql_config:
                print("❌ 未找到MySQL配置")
                return

            # 创建连接池
            self.db_pool = await aiomysql.create_pool(
                host=self.mysql_config.get("host", "localhost"),
                port=self.mysql_config.get("port", 3306),
                user=self.mysql_config.get("username", "root"),
                password=self.mysql_config.get("password", ""),
                db=self.mysql_config.get("database", "hngpt"),
                charset=self.mysql_config.get("character_set", "utf8mb4"),
                autocommit=True,
                maxsize=10,
                minsize=1
            )

            # 创建表
            await self._create_mysql_tables()

            print(f"✅ MySQL数据库初始化成功: {self.mysql_config.get('host')}:{self.mysql_config.get('port')}/{self.mysql_config.get('database')}")

        except Exception as e:
            print(f"⚠️ MySQL数据库初始化失败: {e}")
            self.db_pool = None
    
    async def _create_mysql_tables(self):
        """创建MySQL数据库表"""
        if not self.db_pool:
            return

        async with self.db_pool.acquire() as conn:
            async with conn.cursor() as cursor:

                # 创建项目提取表
                await cursor.execute("""
                    CREATE TABLE IF NOT EXISTS `project_extract` (
                        `id` INTEGER PRIMARY KEY,
                        `name` TEXT NOT NULL,
                        `start_date` DATE,
                        `end_date` DATE,
                        `total_investment` INTEGER,
                        `responsible_unit` TEXT,
                        `leader` TEXT,
                        `research_points` TEXT,
                        `innovation` TEXT,
                        `main_deliverables` TEXT,
                        `patent` TEXT
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目提取表'
                """)

                # 创建文书提取表
                await cursor.execute("""
                    CREATE TABLE IF NOT EXISTS `conference_extract` (
                        `id` INTEGER PRIMARY KEY,
                        `name` TEXT NOT NULL,
                        `date` DATE,
                        `type` TEXT,
                        `organizer` TEXT,
                        `participants` TEXT,
                        `summary` TEXT
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文书提取表'
                """)

                # 创建处理记录表
                await cursor.execute("""
                    CREATE TABLE IF NOT EXISTS `processing_records` (
                        `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
                        `task_id` VARCHAR(100) NOT NULL UNIQUE,
                        `project_name` VARCHAR(500) NOT NULL,
                        `action` VARCHAR(50) NOT NULL,
                        `status` VARCHAR(50) NOT NULL,
                        `total_files` INT DEFAULT 0,
                        `processed_files` INT DEFAULT 0,
                        `extracted_records` INT DEFAULT 0,
                        `processing_time_seconds` DECIMAL(10,2) DEFAULT 0.00,
                        `start_time` DATETIME NOT NULL,
                        `end_time` DATETIME NOT NULL,
                        `error_message` TEXT,
                        `metadata` JSON,
                        `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX `idx_project_name` (`project_name`(100)),
                        INDEX `idx_status` (`status`),
                        INDEX `idx_create_time` (`create_time`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='处理记录表'
                """)

                await conn.commit()
    
    async def _store_to_mysql_database(self, merged_information: Dict[str, Any], context: BusinessAgentContext) -> Dict[str, Any]:
        """存储到MySQL数据库"""
        storage_result = {
            "storage_success": False,
            "records_stored": 0,
            "table_name": "",
            "record_id": None,
            "error": None,
            "storage_details": {}
        }

        if not self.db_pool:
            storage_result["error"] = "MySQL connection pool not available"
            return storage_result

        if not merged_information.get("merged_data"):
            storage_result["error"] = "No merged data to store"
            return storage_result
        
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    merged_data = merged_information["merged_data"]
                    current_time = datetime.now()

                    if context.action == "项目档案":
                        # 存储项目档案到project_extract表
                        table_name = "project_extract"
                        storage_result["table_name"] = table_name

                        # 生成唯一ID (时间戳+随机数)
                        import time
                        import random
                        record_id = int(time.time() * 1000) + random.randint(0, 9999)

                        await cursor.execute("""
                            INSERT INTO `project_extract` (
                                `id`, `name`, `start_date`, `end_date`, `total_investment`,
                                `responsible_unit`, `leader`, `research_points`, `innovation`,
                                `main_deliverables`, `patent`
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            ON DUPLICATE KEY UPDATE
                                `start_date` = VALUES(`start_date`),
                                `end_date` = VALUES(`end_date`),
                                `total_investment` = VALUES(`total_investment`),
                                `responsible_unit` = VALUES(`responsible_unit`),
                                `leader` = VALUES(`leader`),
                                `research_points` = VALUES(`research_points`),
                                `innovation` = VALUES(`innovation`),
                                `main_deliverables` = VALUES(`main_deliverables`),
                                `patent` = VALUES(`patent`)
                        """, (
                            record_id,
                            context.project_name,
                            merged_data.get("start_date", ""),
                            merged_data.get("end_date", ""),
                            merged_data.get("total_investment", ""),
                            merged_data.get("responsible_unit", ""),
                            merged_data.get("leader", ""),
                            merged_data.get("research_points", ""),
                            merged_data.get("innovation", ""),
                            merged_data.get("main_deliverables", ""),
                            merged_data.get("patent", "")
                        ))
                
                    elif context.action == "文书档案":
                        # 存储文书档案到conference_extract表
                        table_name = "conference_extract"
                        storage_result["table_name"] = table_name

                        # 生成唯一ID (时间戳+随机数)
                        import time
                        import random
                        record_id = int(time.time() * 1000) + random.randint(0, 9999)

                        await cursor.execute("""
                            INSERT INTO `conference_extract` (
                                `id`, `name`, `date`, `type`, `organizer`, `participants`, `summary`
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                            ON DUPLICATE KEY UPDATE
                                `date` = VALUES(`date`),
                                `type` = VALUES(`type`),
                                `organizer` = VALUES(`organizer`),
                                `participants` = VALUES(`participants`),
                                `summary` = VALUES(`summary`)
                        """, (
                            record_id,
                            context.project_name,
                            merged_data.get("date", ""),
                            merged_data.get("type", ""),
                            merged_data.get("organizer", ""),
                            merged_data.get("participants", ""),
                            merged_data.get("summary", "")
                        ))
            
                    # 存储处理记录
                    init_info = context.get_extracted_data("init_info", {})
                    processing_start_str = init_info.get("initialization_time", current_time.isoformat())

                    # 计算处理时间
                    try:
                        start_dt = datetime.fromisoformat(processing_start_str.replace('Z', '+00:00'))
                        end_dt = current_time
                        processing_time = (end_dt - start_dt).total_seconds()
                        processing_start = start_dt
                    except:
                        processing_time = 0.0
                        processing_start = current_time

                    await cursor.execute("""
                        INSERT INTO `processing_records` (
                            `task_id`, `project_name`, `action`, `status`, `total_files`,
                            `processed_files`, `extracted_records`, `processing_time_seconds`,
                            `start_time`, `end_time`, `error_message`, `metadata`
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                            `status` = VALUES(`status`),
                            `total_files` = VALUES(`total_files`),
                            `processed_files` = VALUES(`processed_files`),
                            `extracted_records` = VALUES(`extracted_records`),
                            `processing_time_seconds` = VALUES(`processing_time_seconds`),
                            `end_time` = VALUES(`end_time`),
                            `error_message` = VALUES(`error_message`),
                            `metadata` = VALUES(`metadata`)
                    """, (
                        context.task_id,
                        context.project_name,
                        context.action,
                        "completed",
                        init_info.get("file_count", 0),
                        1,  # 假设处理了1个文件
                        1,  # 假设提取了1条记录
                        processing_time,
                        processing_start,
                        current_time,
                        None,
                        json.dumps({
                            "source_files": merged_information.get("source_files", []),
                            "confidence_scores": merged_information.get("confidence_scores", {}),
                            "workflow_version": "business_agent_v1.0"
                        }, ensure_ascii=False)
                    ))

                    await conn.commit()
            
                    storage_result["storage_success"] = True
                    storage_result["records_stored"] = 2  # 主记录 + 处理记录
                    storage_result["record_id"] = cursor.lastrowid
                    storage_result["storage_details"] = {
                        "main_table": table_name,
                        "processing_table": "processing_records",
                        "processing_time_seconds": processing_time,
                        "fields_stored": len(merged_data)
                    }

        except Exception as e:
            storage_result["error"] = str(e)
            print(f"MySQL存储失败: {e}")

        return storage_result
    
    def _get_table_name(self, action: str) -> str:
        """获取表名"""
        if action == "项目档案":
            return "project_extract"
        elif action == "文书档案":
            return "conference_extract"
        else:
            return "unknown"
    
    def _validate_database_storage(self, storage_results: Dict[str, Any]) -> Dict[str, Any]:
        """验证数据库存储"""
        validation = {
            "storage_successful": storage_results.get("storage_success", False),
            "records_stored": storage_results.get("records_stored", 0),
            "table_name": storage_results.get("table_name", "unknown"),
            "data_integrity": True,
            "validation_details": {}
        }
        
        if storage_results.get("storage_success"):
            validation["validation_details"] = {
                "record_id": storage_results.get("record_id"),
                "storage_method": "upsert",
                "fields_stored": storage_results.get("storage_details", {}).get("fields_stored", 0),
                "processing_time": storage_results.get("storage_details", {}).get("processing_time_seconds", 0)
            }
        else:
            validation["data_integrity"] = False
            validation["validation_details"] = {
                "error": storage_results.get("error", "unknown"),
                "failure_reason": "Database storage failed"
            }
        
        return validation
    
    async def _calculate_storage_statistics(self, storage_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算存储统计"""
        stats = {
            "total_operations": 1,
            "successful_records": storage_results.get("records_stored", 0) if storage_results.get("storage_success") else 0,
            "failed_records": 0 if storage_results.get("storage_success") else 1,
            "storage_efficiency": 1.0 if storage_results.get("storage_success") else 0.0,
            "database_size": 0,
            "table_record_count": 0
        }
        
        # 尝试获取MySQL数据库统计信息
        if self.db_pool and storage_results.get("storage_success"):
            try:
                async with self.db_pool.acquire() as conn:
                    async with conn.cursor() as cursor:
                        table_name = storage_results.get("table_name", "")

                        if table_name:
                            await cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                            result = await cursor.fetchone()
                            stats["table_record_count"] = result[0] if result else 0

                        # 获取数据库大小信息
                        try:
                            await cursor.execute("""
                                SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size in MB'
                                FROM information_schema.tables
                                WHERE table_schema = %s
                            """, (self.mysql_config.get("database", "hngpt"),))
                            result = await cursor.fetchone()
                            if result and result[0]:
                                stats["database_size"] = float(result[0]) * 1024 * 1024  # 转换为字节
                        except:
                            pass

            except Exception as e:
                print(f"获取MySQL数据库统计信息失败: {e}")
        
        return stats
    
    def _generate_final_report(self, storage_results: Dict[str, Any], context: BusinessAgentContext) -> Dict[str, Any]:
        """生成最终报告"""
        # 收集所有阶段的信息
        init_info = context.get_extracted_data("init_info", {})
        download_info = context.get_extracted_data("download_info", {})
        parse_info = context.get_extracted_data("parse_info", {})
        chunking_info = context.get_extracted_data("chunking_info", {})
        vectorization_info = context.get_extracted_data("vectorization_info", {})
        storage_info = context.get_extracted_data("storage_info", {})
        extraction_info = context.get_extracted_data("extraction_info", {})
        
        report = {
            "report_time": datetime.now().isoformat(),
            "task_summary": {
                "task_id": context.task_id,
                "project_name": context.project_name,
                "action": context.action,
                "workflow_status": "completed" if storage_results.get("storage_success") else "failed",
                "total_processing_time": self._calculate_total_processing_time(init_info)
            },
            "stage_summary": {
                "initialization": {"status": "completed", "progress": 10},
                "minio_download": {"status": "completed", "progress": 20, "files_downloaded": download_info.get("total_downloaded", 0)},
                "document_parse": {"status": "completed", "progress": 35, "files_parsed": parse_info.get("successful_parses", 0)},
                "text_chunking": {"status": "completed", "progress": 50, "chunks_created": chunking_info.get("total_chunks", 0)},
                "vector_embedding": {"status": "completed", "progress": 65, "vectors_generated": vectorization_info.get("successful_vectorizations", 0)},
                "elasticsearch_storage": {"status": "completed", "progress": 80, "documents_stored": storage_info.get("successful_storage", 0)},
                "llm_extraction": {"status": "completed", "progress": 90, "extractions_completed": extraction_info.get("successful_extractions", 0)},
                "database_storage": {"status": "completed" if storage_results.get("storage_success") else "failed", "progress": 100, "records_stored": storage_results.get("records_stored", 0)}
            },
            "final_results": {
                "structured_data_stored": storage_results.get("storage_success", False),
                "database_table": storage_results.get("table_name", "unknown"),
                "record_id": storage_results.get("record_id"),
                "searchable_documents": storage_info.get("searchable_documents", 0),
                "workflow_completed": True
            },
            "performance_metrics": {
                "files_processed": download_info.get("total_downloaded", 0),
                "text_chunks_created": chunking_info.get("total_chunks", 0),
                "vectors_generated": vectorization_info.get("successful_vectorizations", 0),
                "es_documents_stored": storage_info.get("successful_storage", 0),
                "database_records_stored": storage_results.get("records_stored", 0)
            },
            "recommendations": self._generate_recommendations(storage_results, context)
        }
        
        return report
    
    def _calculate_total_processing_time(self, init_info: Dict[str, Any]) -> float:
        """计算总处理时间"""
        try:
            start_time = init_info.get("initialization_time", "")
            if start_time:
                start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                end_dt = datetime.now()
                return (end_dt - start_dt).total_seconds()
        except:
            pass
        return 0.0
    
    def _generate_recommendations(self, storage_results: Dict[str, Any], context: BusinessAgentContext) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if storage_results.get("storage_success"):
            recommendations.append("✅ 文档处理工作流成功完成，结构化数据已存储到数据库")
            recommendations.append("📊 可以通过数据库查询或ES检索来访问处理后的数据")
            recommendations.append("🔍 建议定期备份数据库以确保数据安全")
        else:
            recommendations.append("❌ 数据库存储失败，请检查数据库连接和权限")
            recommendations.append("🔧 建议检查SQLite数据库路径和写入权限")
        
        # 根据处理结果添加具体建议
        extraction_info = context.get_extracted_data("extraction_info", {})
        if extraction_info.get("successful_extractions", 0) == 0:
            recommendations.append("⚠️ 未成功提取任何结构化信息，建议检查文档内容和LLM配置")
        
        vectorization_info = context.get_extracted_data("vectorization_info", {})
        if vectorization_info.get("successful_vectorizations", 0) == 0:
            recommendations.append("⚠️ 向量化失败，建议检查向量化模型配置")
        
        return recommendations
    
    async def _cleanup_resources(self, context: BusinessAgentContext):
        """清理资源"""
        try:
            # 关闭MySQL连接池
            if self.db_pool:
                self.db_pool.close()
                await self.db_pool.wait_closed()
                self.db_pool = None
            
            # 清理临时文件（可选）
            init_info = context.get_extracted_data("init_info", {})
            work_directory = init_info.get("work_directory", "")
            
            if work_directory and work_directory.startswith("/tmp/"):
                # 可以选择保留或清理临时文件
                print(f"工作目录保留: {work_directory}")
                # shutil.rmtree(work_directory)  # 如果需要清理
            
            print("✅ 资源清理完成")
            
        except Exception as e:
            print(f"⚠️ 资源清理失败: {e}")
    
    def get_stage_name(self) -> str:
        return "database_storage"
    
    def get_progress_percentage(self) -> int:
        return 100
    
    def get_stage_description(self) -> str:
        return "存储结构化信息到SQLite数据库"
