#!/usr/bin/env python3
"""
HNGPT工作流执行器
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from dataclasses import dataclass, field

try:
    from .hngpt_base_agent import HNGPTBaseAgent, WorkflowContext, AgentResult, AgentStatus
    from .hngpt_workflow_dag import HNGPTWorkflowDAG, WorkflowStatus, AgentNode
except ImportError:
    from agents.hngpt_base_agent import HNGPTBaseAgent, WorkflowContext, AgentResult, AgentStatus
    from agents.hngpt_workflow_dag import HNGPTWorkflowDAG, WorkflowStatus, AgentNode

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ExecutionConfig:
    """执行配置"""
    max_concurrent_agents: int = 10
    timeout_seconds: float = 3600.0  # 1小时
    retry_failed_agents: bool = True
    continue_on_failure: bool = False
    progress_callback: Optional[Callable] = None
    log_level: str = "INFO"

@dataclass
class ExecutionResult:
    """执行结果"""
    success: bool
    workflow_id: str
    status: WorkflowStatus
    results: Dict[str, AgentResult] = field(default_factory=dict)
    execution_time: float = 0.0
    start_time: str = ""
    end_time: str = ""
    error_message: str = ""
    statistics: Dict[str, Any] = field(default_factory=dict)

class HNGPTWorkflowExecutor:
    """HNGPT工作流执行器"""
    
    def __init__(self, dag: HNGPTWorkflowDAG, config: ExecutionConfig = None):
        self.dag = dag
        self.config = config or ExecutionConfig()
        self.semaphore = asyncio.Semaphore(self.config.max_concurrent_agents)
        self.execution_results: Dict[str, AgentResult] = {}
        self.completed_agents: List[str] = []
        self.failed_agents: List[str] = []
        self.skipped_agents: List[str] = []
        
        # 设置日志级别
        logger.setLevel(getattr(logging, self.config.log_level.upper()))
    
    async def execute(self, context: WorkflowContext) -> ExecutionResult:
        """执行工作流"""
        start_time = datetime.now()
        self.dag.status = WorkflowStatus.RUNNING
        self.dag.execution_stats["start_time"] = start_time.isoformat()
        
        logger.info(f"🚀 开始执行工作流: {self.dag.workflow_id}")
        
        result = ExecutionResult(
            success=False,
            workflow_id=self.dag.workflow_id,
            status=WorkflowStatus.RUNNING,
            start_time=start_time.isoformat()
        )
        
        try:
            # 构建执行层级
            execution_levels = self.dag.build_execution_levels()
            logger.info(f"📊 工作流包含 {len(execution_levels)} 个执行层级，总共 {len(self.dag.nodes)} 个Agent")
            
            # 逐层执行
            for level_idx, level in enumerate(execution_levels):
                logger.info(f"🔄 执行第 {level_idx + 1} 层，包含 {len(level)} 个Agent: {[self.dag.nodes[nid].agent.metadata.name for nid in level]}")
                
                # 并行执行同一层级的Agent
                level_results = await self._execute_level(level, context)
                
                # 处理层级结果
                level_success = True
                for node_id, agent_result in level_results.items():
                    self.execution_results[node_id] = agent_result
                    node = self.dag.nodes[node_id]
                    node.result = agent_result
                    
                    if agent_result.success:
                        node.status = AgentStatus.COMPLETED
                        self.completed_agents.append(node_id)
                        
                        # 将Agent输出合并到上下文
                        for key, value in agent_result.data.items():
                            context.set_data(key, value)
                        
                        logger.info(f"✅ Agent {node.agent.metadata.name} 执行成功")
                    else:
                        node.status = AgentStatus.FAILED
                        self.failed_agents.append(node_id)
                        level_success = False
                        
                        logger.error(f"❌ Agent {node.agent.metadata.name} 执行失败: {agent_result.errors}")
                
                # 检查是否继续执行
                if not level_success and not self.config.continue_on_failure:
                    logger.error(f"💥 第 {level_idx + 1} 层执行失败，停止工作流执行")
                    break
                
                # 进度回调
                if self.config.progress_callback:
                    progress = (level_idx + 1) / len(execution_levels) * 100
                    await self._safe_callback(self.config.progress_callback, {
                        "workflow_id": self.dag.workflow_id,
                        "progress": progress,
                        "current_level": level_idx + 1,
                        "total_levels": len(execution_levels),
                        "completed_agents": len(self.completed_agents),
                        "total_agents": len(self.dag.nodes)
                    })
            
            # 计算最终结果
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            success = len(self.failed_agents) == 0
            final_status = WorkflowStatus.COMPLETED if success else WorkflowStatus.FAILED
            
            # 更新DAG状态
            self.dag.status = final_status
            self.dag.execution_stats.update({
                "end_time": end_time.isoformat(),
                "total_execution_time": execution_time,
                "completed_nodes": len(self.completed_agents),
                "failed_nodes": len(self.failed_agents),
                "skipped_nodes": len(self.skipped_agents)
            })
            
            # 构建执行结果
            result.success = success
            result.status = final_status
            result.results = self.execution_results
            result.execution_time = execution_time
            result.end_time = end_time.isoformat()
            result.statistics = {
                "total_agents": len(self.dag.nodes),
                "completed_agents": len(self.completed_agents),
                "failed_agents": len(self.failed_agents),
                "skipped_agents": len(self.skipped_agents),
                "execution_levels": len(execution_levels),
                "average_agent_time": execution_time / len(self.dag.nodes) if self.dag.nodes else 0
            }
            
            if success:
                logger.info(f"🎉 工作流执行成功！耗时 {execution_time:.2f} 秒")
            else:
                result.error_message = f"工作流执行失败，{len(self.failed_agents)} 个Agent失败"
                logger.error(f"💥 工作流执行失败！{result.error_message}")
            
        except asyncio.TimeoutError:
            result.status = WorkflowStatus.FAILED
            result.error_message = f"工作流执行超时（{self.config.timeout_seconds} 秒）"
            logger.error(result.error_message)
            
        except Exception as e:
            result.status = WorkflowStatus.FAILED
            result.error_message = f"工作流执行异常: {str(e)}"
            logger.error(f"💥 工作流执行异常: {e}", exc_info=True)
            
        finally:
            if not result.end_time:
                result.end_time = datetime.now().isoformat()
                result.execution_time = (datetime.now() - start_time).total_seconds()
        
        return result
    
    async def _execute_level(self, level: List[str], context: WorkflowContext) -> Dict[str, AgentResult]:
        """执行一个层级的Agent"""
        tasks = []
        
        for node_id in level:
            node = self.dag.nodes[node_id]
            task = self._execute_agent_with_semaphore(node, context)
            tasks.append((node_id, task))
        
        # 并行执行所有任务
        results = {}
        completed_tasks = await asyncio.gather(
            *[task for _, task in tasks], 
            return_exceptions=True
        )
        
        # 处理结果
        for i, (node_id, _) in enumerate(tasks):
            result = completed_tasks[i]
            
            if isinstance(result, Exception):
                # 创建失败结果
                agent_result = AgentResult(
                    success=False,
                    status=AgentStatus.FAILED
                )
                agent_result.add_error("execution_exception", str(result))
                results[node_id] = agent_result
            else:
                results[node_id] = result
        
        return results
    
    async def _execute_agent_with_semaphore(self, node: AgentNode, context: WorkflowContext) -> AgentResult:
        """使用信号量控制并发执行Agent"""
        async with self.semaphore:
            return await self._execute_single_agent(node, context)
    
    async def _execute_single_agent(self, node: AgentNode, context: WorkflowContext) -> AgentResult:
        """执行单个Agent"""
        agent = node.agent
        logger.info(f"🤖 开始执行 Agent: {agent.metadata.name} ({node.node_id})")

        # 更新Agent状态为运行中
        if self.config.progress_callback:
            await self._safe_callback(self.config.progress_callback, {
                "current_agents": [{
                    "agent_id": node.node_id,
                    "agent_name": agent.metadata.name,
                    "status": AgentStatus.RUNNING.value,
                    "progress": 0,
                    "message": "开始执行",
                    "execution_time": 0.0
                }]
            })

        try:
            # 检查执行条件
            if node.condition and not self._evaluate_condition(node.condition, context):
                logger.info(f"⏭️ Agent {agent.metadata.name} 跳过执行（条件不满足）")
                result = AgentResult(
                    success=True,
                    status=AgentStatus.SKIPPED
                )
                result.metadata["skip_reason"] = "condition_not_met"
                self.skipped_agents.append(node.node_id)
                return result
            
            # 检查依赖
            missing_deps = [dep for dep in node.dependencies if dep not in self.completed_agents]
            if missing_deps:
                logger.error(f"❌ Agent {agent.metadata.name} 依赖未满足: {missing_deps}")
                result = AgentResult(
                    success=False,
                    status=AgentStatus.FAILED
                )
                result.add_error("dependency_not_met", f"Missing dependencies: {missing_deps}")
                return result
            
            # 执行Agent
            result = await agent.execute(context)

            # 更新Agent完成状态
            if self.config.progress_callback:
                await self._safe_callback(self.config.progress_callback, {
                    "current_agents": [{
                        "agent_id": node.node_id,
                        "agent_name": agent.metadata.name,
                        "status": AgentStatus.COMPLETED.value if result.success else AgentStatus.FAILED.value,
                        "progress": 100 if result.success else 0,
                        "message": "执行完成" if result.success else "执行失败",
                        "error": result.errors[0]["message"] if result.errors else None,
                        "execution_time": result.execution_time
                    }]
                })

            if result.success:
                logger.info(f"✅ Agent {agent.metadata.name} 执行成功，耗时 {result.execution_time:.2f} 秒")
            else:
                logger.error(f"❌ Agent {agent.metadata.name} 执行失败: {result.errors}")

            return result
            
        except Exception as e:
            logger.error(f"💥 Agent {agent.metadata.name} 执行异常: {e}", exc_info=True)
            result = AgentResult(
                success=False,
                status=AgentStatus.FAILED
            )
            result.add_error("execution_exception", str(e))
            return result
    
    def _evaluate_condition(self, condition: str, context: WorkflowContext) -> bool:
        """评估执行条件"""
        try:
            # 简单的条件评估（可以扩展为更复杂的表达式解析）
            # 例如: "document_type == 'project'"
            if "==" in condition:
                key, value = condition.split("==")
                key = key.strip()
                value = value.strip().strip("'\"")
                return context.get_data(key) == value
            elif "!=" in condition:
                key, value = condition.split("!=")
                key = key.strip()
                value = value.strip().strip("'\"")
                return context.get_data(key) != value
            elif condition.startswith("has_"):
                key = condition[4:]
                return context.has_data(key)
            else:
                # 默认检查数据是否存在且为真值
                return bool(context.get_data(condition))
        except Exception as e:
            logger.warning(f"⚠️ 条件评估失败: {condition}, 错误: {e}")
            return False
    
    async def _safe_callback(self, callback: Callable, data: Dict[str, Any]):
        """安全执行回调函数"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(data)
            else:
                callback(data)
        except Exception as e:
            logger.warning(f"⚠️ 进度回调执行失败: {e}")
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        return {
            "workflow_id": self.dag.workflow_id,
            "status": self.dag.status.value,
            "total_agents": len(self.dag.nodes),
            "completed_agents": len(self.completed_agents),
            "failed_agents": len(self.failed_agents),
            "skipped_agents": len(self.skipped_agents),
            "execution_stats": self.dag.execution_stats,
            "agent_results": {
                node_id: result.to_dict() 
                for node_id, result in self.execution_results.items()
            }
        }
