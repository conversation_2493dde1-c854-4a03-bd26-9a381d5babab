#!/usr/bin/env python3
"""
业务向量化Agent - 生成文本块的语义向量
"""

import asyncio
import os
import sys
import json
import numpy as np
from typing import Dict, Any, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, '/workspace/hngpt')

from .business_base_agent import BusinessBaseAgent, BusinessAgentContext

# 导入实际的OSentenceVector
try:
    from utils.sentence_vector import OSentenceVector
except ImportError:
    print("警告: 无法导入OSentenceVector，使用备用实现")
    
    class OSentenceVector:
        """备用的向量化实现"""
        def __init__(self):
            pass
        
        def encode(self, text: str) -> np.ndarray:
            """生成模拟向量"""
            # 基于文本内容生成确定性的模拟向量
            import hashlib
            text_hash = hashlib.md5(text.encode()).hexdigest()
            # 生成384维向量（常见的embedding维度）
            np.random.seed(int(text_hash[:8], 16))
            vector = np.random.normal(0, 1, 384)
            # 归一化
            vector = vector / np.linalg.norm(vector)
            return vector

class BusinessVectorEmbeddingAgent(BusinessBaseAgent):
    """业务向量化Agent"""
    
    def __init__(self):
        super().__init__(name="业务向量化Agent")
        self.add_dependency("chunking_info")  # 依赖文本切割Agent
        self.embedding_model = None
    
    async def process(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """生成文本块的语义向量"""
        await asyncio.sleep(0.6)  # 模拟向量化时间
        
        # 初始化向量化模型
        await self._initialize_embedding_model()
        
        # 获取切割信息
        chunking_info = context.get_extracted_data("chunking_info", {})
        chunking_results = context.get_extracted_data("chunking_results", [])
        
        # 生成向量
        vectorization_results = await self._vectorize_chunks(chunking_results)
        
        # 验证向量质量
        vector_validation = self._validate_vectors(vectorization_results)
        
        # 计算向量统计
        vector_statistics = self._calculate_vector_statistics(vectorization_results)
        
        # 保存向量到文件
        vector_files = await self._save_vectors_to_files(vectorization_results, context)
        
        # 生成向量索引
        vector_index = self._generate_vector_index(vectorization_results)
        
        return {
            "vectorization_info": {
                "vectorization_time": datetime.now().isoformat(),
                "total_chunks": vector_statistics["total_chunks"],
                "successful_vectorizations": vector_statistics["successful_vectorizations"],
                "vector_dimension": vector_statistics["vector_dimension"],
                "embedding_model": "OSentenceVector",
                "batch_size": 32
            },
            "vectorization_results": vectorization_results,
            "vector_validation": vector_validation,
            "vector_statistics": vector_statistics,
            "vector_files": vector_files,
            "vector_index": vector_index,
            "vectorization_summary": {
                "status": "completed",
                "vectorization_successful": vector_statistics["successful_vectorizations"] > 0,
                "vectors_ready_for_storage": vector_statistics["valid_vectors"],
                "next_stage": "elasticsearch_storage"
            }
        }
    
    async def _initialize_embedding_model(self):
        """初始化向量化模型"""
        if self.embedding_model is None:
            try:
                self.embedding_model = OSentenceVector()
                print("✅ OSentenceVector模型初始化成功")
            except Exception as e:
                print(f"⚠️ OSentenceVector初始化失败，使用备用模型: {e}")
                self.embedding_model = OSentenceVector()  # 使用备用实现
    
    async def _vectorize_chunks(self, chunking_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """向量化文本块"""
        vectorization_results = []
        
        for chunk_result in chunking_results:
            try:
                file_name = chunk_result.get("file_name", "unknown")
                chunks = chunk_result.get("chunks", [])
                
                if not chunk_result.get("chunking_success") or not chunks:
                    vectorization_results.append({
                        "file_name": file_name,
                        "vectorized_chunks": [],
                        "vectorization_success": False,
                        "error": "No chunks to vectorize",
                        "metadata": chunk_result.get("metadata", {})
                    })
                    continue
                
                # 批量向量化处理
                vectorized_chunks = await self._batch_vectorize(chunks)
                
                vectorization_results.append({
                    "file_name": file_name,
                    "vectorized_chunks": vectorized_chunks,
                    "vectorization_success": True,
                    "error": None,
                    "metadata": chunk_result.get("metadata", {}),
                    "original_chunk_count": len(chunks),
                    "vectorized_count": len(vectorized_chunks)
                })
                
            except Exception as e:
                vectorization_results.append({
                    "file_name": chunk_result.get("file_name", "unknown"),
                    "vectorized_chunks": [],
                    "vectorization_success": False,
                    "error": str(e),
                    "metadata": chunk_result.get("metadata", {})
                })
        
        return vectorization_results
    
    async def _batch_vectorize(self, chunks: List[Dict[str, Any]], batch_size: int = 32) -> List[Dict[str, Any]]:
        """批量向量化处理"""
        vectorized_chunks = []
        
        # 按批次处理
        for i in range(0, len(chunks), batch_size):
            batch_chunks = chunks[i:i + batch_size]
            
            for chunk in batch_chunks:
                try:
                    content = chunk.get("content", "")
                    if not content.strip():
                        continue
                    
                    # 生成向量
                    vector = self.embedding_model.encode(content)
                    
                    # 确保向量是numpy数组
                    if not isinstance(vector, np.ndarray):
                        vector = np.array(vector)
                    
                    vectorized_chunks.append({
                        "chunk_id": chunk.get("chunk_id"),
                        "chunk_index": chunk.get("chunk_index"),
                        "content": content,
                        "vector": vector,
                        "vector_dimension": len(vector),
                        "source_file": chunk.get("source_file"),
                        "metadata": {
                            **chunk.get("metadata", {}),
                            "vectorization_time": datetime.now().isoformat(),
                            "vector_model": "OSentenceVector"
                        }
                    })
                    
                except Exception as e:
                    print(f"向量化失败 {chunk.get('chunk_id', 'unknown')}: {e}")
                    continue
            
            # 批次间短暂休息
            if i + batch_size < len(chunks):
                await asyncio.sleep(0.1)
        
        return vectorized_chunks
    
    def _validate_vectors(self, vectorization_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证向量质量"""
        validation = {
            "total_files": len(vectorization_results),
            "successful_files": 0,
            "failed_files": 0,
            "total_vectors": 0,
            "valid_vectors": 0,
            "invalid_vectors": 0,
            "vector_dimensions": set(),
            "validation_details": []
        }
        
        for result in vectorization_results:
            if result["vectorization_success"]:
                validation["successful_files"] += 1
                vectorized_chunks = result["vectorized_chunks"]
                validation["total_vectors"] += len(vectorized_chunks)
                
                valid_vectors_in_file = 0
                invalid_vectors_in_file = 0
                
                for chunk in vectorized_chunks:
                    vector = chunk.get("vector")
                    
                    if isinstance(vector, np.ndarray) and vector.size > 0:
                        # 检查向量是否包含有效数值
                        if not np.isnan(vector).any() and not np.isinf(vector).any():
                            valid_vectors_in_file += 1
                            validation["valid_vectors"] += 1
                            validation["vector_dimensions"].add(len(vector))
                        else:
                            invalid_vectors_in_file += 1
                            validation["invalid_vectors"] += 1
                    else:
                        invalid_vectors_in_file += 1
                        validation["invalid_vectors"] += 1
                
                validation["validation_details"].append({
                    "file_name": result["file_name"],
                    "status": "success",
                    "total_chunks": result.get("original_chunk_count", 0),
                    "vectorized_chunks": result.get("vectorized_count", 0),
                    "valid_vectors": valid_vectors_in_file,
                    "invalid_vectors": invalid_vectors_in_file
                })
            else:
                validation["failed_files"] += 1
                validation["validation_details"].append({
                    "file_name": result["file_name"],
                    "status": "failed",
                    "error": result.get("error", "unknown")
                })
        
        # 转换set为list以便JSON序列化
        validation["vector_dimensions"] = list(validation["vector_dimensions"])
        
        return validation
    
    def _calculate_vector_statistics(self, vectorization_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算向量统计"""
        stats = {
            "total_files": len(vectorization_results),
            "successful_files": 0,
            "failed_files": 0,
            "total_chunks": 0,
            "successful_vectorizations": 0,
            "failed_vectorizations": 0,
            "vector_dimension": 0,
            "valid_vectors": 0,
            "average_vector_norm": 0.0,
            "vector_quality_score": 0.0
        }
        
        all_norms = []
        
        for result in vectorization_results:
            if result["vectorization_success"]:
                stats["successful_files"] += 1
                vectorized_chunks = result["vectorized_chunks"]
                stats["total_chunks"] += result.get("original_chunk_count", 0)
                stats["successful_vectorizations"] += len(vectorized_chunks)
                
                for chunk in vectorized_chunks:
                    vector = chunk.get("vector")
                    if isinstance(vector, np.ndarray) and vector.size > 0:
                        if not np.isnan(vector).any() and not np.isinf(vector).any():
                            stats["valid_vectors"] += 1
                            if stats["vector_dimension"] == 0:
                                stats["vector_dimension"] = len(vector)
                            
                            # 计算向量范数
                            norm = np.linalg.norm(vector)
                            all_norms.append(norm)
            else:
                stats["failed_files"] += 1
                stats["failed_vectorizations"] += result.get("original_chunk_count", 0)
        
        # 计算平均向量范数
        if all_norms:
            stats["average_vector_norm"] = sum(all_norms) / len(all_norms)
            
            # 计算质量分数（基于有效向量比例和范数分布）
            valid_ratio = stats["valid_vectors"] / max(1, stats["total_chunks"])
            norm_consistency = 1.0 - (np.std(all_norms) / max(np.mean(all_norms), 0.001))
            stats["vector_quality_score"] = (valid_ratio + norm_consistency) / 2
        
        return stats
    
    async def _save_vectors_to_files(self, vectorization_results: List[Dict[str, Any]], context: BusinessAgentContext) -> Dict[str, Any]:
        """保存向量到文件"""
        init_info = context.get_extracted_data("init_info", {})
        work_directory = init_info.get("work_directory", "/tmp")
        vectors_dir = os.path.join(work_directory, "vectors")
        
        os.makedirs(vectors_dir, exist_ok=True)
        
        saved_files = {
            "vectors_directory": vectors_dir,
            "saved_files": [],
            "total_files": 0,
            "total_vectors": 0,
            "total_size": 0
        }
        
        for result in vectorization_results:
            if result["vectorization_success"] and result["vectorized_chunks"]:
                file_name = result["file_name"]
                safe_file_name = "".join(c for c in file_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                
                # 保存向量数据（numpy格式）
                vectors_file = os.path.join(vectors_dir, f"{safe_file_name}_vectors.npz")
                metadata_file = os.path.join(vectors_dir, f"{safe_file_name}_metadata.json")
                
                try:
                    # 准备向量数据
                    vectors = []
                    metadata_list = []
                    
                    for chunk in result["vectorized_chunks"]:
                        vectors.append(chunk["vector"])
                        metadata_list.append({
                            "chunk_id": chunk["chunk_id"],
                            "chunk_index": chunk["chunk_index"],
                            "content": chunk["content"],
                            "source_file": chunk["source_file"],
                            "metadata": chunk["metadata"]
                        })
                    
                    # 保存向量
                    if vectors:
                        np.savez_compressed(vectors_file, vectors=np.array(vectors))
                    
                    # 保存元数据
                    with open(metadata_file, 'w', encoding='utf-8') as f:
                        json.dump({
                            "file_name": file_name,
                            "vector_count": len(vectors),
                            "vector_dimension": len(vectors[0]) if vectors else 0,
                            "vectorization_time": datetime.now().isoformat(),
                            "chunks": metadata_list
                        }, f, ensure_ascii=False, indent=2)
                    
                    # 计算文件大小
                    vectors_size = os.path.getsize(vectors_file) if os.path.exists(vectors_file) else 0
                    metadata_size = os.path.getsize(metadata_file) if os.path.exists(metadata_file) else 0
                    total_size = vectors_size + metadata_size
                    
                    saved_files["saved_files"].append({
                        "original_file": file_name,
                        "vectors_file": vectors_file,
                        "metadata_file": metadata_file,
                        "vector_count": len(vectors),
                        "file_size": total_size
                    })
                    
                    saved_files["total_files"] += 1
                    saved_files["total_vectors"] += len(vectors)
                    saved_files["total_size"] += total_size
                    
                except Exception as e:
                    print(f"保存向量文件失败 {vectors_file}: {e}")
        
        return saved_files
    
    def _generate_vector_index(self, vectorization_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成向量索引"""
        index = {
            "index_time": datetime.now().isoformat(),
            "total_vectors": 0,
            "vector_dimension": 0,
            "files": [],
            "chunk_mapping": {}
        }
        
        for result in vectorization_results:
            if result["vectorization_success"]:
                vectorized_chunks = result["vectorized_chunks"]
                
                if vectorized_chunks:
                    # 更新索引信息
                    index["total_vectors"] += len(vectorized_chunks)
                    if index["vector_dimension"] == 0:
                        index["vector_dimension"] = vectorized_chunks[0].get("vector_dimension", 0)
                    
                    # 添加文件信息
                    index["files"].append({
                        "file_name": result["file_name"],
                        "vector_count": len(vectorized_chunks),
                        "chunk_ids": [chunk["chunk_id"] for chunk in vectorized_chunks]
                    })
                    
                    # 添加chunk映射
                    for chunk in vectorized_chunks:
                        index["chunk_mapping"][chunk["chunk_id"]] = {
                            "file_name": result["file_name"],
                            "chunk_index": chunk["chunk_index"],
                            "content_preview": chunk["content"][:100] + "..." if len(chunk["content"]) > 100 else chunk["content"]
                        }
        
        return index
    
    def get_stage_name(self) -> str:
        return "vector_embedding"
    
    def get_progress_percentage(self) -> int:
        return 65
    
    def get_stage_description(self) -> str:
        return "生成文本块的语义向量"
