#!/usr/bin/env python3
"""
信息合并Agent - 负责合并所有提取的信息，生成文档摘要和统计信息
"""

import asyncio
import json
from typing import Dict, Any, List
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class MergeInfoAgent(BaseAgent):
    """信息合并Agent - 整合所有提取的信息生成最终结果"""
    
    def __init__(self):
        super().__init__(name="信息合并Agent")
        self.add_dependency("extract_relations")  # 依赖关系信息提取Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """合并所有提取的信息"""
        await asyncio.sleep(0.9)  # 模拟合并时间
        
        # 获取所有阶段的信息
        basic_info = context.get_extracted_data("basic_info", {})
        structure_info = context.get_extracted_data("structure_info", {})
        entity_info = context.get_extracted_data("entity_info", {})
        relation_info = context.get_extracted_data("relation_info", {})
        
        # 合并文档元信息
        document_metadata = self._merge_document_metadata(context)
        
        # 合并内容信息
        content_summary = self._merge_content_summary(context)
        
        # 合并结构信息
        structure_summary = self._merge_structure_summary(context)
        
        # 合并实体信息
        entity_summary = self._merge_entity_summary(context)
        
        # 合并关系信息
        relation_summary = self._merge_relation_summary(context)
        
        # 生成文档摘要
        document_abstract = self._generate_document_abstract(context)
        
        # 生成关键信息
        key_information = self._extract_key_information(context)
        
        # 计算质量指标
        quality_metrics = self._calculate_quality_metrics(context)
        
        # 生成处理统计
        processing_statistics = self._generate_processing_statistics(context)
        
        # 构建最终的合并结果
        merged_result = {
            "document_id": context.metadata.get("doc_id", ""),
            "processing_time": datetime.now().isoformat(),
            "document_metadata": document_metadata,
            "content_summary": content_summary,
            "structure_summary": structure_summary,
            "entity_summary": entity_summary,
            "relation_summary": relation_summary,
            "document_abstract": document_abstract,
            "key_information": key_information,
            "quality_metrics": quality_metrics,
            "processing_statistics": processing_statistics
        }
        
        return {
            "merge_info": {
                "merge_time": datetime.now().isoformat(),
                "total_data_points": self._count_data_points(context),
                "merge_success": True,
                "data_completeness": quality_metrics.get("completeness_score", 0.0),
                "information_richness": quality_metrics.get("information_richness", 0.0)
            },
            "merged_result": merged_result,
            "merge_summary": {
                "status": "completed",
                "merge_successful": True,
                "data_integrity": quality_metrics.get("data_integrity", 0.0),
                "final_quality_score": quality_metrics.get("overall_quality", 0.0),
                "next_stage": "validate_result"
            }
        }
    
    def _merge_document_metadata(self, context: AgentContext) -> Dict[str, Any]:
        """合并文档元信息"""
        basic_info = context.get_extracted_data("basic_info", {})
        parse_info = context.get_extracted_data("parse_info", {})
        file_metadata = context.get_extracted_data("file_metadata", {})
        
        metadata = {
            "title": basic_info.get("title", "未知标题"),
            "author": basic_info.get("author", "未知作者"),
            "creation_date": basic_info.get("creation_date", ""),
            "document_type": basic_info.get("document_type", "未知类型"),
            "language": basic_info.get("language", "zh-CN"),
            "page_count": basic_info.get("page_count", 0),
            "word_count": basic_info.get("word_count", 0),
            "file_size": file_metadata.get("estimated_words", 0),
            "processing_date": datetime.now().isoformat(),
            "source_url": context.file_url,
            "document_version": "1.0"
        }
        
        # 添加额外的元信息
        if file_metadata.get("has_metadata"):
            metadata["has_original_metadata"] = True
            metadata["original_metadata"] = file_metadata.get("file_info", {})
        
        return metadata
    
    def _merge_content_summary(self, context: AgentContext) -> Dict[str, Any]:
        """合并内容摘要"""
        text_statistics = context.get_extracted_data("text_statistics", {})
        content_classification = context.get_extracted_data("content_classification", {})
        summary_info = context.get_extracted_data("summary_info", {})
        
        content_summary = {
            "abstract": summary_info.get("abstract", ""),
            "main_topics": summary_info.get("main_topics", []),
            "key_points": summary_info.get("key_points", []),
            "content_type": content_classification.get("document_type", "未知"),
            "technical_level": content_classification.get("technical_level", "medium"),
            "readability": {
                "total_characters": text_statistics.get("total_characters", 0),
                "total_words": text_statistics.get("total_words", 0),
                "total_sentences": text_statistics.get("total_sentences", 0),
                "total_paragraphs": text_statistics.get("total_paragraphs", 0),
                "average_page_length": text_statistics.get("average_page_length", 0),
                "language_detected": text_statistics.get("language_detected", "zh-CN")
            }
        }
        
        return content_summary
    
    def _merge_structure_summary(self, context: AgentContext) -> Dict[str, Any]:
        """合并结构信息"""
        document_structure = context.get_extracted_data("document_structure", {})
        chapter_structure = context.get_extracted_data("chapter_structure", {})
        section_structure = context.get_extracted_data("section_structure", {})
        list_structure = context.get_extracted_data("list_structure", {})
        document_outline = context.get_extracted_data("document_outline", {})
        content_hierarchy = context.get_extracted_data("content_hierarchy", {})
        
        structure_summary = {
            "structure_type": document_structure.get("structure_type", "unknown"),
            "hierarchy_depth": content_hierarchy.get("max_depth", 1),
            "chapter_count": chapter_structure.get("chapter_count", 0),
            "section_count": section_structure.get("section_count", 0),
            "list_count": list_structure.get("list_count", 0),
            "outline": document_outline.get("outline", []),
            "structure_complexity": content_hierarchy.get("hierarchy_type", "simple"),
            "numbering_styles": {
                "chapter_style": chapter_structure.get("numbering_style", "unknown"),
                "section_style": section_structure.get("numbering_style", "unknown")
            },
            "list_types": list_structure.get("list_types", [])
        }
        
        return structure_summary
    
    def _merge_entity_summary(self, context: AgentContext) -> Dict[str, Any]:
        """合并实体信息"""
        person_entities = context.get_extracted_data("person_entities", {})
        organization_entities = context.get_extracted_data("organization_entities", {})
        location_entities = context.get_extracted_data("location_entities", {})
        date_entities = context.get_extracted_data("date_entities", {})
        number_entities = context.get_extracted_data("number_entities", {})
        keyword_entities = context.get_extracted_data("keyword_entities", {})
        technical_entities = context.get_extracted_data("technical_entities", {})
        important_entities = context.get_extracted_data("important_entities", {})
        
        entity_summary = {
            "total_entities": (
                person_entities.get("count", 0) +
                organization_entities.get("count", 0) +
                location_entities.get("count", 0) +
                date_entities.get("count", 0) +
                number_entities.get("count", 0) +
                keyword_entities.get("count", 0) +
                technical_entities.get("count", 0)
            ),
            "entity_counts": {
                "persons": person_entities.get("count", 0),
                "organizations": organization_entities.get("count", 0),
                "locations": location_entities.get("count", 0),
                "dates": date_entities.get("count", 0),
                "numbers": number_entities.get("count", 0),
                "keywords": keyword_entities.get("count", 0),
                "technical_terms": technical_entities.get("count", 0)
            },
            "important_entities": {
                "high_confidence": important_entities.get("high_confidence", []),
                "high_frequency": important_entities.get("high_frequency", []),
                "contextually_important": important_entities.get("contextually_important", [])
            },
            "entity_diversity": len([c for c in [
                person_entities.get("count", 0),
                organization_entities.get("count", 0),
                location_entities.get("count", 0),
                date_entities.get("count", 0),
                number_entities.get("count", 0),
                keyword_entities.get("count", 0),
                technical_entities.get("count", 0)
            ] if c > 0])
        }
        
        return entity_summary
    
    def _merge_relation_summary(self, context: AgentContext) -> Dict[str, Any]:
        """合并关系信息"""
        employment_relations = context.get_extracted_data("employment_relations", {})
        location_relations = context.get_extracted_data("location_relations", {})
        hierarchical_relations = context.get_extracted_data("hierarchical_relations", {})
        collaboration_relations = context.get_extracted_data("collaboration_relations", {})
        temporal_relations = context.get_extracted_data("temporal_relations", {})
        semantic_relations = context.get_extracted_data("semantic_relations", {})
        dependency_relations = context.get_extracted_data("dependency_relations", {})
        relation_network = context.get_extracted_data("relation_network", {})
        
        relation_summary = {
            "total_relations": (
                employment_relations.get("count", 0) +
                location_relations.get("count", 0) +
                hierarchical_relations.get("count", 0) +
                collaboration_relations.get("count", 0) +
                temporal_relations.get("count", 0) +
                semantic_relations.get("count", 0) +
                dependency_relations.get("count", 0)
            ),
            "relation_counts": {
                "employment": employment_relations.get("count", 0),
                "location": location_relations.get("count", 0),
                "hierarchical": hierarchical_relations.get("count", 0),
                "collaboration": collaboration_relations.get("count", 0),
                "temporal": temporal_relations.get("count", 0),
                "semantic": semantic_relations.get("count", 0),
                "dependency": dependency_relations.get("count", 0)
            },
            "network_analysis": {
                "node_count": relation_network.get("node_count", 0),
                "edge_count": relation_network.get("edge_count", 0),
                "density": relation_network.get("density", 0.0),
                "complexity": relation_network.get("complexity", "low")
            },
            "relation_diversity": len([c for c in [
                employment_relations.get("count", 0),
                location_relations.get("count", 0),
                hierarchical_relations.get("count", 0),
                collaboration_relations.get("count", 0),
                temporal_relations.get("count", 0),
                semantic_relations.get("count", 0),
                dependency_relations.get("count", 0)
            ] if c > 0])
        }
        
        return relation_summary
    
    def _generate_document_abstract(self, context: AgentContext) -> Dict[str, Any]:
        """生成文档摘要"""
        basic_info = context.get_extracted_data("basic_info", {})
        summary_info = context.get_extracted_data("summary_info", {})
        entity_summary = self._merge_entity_summary(context)
        structure_summary = self._merge_structure_summary(context)
        
        # 生成自动摘要
        auto_abstract = []
        
        # 基本信息
        title = basic_info.get("title", "未知文档")
        doc_type = basic_info.get("document_type", "文档")
        author = basic_info.get("author", "未知作者")
        
        auto_abstract.append(f"本{doc_type}《{title}》由{author}编写。")
        
        # 结构信息
        if structure_summary["chapter_count"] > 0:
            auto_abstract.append(f"文档共包含{structure_summary['chapter_count']}个章节。")
        
        if structure_summary["section_count"] > 0:
            auto_abstract.append(f"包含{structure_summary['section_count']}个小节。")
        
        # 实体信息
        if entity_summary["entity_counts"]["persons"] > 0:
            auto_abstract.append(f"涉及{entity_summary['entity_counts']['persons']}个人员。")
        
        if entity_summary["entity_counts"]["organizations"] > 0:
            auto_abstract.append(f"涉及{entity_summary['entity_counts']['organizations']}个机构。")
        
        abstract = {
            "original_abstract": summary_info.get("abstract", ""),
            "auto_generated_abstract": "".join(auto_abstract),
            "key_topics": summary_info.get("main_topics", [])[:5],
            "document_scope": self._determine_document_scope(context),
            "target_audience": self._determine_target_audience(context),
            "abstract_quality": "high" if summary_info.get("summary_available") else "generated"
        }
        
        return abstract
    
    def _extract_key_information(self, context: AgentContext) -> Dict[str, Any]:
        """提取关键信息"""
        important_entities = context.get_extracted_data("important_entities", {})
        relation_statistics = context.get_extracted_data("relation_statistics", {})
        
        key_info = {
            "key_persons": [e.get("name", "") for e in important_entities.get("high_confidence", []) 
                          if e.get("type") == "person"][:5],
            "key_organizations": [e.get("name", "") for e in important_entities.get("high_confidence", []) 
                                if e.get("type") == "organization"][:5],
            "key_concepts": [e.get("name", "") for e in important_entities.get("high_frequency", [])
                           if e.get("frequency", 0) >= 3][:10],
            "important_dates": [e.get("date", "") for e in context.get_extracted_data("date_entities", {}).get("entities", [])][:5],
            "key_numbers": [e.get("number", "") for e in context.get_extracted_data("number_entities", {}).get("entities", [])][:5],
            "critical_relations": relation_statistics.get("high_confidence_count", 0),
            "information_density": self._calculate_information_density(context)
        }
        
        return key_info
    
    def _calculate_quality_metrics(self, context: AgentContext) -> Dict[str, Any]:
        """计算质量指标"""
        validation_report = context.get_extracted_data("validation_report", {})
        entity_info = context.get_extracted_data("entity_info", {})
        relation_info = context.get_extracted_data("relation_info", {})
        
        metrics = {
            "data_integrity": validation_report.get("validation_score", 0.0),
            "completeness_score": self._calculate_completeness_score(context),
            "information_richness": self._calculate_information_richness(context),
            "extraction_confidence": (
                entity_info.get("extraction_confidence", 0.0) * 0.5 +
                relation_info.get("average_confidence", 0.0) * 0.5
            ),
            "structure_quality": self._assess_structure_quality(context),
            "content_coherence": self._assess_content_coherence(context),
            "overall_quality": 0.0
        }
        
        # 计算总体质量分数
        metrics["overall_quality"] = (
            metrics["data_integrity"] * 0.2 +
            metrics["completeness_score"] * 0.2 +
            metrics["information_richness"] * 0.2 +
            metrics["extraction_confidence"] * 0.2 +
            metrics["structure_quality"] * 0.1 +
            metrics["content_coherence"] * 0.1
        )
        
        return metrics
    
    def _generate_processing_statistics(self, context: AgentContext) -> Dict[str, Any]:
        """生成处理统计信息"""
        stats = {
            "processing_stages": 9,  # 当前已完成的阶段数
            "total_processing_time": "估算",
            "data_points_extracted": self._count_data_points(context),
            "extraction_success_rate": self._calculate_success_rate(context),
            "agent_performance": {
                "init_agent": {"status": "completed", "confidence": 1.0},
                "download_agent": {"status": "completed", "confidence": 0.9},
                "validate_agent": {"status": "completed", "confidence": 0.8},
                "parse_agent": {"status": "completed", "confidence": 0.8},
                "basic_extract_agent": {"status": "completed", "confidence": 0.7},
                "structure_extract_agent": {"status": "completed", "confidence": 0.7},
                "entity_extract_agent": {"status": "completed", "confidence": 0.6},
                "relation_extract_agent": {"status": "completed", "confidence": 0.6},
                "merge_info_agent": {"status": "completed", "confidence": 0.8}
            },
            "memory_usage": "适中",
            "processing_efficiency": "良好"
        }
        
        return stats
    
    def _count_data_points(self, context: AgentContext) -> int:
        """计算数据点总数"""
        count = 0
        
        # 实体数据点
        entity_types = ["person_entities", "organization_entities", "location_entities", 
                       "date_entities", "number_entities", "keyword_entities", "technical_entities"]
        for entity_type in entity_types:
            entities = context.get_extracted_data(entity_type, {})
            count += entities.get("count", 0)
        
        # 关系数据点
        relation_types = ["employment_relations", "location_relations", "hierarchical_relations",
                         "collaboration_relations", "temporal_relations", "semantic_relations", "dependency_relations"]
        for relation_type in relation_types:
            relations = context.get_extracted_data(relation_type, {})
            count += relations.get("count", 0)
        
        # 结构数据点
        structure_info = context.get_extracted_data("structure_info", {})
        count += structure_info.get("total_chapters", 0)
        count += structure_info.get("total_sections", 0)
        count += structure_info.get("total_lists", 0)
        
        return count
    
    def _calculate_completeness_score(self, context: AgentContext) -> float:
        """计算完整性分数"""
        basic_info = context.get_extracted_data("basic_info", {})
        
        # 检查基本信息完整性
        required_fields = ["title", "author", "document_type", "creation_date"]
        completed_fields = sum(1 for field in required_fields if basic_info.get(field))
        
        basic_completeness = completed_fields / len(required_fields)
        
        # 检查内容完整性
        entity_info = context.get_extracted_data("entity_info", {})
        relation_info = context.get_extracted_data("relation_info", {})
        
        content_completeness = min(1.0, (
            entity_info.get("total_entities", 0) / 10 +  # 期望至少10个实体
            relation_info.get("total_relations", 0) / 5   # 期望至少5个关系
        ) / 2)
        
        return (basic_completeness + content_completeness) / 2
    
    def _calculate_information_richness(self, context: AgentContext) -> float:
        """计算信息丰富度"""
        entity_info = context.get_extracted_data("entity_info", {})
        relation_info = context.get_extracted_data("relation_info", {})
        structure_info = context.get_extracted_data("structure_info", {})
        
        # 实体多样性
        entity_diversity = entity_info.get("entity_types", 0) / 7  # 最多7种实体类型
        
        # 关系多样性
        relation_diversity = relation_info.get("relation_types", 0) / 7  # 最多7种关系类型
        
        # 结构复杂度
        structure_complexity = min(1.0, structure_info.get("total_chapters", 0) / 5)  # 期望最多5章
        
        return (entity_diversity + relation_diversity + structure_complexity) / 3
    
    def _assess_structure_quality(self, context: AgentContext) -> float:
        """评估结构质量"""
        content_hierarchy = context.get_extracted_data("content_hierarchy", {})
        document_outline = context.get_extracted_data("document_outline", {})
        
        # 层次深度合理性
        depth_score = min(1.0, content_hierarchy.get("max_depth", 1) / 3)  # 期望最多3层
        
        # 大纲完整性
        outline_score = 1.0 if len(document_outline.get("outline", [])) > 0 else 0.0
        
        return (depth_score + outline_score) / 2
    
    def _assess_content_coherence(self, context: AgentContext) -> float:
        """评估内容连贯性"""
        relation_network = context.get_extracted_data("relation_network", {})
        
        # 基于关系网络密度评估连贯性
        density = relation_network.get("density", 0.0)
        
        # 密度适中表示连贯性好
        if 0.1 <= density <= 0.5:
            return 1.0
        elif density < 0.1:
            return density * 10  # 密度太低
        else:
            return max(0.0, 1.0 - (density - 0.5) * 2)  # 密度太高
    
    def _calculate_success_rate(self, context: AgentContext) -> float:
        """计算提取成功率"""
        # 简化的成功率计算
        validation_report = context.get_extracted_data("validation_report", {})
        entity_info = context.get_extracted_data("entity_info", {})
        relation_info = context.get_extracted_data("relation_info", {})
        
        validation_success = 1.0 if validation_report.get("overall_valid") else 0.0
        entity_success = 1.0 if entity_info.get("total_entities", 0) > 0 else 0.0
        relation_success = 1.0 if relation_info.get("total_relations", 0) > 0 else 0.0
        
        return (validation_success + entity_success + relation_success) / 3
    
    def _determine_document_scope(self, context: AgentContext) -> str:
        """确定文档范围"""
        basic_info = context.get_extracted_data("basic_info", {})
        word_count = basic_info.get("word_count", 0)
        
        if word_count > 10000:
            return "comprehensive"
        elif word_count > 3000:
            return "detailed"
        elif word_count > 1000:
            return "moderate"
        else:
            return "brief"
    
    def _determine_target_audience(self, context: AgentContext) -> str:
        """确定目标受众"""
        content_classification = context.get_extracted_data("content_classification", {})
        technical_level = content_classification.get("technical_level", "medium")
        
        if technical_level == "high":
            return "technical_professionals"
        elif technical_level == "medium":
            return "general_professionals"
        else:
            return "general_public"
    
    def _calculate_information_density(self, context: AgentContext) -> float:
        """计算信息密度"""
        total_entities = context.get_extracted_data("entity_info", {}).get("total_entities", 0)
        total_relations = context.get_extracted_data("relation_info", {}).get("total_relations", 0)
        word_count = context.get_extracted_data("basic_info", {}).get("word_count", 1)
        
        return (total_entities + total_relations) / word_count * 1000  # 每千字的信息点数
    
    def get_stage_name(self) -> str:
        return "merge_info"
    
    def get_progress_percentage(self) -> int:
        return 90
    
    def get_stage_description(self) -> str:
        return "合并所有提取信息，生成文档摘要和统计"
