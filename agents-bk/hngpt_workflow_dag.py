#!/usr/bin/env python3
"""
HNGPT工作流DAG构建器
"""

import asyncio
import uuid
from typing import Dict, Any, List, Optional, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json

try:
    from .hngpt_base_agent import HNGPTBaseAgent, WorkflowContext, AgentResult, AgentStatus
except ImportError:
    from agents.hngpt_base_agent import HNGPTBaseAgent, WorkflowContext, AgentResult, AgentStatus

class WorkflowStatus(Enum):
    """工作流状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

@dataclass
class AgentNode:
    """Agent节点定义"""
    node_id: str
    agent: HNGPTBaseAgent
    dependencies: List[str] = field(default_factory=list)
    parallel_group: Optional[str] = None
    condition: Optional[str] = None
    status: AgentStatus = AgentStatus.PENDING
    result: Optional[AgentResult] = None
    level: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "node_id": self.node_id,
            "agent_info": self.agent.get_info(),
            "dependencies": self.dependencies,
            "parallel_group": self.parallel_group,
            "condition": self.condition,
            "status": self.status.value,
            "level": self.level,
            "result": self.result.to_dict() if self.result else None
        }

class HNGPTWorkflowDAG:
    """HNGPT工作流DAG构建器"""
    
    def __init__(self, workflow_id: str = None, name: str = ""):
        self.workflow_id = workflow_id or f"workflow_{uuid.uuid4().hex[:8]}"
        self.name = name or self.workflow_id
        self.nodes: Dict[str, AgentNode] = {}
        self.edges: List[Tuple[str, str]] = []
        self.execution_levels: List[List[str]] = []
        self.parallel_groups: Dict[str, List[str]] = {}
        self.status = WorkflowStatus.PENDING
        self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()
        
        # 执行统计
        self.execution_stats = {
            "total_nodes": 0,
            "completed_nodes": 0,
            "failed_nodes": 0,
            "skipped_nodes": 0,
            "total_execution_time": 0.0,
            "start_time": None,
            "end_time": None
        }
    
    def add_agent(self, 
                  agent: HNGPTBaseAgent, 
                  dependencies: List[str] = None,
                  parallel_group: str = None,
                  condition: str = None) -> str:
        """
        添加Agent节点
        
        Args:
            agent: Agent实例
            dependencies: 依赖的节点ID列表
            parallel_group: 并行组标识
            condition: 执行条件
            
        Returns:
            str: 节点ID
        """
        node_id = agent.agent_id
        
        # 验证依赖是否存在
        if dependencies:
            missing_deps = [dep for dep in dependencies if dep not in self.nodes]
            if missing_deps:
                raise ValueError(f"Dependencies not found: {missing_deps}")
        
        # 使用Agent自身的并行组设置（如果未指定）
        if parallel_group is None:
            parallel_group = agent.parallel_group
        
        node = AgentNode(
            node_id=node_id,
            agent=agent,
            dependencies=dependencies or [],
            parallel_group=parallel_group,
            condition=condition
        )
        
        self.nodes[node_id] = node
        
        # 添加边
        for dep in (dependencies or []):
            self.edges.append((dep, node_id))
        
        # 管理并行组
        if parallel_group:
            if parallel_group not in self.parallel_groups:
                self.parallel_groups[parallel_group] = []
            self.parallel_groups[parallel_group].append(node_id)
        
        self.updated_at = datetime.now().isoformat()
        return node_id
    
    def remove_agent(self, node_id: str) -> bool:
        """移除Agent节点"""
        if node_id not in self.nodes:
            return False
        
        # 移除节点
        node = self.nodes.pop(node_id)
        
        # 移除相关的边
        self.edges = [(f, t) for f, t in self.edges if f != node_id and t != node_id]
        
        # 从并行组中移除
        if node.parallel_group and node.parallel_group in self.parallel_groups:
            if node_id in self.parallel_groups[node.parallel_group]:
                self.parallel_groups[node.parallel_group].remove(node_id)
            if not self.parallel_groups[node.parallel_group]:
                del self.parallel_groups[node.parallel_group]
        
        self.updated_at = datetime.now().isoformat()
        return True
    
    def build_execution_levels(self) -> List[List[str]]:
        """构建执行层级（拓扑排序 + 并行分组）"""
        if not self.nodes:
            return []
        
        # 计算入度
        in_degree = {node_id: 0 for node_id in self.nodes}
        for from_node, to_node in self.edges:
            in_degree[to_node] += 1
        
        execution_levels = []
        remaining_nodes = set(self.nodes.keys())
        level = 0
        
        while remaining_nodes:
            # 找到当前可执行的节点（入度为0）
            ready_nodes = [node_id for node_id in remaining_nodes if in_degree[node_id] == 0]
            
            if not ready_nodes:
                # 检测到循环依赖
                raise ValueError(f"Circular dependency detected in workflow {self.workflow_id}")
            
            # 按并行组分组
            level_groups = {}
            ungrouped_nodes = []
            
            for node_id in ready_nodes:
                node = self.nodes[node_id]
                node.level = level
                
                if node.parallel_group:
                    if node.parallel_group not in level_groups:
                        level_groups[node.parallel_group] = []
                    level_groups[node.parallel_group].append(node_id)
                else:
                    ungrouped_nodes.append(node_id)
            
            # 添加并行组到执行级别
            for group_nodes in level_groups.values():
                execution_levels.append(group_nodes)
            
            # 添加未分组的节点（每个节点单独一个级别）
            for node_id in ungrouped_nodes:
                execution_levels.append([node_id])
            
            # 更新入度
            for node_id in ready_nodes:
                remaining_nodes.remove(node_id)
                for from_node, to_node in self.edges:
                    if from_node == node_id and to_node in remaining_nodes:
                        in_degree[to_node] -= 1
            
            level += 1
        
        self.execution_levels = execution_levels
        return execution_levels
    
    def validate_workflow(self) -> Dict[str, Any]:
        """验证工作流"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "statistics": {}
        }
        
        try:
            # 检查循环依赖
            self.build_execution_levels()
            
            # 检查数据依赖
            for node_id, node in self.nodes.items():
                agent = node.agent
                
                # 检查输入数据是否能被满足
                for input_schema in agent.input_schemas:
                    if input_schema.required:
                        # 检查是否有前置Agent能提供这个数据
                        providers = []
                        for dep_id in node.dependencies:
                            dep_agent = self.nodes[dep_id].agent
                            for output_schema in dep_agent.output_schemas:
                                if output_schema.name == input_schema.name:
                                    providers.append(dep_id)
                        
                        if not providers and input_schema.name not in ["task_id", "workflow_id"]:
                            validation_result["warnings"].append({
                                "type": "missing_data_provider",
                                "message": f"Agent {agent.metadata.name} requires input '{input_schema.name}' but no dependency provides it",
                                "node_id": node_id
                            })
            
            # 统计信息
            validation_result["statistics"] = {
                "total_nodes": len(self.nodes),
                "total_levels": len(self.execution_levels),
                "max_parallelism": max(len(level) for level in self.execution_levels) if self.execution_levels else 0,
                "avg_parallelism": sum(len(level) for level in self.execution_levels) / len(self.execution_levels) if self.execution_levels else 0,
                "parallel_groups": len(self.parallel_groups)
            }
            
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append({
                "type": "validation_error",
                "message": str(e)
            })
        
        return validation_result
    
    def get_workflow_info(self) -> Dict[str, Any]:
        """获取工作流信息"""
        return {
            "workflow_id": self.workflow_id,
            "name": self.name,
            "status": self.status.value,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "nodes": {node_id: node.to_dict() for node_id, node in self.nodes.items()},
            "edges": self.edges,
            "execution_levels": self.execution_levels,
            "parallel_groups": self.parallel_groups,
            "execution_stats": self.execution_stats,
            "validation": self.validate_workflow()
        }
    
    def visualize_mermaid(self) -> str:
        """生成Mermaid图表"""
        lines = ["graph TD"]
        
        # 添加节点
        for node_id, node in self.nodes.items():
            agent_name = node.agent.metadata.name
            node_label = f"{agent_name}"
            
            if node.parallel_group:
                node_label += f"\\n[{node.parallel_group}]"
            
            # 根据状态设置样式
            if node.status == AgentStatus.COMPLETED:
                lines.append(f'    {node_id}["{node_label}"]:::completed')
            elif node.status == AgentStatus.FAILED:
                lines.append(f'    {node_id}["{node_label}"]:::failed')
            elif node.status == AgentStatus.RUNNING:
                lines.append(f'    {node_id}["{node_label}"]:::running')
            else:
                lines.append(f'    {node_id}["{node_label}"]')
        
        # 添加边
        for from_node, to_node in self.edges:
            lines.append(f"    {from_node} --> {to_node}")
        
        # 添加样式
        lines.extend([
            "",
            "    classDef completed fill:#d4edda,stroke:#155724,stroke-width:2px",
            "    classDef failed fill:#f8d7da,stroke:#721c24,stroke-width:2px",
            "    classDef running fill:#fff3cd,stroke:#856404,stroke-width:2px"
        ])
        
        return "\n".join(lines)
    
    def export_json(self) -> str:
        """导出为JSON格式"""
        return json.dumps(self.get_workflow_info(), indent=2, ensure_ascii=False)
    
    def clone(self) -> 'HNGPTWorkflowDAG':
        """克隆工作流"""
        new_dag = HNGPTWorkflowDAG(name=f"{self.name}_clone")
        
        # 复制节点（注意：Agent实例是共享的）
        for node_id, node in self.nodes.items():
            new_dag.add_agent(
                agent=node.agent,
                dependencies=node.dependencies.copy(),
                parallel_group=node.parallel_group,
                condition=node.condition
            )
        
        return new_dag
