#!/usr/bin/env python3
"""
Business Agent工作流示例 - 展示如何使用business开头的Agent
"""

from typing import Dict, Any, List
from datetime import datetime

# 导入Business Agent
from .business_base_agent import BusinessAgentContext, BusinessAgentResult
from .business_init_agent import BusinessInitAgent
from .business_minio_download_agent import BusinessMinIODownloadAgent
from .business_document_parse_agent import BusinessDocumentParseAgent
from .business_llm_extraction_agent import BusinessLLMExtractionAgent
from .business_text_chunking_agent import BusinessTextChunkingAgent
from .business_vector_embedding_agent import BusinessVectorEmbeddingAgent
from .business_elasticsearch_storage_agent import BusinessElasticsearchStorageAgent
from .business_database_storage_agent import BusinessDatabaseStorageAgent

class BusinessDocumentWorkflow:
    """业务文档处理工作流 - 使用Business Agent"""
    
    def __init__(self, task_manager=None):
        self.task_manager = task_manager
        self.agents = self._setup_business_agents()
        
    def _setup_business_agents(self) -> List:
        """设置Business Agent"""
        return [
            BusinessInitAgent(),                    # 1. 初始化环境
            BusinessMinIODownloadAgent(),           # 2. 下载文件
            BusinessDocumentParseAgent(),           # 3. 解析文档
            BusinessTextChunkingAgent(),            # 4. 文本分块
            BusinessLLMExtractionAgent(),           # 5. LLM信息提取
            BusinessVectorEmbeddingAgent(),         # 6. 向量嵌入
            BusinessElasticsearchStorageAgent(),    # 7. ES存储
            BusinessDatabaseStorageAgent()          # 8. 数据库存储
        ]
    
    async def process_documents(
        self, 
        task_id: str, 
        project_name: str, 
        action: str, 
        file_urls: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理多个文档 - Business Agent方式"""
        
        # 创建Business Agent上下文
        context = BusinessAgentContext(
            task_id=task_id,
            project_name=project_name,
            action=action,
            file_urls=file_urls,
            config=config,
            extracted_data={},
            metadata={
                "workflow_type": "business_agent",
                "total_files": len(file_urls),
                "processing_mode": "batch"
            }
        )
        
        print(f"🚀 开始执行Business Agent工作流")
        print(f"📋 项目: {project_name}")
        print(f"📄 文件数量: {len(file_urls)}")
        print(f"🎯 处理类型: {action}")
        
        results = {}
        overall_success = True
        
        # 按顺序执行Business Agent
        for i, agent in enumerate(self.agents, 1):
            agent_name = agent.name
            print(f"🤖 [{i}/{len(self.agents)}] 开始执行 {agent_name}")
            
            try:
                # 执行Agent
                start_time = datetime.now()
                agent_output = await agent.process(context)
                execution_time = (datetime.now() - start_time).total_seconds()
                
                # 更新上下文
                if isinstance(agent_output, dict):
                    for key, value in agent_output.items():
                        context.update_extracted_data(key, value)
                
                # 记录结果
                results[agent.name] = BusinessAgentResult(
                    success=True,
                    status="completed",
                    output_data=agent_output,
                    execution_time=execution_time,
                    stage_name=agent_name,
                    progress_percentage=int((i / len(self.agents)) * 100)
                )
                
                print(f"✅ {agent_name} 执行成功 (耗时: {execution_time:.2f}s)")
                
            except Exception as e:
                print(f"❌ {agent_name} 执行失败: {str(e)}")
                
                results[agent.name] = BusinessAgentResult(
                    success=False,
                    status="failed",
                    error_message=str(e),
                    stage_name=agent_name,
                    progress_percentage=int((i / len(self.agents)) * 100)
                )
                
                overall_success = False
                # 可以选择继续执行或停止
                # break  # 如果要在失败时停止
        
        print(f"🎉 Business Agent工作流执行完成")
        
        return {
            "success": overall_success,
            "workflow_type": "business_agent",
            "task_id": task_id,
            "project_name": project_name,
            "action": action,
            "processed_files": len(file_urls),
            "agent_results": {name: result.__dict__ for name, result in results.items()},
            "extracted_data": context.extracted_data,
            "final_context": context.__dict__
        }

class BusinessWorkflowComparison:
    """Business Agent与当前Agent的对比分析"""
    
    @staticmethod
    def compare_approaches():
        """对比两种Agent方法"""
        
        comparison = {
            "当前Agent方法": {
                "优势": [
                    "✅ 已经实现并在使用",
                    "✅ 单文件处理逻辑清晰",
                    "✅ 与现有系统集成良好",
                    "✅ 错误处理机制完善"
                ],
                "劣势": [
                    "❌ 每个文件单独处理，效率较低",
                    "❌ 业务逻辑分散在多个Agent中",
                    "❌ 缺乏批量处理能力",
                    "❌ 统计信息不够详细"
                ]
            },
            "Business Agent方法": {
                "优势": [
                    "✅ 批量处理多个文件",
                    "✅ 业务流程更清晰",
                    "✅ 更丰富的统计和监控",
                    "✅ 更好的资源利用率"
                ],
                "劣势": [
                    "❌ 尚未集成到现有系统",
                    "❌ 需要重构现有接口",
                    "❌ 复杂度更高",
                    "❌ 测试和调试更困难"
                ]
            }
        }
        
        return comparison

def demonstrate_business_agent_usage():
    """演示Business Agent的使用方法"""
    
    print("🎯 Business Agent使用示例")
    print("=" * 60)
    
    # 示例配置
    task_id = "business-demo-001"
    project_name = "智慧城市项目"
    action = "项目档案"
    file_urls = [
        "docs/project/requirements.pdf",
        "docs/project/design.docx",
        "docs/project/implementation.pdf"
    ]
    config = {
        "llm": {"api_url": "http://localhost:8000", "token": "xxx"},
        "minio": {"endpoint": "localhost:9000", "bucket": "documents"},
        "elasticsearch": {"host": "localhost:9200"},
        "mysql": {"host": "localhost:3306", "database": "hngpt"}
    }
    
    print(f"📋 任务配置:")
    print(f"   任务ID: {task_id}")
    print(f"   项目名称: {project_name}")
    print(f"   处理类型: {action}")
    print(f"   文件数量: {len(file_urls)}")
    
    print(f"\n🤖 Business Agent执行顺序:")
    workflow = BusinessDocumentWorkflow()
    for i, agent in enumerate(workflow.agents, 1):
        print(f"   {i}. {agent.name}")
    
    print(f"\n💡 与当前方法的对比:")
    comparison = BusinessWorkflowComparison.compare_approaches()
    
    for method, details in comparison.items():
        print(f"\n📊 {method}:")
        for category, items in details.items():
            print(f"   {category}:")
            for item in items:
                print(f"     {item}")

if __name__ == "__main__":
    demonstrate_business_agent_usage()
