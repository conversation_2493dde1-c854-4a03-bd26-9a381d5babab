#!/usr/bin/env python3
"""
存储结果Agent - 负责将结构化信息保存到数据库
"""

import asyncio
import json
from typing import Dict, Any, List
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class StoreAgent(BaseAgent):
    """存储结果Agent - 将提取的结构化信息保存到数据库"""
    
    def __init__(self):
        super().__init__(name="存储结果Agent")
        self.add_dependency("validate_result")  # 依赖结果验证Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """存储提取结果到数据库"""
        await asyncio.sleep(0.6)  # 模拟存储时间
        
        # 获取验证后的结果
        merged_result = context.get_extracted_data("merged_result", {})
        validation_info = context.get_extracted_data("validation_info", {})
        validation_report = context.get_extracted_data("validation_report", {})
        
        # 检查是否通过验证
        if not validation_info.get("validation_passed", False):
            raise ValueError("验证未通过，无法存储结果")
        
        # 准备存储数据
        storage_data = self._prepare_storage_data(merged_result, context)
        
        # 模拟数据库存储操作
        document_storage = await self._store_document_info(storage_data["document_info"])
        entity_storage = await self._store_entities(storage_data["entities"])
        relation_storage = await self._store_relations(storage_data["relations"])
        structure_storage = await self._store_structure(storage_data["structure"])
        
        # 存储处理日志
        processing_log = await self._store_processing_log(storage_data["processing_log"], context)
        
        # 生成存储统计
        storage_statistics = self._generate_storage_statistics(
            document_storage, entity_storage, relation_storage, structure_storage, processing_log
        )
        
        # 创建存储索引
        indexing_result = await self._create_indexes(storage_data)
        
        # 验证存储完整性
        storage_verification = await self._verify_storage(storage_data, storage_statistics)
        
        return {
            "storage_info": {
                "storage_time": datetime.now().isoformat(),
                "storage_success": storage_verification["verification_passed"],
                "total_records_stored": storage_statistics["total_records"],
                "storage_size": storage_statistics["estimated_size"],
                "database_operations": storage_statistics["operations_count"]
            },
            "document_storage": document_storage,
            "entity_storage": entity_storage,
            "relation_storage": relation_storage,
            "structure_storage": structure_storage,
            "processing_log": processing_log,
            "storage_statistics": storage_statistics,
            "indexing_result": indexing_result,
            "storage_verification": storage_verification,
            "storage_summary": {
                "status": "completed",
                "storage_successful": storage_verification["verification_passed"],
                "records_stored": storage_statistics["total_records"],
                "data_integrity": storage_verification["integrity_score"],
                "next_stage": "complete"
            }
        }
    
    def _prepare_storage_data(self, merged_result: Dict, context: AgentContext) -> Dict[str, Any]:
        """准备存储数据"""
        document_metadata = merged_result.get("document_metadata", {})
        entity_summary = merged_result.get("entity_summary", {})
        relation_summary = merged_result.get("relation_summary", {})
        structure_summary = merged_result.get("structure_summary", {})
        quality_metrics = merged_result.get("quality_metrics", {})
        
        # 准备文档信息
        document_info = {
            "doc_id": context.metadata.get("doc_id", ""),
            "task_id": context.task_id,
            "title": document_metadata.get("title", ""),
            "author": document_metadata.get("author", ""),
            "document_type": document_metadata.get("document_type", ""),
            "creation_date": document_metadata.get("creation_date", ""),
            "language": document_metadata.get("language", "zh-CN"),
            "page_count": document_metadata.get("page_count", 0),
            "word_count": document_metadata.get("word_count", 0),
            "source_url": document_metadata.get("source_url", ""),
            "processing_date": datetime.now().isoformat(),
            "quality_score": quality_metrics.get("overall_quality", 0.0),
            "status": "processed"
        }
        
        # 准备实体数据
        entities = []
        for entity_type in ["person_entities", "organization_entities", "location_entities", 
                           "date_entities", "number_entities", "keyword_entities", "technical_entities"]:
            entity_data = context.get_extracted_data(entity_type, {})
            for entity in entity_data.get("entities", []):
                entities.append({
                    "doc_id": document_info["doc_id"],
                    "entity_type": entity_type.replace("_entities", ""),
                    "entity_name": entity.get("name") or entity.get("keyword") or entity.get("term") or entity.get("date") or entity.get("number", ""),
                    "entity_value": json.dumps(entity, ensure_ascii=False),
                    "confidence": entity.get("confidence", 0.0),
                    "position": entity.get("position", 0),
                    "context": entity.get("context", ""),
                    "source": entity.get("source", ""),
                    "created_at": datetime.now().isoformat()
                })
        
        # 准备关系数据
        relations = []
        for relation_type in ["employment_relations", "location_relations", "hierarchical_relations",
                             "collaboration_relations", "temporal_relations", "semantic_relations", "dependency_relations"]:
            relation_data = context.get_extracted_data(relation_type, {})
            for relation in relation_data.get("relations", []):
                relations.append({
                    "doc_id": document_info["doc_id"],
                    "relation_type": relation_type.replace("_relations", ""),
                    "subject": self._extract_relation_subject(relation),
                    "object": self._extract_relation_object(relation),
                    "relation_value": json.dumps(relation, ensure_ascii=False),
                    "confidence": relation.get("confidence", 0.0),
                    "evidence": relation.get("evidence", ""),
                    "source": relation.get("source", ""),
                    "created_at": datetime.now().isoformat()
                })
        
        # 准备结构数据
        structure = {
            "doc_id": document_info["doc_id"],
            "structure_type": structure_summary.get("structure_type", ""),
            "hierarchy_depth": structure_summary.get("hierarchy_depth", 1),
            "chapter_count": structure_summary.get("chapter_count", 0),
            "section_count": structure_summary.get("section_count", 0),
            "list_count": structure_summary.get("list_count", 0),
            "outline": json.dumps(structure_summary.get("outline", []), ensure_ascii=False),
            "structure_complexity": structure_summary.get("structure_complexity", "simple"),
            "created_at": datetime.now().isoformat()
        }
        
        # 准备处理日志
        processing_log = {
            "doc_id": document_info["doc_id"],
            "task_id": context.task_id,
            "processing_start": context.created_at,
            "processing_end": datetime.now().isoformat(),
            "processing_stages": json.dumps(self._get_processing_stages(context), ensure_ascii=False),
            "quality_metrics": json.dumps(quality_metrics, ensure_ascii=False),
            "validation_result": json.dumps(context.get_extracted_data("validation_report", {}), ensure_ascii=False),
            "agent_performance": json.dumps(merged_result.get("processing_statistics", {}).get("agent_performance", {}), ensure_ascii=False),
            "created_at": datetime.now().isoformat()
        }
        
        return {
            "document_info": document_info,
            "entities": entities,
            "relations": relations,
            "structure": structure,
            "processing_log": processing_log
        }
    
    async def _store_document_info(self, document_info: Dict) -> Dict[str, Any]:
        """存储文档信息"""
        # 模拟数据库存储操作
        await asyncio.sleep(0.1)
        
        storage_result = {
            "table": "documents",
            "operation": "insert",
            "record_id": document_info["doc_id"],
            "success": True,
            "affected_rows": 1,
            "storage_time": datetime.now().isoformat(),
            "data_size": len(json.dumps(document_info, ensure_ascii=False))
        }
        
        # 模拟可能的存储错误
        if not document_info.get("title") or not document_info.get("doc_id"):
            storage_result["success"] = False
            storage_result["error"] = "缺少必需字段"
            storage_result["affected_rows"] = 0
        
        return storage_result
    
    async def _store_entities(self, entities: List[Dict]) -> Dict[str, Any]:
        """存储实体信息"""
        await asyncio.sleep(0.2)
        
        successful_inserts = 0
        failed_inserts = 0
        
        for entity in entities:
            # 模拟单个实体存储
            if entity.get("entity_name") and entity.get("doc_id"):
                successful_inserts += 1
            else:
                failed_inserts += 1
        
        storage_result = {
            "table": "entities",
            "operation": "batch_insert",
            "total_records": len(entities),
            "successful_inserts": successful_inserts,
            "failed_inserts": failed_inserts,
            "success": failed_inserts == 0,
            "storage_time": datetime.now().isoformat(),
            "data_size": sum(len(json.dumps(entity, ensure_ascii=False)) for entity in entities)
        }
        
        return storage_result
    
    async def _store_relations(self, relations: List[Dict]) -> Dict[str, Any]:
        """存储关系信息"""
        await asyncio.sleep(0.15)
        
        successful_inserts = 0
        failed_inserts = 0
        
        for relation in relations:
            # 模拟单个关系存储
            if relation.get("subject") and relation.get("object") and relation.get("doc_id"):
                successful_inserts += 1
            else:
                failed_inserts += 1
        
        storage_result = {
            "table": "relations",
            "operation": "batch_insert",
            "total_records": len(relations),
            "successful_inserts": successful_inserts,
            "failed_inserts": failed_inserts,
            "success": failed_inserts == 0,
            "storage_time": datetime.now().isoformat(),
            "data_size": sum(len(json.dumps(relation, ensure_ascii=False)) for relation in relations)
        }
        
        return storage_result
    
    async def _store_structure(self, structure: Dict) -> Dict[str, Any]:
        """存储结构信息"""
        await asyncio.sleep(0.05)
        
        storage_result = {
            "table": "document_structure",
            "operation": "insert",
            "record_id": structure["doc_id"],
            "success": True,
            "affected_rows": 1,
            "storage_time": datetime.now().isoformat(),
            "data_size": len(json.dumps(structure, ensure_ascii=False))
        }
        
        if not structure.get("doc_id"):
            storage_result["success"] = False
            storage_result["error"] = "缺少doc_id"
            storage_result["affected_rows"] = 0
        
        return storage_result
    
    async def _store_processing_log(self, processing_log: Dict, context: AgentContext) -> Dict[str, Any]:
        """存储处理日志"""
        await asyncio.sleep(0.05)
        
        storage_result = {
            "table": "processing_logs",
            "operation": "insert",
            "record_id": f"{processing_log['doc_id']}_{processing_log['task_id']}",
            "success": True,
            "affected_rows": 1,
            "storage_time": datetime.now().isoformat(),
            "data_size": len(json.dumps(processing_log, ensure_ascii=False))
        }
        
        return storage_result
    
    def _generate_storage_statistics(self, *storage_results) -> Dict[str, Any]:
        """生成存储统计"""
        stats = {
            "total_records": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "operations_count": len(storage_results),
            "estimated_size": 0,
            "storage_efficiency": 0.0,
            "operation_details": []
        }
        
        for result in storage_results:
            if isinstance(result, dict):
                # 单个记录存储结果
                if result.get("success"):
                    stats["successful_operations"] += 1
                    stats["total_records"] += result.get("affected_rows", 0)
                else:
                    stats["failed_operations"] += 1
                
                stats["estimated_size"] += result.get("data_size", 0)
                
                stats["operation_details"].append({
                    "table": result.get("table", "unknown"),
                    "operation": result.get("operation", "unknown"),
                    "success": result.get("success", False),
                    "records": result.get("affected_rows", 0) or result.get("total_records", 0)
                })
        
        # 计算存储效率
        if stats["operations_count"] > 0:
            stats["storage_efficiency"] = stats["successful_operations"] / stats["operations_count"]
        
        return stats
    
    async def _create_indexes(self, storage_data: Dict) -> Dict[str, Any]:
        """创建存储索引"""
        await asyncio.sleep(0.1)
        
        indexing_result = {
            "indexes_created": [],
            "indexing_success": True,
            "indexing_time": datetime.now().isoformat(),
            "performance_improvement": "预期提升查询性能30-50%"
        }
        
        # 模拟创建索引
        doc_id = storage_data["document_info"]["doc_id"]
        
        indexes_to_create = [
            f"idx_documents_doc_id_{doc_id[:8]}",
            f"idx_entities_doc_id_{doc_id[:8]}",
            f"idx_relations_doc_id_{doc_id[:8]}",
            f"idx_entities_type_{doc_id[:8]}",
            f"idx_relations_type_{doc_id[:8]}"
        ]
        
        for index_name in indexes_to_create:
            indexing_result["indexes_created"].append({
                "index_name": index_name,
                "status": "created",
                "creation_time": datetime.now().isoformat()
            })
        
        return indexing_result
    
    async def _verify_storage(self, storage_data: Dict, storage_statistics: Dict) -> Dict[str, Any]:
        """验证存储完整性"""
        await asyncio.sleep(0.1)
        
        verification = {
            "verification_passed": True,
            "integrity_score": 0.0,
            "verification_details": {},
            "data_consistency_check": {},
            "verification_time": datetime.now().isoformat()
        }
        
        # 验证存储统计
        expected_records = (
            1 +  # document_info
            len(storage_data["entities"]) +
            len(storage_data["relations"]) +
            1 +  # structure
            1    # processing_log
        )
        
        actual_records = storage_statistics["total_records"]
        
        if actual_records == expected_records:
            verification["verification_details"]["record_count"] = "passed"
        else:
            verification["verification_passed"] = False
            verification["verification_details"]["record_count"] = f"failed: expected {expected_records}, got {actual_records}"
        
        # 验证存储成功率
        storage_efficiency = storage_statistics["storage_efficiency"]
        if storage_efficiency >= 0.95:
            verification["verification_details"]["storage_efficiency"] = "passed"
        else:
            verification["verification_passed"] = False
            verification["verification_details"]["storage_efficiency"] = f"failed: efficiency {storage_efficiency:.2f} < 0.95"
        
        # 数据一致性检查
        doc_id = storage_data["document_info"]["doc_id"]
        
        verification["data_consistency_check"] = {
            "doc_id_consistency": all(
                item.get("doc_id") == doc_id 
                for item in storage_data["entities"] + storage_data["relations"] + [storage_data["structure"], storage_data["processing_log"]]
            ),
            "timestamp_consistency": True,  # 简化检查
            "data_format_consistency": True  # 简化检查
        }
        
        # 计算完整性分数
        passed_checks = sum(1 for check in verification["verification_details"].values() if check == "passed")
        total_checks = len(verification["verification_details"])
        consistency_score = sum(1 for check in verification["data_consistency_check"].values() if check)
        
        if total_checks > 0:
            verification["integrity_score"] = (passed_checks / total_checks + consistency_score / len(verification["data_consistency_check"])) / 2
        
        return verification
    
    def _extract_relation_subject(self, relation: Dict) -> str:
        """提取关系主体"""
        return (relation.get("person") or 
                relation.get("entity") or 
                relation.get("entity1") or 
                relation.get("superior") or 
                relation.get("subject") or 
                relation.get("event") or 
                relation.get("predecessor") or "")
    
    def _extract_relation_object(self, relation: Dict) -> str:
        """提取关系客体"""
        return (relation.get("organization") or 
                relation.get("location") or 
                relation.get("entity2") or 
                relation.get("subordinate") or 
                relation.get("object") or 
                relation.get("time") or 
                relation.get("successor") or "")
    
    def _get_processing_stages(self, context: AgentContext) -> List[Dict]:
        """获取处理阶段信息"""
        stages = []
        
        # 从上下文中提取各阶段信息
        stage_names = [
            "init_info", "download_info", "validation_report", "parse_info",
            "basic_info", "structure_info", "entity_info", "relation_info",
            "merge_info", "validation_info"
        ]
        
        for stage_name in stage_names:
            stage_data = context.get_extracted_data(stage_name, {})
            if stage_data:
                stages.append({
                    "stage": stage_name,
                    "status": "completed",
                    "timestamp": stage_data.get("extraction_time") or stage_data.get("validation_time") or stage_data.get("merge_time") or datetime.now().isoformat(),
                    "data_size": len(json.dumps(stage_data, ensure_ascii=False))
                })
        
        return stages
    
    def get_stage_name(self) -> str:
        return "store"
    
    def get_progress_percentage(self) -> int:
        return 98
    
    def get_stage_description(self) -> str:
        return "将结构化信息保存到数据库"
