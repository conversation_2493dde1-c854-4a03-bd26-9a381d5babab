#!/usr/bin/env python3
"""
Agent工作流管理器 - 简化版本，确保基本功能可用
"""

from typing import Dict, Any, List
from datetime import datetime

# 尝试导入Business Agent，如果失败则使用传统Agent
try:
    from .business_base_agent import BusinessAgentContext
    from .business_init_agent import BusinessInitAgent
    from .business_minio_download_agent import BusinessMinIODownloadAgent
    from .business_document_parse_agent import BusinessDocumentParseAgent
    from .business_llm_extraction_agent import BusinessLLMExtractionAgent
    from .business_elasticsearch_storage_agent import BusinessElasticsearchStorageAgent
    from .business_database_storage_agent import BusinessDatabaseStorageAgent
    BUSINESS_AGENTS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Business Agent导入失败，使用传统Agent: {e}")
    BUSINESS_AGENTS_AVAILABLE = False
    # 导入传统Agent
    from .base_agent import AgentWorkflow, AgentContext
    from .document_agents import (
        InitAgent, DownloadAgent, ValidateAgent, ParseAgent,
        BasicExtractAgent, StructureExtractAgent, EntityExtractAgent,
        RelationExtractAgent, MergeInfoAgent, ValidateResultAgent,
        StoreAgent, CompleteAgent
    )

class BusinessDocumentProcessingWorkflow:
    """业务文档处理工作流 - 支持Business Agent和传统Agent"""

    def __init__(self, task_manager=None):
        self.task_manager = task_manager
        self.progress_callback = None

        if BUSINESS_AGENTS_AVAILABLE:
            self.business_agents = self._setup_business_agents()
            self.workflow_type = "business_agent"
            self.workflow = None  # 确保属性存在，即使为None
        else:
            self.workflow = AgentWorkflow()
            self._setup_traditional_agents()
            self.workflow_type = "traditional_agent"
            self.business_agents = []  # 确保属性存在，即使为空列表

    def _setup_business_agents(self) -> List:
        """设置Business Agent"""
        if not BUSINESS_AGENTS_AVAILABLE:
            return []

        agents = [
            BusinessInitAgent(),                    # 1. 初始化环境
            BusinessMinIODownloadAgent(),           # 2. 下载文件
            BusinessDocumentParseAgent(),           # 3. 解析文档
            BusinessLLMExtractionAgent(),           # 4. LLM信息提取
            BusinessElasticsearchStorageAgent(),    # 5. ES存储
            BusinessDatabaseStorageAgent()          # 6. 数据库存储
        ]

        # 为每个Agent设置任务管理器
        for agent in agents:
            if hasattr(agent, 'set_task_manager'):
                agent.set_task_manager(self.task_manager)

        return agents

    def _setup_traditional_agents(self):
        """设置传统Agent（备选方案）"""
        if BUSINESS_AGENTS_AVAILABLE or not hasattr(self, 'workflow') or self.workflow is None:
            return

        agents = [
            InitAgent(),
            DownloadAgent(),
            ValidateAgent(),
            ParseAgent(),
            BasicExtractAgent(),
            StructureExtractAgent(),
            EntityExtractAgent(),
            RelationExtractAgent(),
            MergeInfoAgent(),
            ValidateResultAgent(),
            StoreAgent(),
            CompleteAgent()
        ]

        # 为每个Agent设置任务管理器
        for agent in agents:
            agent.set_task_manager(self.task_manager)
            self.workflow.add_agent(agent)

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    @property
    def workflow_id(self) -> str:
        """获取工作流ID"""
        if self.workflow_type == "business_agent":
            return f"business-workflow-{id(self)}"
        else:
            return self.workflow.workflow_id if self.workflow else f"fallback-workflow-{id(self)}"

    @property
    def agent_count(self) -> int:
        """获取Agent数量"""
        if self.workflow_type == "business_agent":
            return len(self.business_agents) if hasattr(self, 'business_agents') and self.business_agents else 0
        else:
            return len(self.workflow.agents) if self.workflow else 0

    def get_progress(self) -> int:
        """获取整体进度"""
        if self.workflow_type == "business_agent":
            return 0  # 简单实现
        else:
            return self.workflow.get_overall_progress() if self.workflow else 0

    def get_workflow_info(self) -> Dict[str, Any]:
        """获取工作流信息"""
        if self.workflow_type == "business_agent":
            return {
                "workflow_id": self.workflow_id,
                "agent_count": self.agent_count,
                "workflow_type": "business_agent",
                "agents": [agent.name for agent in self.business_agents] if hasattr(self, 'business_agents') and self.business_agents else []
            }
        else:
            return self.workflow.get_workflow_info() if self.workflow else {"workflow_id": self.workflow_id, "agent_count": 0, "workflow_type": "fallback"}

    def is_completed(self) -> bool:
        """检查工作流是否完成"""
        if self.workflow_type == "business_agent":
            return False  # 简单实现
        else:
            return self.workflow.status == "completed" if self.workflow else False

    def has_failed(self) -> bool:
        """检查工作流是否失败"""
        if self.workflow_type == "business_agent":
            return False  # 简单实现
        else:
            return self.workflow.status == "failed" if self.workflow else False

    async def process_documents(
        self,
        task_id: str,
        project_name: str,
        action: str,
        file_urls: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理多个文档 - Business Agent方式"""

        # 创建Business Agent上下文
        context = BusinessAgentContext(
            task_id=task_id,
            project_name=project_name,
            action=action,
            file_urls=file_urls,
            config=config,
            extracted_data={},
            metadata={
                "workflow_type": "business_agent",
                "total_files": len(file_urls),
                "processing_mode": "batch"
            }
        )

        print(f"🚀 开始执行Business Agent工作流")
        print(f"📋 项目: {project_name}")
        print(f"📄 文件数量: {len(file_urls)}")
        print(f"🎯 处理类型: {action}")

        results = {}
        overall_success = True

        # 按顺序执行Business Agent
        for i, agent in enumerate(self.business_agents, 1):
            agent_name = agent.name
            print(f"🤖 [{i}/{len(self.business_agents)}] 开始执行 {agent_name}")

            try:
                # 执行Agent
                start_time = datetime.now()
                agent_output = await agent.process(context)
                execution_time = (datetime.now() - start_time).total_seconds()

                # 更新上下文
                if isinstance(agent_output, dict):
                    for key, value in agent_output.items():
                        context.update_extracted_data(key, value)

                # 记录结果
                results[agent.name] = {
                    "success": True,
                    "status": "completed",
                    "output_data": agent_output,
                    "execution_time": execution_time,
                    "stage_name": agent_name,
                    "progress_percentage": int((i / len(self.business_agents)) * 100)
                }

                print(f"✅ {agent_name} 执行成功 (耗时: {execution_time:.2f}s)")

            except Exception as e:
                print(f"❌ {agent_name} 执行失败: {str(e)}")

                results[agent.name] = {
                    "success": False,
                    "status": "failed",
                    "error_message": str(e),
                    "stage_name": agent_name,
                    "progress_percentage": int((i / len(self.business_agents)) * 100)
                }

                overall_success = False
                # 继续执行其他Agent

        print(f"🎉 Business Agent工作流执行完成")

        return {
            "success": overall_success,
            "workflow_type": "business_agent",
            "task_id": task_id,
            "project_name": project_name,
            "action": action,
            "processed_files": len(file_urls),
            "agent_results": results,
            "extracted_data": context.extracted_data,
            "final_context": context.__dict__
        }

    async def process_document(self, task_id: str, file_url: str, file_content: Dict[str, Any], project_name: str = None, action: str = None) -> Dict[str, Any]:
        """向后兼容的单文档处理方法"""

        if self.workflow_type == "business_agent" and BUSINESS_AGENTS_AVAILABLE:
            return await self._process_with_business_agents(task_id, file_url, file_content, project_name, action)
        else:
            return await self._process_with_traditional_agents(task_id, file_url, file_content, project_name, action)

    async def _process_with_business_agents(self, task_id: str, file_url: str, file_content: Dict[str, Any], project_name: str = None, action: str = None) -> Dict[str, Any]:
        """使用Business Agent处理文档"""
        # 将单文件转换为文件列表，调用批量处理方法
        file_urls = [file_url]
        config = {
            "llm": {"api_url": "http://localhost:8000", "token": "xxx"},
            "minio": {"endpoint": "localhost:9000", "bucket": "documents"},
            "elasticsearch": {"host": "localhost:9200"},
            "mysql": {"host": "localhost:3306", "database": "hngpt"}
        }

        # 调用批量处理方法
        result = await self.process_documents(
            task_id=task_id,
            project_name=project_name or "未知项目",
            action=action or "项目档案",
            file_urls=file_urls,
            config=config
        )

        # 转换结果格式以保持向后兼容
        return {
            "success": result["success"],
            "workflow_id": self.workflow_id,
            "task_id": task_id,
            "file_url": file_url,
            "results": result["agent_results"],
            "extracted_data": result["extracted_data"],
            "workflow_info": self.get_workflow_info(),
            "final_context": result["final_context"]
        }

    async def _process_with_traditional_agents(self, task_id: str, file_url: str, file_content: Dict[str, Any], project_name: str = None, action: str = None) -> Dict[str, Any]:
        """使用传统Agent处理文档（备选方案）"""
        print(f"🔄 使用传统Agent工作流处理文档")

        # 检查workflow是否可用
        if not self.workflow:
            return {
                "success": False,
                "workflow_id": self.workflow_id,
                "task_id": task_id,
                "file_url": file_url,
                "error": "传统Agent工作流不可用",
                "error_type": "WorkflowNotAvailable"
            }

        # 创建传统Agent上下文
        context = AgentContext(
            task_id=task_id,
            file_url=file_url,
            file_content=file_content,
            extracted_data={},
            metadata={
                "workflow_id": self.workflow_id,
                "processing_mode": "traditional_agent",
                "project_name": project_name or "未知项目",
                "action": action or "项目档案",
                "doc_id": file_content.get("doc_id")
            },
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )

        try:
            # 执行传统工作流
            results = await self.workflow.execute(context)

            return {
                "success": True,
                "workflow_id": self.workflow_id,
                "task_id": task_id,
                "file_url": file_url,
                "results": {k: v.to_dict() for k, v in results.items()},
                "extracted_data": context.extracted_data,
                "workflow_info": self.get_workflow_info(),
                "final_context": context.to_dict()
            }

        except Exception as e:
            return {
                "success": False,
                "workflow_id": self.workflow_id,
                "task_id": task_id,
                "file_url": file_url,
                "error": str(e),
                "error_type": type(e).__name__
            }

# 为了向后兼容，创建一个别名
DocumentProcessingWorkflow = BusinessDocumentProcessingWorkflow

class WorkflowFactory:
    """工作流工厂"""

    @staticmethod
    def create_document_workflow(task_manager=None) -> DocumentProcessingWorkflow:
        """创建文档处理工作流 - 使用Business Agent"""
        return DocumentProcessingWorkflow(task_manager)

    @staticmethod
    def create_business_workflow(task_manager=None) -> BusinessDocumentProcessingWorkflow:
        """创建业务文档处理工作流"""
        return BusinessDocumentProcessingWorkflow(task_manager)

class WorkflowMonitor:
    """工作流监控器"""
    
    def __init__(self):
        self.active_workflows: Dict[str, DocumentProcessingWorkflow] = {}
        self.workflow_history: List[Dict[str, Any]] = []
    
    def register_workflow(self, workflow_id: str, workflow: DocumentProcessingWorkflow):
        """注册工作流"""
        self.active_workflows[workflow_id] = workflow
    
    def unregister_workflow(self, workflow_id: str):
        """注销工作流"""
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows.pop(workflow_id)
            # 保存到历史记录
            self.workflow_history.append({
                "workflow_id": workflow_id,
                "final_status": "completed",
                "completed_at": datetime.now().isoformat()
            })
    
    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """获取工作流状态"""
        if workflow_id in self.active_workflows:
            return self.active_workflows[workflow_id].get_workflow_info()
        return {"error": "Workflow not found"}

    def get_all_workflows(self) -> Dict[str, Any]:
        """获取所有工作流状态"""
        return {
            "active_workflows": {
                wid: workflow.get_workflow_info()
                for wid, workflow in self.active_workflows.items()
            },
            "workflow_count": len(self.active_workflows),
            "history_count": len(self.workflow_history)
        }

    def get_workflow_metrics(self) -> Dict[str, Any]:
        """获取工作流指标"""
        active_count = len(self.active_workflows)
        completed_count = len(self.workflow_history)

        # 计算成功率（简化版本）
        success_count = completed_count  # 假设完成的都是成功的
        success_rate = success_count / completed_count if completed_count > 0 else 0

        return {
            "active_workflows": active_count,
            "completed_workflows": completed_count,
            "success_rate": success_rate,
            "total_processed": active_count + completed_count
        }

# 全局工作流监控器实例
workflow_monitor = WorkflowMonitor()
