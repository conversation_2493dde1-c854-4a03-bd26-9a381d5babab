#!/usr/bin/env python3
"""
Agent工作流包
"""

# 原有Agent系统
from .base_agent import BaseAgent, AgentContext, AgentResult, AgentWorkflow, AgentStatus
from .document_agents import (
    InitAgent, DownloadAgent, ValidateAgent, ParseAgent,
    BasicExtractAgent, StructureExtractAgent, EntityExtractAgent,
    RelationExtractAgent, MergeInfoAgent, ValidateResultAgent,
    StoreAgent, CompleteAgent
)
from .workflow_manager import DocumentProcessingWorkflow, WorkflowFactory, WorkflowMonitor, workflow_monitor

# 新的HNGPT标准化Agent系统
# 暂时注释掉以调试导入问题
# from .hngpt_base_agent import (
#     HNGPTBaseAgent, WorkflowContext, AgentMetadata, DataSchema, DataType
# )
# from .hngpt_workflow_dag import HNGPTWorkflowDAG, WorkflowStatus, AgentNode
# from .hngpt_workflow_executor import (
#     HNGPTWorkflowExecutor, ExecutionConfig, ExecutionResult
# )
# from .hngpt_document_agents import (
#     HNGPTInitAgent, HNGPTDownloadAgent, HNGPTParseAgent,
#     HNGPTExtractionAgent, HNGPTStorageAgent
# )
# from .hngpt_workflow_factory import HNGPTWorkflowFactory
# from .hngpt_workflow_utils import (
#     HNGPTWorkflowValidator, HNGPTWorkflowExporter,
#     HNGPTWorkflowImporter, HNGPTWorkflowMonitor, workflow_monitor as hngpt_workflow_monitor
# )

__all__ = [
    # 原有基础类
    "BaseAgent",
    "AgentContext",
    "AgentResult",
    "AgentWorkflow",
    "AgentStatus",

    # 原有文档处理Agent
    "InitAgent",
    "DownloadAgent",
    "ValidateAgent",
    "ParseAgent",
    "BasicExtractAgent",
    "StructureExtractAgent",
    "EntityExtractAgent",
    "RelationExtractAgent",
    "MergeInfoAgent",
    "ValidateResultAgent",
    "StoreAgent",
    "CompleteAgent",

    # 原有工作流管理
    "DocumentProcessingWorkflow",
    "WorkflowFactory",
    "WorkflowMonitor",
    "workflow_monitor",

    # 新的HNGPT标准化Agent系统
    "HNGPTBaseAgent",
    "WorkflowContext",
    "AgentMetadata",
    "DataSchema",
    "DataType",
    "HNGPTWorkflowDAG",
    "WorkflowStatus",
    "AgentNode",
    "HNGPTWorkflowExecutor",
    "ExecutionConfig",
    "ExecutionResult",
    "HNGPTInitAgent",
    "HNGPTDownloadAgent",
    "HNGPTParseAgent",
    "HNGPTExtractionAgent",
    "HNGPTStorageAgent",
    "HNGPTWorkflowFactory",
    "HNGPTWorkflowValidator",
    "HNGPTWorkflowExporter",
    "HNGPTWorkflowImporter",
    "HNGPTWorkflowMonitor",
    "hngpt_workflow_monitor"
]
