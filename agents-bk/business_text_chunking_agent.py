#!/usr/bin/env python3
"""
业务文本切割Agent - 将文档内容切割成语义块
"""

import asyncio
import os
import sys
from typing import Dict, Any, List
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径以导入split_text2
sys.path.insert(0, '/workspace/hngpt')

from .business_base_agent import BusinessBaseAgent, BusinessAgentContext

# 导入实际的split_text2函数
try:
    from loaders.detect import split_text2
except ImportError:
    print("警告: 无法导入split_text2，使用备用实现")
    def split_text2(text, min_length=512, buffer_min_length=0):
        """备用的文本切割实现"""
        if not text or len(text) < min_length:
            return [text] if text else []
        
        # 简单按长度切割
        chunks = []
        start = 0
        while start < len(text):
            end = start + min_length
            if end < len(text):
                # 尝试在句号处断开
                for i in range(end, min(end + 100, len(text))):
                    if text[i] in ['。', '！', '？', '\n']:
                        end = i + 1
                        break
            chunks.append(text[start:end])
            start = end
        return chunks

class BusinessTextChunkingAgent(BusinessBaseAgent):
    """业务文本切割Agent"""
    
    def __init__(self):
        super().__init__(name="业务文本切割Agent")
        self.add_dependency("parse_info")  # 依赖文档解析Agent
    
    async def process(self, context: BusinessAgentContext) -> Dict[str, Any]:
        """切割解析后的文本"""
        await asyncio.sleep(0.4)  # 模拟切割时间
        
        # 获取解析信息
        parse_info = context.get_extracted_data("parse_info", {})
        extracted_texts = context.get_extracted_data("extracted_texts", [])
        
        # 获取切割配置
        chunk_size = context.config.get("chunk_size", 256)
        chunk_overlap = context.config.get("chunk_overlap", 0)
        
        # 切割文本
        chunking_results = await self._chunk_texts(extracted_texts, chunk_size, chunk_overlap)
        
        # 验证切割结果
        chunk_validation = self._validate_chunks(chunking_results)
        
        # 计算切割统计
        chunk_statistics = self._calculate_chunk_statistics(chunking_results)
        
        # 生成切割清单
        chunk_manifest = self._generate_chunk_manifest(chunking_results, context)
        
        # 保存切割结果到文件
        chunk_files = await self._save_chunks_to_files(chunking_results, context)
        
        return {
            "chunking_info": {
                "chunking_time": datetime.now().isoformat(),
                "total_documents": len(extracted_texts),
                "total_chunks": chunk_statistics["total_chunks"],
                "chunk_size": chunk_size,
                "chunk_overlap": chunk_overlap,
                "chunking_method": "split_text2"
            },
            "chunking_results": chunking_results,
            "chunk_validation": chunk_validation,
            "chunk_statistics": chunk_statistics,
            "chunk_manifest": chunk_manifest,
            "chunk_files": chunk_files,
            "chunking_summary": {
                "status": "completed",
                "chunking_successful": chunk_statistics["total_chunks"] > 0,
                "chunks_ready_for_vectorization": chunk_statistics["valid_chunks"],
                "next_stage": "vector_embedding"
            }
        }
    
    async def _chunk_texts(self, extracted_texts: List[Dict[str, Any]], chunk_size: int, chunk_overlap: int) -> List[Dict[str, Any]]:
        """切割文本列表"""
        chunking_results = []
        
        for text_info in extracted_texts:
            try:
                file_name = text_info.get("file_name", "unknown")
                content = text_info.get("content", "")
                metadata = text_info.get("metadata", {})
                
                if not content:
                    chunking_results.append({
                        "file_name": file_name,
                        "chunks": [],
                        "chunk_count": 0,
                        "chunking_success": False,
                        "error": "Empty content",
                        "metadata": metadata
                    })
                    continue
                
                # 使用实际的split_text2函数进行切割
                chunks = split_text2(content, min_length=chunk_size, buffer_min_length=chunk_overlap)
                
                # 为每个chunk添加元数据
                chunk_objects = []
                for i, chunk_text in enumerate(chunks):
                    chunk_objects.append({
                        "chunk_id": f"{file_name}_chunk_{i+1}",
                        "chunk_index": i,
                        "content": chunk_text,
                        "length": len(chunk_text),
                        "source_file": file_name,
                        "metadata": {
                            **metadata,
                            "chunk_method": "split_text2",
                            "chunk_size": chunk_size,
                            "chunk_overlap": chunk_overlap
                        }
                    })
                
                chunking_results.append({
                    "file_name": file_name,
                    "chunks": chunk_objects,
                    "chunk_count": len(chunk_objects),
                    "chunking_success": True,
                    "error": None,
                    "metadata": metadata,
                    "original_length": len(content),
                    "total_chunk_length": sum(len(chunk_text) for chunk_text in chunks)
                })
                
            except Exception as e:
                chunking_results.append({
                    "file_name": text_info.get("file_name", "unknown"),
                    "chunks": [],
                    "chunk_count": 0,
                    "chunking_success": False,
                    "error": str(e),
                    "metadata": text_info.get("metadata", {})
                })
        
        return chunking_results
    
    def _validate_chunks(self, chunking_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证切割结果"""
        validation = {
            "total_files": len(chunking_results),
            "successful_chunking": 0,
            "failed_chunking": 0,
            "total_chunks": 0,
            "valid_chunks": 0,
            "empty_chunks": 0,
            "validation_details": []
        }
        
        for result in chunking_results:
            if result["chunking_success"]:
                validation["successful_chunking"] += 1
                validation["total_chunks"] += result["chunk_count"]
                
                # 验证每个chunk
                valid_chunks_in_file = 0
                empty_chunks_in_file = 0
                
                for chunk in result["chunks"]:
                    if chunk["content"].strip():
                        valid_chunks_in_file += 1
                        validation["valid_chunks"] += 1
                    else:
                        empty_chunks_in_file += 1
                        validation["empty_chunks"] += 1
                
                validation["validation_details"].append({
                    "file_name": result["file_name"],
                    "status": "success",
                    "chunk_count": result["chunk_count"],
                    "valid_chunks": valid_chunks_in_file,
                    "empty_chunks": empty_chunks_in_file,
                    "original_length": result.get("original_length", 0),
                    "total_chunk_length": result.get("total_chunk_length", 0)
                })
            else:
                validation["failed_chunking"] += 1
                validation["validation_details"].append({
                    "file_name": result["file_name"],
                    "status": "failed",
                    "error": result.get("error", "unknown")
                })
        
        return validation
    
    def _calculate_chunk_statistics(self, chunking_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算切割统计"""
        stats = {
            "total_files": len(chunking_results),
            "successful_files": 0,
            "failed_files": 0,
            "total_chunks": 0,
            "valid_chunks": 0,
            "average_chunk_size": 0,
            "min_chunk_size": float('inf'),
            "max_chunk_size": 0,
            "chunk_size_distribution": {}
        }
        
        all_chunk_sizes = []
        
        for result in chunking_results:
            if result["chunking_success"]:
                stats["successful_files"] += 1
                stats["total_chunks"] += result["chunk_count"]
                
                for chunk in result["chunks"]:
                    chunk_size = chunk["length"]
                    if chunk["content"].strip():  # 只统计非空chunk
                        stats["valid_chunks"] += 1
                        all_chunk_sizes.append(chunk_size)
                        stats["min_chunk_size"] = min(stats["min_chunk_size"], chunk_size)
                        stats["max_chunk_size"] = max(stats["max_chunk_size"], chunk_size)
            else:
                stats["failed_files"] += 1
        
        if all_chunk_sizes:
            stats["average_chunk_size"] = sum(all_chunk_sizes) / len(all_chunk_sizes)
            
            # 计算chunk大小分布
            size_ranges = [
                (0, 100, "very_small"),
                (100, 300, "small"),
                (300, 600, "medium"),
                (600, 1000, "large"),
                (1000, float('inf'), "very_large")
            ]
            
            for min_size, max_size, label in size_ranges:
                count = sum(1 for size in all_chunk_sizes if min_size <= size < max_size)
                stats["chunk_size_distribution"][label] = count
        else:
            stats["min_chunk_size"] = 0
        
        return stats
    
    def _generate_chunk_manifest(self, chunking_results: List[Dict[str, Any]], context: BusinessAgentContext) -> Dict[str, Any]:
        """生成切割清单"""
        manifest = {
            "manifest_time": datetime.now().isoformat(),
            "task_id": context.task_id,
            "project_name": context.project_name,
            "action": context.action,
            "files": [],
            "total_chunks": 0,
            "chunk_summary": {}
        }
        
        for result in chunking_results:
            if result["chunking_success"]:
                file_info = {
                    "file_name": result["file_name"],
                    "chunk_count": result["chunk_count"],
                    "original_length": result.get("original_length", 0),
                    "chunks": []
                }
                
                for chunk in result["chunks"]:
                    file_info["chunks"].append({
                        "chunk_id": chunk["chunk_id"],
                        "chunk_index": chunk["chunk_index"],
                        "length": chunk["length"],
                        "preview": chunk["content"][:100] + "..." if len(chunk["content"]) > 100 else chunk["content"]
                    })
                
                manifest["files"].append(file_info)
                manifest["total_chunks"] += result["chunk_count"]
        
        # 生成摘要
        manifest["chunk_summary"] = {
            "total_files": len([f for f in manifest["files"]]),
            "total_chunks": manifest["total_chunks"],
            "average_chunks_per_file": manifest["total_chunks"] / max(1, len(manifest["files"]))
        }
        
        return manifest
    
    async def _save_chunks_to_files(self, chunking_results: List[Dict[str, Any]], context: BusinessAgentContext) -> Dict[str, Any]:
        """保存切割结果到文件"""
        init_info = context.get_extracted_data("init_info", {})
        work_directory = init_info.get("work_directory", "/tmp")
        chunks_dir = os.path.join(work_directory, "chunks")
        
        os.makedirs(chunks_dir, exist_ok=True)
        
        saved_files = {
            "chunks_directory": chunks_dir,
            "saved_files": [],
            "total_files": 0,
            "total_size": 0
        }
        
        for result in chunking_results:
            if result["chunking_success"] and result["chunks"]:
                file_name = result["file_name"]
                safe_file_name = "".join(c for c in file_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                chunks_file = os.path.join(chunks_dir, f"{safe_file_name}_chunks.txt")
                
                try:
                    with open(chunks_file, 'w', encoding='utf-8') as f:
                        f.write(f"# 文件: {file_name}\n")
                        f.write(f"# 切割时间: {datetime.now().isoformat()}\n")
                        f.write(f"# 总块数: {result['chunk_count']}\n")
                        f.write(f"# 原始长度: {result.get('original_length', 0)}\n\n")
                        
                        for chunk in result["chunks"]:
                            f.write(f"## Chunk {chunk['chunk_index'] + 1} ({chunk['length']} chars)\n")
                            f.write(f"ID: {chunk['chunk_id']}\n")
                            f.write(f"Content:\n{chunk['content']}\n")
                            f.write("-" * 50 + "\n\n")
                    
                    file_size = os.path.getsize(chunks_file)
                    saved_files["saved_files"].append({
                        "original_file": file_name,
                        "chunks_file": chunks_file,
                        "chunk_count": result["chunk_count"],
                        "file_size": file_size
                    })
                    saved_files["total_files"] += 1
                    saved_files["total_size"] += file_size
                    
                except Exception as e:
                    print(f"保存chunks文件失败 {chunks_file}: {e}")
        
        return saved_files
    
    def get_stage_name(self) -> str:
        return "text_chunking"
    
    def get_progress_percentage(self) -> int:
        return 50
    
    def get_stage_description(self) -> str:
        return "将文档内容切割成语义块"
