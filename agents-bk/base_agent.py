#!/usr/bin/env python3
"""
Agent工作流基础框架
"""

import asyncio
import json
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import uuid

class AgentStatus(Enum):
    """Agent状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class AgentContext:
    """Agent上下文数据"""
    task_id: str
    file_url: str
    file_content: Dict[str, Any]
    extracted_data: Dict[str, Any]
    metadata: Dict[str, Any]
    created_at: str
    updated_at: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    def update_extracted_data(self, key: str, value: Any):
        """更新提取的数据"""
        self.extracted_data[key] = value
        self.updated_at = datetime.now().isoformat()
    
    def get_extracted_data(self, key: str, default=None):
        """获取提取的数据"""
        return self.extracted_data.get(key, default)

@dataclass
class AgentResult:
    """Agent执行结果"""
    agent_id: str
    agent_name: str
    status: AgentStatus
    success: bool
    output_data: Dict[str, Any]
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result['status'] = self.status.value
        return result

class BaseAgent(ABC):
    """Agent基类"""
    
    def __init__(self, agent_id: str = None, name: str = None):
        self.agent_id = agent_id or str(uuid.uuid4())
        self.name = name or self.__class__.__name__
        self.status = AgentStatus.IDLE
        self.dependencies: List[str] = []  # 依赖的Agent ID列表
        self.task_manager = None
        
    def set_task_manager(self, task_manager):
        """设置任务管理器"""
        self.task_manager = task_manager
    
    def add_dependency(self, agent_id: str):
        """添加依赖的Agent"""
        if agent_id not in self.dependencies:
            self.dependencies.append(agent_id)
    
    async def execute(self, context: AgentContext) -> AgentResult:
        """执行Agent任务"""
        start_time = datetime.now()
        self.status = AgentStatus.RUNNING
        
        try:
            # 更新任务进度
            if self.task_manager:
                await self.task_manager.update_file_progress(
                    context.task_id,
                    context.file_url,
                    "processing",
                    self.get_progress_percentage(),
                    f"正在执行: {self.name}",
                    stage=self.get_stage_name(),
                    stage_detail=self.name
                )
            
            # 执行具体的Agent逻辑
            output_data = await self.process(context)
            
            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()
            
            self.status = AgentStatus.COMPLETED
            
            return AgentResult(
                agent_id=self.agent_id,
                agent_name=self.name,
                status=self.status,
                success=True,
                output_data=output_data,
                execution_time=execution_time,
                metadata={"completed_at": datetime.now().isoformat()}
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.status = AgentStatus.FAILED
            
            # 更新失败状态
            if self.task_manager:
                await self.task_manager.update_file_progress(
                    context.task_id,
                    context.file_url,
                    "failed",
                    self.get_progress_percentage(),
                    f"Agent执行失败: {str(e)}",
                    error=str(e),
                    stage=self.get_stage_name(),
                    stage_detail=f"{self.name} - 失败"
                )
            
            return AgentResult(
                agent_id=self.agent_id,
                agent_name=self.name,
                status=self.status,
                success=False,
                output_data={},
                error_message=str(e),
                execution_time=execution_time,
                metadata={"failed_at": datetime.now().isoformat()}
            )
    
    @abstractmethod
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """
        Agent的具体处理逻辑
        子类必须实现此方法
        """
        pass
    
    @abstractmethod
    def get_stage_name(self) -> str:
        """获取阶段名称"""
        pass
    
    @abstractmethod
    def get_progress_percentage(self) -> int:
        """获取进度百分比"""
        pass
    
    def can_execute(self, completed_agents: List[str], agent_stage_map: Dict[str, str] = None) -> bool:
        """检查是否可以执行（依赖是否满足）"""
        if not self.dependencies:
            return True

        # 如果提供了stage映射，使用stage名称匹配
        if agent_stage_map:
            completed_stages = [agent_stage_map.get(agent_id, agent_id) for agent_id in completed_agents]
            return all(dep in completed_stages for dep in self.dependencies)

        # 否则直接使用Agent ID匹配
        return all(dep in completed_agents for dep in self.dependencies)
    
    def get_info(self) -> Dict[str, Any]:
        """获取Agent信息"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "status": self.status.value,
            "dependencies": self.dependencies,
            "stage": self.get_stage_name(),
            "progress": self.get_progress_percentage()
        }

class AgentWorkflow:
    """Agent工作流管理器"""
    
    def __init__(self, workflow_id: str = None):
        self.workflow_id = workflow_id or str(uuid.uuid4())
        self.agents: Dict[str, BaseAgent] = {}
        self.execution_order: List[str] = []
        self.results: Dict[str, AgentResult] = {}
        self.status = AgentStatus.IDLE
        
    def add_agent(self, agent: BaseAgent):
        """添加Agent到工作流"""
        self.agents[agent.agent_id] = agent
        
    def set_execution_order(self, agent_ids: List[str]):
        """设置执行顺序"""
        self.execution_order = agent_ids
        
    def build_dependency_graph(self):
        """构建依赖图并确定执行顺序"""
        # 简单的拓扑排序实现
        in_degree = {agent_id: 0 for agent_id in self.agents.keys()}
        
        # 计算入度
        for agent in self.agents.values():
            for dep in agent.dependencies:
                if dep in in_degree:
                    in_degree[agent.agent_id] += 1
        
        # 拓扑排序
        queue = [agent_id for agent_id, degree in in_degree.items() if degree == 0]
        execution_order = []
        
        while queue:
            current = queue.pop(0)
            execution_order.append(current)
            
            # 更新依赖此Agent的其他Agent的入度
            for agent in self.agents.values():
                if current in agent.dependencies:
                    in_degree[agent.agent_id] -= 1
                    if in_degree[agent.agent_id] == 0:
                        queue.append(agent.agent_id)
        
        if len(execution_order) != len(self.agents):
            raise ValueError("检测到循环依赖，无法构建执行顺序")
        
        self.execution_order = execution_order
        
    async def execute(self, context: AgentContext) -> Dict[str, AgentResult]:
        """执行工作流"""
        self.status = AgentStatus.RUNNING
        completed_agents = []
        
        try:
            print(f"🔧 开始构建Agent执行顺序...")
            # 如果没有设置执行顺序，自动构建依赖图
            if not self.execution_order:
                self.build_dependency_graph()

            print(f"📋 Agent执行顺序: {self.execution_order}")

            # 创建Agent ID到stage名称的映射
            agent_stage_map = {agent_id: agent.get_stage_name() for agent_id, agent in self.agents.items()}
            print(f"🗺️ Agent Stage映射: {agent_stage_map}")

            # 按顺序执行Agent
            for i, agent_id in enumerate(self.execution_order, 1):
                agent = self.agents[agent_id]
                print(f"🤖 [{i}/{len(self.execution_order)}] 开始执行 Agent: {agent.name} (ID: {agent_id}, Stage: {agent.get_stage_name()})")

                # 检查依赖是否满足
                if not agent.can_execute(completed_agents, agent_stage_map):
                    error_msg = f"Agent {agent.name} 的依赖未满足，需要依赖: {agent.dependencies}，已完成的Agent: {completed_agents}"
                    print(f"❌ {error_msg}")
                    print(f"🔍 已完成的Stage: {[agent_stage_map.get(aid, aid) for aid in completed_agents]}")
                    raise RuntimeError(error_msg)

                try:
                    # 执行Agent
                    result = await agent.execute(context)
                    self.results[agent_id] = result

                    if result.success:
                        print(f"✅ Agent {agent.name} 执行成功 (耗时: {result.execution_time:.2f}s)")
                        # 将Agent的输出数据合并到上下文中
                        for key, value in result.output_data.items():
                            context.update_extracted_data(key, value)
                        completed_agents.append(agent_id)
                    else:
                        # 如果某个Agent失败，整个工作流失败
                        error_msg = f"Agent {agent.name} 执行失败: {result.error_message}"
                        print(f"❌ {error_msg}")
                        self.status = AgentStatus.FAILED
                        raise RuntimeError(error_msg)

                except Exception as agent_exc:
                    error_msg = f"Agent {agent.name} 执行异常: {str(agent_exc)}"
                    print(f"💥 {error_msg}")
                    print(f"🔍 异常类型: {type(agent_exc).__name__}")

                    # 记录异常到结果中
                    from datetime import datetime
                    self.results[agent_id] = AgentResult(
                        agent_id=agent_id,
                        success=False,
                        output_data={},
                        error_message=str(agent_exc),
                        execution_time=0.0,
                        timestamp=datetime.now().isoformat(),
                        status=AgentStatus.FAILED
                    )

                    self.status = AgentStatus.FAILED
                    raise agent_exc

            print(f"🎉 所有Agent执行完成，成功完成 {len(completed_agents)} 个Agent")
            self.status = AgentStatus.COMPLETED
            return self.results

        except Exception as e:
            print(f"💥 工作流执行失败: {str(e)}")
            print(f"🔍 失败位置: 已完成 {len(completed_agents)} 个Agent，总共 {len(self.execution_order)} 个")
            self.status = AgentStatus.FAILED
            raise e
    
    def get_workflow_info(self) -> Dict[str, Any]:
        """获取工作流信息"""
        return {
            "workflow_id": self.workflow_id,
            "status": self.status.value,
            "agents": [agent.get_info() for agent in self.agents.values()],
            "execution_order": self.execution_order,
            "results": {k: v.to_dict() for k, v in self.results.items()}
        }
    
    def get_overall_progress(self) -> int:
        """获取整体进度"""
        if not self.execution_order:
            return 0
        
        completed_count = len([r for r in self.results.values() if r.success])
        return int((completed_count / len(self.execution_order)) * 100)
