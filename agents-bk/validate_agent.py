#!/usr/bin/env python3
"""
文件验证Agent - 负责验证文件格式和完整性
"""

import asyncio
import json
import re
from typing import Dict, Any, List
from datetime import datetime
from .base_agent import BaseAgent, AgentContext

class ValidateAgent(BaseAgent):
    """文件验证Agent - 深度验证文件格式和内容完整性"""
    
    def __init__(self):
        super().__init__(name="文件验证Agent")
        self.add_dependency("download")  # 依赖下载Agent
    
    async def process(self, context: AgentContext) -> Dict[str, Any]:
        """验证文件格式和完整性"""
        await asyncio.sleep(0.4)  # 模拟验证时间
        
        # 获取下载信息
        download_info = context.get_extracted_data("download_info", {})
        content_validation = context.get_extracted_data("content_validation", {})
        file_metadata = context.get_extracted_data("file_metadata", {})
        
        # 执行深度验证
        format_validation = self._validate_format(context.file_content)
        content_integrity = self._check_content_integrity(context.file_content)
        structure_validation = self._validate_structure(context.file_content)
        encoding_validation = self._validate_encoding(context.file_content)
        
        # 安全性检查
        security_check = self._security_scan(context.file_content)
        
        # 质量评估
        quality_assessment = self._assess_quality(context.file_content)
        
        # 生成验证报告
        validation_report = self._generate_validation_report(
            format_validation, content_integrity, structure_validation,
            encoding_validation, security_check, quality_assessment
        )
        
        return {
            "format_validation": format_validation,
            "content_integrity": content_integrity,
            "structure_validation": structure_validation,
            "encoding_validation": encoding_validation,
            "security_check": security_check,
            "quality_assessment": quality_assessment,
            "validation_report": validation_report,
            "validation_summary": {
                "overall_valid": validation_report["overall_valid"],
                "validation_score": validation_report["validation_score"],
                "critical_issues": validation_report["critical_issues"],
                "warnings": validation_report["warnings"],
                "next_stage": "parse" if validation_report["overall_valid"] else "failed"
            }
        }
    
    def _validate_format(self, file_content: Dict[str, Any]) -> Dict[str, Any]:
        """验证文件格式"""
        validation = {
            "is_valid_json": True,
            "has_required_fields": True,
            "field_types_correct": True,
            "format_errors": []
        }
        
        # 检查必需字段
        required_fields = ["pages"]
        for field in required_fields:
            if field not in file_content:
                validation["has_required_fields"] = False
                validation["format_errors"].append(f"缺少必需字段: {field}")
        
        # 检查字段类型
        if "pages" in file_content:
            if not isinstance(file_content["pages"], list):
                validation["field_types_correct"] = False
                validation["format_errors"].append("pages字段必须是数组类型")
        
        if "metadata" in file_content:
            if not isinstance(file_content["metadata"], dict):
                validation["field_types_correct"] = False
                validation["format_errors"].append("metadata字段必须是对象类型")
        
        validation["is_valid_json"] = len(validation["format_errors"]) == 0
        return validation
    
    def _check_content_integrity(self, file_content: Dict[str, Any]) -> Dict[str, Any]:
        """检查内容完整性"""
        integrity = {
            "pages_complete": True,
            "content_not_empty": True,
            "page_sequence_valid": True,
            "integrity_issues": []
        }
        
        pages = file_content.get("pages", [])
        
        if not pages:
            integrity["pages_complete"] = False
            integrity["integrity_issues"].append("文档页面为空")
            return integrity
        
        # 检查页面内容
        empty_pages = 0
        for i, page in enumerate(pages):
            content = page.get("content", "")
            if not content or not content.strip():
                empty_pages += 1
                integrity["integrity_issues"].append(f"第{i+1}页内容为空")
        
        if empty_pages > 0:
            integrity["content_not_empty"] = False
        
        # 检查页面序号连续性
        page_numbers = []
        for page in pages:
            if "page_number" in page:
                page_numbers.append(page["page_number"])
        
        if page_numbers:
            page_numbers.sort()
            expected = list(range(1, len(page_numbers) + 1))
            if page_numbers != expected:
                integrity["page_sequence_valid"] = False
                integrity["integrity_issues"].append("页面序号不连续")
        
        return integrity
    
    def _validate_structure(self, file_content: Dict[str, Any]) -> Dict[str, Any]:
        """验证文档结构"""
        structure = {
            "has_title": False,
            "has_chapters": False,
            "has_sections": False,
            "structure_score": 0.0,
            "structure_analysis": {}
        }
        
        pages = file_content.get("pages", [])
        all_content = ""
        
        for page in pages:
            content = page.get("content", "")
            all_content += content + "\n"
        
        # 检查标题
        title_patterns = [
            r'^.{1,50}$',  # 短标题
            r'.*方案.*',    # 方案类文档
            r'.*报告.*',    # 报告类文档
            r'.*系统.*'     # 系统类文档
        ]
        
        first_line = all_content.split('\n')[0] if all_content else ""
        for pattern in title_patterns:
            if re.match(pattern, first_line):
                structure["has_title"] = True
                break
        
        # 检查章节结构
        chapter_patterns = [
            r'第[一二三四五六七八九十\d]+章',
            r'[一二三四五六七八九十\d]+\.',
            r'^\d+\s',
        ]
        
        chapter_count = 0
        for pattern in chapter_patterns:
            matches = re.findall(pattern, all_content, re.MULTILINE)
            chapter_count += len(matches)
        
        if chapter_count > 0:
            structure["has_chapters"] = True
        
        # 检查小节
        section_patterns = [
            r'\d+\.\d+',
            r'[一二三四五六七八九十]+、',
        ]
        
        section_count = 0
        for pattern in section_patterns:
            matches = re.findall(pattern, all_content)
            section_count += len(matches)
        
        if section_count > 0:
            structure["has_sections"] = True
        
        # 计算结构分数
        score = 0
        if structure["has_title"]:
            score += 0.4
        if structure["has_chapters"]:
            score += 0.4
        if structure["has_sections"]:
            score += 0.2
        
        structure["structure_score"] = score
        structure["structure_analysis"] = {
            "estimated_chapters": chapter_count,
            "estimated_sections": section_count,
            "content_length": len(all_content),
            "structure_complexity": "high" if score > 0.8 else "medium" if score > 0.4 else "low"
        }
        
        return structure
    
    def _validate_encoding(self, file_content: Dict[str, Any]) -> Dict[str, Any]:
        """验证文本编码"""
        encoding = {
            "encoding_valid": True,
            "has_special_chars": False,
            "chinese_content": False,
            "encoding_issues": []
        }
        
        pages = file_content.get("pages", [])
        
        for i, page in enumerate(pages):
            content = page.get("content", "")
            
            # 检查中文内容
            if re.search(r'[\u4e00-\u9fff]', content):
                encoding["chinese_content"] = True
            
            # 检查特殊字符
            if re.search(r'[^\x00-\x7F\u4e00-\u9fff\s]', content):
                encoding["has_special_chars"] = True
            
            # 检查乱码
            if '�' in content or '\ufffd' in content:
                encoding["encoding_valid"] = False
                encoding["encoding_issues"].append(f"第{i+1}页存在编码问题")
        
        return encoding
    
    def _security_scan(self, file_content: Dict[str, Any]) -> Dict[str, Any]:
        """安全性扫描"""
        security = {
            "is_safe": True,
            "security_score": 1.0,
            "threats_detected": [],
            "suspicious_patterns": []
        }
        
        pages = file_content.get("pages", [])
        all_content = ""
        
        for page in pages:
            content = page.get("content", "")
            all_content += content + " "
        
        # 检查可疑模式
        suspicious_patterns = [
            (r'<script.*?>', "可能包含脚本代码"),
            (r'javascript:', "可能包含JavaScript"),
            (r'eval\s*\(', "可能包含eval函数"),
            (r'document\.', "可能包含DOM操作"),
        ]
        
        for pattern, description in suspicious_patterns:
            if re.search(pattern, all_content, re.IGNORECASE):
                security["suspicious_patterns"].append(description)
                security["security_score"] -= 0.2
        
        # 检查敏感信息
        sensitive_patterns = [
            (r'\b\d{15,19}\b', "可能包含银行卡号"),
            (r'\b\d{3}-\d{2}-\d{4}\b', "可能包含社会保险号"),
            (r'password\s*[:=]\s*\S+', "可能包含密码"),
        ]
        
        for pattern, description in sensitive_patterns:
            if re.search(pattern, all_content, re.IGNORECASE):
                security["threats_detected"].append(description)
                security["security_score"] -= 0.3
        
        security["is_safe"] = security["security_score"] > 0.5
        security["security_score"] = max(0.0, security["security_score"])
        
        return security
    
    def _assess_quality(self, file_content: Dict[str, Any]) -> Dict[str, Any]:
        """评估内容质量"""
        quality = {
            "readability_score": 0.0,
            "completeness_score": 0.0,
            "consistency_score": 0.0,
            "overall_quality": 0.0,
            "quality_issues": []
        }
        
        pages = file_content.get("pages", [])
        
        if not pages:
            quality["quality_issues"].append("无内容可评估")
            return quality
        
        total_content = ""
        page_lengths = []
        
        for page in pages:
            content = page.get("content", "")
            total_content += content + " "
            page_lengths.append(len(content))
        
        # 可读性评估
        avg_sentence_length = len(total_content.split('。')) / max(1, len(total_content.split()))
        if avg_sentence_length > 0.1:
            quality["readability_score"] = min(1.0, avg_sentence_length * 5)
        
        # 完整性评估
        if len(total_content) > 100:
            quality["completeness_score"] = min(1.0, len(total_content) / 1000)
        
        # 一致性评估
        if page_lengths:
            avg_length = sum(page_lengths) / len(page_lengths)
            variance = sum((x - avg_length) ** 2 for x in page_lengths) / len(page_lengths)
            consistency = 1.0 / (1.0 + variance / 1000)
            quality["consistency_score"] = consistency
        
        # 总体质量
        quality["overall_quality"] = (
            quality["readability_score"] * 0.4 +
            quality["completeness_score"] * 0.4 +
            quality["consistency_score"] * 0.2
        )
        
        return quality
    
    def _generate_validation_report(self, format_val, integrity, structure, encoding, security, quality) -> Dict[str, Any]:
        """生成验证报告"""
        critical_issues = []
        warnings = []
        
        # 收集关键问题
        if not format_val["is_valid_json"]:
            critical_issues.extend(format_val["format_errors"])
        
        if not integrity["pages_complete"]:
            critical_issues.extend(integrity["integrity_issues"])
        
        if not encoding["encoding_valid"]:
            critical_issues.extend(encoding["encoding_issues"])
        
        if not security["is_safe"]:
            critical_issues.extend(security["threats_detected"])
        
        # 收集警告
        if not structure["has_title"]:
            warnings.append("文档可能缺少标题")
        
        if structure["structure_score"] < 0.5:
            warnings.append("文档结构不够清晰")
        
        if quality["overall_quality"] < 0.6:
            warnings.append("文档质量有待提高")
        
        # 计算总体验证分数
        validation_score = (
            (1.0 if format_val["is_valid_json"] else 0.0) * 0.3 +
            (1.0 if integrity["pages_complete"] else 0.0) * 0.2 +
            structure["structure_score"] * 0.2 +
            (1.0 if encoding["encoding_valid"] else 0.0) * 0.1 +
            security["security_score"] * 0.1 +
            quality["overall_quality"] * 0.1
        )
        
        overall_valid = len(critical_issues) == 0 and validation_score > 0.7
        
        return {
            "overall_valid": overall_valid,
            "validation_score": validation_score,
            "critical_issues": critical_issues,
            "warnings": warnings,
            "validation_timestamp": datetime.now().isoformat(),
            "recommendation": "可以继续处理" if overall_valid else "需要修复问题后再处理"
        }
    
    def get_stage_name(self) -> str:
        return "validate"
    
    def get_progress_percentage(self) -> int:
        return 25
    
    def get_stage_description(self) -> str:
        return "深度验证文件格式、完整性和安全性"
