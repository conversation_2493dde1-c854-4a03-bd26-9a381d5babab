#!/usr/bin/env python3
"""
HNGPT文档处理Agent集合
"""

import asyncio
import os
import json
import hashlib
from typing import Dict, Any, List
from datetime import datetime

try:
    from .hngpt_base_agent import (
        HNGPTBaseAgent, WorkflowContext, AgentMetadata,
        DataSchema, DataType
    )
except ImportError:
    from agents.hngpt_base_agent import (
        HNGPTBaseAgent, WorkflowContext, AgentMetadata,
        DataSchema, DataType
    )

class HNGPTInitAgent(HNGPTBaseAgent):
    """初始化Agent"""
    
    def _get_metadata(self) -> AgentMetadata:
        return AgentMetadata(
            name="InitAgent",
            version="1.0.0",
            description="初始化工作流，创建工作目录和基础配置",
            category="initialization",
            tags=["init", "setup"],
            estimated_duration=1.0
        )
    
    def _define_input_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="task_id",
                data_type=DataType.TEXT,
                required=True,
                description="任务ID"
            ),
            DataSchema(
                name="project_name",
                data_type=DataType.TEXT,
                required=True,
                description="项目名称"
            ),
            DataSchema(
                name="action",
                data_type=DataType.TEXT,
                required=True,
                description="操作类型（项目档案/文书档案）"
            )
        ]
    
    def _define_output_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="work_directory",
                data_type=DataType.FILE_PATH,
                required=True,
                description="工作目录路径"
            ),
            DataSchema(
                name="init_info",
                data_type=DataType.DICT,
                required=True,
                description="初始化信息"
            ),
            DataSchema(
                name="config",
                data_type=DataType.DICT,
                required=True,
                description="配置信息"
            )
        ]
    
    async def process(self, context: WorkflowContext) -> Dict[str, Any]:
        task_id = context.get_data("task_id")
        project_name = context.get_data("project_name")
        action = context.get_data("action")
        
        # 创建工作目录
        work_directory = f"/tmp/hngpt_task_{task_id}"
        os.makedirs(work_directory, exist_ok=True)
        
        # 创建子目录
        subdirs = ["downloads", "parsed", "chunks", "vectors", "logs", "results"]
        for subdir in subdirs:
            os.makedirs(os.path.join(work_directory, subdir), exist_ok=True)
        
        # 初始化信息
        init_info = {
            "task_id": task_id,
            "project_name": project_name,
            "action": action,
            "work_directory": work_directory,
            "subdirectories": subdirs,
            "initialization_time": datetime.now().isoformat(),
            "status": "initialized"
        }
        
        # 基础配置
        config = {
            "chunk_size": 1000,
            "chunk_overlap": 200,
            "max_file_size": 100 * 1024 * 1024,  # 100MB
            "supported_formats": [".pdf", ".docx", ".txt", ".json", ".md"],
            "timeout_seconds": 300
        }
        
        return {
            "work_directory": work_directory,
            "init_info": init_info,
            "config": config
        }

class HNGPTDownloadAgent(HNGPTBaseAgent):
    """文件下载Agent"""
    
    def _get_metadata(self) -> AgentMetadata:
        return AgentMetadata(
            name="DownloadAgent",
            version="1.0.0",
            description="从指定URL下载文件到本地工作目录",
            category="data_acquisition",
            tags=["download", "file", "minio"],
            estimated_duration=5.0
        )
    
    def _define_input_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="work_directory",
                data_type=DataType.FILE_PATH,
                required=True,
                description="工作目录路径"
            ),
            DataSchema(
                name="file_urls",
                data_type=DataType.LIST,
                required=True,
                description="要下载的文件URL列表"
            ),
            DataSchema(
                name="config",
                data_type=DataType.DICT,
                required=True,
                description="配置信息"
            )
        ]
    
    def _define_output_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="downloaded_files",
                data_type=DataType.LIST,
                required=True,
                description="下载的文件信息列表"
            ),
            DataSchema(
                name="download_stats",
                data_type=DataType.DICT,
                required=True,
                description="下载统计信息"
            )
        ]
    
    def _define_dependencies(self) -> List[str]:
        return ["InitAgent"]
    
    async def process(self, context: WorkflowContext) -> Dict[str, Any]:
        work_directory = context.get_data("work_directory")
        file_urls = context.get_data("file_urls")
        config = context.get_data("config")
        task_id = context.get_data("task_id", "unknown")

        download_dir = os.path.join(work_directory, "downloads")
        downloaded_files = []
        failed_downloads = []

        # 获取TaskManager实例来更新文件进度
        from agents.hngpt_task_integration import hngpt_task_integration

        for url in file_urls:
            try:
                # 更新文件进度：开始下载
                if hngpt_task_integration:
                    await hngpt_task_integration.task_manager.update_file_progress(
                        task_id=task_id,
                        file_url=url,
                        stage="download",
                        status="processing",
                        progress=0,
                        message="开始下载文件"
                    )

                # 模拟文件下载（实际实现中应该从MinIO或其他存储下载）
                filename = os.path.basename(url)
                if not filename:
                    filename = f"file_{hashlib.md5(url.encode()).hexdigest()[:8]}.txt"

                local_path = os.path.join(download_dir, filename)

                # 模拟下载内容（实际实现中应该真正下载文件）
                await asyncio.sleep(0.1)  # 模拟下载时间
                
                # 创建模拟文件内容
                mock_content = {
                    "title": "示例文档",
                    "content": "这是一个示例文档内容",
                    "metadata": {
                        "source_url": url,
                        "download_time": datetime.now().isoformat()
                    }
                }
                
                with open(local_path, 'w', encoding='utf-8') as f:
                    json.dump(mock_content, f, ensure_ascii=False, indent=2)
                
                file_info = {
                    "url": url,
                    "local_path": local_path,
                    "filename": filename,
                    "file_size": os.path.getsize(local_path),
                    "download_time": datetime.now().isoformat(),
                    "status": "success"
                }
                
                downloaded_files.append(file_info)

                # 更新文件进度：下载完成
                if hngpt_task_integration:
                    await hngpt_task_integration.task_manager.update_file_progress(
                        task_id=task_id,
                        file_url=url,
                        stage="download",
                        status="completed",
                        progress=100,
                        message="文件下载完成"
                    )

            except Exception as e:
                failed_downloads.append({
                    "url": url,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
        
        download_stats = {
            "total_requested": len(file_urls),
            "successful_downloads": len(downloaded_files),
            "failed_downloads": len(failed_downloads),
            "download_directory": download_dir,
            "total_size": sum(f["file_size"] for f in downloaded_files)
        }
        
        return {
            "downloaded_files": downloaded_files,
            "download_stats": download_stats
        }

class HNGPTParseAgent(HNGPTBaseAgent):
    """文档解析Agent"""
    
    def _get_metadata(self) -> AgentMetadata:
        return AgentMetadata(
            name="ParseAgent",
            version="1.0.0",
            description="解析下载的文档，提取文本内容",
            category="data_processing",
            tags=["parse", "text_extraction", "document"],
            estimated_duration=3.0
        )
    
    def _define_input_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="downloaded_files",
                data_type=DataType.LIST,
                required=True,
                description="下载的文件信息列表"
            ),
            DataSchema(
                name="work_directory",
                data_type=DataType.FILE_PATH,
                required=True,
                description="工作目录路径"
            )
        ]
    
    def _define_output_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="parsed_documents",
                data_type=DataType.LIST,
                required=True,
                description="解析后的文档列表"
            ),
            DataSchema(
                name="parse_stats",
                data_type=DataType.DICT,
                required=True,
                description="解析统计信息"
            )
        ]
    
    def _define_dependencies(self) -> List[str]:
        return ["DownloadAgent"]
    
    async def process(self, context: WorkflowContext) -> Dict[str, Any]:
        downloaded_files = context.get_data("downloaded_files")
        work_directory = context.get_data("work_directory")
        task_id = context.get_data("task_id", "unknown")

        parsed_dir = os.path.join(work_directory, "parsed")
        parsed_documents = []
        failed_parses = []

        # 获取TaskManager实例来更新文件进度
        from agents.hngpt_task_integration import hngpt_task_integration

        for file_info in downloaded_files:
            try:
                file_url = file_info.get("url", file_info.get("filename", "unknown"))

                # 更新文件进度：开始解析
                if hngpt_task_integration:
                    await hngpt_task_integration.task_manager.update_file_progress(
                        task_id=task_id,
                        file_url=file_url,
                        stage="parse",
                        status="processing",
                        progress=0,
                        message="开始解析文档"
                    )

                local_path = file_info["local_path"]

                # 读取文件内容
                with open(local_path, 'r', encoding='utf-8') as f:
                    if local_path.endswith('.json'):
                        content = json.load(f)
                        text_content = json.dumps(content, ensure_ascii=False)
                    else:
                        text_content = f.read()
                
                # 解析文档
                parsed_doc = {
                    "source_file": file_info["filename"],
                    "source_path": local_path,
                    "text_content": text_content,
                    "content_length": len(text_content),
                    "parse_time": datetime.now().isoformat(),
                    "metadata": {
                        "file_type": os.path.splitext(file_info["filename"])[1],
                        "original_size": file_info["file_size"]
                    }
                }
                
                # 保存解析结果
                parsed_filename = f"parsed_{file_info['filename']}.json"
                parsed_path = os.path.join(parsed_dir, parsed_filename)
                
                with open(parsed_path, 'w', encoding='utf-8') as f:
                    json.dump(parsed_doc, f, ensure_ascii=False, indent=2)
                
                parsed_doc["parsed_path"] = parsed_path
                parsed_documents.append(parsed_doc)

                # 更新文件进度：解析完成
                if hngpt_task_integration:
                    await hngpt_task_integration.task_manager.update_file_progress(
                        task_id=task_id,
                        file_url=file_url,
                        stage="parse",
                        status="completed",
                        progress=100,
                        message="文档解析完成"
                    )

            except Exception as e:
                failed_parses.append({
                    "file": file_info["filename"],
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
        
        parse_stats = {
            "total_files": len(downloaded_files),
            "successful_parses": len(parsed_documents),
            "failed_parses": len(failed_parses),
            "total_text_length": sum(doc["content_length"] for doc in parsed_documents),
            "parse_directory": parsed_dir
        }
        
        return {
            "parsed_documents": parsed_documents,
            "parse_stats": parse_stats
        }

class HNGPTExtractionAgent(HNGPTBaseAgent):
    """信息提取Agent"""
    
    def _get_metadata(self) -> AgentMetadata:
        return AgentMetadata(
            name="ExtractionAgent",
            version="1.0.0",
            description="从解析的文档中提取结构化信息",
            category="data_processing",
            tags=["extraction", "nlp", "structured_data"],
            estimated_duration=10.0
        )
    
    def _define_input_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="parsed_documents",
                data_type=DataType.LIST,
                required=True,
                description="解析后的文档列表"
            ),
            DataSchema(
                name="action",
                data_type=DataType.TEXT,
                required=True,
                description="操作类型（项目档案/文书档案）"
            )
        ]
    
    def _define_output_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="extracted_data",
                data_type=DataType.DICT,
                required=True,
                description="提取的结构化数据"
            ),
            DataSchema(
                name="extraction_stats",
                data_type=DataType.DICT,
                required=True,
                description="提取统计信息"
            )
        ]
    
    def _define_dependencies(self) -> List[str]:
        return ["ParseAgent"]
    
    def _get_parallel_group(self) -> str:
        return "extraction_group"
    
    async def process(self, context: WorkflowContext) -> Dict[str, Any]:
        parsed_documents = context.get_data("parsed_documents")
        action = context.get_data("action")
        
        extracted_data = {}
        extraction_stats = {
            "total_documents": len(parsed_documents),
            "extraction_type": action,
            "extraction_time": datetime.now().isoformat()
        }
        
        # 模拟信息提取
        await asyncio.sleep(1.0)  # 模拟LLM处理时间
        
        if action == "项目档案":
            extracted_data = {
                "project_name": "示例项目",
                "project_number": "PROJ-2024-001",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "budget": "100万元",
                "responsible_unit": "技术部",
                "project_leader": "张三",
                "project_description": "这是一个示例项目的描述",
                "deliverables": ["需求文档", "设计文档", "系统代码"],
                "milestones": [
                    {"name": "需求分析", "date": "2024-03-01"},
                    {"name": "系统设计", "date": "2024-06-01"},
                    {"name": "开发完成", "date": "2024-10-01"}
                ]
            }
        elif action == "文书档案":
            extracted_data = {
                "document_type": "合同",
                "document_number": "DOC-2024-001",
                "parties": ["甲方公司", "乙方公司"],
                "contract_amount": "50万元",
                "signing_date": "2024-01-15",
                "effective_date": "2024-02-01",
                "expiry_date": "2025-01-31",
                "key_terms": ["付款条件", "交付时间", "质量标准"],
                "attachments": ["技术规格书", "报价单"]
            }
        
        extraction_stats.update({
            "fields_extracted": len(extracted_data),
            "extraction_success": True
        })
        
        return {
            "extracted_data": extracted_data,
            "extraction_stats": extraction_stats
        }

class HNGPTStorageAgent(HNGPTBaseAgent):
    """数据存储Agent"""
    
    def _get_metadata(self) -> AgentMetadata:
        return AgentMetadata(
            name="StorageAgent",
            version="1.0.0",
            description="将提取的数据存储到数据库",
            category="data_storage",
            tags=["storage", "database", "persistence"],
            estimated_duration=2.0
        )
    
    def _define_input_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="extracted_data",
                data_type=DataType.DICT,
                required=True,
                description="提取的结构化数据"
            ),
            DataSchema(
                name="task_id",
                data_type=DataType.TEXT,
                required=True,
                description="任务ID"
            )
        ]
    
    def _define_output_schemas(self) -> List[DataSchema]:
        return [
            DataSchema(
                name="storage_result",
                data_type=DataType.DICT,
                required=True,
                description="存储结果"
            )
        ]
    
    def _define_dependencies(self) -> List[str]:
        return ["ExtractionAgent"]
    
    async def process(self, context: WorkflowContext) -> Dict[str, Any]:
        extracted_data = context.get_data("extracted_data")
        task_id = context.get_data("task_id")
        
        # 模拟数据库存储
        await asyncio.sleep(0.5)
        
        storage_result = {
            "task_id": task_id,
            "storage_time": datetime.now().isoformat(),
            "records_stored": 1,
            "storage_location": "database",
            "record_id": f"record_{task_id}",
            "data_size": len(str(extracted_data)),
            "storage_success": True
        }
        
        return {
            "storage_result": storage_result
        }
