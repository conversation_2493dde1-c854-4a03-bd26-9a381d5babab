#!/usr/bin/env python3
"""
HNGPT工作流工厂类
"""

from typing import Dict, Any, List, Optional
try:
    from .hngpt_base_agent import HNGPTBaseAgent, WorkflowContext
    from .hngpt_workflow_dag import HNGPTWorkflowDAG
    from .hngpt_workflow_executor import HNGPTWorkflowExecutor, ExecutionConfig
    from .hngpt_document_agents import (
        HNGPTInitAgent, HNGPTDownloadAgent, HNGPTParseAgent,
        HNGPTExtractionAgent, HNGPTStorageAgent
    )
except ImportError:
    from agents.hngpt_base_agent import HNGPTBaseAgent, WorkflowContext
    from agents.hngpt_workflow_dag import HNGPTWorkflowDAG
    from agents.hngpt_workflow_executor import HNGPTWorkflowExecutor, ExecutionConfig
    from agents.hngpt_document_agents import (
        HNGPTInitAgent, HNGPTDownloadAgent, HNGPTParseAgent,
        HNGPTExtractionAgent, HNGPTStorageAgent
    )

class HNGPTWorkflowFactory:
    """HNGPT工作流工厂"""
    
    @staticmethod
    def create_document_processing_workflow(workflow_name: str = "document_processing") -> HNGPTWorkflowDAG:
        """创建文档处理工作流"""
        dag = HNGPTWorkflowDAG(name=workflow_name)
        
        # 创建Agent实例
        init_agent = HNGPTInitAgent()
        download_agent = HNGPTDownloadAgent()
        parse_agent = HNGPTParseAgent()
        extraction_agent = HNGPTExtractionAgent()
        storage_agent = HNGPTStorageAgent()
        
        # 添加Agent到DAG
        init_id = dag.add_agent(init_agent)
        download_id = dag.add_agent(download_agent, dependencies=[init_id])
        parse_id = dag.add_agent(parse_agent, dependencies=[download_id])
        extraction_id = dag.add_agent(extraction_agent, dependencies=[parse_id])
        storage_id = dag.add_agent(storage_agent, dependencies=[extraction_id])
        
        return dag
    
    @staticmethod
    def create_parallel_extraction_workflow(workflow_name: str = "parallel_extraction") -> HNGPTWorkflowDAG:
        """创建并行提取工作流"""
        dag = HNGPTWorkflowDAG(name=workflow_name)
        
        # 基础处理Agent
        init_agent = HNGPTInitAgent()
        download_agent = HNGPTDownloadAgent()
        parse_agent = HNGPTParseAgent()
        
        # 并行提取Agent
        entity_agent = HNGPTEntityExtractionAgent()
        relation_agent = HNGPTRelationExtractionAgent()
        keyword_agent = HNGPTKeywordExtractionAgent()
        
        # 合并和存储Agent
        merge_agent = HNGPTMergeAgent()
        storage_agent = HNGPTStorageAgent()
        
        # 构建DAG
        init_id = dag.add_agent(init_agent)
        download_id = dag.add_agent(download_agent, dependencies=[init_id])
        parse_id = dag.add_agent(parse_agent, dependencies=[download_id])
        
        # 并行提取
        entity_id = dag.add_agent(entity_agent, dependencies=[parse_id], parallel_group="extractors")
        relation_id = dag.add_agent(relation_agent, dependencies=[parse_id], parallel_group="extractors")
        keyword_id = dag.add_agent(keyword_agent, dependencies=[parse_id], parallel_group="extractors")
        
        # 合并和存储
        merge_id = dag.add_agent(merge_agent, dependencies=[entity_id, relation_id, keyword_id])
        storage_id = dag.add_agent(storage_agent, dependencies=[merge_id])
        
        return dag
    
    @staticmethod
    def create_conditional_workflow(workflow_name: str = "conditional_processing") -> HNGPTWorkflowDAG:
        """创建条件处理工作流"""
        dag = HNGPTWorkflowDAG(name=workflow_name)
        
        # 基础处理
        init_agent = HNGPTInitAgent()
        download_agent = HNGPTDownloadAgent()
        parse_agent = HNGPTParseAgent()
        detection_agent = HNGPTDocumentTypeDetectionAgent()
        
        # 条件处理Agent
        project_agent = HNGPTProjectDocumentProcessor()
        contract_agent = HNGPTContractProcessor()
        general_agent = HNGPTGeneralDocumentProcessor()
        
        # 合并Agent
        merge_agent = HNGPTResultMergeAgent()
        storage_agent = HNGPTStorageAgent()
        
        # 构建DAG
        init_id = dag.add_agent(init_agent)
        download_id = dag.add_agent(download_agent, dependencies=[init_id])
        parse_id = dag.add_agent(parse_agent, dependencies=[download_id])
        detection_id = dag.add_agent(detection_agent, dependencies=[parse_id])
        
        # 条件处理（并行）
        project_id = dag.add_agent(
            project_agent, 
            dependencies=[detection_id], 
            parallel_group="processors",
            condition="document_type == 'project'"
        )
        contract_id = dag.add_agent(
            contract_agent, 
            dependencies=[detection_id], 
            parallel_group="processors",
            condition="document_type == 'contract'"
        )
        general_id = dag.add_agent(
            general_agent, 
            dependencies=[detection_id], 
            parallel_group="processors",
            condition="document_type == 'general'"
        )
        
        # 合并和存储
        merge_id = dag.add_agent(merge_agent, dependencies=[project_id, contract_id, general_id])
        storage_id = dag.add_agent(storage_agent, dependencies=[merge_id])
        
        return dag
    
    @staticmethod
    def create_custom_workflow(
        workflow_config: Dict[str, Any], 
        workflow_name: str = "custom_workflow"
    ) -> HNGPTWorkflowDAG:
        """根据配置创建自定义工作流"""
        dag = HNGPTWorkflowDAG(name=workflow_name)
        
        # 解析配置并创建Agent
        agent_registry = HNGPTWorkflowFactory._get_agent_registry()
        created_agents = {}
        
        for agent_config in workflow_config.get("agents", []):
            agent_type = agent_config["type"]
            agent_params = agent_config.get("params", {})
            
            if agent_type not in agent_registry:
                raise ValueError(f"Unknown agent type: {agent_type}")
            
            agent_class = agent_registry[agent_type]
            agent = agent_class(**agent_params)
            created_agents[agent_config["id"]] = agent
        
        # 添加Agent到DAG
        for agent_config in workflow_config.get("agents", []):
            agent_id = agent_config["id"]
            agent = created_agents[agent_id]
            dependencies = agent_config.get("dependencies", [])
            parallel_group = agent_config.get("parallel_group")
            condition = agent_config.get("condition")
            
            dag.add_agent(
                agent=agent,
                dependencies=dependencies,
                parallel_group=parallel_group,
                condition=condition
            )
        
        return dag
    
    @staticmethod
    def _get_agent_registry() -> Dict[str, type]:
        """获取Agent注册表"""
        return {
            "init": HNGPTInitAgent,
            "download": HNGPTDownloadAgent,
            "parse": HNGPTParseAgent,
            "extraction": HNGPTExtractionAgent,
            "storage": HNGPTStorageAgent,
            # 可以继续添加更多Agent类型
        }
    
    @staticmethod
    def create_executor(
        dag: HNGPTWorkflowDAG, 
        config: Dict[str, Any] = None
    ) -> HNGPTWorkflowExecutor:
        """创建工作流执行器"""
        execution_config = ExecutionConfig(
            max_concurrent_agents=config.get("max_concurrent_agents", 10),
            timeout_seconds=config.get("timeout_seconds", 3600.0),
            retry_failed_agents=config.get("retry_failed_agents", True),
            continue_on_failure=config.get("continue_on_failure", False),
            log_level=config.get("log_level", "INFO")
        )
        
        return HNGPTWorkflowExecutor(dag, execution_config)

# 扩展的Agent类（用于演示）
class HNGPTEntityExtractionAgent(HNGPTExtractionAgent):
    """实体提取Agent"""
    
    def _get_metadata(self):
        metadata = super()._get_metadata()
        metadata.name = "EntityExtractionAgent"
        metadata.description = "提取文档中的实体信息"
        metadata.tags = ["entity", "ner", "extraction"]
        return metadata
    
    def _get_parallel_group(self) -> str:
        return "extractors"

class HNGPTRelationExtractionAgent(HNGPTExtractionAgent):
    """关系提取Agent"""
    
    def _get_metadata(self):
        metadata = super()._get_metadata()
        metadata.name = "RelationExtractionAgent"
        metadata.description = "提取文档中的关系信息"
        metadata.tags = ["relation", "extraction"]
        return metadata
    
    def _get_parallel_group(self) -> str:
        return "extractors"

class HNGPTKeywordExtractionAgent(HNGPTExtractionAgent):
    """关键词提取Agent"""
    
    def _get_metadata(self):
        metadata = super()._get_metadata()
        metadata.name = "KeywordExtractionAgent"
        metadata.description = "提取文档中的关键词"
        metadata.tags = ["keyword", "extraction"]
        return metadata
    
    def _get_parallel_group(self) -> str:
        return "extractors"

class HNGPTMergeAgent(HNGPTBaseAgent):
    """数据合并Agent"""
    
    def _get_metadata(self):
        try:
            from .hngpt_base_agent import AgentMetadata
        except ImportError:
            from agents.hngpt_base_agent import AgentMetadata
        return AgentMetadata(
            name="MergeAgent",
            version="1.0.0",
            description="合并多个Agent的提取结果",
            category="data_processing",
            tags=["merge", "aggregation"]
        )

    def _define_input_schemas(self):
        try:
            from .hngpt_base_agent import DataSchema, DataType
        except ImportError:
            from agents.hngpt_base_agent import DataSchema, DataType
        return [
            DataSchema("extracted_data", DataType.DICT, True, "提取的数据")
        ]

    def _define_output_schemas(self):
        try:
            from .hngpt_base_agent import DataSchema, DataType
        except ImportError:
            from agents.hngpt_base_agent import DataSchema, DataType
        return [
            DataSchema("merged_data", DataType.DICT, True, "合并后的数据")
        ]
    
    async def process(self, context: WorkflowContext) -> Dict[str, Any]:
        # 合并所有提取的数据
        merged_data = {}
        
        # 从上下文中收集所有相关数据
        for key, value in context.data.items():
            if key.endswith("_data") or key.endswith("_result"):
                if isinstance(value, dict):
                    merged_data.update(value)
        
        return {"merged_data": merged_data}

class HNGPTDocumentTypeDetectionAgent(HNGPTBaseAgent):
    """文档类型检测Agent"""
    
    def _get_metadata(self):
        try:
            from .hngpt_base_agent import AgentMetadata
        except ImportError:
            from agents.hngpt_base_agent import AgentMetadata
        return AgentMetadata(
            name="DocumentTypeDetectionAgent",
            version="1.0.0",
            description="检测文档类型",
            category="classification",
            tags=["detection", "classification"]
        )
    
    def _define_input_schemas(self):
        try:
            from .hngpt_base_agent import DataSchema, DataType
        except ImportError:
            from agents.hngpt_base_agent import DataSchema, DataType
        return [
            DataSchema("parsed_documents", DataType.LIST, True, "解析的文档")
        ]

    def _define_output_schemas(self):
        try:
            from .hngpt_base_agent import DataSchema, DataType
        except ImportError:
            from agents.hngpt_base_agent import DataSchema, DataType
        return [
            DataSchema("document_type", DataType.TEXT, True, "文档类型")
        ]
    
    async def process(self, context: WorkflowContext) -> Dict[str, Any]:
        parsed_documents = context.get_data("parsed_documents")
        
        # 简单的文档类型检测逻辑
        if any("项目" in doc.get("text_content", "") for doc in parsed_documents):
            doc_type = "project"
        elif any("合同" in doc.get("text_content", "") for doc in parsed_documents):
            doc_type = "contract"
        else:
            doc_type = "general"
        
        return {"document_type": doc_type}

class HNGPTProjectDocumentProcessor(HNGPTExtractionAgent):
    """项目文档处理Agent"""
    
    def _get_metadata(self):
        metadata = super()._get_metadata()
        metadata.name = "ProjectDocumentProcessor"
        metadata.description = "处理项目类型文档"
        return metadata

class HNGPTContractProcessor(HNGPTExtractionAgent):
    """合同处理Agent"""
    
    def _get_metadata(self):
        metadata = super()._get_metadata()
        metadata.name = "ContractProcessor"
        metadata.description = "处理合同类型文档"
        return metadata

class HNGPTGeneralDocumentProcessor(HNGPTExtractionAgent):
    """通用文档处理Agent"""
    
    def _get_metadata(self):
        metadata = super()._get_metadata()
        metadata.name = "GeneralDocumentProcessor"
        metadata.description = "处理通用类型文档"
        return metadata

class HNGPTResultMergeAgent(HNGPTMergeAgent):
    """结果合并Agent"""
    
    def _get_metadata(self):
        metadata = super()._get_metadata()
        metadata.name = "ResultMergeAgent"
        metadata.description = "合并不同处理器的结果"
        return metadata
