from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from urllib.parse import quote_plus
from utils.config import config
from utils.log import log
import asyncio

# 数据库连接配置
DATABASE_URL = (
    f"mysql+asyncmy://{config.get('mysql.username', 'root')}:{quote_plus(config.get('mysql.password', 'startfrom2023'))}"
    f"@{config.get('mysql.host', '***********')}:{config.get('mysql.port', 3306)}/{config.get('mysql.database', 'hngpt')}"
    "?charset=utf8mb4"
)
log.info(f"数据库连接配置: {DATABASE_URL}")

# 创建异步引擎 - 优化配置避免greenlet问题
engine = create_async_engine(
    DATABASE_URL,
    # 连接池配置
    pool_size=20,  # 连接池大小
    max_overflow=30,  # 最大溢出连接数
    pool_timeout=30,  # 获取连接超时时间
    pool_recycle=3600,  # 连接回收时间（1小时）
    pool_pre_ping=True,  # 连接前ping检查
    # 异步配置
    echo=False,  # 生产环境关闭SQL日志
    future=True,  # 使用2.0风格API
)

# 创建异步会话工厂 - 优化配置避免greenlet问题
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,  # 关闭自动flush，手动控制
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,  # 提交后不过期对象，避免懒加载问题
)

# 声明基类
Base = declarative_base()

async def get_db():
    """异步数据库会话依赖项 - 确保正确的事务管理"""
    async with SessionLocal() as db:
        try:
            yield db
            # 显式提交事务
            await db.commit()
        except Exception as e:
            await db.rollback()
            raise



class DatabaseManager:
    """数据库管理器 - 提供更安全的数据库操作方法"""

    @staticmethod
    async def execute_in_transaction(operation, *args, **kwargs):
        """在事务中执行操作，自动处理异常和回滚"""
        async with SessionLocal() as db:
            try:
                async with db.begin():
                    result = await operation(db, *args, **kwargs)
                    return result
            except Exception as e:
                await db.rollback()
                raise e

    @staticmethod
    async def execute_query(query, params=None):
        """执行查询操作"""
        async with SessionLocal() as db:
            try:
                result = await db.execute(query, params or {})
                return result
            except Exception as e:
                raise e

    @staticmethod
    async def execute_and_commit(query, params=None):
        """执行更新操作并提交"""
        async with SessionLocal() as db:
            try:
                result = await db.execute(query, params or {})
                await db.commit()
                return result
            except Exception as e:
                await db.rollback()
                raise e
