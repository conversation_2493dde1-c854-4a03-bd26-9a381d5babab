#!/usr/bin/env python3
"""
数据库和索引重置工具
清空 Elasticsearch 索引和 MySQL 数据库表
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config import config
from utils.log import log
import asyncmy
from elasticsearch import AsyncElasticsearch
import yaml


class DatabaseResetter:
    """数据库和索引重置器"""
    
    def __init__(self):
        self.config = config
        self.es_client = None
        self.mysql_connection = None
        
    async def initialize(self):
        """初始化连接"""
        try:
            # 初始化 Elasticsearch 连接
            await self._init_elasticsearch()
            
            # 初始化 MySQL 连接
            await self._init_mysql()
            
            log.info("✅ 所有连接初始化成功")
            return True
            
        except Exception as e:
            log.error(f"❌ 初始化连接失败: {e}")
            return False
    
    async def _init_elasticsearch(self):
        """初始化 Elasticsearch 连接"""
        es_config = self.config.get("elasticsearch", {})
        
        host = es_config.get("host", "***********")
        port = es_config.get("port", 9200)
        username = es_config.get("username", "elastic")
        password = es_config.get("password", "elastic")
        
        # 构建连接URL
        if username and password:
            es_url = f"http://{username}:{password}@{host}:{port}"
        else:
            es_url = f"http://{host}:{port}"
        
        self.es_client = AsyncElasticsearch([es_url])
        
        # 测试连接
        info = await self.es_client.info()
        log.info(f"✅ Elasticsearch 连接成功: {info['version']['number']}")
    
    async def _init_mysql(self):
        """初始化 MySQL 连接"""
        mysql_config = self.config.get("mysql", {})

        self.connection_params = {
            "host": mysql_config.get("host", "***********"),
            "port": mysql_config.get("port", 3306),
            "user": mysql_config.get("user", "root"),
            "password": mysql_config.get("password", "startfrom2023"),
            "database": mysql_config.get("database", "hngpt"),
            "charset": "utf8mb4"
        }

        self.mysql_connection = await asyncmy.connect(**self.connection_params)

        # 测试连接
        async with self.mysql_connection.cursor() as cursor:
            await cursor.execute("SELECT VERSION()")
            version = await cursor.fetchone()
            log.info(f"✅ MySQL 连接成功: {version[0]}")
    
    async def reset_elasticsearch_indices(self):
        """重置 Elasticsearch 索引"""
        indices_to_reset = ["docs", "projectinfo"]
        
        log.info("🔄 开始重置 Elasticsearch 索引...")
        
        for index_name in indices_to_reset:
            try:
                # 检查索引是否存在
                exists = await self.es_client.indices.exists(index=index_name)
                
                if exists:
                    # 获取索引中的文档数量
                    count_result = await self.es_client.count(index=index_name)
                    doc_count = count_result["count"]
                    
                    if doc_count > 0:
                        log.info(f"📊 索引 {index_name} 包含 {doc_count} 个文档")
                        
                        # 删除所有文档
                        delete_result = await self.es_client.delete_by_query(
                            index=index_name,
                            body={"query": {"match_all": {}}},
                            refresh=True
                        )
                        
                        deleted_count = delete_result.get("deleted", 0)
                        log.info(f"✅ 索引 {index_name} 已清空，删除了 {deleted_count} 个文档")
                    else:
                        log.info(f"ℹ️ 索引 {index_name} 已经是空的")
                else:
                    log.warning(f"⚠️ 索引 {index_name} 不存在")
                    
            except Exception as e:
                log.error(f"❌ 重置索引 {index_name} 失败: {e}")
    
    async def reset_mysql_tables(self):
        """重置 MySQL 数据库表"""
        tables_to_reset = [
            "documents",
            "conference_extract",
            "project_extract",
            "document_permissions",
            "permission_requests",
            "assembly_conversations"
        ]

        log.info("🔄 开始重置 MySQL 数据库表...")

        # 获取数据库名称
        database_name = self.connection_params.get("database", "hngpt")

        async with self.mysql_connection.cursor() as cursor:
            for table_name in tables_to_reset:
                try:
                    # 检查表是否存在
                    await cursor.execute(
                        "SELECT COUNT(*) FROM information_schema.tables "
                        "WHERE table_schema = %s AND table_name = %s",
                        (database_name, table_name)
                    )
                    result = await cursor.fetchone()
                    table_exists = result[0] > 0

                    if table_exists:
                        # 获取表中的记录数量
                        await cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                        result = await cursor.fetchone()
                        record_count = result[0]

                        if record_count > 0:
                            log.info(f"📊 表 {table_name} 包含 {record_count} 条记录")

                            # 清空表
                            await cursor.execute(f"TRUNCATE TABLE `{table_name}`")
                            log.info(f"✅ 表 {table_name} 已清空")
                        else:
                            log.info(f"ℹ️ 表 {table_name} 已经是空的")
                    else:
                        log.warning(f"⚠️ 表 {table_name} 不存在")

                except Exception as e:
                    log.error(f"❌ 重置表 {table_name} 失败: {e}")

            # 提交事务
            await self.mysql_connection.commit()
    
    async def close_connections(self):
        """关闭所有连接"""
        try:
            if self.es_client:
                await self.es_client.close()
                log.info("✅ Elasticsearch 连接已关闭")
            
            if self.mysql_connection:
                await self.mysql_connection.ensure_closed()
                log.info("✅ MySQL 连接已关闭")
                
        except Exception as e:
            log.error(f"❌ 关闭连接时出错: {e}")
    
    async def reset_all(self):
        """执行完整的重置操作"""
        log.info("🚀 开始数据库和索引重置操作")
        log.info("=" * 60)
        
        try:
            # 初始化连接
            if not await self.initialize():
                return False
            
            # 重置 Elasticsearch 索引
            await self.reset_elasticsearch_indices()
            
            # 重置 MySQL 表
            await self.reset_mysql_tables()
            
            log.info("=" * 60)
            log.info("🎉 所有重置操作完成！")
            return True
            
        except Exception as e:
            log.error(f"❌ 重置操作失败: {e}")
            return False
        finally:
            await self.close_connections()


def confirm_reset():
    """确认重置操作"""
    print("⚠️  警告：此操作将清空以下数据：")
    print("   📁 Elasticsearch 索引:")
    print("      - docs")
    print("      - projectinfo")
    print("   🗄️  MySQL 数据库表:")
    print("      - documents")
    print("      - conference_extract")
    print("      - project_extract")
    print("      - document_permissions")
    print("      - permission_requests")
    print("      - assembly_conversations")
    print()
    print("❗ 此操作不可逆，所有数据将被永久删除！")
    print()
    
    while True:
        confirm = input("确认执行重置操作吗？(yes/no): ").strip().lower()
        if confirm in ['yes', 'y']:
            return True
        elif confirm in ['no', 'n']:
            return False
        else:
            print("请输入 'yes' 或 'no'")


async def main():
    """主函数"""
    print("🔧 数据库和索引重置工具")
    print("=" * 40)
    
    # 确认操作
    if not confirm_reset():
        print("❌ 操作已取消")
        return
    
    print("\n🚀 开始执行重置操作...")
    
    # 执行重置
    resetter = DatabaseResetter()
    success = await resetter.reset_all()
    
    if success:
        print("\n✅ 重置操作成功完成！")
        print("💡 提示：你现在可以重新导入数据或开始新的测试")
    else:
        print("\n❌ 重置操作失败，请检查日志")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n❌ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行过程中发生错误: {e}")
        sys.exit(1)
